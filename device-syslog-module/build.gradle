import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot' version '3.0.5' apply false
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id 'maven-publish'
    id "io.freefair.lombok" version "6.5.0.3"
    id 'checkstyle'
    id 'jacoco'
}

group = 'com.phonecheck'
version = '1.0'

repositories {
    mavenLocal()
    mavenCentral()
}

dependencies {
    implementation project(":model")
    implementation project(":executor")
    implementation project(":commands-module")
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.apache.commons:commons-lang3:3.12.0'

    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.6.1'
}

test {
    useJUnitPlatform()
    finalizedBy(jacocoTestReport)
}

dependencyManagement {
    imports {
        mavenBom SpringBootPlugin.BOM_COORDINATES
    }
}


jacoco {
    toolVersion = "0.8.12"
}

jacocoTestReport {
    reports {
        xml.required = false
        csv.required = false
        html.outputLocation=layout.buildDirectory.dir("$buildDir/reports/jacoco")
    }
}

jacocoTestCoverageVerification {
    dependsOn jacocoTestReport // tests are required to run before generating the report
    violationRules {
        rule {
            limit {
                minimum = 0.2
            }
        }
    }
}

check.dependsOn(jacocoTestCoverageVerification)