package com.phonecheck.syslog.android;

import com.phonecheck.command.device.android.syslog.AndroidStartSyslogCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidParseLogStreamEvent;
import com.phonecheck.model.syslog.DeviceSysLogRoutine;
import com.phonecheck.syslog.SysLogFileWriterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Map;

import static com.phonecheck.model.syslog.android.AndroidSysLogKey.START_READING_TEST_RESULTS;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidSysLogServiceTest {
    private final AndroidDevice device = new AndroidDevice();
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private CommandExecutor executor;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private Process process;
    @Mock
    private SysLogFileWriterService sysLogFileWriterService;
    @InjectMocks
    private AndroidSysLogService deviceSysLogService;

    @BeforeEach
    void setUp() throws IOException {
        device.setId("demo-device-id");
        when(executor.getProcess(any(AndroidStartSyslogCommand.class))).thenReturn(process);
        String str = "text in syslog";
        InputStream inputStream = new ByteArrayInputStream(str.getBytes());
        when(process.getInputStream()).thenReturn(inputStream);
    }

    @Test
    void startSysLogProcessForDevice()
            throws NoSuchFieldException, IllegalAccessException, IOException, InterruptedException {
        deviceSysLogService.startSysLogProcessForDevice(device);

        Field routines = AndroidSysLogService.class.getDeclaredField("deviceSysLogRoutines");
        routines.setAccessible(true);
        Map<String, DeviceSysLogRoutine> deviceSysLogRoutines =
                (Map<String, DeviceSysLogRoutine>) routines.get(deviceSysLogService);

        Thread.sleep(2000);
        // do this to stop the thread from running forever
        deviceSysLogRoutines.remove(device.getId());
        // wait for the thread to end before we verify the mocks
        Thread.sleep(4000);
        verify(executor).getProcess(any(AndroidStartSyslogCommand.class));
        verify(process).getInputStream();
    }

    @Test
    void readSysLogForDevice() throws NoSuchFieldException, IOException, InterruptedException,
            IllegalAccessException {

        StringBuilder sb = new StringBuilder();
        sb.append((START_READING_TEST_RESULTS.getKey() + "\n").repeat(1500));
        String str = sb.toString();
        InputStream inputStream = new ByteArrayInputStream(str.getBytes());
        when(process.getInputStream()).thenReturn(inputStream);

        deviceSysLogService.startSysLogProcessForDevice(device);

        Field routines = AndroidSysLogService.class.getDeclaredField("deviceSysLogRoutines");
        routines.setAccessible(true);
        Map<String, DeviceSysLogRoutine> deviceSysLogRoutines =
                (Map<String, DeviceSysLogRoutine>) routines.get(deviceSysLogService);

        Thread.sleep(2000);
        // do this to stop the thread from running forever
        deviceSysLogRoutines.remove(device.getId());
        // wait for the thread to end before we verify the mocks
        Thread.sleep(4000);

        verify(executor, times(1)).getProcess(any(AndroidStartSyslogCommand.class));
        verify(process, times(1)).getInputStream();
        verify(eventPublisher, times(1500)).publishEvent(any(AndroidParseLogStreamEvent.class));
    }

    @Test
    public void waitingForDeviceTest() throws NoSuchFieldException, IOException, InterruptedException,
            IllegalAccessException {
        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);

        // overwrite the setup values to verify waiting for device behavior
        String str = "Waiting for device to come back";
        InputStream inputStream = new ByteArrayInputStream(str.getBytes());
        when(process.getInputStream()).thenReturn(inputStream);

        deviceSysLogService.startSysLogProcessForDevice(device);

        Field routines = AndroidSysLogService.class.getDeclaredField("deviceSysLogRoutines");
        routines.setAccessible(true);
        Map<String, DeviceSysLogRoutine> deviceSysLogRoutines =
                (Map<String, DeviceSysLogRoutine>) routines.get(deviceSysLogService);

        // wait for the thread to end before we verify the mocks
        Thread.sleep(6000);
        // do this to stop the thread from running forever
        deviceSysLogRoutines.remove(device.getId());

        // The executor should get called twice since the while loop will break the first time
        // when "Waiting for device to come back" is encountered, it will kill the process and restart the syslog
        // process.
        verify(executor, times(2)).getProcess(any(AndroidStartSyslogCommand.class));
        verify(process, times(2)).getInputStream();
        // event publisher should not be called
        verify(eventPublisher, never()).publishEvent(any(AndroidParseLogStreamEvent.class));
    }

    @Test
    public void stopSysLogForDeviceTest()
            throws NoSuchFieldException, IllegalAccessException, InterruptedException {
        deviceSysLogService.startSysLogProcessForDevice(device);
        Thread.sleep(2000);

        deviceSysLogService.stopSysLogForDevice(device.getId());

        Field routines = AndroidSysLogService.class.getDeclaredField("deviceSysLogRoutines");
        routines.setAccessible(true);
        Map<String, DeviceSysLogRoutine> deviceSysLogRoutines =
                (Map<String, DeviceSysLogRoutine>) routines.get(deviceSysLogService);

        assertNull(deviceSysLogRoutines.get(device.getId()));
    }
}
