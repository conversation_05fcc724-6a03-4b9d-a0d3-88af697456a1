plugins {
    id 'java'
    id 'org.springframework.boot' version '3.0.5'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'maven-publish'
    id "io.freefair.lombok" version "*******"
    id 'checkstyle'
}

group = 'com.phonecheck'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenLocal()
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'

    implementation project(':mqtt')
    implementation project(':model')

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
}

test {
    useJUnitPlatform()
}
