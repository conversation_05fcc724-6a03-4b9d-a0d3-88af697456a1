package com.phonecheck.daemon;

import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Reacts when an exit message is published from the backend
 */
@Component
@AllArgsConstructor
public class ExitApplicationSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExitApplicationSubscriber.class);

    private final IMqttAsyncClient mqttClient;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("exit", "daemon")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Shutdown message received in daemon: {}", payload);

        System.exit(0);
    }

}
