server.port=8081

# mqtt configurations
mqtt.scheme=tcp://
mqtt.host=127.0.0.1
mqtt.port=1883
mqtt.server.enabled=true
mqtt.autoReconnect=true
mqtt.maxReconnectDelay=-1
mqtt.connectionTimeout=3600
mqtt.keepAlive=120
mqtt.maxInFlight=32000
mqtt.client.id=phonecheck-daemon

# logging configurations
logging.level.com.phonecheck=INFO
logging.path=${user.home}/AppData/Local/PhoneCheck3/logs

management.endpoints.web.exposure.include=*

# spring configurations
spring.main.web-application-type=servlet
spring.jmx.enabled=false
