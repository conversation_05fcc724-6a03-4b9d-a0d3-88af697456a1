package com.phonecheck.parser.util;

import com.phonecheck.model.ios.ProcessName;
import com.phonecheck.parser.device.ios.process.GetIosProcessIdParser;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class GetIosProcessIdParserTest {

    @Test
    void testParseExpectedInput() throws IOException {
        final String input = "48 powerd";
        final Map<ProcessName, String> results = new GetIosProcessIdParser().parse(input, ProcessName.POWERD,
                ProcessName.SHARINGD);
        final Map<ProcessName, String> expectedResult = new HashMap<>();
        expectedResult.put(ProcessName.POWERD, "48");
        Assertions.assertNotNull(results);
        Assertions.assertEquals(expectedResult, results);
    }

    @Test
    void testParseUnexpectedInput() throws IOException {
        final String input = "48    powerzzxdd";
        final Map<ProcessName, String> results = new GetIosProcessIdParser().parse(input, ProcessName.POWERD,
                ProcessName.SHARINGD);
        final Map<ProcessName, String> expectedResult = new HashMap<>();
        Assertions.assertNotNull(results);
        Assertions.assertEquals(expectedResult, results);
    }
}
