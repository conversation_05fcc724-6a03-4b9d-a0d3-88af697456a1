package com.phonecheck.parser.device.ios.info;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class IosAppleIdInfoParserTest {
    @Test
    public void testIcloudAppleIdInputTrue() throws IOException {
        final String input = "AppleID: <EMAIL>";
        String result = new IosAppleIdInfoParser().parse(input);
        Assertions.assertEquals("<EMAIL>", result);
    }
}
