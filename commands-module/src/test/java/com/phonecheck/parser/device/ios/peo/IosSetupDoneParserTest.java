package com.phonecheck.parser.device.ios.peo;

import com.phonecheck.model.status.SetupDoneStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class IosSetupDoneParserTest {
    private static final String FULL_OUTPUT_DONE = """
            CKPerBootTasks[1]:
             0: CKAcccountInfoCacheReset
            CKStartupTime: 1742967520
            GuessedCountry[1]:
             0: PK
            HSA2UpgradeMiniBuddy3Ran: true
            LockdownSetLanguage: true
            LockdownSetLocale: true
            PBAppActivity2Presented: false
            PBDiagnostics4Presented: false
            PaymentMiniBuddy4Ran: true
            PhoneNumberPermissionPresentedKey: true
            SetupDone: true
            SetupFinishedAllSteps: true
            SetupLastExit: 2025-03-26T10:41:22Z
            SetupState: SetupUsingAssistant
            SetupVersion: 11
            chronicle:
             features[0]:
            lastPrepareLaunchSentinel[2]:
             0: 2025-03-26T11:19:03Z
             1: 0
            setupMigratorVersion: 12
            RegulatoryModelNumber: A1905
            DeviceEnclosureColor: 7
            TotalDiskCapacity: 64000000000
            BatteryCurrentCapacity: 57
            SetupDone: true
            IsAssociated: false""";

    private static final String OUTPUT_WITH_SETUP_LAST_EXIT = """
            SomeRandomInfo: value
            SetupLastExit: 2025-03-26T10:41:22Z
            OtherInfo: data""";

    private static final String OUTPUT_WITH_SETUP_FINISHED = """
            RandomData: 123
            SetupFinishedAllSteps: true
            MoreData: abc""";

    private static final String OUTPUT_WITH_SCREEN_TIME = """
            ScreenTimePresented: true
            AdditionalInfo: xyz""";

    private static final String OUTPUT_NOT_DONE = """
            SetupDone: false
            SetupFinishedAllSteps: false
            SetupLastExit: invalid-date
            ScreenTimePresented: false""";

    @Test
    void testFullOutputSetupDone() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse(FULL_OUTPUT_DONE);
        Assertions.assertEquals(SetupDoneStatus.DONE, result);
    }

    @Test
    void testSetupLastExitOnly() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse(OUTPUT_WITH_SETUP_LAST_EXIT);
        Assertions.assertEquals(SetupDoneStatus.DONE, result);
    }

    @Test
    void testSetupFinishedOnly() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse(OUTPUT_WITH_SETUP_FINISHED);
        Assertions.assertEquals(SetupDoneStatus.DONE, result);
    }

    @Test
    void testScreenTimePresentedOnly() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse(OUTPUT_WITH_SCREEN_TIME);
        Assertions.assertEquals(SetupDoneStatus.DONE, result);
    }

    @Test
    void testSetupNotDone() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse(OUTPUT_NOT_DONE);
        Assertions.assertEquals(SetupDoneStatus.NOT_DONE, result);
    }

    @Test
    void testNullOutput() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse(null);
        Assertions.assertEquals(SetupDoneStatus.NOT_DONE, result);
    }

    @Test
    void testEmptyOutput() throws IOException {
        final SetupDoneStatus result = new IosSetupDoneParser().parse("");
        Assertions.assertEquals(SetupDoneStatus.NOT_DONE, result);
    }

    @Test
    void testInvalidSetupLastExit() throws IOException {
        final String input = "SetupLastExit: invalid-date";
        final SetupDoneStatus result = new IosSetupDoneParser().parse(input);
        Assertions.assertEquals(SetupDoneStatus.NOT_DONE, result);
    }
}