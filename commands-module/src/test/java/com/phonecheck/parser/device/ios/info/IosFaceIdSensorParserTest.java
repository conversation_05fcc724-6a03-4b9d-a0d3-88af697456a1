package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class IosFaceIdSensorParserTest {

    @BeforeAll
    public static void setup() {
        IosSysLogKey.IOS_SYS_LOG_KEYS.addAll(IosSysLogKey.FALLBACK_FACEID_KEYS);
    }

    @Test
    public void testFaceIdParserExpectedInput1() {
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>:" +
                " [HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2 " +
                "_childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] PearlCamStateEntryProc_FACE_FOUND\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        WorkingStatus faceIdWorkingStatus = new IosFaceIdSensorParser().parse(input);

        Assertions.assertEquals(WorkingStatus.YES, faceIdWorkingStatus);
    }

    @Test
    public void testFaceIdParserExpectedInput2() {
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>: " +
                "[HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2" +
                " _childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Error>: powerOnCamera\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        WorkingStatus faceIdWorkingStatus = new IosFaceIdSensorParser().parse(input);

        Assertions.assertEquals(WorkingStatus.NO, faceIdWorkingStatus);
    }

    @Test
    public void testFaceIdParserUnExpectedInput() {
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>: " +
                "[HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2 " +
                "_childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962]" +
                " <Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: powerOnCamera\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        WorkingStatus faceIdWorkingStatus = new IosFaceIdSensorParser().parse(input);

        Assertions.assertNull(faceIdWorkingStatus);
    }
}