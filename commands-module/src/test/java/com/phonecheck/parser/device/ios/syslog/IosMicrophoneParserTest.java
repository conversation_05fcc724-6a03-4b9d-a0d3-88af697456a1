package com.phonecheck.parser.device.ios.syslog;

import com.phonecheck.model.device.IosDevice;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class IosMicrophoneParserTest {
    @Test
    public void testMicrophoneParserExpectedInput1() {
        final String mic = "0.7588";
        final String fMic = "0.8969";
        final String vMic = "0.3906";
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>:" +
                " [HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2 " +
                "_childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] (AudiothresholdDataStart-Microphone=" +
                mic + ",Front Microphone=" + fMic + ",Video Microphone=" + vMic + "-AudiothresholdDataEnd)\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        IosDevice device = new IosDevice();
        boolean foundMicrophone = new IosMicrophoneParser().parse(device, input);

        Assertions.assertTrue(foundMicrophone);
        Assertions.assertEquals(mic, device.getMicrophone());
        Assertions.assertEquals(fMic, device.getFrontMicrophone());
        Assertions.assertEquals(vMic, device.getVideoMicrophone());
    }

    @Test
    public void testMicrophoneParserExpectedInput2() {
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>:" +
                " [HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2 " +
                "_childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] (AudiothresholdDataStart-Microphone=" +
                ",Front Microphone=,Video Microphone=-AudiothresholdDataEnd)\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962]" +
                " <Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        IosDevice device = new IosDevice();
        boolean foundMicrophone = new IosMicrophoneParser().parse(device, input);

        Assertions.assertTrue(foundMicrophone);
        Assertions.assertEquals("", device.getMicrophone());
        Assertions.assertEquals("", device.getFrontMicrophone());
        Assertions.assertEquals("", device.getVideoMicrophone());
    }

    @Test
    public void testMicrophoneParserUnExpectedInput() {
        final String mic = "0.7588";
        final String fMic = "0.8969";
        final String vMic = "0.3906";
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>:" +
                " [HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2" +
                " _childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] (Microphone=" + mic + ",Front Microphone=" +
                fMic + ",Video Microphone=" + vMic + "-AudiothresholdDataEnd)\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        IosDevice device = new IosDevice();
        boolean foundMicrophone = new IosMicrophoneParser().parse(device, input);

        Assertions.assertFalse(foundMicrophone);
        Assertions.assertNull(device.getMicrophone());
        Assertions.assertNull(device.getFrontMicrophone());
        Assertions.assertNull(device.getVideoMicrophone());
    }
}