package com.phonecheck.parser.device.ios.mount;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Iterator;
import java.util.List;

public class IosListMountedDiskImagesParserTest {

    @Test
    @DisplayName("One device is parsed")
    void testParseOne() throws IOException {
        final String output = "ImageSignature[1]:\n"
                + "0: HlNRRj+4ep5I08MG3qvbiS=\n"
                + "Status: Complete";

        final List<String> results = new IosListMountedDiskImagesParser().parse(output);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(1, results.size());

        final String hash = results.iterator().next();
        Assertions.assertNotNull(hash);
        Assertions.assertEquals("HlNRRj+4ep5I08MG3qvbiS=", hash);
    }

    @Test
    @DisplayName("Multiple hashes are parsed")
    void testParseMultiple() throws IOException {
        final String output = "ImageSignature[1]:\n"
                + "0: HlNRRj+4ep5I08MG3qvbiS=\n"
                + "1: OggVorbisSaysWhat=\n"
                + "Status: Complete";

        final List<String> results = new IosListMountedDiskImagesParser().parse(output);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(2, results.size());

        Iterator<String> iterator = results.iterator();
        String hash = iterator.next();
        Assertions.assertNotNull(hash);
        Assertions.assertEquals("HlNRRj+4ep5I08MG3qvbiS=", hash);

        hash = iterator.next();
        Assertions.assertNotNull(hash);
        Assertions.assertEquals("OggVorbisSaysWhat=", hash);
    }

    @Test
    @DisplayName("Empty output is parsed")
    void testParseEmpty() throws IOException {
        final String output = "";

        final List<String> results = new IosListMountedDiskImagesParser().parse(output);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(0, results.size());
    }

    @Test
    @DisplayName("Null output is parsed")
    void testParseNull() throws IOException {
        final List<String> results = new IosListMountedDiskImagesParser().parse(null);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(0, results.size());
    }

}
