package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.OsChecker;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosDfuParserTest {


    @InjectMocks
    private IosDfuParser iosDfuParser;
    @Mock
    private OsChecker osChecker;

    @Test
    public void testParseResponseForDfuCommand() {
        String response = "Found DFU: [05ac:1281] ver=0000, devnum=2, cfg=1, intf=0, " +
                "path=\"1-1.1\", alt=0, name=\"UNKNOWN\", " +
                "serial=\"SDOM:01 CPID:8003 CPRV:01 CPFM:03 SCEP:01 BDID:04 ECID:00167110000A2C26 " +
                "IBFL:1D SRNM:[F17QGZGCGRY5]\"\n";
        when(osChecker.isMac()).thenReturn(true);
        Set<IosDevice> dfuEnabledDevices = iosDfuParser.parse(response);
        Assertions.assertNotNull(dfuEnabledDevices);
        Assertions.assertEquals(1, dfuEnabledDevices.size());
        IosDevice outputDevice = dfuEnabledDevices.stream().findFirst().get();
        Assertions.assertEquals("167110000A2C26",
                outputDevice.getIdInRecoveryMode());
        Assertions.assertEquals("04", outputDevice.getBdid());
        Assertions.assertEquals("167110000A2C26", outputDevice.getEcid());
        Assertions.assertEquals("8003", outputDevice.getCpid());
    }

    @Test
    public void testParseResponseForInvalidResponse() {
        String response = "Found DFU: [05ac:1281] ver=0000, devnum=2, cfg=1, intf=0, " +
                "path=\"1-1.1\", alt=0, name=\"UNKNOWN\", " +
                "serial=SDOM:01 CPID:8003 CPRV:01 CPFM:03 SCEP:01";
        when(osChecker.isMac()).thenReturn(true);
        Set<IosDevice> dfuEnabledDevices = iosDfuParser.parse(response);
        Assertions.assertEquals(0, dfuEnabledDevices.size());
    }

    @Test
    public void testParseResponseForDfuCommandForWindows() {
        String response = "recovery:SDOM:01_CPID:8003_CPRV:01_CPFM:03_SCEP:01_BDID:04_ECID:" +
                "001505D230DB8226_IBFL:1D_SRNM:[F18RT0RZGRYG]";
        when(osChecker.isMac()).thenReturn(false);
        Set<IosDevice> dfuEnabledDevices = iosDfuParser.parse(response);
        Assertions.assertNotNull(dfuEnabledDevices);
        Assertions.assertEquals(1, dfuEnabledDevices.size());
        IosDevice outputDevice = dfuEnabledDevices.stream().findFirst().get();
        Assertions.assertEquals("1505D230DB8226",
                outputDevice.getIdInRecoveryMode());
        Assertions.assertEquals("04", outputDevice.getBdid());
        Assertions.assertEquals("1505D230DB8226", outputDevice.getEcid());
        Assertions.assertEquals("8003", outputDevice.getCpid());
    }

    @Test
    public void testParseResponseForInvalidResponseForWindows() {
        String response = "recovery:SDOM:01_CPID:8003_CPRV:01_CPFM:03_SCEP:01";
        when(osChecker.isMac()).thenReturn(false);
        Set<IosDevice> dfuEnabledDevices = iosDfuParser.parse(response);
        Assertions.assertEquals(0, dfuEnabledDevices.size());
    }
}
