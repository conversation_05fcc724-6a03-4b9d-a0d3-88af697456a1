package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.status.WorkingStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class IosTouchIdCommand2ParserTest {
    @Test
    void testForCommand2OutputNO() throws Exception {
        String output = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" " +
                "\"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n" +
                "<plist version=\"1.0\">\n" +
                "<dict>\n" +
                "        <key>IORegistry</key>\n" +
                "        <dict>\n" +
                "                <key>CFBundleIdentifier</key>\n" +
                "                <string>com.apple.driver.AppleMesaSEPDriver</string>\n" +
                "                <key>CFBundleIdentifierKernel</key>\n" +
                "                <string>com.apple.driver.AppleMesaSEPDriver</string>\n" +
                "                <key>IOClass</key>\n" +
                "                <string>AppleMesaSEPDriver</string>\n" +
                "                <key>IOGeneralInterest</key>\n" +
                "                <string>IOCommand is not serializable</string>\n" +
                "                <key>IOMatchCategory</key>\n" +
                "                <string>com_apple_driver_AppleMesaSEPDriver</string>\n" +
                "                <key>IOMatchedAtBoot</key>\n" +
                "                <true/>\n" +
                "                <key>IONameMatch</key>\n" +
                "                <string>sep-endpoint,sbio</string>\n" +
                "                <key>IONameMatched</key>\n" +
                "                <string>sep-endpoint,sbio</string>\n" +
                "                <key>IOPowerManagement</key>\n" +
                "                <dict>\n" +
                "                        <key>CapabilityFlags</key>\n" +
                "                        <integer>2</integer>\n" +
                "                        <key>CurrentPowerState</key>\n" +
                "                        <integer>1</integer>\n" +
                "                        <key>MaxPowerState</key>\n" +
                "                        <integer>1</integer>\n" +
                "                </dict>\n" +
                "                <key>IOProbeScore</key>\n" +
                "                <integer>1000</integer>\n" +
                "                <key>IOProviderClass</key>\n" +
                "                <string>AppleSEPDeviceService</string>\n" +
                "                <key>MesaCal123BlobSource</key>\n" +
                "                <string>FDR</string>\n" +
                "        </dict>\n" +
                "</dict>\n" +
                "</plist>\n";
        WorkingStatus workingStatus = new IosTouchIdCommand2Parser().parse(output);
        Assertions.assertEquals(WorkingStatus.NO, workingStatus);
    }

    @Test
    void testForCommand2OutputPending() throws Exception {
        String output = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\"" +
                " \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n" +
                "<plist version=\"1.0\">\n" +
                "<dict>\n" +
                "        <key>IORegistry</key>\n" +
                "        <dict>\n" +
                "                <key>CFBundleIdentifier</key>\n" +
                "                <string>com.apple.driver.AppleMesaSEPDriver</string>\n" +
                "                <key>CFBundleIdentifierKernel</key>\n" +
                "                <string>com.apple.driver.AppleMesaSEPDriver</string>\n" +
                "                <key>IOClass</key>\n" +
                "                <string>AppleMesaSEPDriver</string>\n" +
                "                <key>IOGeneralInterest</key>\n" +
                "                <string>IOCommand is not serializable</string>\n" +
                "                <key>IOMatchCategory</key>\n" +
                "                <string>com_apple_driver_AppleMesaSEPDriver</string>\n" +
                "                <key>IOMatchedAtBoot</key>\n" +
                "                <true/>\n" +
                "                <key>IONameMatch</key>\n" +
                "                <string>sep-endpoint,sbio</string>\n" +
                "                <key>IONameMatched</key>\n" +
                "                <string>sep-endpoint,sbio</string>\n" +
                "                <key>IOPowerManagement</key>\n" +
                "                <dict>\n" +
                "                        <key>CapabilityFlags</key>\n" +
                "                        <integer>2</integer>\n" +
                "                        <key>CurrentPowerState</key>\n" +
                "                        <integer>1</integer>\n" +
                "                        <key>MaxPowerState</key>\n" +
                "                        <integer>1</integer>\n" +
                "                </dict>\n" +
                "                <key>IOProbeScore</key>\n" +
                "                <integer>1000</integer>\n" +
                "                <key>IOProviderClass</key>\n" +
                "                <string>AppleSEPDeviceService</string>\n" +
                "                <key>MesaCalBlobSource</key>\n" +
                "                <string>FDR</string>\n" +
                "        </dict>\n" +
                "</dict>\n" +
                "</plist>\n";
        WorkingStatus workingStatus = new IosTouchIdCommand2Parser().parse(output);
        Assertions.assertEquals(WorkingStatus.PENDING, workingStatus);
    }

}
