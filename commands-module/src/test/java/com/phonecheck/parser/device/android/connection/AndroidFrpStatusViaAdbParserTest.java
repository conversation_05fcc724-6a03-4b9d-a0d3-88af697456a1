package com.phonecheck.parser.device.android.connection;

import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.parser.device.android.info.AndroidFrpStatusViaAdbParser;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AndroidFrpStatusViaAdbParserTest {
    @Test
    public void testParseFrpOn() {
        String response = "Accounts:\n" +
                "com.google.account1\n" +
                "Accounts History\n" +
                "action_account_add\n";

        DeviceLock deviceLock = new AndroidFrpStatusViaAdbParser().parse(response);

        assertEquals(DeviceLock.ON, deviceLock);
    }

    @Test
    public void testParseFrpOff() {
        String response = "Accounts:\n" +
                "non-google-account1\n" +
                "Accounts History\n" +
                "action_account_remove\n";

        DeviceLock deviceLock = new AndroidFrpStatusViaAdbParser().parse(response);

        assertEquals(DeviceLock.OFF, deviceLock);
    }

}
