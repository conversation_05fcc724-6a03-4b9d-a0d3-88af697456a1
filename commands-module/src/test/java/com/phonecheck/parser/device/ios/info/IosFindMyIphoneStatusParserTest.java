package com.phonecheck.parser.device.ios.info;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class IosFindMyIphoneStatusParserTest {

    @Test
    public void testFindMyIphoneExpectedInputTrue() throws IOException {
        final String input = "false\ntrue";
        boolean result = new IosFindMyIphoneStatusParser().parse(input);
        Assertions.assertTrue(result);
    }

    @Test
    public void testFindMyIphoneExpectedInputFalse() throws IOException {
        final String input = "false";
        boolean result = new IosFindMyIphoneStatusParser().parse(input);
        Assertions.assertFalse(result);
    }
}
