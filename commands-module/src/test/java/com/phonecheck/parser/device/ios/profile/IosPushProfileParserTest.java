package com.phonecheck.parser.device.ios.profile;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class IosPushProfileParserTest {

    @Test
    void expectedInputTest() throws IOException {
        final String input = "success asdv";
        final Boolean result = new IosPushProfileParser().parse(input);
        final Boolean expectedResult = true;
        Assertions.assertNotNull(result);
        Assertions.assertEquals(result, expectedResult);
    }
}
