package com.phonecheck.parser.system.mac;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

class GetMacProcessIdParserTest {
    @Test
    void testParseExpectedInput() throws IOException {
        final String input = """
                COMMAND     PID  USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
                redis-ser 29980 apple    6u  IPv4 0x61d726b482a9bcb9      0t0  TCP *:6379 (LISTEN)
                redis-ser 29980 apple    7u  IPv6 0x61d726b9493b68b1      0t0  TCP *:6379 (LISTEN)""";
        final String result = new GetMacProcessIdParser().parse(input);
        Assertions.assertNotNull(result);
        Assertions.assertEquals("29980", result);
    }

    @Test
    void testParseUnexpectedInput() throws IOException {
        final String input = """
                COMMAND     PID  USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
                redis-ser 29980 apple    6u  IPv4 0x61d726b482a9bcb9      0t0  TCP *:6379
                redis-ser 29980 apple    7u  IPv6 0x61d726b9493b68b1      0t0  TCP *:6379""";
        final String result = new GetMacProcessIdParser().parse(input);
        Assertions.assertNull(result);
    }
}