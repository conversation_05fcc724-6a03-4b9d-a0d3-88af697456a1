package com.phonecheck.parser.print.mac;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parser class for Printer List command to retrieve the printer names from
 * Printer List command response string.
 * <p>
 * Sample Output:
 * DYMO_LabelWriter_450_Turbo accepting requests since Fri Dec 23 14:30:16 2022
 * Zebra_Technologies_ZTC_GK420t accepting requests since Wed Apr 26 10:57:05 2023
 */
@Component
public class MacPrintersListParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(MacPrintersListParser.class);

    public List<String> parse(final String output, final String localizedPrinterKeyword) throws IOException {
        LOGGER.info("Parsing printers list output, the localized printer keyword is: {}",
                localizedPrinterKeyword);
        List<String> printers = new ArrayList<>();
        String[] lines = output.split("\n");

        for (String line : lines) {
            if (!line.isBlank()) {
                line = line.trim().replace("„", "").replace("“", "");
                String printerName = extractPrinterName(line, localizedPrinterKeyword);
                if (printerName != null && !printerName.isEmpty()) {
                    printers.add(printerName);
                }
            }
        }

        return printers;
    }

    /**
     * Method to retrieve printer name properly
     *
     * @param line                    output line
     * @param localizedPrinterKeyword localized 'printer' keyword
     * @return printer name
     */
    private String extractPrinterName(final String line, final String localizedPrinterKeyword) {
        // Case 1: Line contains localized printer keyword
        Pattern pattern1 = Pattern.compile("(?i)\\b" + Pattern.quote(localizedPrinterKeyword) + "\\s+([\\w-]+)");
        Matcher matcher1 = pattern1.matcher(line);
        if (matcher1.find()) {
            return matcher1.group(1);
        }

        // Case 2: Line contains "printer" keyword
        Pattern pattern2 = Pattern.compile("(?i)\\b" + Pattern.quote("printer") + "\\s+([\\w-]+)");
        Matcher matcher2 = pattern2.matcher(line);
        if (matcher2.find()) {
            return matcher2.group(1);
        }

        // Case 4: Fallback - take the first word if no other pattern matches
        String[] words = line.split("\\s+");
        if (words.length > 1 && words[0].equalsIgnoreCase(localizedPrinterKeyword)) {
            return words[1];
        } else if (words.length > 0) {
            return words[0];
        }

        return null;
    }
}