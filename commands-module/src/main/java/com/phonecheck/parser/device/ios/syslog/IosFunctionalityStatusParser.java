package com.phonecheck.parser.device.ios.syslog;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses device functionality status from syslog and sets device property.
 * i.e. brightStarFunctionality
 */
@Component
public class IosFunctionalityStatusParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosFunctionalityStatusParser.class);

    private final Pattern functionalityPattern = Pattern.compile(
            "(" + IosSysLogKey.FUNCTIONALITY_STATUS_START.getKey() +
                    ")(.*)(" + IosSysLogKey.FUNCTIONALITY_STATUS_END.getKey() + ")"
    );

    public boolean parse(final IosDevice device, final String sysLogResponse) {
        LOGGER.debug("Syslog parsing output: {}", sysLogResponse);

        Matcher matcher = functionalityPattern.matcher(sysLogResponse);
        if (matcher.find()) {
            String value = matcher.group(2);
            if (StringUtils.isNotBlank(value)) {
                device.setBrightStarFunctionality(value);
            }
            return true;
        }

        return false;
    }
}
