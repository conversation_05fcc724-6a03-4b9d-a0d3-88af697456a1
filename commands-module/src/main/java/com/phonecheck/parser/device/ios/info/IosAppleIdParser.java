package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.device.IosDevice;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Parses old and current AppleId from syslog and sets device properties.
 * i.e. oldAppleId, currentAppleId
 */
@Component
public class IosAppleIdParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosAppleIdParser.class);

    private static final String APPLE_ID_CHANGED_MARKER = "Apple ID changed from";
    private static final String TO_KEYWORD = "to";
    private static final String NULL_KEYWORD = "null";

    public boolean parse(final IosDevice device, final String sysLogResponse) {
        LOGGER.info("Syslog parsing output: {}", sysLogResponse);
        if (sysLogResponse.contains(APPLE_ID_CHANGED_MARKER)) {
            String oldAppleId;
            String currentAppleId;

            int oldAppleIdStartIndex =
                    sysLogResponse.indexOf(APPLE_ID_CHANGED_MARKER) + APPLE_ID_CHANGED_MARKER.length() + 1;
            int oldAppleIdEndIndex = sysLogResponse.indexOf(TO_KEYWORD, oldAppleIdStartIndex);
            oldAppleId = sysLogResponse.substring(oldAppleIdStartIndex, oldAppleIdEndIndex).trim();

            int currentAppleIdEndIndex = sysLogResponse.indexOf("\n", oldAppleIdEndIndex);
            if (currentAppleIdEndIndex == -1) {
                currentAppleIdEndIndex = sysLogResponse.length();
            }
            currentAppleId = sysLogResponse.substring(oldAppleIdEndIndex + 2, currentAppleIdEndIndex).trim();

            if (StringUtils.isNotBlank(oldAppleId) && !NULL_KEYWORD.equalsIgnoreCase(oldAppleId)) {
                device.setOldAppleId(oldAppleId);
            }
            if (StringUtils.isNotBlank(currentAppleId) && !NULL_KEYWORD.equalsIgnoreCase(currentAppleId)) {
                device.setCurrentAppleId(currentAppleId);
            }
            return true;
        }
        return false;
    }
}
