package com.phonecheck.parser.device.ios.syslog;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Parses OEM display notice from syslog and sets its property in device object.
 */
@Component
public class IosOemDisplayNoticeParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosOemDisplayNoticeParser.class);
    private static final String OEM_DISPLAY_NOTICE = "Unable to verify this iPhone has a genuine Apple display";

    public boolean parse(final IosDevice device, final String sysLogResponse) {
        LOGGER.debug("Syslog OEM display notice parsing output: {}", sysLogResponse);

        if (StringUtils.isBlank(device.getOemDisplayNotice())) {
            if (StringUtils.containsIgnoreCase(sysLogResponse, IosSysLogKey.OEM_DISPLAY_NOTICE.getKey())) {
                LOGGER.info("Setting OEM display notice as: {}", OEM_DISPLAY_NOTICE);
                device.setOemDisplayNotice(OEM_DISPLAY_NOTICE);
                return true;
            }
        } else {
            LOGGER.info("OEM display notice is already set with value {}, so not overriding it",
                    device.getOemDisplayNotice());
        }
        return false;
    }
}
