package com.phonecheck.parser.device.ios.info;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Returns appleId if exists else null
 */
@Component
public class IosAppleIdInfoParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosAppleIdInfoParser.class);

    public String parse(final String output) throws IOException {
        LOGGER.debug("GetAppleId info command parsing output: {}", output);

        if (StringUtils.isEmpty(output)) {
            return null;
        }
        Matcher matcher = Pattern.compile("AppleID: (.*@.*)").matcher(output);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
