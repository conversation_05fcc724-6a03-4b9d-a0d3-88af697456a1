package com.phonecheck.parser.device.ios.info;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses the disk size of the connected device.
 * <p>
 * DiskSize output example:
 * TotalDataAvailable: 123182940160
 * TotalDataCapacity: 247794872320
 * TotalDiskCapacity: 256000000000
 */
@Component
public class IosDiskSizeParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosDiskSizeParser.class);
    private static final String TOTAL_DISK_MARKER = "TotalDiskCapacity: ";
    private static final String LOCKDOWND_ERROR_MARKER = "ERROR: Could not connect to lockdownd";

    public Long parse(final String output) throws IOException {
        LOGGER.info("DiskSize Parsing command output: {}", output);
        if (StringUtils.isEmpty(output) || StringUtils.containsIgnoreCase(output, LOCKDOWND_ERROR_MARKER)) {
            return null;
        }

        int index = output.indexOf(TOTAL_DISK_MARKER);
        if (index < 0) {
            // if index not found then output might be disk size in long as it is or
            // disk size plus some warning so, we will retrieve long from output string

            final Pattern pattern = Pattern.compile("\\d+");
            final Matcher matcher = pattern.matcher(output);
            if (matcher.find()) {
                return Long.valueOf(matcher.group());
            }
        }
        index += TOTAL_DISK_MARKER.length();
        String result = output.substring(index, index + 16).trim();
        result = result.substring(0, result.indexOf("\n"));
        return Long.valueOf(result);
    }
}
