package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.ios.MdmInfoProperty;
import com.phonecheck.model.status.SetupDoneStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Parses CloudConfig info
 */
@Component
public class IosCloudConfigInfoResponseParser {
    public static final Logger LOGGER = LoggerFactory.getLogger(IosCloudConfigInfoResponseParser.class);
    private static final List<String> PROPERTIES_TO_BE_PARSED = List.of("AllowPairing",
            "CloudConfigurationUIComplete",
            "ConfigurationWasApplied",
            "IsMDMUnremovable",
            "IsMandatory",
            "IsSupervised",
            "OrganizationName",
            "Status",
            "ConfigurationSource",
            "PostSetupProfileWasInstalled",
            "OrganizationName",
            "ConfigurationURL"
    );
    private static final List<String> NON_BOOLEAN_PROPERTIES = List.of(MdmInfoProperty.CONFIGURATION_SOURCE.getName(),
            MdmInfoProperty.STATUS.getName(),
            MdmInfoProperty.ORGANIZATION_NAME.getName()
    );
    private static final String EVERYTHING_WORKING_MARKER = "Every Thing Working..\n";

    public Map<MdmInfoProperty, String> parse(final String output, final SetupDoneStatus setUpDone) throws Exception {
        LOGGER.debug("CloudConfig Response Parser input: {}", output);
        final Map<MdmInfoProperty, String> results = new HashMap<>();
        // Check if null or valid xml
        if (StringUtils.isEmpty(output) || !output.matches("(?s).*(<(\\w+)[^>]*>.*</\\2>|<(\\w+)[^>]*/>).*")) {
            return results;
        }
        // Remove the EVERYTHING_WORKING_MARKER and extra xml before loading xml
        String str = output.replace(EVERYTHING_WORKING_MARKER, "");
        String[] splits = str.split("(?<=</plist>)");
        // load xml string into a Document object
        final Document doc = loadXMLFromString(splits[0]);
        // normalize xml structure
        if (null == doc) {
            return results;
        }
        doc.getDocumentElement().normalize();
        // iterate through the whole xml
        NodeList nodeList = doc.getElementsByTagName("*");
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                // if node is <key> and value is present in the list of properties needed to be parsed
                if (node.getNodeName().equals("key") && PROPERTIES_TO_BE_PARSED.contains(node.getTextContent())) {
                    i++;
                    if (NON_BOOLEAN_PROPERTIES.contains(node.getTextContent())) {
                        results.put(MdmInfoProperty.getByName(
                                node.getTextContent()), nodeList.item(i).getTextContent());
                    } else {
                        if (nodeList.item(i).getNodeName().equals("false")
                                || nodeList.item(i).getNodeName().equals("true")) {
                            results.put(MdmInfoProperty.getByName(
                                    node.getTextContent()), nodeList.item(i).getNodeName());
                        } else {
                            results.put(MdmInfoProperty.getByName(
                                    node.getTextContent()), nodeList.item(i).getTextContent());
                        }
                    }
                }
            }
        }

        // Insert "MDMStatus" field in the results, so it can be returned from iosUtils.

        if (results.containsKey(MdmInfoProperty.ORGANIZATION_NAME) && results.get(MdmInfoProperty.ORGANIZATION_NAME)
                .equalsIgnoreCase("PhoneCheck")) {
            results.put(MdmInfoProperty.MDM_STATUS, "-2");
            return results; // MDM ON for PhoneCheck
        }

        Boolean isSupervised = null;
        boolean isMDMUnremovable = false;
        String configurationUrl = "";
        int configurationSource = -1;
        boolean isPostSetupProfileInstalled = false;

        if (results.containsKey(MdmInfoProperty.IS_SUPERVISED)) {
            isSupervised = Boolean.parseBoolean(results.get(MdmInfoProperty.IS_SUPERVISED));
        }
        if (results.containsKey(MdmInfoProperty.CONFIGURATION_URL)) {
            configurationUrl = results.get(MdmInfoProperty.CONFIGURATION_URL);
        }
        if (results.containsKey(MdmInfoProperty.MDM_UNREMOVABLE)) {
            String value = results.get(MdmInfoProperty.MDM_UNREMOVABLE);
            if (value.matches("\\d+")) {  // Check if the value is a digit
                isMDMUnremovable = Integer.parseInt(value) == 1; // True if 1, false otherwise
                // set the value to a boolean as that is what the code will expect
                results.put(MdmInfoProperty.MDM_UNREMOVABLE, Boolean.toString(isMDMUnremovable));
            } else {
                isMDMUnremovable = Boolean.parseBoolean(value); // Handle boolean string
            }
        }
        if (results.containsKey(MdmInfoProperty.CONFIGURATION_SOURCE)) {
            configurationSource = Integer.parseInt(results.get(MdmInfoProperty.CONFIGURATION_SOURCE));
        }
        if (results.containsKey(MdmInfoProperty.POST_SETUP_PROFILE_WAS_INSTALLED)) {
            isPostSetupProfileInstalled = Boolean.parseBoolean(
                    results.get(MdmInfoProperty.POST_SETUP_PROFILE_WAS_INSTALLED));
        }

        if (Boolean.TRUE.equals(isSupervised)
                || StringUtils.isNotBlank(configurationUrl)
                || isMDMUnremovable) {
            results.put(MdmInfoProperty.MDM_STATUS, "-38"); // MDM ON
        } else if ((isPostSetupProfileInstalled
                || configurationSource > 0) && SetupDoneStatus.NOT_DONE.equals(setUpDone)) {
            results.put(MdmInfoProperty.MDM_STATUS, "-3");  // Preconfigured
        } else if (Boolean.FALSE.equals(isSupervised)
                || configurationSource == 0) {
            results.put(MdmInfoProperty.MDM_STATUS, "-1");  // MDM OFF
        } else {
            results.put(MdmInfoProperty.MDM_STATUS, "0");   // Prepare Device more.
        }

        return results;
    }

    private Document loadXMLFromString(final String xml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        DocumentBuilder builder = factory.newDocumentBuilder();
        InputSource is = new InputSource(new StringReader(xml));
        Document result = builder.parse(is);
        return result;
    }
}
