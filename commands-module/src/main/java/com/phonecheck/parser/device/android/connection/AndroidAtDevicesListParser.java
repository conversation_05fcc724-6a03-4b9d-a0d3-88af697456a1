package com.phonecheck.parser.device.android.connection;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AtPort;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Output examples:
 * Mac: /dev/cu.usbmodem142302:ce0317131441621302
 * Windows: ce0317131441621301
 */
@Component
@AllArgsConstructor
public class AndroidAtDevicesListParser {
    private ObjectMapper objectMapper;

    public List<AtPort> parse(final String output) throws IOException {
        List<AtPort> atPorts = new ArrayList<>();
        if (StringUtils.isBlank(output)
                || output.contains("No serial ports found!")) {
            return atPorts;
        }

        atPorts = objectMapper.readValue(output, new TypeReference<>() {
        });

        return atPorts;
    }
}
