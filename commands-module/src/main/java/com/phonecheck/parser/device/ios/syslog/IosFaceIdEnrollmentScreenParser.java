package com.phonecheck.parser.device.ios.syslog;

import com.phonecheck.model.syslog.ios.IosSysLogKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses the status of face id enrollment screen. Returns true if user is on face id enrollment screen
 */
@Component
public class IosFaceIdEnrollmentScreenParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosFaceIdEnrollmentScreenParser.class);

    private final Pattern manualFaceIdPattern = Pattern.compile(
            "(" + IosSysLogKey.MANUAL_FACE_ID_ENROLLMENT_SCREEN_OPEN_2.getKey() +
                    ")(.*)(" + IosSysLogKey.MANUAL_FACE_ID_ENROLLMENT_SCREEN_OPEN_3.getKey() + ")(.*)"
    );

    public boolean parse(final String sysLogResponse) {
        LOGGER.debug("Syslog parsing output: {}", sysLogResponse);

        Matcher matcher = manualFaceIdPattern.matcher(sysLogResponse);
        return matcher.find() || sysLogResponse.contains(IosSysLogKey.
                MANUAL_FACE_ID_ENROLLMENT_SCREEN_OPEN_1.getKey());
    }
}
