package com.phonecheck.parser.device.ios.info;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Parses the ./idevicePurpleBuddy -u command output to check
 * if device is on hello screen
 */
@Component
public class IosIsDeviceOnHelloParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosIsDeviceOnHelloParser.class);

    public Boolean parse(final String output) {
        LOGGER.info("Command parsing output: {}", output);
        return StringUtils.isNotBlank(output) &&
                (StringUtils.containsIgnoreCase(output, "lastPrepareLaunchSentinel") ||
                        StringUtils.containsIgnoreCase(output, "GuessedCountry"));
    }
}
