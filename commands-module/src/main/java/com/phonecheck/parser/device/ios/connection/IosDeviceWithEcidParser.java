package com.phonecheck.parser.device.ios.connection;

import com.phonecheck.model.device.IosDevice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Output example (one device per line):
 * iOS >
 * 2523dcebc561327daee9bbbd5ce094ad718b3a79-6316763021716518
 */
@Component
public class IosDeviceWithEcidParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosDeviceWithEcidParser.class);

    public Set<IosDevice> parse(final String output) {
        Set<IosDevice> devices = new HashSet<>();


        String[] uidEcidList = output.split("\n");
        for (String uidEcidStr : uidEcidList) {

            // Find the last occurrence of hyphen
            int lastIndex = uidEcidStr.lastIndexOf('-');

            String id, ecid;
            if (lastIndex != -1) {
                // Get the substring after the last hyphen
                ecid = uidEcidStr.substring(lastIndex + 1);
                id = uidEcidStr.substring(0, lastIndex);
            } else {
                LOGGER.warn("No hyphen found in the output string: {}", uidEcidStr);
                continue;
            }

            String[] segments = id.split(" ");
            if (segments.length <= 0) {
                continue;
            }
            IosDevice iosDevice = new IosDevice();
            // The last segment is the device's UDID
            iosDevice.setId(segments[segments.length - 1]);
            iosDevice.setEcid(ecid);
            devices.add(iosDevice);
        }
        return devices;
    }

    private boolean hasTwoHyphens(final String input) {
        Pattern pattern = Pattern.compile(".*-.*-.*");
        Matcher matcher = pattern.matcher(input);
        return matcher.matches();
    }
}
