package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.ios.IosProperty;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Parses output of command: ideviceinfo -u <UDID>
 * <p>
 * Output example:
 * BasebandSerialNumber: FTxreQ==
 * BasebandVersion: 7.60.00
 * BoardId: 10
 * BuildVersion: 14G60
 */
@Component
public class IosGetPropertiesParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosGetPropertiesParser.class);
    private static final String ERROR = "No device found with udid";

    public Map<IosProperty, String> parse(final String output, final IosProperty... iosProperties) throws IOException {
        LOGGER.debug("Parsing output: {}", output);
        final Map<String, IosProperty> nameToProperty = new HashMap<>();
        for (IosProperty property : iosProperties) {
            nameToProperty.put(property.getName().toLowerCase(Locale.ROOT), property);
        }
        final Map<IosProperty, String> results = new HashMap<>();
        if (null == output || output.contains(ERROR)) {
            LOGGER.info("Error output while getting ios properties: {}", output);
            return results;
        }

        try (BufferedReader reader = new BufferedReader(new StringReader(output))) {
            String line;
            while (null != (line = reader.readLine())) {
                int index = line.indexOf(": ");
                if (StringUtils.isBlank(line) || index < 0) {
                    continue;
                }

                // Read from the beginning of the line to just before the colon
                final String name = line.substring(0, index).toLowerCase(Locale.ROOT);
                if (!nameToProperty.containsKey(name)) {
                    continue;
                }

                final IosProperty property = nameToProperty.get(name);
                // Read from the colon/space to the remainder of the line to get this property's value
                final String value = line.substring(index + 2);
                results.put(property, value.trim());
                // Remove this property from our target properties so we don't try to read it again
                nameToProperty.remove(name);

                // If there are no more target properties to find, exit
                if (nameToProperty.isEmpty()) {
                    break;
                }
            }
        }

        return results;
    }
}
