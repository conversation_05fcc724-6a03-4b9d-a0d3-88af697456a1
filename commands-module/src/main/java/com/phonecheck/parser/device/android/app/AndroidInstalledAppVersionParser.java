package com.phonecheck.parser.device.android.app;

import org.springframework.stereotype.Component;

/**
 * Output example (one device per line):
 * versionName=version
 */
@Component
public class AndroidInstalledAppVersionParser {
    public String parse(final String output) {
        int equalsIndex = output.indexOf('=');

        if (equalsIndex != -1 && equalsIndex < output.length() - 1) {
            return output.substring(equalsIndex + 1).trim();
        } else {
            return null;
        }
    }
}
