package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.status.WorkingStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses Touch id command to check if touch id is working or not
 */
@Component
public class IosTouchIdCommand1Parser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosTouchIdCommand1Parser.class);

    public WorkingStatus parse(final String output) throws Exception {
        LOGGER.debug("TouchId Command1 parsing output: {}", output);

        if (StringUtils.isEmpty(output)) {
            return WorkingStatus.PENDING;
        }

        Pattern pattern = Pattern.compile("<key>MesaSelfTest</key>\\s*<string>SPIError</string>");
        Matcher matcher = pattern.matcher(output);
        if (matcher.find()) {
            return WorkingStatus.NO;
        }
        return WorkingStatus.PENDING;
    }
}
