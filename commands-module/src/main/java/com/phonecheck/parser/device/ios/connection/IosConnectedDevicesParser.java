package com.phonecheck.parser.device.ios.connection;

import com.phonecheck.model.device.IosDevice;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashSet;
import java.util.Set;

/**
 * Output example (one device per line):
 * c47d828ef7036952d777795e7a50034639350b16: MUX:IOS
 */
@Component
public class IosConnectedDevicesParser {
    public Set<IosDevice> parse(final String output) throws IOException {
        final Set<IosDevice> devices = new HashSet<>();
        if (StringUtils.isBlank(output) || StringUtils.containsIgnoreCase(output, "error")) {
            return devices;
        }

        try (BufferedReader reader = new BufferedReader(new StringReader(output))) {
            String line;
            while (null != (line = reader.readLine())) {
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                String[] segments = line.split(":");
                if (segments.length <= 0) {
                    continue;
                }

                IosDevice device = new IosDevice();
                // The first segment is the device's UDID
                device.setId(segments[0]);
                // The second segment tells whether device is in USB/MUX mode
                device.setUsbMode("USB".equals(segments[1].trim()));
                devices.add(device);
            }
        }

        return devices;
    }
}
