package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.status.RootedStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.Arrays;

/**
 * Parses output of the command: ideviceinstaller -u UDID -l -o list_all
 * <p>
 * Rooted output example:
 * com.app.Cydia, "Cydia"
 */
@Component
public class IosRootedDevicesParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosRootedDevicesParser.class);

    private static final String[] DEVICE_ROOTED_MARKER = new String[]{
            "cydia", "sileo", "tweakbox", "zestia", "inojb", "emus4u",
            "appeven", "appvalley", "asterix", "pangu", "undecimus", "unc0ver"};

    public RootedStatus parse(final String output) throws IOException {
        LOGGER.debug("Rooted devices command parsing output: {}", output);

        if (StringUtils.isEmpty(output)) {
            return RootedStatus.NOT_ROOTED;
        }
        try (BufferedReader reader = new BufferedReader(new StringReader(output))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String finalLine = line;
                boolean isDeviceRooted = Arrays.stream(DEVICE_ROOTED_MARKER).anyMatch(
                        x -> finalLine.toLowerCase().contains(x));
                if (isDeviceRooted) {
                    //Device is Rooted
                    return RootedStatus.ROOTED;
                }
            }
        }
        return RootedStatus.NOT_ROOTED;
    }
}
