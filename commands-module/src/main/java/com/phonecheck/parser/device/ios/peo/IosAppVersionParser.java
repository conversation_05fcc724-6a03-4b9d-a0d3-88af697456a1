package com.phonecheck.parser.device.ios.peo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses the ios app version
 */
@Component
public class IosAppVersionParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosAppVersionParser.class);
    private static final String APP_VERSION_REGEX = ".*<key>CFBundleVersion</key>\\n.*<string>(.*)</string>";

    public String parse(final String output) throws IOException {
        LOGGER.debug("Get app version: {}", output);
        final Matcher appVersionMatcher = Pattern.compile(APP_VERSION_REGEX).matcher(output);
        if (appVersionMatcher.find()) {
            return appVersionMatcher.group(1);
        }
        return null;
    }
}
