package com.phonecheck.parser.device.ios.profile;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Parses push profile status from ./ideviceconfiguration -u install -k file.absolutepath
 * <p>
 * Successful output example:
 * success
 * <p>
 * Error output example:
 * No device found with udid c47d8..., is it plugged in?
 */
@Component
public class IosPushProfileParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosPushProfileParser.class);

    private static final String SUCCESS_MARKER = "success";
    private static final String FAILURE_NO_DEVICES_MARKER = "No device found";

    public Boolean parse(final String output) throws IOException {
        LOGGER.debug("Command parsing output: {}", output);

        if (StringUtils.isBlank(output) || output.contains(FAILURE_NO_DEVICES_MARKER)) {
            return null;
        }
        return StringUtils.containsIgnoreCase(output, SUCCESS_MARKER);
    }
}
