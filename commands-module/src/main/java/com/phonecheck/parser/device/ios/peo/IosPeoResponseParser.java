package com.phonecheck.parser.device.ios.peo;


import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Parses PEO Response
 */
@Component
public class IosPeoResponseParser {
    public static final Logger LOGGER = LoggerFactory.getLogger(IosPeoResponseParser.class);
    private static final String EVERYTHING_WORKING_MARKER = "Every Thing Working..\n";
    private static final String LANGUAGE_SUCCESS_MARKER = "Language-Success\n";
    private static final String LOCALE_SUCCESS_MARKER = "Locale-Success\n";
    private static final String PREPARE_SUCCESS_MARKER = "Prepare-Success";
    private static final String PREPARE_FAIL_MARKER = "Prepare-Fail";
    private static final String ENROLMENT_PROGRAM_MARKER =
            "This device must be configured using the Device Enrollment Program";
    private static final String PREPARE_DASH_MARKER = "Prepare-";
    private static final String MDM_ERROR_MARKER_1 = "errorcode -38";
    private static final String MDM_ERROR_MARKER_2 = "errorcode -256";
    private static final String ERROR_DOMAIN_KEY = "ErrorDomain";
    private static final String MC_CLOUD_CONFIG_VALUE = "MCCloudConfigErrorDomain";
    private static final String ERROR_ARCHIVE_KEY = "CommandErrorArchive";
    private static final String ERROR_ARCHIVE_VALUE =
            "This device must be configured using the Device Enrollment Program";
    private static final String ERROR_LOCKDOWND_FAILED = "ERROR: Could not connect to lockdownd";
    private static final String ERROR_COULD_NOT_START_SERVICE =
            "Could not start service \"com.apple.mobile.MCInstall\"";

    private static final List<String> PROPERTIES_TO_BE_PARSED = List.of(
            ERROR_DOMAIN_KEY,
            ERROR_ARCHIVE_KEY
    );

    public int parse(final String output) throws Exception {
        LOGGER.info("Prepare command output: {}", output);
        final Map<String, String> results = new HashMap<>();

        if (output.contains(PREPARE_SUCCESS_MARKER)) {
            return 0;
        } else if ((output.contains(PREPARE_DASH_MARKER) && output.contains(MDM_ERROR_MARKER_1))) {
            return -38;
        } else if (output.contains(ENROLMENT_PROGRAM_MARKER) ||
                (output.contains(PREPARE_DASH_MARKER) && output.contains(MDM_ERROR_MARKER_2))) {
            return -4;
        } else if (output.contains(ERROR_LOCKDOWND_FAILED)) {
            return -2;
        } else if (output.contains(ERROR_COULD_NOT_START_SERVICE)) {
            return -5;
        } else if (StringUtils.isEmpty(output) ||
                !output.matches("(?s).*(<(\\w+)[^>]*>.*</\\2>|<(\\w+)[^>]*/>).*")) {
            // Check if null or not valid xml
            return -1;
        }

        // Remove the EVERYTHING_WORKING_MARKER and extra xml before loading xml
        String str = output.replace(EVERYTHING_WORKING_MARKER, "")
                .replace(LANGUAGE_SUCCESS_MARKER, "")
                .replace(LOCALE_SUCCESS_MARKER, "");
        String[] splits = str.split("(?<=</plist>)");
        // load xml string into a Document object
        final Document doc = loadXMLFromString(splits[0]);
        // normalize xml structure
        if (null == doc) {
            return -1;
        }
        doc.getDocumentElement().normalize();
        // iterate through the whole xml
        NodeList nodeList = doc.getElementsByTagName("*");
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                // if node is <key> and value is present in the list of properties needed to be parsed
                if (node.getNodeName().equals("key") && PROPERTIES_TO_BE_PARSED.contains(node.getTextContent())) {
                    results.put(node.getTextContent(), nodeList.item(++i).getTextContent().trim());
                }
            }
        }

        String errorDomain = results.get(ERROR_DOMAIN_KEY);
        if (errorDomain != null && errorDomain.equals(MC_CLOUD_CONFIG_VALUE)) {
            return -4;
        } else {
            String errorArchive = results.get(ERROR_ARCHIVE_KEY);
            if (errorArchive != null) {
                byte[] original = Base64.decodeBase64(errorArchive);
                String originalStr = new String(original, StandardCharsets.UTF_8);
                if (originalStr.contains(ERROR_ARCHIVE_VALUE)) {
                    return -4;
                }
            } else if (output.contains(PREPARE_FAIL_MARKER)) {
                return -3;
            }
        }

        return -1;
    }

    private Document loadXMLFromString(final String xml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        DocumentBuilder builder = factory.newDocumentBuilder();
        InputSource is = new InputSource(new StringReader(xml));
        Document result = builder.parse(is);
        return result;
    }
}
