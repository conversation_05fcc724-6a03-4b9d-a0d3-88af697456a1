package com.phonecheck.parser.device.iwatch;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.device.IosDevice;
import org.springframework.stereotype.Component;

/**
 * Parses battery info from an iWatch battery data response.
 */
@Component
public class IWatchBatteryInfoParser {

    /**
     * Parses the battery information from an iWatch response.
     *
     * @param device   The iWatch device for which battery data is being parsed.
     * @param response The raw response string containing battery information.
     * @return A {@link BatteryInfo} object containing parsed battery data, or {@code null} if parsing fails.
     */
    public BatteryInfo parseIWatchBatteryResponse(final IosDevice device, final String response) {
        try {

            int batteryCapacity = 0;
            try {
                batteryCapacity = Integer.parseInt(device.getIWatchInfo().getBatteryCapacity());
            } catch (Exception e) {
                // Ignore invalid or missing battery capacity values.
            }

            boolean chargingStatus = false;
            try {
                chargingStatus = Boolean.parseBoolean(device.getIWatchInfo().getChargingStatus());
            } catch (Exception e) {
                // Ignore invalid or missing charging status values.
            }

            // Extract the JSON portion from the response
            String jsonResponse = extractJsonFromResponse(response);

            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            return BatteryInfo.builder()
                    .averageTemperature(getIntValue(rootNode, "AverageTemperature"))
                    .cycle(getIntValue(rootNode, "CycleCount"))
                    .eeeeCode(getStringValue(rootNode, "EEEECode"))
                    .maximumDischargeCurrent(getIntValue(rootNode, "MaximumDischargeCurrent"))
                    .nominalChargeCapacity(getIntValue(rootNode, "NominalChargeCapacity"))
                    .serviceOption(getStringValue(rootNode, "ServiceOption"))
                    .totalOperatingTime(getIntValue(rootNode, "TotalOperatingTime"))
                    .weekMfd(getStringValue(rootNode, "WeekMfd"))
                    .batteryServiceFlags(getStringValue(rootNode, "batteryServiceFlags"))
                    .designedCapacity(getIntValue(rootNode, "DesignCapacity"))
                    .healthPercentage(extractBatteryHealth(rootNode.get("BatteryHealth")))
                    .batteryPercentage(batteryCapacity)
                    .isCharging(chargingStatus)
                    .build();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Extracts the JSON portion from the response string.
     *
     * @param response The raw response string containing battery data.
     * @return The extracted JSON string.
     * @throws IllegalArgumentException if no valid JSON data is found in the response.
     */
    private String extractJsonFromResponse(final String response) {
        int jsonStartIndex = response.indexOf("{");
        if (jsonStartIndex == -1) {
            throw new IllegalArgumentException("No JSON data found in the response.");
        }

        int jsonEndIndex = response.lastIndexOf("}");
        if (jsonEndIndex == -1) {
            throw new IllegalArgumentException("Invalid JSON data in the response.");
        }

        return response.substring(jsonStartIndex, jsonEndIndex + 1);
    }

    /**
     * Extracts battery health from the provided JSON node based on priority:
     * <ul>
     *     <li>Checks "Device" first. If empty or 0, proceeds to "nccAlt".</li>
     *     <li>If "nccAlt" is empty or 0, checks "Nom".</li>
     *     <li>If "Nom" is empty or 0, checks "ncc".</li>
     *     <li>If no valid data is found, returns -1.</li>
     * </ul>
     *
     * @param batteryHealthNode The JSON node containing battery health details.
     * @return The extracted battery health value, or -1 if no valid data is found.
     */
    private int extractBatteryHealth(final JsonNode batteryHealthNode) {
        if (batteryHealthNode == null) {
            return -1;
        }

        int device = getIntValue(batteryHealthNode, "Device");
        int nccAlt = getIntValue(batteryHealthNode, "nccAlt");
        int nom = getIntValue(batteryHealthNode, "Nom");
        int ncc = getIntValue(batteryHealthNode, "ncc");

        if (device > 0) {
            return device;
        } else if (nccAlt > 0) {
            return nccAlt;
        } else if (nom > 0) {
            return nom;
        } else if (ncc > 0) {
            return ncc;
        } else {
            return -1;
        }
    }

    /**
     * Retrieves an integer value from a JSON node.
     *
     * @param node The JSON node to extract the value from.
     * @param key  The key of the integer field.
     * @return The integer value if present, otherwise 0.
     */
    private int getIntValue(final JsonNode node, final String key) {
        return (node != null && node.has(key) && node.get(key).canConvertToInt()) ? node.get(key).asInt() : 0;
    }

    /**
     * Retrieves a string value from a JSON node.
     *
     * @param node The JSON node to extract the value from.
     * @param key  The key of the string field.
     * @return The string value if present, otherwise an empty string.
     */
    private String getStringValue(final JsonNode node, final String key) {
        return (node != null && node.has(key)) ? node.get(key).asText() : "";
    }
}
