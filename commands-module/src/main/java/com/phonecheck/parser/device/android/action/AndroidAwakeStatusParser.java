package com.phonecheck.parser.device.android.action;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Output example:
 *   mStayOn=true
 *   mStayOnWhilePluggedInSetting=7
 */
@Component
public class AndroidAwakeStatusParser {
    public boolean parse(final String output) {
        if (StringUtils.isNotBlank(output) && output.contains("mStayOn=true")) {
            return true;
        }
        return false;
    }
}
