package com.phonecheck.parser.device.android.imei;

import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class ImeiOppoSubInfoParser {
    private static final Pattern PATTERN = Pattern.compile("'([^']*)'");

    private String extractTextBetweenSingleQuotes(final String input) {
        StringBuilder result = new StringBuilder();

        // Use regex to match text between single quotes
        Matcher matcher = PATTERN.matcher(input);

        // Find all matches and append to the result
        while (matcher.find()) {
            result.append(matcher.group(1));
        }

        return result.toString();
    }

    private String removeDotsAndSpaces(final String input) {
        // Use regex to remove dots and spaces
        return input.replaceAll("[.\\s]", "");
    }

    public String parse(final String output) throws IOException {
        String extractedText = extractTextBetweenSingleQuotes(output);
        return removeDotsAndSpaces(extractedText);
    }


}