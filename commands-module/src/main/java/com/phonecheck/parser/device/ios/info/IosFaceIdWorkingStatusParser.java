package com.phonecheck.parser.device.ios.info;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses FaceId related keys from syslog and sets face id working status.
 */
@Component
public class IosFaceIdWorkingStatusParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosFaceIdWorkingStatusParser.class);
    private static final Pattern PEARL_SELF_TEST_RESULT_PATTERN =
            Pattern.compile(".*<key>PearlSelfTestResult</key>\\n\\t*<integer>(.*)</integer>");
    private static final Pattern MB_ARMED_PATTERN =
            Pattern.compile(".*<key>MB Armed</key>\\n\\t*(<false/>|<true/>)");
    private static final Pattern FRONT_CAMERA_SERIAL_NUM_PATTERN =
            Pattern.compile(".*<key>FrontIRCameraModuleSerialNumString</key>\\s*<string>(.*)</string>");


    public boolean checkPearlSelfTestResult(final String output) {
        Matcher matcher = PEARL_SELF_TEST_RESULT_PATTERN.matcher(output);
        if (matcher.find()) {
            try {
                String result = matcher.group(1);
                return "0".equals(result) || "64".equals(result);
            } catch (Exception e) {
                LOGGER.error("Exception occurred when trying to find PearlSelfTestResult key in syslogs", e);
            }
        }
        return false;
    }

    public boolean checkMbArmedResult(final String output) {
        Matcher matcher = MB_ARMED_PATTERN.matcher(output);
        if (matcher.find()) {
            try {
                String result = matcher.group(1);
                return "<true/>".equalsIgnoreCase(result);
            } catch (Exception e) {
                LOGGER.error("Exception occurred when trying to find 'MB Armed' key in syslogs", e);
            }
        }
        return false;
    }

    public boolean checkFrontCameraSerialNumberResult(final String output) {
        Matcher matcher = FRONT_CAMERA_SERIAL_NUM_PATTERN.matcher(output);
        if (matcher.find()) {
            try {
                String result = matcher.group(1);
                return StringUtils.isNotEmpty(result) && !result.contains("00000000000000000") ?
                        Boolean.TRUE : Boolean.FALSE;
            } catch (Exception e) {
                LOGGER.error("Exception occurred when trying" +
                        " to find FrontIRCameraModuleSerialNumString key in syslogs", e);
            }
        }
        return false;
    }
}
