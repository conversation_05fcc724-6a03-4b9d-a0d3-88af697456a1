package com.phonecheck.parser.device.ios.peo;

import com.phonecheck.parser.device.ios.info.IosIsDeviceOnHelloParser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class IosPushFilesParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosIsDeviceOnHelloParser.class);

    private static final String SUCCESS_MARKER = "success";
    private static final String FAILURE_NO_DEVICES_MARKER = "No device found";
    private static final String FAILURE_ERROR_MARKER = "ERROR";
    private static final String DEVICE_HOUSE_ARREST_MARKER = "HouseArrestService";
    private static final String FILES_PUSHED_FAILED_MARKER = "Failed";
    private static final String FAILURE_UNKNOWN_MARKER = "Failed for unknown reason";
    private static final String ERROR_LOCKDOWND_FAILED = "ERROR: Could not connect to lockdownd";


    public String parse(final String output) throws IOException {
        if (StringUtils.isBlank(output) || output.contains(FAILURE_NO_DEVICES_MARKER)) {
            return null;
        }
        if (StringUtils.containsIgnoreCase(output, FAILURE_ERROR_MARKER)) {
            if (StringUtils.containsIgnoreCase(output, DEVICE_HOUSE_ARREST_MARKER)) {
                return DEVICE_HOUSE_ARREST_MARKER;
            } else if (StringUtils.containsIgnoreCase(output, FILES_PUSHED_FAILED_MARKER)) {
                return FILES_PUSHED_FAILED_MARKER;
            } else if (StringUtils.containsIgnoreCase(output, ERROR_LOCKDOWND_FAILED)) {
                return ERROR_LOCKDOWND_FAILED;
            }
        } else if (StringUtils.containsIgnoreCase(output, SUCCESS_MARKER)) {
            return SUCCESS_MARKER;
        }
        return FAILURE_UNKNOWN_MARKER;
    }
}
