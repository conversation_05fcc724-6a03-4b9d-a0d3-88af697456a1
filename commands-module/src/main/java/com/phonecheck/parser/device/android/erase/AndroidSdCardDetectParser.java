package com.phonecheck.parser.device.android.erase;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class AndroidSdCardDetectParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidSdCardDetectParser.class);
    private static final String STORAGE_MARKER = "/storage/";
    private static final String EMULATED_MARKER = "emulated";

    public String parse(final String output) throws IOException {
        if (StringUtils.isNotBlank(output)) {
            LOGGER.info("sd card response {}", output);
            // Split the output into lines
            for (String line : output.split(System.lineSeparator())) {
                // Check if the line contains "/storage/"
                if (line.contains(STORAGE_MARKER)) {
                    // Split the line into columns
                    for (String storageCol : line.split(" ")) {
                        // Check if the column contains "/storage/"
                        if (storageCol.contains(STORAGE_MARKER)) {
                            // Remove any trailing "\r" characters
                            if (storageCol.contains("\r")) {
                                storageCol = storageCol.substring(0, storageCol.indexOf("\r"));
                            }
                            // Check if it's not an emulated storage
                            if (!storageCol.contains(EMULATED_MARKER)) {
                                //SD card storage detected
                                return storageCol;
                            }
                        }
                    }
                }
            }
        }
        // No SD card storage detected
        return null;
    }
}
