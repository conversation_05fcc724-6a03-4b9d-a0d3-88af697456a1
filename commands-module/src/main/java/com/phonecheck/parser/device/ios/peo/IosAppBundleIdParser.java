package com.phonecheck.parser.device.ios.peo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parses the ios app bundle identifier
 */
@Component
public class IosAppBundleIdParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosAppBundleIdParser.class);
    private static final String APP_BUNDLE_ID_REGEX = ".*<key>CFBundleIdentifier</key>\\n.*<string>(.*)</string>";

    public String parse(final String output) throws IOException {
        LOGGER.debug("Get app bundle id: {}", output);
        final Matcher appBundleIdMatcher = Pattern.compile(APP_BUNDLE_ID_REGEX).matcher(output);
        if (appBundleIdMatcher.find()) {
            return appBundleIdMatcher.group(1);
        }
        return null;
    }
}
