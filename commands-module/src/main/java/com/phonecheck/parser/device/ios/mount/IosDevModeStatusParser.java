package com.phonecheck.parser.device.ios.mount;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class IosDevModeStatusParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosDevModeStatusParser.class);

    public boolean parse(final String output) throws IOException {
        LOGGER.debug("Command parsing output: {}", output);

        if (StringUtils.isNotBlank(output)) {
            return output.contains("ON");
        }

        return false;
    }
}
