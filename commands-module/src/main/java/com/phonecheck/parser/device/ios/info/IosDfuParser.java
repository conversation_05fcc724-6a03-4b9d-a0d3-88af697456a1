package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.OsChecker;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;


/**
 * Parses DFU command response to see if device is in DFU mode
 * i.e. color
 */
@Component
@AllArgsConstructor
public class IosDfuParser {
    private static final String DFU_FOUND = "Found DFU:";
    private static final String RECOVERY_FOUND = "recovery:";
    private static final String SDOM = "SDOM";
    private static final String SERIAL_SEPARATOR = "serial=";
    private static final String COLON_SEPARATOR = ":";
    private static final String ECID_PATTERN = "^0*";
    private static final String DEVICE_SERIAL_PATTERN = "[\\[\\]\"]";
    private static final String SPACE_SEPARATOR = " ";
    private static final String CPID = "CPID";
    private static final String ECID = "ECID";
    private static final String BDID = "BDID";

    private static final String SERIAL = "SRNM";

    private final OsChecker osChecker;

    public Set<IosDevice> parse(final String output) {
        Set<IosDevice> devices = null;
        if (StringUtils.isNotBlank(output) && (output.contains(DFU_FOUND) || output.contains(RECOVERY_FOUND))) {
            devices = new HashSet<>();
            String[] lines = output.split("\n");

            for (String line : lines) {

                if (line.startsWith(DFU_FOUND) || line.startsWith(RECOVERY_FOUND)) {
                    String[] splits;
                    if (osChecker.isMac()) {
                        splits = (line.split(SERIAL_SEPARATOR)[1]).split(SPACE_SEPARATOR);
                    } else {
                        String removeUnderscore = line.replaceAll("_", " ");
                        splits = (removeUnderscore.split(RECOVERY_FOUND)[1]).split(SPACE_SEPARATOR);
                    }

                    if (splits.length >= 8) {

                        String cpid = null;
                        String bdid = null;
                        String ecid = null;
                        String deviceSerial = null;
                       // StringBuilder deviceIdBuilder = new StringBuilder();

                        for (String s : splits) {
                            String[] data = s.split(COLON_SEPARATOR);
                            String key = data.length > 0 ? data[0] : StringUtils.EMPTY;
                            String value = data.length > 1 ? data[1] : StringUtils.EMPTY;
                            key = key.replaceAll("\"", "");
                            switch (key.toUpperCase()) {
                                case CPID : cpid = value;
                                break;
                                case BDID : bdid = value;
                                break;
                                case ECID : ecid = value.replaceFirst(ECID_PATTERN, "");
                                break;
                                case SERIAL : deviceSerial = value.replaceAll(DEVICE_SERIAL_PATTERN, "");
                                break;
                                default:
                            }
                        }

                        IosDevice device = new IosDevice();
                        device.setRecoveryMode(true);
                        device.setEcid(ecid);
                        device.setSerial(StringUtils.isBlank(deviceSerial) ? StringUtils.EMPTY : deviceSerial);
                        device.setBdid(bdid);
                        device.setCpid(cpid);
                        device.setIdInRecoveryMode(ecid);
                        device.setId(ecid);
                        devices.add(device);
                    }
                }
            }
        }

        return devices;
    }
}
