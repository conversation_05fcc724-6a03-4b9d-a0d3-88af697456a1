package com.phonecheck.command.system.mac;

import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class MacUnzipFileCommand extends AbstractCommand {
    private final String filePath;
    private final String destinationPath;

    @Override
    public String[] getCmd() {
        return new String[]{"/usr/bin/unzip", "-o", filePath, "-d", destinationPath};
    }
}
