package com.phonecheck.command.system.mac;


import com.phonecheck.command.AbstractIosCommand;

/**
 * The hardware info command gives us all the required information for the workstation running
 * the phonecheck desktop application.
 */
public class GetMacHardwareInfoCommand extends AbstractIosCommand {

    @Override
    public String[] getCmd() {
        return new String[]{"system_profiler", "SPHardwareDataType"};
    }
}
