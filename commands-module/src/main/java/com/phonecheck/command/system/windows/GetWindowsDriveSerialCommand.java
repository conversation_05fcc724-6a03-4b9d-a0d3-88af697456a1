package com.phonecheck.command.system.windows;


import com.phonecheck.command.AbstractCommand;

/**
 * The hardware id command gives us all the required UDID for the workstation running
 * the phonecheck desktop application.
 */
public class GetWindowsDriveSerialCommand extends AbstractCommand {

    @Override
    public String[] getCmd() {

        return new String[]{
                "powershell.exe",
                "-Command",
                "\"Get-WmiObject",
                "-Class",
                "Win32_DiskDrive",
                "|",
                "Select-Object",
                "-ExpandProperty",
                "SerialNumber\""
        };
    }
}