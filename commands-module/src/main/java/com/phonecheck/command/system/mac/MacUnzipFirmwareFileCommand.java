package com.phonecheck.command.system.mac;

import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;

/**
 * Command for unzipping a firmware file
 */
@AllArgsConstructor
public class MacUnzipFirmwareFileCommand extends AbstractCommand {
    private final String filePath;
    private final String destinationPath;

    @Override
    public String[] getCmd() {
        return new String[]{"/usr/bin/unzip", filePath, "-d", destinationPath};
    }
}
