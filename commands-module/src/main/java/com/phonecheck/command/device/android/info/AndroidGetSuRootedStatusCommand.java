package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidGetSuRootedStatusCommand extends AbstractAndroidCommand {
    private final String identifier;
    private final boolean isWhichSuCommand;

    @Override
    public String[] getCmd() {
        if (isWhichSuCommand) {
            return new String[]{"./adb", "-s", this.identifier, "shell", "which", "su"};
        } else {
            return new String[]{"./adb", "-s", this.identifier, "shell", "su"};
        }
    }
}