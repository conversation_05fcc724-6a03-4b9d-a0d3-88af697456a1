package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Determines whether Touch ID is working or not
 */
@AllArgsConstructor
public class IosGetTouchIdStatusCommand2 extends AbstractIosCommand {

    private final String udid;

    @Override
    public String[] getCmd() {
        return new String[]{"./idevicediagnostics", "-u", udid, "ioregname", "AppleMesaSEPDriver"};
    }
}
