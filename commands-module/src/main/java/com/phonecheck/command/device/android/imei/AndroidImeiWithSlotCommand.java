package com.phonecheck.command.device.android.imei;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidImeiWithSlotCommand extends AbstractAndroidCommand {
    private final String identifier;
    private final String transactionCode;
    private final String slotNumber;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb",
                "-s",
                identifier,
                "shell",
                "service",
                "call",
                "phone",
                transactionCode,
                "i32",
                slotNumber,
                "s16",
                "com.android.shell"
        };
    }
}
