package com.phonecheck.command.device.android.prepare;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidPerformHomeClickCommand extends AbstractAndroidCommand {

    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "monkey",
                "-c", "android.intent.category.HOME", "1", "&>/dev/null"};
    }
}
