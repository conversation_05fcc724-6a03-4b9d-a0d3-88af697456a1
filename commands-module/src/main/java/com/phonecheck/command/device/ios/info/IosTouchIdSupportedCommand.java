package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Determines whether Touch ID is supported by reading device diagnostics
 */
@AllArgsConstructor
public class IosTouchIdSupportedCommand extends AbstractIosCommand {

    private final String udid;
    private final boolean above17;

    @Override
    public String[] getCmd() {
        if (above17) {
            return new String[]{"./ideviceinfo", "-u", udid, "-k", "HasMesa"};
        } else {
            return new String[]{"./idevicediagnostics", "-u", udid, "mobilegestalt", "HasMesa"};
        }
    }
}
