package com.phonecheck.command.device.android.prepare;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidFlushSamsungSetupCommand extends AbstractAndroidCommand {

    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "pm",
                "clear", "com.sec.android.app.SecSetupWizard"};
    }
}
