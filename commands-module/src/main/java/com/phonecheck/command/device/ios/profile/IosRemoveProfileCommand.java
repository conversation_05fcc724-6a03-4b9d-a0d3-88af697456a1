package com.phonecheck.command.device.ios.profile;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
public class IosRemoveProfileCommand extends AbstractIosCommand {
    private final String identifier;
    private final String profileId;

    @Override
    public String[] getCmd() {
        // If ssid not blank means we need to remove Wi-Fi profile otherwise battery profile
        return new String[]{
                "./ideviceconfiguration",
                "-u",
                identifier,
                "DeleteProfile",
                StringUtils.isNotBlank(profileId)
                        ? profileId
                        : "hello" // In some old devices "home" can be used to remove battery profile
        };

    }
}
