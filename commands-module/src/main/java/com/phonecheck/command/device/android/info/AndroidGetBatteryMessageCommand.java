package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves connected android device battery message in Japanese or English
 */
@AllArgsConstructor
public class AndroidGetBatteryMessageCommand extends AbstractAndroidCommand {

    private final String identifier;
    private final String adbPath;

    @Override
    public String[] getCmd() {
        return new String[]{"./GetBatteryMessage", identifier, "ocr", "1000", adbPath};
    }
}