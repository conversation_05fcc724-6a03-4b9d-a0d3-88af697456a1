package com.phonecheck.command.device.ios.restore;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Command to reboot the IOS device into recovery.
 */
@AllArgsConstructor
public class IosRestoreRebootCommand extends AbstractIosCommand {
    private final boolean isMac;
    private final String ecid;

    @Override
    public String[] getCmd() {
        final String command = isMac ? "./phonecheckRestore" :
                ".\\restore64\\phonecheckRestore";

        return new String[]{command, "-r", "-i", ecid};
    }
}