package com.phonecheck.command.device.ios.mount;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Attempts to mount a disk image to a device's file system
 */
@AllArgsConstructor
public class IosMountDiskImageCommand extends AbstractIosCommand {
    // Device UDID
    private final String id;
    // .dmg file
    private final String imagePath;
    // .dmg.signature file
    private final String signaturePath;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceimagemounter", "-u", id, imagePath, signaturePath};
    }
}
