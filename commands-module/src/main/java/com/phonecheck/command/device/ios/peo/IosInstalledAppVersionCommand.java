package com.phonecheck.command.device.ios.peo;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class IosInstalledAppVersionCommand extends AbstractIosCommand {

    private final String identifier;

    @Override
    public String[] getCmd() {
            return new String[]{"./ideviceinstaller", "-u", identifier, "-l", "-o", "list_user"};
    }

}
