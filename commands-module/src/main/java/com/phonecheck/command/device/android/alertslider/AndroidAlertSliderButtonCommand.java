package com.phonecheck.command.device.android.alertslider;

import com.phonecheck.command.AbstractAndroidCommand;
import com.phonecheck.model.constants.android.AndroidAppPackageConstants;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidAlertSliderButtonCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "am", "start", "-n",
                AndroidAppPackageConstants.CONSUMER_APP_PACKAGE +
                        "/com.phonecheck.buttons.ui.activities.PCButtonTestActivity",
                "-a", "com.phonecheck.oneplusalertkey"};
    }
}
