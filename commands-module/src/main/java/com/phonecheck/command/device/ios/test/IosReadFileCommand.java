package com.phonecheck.command.device.ios.test;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Attempts to read a file from an IOS device
 */
@AllArgsConstructor
public class IosReadFileCommand extends AbstractIosCommand {

    private final String udid;
    private final String fileName;
    private final String iosAppIdentifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./idevicefuse", "-u", udid, "-r", fileName, iosAppIdentifier};
    }
}
