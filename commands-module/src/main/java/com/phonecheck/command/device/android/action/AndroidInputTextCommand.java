package com.phonecheck.command.device.android.action;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidInputTextCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{
            "./adb", "-s", identifier, "shell", "input", "text", "*#0*#"
        };
    }
}
