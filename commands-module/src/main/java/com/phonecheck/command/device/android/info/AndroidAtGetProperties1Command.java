package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Command to retrieve AT device properties before activation
 */
@AllArgsConstructor
public class AndroidAtGetProperties1Command extends AbstractAndroidCommand {
    private final String portName;

    @Override
    public String[] getCmd() {
        return new String[]{"./androidmodels", portName, "PM"};
    }
}