package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidFrpStatusCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "dumpsys", "account"};
    }

}
