package com.phonecheck.command.device.android.imei;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public class AndroidInputTapCommand extends AbstractAndroidCommand {

    private final String identifier;
    private final List<String> arg;

    @Override
    public String[] getCmd() {
        List<String> tapCode = List.of("./adb", "-s", identifier, "shell", "input", "tap");
        List<String> finalCommand = new ArrayList<>(tapCode);
        finalCommand.addAll(arg);
        return finalCommand.toArray(new String[0]);
    }
}
