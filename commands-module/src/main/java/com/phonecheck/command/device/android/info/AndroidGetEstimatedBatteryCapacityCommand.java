package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves connected android device estimated battery capacity
 */
@AllArgsConstructor
public class AndroidGetEstimatedBatteryCapacityCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell",
                "dumpsys", "batterystats", "|", "grep", "\"Estimated battery capacity\""};
    }
}
