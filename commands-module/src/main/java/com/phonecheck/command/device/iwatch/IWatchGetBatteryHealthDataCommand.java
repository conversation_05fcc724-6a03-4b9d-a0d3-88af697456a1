package com.phonecheck.command.device.iwatch;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Command class to get battery health data of iWatch
 */
@AllArgsConstructor
public class IWatchGetBatteryHealthDataCommand extends AbstractIosCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./iWatch-Util", "-udid", identifier};
    }
}
