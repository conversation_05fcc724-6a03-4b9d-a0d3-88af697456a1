package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Retrieves activation info for the device
 */
@AllArgsConstructor
public class IosGetActivationInfoCommand extends AbstractIosCommand {
    private final String udid;

    @Override
    public String[] getCmd() {
        List<String> args = new ArrayList<>();
        args.add("./ideviceactivation");
        args.add("-u");
        args.add(udid);
        args.add("activate");
        args.add("-i");
        return args.toArray(new String[0]);
    }
}