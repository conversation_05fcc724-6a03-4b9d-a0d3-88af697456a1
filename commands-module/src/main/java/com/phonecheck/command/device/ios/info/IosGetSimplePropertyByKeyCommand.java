package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class IosGetSimplePropertyByKeyCommand extends AbstractIosCommand {
    private final String udid;
    private final String key;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceinfo", "-s", "-u", udid, "-k", key};
    }
}