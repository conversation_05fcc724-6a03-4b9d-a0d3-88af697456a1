package com.phonecheck.command.device.ios.peo;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Prepares for PEO for a specific IOS device
 */
@AllArgsConstructor
public class IosPreparePeoCommand extends AbstractIosCommand {
    private final String udid;
    private final String prepareListPath;
    private final String locale;
    private final String lang;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceconfiguration", "-u", udid, "prepare", prepareListPath, locale, lang};
    }
}
