package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves eSim support status from Euicc
 */
@AllArgsConstructor
public class AndroidESimStatusFromEuiccCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "pm",
                "has-feature", "android.hardware.telephony.euicc"};
    }
}
