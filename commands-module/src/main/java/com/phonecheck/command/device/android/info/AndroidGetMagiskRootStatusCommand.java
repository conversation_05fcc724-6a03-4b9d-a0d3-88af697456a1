package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves package name for Magisk root status
 */
@AllArgsConstructor
public class AndroidGetMagiskRootStatusCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "pm", "list", "packages", "|", "grep \"magisk\""};
    }
}
