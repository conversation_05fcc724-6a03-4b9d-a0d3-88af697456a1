package com.phonecheck.command.device.ios.profile;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

import java.io.File;

@AllArgsConstructor
public class IosPushProfileCommand extends AbstractIosCommand {
    private final String identifier;
    private final File file;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceconfiguration", "-u", identifier, "install", file.getAbsolutePath()};
    }
}
