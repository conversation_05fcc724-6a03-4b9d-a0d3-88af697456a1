package com.phonecheck.command.device.ios.syslog;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves device system log
 */
@AllArgsConstructor
public class IosStartSyslogCommand extends AbstractIosCommand {
    private final String udid;
    private final boolean isMac;

    @Override
    public String[] getCmd() {
        if (isMac) {
            return new String[]{"./idevicesyslog", "-u", udid, "-x"};
        } else {
            return new String[]{".\\idevicesyslog", "-u", udid};
        }
    }
}
