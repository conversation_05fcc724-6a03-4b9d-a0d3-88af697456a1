package com.phonecheck.command.device.android.app;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidGivePermissionsCommand extends AbstractAndroidCommand {
    private final String identifier;
    private final String packageName;
    private final String permission;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "pm", "grant", "-n",
                packageName,
                permission};
    }
}
