package com.phonecheck.command.device.android.fingerprint;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidRunFingerPrintActivityCommand extends AbstractAndroidCommand {
    private final String identifier;
    private final String activity;
    private final String key;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "am", "start", "-n", activity, "-a", key};
    }
}
