package com.phonecheck.command.device.android.app;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidOpenAppCommand extends AbstractAndroidCommand {
    private final String identifier;
    private final String appPackage;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier,
                "shell", "monkey", "-p", appPackage,
                "-c", "android.intent.category.LAUNCHER", "1"};
    }
}
