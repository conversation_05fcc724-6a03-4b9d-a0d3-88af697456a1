package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves find my iphone status
 */
@AllArgsConstructor
public class IosGetFindMyIphoneStatusCommand extends AbstractIosCommand {

    private final String udid;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceinfo", "-u", udid, "-q", "com.apple.fmip", "-k", "IsAssociated"};
    }
}
