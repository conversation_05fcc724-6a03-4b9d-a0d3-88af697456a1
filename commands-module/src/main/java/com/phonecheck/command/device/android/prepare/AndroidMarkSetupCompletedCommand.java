package com.phonecheck.command.device.android.prepare;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidMarkSetupCompletedCommand extends AbstractAndroidCommand {

    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "settings",
                "put", "global", "setup_wizard_has_run", "1"};
    }
}
