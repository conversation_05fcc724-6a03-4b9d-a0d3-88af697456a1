package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class IosGetSimCarrierBundleInfoCommand extends AbstractIosCommand {
    private final String identifier;
    private final boolean isSimple;

    @Override
    public String[] getCmd() {
        if (isSimple) {
            return new String[]{"./ideviceinfo", "-s", "-u", identifier, "-k", "CarrierBundleInfoArray"};
        } else {
            return new String[]{"./ideviceinfo", "-u", identifier, "-k", "CarrierBundleInfoArray"};
        }
    }
}
