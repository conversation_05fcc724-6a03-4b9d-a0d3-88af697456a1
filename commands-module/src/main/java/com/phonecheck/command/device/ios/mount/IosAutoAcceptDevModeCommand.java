package com.phonecheck.command.device.ios.mount;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Auto accepts Dev Mode for a specific IOS device
 */
@AllArgsConstructor
public class IosAutoAcceptDevModeCommand extends AbstractIosCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./idevicedevmode", "-a", "-u", identifier};
    }
}
