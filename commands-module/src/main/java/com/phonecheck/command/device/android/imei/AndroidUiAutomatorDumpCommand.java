package com.phonecheck.command.device.android.imei;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidUiAutomatorDumpCommand extends AbstractAndroidCommand {

    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "uiautomator", "dump"};

    }
}
