package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves iCloud if exists
 */
@AllArgsConstructor
public class IosGetIcloudStatusCommand extends AbstractIosCommand {
    private final String udid;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceinfo", "-u", udid, "-q", "com.apple.mobile.data_sync"};
    }
}