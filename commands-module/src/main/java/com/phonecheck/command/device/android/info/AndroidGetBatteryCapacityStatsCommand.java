package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves connected android device battery capacity stats
 */
@AllArgsConstructor
public class AndroidGetBatteryCapacityStatsCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell",
                "dumpsys", "batterystats", "|", "grep", "\"Capacity:\""};
    }
}
