package com.phonecheck.command.print.windows;

import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
public class WindowsPrintWithDymoPrinterCommand extends AbstractCommand {
    private String imagePath;
    private String printerName;
    private String configFileName;
    private String printTray;

    @Override
    public String[] getCmd() {
        if (StringUtils.isNotBlank(printTray)) {
            return new String[]{
                    ".\\print\\dymo\\DymoPrinter",
                    "-print",
                    "\"" + printerName + "\"",
                    "\".\\print\\dymo\\config\\" + configFileName + "\"",
                    imagePath,
                    printTray
            };
        } else {
            return new String[]{
                    ".\\print\\dymo\\DymoPrinter",
                    "-print",
                    "\"" + printerName + "\"",
                    "\".\\print\\dymo\\config\\" + configFileName + "\"",
                    imagePath
            };
        }
    }
}
