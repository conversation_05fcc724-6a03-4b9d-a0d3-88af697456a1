package com.phonecheck.command.print.windows;

import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class WindowsPrintToPdfCommand extends AbstractCommand {
    private String pdfPath;
    private String imagePath;

    @Override
    public String[] getCmd() {
        return new String[]{
                ".\\print\\dymo\\DymoPrinter",
                "-pdf",
                pdfPath,
                imagePath
        };
    }
}
