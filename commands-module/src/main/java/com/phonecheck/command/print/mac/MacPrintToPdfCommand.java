package com.phonecheck.command.print.mac;

import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class MacPrintToPdfCommand extends AbstractCommand {

    private final String sourceFilePath;
    private final String destFilePath;

    @Override
    public String[] getCmd() {
        return new String[]{"/usr/bin/sips", "-s", "format", "pdf",
                sourceFilePath, "--out", destFilePath};
    }
}
