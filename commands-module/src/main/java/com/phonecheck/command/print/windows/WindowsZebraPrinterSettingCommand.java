package com.phonecheck.command.print.windows;

import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;


@AllArgsConstructor
public class WindowsZebraPrinterSettingCommand extends AbstractCommand {
    private final String printerName;

    @Override
    public String[] getCmd() {
        return new String[]{
                ".\\print\\zebra\\ZebraPrinterSetting",
                printerName
        };
    }
}
