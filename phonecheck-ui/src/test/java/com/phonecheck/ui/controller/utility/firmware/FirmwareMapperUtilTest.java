package com.phonecheck.ui.controller.utility.firmware;

import com.phonecheck.ui.controller.utility.FirmwareMapperUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith({MockitoExtension.class})
public class FirmwareMapperUtilTest {
    private FirmwareMapperUtil firmwareMapperUtil;


    @BeforeEach
    public void setUp() {
        firmwareMapperUtil = new FirmwareMapperUtil();
    }

    @Test
    public void convertBytesToFileSizeWithSITest() {
        long sizeInBytes = 2048;
        boolean useSI = true;
        String result = firmwareMapperUtil.convertBytesToFileSize(sizeInBytes, useSI);
        assertEquals("2.0 kB", result);
    }

    @Test
    public void convertBytesToFileSizeWithoutSITest() {
        long sizeInBytes = 2048;
        boolean useSI = false;
        String result = firmwareMapperUtil.convertBytesToFileSize(sizeInBytes, useSI);
        assertEquals("2.0 KiB", result);
    }


}
