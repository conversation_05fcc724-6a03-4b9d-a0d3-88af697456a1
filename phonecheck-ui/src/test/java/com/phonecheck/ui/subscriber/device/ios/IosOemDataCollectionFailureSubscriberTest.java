package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.ios.IosOemInfoFailureEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosOemInfoFailureMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosOemDataCollectionFailureSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosOemDataCollectionFailureSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosOemDataCollectionFailureSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final IosOemInfoFailureMessage responseMessage = new IosOemInfoFailureMessage();
        responseMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage(DeviceFamily.IOS + "/oem/collection/failure",
                mapper.writeValueAsString(responseMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(IosOemInfoFailureEvent.class));
    }
}

