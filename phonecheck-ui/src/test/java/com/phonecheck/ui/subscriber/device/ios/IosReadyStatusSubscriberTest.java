package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.ios.IosReadyStatusEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosReadyStatusMessage;
import com.phonecheck.model.store.UiInMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosReadyStatusSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private UiInMemoryStore uiInMemoryStore;
    private IosReadyStatusSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosReadyStatusSubscriber(mapper, client, eventPublisher, uiInMemoryStore);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final IosReadyStatusMessage iosReadyStatusMessage = new IosReadyStatusMessage();
        iosReadyStatusMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage(DeviceFamily.IOS + "/ready",
                mapper.writeValueAsString(iosReadyStatusMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(IosReadyStatusEvent.class));
    }
}
