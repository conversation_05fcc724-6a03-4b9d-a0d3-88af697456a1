package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.android.AndroidKnoxInfoEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidKnoxInfoMessage;
import com.phonecheck.model.status.KnoxStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AndroidKnoxInfoSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private AndroidKnoxInfoSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new AndroidKnoxInfoSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final AndroidKnoxInfoMessage message = new AndroidKnoxInfoMessage();
        message.setId("id");
        message.setKnoxStatus(KnoxStatus.KNOX_ON);

        MqttTopicMessage mqttTopicMessage = new MqttTopicMessage(
                "/knox/response",
                mapper.writeValueAsString(message));
        subscriber.onMessage(mqttTopicMessage);

        verify(eventPublisher).publishEvent(any(AndroidKnoxInfoEvent.class));
    }
}
