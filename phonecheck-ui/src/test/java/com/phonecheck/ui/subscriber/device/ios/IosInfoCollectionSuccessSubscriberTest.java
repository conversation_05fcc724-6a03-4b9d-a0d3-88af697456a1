package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.ios.IosInfoCollectionSuccessEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosInfoCollectionSuccessMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosInfoCollectionSuccessSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosInfoCollectionSuccessSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosInfoCollectionSuccessSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        // Create the info collection success message and payload
        IosInfoCollectionSuccessMessage iosInfoCollectionSuccessMessage =
                new IosInfoCollectionSuccessMessage();
        iosInfoCollectionSuccessMessage.setId("1L");

        String payload = mapper.writeValueAsString(iosInfoCollectionSuccessMessage);

        MqttTopicMessage message = new MqttTopicMessage(
                "/info/collection/success", payload);
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(IosInfoCollectionSuccessEvent.class));


    }

}