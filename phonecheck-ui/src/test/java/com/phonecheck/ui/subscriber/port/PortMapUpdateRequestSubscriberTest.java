package com.phonecheck.ui.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.port.PortMapUpdateRequestEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapUpdateRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PortMapUpdateRequestSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    private PortMapUpdateRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new PortMapUpdateRequestSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessagePortMapDeviceConnection() throws JsonProcessingException {
        final PortMapUpdateRequestMessage requestMessage = new PortMapUpdateRequestMessage();

        MqttTopicMessage message = new MqttTopicMessage("port-map/update/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(PortMapUpdateRequestEvent.class));
    }
}