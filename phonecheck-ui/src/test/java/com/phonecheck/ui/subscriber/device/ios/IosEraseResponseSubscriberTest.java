package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.ios.IosEraseResponseEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosEraseResponseMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosEraseResponseSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosEraseResponseSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosEraseResponseSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException, InterruptedException {
        final IosEraseResponseMessage responseMessage = new IosEraseResponseMessage();
        responseMessage.setId("id");
        responseMessage.setSerial("serial");

        MqttTopicMessage message = new MqttTopicMessage("/erase/response",
                mapper.writeValueAsString(responseMessage));
        subscriber.onMessage(message);
        Thread.sleep(2000);
        verify(eventPublisher).publishEvent(any(IosEraseResponseEvent.class));
    }
}
