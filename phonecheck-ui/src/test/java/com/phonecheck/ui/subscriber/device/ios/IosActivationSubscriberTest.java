package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.ios.IosActivationNeededEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosActivationRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosActivationSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosActivationSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosActivationSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final IosActivationRequestMessage iosActivationRequestMessage = new IosActivationRequestMessage();
        iosActivationRequestMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage(
                TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "activation", "start"),
                mapper.writeValueAsString(iosActivationRequestMessage));
        subscriber.onMessage(message);

        ArgumentCaptor<IosActivationNeededEvent> eventCaptor =
                ArgumentCaptor.forClass(IosActivationNeededEvent.class);
        verify(eventPublisher).publishEvent(eventCaptor.capture());

        IosActivationNeededEvent capturedEvent = eventCaptor.getValue();

        assertEquals(subscriber, capturedEvent.getSource());
        assertEquals(iosActivationRequestMessage.getId(), capturedEvent.getDevice().getId());
        assertEquals(DeviceStage.ACTIVATING, capturedEvent.getDevice().getStage());
    }
}
