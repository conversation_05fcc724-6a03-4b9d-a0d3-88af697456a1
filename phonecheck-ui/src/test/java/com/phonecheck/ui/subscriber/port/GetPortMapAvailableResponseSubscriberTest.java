package com.phonecheck.ui.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.port.GetPortMapAvailableResponseEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.GetPortMapAvailableResponseMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class GetPortMapAvailableResponseSubscriberTest {

    private ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private GetPortMapAvailableResponseSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new GetPortMapAvailableResponseSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final GetPortMapAvailableResponseMessage getPortMapAvailableResponseMessage =
                new GetPortMapAvailableResponseMessage();
        getPortMapAvailableResponseMessage.setPortCount(4);
        getPortMapAvailableResponseMessage.setPortMapAvailable(true);

        MqttTopicMessage message = new MqttTopicMessage("port-map/available/response",
                mapper.writeValueAsString(getPortMapAvailableResponseMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(GetPortMapAvailableResponseEvent.class));
    }
}
