package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.android.AndroidInfoCollectionFailureEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidInfoCollectionFailureMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AndroidInfoCollectionFailureSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private AndroidInfoCollectionFailureSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new AndroidInfoCollectionFailureSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final AndroidInfoCollectionFailureMessage responseMessage = new AndroidInfoCollectionFailureMessage();
        responseMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage("/info/collection/failure",
                mapper.writeValueAsString(responseMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(AndroidInfoCollectionFailureEvent.class));
    }
}