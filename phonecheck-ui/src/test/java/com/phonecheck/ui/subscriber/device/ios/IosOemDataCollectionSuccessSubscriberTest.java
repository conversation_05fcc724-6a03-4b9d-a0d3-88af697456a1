package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.ios.IosOemInfoSuccessEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosOemInfoSuccessMessage;
import com.phonecheck.model.test.OemPartsData;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosOemDataCollectionSuccessSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosOemDataCollectionSuccessSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosOemDataCollectionSuccessSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        // Create a sample response message
        final IosOemInfoSuccessMessage request = new IosOemInfoSuccessMessage();
        request.setId("id");
        // Set other properties of responseMessage as needed for the test

        // Set OemPartsDataCurrent properties
        OemPartsData oemPartsDataCurrent = new OemPartsData();
        oemPartsDataCurrent.setSerialNumber("serialNumber");
        oemPartsDataCurrent.setMainBoardSerial("mainBoardSerialNumber");
        oemPartsDataCurrent.setBatterySerial("batterySerialNumber");
        oemPartsDataCurrent.setFrontCameraSerial("frontCameraSerialNumber");
        oemPartsDataCurrent.setBackCameraSerial("backCameraSerialNumber");
        oemPartsDataCurrent.setLcdSerial("lcdSerialNumber");
        request.setOemPartsDataCurrent(oemPartsDataCurrent);

        // Set OemPartsDataFactory properties
        OemPartsData oemPartsDataFactory = new OemPartsData();
        oemPartsDataFactory.setSerialNumber("factorySerialNumber");
        oemPartsDataFactory.setBackCameraSerial("factoryBackCameraSerialNumber");
        oemPartsDataFactory.setBatterySerial("factoryBatterySerialNumber");
        oemPartsDataFactory.setFrontCameraSerial("factoryFrontCameraSerialNumber");
        oemPartsDataFactory.setBackCameraSerial("factoryBackCameraSerialNumber");
        oemPartsDataFactory.setLcdSerial("factoryLcdSerialNumber");
        request.setOemPartsDataFactory(oemPartsDataFactory);

        // Create the MQTT topic message with the response message payload
        MqttTopicMessage message = new MqttTopicMessage(DeviceFamily.IOS + "/oem/collection/success",
                mapper.writeValueAsString(request));

        // Call the onMessage method of the subscriber
        subscriber.onMessage(message);

        // Verify that the eventPublisher.publishEvent method is called with the expected event
        verify(eventPublisher).publishEvent(any(IosOemInfoSuccessEvent.class));

    }
}
