package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.ios.IosAppInstallSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosAppInstallSuccessMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosAppInstallSuccessSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosAppInstallSuccessSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosAppInstallSuccessSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final IosAppInstallSuccessMessage iosAppInstallSuccessMessage = new IosAppInstallSuccessMessage();
        iosAppInstallSuccessMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage(
                TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "app-install", "success"),
                mapper.writeValueAsString(iosAppInstallSuccessMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(IosAppInstallSuccessEvent.class));
    }

    @Test
    void testOnMessageWithInvalidPayload() {
        final String invalidPayload = "invalid";

        MqttTopicMessage message = new MqttTopicMessage(
                TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "app-install", "success"),
                invalidPayload);
        subscriber.onMessage(message);

        verify(eventPublisher, never()).publishEvent(any(IosAppInstallSuccessEvent.class));
    }
}