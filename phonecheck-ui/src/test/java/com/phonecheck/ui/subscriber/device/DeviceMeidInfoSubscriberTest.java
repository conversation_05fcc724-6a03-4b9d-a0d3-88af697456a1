package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.DeviceMeidInfoEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.DeviceMeidInfoMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class DeviceMeidInfoSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private DeviceMeidInfoSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new DeviceMeidInfoSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final DeviceMeidInfoMessage message = new DeviceMeidInfoMessage();
        message.setId("id");
        message.setMeid("meid");
        message.setMeid2("meid2");
        message.setMeidDecimal("meidDecimal");
        message.setMeidDecimal2("meidDecimal2");
        message.setPesn("pesn");
        message.setPesn2("pesn2");

        MqttTopicMessage mqttMessage = new MqttTopicMessage("/meid/info/collection/success",
                mapper.writeValueAsString(message));
        subscriber.onMessage(mqttMessage);

        verify(eventPublisher).publishEvent(any(DeviceMeidInfoEvent.class));
    }
}
