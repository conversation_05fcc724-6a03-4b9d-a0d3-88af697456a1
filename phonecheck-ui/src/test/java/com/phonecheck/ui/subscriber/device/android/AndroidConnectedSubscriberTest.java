package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.android.AndroidConnectedEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidConnectedMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AndroidConnectedSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private AndroidConnectedSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new AndroidConnectedSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException, InterruptedException {
        final AndroidConnectedMessage message = new AndroidConnectedMessage();
        message.setId("id");
        message.setAuthorizationStatus(AuthorizationStatus.AUTHORIZED);

        MqttTopicMessage mqttTopicMessage = new MqttTopicMessage(
                "/connected",
                mapper.writeValueAsString(message));
        subscriber.onMessage(mqttTopicMessage);
        Thread.sleep(3000);
        verify(eventPublisher).publishEvent(any(AndroidConnectedEvent.class));
    }
}