package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.constants.EsnFieldColor;
import com.phonecheck.model.event.device.DeviceEsnResponseEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.EsnResponseMessage;
import com.phonecheck.model.status.EsnStatus;
import com.phonecheck.ui.subscriber.device.DeviceEsnResponseSubscriber;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class DeviceEsnResponseSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private DeviceEsnResponseSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new DeviceEsnResponseSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final EsnResponseMessage responseMessage = new EsnResponseMessage();
        responseMessage.setId("id");
        responseMessage.setEsnStatus(EsnStatus.ESN_GOOD);
        responseMessage.setEsnFieldColor(EsnFieldColor.GREEN);

        MqttTopicMessage message = new MqttTopicMessage("/esn/response",
                mapper.writeValueAsString(responseMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(DeviceEsnResponseEvent.class));
    }
}
