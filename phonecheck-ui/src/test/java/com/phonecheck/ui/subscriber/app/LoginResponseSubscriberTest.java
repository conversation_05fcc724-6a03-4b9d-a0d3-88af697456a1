package com.phonecheck.ui.subscriber.app;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.communicator.CommunicatorService;
import com.phonecheck.model.event.login.LoginEvent;
import com.phonecheck.model.mqtt.messages.LoginResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class LoginResponseSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private LoginResponseSubscriber subscriber;

    @Mock
    private CommunicatorService communicatorService;

    @Mock
    private InMemoryStore inMemoryStore;

    @BeforeEach
    void setup() {
        subscriber = new LoginResponseSubscriber(mapper, client, eventPublisher, communicatorService);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
  /*      String responseStr = "{\"status\":\"success\",\n" +
                "                \"msg\":\"label fetched successfully\",\n" +
                "                \"data\":[{\"id\":450,\n" +
                "                \"name\":\"Muzi12\",\n" +
                "                \"fxml\":\"\",\n" +
                "                \"image\":\"\",\n" +
                "                \"updated_at\":\"2023-08-31T07:31:21.000Z\",\n" +
                "                \"canvas_size\":\"3x1\"}]}";*/

        LabelFxmlResponse labelFxmlResponse = new LabelFxmlResponse();
        labelFxmlResponse.setStatus("success");
        labelFxmlResponse.setMessage("Labels fetched successfully");
        labelFxmlResponse.setDataList(new ArrayList<>());

        Map<String, LabelFxmlResponse.Data> expected = labelFxmlResponse.getDataList().stream()
                .collect(Collectors.toMap(
                        LabelFxmlResponse.Data :: getName,
                        data -> data));

        final LoginResponseMessage responseMessage = new LoginResponseMessage();
        responseMessage.setUser("Test User");
        responseMessage.setLoginMsg("Test Login");

        MqttTopicMessage message = new MqttTopicMessage("/login/response",
                mapper.writeValueAsString(responseMessage));
        Mockito.when(communicatorService.getData("login/response")).thenReturn(mapper.writeValueAsString(expected));
        subscriber.onMessage(message);

        ArgumentCaptor<LoginEvent> eventCaptor =
                ArgumentCaptor.forClass(LoginEvent.class);
        verify(eventPublisher).publishEvent(eventCaptor.capture());

        LoginEvent capturedEvent = eventCaptor.getValue();
        assertEquals(subscriber, capturedEvent.getSource());
        assertEquals(responseMessage.getUser(), capturedEvent.getUserId());
        assertEquals(responseMessage.getLoginMsg(), capturedEvent.getLoginMsg());
    }
}
