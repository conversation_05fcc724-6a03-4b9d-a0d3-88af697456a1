package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.ios.IosPushFilesStatusEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.PushFilesStatusMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosPushFilesStatusSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosPushFilesStatusSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosPushFilesStatusSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final PushFilesStatusMessage pushFilesStatusMessage = new PushFilesStatusMessage();
        pushFilesStatusMessage.setId("id");
        pushFilesStatusMessage.setMandatoryFilesPushStatus(true);

        MqttTopicMessage message = new MqttTopicMessage(
                "/push-files/status",
                mapper.writeValueAsString(pushFilesStatusMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(IosPushFilesStatusEvent.class));
    }
}
