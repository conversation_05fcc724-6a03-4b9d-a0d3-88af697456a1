package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.event.device.DeviceBatteryInfoSuccessEvent;
import com.phonecheck.model.mqtt.messages.DeviceBatteryInfoSuccessMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class DeviceBatteryInfoCollectionSuccessSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private DeviceBatteryInfoSuccessSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new DeviceBatteryInfoSuccessSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final DeviceBatteryInfoSuccessMessage batteryInfoSuccessMessage = new DeviceBatteryInfoSuccessMessage();
        batteryInfoSuccessMessage.setId("id");
        batteryInfoSuccessMessage.setBatteryInfo(new BatteryInfo());
        batteryInfoSuccessMessage.setBatteryStateHealth(10);
        batteryInfoSuccessMessage.setBatteryPercentage(80);

        MqttTopicMessage message = new MqttTopicMessage(
                "battery/info/collection/success",
                mapper.writeValueAsString(batteryInfoSuccessMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(DeviceBatteryInfoSuccessEvent.class));
    }

}
