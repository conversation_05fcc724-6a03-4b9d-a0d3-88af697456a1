package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.ios.IosDiskImageMountFailureEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosDiskImageMountFailureMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosDiskImageMountFailureSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosDiskImageMountFailureSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosDiskImageMountFailureSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final IosDevice device = new IosDevice();
        device.setId("id");

        final IosDiskImageMountFailureMessage failureMessage = new IosDiskImageMountFailureMessage();
        failureMessage.setId(device.getId());

        MqttTopicMessage message = new MqttTopicMessage(
                TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "mount-image", "failure"),
                mapper.writeValueAsString(failureMessage));

        subscriber.onMessage(message);

        verify(eventPublisher, times(1)).publishEvent(any(IosDiskImageMountFailureEvent.class));
    }
}
