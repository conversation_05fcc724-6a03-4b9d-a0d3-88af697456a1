package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.device.android.AndroidInfoCollectionSuccessEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidInfoCollectionSuccessMessage;
import com.phonecheck.model.status.SimStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AndroidInfoCollectionSuccessSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private AndroidInfoCollectionSuccessSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new AndroidInfoCollectionSuccessSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        // Create the info collection success message and payload
        AndroidInfoCollectionSuccessMessage androidInfoCollectionSuccessMessage =
                new AndroidInfoCollectionSuccessMessage();
        androidInfoCollectionSuccessMessage.setId("1L");
        androidInfoCollectionSuccessMessage.setImei("123456789");
        androidInfoCollectionSuccessMessage.setSerial("ABC123");
        androidInfoCollectionSuccessMessage.setRegion("US");
        androidInfoCollectionSuccessMessage.setModelNo("iPhone X");
        androidInfoCollectionSuccessMessage.setSimStatus(SimStatus.READY);

        String payload = mapper.writeValueAsString(androidInfoCollectionSuccessMessage);

        MqttTopicMessage message = new MqttTopicMessage(
                "/info/collection/success", payload);
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(AndroidInfoCollectionSuccessEvent.class));


    }

}