package com.phonecheck.ui.subscriber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.UphInfoUpdateEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.UphInfoUpdateMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class UphInfoUpdateSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    private UphInfoUpdateSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new UphInfoUpdateSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final UphInfoUpdateMessage responseMessage = new UphInfoUpdateMessage();

        MqttTopicMessage message = new MqttTopicMessage("uph/info",
                mapper.writeValueAsString(responseMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(UphInfoUpdateEvent.class));
    }
}