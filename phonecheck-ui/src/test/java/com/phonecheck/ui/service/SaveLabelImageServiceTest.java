//package com.phonecheck.ui.service;
//
//import com.phonecheck.command.print.mac.GetMacPrinterDpiCommand;
//import com.phonecheck.executor.CommandExecutor;
//import com.phonecheck.model.device.IosDevice;
//import com.phonecheck.model.print.PrintOperation;
//import com.phonecheck.model.print.brother.BrotherRollType;
//import com.phonecheck.model.print.dymo.DymoPaperTray;
//import com.phonecheck.model.print.label.LabelOrientation;
//import com.phonecheck.model.print.label.LabelSizeUnit;
//import com.phonecheck.model.store.UiInMemoryStore;
//import com.phonecheck.model.util.LocalizationService;
//import com.phonecheck.model.util.OsChecker;
//import com.phonecheck.parser.print.mac.MacPrinterDpiParser;
//import com.phonecheck.ui.controller.MainController;
//import com.phonecheck.ui.controller.print.labels.AbstractLabelController;
//import com.phonecheck.ui.controller.utility.FXMLLoaderUtil;
//import javafx.fxml.FXMLLoader;
//import javafx.scene.layout.Pane;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.context.ApplicationContext;
//import org.testfx.framework.junit5.ApplicationExtension;
//
//import static org.mockito.Mockito.*;
//
//@ExtendWith({ApplicationExtension.class, MockitoExtension.class})
//public class SaveLabelImageServiceTest {
//    @Mock
//    private FXMLLoaderUtil fxmlLoaderUtil;
//    @Mock
//    private MacPrinterDpiParser macPrinterDpiParser;
//    @Mock
//    private FXMLLoader fxmlLoader;
//    @Mock
//    private AbstractLabelController controller;
//    @Mock
//    private OsChecker osChecker;
//    @Mock
//    private CommandExecutor executor;
//    @Mock
//    private UiInMemoryStore uiInMemoryStore;
//    @Mock
//    private ApplicationContext applicationContext;
//    @Mock
//    private MainController mainController;
//
//    @Mock
//    private LocalizationService localizationService;
//    @InjectMocks
//    private SaveLabelImageService saveLabelImageService;
//
//    @Test
//    public void testSaveLabelImageOperation() throws Exception {
//        // Arrange
//        String labelName = "Branded Label Landscape";
//        float width = 4;
//        float height = 2;
//        String zebraDpiCommandOutput = "300";
//
//        PrintOperation printOperation = new PrintOperation();
//        printOperation.setLabelName(labelName);
//        printOperation.setPrinterName("Zebra_Printer");
//        printOperation.setDevice(new IosDevice());
//        printOperation.setRollType(BrotherRollType.LABEL);
//        printOperation.setDymoPaperTray(DymoPaperTray.LEFT_P1);
//        printOperation.setCloudRollOrientation(LabelOrientation.PORTRAIT);
//
//        Pane pane = new Pane();
//        pane.setPrefWidth(width);
//        pane.setPrefHeight(height);
//
//        when(osChecker.isMac()).thenReturn(true);
//        when(fxmlLoaderUtil.getFxmlLoader(anyString())).thenReturn(fxmlLoader);
//        when(fxmlLoader.load()).thenReturn(pane);
//        when(fxmlLoader.getController()).thenReturn(controller);
//        when(executor.execute(any(GetMacPrinterDpiCommand.class))).thenReturn("300");
//        when(macPrinterDpiParser.parse(anyString())).thenReturn(300);
//        when(controller.getSizeUnit()).thenReturn(LabelSizeUnit.INCH);
//        when(controller.getWidth()).thenReturn(width);
//        when(controller.getHeight()).thenReturn(height);
//        when(controller.getHeight()).thenReturn(height);
//        when(controller.getLpCommandOptionsForDymo(
//                printOperation.getDymoPaperTray(),
//                printOperation.getCloudRollOrientation()
//        )).thenReturn("Width x Height");
//        when(controller.getLpCommandOptionsForZebra(eq(LabelOrientation.PORTRAIT)))
//                .thenReturn("Width x Height");
//        when(controller.getLpCommandOptionsForBrother(
//                eq(BrotherRollType.LABEL),
//                eq(LabelOrientation.PORTRAIT)))
//                .thenReturn("Width x Height");
//        when(applicationContext.getBean(MainController.class)).thenReturn(mainController);
//
//        // Act
//        saveLabelImageService.enqueueSaveLabelImageOperation(printOperation);
//        Thread.sleep(2000);
//
//        // Assert
//        verify(controller, timeout(10000)).initializeViews(printOperation.getDevice());
//        Thread.sleep(1000); // This sleep is to make sure that controller thread ends completely
//        verify(controller).getSizeUnit();
//        verify(controller).getWidth();
//        verify(controller).getHeight();
//        verify(controller).getLpCommandOptionsForDymo(
//                printOperation.getDymoPaperTray(), printOperation.getCloudRollOrientation()
//        );
//        verify(controller).getLpCommandOptionsForZebra(eq(LabelOrientation.PORTRAIT));
//        verify(controller).getLpCommandOptionsForBrother(
//                eq(BrotherRollType.LABEL), eq(LabelOrientation.PORTRAIT)
//        );
//        verify(controller).setUiInMemoryStore(uiInMemoryStore);
//        verify(macPrinterDpiParser).parse(eq(zebraDpiCommandOutput));
//        verify(executor).execute(any(GetMacPrinterDpiCommand.class));
//    }
//}
