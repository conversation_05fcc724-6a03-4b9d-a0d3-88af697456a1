<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import java.net.*?>
<?import javafx.collections.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.text.*?>

<StackPane fx:id="stackPane" style="-fx-background-color: transparent;" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1">
    <stylesheets>
        <URL value="@../../style/login.css" />
    </stylesheets>

    <children>
        <AnchorPane maxWidth="377.0" minWidth="377.0" prefHeight="690.0" prefWidth="375.0" styleClass="root">
            <children>
                <VBox prefHeight="690.0" prefWidth="375.0">
                    <children>
                        <HBox prefHeight="199.0" prefWidth="375.0" style="-fx-background-radius: 26 26 0 0; -fx-background-color: linear-gradient( #3C3C3C 0%, #000000 100%);">
                            <children>
                                <VBox maxHeight="113.0" prefHeight="113.0" prefWidth="137.0">
                                    <HBox.margin>
                                        <Insets left="116.0" top="35.0" />
                                    </HBox.margin>
                                    <ImageView fitHeight="81.03" fitWidth="88.29" pickOnBounds="true" preserveRatio="true">
                                        <VBox.margin>
                                            <Insets left="23.04" />
                                        </VBox.margin>
                                        <image>
                                            <Image url="@../../image/login-tick.png" />
                                        </image>
                                    </ImageView>
                                    <Label text="Phonecheck" textFill="WHITE">
                                        <font>
                                            <Font name="Helvetica Bold" size="23.0" />
                                        </font>
                                        <VBox.margin>
                                            <Insets top="2.0" />
                                        </VBox.margin>
                                    </Label>
                                </VBox>
                                <VBox maxHeight="39.0" prefHeight="39.0" prefWidth="39.0" style="-fx-background-color: #333333; -fx-background-radius: 20 20 20 20;">
                                    <HBox.margin>
                                        <Insets left="70.0" top="12.0" />
                                    </HBox.margin>
                                    <children>
                                        <ImageView fitHeight="24.0" fitWidth="20.0" onMouseClicked="#onCloseLoginScreenClicked" pickOnBounds="true" preserveRatio="true">
                                            <VBox.margin>
                                                <Insets left="10.0" top="9.0" />
                                            </VBox.margin>
                                            <Image url="@../../image/login-cross.png" />
                                        </ImageView>
                                    </children>
                                </VBox>
                            </children>
                        </HBox>
                        <Line endX="271.0" startX="-100.0" strokeWidth="6.0"
                              style="-fx-stroke: linear-gradient( #32A0B3 0%, #3EDC72 100%);"/>
                        <VBox fx:id="progressBarVBox" alignment="CENTER" prefHeight="412" prefWidth="-Infinity">
                            <Label fx:id="progressLabel" text="%phonecheckIsStarting" textFill="black">
                                <font>
                                    <Font name="Helvetica Bold" size="15.0" />
                                </font>
                                <VBox.margin>
                                    <Insets bottom="12.0" />
                                </VBox.margin>
                            </Label>
                            <ProgressBar prefHeight="12" prefWidth="300.0" stylesheets="@../../style/login-progress-bar.css" />
                        </VBox>
                        <VBox fx:id="updateDownloadLayout" alignment="CENTER" managed="false" prefHeight="412" prefWidth="-Infinity" visible="false">
                            <children>
                                <Text id="downloadUpdateText" strokeType="OUTSIDE" strokeWidth="0.0" text="Downloading Update">
                                    <font>
                                        <Font name="Helvetica" size="26.0" />
                                    </font>
                                </Text>
                            </children>
                        </VBox>
                        <VBox fx:id="errorVBox" alignment="CENTER" managed="false" prefHeight="412" prefWidth="-Infinity" visible="false">
                            <Label fx:id="errorLabel" textAlignment="CENTER" textFill="black">
                                <font>
                                    <Font name="Helvetica Bold" size="15.0" />
                                </font>
                                <VBox.margin>
                                    <Insets bottom="12.0" />
                                </VBox.margin>
                            </Label>
                        </VBox>
                        <VBox fx:id="loginErrorVBox" alignment="CENTER" managed="false" visible="false">
                            <VBox.margin>
                                <Insets top="130.0" />
                            </VBox.margin>
                            <ImageView preserveRatio="true">
                                <Image url="@../../image/login-error.png" />
                            </ImageView>

                            <Label text="Login Error" textAlignment="CENTER" textFill="black">
                                <font>
                                    <Font name="Helvetica" size="14.0" />
                                </font>
                                <VBox.margin>
                                    <Insets top="10.0" />
                                </VBox.margin>
                            </Label>
                            <Text fx:id="loginErrorText" fill="black" strokeType="OUTSIDE" textAlignment="CENTER" wrappingWidth="240">
                                <font>
                                    <Font name="Helvetica Bold" size="17.0" />
                                </font>
                                <VBox.margin>
                                    <Insets top="40.0" />
                                </VBox.margin>
                            </Text>
                            <Text fx:id="loginErrorEmailText" fill="royalblue" strokeType="OUTSIDE" textAlignment="CENTER">
                                <font>
                                    <Font name="Helvetica" size="18.0" />
                                </font>
                                <VBox.margin>
                                    <Insets top="10.0" />
                                </VBox.margin>
                            </Text>
                        </VBox>

                        <VBox fx:id="loginVBox" managed="false" maxHeight="445.0" visible="false">
                            <children>
                                <TextField fx:id="usernameTextField" focusTraversable="false" maxHeight="35.0" minHeight="35.0" onKeyTyped="#clearError" prefHeight="35.0" promptText="%username" style="-fx-background-insets: 0;     -fx-background-color: transparent, white, transparent, white;     -fx-background-radius: 0, 0, 0, 0;      -fx-box-border: none;     -fx-focus-color: -fx-control-inner-background;     -fx-faint-focus-color: -fx-control-inner-background;     -fx-text-box-border: -fx-control-inner-background;">
                                    <font>
                                        <Font name="Helvetica" size="17.0" />
                                    </font>
                                </TextField>
                                <Line endX="226.0" startX="-100.0" stroke="#e2e2e2">
                                    <VBox.margin>
                                        <Insets top="10.0" />
                                    </VBox.margin>
                                </Line>
                                <PasswordField fx:id="passwordTextField" focusTraversable="false" minHeight="32.0" onKeyTyped="#clearError" prefHeight="24.0" promptText="%password" style="-fx-background-insets: 0;     -fx-background-color: transparent, white, transparent, white;     -fx-background-radius: 0, 0, 0, 0;      -fx-box-border: none;     -fx-focus-color: -fx-control-inner-background;     -fx-faint-focus-color: -fx-control-inner-background;     -fx-text-box-border: -fx-control-inner-background;">
                                    <font>
                                        <Font name="Helvetica" size="17.0" />
                                    </font>
                                    <VBox.margin>
                                        <Insets top="10.0" />
                                    </VBox.margin>
                                </PasswordField>
                                <Line endX="226.0" startX="-100.0" stroke="#e2e2e2">
                                    <VBox.margin>
                                        <Insets top="10.0" />
                                    </VBox.margin>
                                </Line>
                                <HBox fx:id="infoContainer" alignment="CENTER_LEFT" minHeight="17.0">
                                    <Text fx:id="infoText" fill="#f50057" strokeType="OUTSIDE" strokeWidth="0.0" wrappingWidth="320.0">
                                        <font>
                                            <Font size="14.0" />
                                        </font>
                                    </Text>
                                </HBox>
                                <HBox minHeight="24.0">
                                    <VBox.margin>
                                        <Insets top="14.0" />
                                    </VBox.margin>
                                    <children>
                                        <TextField fx:id="testerTextFieldOptional" focusTraversable="false" maxHeight="24.0" minHeight="24.0" onKeyTyped="#clearError" prefHeight="24.0" prefWidth="240.0" promptText="%tester" style="-fx-background-insets: 0;     -fx-background-color: transparent, white, transparent, white;     -fx-background-radius: 0, 0, 0, 0;      -fx-box-border: none;     -fx-focus-color: -fx-control-inner-background;     -fx-faint-focus-color: -fx-control-inner-background;     -fx-text-box-border: -fx-control-inner-background;">
                                            <font>
                                                <Font name="Helvetica" size="17.0" />
                                            </font>
                                        </TextField>
                                        <Label text="%optional" textFill="#8f8f8f">
                                            <font>
                                                <Font name="Helvetica" size="17.0" />
                                            </font>
                                        </Label>
                                    </children>
                                </HBox>
                                <Line endX="226.0" startX="-100.0" stroke="#e2e2e2">
                                    <VBox.margin>
                                        <Insets top="10.0" />
                                    </VBox.margin>
                                </Line>
                                <VBox minHeight="47.0" prefHeight="47.0" prefWidth="100.0">
                                    <children>
                                        <Label text="Language" textFill="#8b8b8b">
                                            <font>
                                                <Font size="12.0" />
                                            </font>
                                            <VBox.margin>
                                                <Insets left="13.0" />
                                            </VBox.margin>
                                        </Label>
                                        <ComboBox fx:id="languageDropDown" prefHeight="32.0" prefWidth="320.0" style="-fx-background-color: transparent; -fx-font-size: 17;" value="English">
                                            <items>
                                                <FXCollections fx:factory="observableArrayList">
                                                    <String fx:value="English" />
                                                    <String fx:value="Portuguese" />
                                                    <String fx:value="German" />
                                                    <String fx:value="Spanish" />
                                                    <String fx:value="French" />
                                                    <String fx:value="Italian" />
                                                    <String fx:value="Simplified Chinese" />
                                                    <String fx:value="Japanese" />
                                                    <String fx:value="Korean" />
                                                    <String fx:value="Russian" />
                                                    <String fx:value="Polish" />
                                                </FXCollections>
                                            </items>
                                            <padding>
                                                <Insets top="-4.0" />
                                            </padding>
                                        </ComboBox>
                                    </children>
                                    <VBox.margin>
                                        <Insets top="4.0" />
                                    </VBox.margin>
                                </VBox>
                                <Line endX="226.0" startX="-100.0" stroke="#e2e2e2">
                                    <VBox.margin>
                                        <Insets top="6.0" />
                                    </VBox.margin>
                                </Line>
                                <HBox>
                                    <VBox.margin>
                                        <Insets left="10.0" top="23.0" />
                                    </VBox.margin>
                                    <RadioButton fx:id="stationRadioButton" focusTraversable="false" onAction="#userTypeRadioButtonClicked" prefHeight="19.0" prefWidth="113.0" selected="true" text="%station" textFill="#868686">
                                        <font>
                                            <Font name="Helvetica" size="15.0" />
                                        </font>
                                    </RadioButton>
                                    <RadioButton fx:id="testerRadioButton" focusTraversable="false" onAction="#userTypeRadioButtonClicked" prefHeight="22.0" prefWidth="103.0" text="%tester" textFill="#868686">
                                        <font>
                                            <Font name="Helvetica" size="15.0" />
                                        </font>
                                    </RadioButton>
                                </HBox>
                                <HBox minHeight="95.0">
                                    <children>
                                        <GridPane prefHeight="105.0" prefWidth="214.0">
                                            <columnConstraints>
                                                <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                            </columnConstraints>
                                            <rowConstraints>
                                                <RowConstraints />
                                                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                    <RowConstraints />
                                            </rowConstraints>
                                            <children>
                                                <CheckBox fx:id="downloadAppsCheckBox" focusTraversable="false" mnemonicParsing="false" selected="true" text="%downloadApps" textFill="#868686" GridPane.rowIndex="1">
                                                    <font>
                                                        <Font name="Helvetica" size="15.0" />
                                                    </font>
                                                    <GridPane.margin>
                                                        <Insets left="10.0" />
                                                    </GridPane.margin>
                                                </CheckBox>
                                                <CheckBox fx:id="newTransaction" focusTraversable="false" mnemonicParsing="false" selected="true" text="%newTransaction" textFill="#868686" GridPane.rowIndex="2">
                                                    <font>
                                                        <Font name="Helvetica" size="15.0" />
                                                    </font>
                                                    <GridPane.margin>
                                                        <Insets left="10.0" top="5.0" />
                                                    </GridPane.margin>
                                                </CheckBox>
                                                <CheckBox fx:id="rememberMe" focusTraversable="false" mnemonicParsing="false" selected="true" text="%rememberMe" textFill="#868686" GridPane.rowIndex="3">
                                                    <font>
                                                        <Font name="Helvetica" size="15.0" />
                                                    </font>
                                                    <GridPane.margin>
                                                        <Insets left="10.0" top="8.0" />
                                                    </GridPane.margin>
                                                </CheckBox>
                                                <CheckBox fx:id="refreshCache" focusTraversable="false" mnemonicParsing="false" text="%refreshCache" textFill="#868686" GridPane.rowIndex="4">
                                                    <font>
                                                        <Font name="Helvetica" size="15.0" />
                                                    </font>
                                                    <GridPane.margin>
                                                        <Insets left="10.0" top="5.0" />
                                                    </GridPane.margin>
                                                </CheckBox>
                                            </children>
                                        </GridPane>
                                        <Label fx:id="buildText" alignment="CENTER" prefHeight="25.0" style="-fx-background-color: #EEEEEE; -fx-background-radius: 6 6 6 6;" text="Build " textFill="#868686">
                                            <HBox.margin>
                                                <Insets top="70.0" />
                                            </HBox.margin>
                                            <font>
                                                <Font name="Helvetica" size="13.0" />
                                            </font>
                                            <padding>
                                                <Insets left="8.0" right="8.0" />
                                            </padding>
                                        </Label>
                                    </children>
                                    <VBox.margin>
                                        <Insets top="7.0" />
                                    </VBox.margin>
                                </HBox>
                                <HBox alignment="CENTER" minHeight="56.0" prefHeight="56.0" prefWidth="322.0">
                                    <Button fx:id="loginButton" onMouseClicked="#onLoginButtonClicked" prefHeight="56.0" prefWidth="322.0" styleClass="button-filled" text="%login.button" textAlignment="CENTER">
                                        <font>
                                            <Font name="Helvetica Bold" size="20.0" />
                                        </font>
                                    </Button>
                                    <VBox.margin>
                                        <Insets top="19.0" />
                                    </VBox.margin>
                                </HBox>
                            </children>
                            <VBox.margin>
                                <Insets left="22.0" right="26.0" top="24.0" />
                            </VBox.margin>
                        </VBox>
                        <VBox fx:id="updateAvailableVBox" alignment="CENTER" managed="false" maxHeight="445.0" visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                            <VBox>
                                <Label text="%updateAvailable" textFill="black">
                                    <VBox.margin>
                                        <Insets top="30.0" />
                                    </VBox.margin>
                                    <font>
                                        <Font name="Helvetica Bold" size="25.0" />
                                    </font>
                                </Label>
                                <Label text="%newVersionAvailable" textAlignment="CENTER" textFill="black">
                                    <VBox.margin>
                                        <Insets top="30.0" />
                                    </VBox.margin>
                                    <font>
                                        <Font size="16.0" />
                                    </font>
                                </Label>
                                <Text fill="black" text="%likeToUpdateNow" textAlignment="CENTER">
                                    <font>
                                        <Font size="16.0" />
                                    </font>
                                </Text>
                                <padding>
                                    <Insets left="25.0" right="25.0" top="10" />
                                </padding>
                            </VBox>
                            <Button fx:id="updateButton" minHeight="56.0" onMouseClicked="#onUpdateButtonClicked" prefHeight="56.0" prefWidth="322.0" styleClass="button-filled" text="%updateNow">
                                <font>
                                    <Font name="Helvetica Bold" size="20.0" />
                                </font>
                                <VBox.margin>
                                    <Insets top="160.0" />
                                </VBox.margin>
                            </Button>
                            <Button fx:id="updateLaterButton" minHeight="56.0" onMouseClicked="#onUpdateLaterButtonClicked" prefHeight="56.0" prefWidth="322.0" styleClass="button-outline" text="%later">
                                <font>
                                    <Font name="Helvetica Bold" size="20.0" />
                                </font>
                                <VBox.margin>
                                    <Insets top="20.0" />
                                </VBox.margin>
                            </Button>
                        </VBox>
                        <VBox fx:id="updateDownloadingVBox" alignment="CENTER" managed="false" prefHeight="445.0" visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                            <VBox>
                                <Label text="%installingUpdate" textFill="black">
                                    <font>
                                        <Font name="Helvetica Bold" size="25.0" />
                                    </font>
                                </Label>
                                <padding>
                                    <Insets left="30.0" right="25.0" />
                                </padding>
                                <VBox.margin>
                                    <Insets />
                                </VBox.margin>
                            </VBox>
                            <ProgressBar fx:id="updateProgressBar" prefHeight="22" prefWidth="300.0" stylesheets="@../../style/login-progress-bar.css">
                                <VBox.margin>
                                    <Insets top="30.0" />
                                </VBox.margin>
                            </ProgressBar>
                            <Button fx:id="cancelUpdateButton" onMouseClicked="#onCancelUpdateButtonClicked" prefHeight="56.0" prefWidth="322.0" styleClass="button-outline" text="%cancel">
                                <font>
                                    <Font name="Helvetica Bold" size="20.0" />
                                </font>
                                <VBox.margin>
                                    <Insets top="270.0" />
                                </VBox.margin>
                            </Button>
                        </VBox>
                    </children>
                </VBox>
                <AnchorPane fx:id="downloadingLayout" layoutX="439.0" layoutY="34.0" prefWidth="357.0" style="-fx-background-color: #fff;" styleClass="rounded-border" visible="false" AnchorPane.bottomAnchor="16.0" AnchorPane.rightAnchor="16.0" AnchorPane.topAnchor="22.0">
                    <VBox layoutX="-11.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                        <VBox VBox.vgrow="ALWAYS">
                            <HBox fx:id="progressContainer" alignment="CENTER" VBox.vgrow="ALWAYS">
                                <VBox fx:id="androidContainer" alignment="CENTER" HBox.hgrow="ALWAYS">
                                    <Text fx:id="androidProgressText" strokeType="OUTSIDE" strokeWidth="0.0" text="PhoneCheck Android" textAlignment="CENTER" wrappingWidth="124.0">
                                        <font>
                                            <Font size="12.0" />
                                        </font>
                                    </Text>
                                </VBox>
                                <HBox fx:id="appDivider" prefWidth="2.0" style="-fx-background-color: #E0E0E0;" HBox.hgrow="NEVER">
                                    <HBox.margin>
                                        <Insets bottom="50.0" left="2.0" right="2.0" top="50.0" />
                                    </HBox.margin>
                                </HBox>
                                <VBox fx:id="iosContainer" alignment="CENTER" HBox.hgrow="ALWAYS">
                                    <Text fx:id="iosProgressText" strokeType="OUTSIDE" strokeWidth="0.0" text="Phonecheck iOS App" textAlignment="CENTER" wrappingWidth="124.0">
                                        <font>
                                            <Font size="12.0" />
                                        </font>
                                    </Text>
                                </VBox>
                            </HBox>
                        </VBox>
                    </VBox>
                </AnchorPane>
                <VBox fx:id="progressLayout" alignment="CENTER" layoutX="485.0" layoutY="32.0" prefWidth="357.0" spacing="12.0" style="-fx-background-color: #fff;" styleClass="rounded-border" visible="false" AnchorPane.bottomAnchor="16.0" AnchorPane.rightAnchor="16.0" AnchorPane.topAnchor="22.0">
                    <Text strokeType="OUTSIDE" strokeWidth="0.0" text="%downloading" />
                </VBox>
            </children>
        </AnchorPane>
    </children>
    <padding>
        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
    </padding>
</StackPane>
