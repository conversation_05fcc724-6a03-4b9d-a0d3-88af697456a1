<?xml version="1.0" encoding="UTF-8"?>

<?import com.jfoenix.controls.JFXToolbar?>
<?import de.jensd.fx.glyphs.materialdesignicons.MaterialDesignIconView?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<?import javafx.scene.text.Text?>
<?import java.net.URL?>
<VBox maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="70.0"
      xmlns="http://javafx.com/javafx/8.0.171"
      xmlns:fx="http://javafx.com/fxml/1">
    <stylesheets>
        <URL value="@../../style/toolbar.css"/>
    </stylesheets>
    <JFXToolbar maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minHeight="-Infinity"
                prefHeight="45.0" styleClass="toolbar" VBox.vgrow="ALWAYS">
        <padding>
            <Insets left="8.0" right="8.0"/>
        </padding>
        <center>
            <Text id="toolbar_title" fx:id="titleText" fill="WHITE" strokeType="OUTSIDE" strokeWidth="0.0"
                  styleClass="toolbar_title" textAlignment="CENTER" wrappingWidth="600.0" BorderPane.alignment="CENTER">
                <font>
                    <Font name="System Bold" size="16.0"/>
                </font>
            </Text>
        </center>
        <right>
            <VBox id="toolbar_close" alignment="CENTER" styleClass="close-button"
                  HBox.hgrow="NEVER">
                <MaterialDesignIconView id="closeButton" glyphName="CLOSE" size="24" AnchorPane.rightAnchor="0.0"
                                        AnchorPane.topAnchor="8.0">
                    <cursor>
                        <Cursor fx:constant="HAND"/>
                    </cursor>
                </MaterialDesignIconView>

                <padding>
                    <Insets left="-12.0"/>
                </padding>
                <cursor>
                    <Cursor fx:constant="HAND"/>
                </cursor>
            </VBox>
        </right>
    </JFXToolbar>
</VBox>
