<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<?import java.net.URL?>
<AnchorPane fx:id="root" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="200.0"
            xmlns="http://javafx.com/javafx/8.0.171" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.phonecheck.ui.controller.device.QuestionsController" style="-fx-margin: 7 0 0 0">
    <children>
        <VBox fx:id="subQuestionVB" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="40.0"
              minWidth="-Infinity" prefHeight="40.0" prefWidth="200.0" style="-fx-background-color: white;"
              visible="false">
            <children>
                <HBox alignment="CENTER" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="40.0"
                      minWidth="-Infinity" prefHeight="40.0" prefWidth="200.0" style="-fx-background-color: white;">
                    <children>
                        <HBox alignment="CENTER" prefHeight="40.0" prefWidth="180.0">
                            <children>
                                <Label fx:id="questionLBL" minHeight="40.0" prefHeight="40.0" prefWidth="180.0"
                                       text="%doesTheDevicePowerUpAndFunctionNormally" wrapText="true">
                                    <font>
                                        <Font name="Helvetica Bold" size="11.0"/>
                                    </font>
                                </Label>
                            </children>
                        </HBox>
                        <HBox alignment="CENTER" prefHeight="40.0" minHeight="40.0" prefWidth="113.0" spacing="4.0">
                            <children>
                                <AnchorPane maxHeight="-Infinity" minHeight="38.0" prefHeight="38.0"
                                            prefWidth="50.0">
                                    <children>
                                        <Label fx:id="noUnselected" onMouseClicked="#noButton"
                                               styleClass="options-btn" text="%no">
                                            <padding>
                                                <Insets bottom="1.0" left="15.0" right="15.0" top="100.0"/>
                                            </padding>
                                        </Label>
                                        <Label fx:id="noSelected" onMouseClicked="#noButton"
                                               styleClass="options-btn"
                                               text="%no" visible="false">
                                            <padding>
                                                <Insets bottom="2.0" left="16.0" right="15.0" top="2.0"/>
                                            </padding>
                                        </Label>
                                    </children>
                                </AnchorPane>
                                <AnchorPane maxHeight="-Infinity" minHeight="-Infinity" prefHeight="38.0"
                                            prefWidth="53.0">
                                    <children>
                                        <Label fx:id="yesUnselected" onMouseClicked="#yesButton"
                                               styleClass="options-btn" text="%yes">
                                            <padding>
                                                <Insets bottom="2.0" left="15.0" right="15.0" top="2.0"/>
                                            </padding>
                                            <font>
                                                <Font name="Helvetica" size="13.0"/>
                                            </font>
                                        </Label>
                                        <Label fx:id="yesSelected" onMouseClicked="#yesButton" styleClass="options-btn"
                                               text="%yes" visible="false">
                                            <padding>
                                                <Insets bottom="2.0" left="16.0" right="15.0" top="3.0"/>
                                            </padding>
                                            <font>
                                                <Font name="Helvetica" size="13.0"/>
                                            </font>
                                        </Label>
                                    </children>
                                </AnchorPane>
                            </children>
                        </HBox>
                    </children>
                </HBox>
                <VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity"
                      prefHeight="2.0" prefWidth="300.0" style="-fx-background-color: #aeaeae;"/>
            </children>
            <VBox.margin>
                <Insets top="5.0"/>
            </VBox.margin>
        </VBox>
        <StackPane fx:id="multiQuesVB" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="200.0"
                   AnchorPane.bottomAnchor="0.0" AnchorPane.topAnchor="1.0">
            <children>
                <Label fx:id="quesLBL" alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="200.0"
                       minHeight="38.0" prefHeight="38.0" styleClass="options-btn" text="%yes"
                       textAlignment="CENTER" wrapText="true">
                    <padding>
                        <Insets bottom="1.1" left="14.0" right="14.0" top="1.1"/>
                    </padding>
                    <font>
                        <Font name="Helvetica Bold" size="13.0"/>
                    </font>
                </Label>
            </children>
            <VBox.margin>
                <Insets top="20.0"/>
            </VBox.margin>
        </StackPane>
    </children>
    <stylesheets>
        <URL value="@../../style/grading-system.css"/>
    </stylesheets>
</AnchorPane>