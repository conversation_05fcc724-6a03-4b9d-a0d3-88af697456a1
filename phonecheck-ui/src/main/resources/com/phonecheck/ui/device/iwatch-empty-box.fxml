<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<?import java.net.URL?>
<AnchorPane fx:id="parentAnchor" prefHeight="450.0" prefWidth="230.0" styleClass="device-box"
            xmlns="http://javafx.com/javafx/8.0.171" xmlns:fx="http://javafx.com/fxml/1">
    <stylesheets>
        <URL value="@../../style/device-box.css"/>
    </stylesheets>
    <AnchorPane fx:id="iWatchPortNumberAnchor" prefHeight="450.0" prefWidth="230.0" AnchorPane.bottomAnchor="0.0"
                AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <HBox alignment="CENTER_RIGHT" prefHeight="50.0" prefWidth="200.0" AnchorPane.leftAnchor="0.0"
              AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
            <children>
                <Label fx:id="iWatchPortLabel" text="0">
                    <font>
                        <Font name="Helvetica" size="30.0"/>
                    </font>
                    <padding>
                        <Insets right="10.0"/>
                    </padding>
                </Label>
            </children>
        </HBox>
        <AnchorPane prefHeight="400.0" prefWidth="230.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0"
                    AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="50.0">
            <VBox alignment="TOP_CENTER" prefHeight="400.0" prefWidth="230.0" AnchorPane.leftAnchor="0.0"
                  AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
            <children>
                <ImageView fitHeight="200.0" fitWidth="230.0" pickOnBounds="true" preserveRatio="true"
                           AnchorPane.bottomAnchor="5.0" AnchorPane.leftAnchor="5.0" AnchorPane.rightAnchor="5.0"
                           AnchorPane.topAnchor="5.0">
                    <Image url="@../../image/configure-host.png"/>
                </ImageView>
                <Label text="Connect iPhone with Cable"/>
            </children>
                <padding>
                    <Insets top="75.0"/>
                </padding>
            </VBox>
        </AnchorPane>
    </AnchorPane>
</AnchorPane>
