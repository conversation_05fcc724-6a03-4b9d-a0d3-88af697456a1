<?xml version="1.0" encoding="UTF-8"?>

<?import com.jfoenix.controls.JFXButton?>
<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.Line?>
<?import javafx.scene.text.Font?>
<?import java.net.URL?>
<AnchorPane fx:id="root" prefHeight="560.0" prefWidth="849.0"
            styleClass="root"
            xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1">
    <stylesheets>
        <URL value="@../../style/transaction.css"/>
    </stylesheets>
    <AnchorPane fx:id="topBar" style="-fx-background-color: #ffffff; -fx-background-radius: 10 10 0 0;"
                AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <VBox AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="5.0">
            <children>
                <Label alignment="CENTER" text="%selectTransaction" textFill="black" styleClass="label-bold-text">
                    <HBox.margin>
                        <Insets left="10.0"/>
                    </HBox.margin>
                </Label>
                <Line endX="50.0" startX="-90.0" stroke="black" strokeWidth="2">
                    <VBox.margin>
                        <Insets top="5.0"/>
                    </VBox.margin>
                </Line>
            </children>
            <padding>
                <Insets bottom="10.0" left="10.0" right="5.0" top="10.0"/>
            </padding>
        </VBox>
    </AnchorPane>

    <AnchorPane fx:id="transactionChangeWaitingAnchor" layoutX="200.0" layoutY="150.0"  prefHeight="220"
                prefWidth="200" visible="false">
    <ProgressIndicator fx:id="transactionChangeWaitingBar" layoutX="200.0" layoutY="150.0"  prefHeight="120"
                       prefWidth="120" visible="false"/>
    </AnchorPane>

    <VBox fx:id="mainContentVBox"
          style="-fx-background-color: white; -fx-background-radius: 0 0 10 10; -fx-border-radius: 0 0 10 10;"
          visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0"
          AnchorPane.topAnchor="40.0">
        <HBox alignment="BOTTOM_LEFT">
            <TextField id="searchField" fx:id="searchView" focusTraversable="false" maxHeight="25.0" maxWidth="600.0"
                       minHeight="25.0" minWidth="600.0" prefHeight="25.0" prefWidth="600.0" promptText="%search"
                       style="-fx-background-color: transparent; -fx-border-color: transparent transparent #CCCCCC transparent; -fx-border-width: 0 0 1 0;"
                       HBox.hgrow="ALWAYS">
                <HBox.margin>
                    <Insets left="10.0"/>
                </HBox.margin>
                <font>
                    <Font size="15.0"/>
                </font>
            </TextField>
            <ImageView fitHeight="16.0" fitWidth="16.0" pickOnBounds="true" preserveRatio="true">
                <image>
                    <Image url="@../../image/icon/search-icon.png"/>
                </image>
                <HBox.margin>
                    <Insets bottom="5.0" left="-15.0"/>
                </HBox.margin>
            </ImageView>
            <padding>
                <Insets bottom="10.0" left="10.0" right="10.0"/>
            </padding>
            <VBox.margin>
                <Insets top="20.0"/>
            </VBox.margin>
        </HBox>
        <HBox VBox.vgrow="ALWAYS" fx:id="tableHBox">
            <children>
            </children>
            <VBox.margin>
                <Insets top="20.0"/>
            </VBox.margin>
            <padding>
                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
            </padding>
        </HBox>
        <HBox fx:id="statusBar" alignment="CENTER_RIGHT" prefHeight="35.0" prefWidth="1860.0">
            <children>
                <JFXButton fx:id="moveTransactionButton" prefHeight="26.0" prefWidth="146.0"
                           style="-fx-background-color: #157df2; -fx-font-weight: bold;" text="%move"
                           disable="true" onMouseClicked="#moveTransactionClicked"
                           textFill="WHITE">
                    <HBox.margin>
                        <Insets bottom="10.0" right="40.0" top="13.0"/>
                    </HBox.margin>
                    <font>
                        <Font name="System Bold" size="11.0"/>
                    </font>
                </JFXButton>
                <JFXButton fx:id="cancelButton" prefHeight="26.0" prefWidth="146.0"
                           style="-fx-background-color: #157df2; -fx-font-weight: bold;" text="%cancel"
                           onMouseClicked="#cancelButtonClicked"
                           textFill="WHITE">
                    <HBox.margin>
                        <Insets bottom="10.0" right="40.0" top="13.0"/>
                    </HBox.margin>
                    <font>
                        <Font name="System Bold" size="11.0"/>
                    </font>
                </JFXButton>
            </children>
            <padding>
                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
            </padding>
        </HBox>
    </VBox>
    <padding>
        <Insets bottom="4.0" left="4.0" right="4.0" top="4.0"/>
    </padding>
    <AnchorPane fx:id="transactionChangeResponsePane" layoutX="200.0" layoutY="150.0" prefHeight="180.0"
                prefWidth="350.0" visible="false">
    <VBox fx:id="transactionChangeResDialog" prefHeight="250" prefWidth="550" style="-fx-background-color: #ffffff;
        -fx-background-radius: 0 0 10 10; -fx-border-radius: 0 0 10 10;" alignment="CENTER" spacing="40">
        <Label fx:id="dialogMessage" styleClass="label-bold-text" text="%transactionMovedSuccessfully" wrapText="true"/>
        <padding><Insets bottom="80.0" left="8.0" right="8.0" top="10.0" /></padding>
        <Button text="%ok"  style="-fx-background-color: #157df2" textFill="white" onAction="#onOkClick"/>
    </VBox>
    </AnchorPane>
</AnchorPane>