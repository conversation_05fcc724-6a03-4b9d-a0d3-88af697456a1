<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.effect.ColorAdjust?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import java.net.URL?>
<AnchorPane maxHeight="-Infinity" minHeight="-Infinity" prefHeight="928.0" prefWidth="460.0" style="-fx-background-color: white;" translateX="3.0" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.phonecheck.ui.controller.print.labels.LpnLabelPortraitController">
    <stylesheets>
        <URL value="@../../style/branded-label.css" />
    </stylesheets>
    <VBox AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <children>
            <StackPane alignment="TOP_CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefHeight="82.0" prefWidth="450.0">
                <children>
                    <HBox>
                        <ImageView fx:id="pcLogo" fitHeight="82.0" fitWidth="301.0" nodeOrientation="INHERIT" pickOnBounds="true" preserveRatio="true">
                            <image>
                            </image>
                            <HBox.margin>
                                <Insets />
                            </HBox.margin>
                        </ImageView>
                        <ImageView fx:id="fullyFunctionalLogo" fitHeight="25.0" fitWidth="29.0" nodeOrientation="INHERIT" pickOnBounds="true" preserveRatio="true" visible="false">
                            <image>
                            </image>
                            <HBox.margin>
                                <Insets left="81.0" top="25"/>
                            </HBox.margin>
                        </ImageView>
                    </HBox>
                </children>
            </StackPane>
            <VBox minWidth="-Infinity" prefHeight="36.0" prefWidth="450.0">
                <children>
                    <Text fx:id="title" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="%title" visible="false">
                        <font>
                            <Font size="24.0" />
                        </font>
                    </Text>
                    <HBox>
                        <Text fx:id="carrier" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="%carrier" visible="false">
                            <font>
                                <Font size="24.0" />
                            </font>
                        </Text>
                        <ImageView fx:id="networkLockImageView" fitHeight="22.0" fitWidth="22.0" pickOnBounds="true" preserveRatio="true">
                            <image>
                            </image>
                            <effect>
                                <ColorAdjust brightness="-1.0" contrast="-1.0" hue="-1.0" saturation="-1.0" />
                            </effect>
                            <HBox.margin>
                                <Insets left="5.0" top="3.0" />
                            </HBox.margin>
                        </ImageView>
                    </HBox>
                </children>
                <VBox.margin>
                    <Insets top="10.0" />
                </VBox.margin>
            </VBox>
            <VBox fx:id="imeiElementLayout" minWidth="-Infinity" prefHeight="55.0" prefWidth="450.0" visible="false">
                <HBox spacing="4.0">
                    <children>
                        <Text strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%imeiColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="imeiElementText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%imei">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox alignment="CENTER"  maxWidth="-Infinity" minWidth="-Infinity" prefWidth="450.0">
                    <children>
                        <ImageView fx:id="imeiBarcodeImageView" fitHeight="50.0" fitWidth="460.0" pickOnBounds="true">
                            <image>
                            </image>
                            <HBox.margin>
                                <Insets top="5.0" />
                            </HBox.margin>
                        </ImageView>
                    </children>
                </HBox>
                <VBox.margin>
                    <Insets top="10.0" />
                </VBox.margin>
            </VBox>

            <VBox fx:id="lpnElementLayout" managed="false" minWidth="-Infinity" prefHeight="55.0" prefWidth="450.0"  visible="false">
                <children>
                    <HBox spacing="4.0">
                        <children>
                            <Text strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%lpnColon">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="lpnElementText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%lpn">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <!-- disable these barcodes for recirQ-->
                    <HBox alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="450.0" >
                        <children>
                            <ImageView fx:id="lpnBarcodeImageView" fitHeight="50.0" fitWidth="460.0" pickOnBounds="true">
                                <image>
                                </image>
                                <HBox.margin>
                                    <Insets top="5.0" />
                                </HBox.margin>
                            </ImageView>
                        </children>
                    </HBox>
                </children>
                <VBox.margin>
                    <Insets top="5.0" />
                </VBox.margin>
            </VBox>
            <VBox fx:id="serialLayout" minWidth="-Infinity" prefHeight="55.0" prefWidth="450.0" visible="false">
                <children>
                    <HBox spacing="4.0">
                        <children>
                            <Text strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%serialNo">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="serial" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%serial">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <!-- disable these barcodes for recirQ-->
                    <HBox alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="450.0"
                          visible="false" managed="false">
                        <children>
                            <ImageView fx:id="serialBarcodeImageView" fitHeight="50.0" fitWidth="460.0">
                                <image>
                                </image>
                                <HBox.margin>
                                    <Insets top="5.0" />
                                </HBox.margin>
                            </ImageView>
                        </children>
                    </HBox>
                </children>
                <VBox.margin>
                    <Insets top="5.0" />
                </VBox.margin>
            </VBox>
            <VBox minWidth="-Infinity" prefHeight="330.0" prefWidth="450.0">
                <HBox fx:id="gradeLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="gradeHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%gradeColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="grade" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%grade">
                            <font>
                                <Font size="18.0" />
                            </font>
                            <HBox.margin>
                                <Insets />
                            </HBox.margin>
                        </Text>
                    </children>
                    <padding>
                        <Insets top="10.0" />
                    </padding>
                </HBox>
                <HBox fx:id="colorLayout" layoutX="10.0" layoutY="10.0" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="colorHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%colorColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="color" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%color">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="batteryHealthPercentageLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="batteryHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%batteryColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="batteryHealthPercentage" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="versionLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="versionHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%versionColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="version" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%version">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="rootedLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="rootedHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%jailbreakRooted">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="rootedText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%jailbreak">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="esnLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="esnHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%esnColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="esn" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%esn">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="deviceLockLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="fmiHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%fmiFrp">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="deviceLock" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%fmiFrpStatus">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="cosmeticsLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="cosmeticsHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%cosmeticsColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="cosmeticsText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" wrappingWidth="400.0">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="erasureLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="erasureHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%erasureColon">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="erasureText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" wrappingWidth="400.0">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="functionalityLayout" spacing="4.0">
                    <children>
                        <Text fx:id="functionalityHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%functionality">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="functionality" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%seeNotes">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                </HBox>
                <VBox fx:id="defectsLayout" prefHeight="100.0" style="-fx-border-color: black; -fx-border-width: 1;" visible="false">
                    <children>
                        <Text fx:id="defects" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" wrappingWidth="435.0">
                            <font>
                                <Font size="18.0" />
                            </font>
                            <VBox.margin>
                                <Insets left="3.0" top="3.0" />
                            </VBox.margin>
                        </Text>
                    </children>
                    <HBox.margin>
                        <Insets />
                    </HBox.margin>
                    <padding>
                        <Insets bottom="2.0" left="2.0" right="2.0" top="2.0" />
                    </padding>
                    <VBox.margin>
                        <Insets bottom="5.0" right="5.0" top="10.0" />
                    </VBox.margin>
                </VBox>
            </VBox>
            <HBox>
                <VBox maxWidth="-Infinity" minWidth="-Infinity" prefWidth="340.0">
                    <HBox fx:id="customLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="customHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%customOneColon">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="custom" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%custom">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="dateLayout" layoutX="10.0" layoutY="10.0" spacing="4.0">
                        <children>
                            <Text fx:id="dateHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%dateColon">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="date" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="vendorInvoiceLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="vendorHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%vendorColon">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="vendorInvoice" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%vendor">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="portNumberLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="portNumberHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%port">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="portNumber" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                        <HBox.margin>
                            <Insets bottom="15.0" />
                        </HBox.margin>
                    </HBox>
                    <HBox fx:id="userLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="userHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%userColon">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                            <Text fx:id="user" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%user">
                                <font>
                                    <Font size="18.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox.margin>
                        <Insets top="30.0" />
                    </HBox.margin>
                </VBox>
                <VBox alignment="CENTER" spacing="2.0">
                    <ImageView fx:id="pcWebQr" fitHeight="90.0" fitWidth="90.0" nodeOrientation="INHERIT" preserveRatio="true">
                        <image>
                        </image>
                        <HBox.margin>
                            <Insets />
                        </HBox.margin>
                    </ImageView>
                    <Text fx:id="pcQrLabel" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="PHONECHECK">
                        <font>
                            <Font size="14.0" />
                        </font>
                    </Text>
                </VBox>
                <VBox.margin>
                    <Insets bottom="2.0" />
                </VBox.margin>
            </HBox>
        </children>
    </VBox>
    <padding>
        <Insets left="5.0" right="5.0" />
    </padding>
</AnchorPane>
