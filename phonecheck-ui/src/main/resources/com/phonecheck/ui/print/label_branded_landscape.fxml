<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.effect.ColorAdjust?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import java.net.URL?>
<HBox alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefHeight="300.0" prefWidth="625.0"
      style="-fx-background-color: white;" xmlns="http://javafx.com/javafx/17.0.2-ea"
      xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="com.phonecheck.ui.controller.print.labels.BrandedLabelLandscapeController">
    <stylesheets>
        <URL value="@../../style/branded-label.css"/>
    </stylesheets>
    <VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="295.0"
          prefWidth="312.0">
        <StackPane alignment="TOP_LEFT">
            <children>
                <ImageView fx:id="pcLogo" fitHeight="70.0" fitWidth="300.0" nodeOrientation="INHERIT"
                           pickOnBounds="true" preserveRatio="true" StackPane.alignment="TOP_LEFT">
                    <image>
                    </image>
                    <HBox.margin>
                        <Insets/>
                    </HBox.margin>
                </ImageView>
            </children>
        </StackPane>
        <VBox>
            <children>
                <Text fx:id="title" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="%title"
                      visible="false">
                    <font>
                        <Font size="15.0"/>
                    </font>
                </Text>
                <HBox>
                    <Text fx:id="carrier" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="%carrier"
                          visible="false">
                        <font>
                            <Font size="15.0"/>
                        </font>
                        <HBox.margin>
                            <Insets top="3.0"/>
                        </HBox.margin>
                    </Text>
                    <ImageView fx:id="networkLockImageView" fitHeight="18.0" fitWidth="18.0" pickOnBounds="true"
                               preserveRatio="true">
                        <image>
                        </image>
                        <effect>
                            <ColorAdjust brightness="-1.0" contrast="-1.0" hue="-1.0" saturation="-1.0"/>
                        </effect>
                        <HBox.margin>
                            <Insets left="5.0" top="2.0"/>
                        </HBox.margin>
                    </ImageView>
                </HBox>
            </children>
            <VBox.margin>
                <Insets/>
            </VBox.margin>
        </VBox>
        <VBox fx:id="imeiElementLayout" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="300.0" visible="false">
            <HBox spacing="4.0">
                <children>
                    <Text fx:id="imeiHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="%imei">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                    <Text fx:id="imeiElementText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="%imei">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                </children>
            </HBox>
            <HBox alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="300.0">
                <children>
                    <ImageView fx:id="imeiBarcodeImageView" fitHeight="30.0" fitWidth="300.0" pickOnBounds="true">
                        <image>
                        </image>
                        <HBox.margin>
                            <Insets top="5.0"/>
                        </HBox.margin>
                    </ImageView>
                </children>
            </HBox>
            <VBox.margin>
                <Insets top="5.0"/>
            </VBox.margin>
        </VBox>

        <VBox fx:id="customElementLayout" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="300.0" visible="false">
            <children>
                <HBox spacing="4.0">
                    <children>
                        <Text strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%modelNo">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="customElementText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%customElement">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="300.0">
                    <children>
                        <ImageView fx:id="modelBarcodeImageView" fitHeight="30.0" fitWidth="300.0" pickOnBounds="true">
                            <image>
                            </image>
                            <HBox.margin>
                                <Insets top="5.0"/>
                            </HBox.margin>
                        </ImageView>
                    </children>
                </HBox>
            </children>
            <VBox.margin>
                <Insets top="5.0"/>
            </VBox.margin>
        </VBox>
        <VBox fx:id="serialLayout" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="300.0" visible="false">
            <children>
                <HBox spacing="4.0">
                    <children>
                        <Text strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%serialNo">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="serial" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%serial">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox alignment="CENTER" maxWidth="-Infinity" minWidth="-Infinity" prefWidth="300.0">
                    <children>
                        <ImageView fx:id="serialBarcodeImageView" fitHeight="30.0" fitWidth="300.0">
                            <image>
                            </image>
                            <HBox.margin>
                                <Insets top="5.0"/>
                            </HBox.margin>
                        </ImageView>
                    </children>
                </HBox>
            </children>
            <VBox.margin>
                <Insets top="5.0"/>
            </VBox.margin>
        </VBox>
        <padding>
            <Insets left="10.0"/>
        </padding>
    </VBox>
    <VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="290.0"
          prefWidth="312.0">
        <VBox maxWidth="-Infinity" minWidth="-Infinity" prefHeight="240.0" prefWidth="312.0">
            <HBox>
                <VBox maxWidth="-Infinity" minWidth="-Infinity" prefWidth="220.0">
                    <HBox fx:id="gradeLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="gradeHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%gradeColon">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="grade" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%grade">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                                <HBox.margin>
                                    <Insets/>
                                </HBox.margin>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="colorLayout" layoutX="10.0" layoutY="10.0" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="colorHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%colorColon">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="color" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%color">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="batteryHealthPercentageLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="batteryHeading" strokeType="OUTSIDE" strokeWidth="0.0"
                                  styleClass="text-regular" text="%batteryColon">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="batteryHealthPercentage" strokeType="OUTSIDE" strokeWidth="0.0"
                                  styleClass="text-regular" text="00%">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="batteryCycleLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="batteryCycleHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%bbc">
                                <font>
                                    <Font size="14.0" />
                                </font>
                            </Text>
                            <Text fx:id="batteryCycleText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                                <font>
                                    <Font size="14.0" />
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="versionLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="versionHeading" strokeType="OUTSIDE" strokeWidth="0.0"
                                  styleClass="text-regular" text="%versionColon">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="version" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%version">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="firmwareLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="firmwareHeading" strokeType="OUTSIDE" strokeWidth="0.0"
                                  styleClass="text-regular" text="%firmNetworkColon">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="firmware" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%firmNetwork">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="esnLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="esnHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%esnColon">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="esn" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%esn">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                        </children>
                    </HBox>
                    <HBox fx:id="deviceLockLayout" spacing="4.0" visible="false">
                        <children>
                            <Text fx:id="fmiHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%fmiFrp">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                            <Text fx:id="deviceLock" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                                  text="%fmiFrpStatus">
                                <font>
                                    <Font size="14.0"/>
                                </font>
                            </Text>
                        </children>
                    </HBox>
                </VBox>
                <VBox alignment="CENTER" prefWidth="75.0" spacing="2.0">
                    <ImageView fx:id="fullyFunctionalLogo" fitHeight="25.0" fitWidth="29.0" pickOnBounds="true"
                               preserveRatio="true" visible="false">
                        <image>
                        </image>
                        <VBox.margin>
                            <Insets left="38.0"/>
                        </VBox.margin>
                    </ImageView>
                    <ImageView fx:id="pcWebQr" fitHeight="75.0" fitWidth="75.0" nodeOrientation="INHERIT"
                               preserveRatio="true">
                        <image>
                        </image>
                        <HBox.margin>
                            <Insets/>
                        </HBox.margin>
                    </ImageView>
                    <Text fx:id="pcQrLabel" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="PHONECHECK">
                        <font>
                            <Font size="10.0"/>
                        </font>
                    </Text>
                </VBox>
            </HBox>
            <HBox fx:id="cosmeticsLayout" spacing="4.0" visible="false">
                <children>
                    <Text fx:id="cosmeticsHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="%cosmeticsColon">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                    <Text fx:id="cosmeticsText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          wrappingWidth="220.0">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                </children>
            </HBox>
            <HBox fx:id="erasureLayout" spacing="4.0" visible="false">
                <children>
                    <Text fx:id="erasureHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="%erasureColon">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                    <Text fx:id="erasureText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          wrappingWidth="235.0">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                </children>
            </HBox>
            <HBox fx:id="oemLayout" spacing="4.0" visible="false">
                <children>
                    <Text fx:id="oemHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%oemParts">
                        <font>
                            <Font size="14.0" />
                        </font>
                    </Text>
                    <ImageView fx:id="oemStatusImage" fitHeight="15" fitWidth="15">
                        <image>
                        </image>
                    </ImageView>
                </children>
            </HBox>
            <HBox fx:id="functionalityLayout" spacing="4.0">
                <children>
                    <Text fx:id="functionalityHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="%functionality">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                    <Text fx:id="functionality" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          text="%seeNotes">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                </children>
            </HBox>
            <VBox fx:id="defectsLayout" prefHeight="85.0" style="-fx-border-color: black; -fx-border-width: 1;"
                  visible="false">
                <children>
                    <Text fx:id="defects" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                          wrappingWidth="300.0">
                        <font>
                            <Font size="14.0"/>
                        </font>
                    </Text>
                </children>
                <HBox.margin>
                    <Insets/>
                </HBox.margin>
                <padding>
                    <Insets bottom="2.0" left="2.0" right="2.0" top="2.0"/>
                </padding>
                <VBox.margin>
                    <Insets bottom="5.0" right="5.0" top="5.0"/>
                </VBox.margin>
            </VBox>
        </VBox>
        <HBox maxWidth="-Infinity" minWidth="-Infinity" prefWidth="312.0" spacing="5.0">
            <VBox prefWidth="155.0">
                <Pane maxWidth="-Infinity" minHeight="-Infinity" prefHeight="16.0"/>
                <HBox fx:id="customLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="customHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%customOneColon">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="custom" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%custom">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="portNumberLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="portNumberHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%port">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="portNumber" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="userLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="userHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%userColon">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="user" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%user">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>
            </VBox>

            <VBox HBox.hgrow="NEVER">
                <HBox fx:id="dateLayout" layoutX="10.0" layoutY="10.0" maxWidth="-Infinity" minWidth="-Infinity"
                      prefWidth="130.0" spacing="4.0">
                    <children>
                        <Text fx:id="dateHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%dateColon">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="date" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>
                <HBox fx:id="vendorInvoiceLayout" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="vendorHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%vendorColon">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                        <Text fx:id="vendorInvoice" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular"
                              text="%vendor">
                            <font>
                                <Font size="14.0"/>
                            </font>
                        </Text>
                    </children>
                </HBox>

            </VBox>
        </HBox>
        <padding>
            <Insets left="5.0"/>
        </padding>
    </VBox>
</HBox>
