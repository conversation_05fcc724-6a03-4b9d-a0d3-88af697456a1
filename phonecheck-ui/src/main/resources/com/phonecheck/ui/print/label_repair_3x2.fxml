<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox fx:id="rootPane" alignment="TOP_CENTER" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="333.0" prefWidth="500.0" spacing="4.0" style="-fx-background-color: white;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.phonecheck.ui.controller.print.labels.RepairLabel3x2Controller">

    <stylesheets>
        <URL value="@../../style/repair-label.css" />
    </stylesheets>

    <!-- Header -->
    <HBox spacing="10.0">
        <ImageView fx:id="logoImageView" fitHeight="50.0" fitWidth="120.0" nodeOrientation="INHERIT" pickOnBounds="true" preserveRatio="true">
            <Image url="@../../image/phonecheck-logo.png" />
        </ImageView>
        <VBox spacing="10.0">
            <Text fx:id="dateText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="08/10/1024">
                <font>
                    <Font size="14.0" />
                </font>
            </Text>
            <HBox fx:id="batteryHealthHBox" spacing="12.0">
                <Text strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="BH">
                    <font>
                        <Font size="14.0" />
                    </font>
                </Text>
                <Text fx:id="batteryHealthText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="00%">
                    <font>
                        <Font size="14.0" />
                    </font>
                </Text>
            </HBox>
        </VBox>
        <VBox fx:id="imeiBarcodeVBox" alignment="CENTER">
            <ImageView fx:id="imeiBarcodeImageView" fitHeight="44.0" fitWidth="258.0" pickOnBounds="true" smooth="false">
                <VBox.margin>
                    <Insets />
                </VBox.margin>
                <Image url="@../../image/barcode.png" />
            </ImageView>
            <Text fx:id="imeiText" strokeType="OUTSIDE" strokeWidth="0.0" text="3456135040984567" HBox.hgrow="ALWAYS" />
        </VBox>
    </HBox>

    <!-- Body -->
    <VBox spacing="4" VBox.vgrow="ALWAYS">
        <HBox alignment="CENTER" styleClass="hbox-border">
            <Text fx:id="titleText" styleClass="text-bold" text="(NOKIA G21, NOKIA G21 Duos, 64 GB, Hmdrvo3)">
                <font>
                    <Font size="16.0" />
                </font>
            </Text>
         <VBox.margin>
            <Insets right="12.0" />
         </VBox.margin>
        </HBox>
        <FlowPane fx:id="testResultsPane" hgap="6.0" vgap="6.0">
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>

            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="Acc" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="VM" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="DG" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="15.0" fitWidth="15.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/black-close-icon-100.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="GCrack" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="12.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="MultiT" />
            </HBox>
            <HBox alignment="CENTER_LEFT" prefWidth="70" spacing="4.0">
                <ImageView fitHeight="14.0" fitWidth="12.0" pickOnBounds="true" preserveRatio="true" smooth="false">
                    <Image url="@../../image/fully-functional-tick.png" />
                </ImageView>
                <Text styleClass="text-regular-italic" text="BT" />
            </HBox>
        </FlowPane>
    </VBox>

    <!-- Footer -->
    <HBox alignment="CENTER" spacing="4.0">
        <HBox fx:id="portNoHBox" alignment="CENTER" spacing="4.0">
            <ImageView fitHeight="20.0" fitWidth="20.0" preserveRatio="true">
                <Image url="@../../image/usb-port-black.png" />
            </ImageView>
            <Text fx:id="portNoText" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-bold" text="1">
                <font>
                    <Font size="14.0" />
                </font>
            </Text>
        </HBox>
        <HBox fx:id="notesHBox" HBox.hgrow="ALWAYS">
            <HBox styleClass="hbox-border">
                <Text text="Note" />
                <HBox.margin>
                    <Insets />
                </HBox.margin>
            </HBox>
            <HBox styleClass="hbox-border" HBox.hgrow="ALWAYS">
                <Text fx:id="notesText" text="This is a note." />
                <HBox.margin>
                    <Insets left="-1.0" />
                </HBox.margin>
            </HBox>
            <HBox.margin>
                <Insets left="12.0" />
            </HBox.margin>
        </HBox>
        <Text fx:id="functionalityText" text="Pending(Not Tested)" />
        <VBox.margin>
            <Insets left="12.0" right="36.0" />
        </VBox.margin>
    </HBox>

    <padding>
        <Insets bottom="20.0" left="12.0" right="24.0" top="20.0" />
    </padding>

</VBox>
