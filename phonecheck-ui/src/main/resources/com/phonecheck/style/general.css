* {
    accent-color: #06A9F6
}

.primary-text-color {
    -fx-color: #06A9F6;
    -fx-fill: #06A9F6;
}

.text-color-white-ui {
    -fx-fill: #FFFFFF;
}

.text-color-black-ui {
    -fx-fill: #000000;
}

.text-color-red-ui {
    -fx-fill: red;
}

.primary-color-bg {
    -fx-background-color: #06A9F6;
}

.primaryDark-color-bg {
    -fx-background-color: #0488D2;
}

.white-background {
    -fx-background: white;
}

.border-color {
    -fx-border-color: #06A9F6;
}

.white-text-color {
    -fx-color: white;
}

.black-text-fill-color {
    -fx-text-fill: black;
}

.white-text-fill-color {
    -fx-text-fill: white;
}

.primaryDark-btn {
    -fx-background-color: #0D47A1;

}

.deviceinfo-indicator {
    -fx-padding: 6px;
    -fx-background-color: #06A9F6;
    -fx-background-radius: 2px;
}

.divider {
    -fx-background-color: #0D47A1;
}

.divider-gray {
    -fx-background-color: gray;
}

.white-text-fill-color {
    -fx-text-fill: white;
}

.subhead {
    -fx-font-size: 14px;
    -fx-font-family: "Avenir";
    -fx-font-style: normal;
    -fx-font-weight: bold;
    -fx-font-smoothing-type: lcd;
}

.unplugged-text-fill-color {
    -fx-text-fill: #EF4A3E;
}

.circular-bg:hover {
    -fx-background-radius: 70;
    -fx-background-color: #BBDEFB;
}

.text-color-white {
    -fx-text-fill: white;
}

.button_bg:hover {
    -fx-background-color: #1565C0;
}

.toggle_bg {
    -fx-background-color: rgb(51, 103, 214);
}

.tab-button-bg .tab-button-text {
    -fx-text-fill: #000;
}

.tab-button-bg:hover {
    -fx-background-color: #1565C0;
    -fx-text-fill: #fff;
}

.tab-button-bg:hover .tab-button-text {
    -fx-text-fill: #fff;
}

.menu-item {
    -fx-padding: 0;
    -fx-background-color: transparent;

}

.menu-item:hover {
    -fx-background-color: #1565C0;
}

.header_bg_installed {
    -fx-background-color: rgb(0, 200, 83);
    -fx-background-radius: 3 3 0 0;
}

.header_bg_non_adb {
    -fx-background-color: rgb(255, 170, 7);
    -fx-background-radius: 3 3 0 0;
}

.header_bg_failed {
    -fx-background-color: red;
    -fx-background-radius: 3 3 0 0;
}

.header_bg_normal {
    -fx-background-color: #2196F3;
    -fx-background-radius: 3 3 0 0;
}

.header_bg_activation_failed {
    -fx-background-color: #263238;
    -fx-background-radius: 3 3 0 0;
}

.header_bg_not_connected {
    -fx-background-color: #757575;
    -fx-background-radius: 3 3 0 0;
}

.devicebox_progress_bg_normal {
    -fx-background-color: rgb(51, 51, 51);
}

.devicebox_progress_bg_disconnected {
    -fx-background-color: rgb(98, 98, 98);
}

.devicebox_progress_bg_red {
    -fx-background-color: rgb(244, 76, 75);
}

.radius-3 {
    -fx-background-radius: 3;
}

.devicebox_progress_bg_green {
    -fx-background-color: rgb(0, 200, 83);
    -fx-background-radius: 3 3 3 3;
}

.devicebox_progress_bg_auth {
    -fx-background-color: rgb(255, 170, 7);
    -fx-background-radius: 3 3 3 3;
}

.devicebox_battery_bg {
    -fx-background-color: rgb(250, 250, 250);
    -fx-background-radius: 3 3 3 3;
}

.battery_0 {
    -fx-image: url("../images/battery_0.png");
}

.battery_20 {
    -fx-image: url("../images/battery_20.png");
}

.battery_50 {
    -fx-image: url("../images/battery_50.png");
}

.battery_70 {
    -fx-image: url("../images/battery_70.png");
}

.battery_80 {
    -fx-image: url("../images/battery_80.png");
}

.battery_90 {
    -fx-image: url("../images/battery_90.png");
}

.battery_100 {
    -fx-image: url("../images/battery_100.png");
}

.battery_charging {
    -fx-image: url("../images/charging.png");
}

.add-icon {
    -fx-image: url("../images/Plus_24px.png");
}

.edit2-icon {
    -fx-image: url("../images/Edit.png");
}

.save-white-icon {
    -fx-image: url("../images/Save-24.png");
}

.load-white-icon {
    -fx-image: url("../images/AppleAppStoreRound.png");
}

.licenseinfo-white-icon {
    -fx-image: url("../images/License.png");
}

/*Status images */
.status_ok {
    -fx-image: url("../images/statusOK.png");
}

.status_failed {
    -fx-image: url("../images/statusFailed.png");
}

.status_warning {
    -fx-image: url("../images/statusWarning.png");
}

.login_Button {
    -fx-image: url("../images/Right-48.png");
}

.device-icon {
    -fx-image: url("../images/device-icon.png");
}

.apple-logo {
    -fx-image: url("../images/Apple-48.png");
}

.android-logo {
    -fx-image: url("../images/android-logo.png");
}

.notification-icon {
    -fx-image: url("../images/notificationbell.png");
}

#imageView {


}

.result_btn_failed {
    -fx-background-color: pink;
    -fx-border-color: red;
    -fx-border-width: 1.5;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.result_btn_pass {
    -fx-background-color: lightgreen;
    -fx-border-color: green;
    -fx-border-width: 1.5;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.textfield-normal {
    -fx-background-color: white;
    -fx-border-color: #BDBDBD;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 12px "System";
}

.print-button {
    -fx-background-color: blue;
    -fx-border-color: blue;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-text-fill: white;
}

.textfield-normal-27px {
    -fx-background-color: white;
    -fx-border-color: #BDBDBD;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 11px "System";
    -fx-min-height: 27px;
}

.pane-normal {
    -fx-background-color: white;
    -fx-border-color: #BDBDBD;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
}

.pane-green {
    -fx-background-color: lightgreen;
    -fx-border-color: green;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-fill: white;
}

.combo-box {
    -fx-border-color: white;
    -fx-background-color: white;
    -fx-background: white;
    -fx-border-radius: 0 0 0 0;
    -fx-padding: 0 0 0 0;
    -fx-border-width: 0 0 0 0;
    -fx-text-background:white;
}


.combo-box.hide-arrow .arrow {
    -fx-background-color: white
}

.textfield-yellow {
    -fx-background-color: yellow;
    -fx-border-color: #F57F17;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 11px "System";
}


.textfield-grey {
    -fx-background-color: darkgrey;
    -fx-border-color: #A9A9A9;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 11px "System";
    -fx-prompt-text-fill: #6e6e6e;
    -fx-text-fill: #6e6e6e;
    -fx-opacity: 0.8;
}

.network-disable-cb {
    -fx-background-color: darkgrey;
    -fx-border-color: #A9A9A9;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 11px "System";
    /*-fx-text-fill: #212121;*/
    -fx-opacity: 1;
}

.network-disable-cb .arrow {
    -fx-background-color: darkgrey;
}

.network-disable-cb .arrow-button {
    -fx-background-color: lightgrey;
}

.network-enable-cb {
    -fx-background-color: white;
    -fx-border-color: #BDBDBD;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 11px "System";
}


.textfield-blue {
    -fx-background-color: lightblue;
    -fx-border-color: blue;
    -fx-border-width: 1;
    -fx-border-radius: 2;
    -fx-background-radius: 2;
    -fx-font: 11px "System";
}

.blue-border {
    -fx-padding: 8px;
    -fx-border-color: accent-color;
    -fx-border-radius: 3;
}

.orange-border {
    -fx-padding: 8px;
    -fx-border-color: #ff7518;
    -fx-border-radius: 3;
}

.material-button-20 {
    -fx-pref-height: 20px;
}

.material-button, .material-button-danger, .material-button-warning, .material-button-success, .material-button-blue {
    -fx-background-color: accent-color;
    -fx-border-width: 1;
    -fx-border-radius: 2px;
    -fx-font-size: 14px;
    -fx-text-fill: #fff;
}

.material-button-danger {
    -fx-background-color: #FF1744;
}

.material-button-warning {
    -fx-text-fill: #000;
    -fx-background-color: #FFC107;
}

.material-button-success {
    -fx-background-color: #00C853;
}

.material-button-blue {
    -fx-background-color: #1565C0;
}

.material-button-colorless, .material-button-warning-colorless, .material-button-colorless, .material-button-danger-colorless {
    -fx-background-color: #fff;
    -fx-border-width: 1;
    -fx-border-radius: 2px;
}

.material-button-warning-colorless {
    -fx-border-color: #ff5a19;
    -fx-text-fill: #ff5a19;
}

.material-button-colorless {
    -fx-border-color: accent-color;
    -fx-text-fill: accent-color;
}

.material-button-danger-colorless {
    -fx-border-color: #FF1744;
    -fx-text-fill: #FF1744;
}

.bg-green {
    -fx-background-color: green;
}

.bg-red {
    -fx-background-color: red;
}

.fab {
    -fx-background-color: #FF4081;
    -fx-background-radius: 25;
    -fx-opacity: 1;
}

.fab:hover {
    -fx-opacity: 0.95;
}

.button-with-underline {
    -fx-background-color: transparent;
    -fx-underline: true;
}

.textfield-no-border {
    -fx-background-color: transparent;
    -fx-background-insets: 0, 0, 1, 2;
    -fx-background-radius: 0 0 0 0, 0 0 0 0, 0 0 0 0, 0 0 0 0;
    -fx-padding: 0.333333em 0.666667em 0.333333em 0.666667em;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-border-insets: 0;
    -fx-border-radius: 0;
}

.round-text-field {
    -fx-background-radius: 15;
    -fx-border-radius: 15;
}
