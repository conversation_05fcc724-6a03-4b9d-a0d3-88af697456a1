.root_border {
    -fx-background-color: white;
    -fx-border-color: gray;
    -fx-border-width: 1;
    -fx-border-radius: 5;
    -fx-background-radius: 5;
}

.expand_border {
    -fx-background-color: white;
    -fx-border-radius: 5 0 0 5;
    -fx-background-radius: 5 0 0 5;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 0, 0, 0.8), 10, 0, 0, 0);
}

.expand_border_ques {
    -fx-background-color: white;
    -fx-border-radius: 5 5 5 5;
    -fx-background-radius: 5 5 5 5;
}

.ques_left_shadow {
    -fx-background-color: white;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 0, 0, 0.8), 10, 0, 0, 0);
}

.pb-bg {
    -fx-image: url("../image/progress_bar_bg.png");
}

.round-pb-bg {
    -fx-image: url("../image/oval.png");
}

.progress-bar .bar {
    -fx-background-radius: 10;
    -fx-background-color: #06C22F;


}

.progress-bar .track {
    -fx-background-radius: 10;
    -fx-background-color: transparent;
}

.progress-bar {
    -fx-background-radius: 10;
}

.completed_image {
    -fx-image: url("../image/dh_right_n.png");
}

.toolbar_bg {
    -fx-background-color: #1565C0;
}

.toolbar_close_img {
    -fx-image: url("../image/delete_24px.png");
}

.emptyView_bg {
    -fx-background-color: #E6EBEB;
    -fx-border-radius: 5 0 0 5;
    -fx-background-radius: 5 0 0 5;
}

.options-btn:hover {
    -fx-background-color: #007FFF;
    -fx-background-radius: 30.0;
    -fx-border-radius: 30.0;
    -fx-text-fill: white;
}

.options-btn:pressed {
    -fx-opacity: 0.6;
}

.btn-unselected {
    -fx-border-color: #cee0ed;
    -fx-border-radius: 30;
    -fx-text-fill: #000000;
}

.back_image {
    -fx-image: url("../image/back-arrow-black.png");
}

.back_image:pressed {
    -fx-opacity: 0.6;
}

.options-btn {
    -fx-font-family: Arial;
    -fx-font-weight: bold;
    -fx-border-color: #cee0ed;
    -fx-border-radius: 30.0;
    -fx-background-radius: 30.0;
    -fx-font-size: 11;
    -fx-background-color: #f7f8fa;
}

.label-font{
    -fx-font-family: Arial;
    -fx-font-size: 12;
    -fx-font-weight: bold;
}
