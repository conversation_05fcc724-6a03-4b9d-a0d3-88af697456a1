.root {
    -fx-background-radius: 10;
    -fx-background-color: transparent;
    -fx-border-radius: 10;
    -fx-border-color: gray;
    -fx-border-width: 0;
    -fx-effect: dropshadow(gaussian, grey, 10, 0, 0, 0);
}
.label-bold-text {
    -fx-font-size: 15px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold
 }
.button-bold-text {
    -fx-font-size: 15px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold;
    -fx-background-color: #E1E2E4;
    -fx-border-color: grey;
    -fx-border-width: 0;
    -fx-border-radius: 5;
 }
 .button-bold-text:pressed {
     -fx-background-color: #C1C2C4;
 }
 .combo-box {
     -fx-border-color: white;
     -fx-background-color: white;
     -fx-background: white;
     -fx-border-radius: 0 0 0 0;
     -fx-padding: 0 0 0 0;
     -fx-border-width: 0 0 0 0;
     -fx-text-background:white;
 }


 .combo-box.hide-arrow .arrow {
     -fx-background-color: white
 }

 .text-field-view {
     -fx-border-width: 0; /* Remove borders if needed */
     -fx-background-color: transparent; /* Remove background if needed */
     -fx-text-fill: black; /* Set text color to black or any other bright color */
 }