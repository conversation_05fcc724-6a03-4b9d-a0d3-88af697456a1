/*
    Styles for generic dialog component
*/

#dialogContent {
    -accent-color: #f4f4f4;
    -accent-text-color: #64798b;
}

#toolbar {
    -fx-border: none;
    -fx-background-color: transparent;
    -fx-background-radius: 10px;
}

#toolbar_top {
    -fx-background-color: transparent;
}

#toolbar_title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

/* Material tables */
.table-view {
    -fx-table-header-border-color: white;
    -fx-table-cell-border-color: -accent-color;
    -fx-border-color: white;
}

.table-column {
    -fx-font-size: 16px;
}

/* Darker gray band for table header */
.table-view .column-header,
.table-view .header.column-header,
.table-view .column-header-background,
.table-view .column-header .filler,
.table-view .column-header-background .filler {
    -fx-background-color: -accent-color;
}

/* Column headers are in a slate blue */
.table-view .header.column-header .label {
    -fx-text-fill: -accent-text-color;
    -fx-font-weight: bold;
    -fx-padding: 15px;
    -fx-alignment: CENTER-LEFT;
}

.table-row-cell {
    -fx-border: none;
}


.cell {
    -fx-cell-size: 60;
}
