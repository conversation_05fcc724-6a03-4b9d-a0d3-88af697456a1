.toolbar {
    -fx-background-color: white;
    -fx-border-color: #E2E1E7;
    -fx-border-width: 0 0 1px 0;
    -fx-effect: dropshadow(gaussian, grey, 6px, 0, 0, 0);
}

.toolbar > * {
    -fx-alignment: center;
    /* Put a little space around each toolbar button */
    -fx-padding: 10px 0 0 0;
    -fx-border-insets: 14px;
    -fx-background-insets: 14px;
}

.toolbar .label {
    /* Add some space between each toolbar button's image and its label */
    -fx-padding: 10px 0 10px 0;
    -fx-border-insets: 10px;
    -fx-background-insets: 10px;
}

.statusBar {
    -fx-background-color: white;
    -fx-effect: dropshadow(gaussian, grey, 6px, 0, 0, 0);
}

.label-bold-text {
    -fx-font-size: 15px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold
 }

 .uph-progress-bar > .bar {
     -fx-padding: 5px;
     -fx-background-radius: 15;
     -fx-background-insets: 0;
     -fx-background-color: #FF950A;
 }

 .uph-progress-bar > .track {
     -fx-background-color: #D4D2DB;
     -fx-background-radius: 15;
     -fx-padding: 5px;
     -fx-background-insets: 0;
 }

 .uph-progress-bar.low > .bar {
     -fx-background-color: #FF950A;
 }

 .uph-progress-bar.medium > .bar {
     -fx-background-color: #3961FB;
 }

 .uph-progress-bar.high > .bar {
     -fx-background-color: #13C784;
 }

 .uph-progress-bar.fire > .bar {
     -fx-background-color: linear-gradient(to right, #E5CF09, #E53E09);
 }

 #blueTriangle,
 #greenTriangle,
 #orangeTriangle {
     visibility: hidden;
 }

 #orangeTriangle.low,
 #blueTriangle.medium,
 #greenTriangle.high {
     visibility: visible;
 }