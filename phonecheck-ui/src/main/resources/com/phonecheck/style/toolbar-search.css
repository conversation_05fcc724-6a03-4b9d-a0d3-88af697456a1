#top_header{
    -fx-background-color: #1565C0;
}

.toolbar {
    -fx-background-color: rgb(51,103,214);
}
#searchField{
    -fx-background-color: transparent;
    -fx-fill: white;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: gainsboro;


}
#search_layout{
    -fx-background-radius: 2px;
    -fx-padding: 0 8px 0 8px;
    -fx-background-color: rgb(40,80,167);
}
#search-container{

}
#toolbar_title {
    -fx-fill: white;
    -fx-font-size: 16px;
}

.close-button{
    -fx-cursor: HAND;
    -fx-background-color: transparent;
    -fx-opacity: 1;
}

.close-button:hover{
    -fx-opacity: 0.5;
}

.close-icon {
    -fx-image: url("../images/Delete_24px.png");
}

.search-icon{
    -fx-image: url("../images/Search.png");
}

