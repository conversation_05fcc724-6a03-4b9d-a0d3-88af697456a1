package com.phonecheck.airpods;

/**
 * Interface for observing AirPod testing and device connection events.
 * This interface defines methods that are triggered when specific events occur,
 * such as the completion of a test, or when a device is connected or disconnected.
 */
public interface AdObserver {
    void onTestingCompleted(String status);

    void onDeviceConnected();

    void onDeviceDisconnected();
}
