package com.phonecheck.airpods.model.test;

import com.google.gson.JsonObject;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * Represents a test for an AirPod with its associated test name and test status.
 * This class provides the structure to hold the details of an AirPod test,
 * and includes methods for JSON conversion, equality checks, and hashing.
 */
@Getter
@Setter
@NoArgsConstructor
public class AirpodsTest {

    private AirpodsTestName testName;
    private AirpodsTestStatus testStatus;

    public AirpodsTest(final AirpodsTestName testName, final AirpodsTestStatus testStatus) {
        this.testName = testName;
        this.testStatus = testStatus;
    }

    public static AirpodsTest convertJsonToAirpodTest(final JsonObject jsonObject) {
        // Extract the test name and status from the JsonObject
        String testNameStr = jsonObject.get("name").getAsString();
        String testStatusStr = jsonObject.get("status").getAsString();
        // Convert the strings to their respective enums
        AirpodsTestName testName = AirpodsTestName.fromValue(testNameStr);
        AirpodsTestStatus testStatus = AirpodsTestStatus.fromValue(testStatusStr);

        // Create and return the AirpodTest object
        return new AirpodsTest(testName, testStatus);
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AirpodsTest that = (AirpodsTest) o;
        return Objects.equals(testName, that.testName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(testName);
    }
}

