package com.phonecheck.airpods.model.test;

/**
 * Enum representing the status of a testing process for an AirPod.
 * It defines the different states that the AirPod's testing process can be in,
 * such as pending diagnostics, fully functional, or diagnostics failure.
 */
public enum AirpodsTestingStatus {
    DIAGNOSTICS_PENDING("Diagnostics Pending"),
    FULLY_FUNCTIONAL("Fully Functional"),
    DIAGNOSTICS_FAILED("Diagnostics Failed");

    private final String name;

    AirpodsTestingStatus(final String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
