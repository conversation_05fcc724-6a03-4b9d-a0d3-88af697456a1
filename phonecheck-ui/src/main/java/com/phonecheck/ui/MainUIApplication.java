package com.phonecheck.ui;

import com.phonecheck.model.config.UrlConfigInitializer;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.stage.Stage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ConfigurableApplicationContext;

import java.awt.*;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Locale;

/**
 * This is the JavaFX application we want to start up with Spring.
 * The MQTT broker is started automatically when the MqttConfiguration class is resolved by Spring.
 */
public final class MainUIApplication extends Application {
    private static final Logger LOGGER = LoggerFactory.getLogger(MainUIApplication.class);
    private ConfigurableApplicationContext applicationContext;

    @Override
    public void init() {
        System.setProperty("spring.application.name", "PhoneCheck3");
        System.setProperty("javafx.macosx.embedded", "true");
        System.setProperty("application.type", "ui");

        Toolkit.getDefaultToolkit();

        SpringApplication application = new SpringApplication(MultiModuleApplication.class);
        if (System.getProperty("os.name").toLowerCase(Locale.ROOT).contains("windows")) {
            application.setAdditionalProfiles("windows");
        }
        application.addInitializers(new UrlConfigInitializer());
        applicationContext = application.run();
    }

    @Override
    public void stop() {
        LOGGER.info("Application shutting down");
        if (StringUtils.containsIgnoreCase(System.getProperties().getProperty("os.name"), "windows")) {
            // Delete the app-lock file to release the lock
            deleteWindowsAppLockFile();
        }
        applicationContext.close();
        Platform.exit();
        System.exit(0);
    }

    @Override
    public void start(final Stage stage) {
        LOGGER.info("Application is starting");
        MultiModuleApplication.hideSplashScreen();
        applicationContext.publishEvent(new StageReadyEvent(stage));
    }

    public static class StageReadyEvent extends ApplicationEvent {
        public StageReadyEvent(final Stage stage) {
            super(stage);
        }

        public Stage getStage() {
            return (Stage) getSource();
        }
    }

    /**
     * Deletes the lock file used to prevent multiple instances of the application
     * The lock file is located in the "LOCALAPPDATA/PhoneCheck3" directory and is named ".app_lock".
     * If the lock file exists, it is deleted to release the lock.
     */
    private void deleteWindowsAppLockFile() {
        Path lockFilePath = Path.of(System.getenv("LOCALAPPDATA"), "PhoneCheck3", ".app_lock");
        try {
            Files.deleteIfExists(lockFilePath);
        } catch (IOException e) {
            LOGGER.error("Error occurred while deleting the lock file.", e);
        }
    }


}
