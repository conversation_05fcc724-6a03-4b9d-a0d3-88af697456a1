package com.phonecheck.ui.listener;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.device.android.*;
import com.phonecheck.ui.ConnectedDevices;
import com.phonecheck.ui.controller.device.AndroidDeviceBoxController;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AndroidDeviceBoxListeners {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDeviceBoxListeners.class);

    private final ConnectedDevices connectedDevices;

    @Async
    @EventListener
    public void onDeviceConnected(final AndroidDeviceBoxConnectedEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDeviceConnected(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onPairingNeeded(final AndroidPairingNeededEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onPairingNeeded(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onPairingSucceeded(final AndroidPairingSuccessEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onPairingSucceeded(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onPairingFailed(final AndroidPairingFailureEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onPairingFailed(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }


    @Async
    @EventListener
    public void onDeviceInfoCollectionSucceeded(final AndroidInfoCollectionSuccessEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDeviceInfoCollectionSucceeded(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onDeviceEsimResultUpdated(final AndroidEsimResultUpdateEvent event) {
        final Device device = event.getDevice();
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDeviceEsimResultUpdated(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onDeviceInfoCollectionFailed(final AndroidInfoCollectionFailureEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDeviceInfoCollectionFailed(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onImeiSuccess(final AndroidImeiAcquisitionSuccessEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onImeiSuccess(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onImeiFailed(final AndroidImeiAcquisitionFailedEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onImeiFailed(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onKnoxInfo(final AndroidKnoxInfoEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onKnoxInfoReceived(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onMdmInfoEvent(final AndroidMdmStatusInfoEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDeviceMdmSuccessEvent(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onFrpInfo(final AndroidDeviceLockInfoEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onFrpInfoReceived(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidReadyStatus(final AndroidReadyStatusEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidReadyStatus(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidPcUtilityAppInstallStatus(final AndroidPcUtilityAppInstallStatusEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidPcUtilityAppInstallStatus(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidAppInstallInitiated(final AndroidAppInstallInitiatedEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidAppInstallInitiated(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidAppInstallSuccess(final AndroidAppInstallSuccessEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidAppInstallSuccess(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidAppInstallFailure(final AndroidAppInstallFailureEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidAppInstallFailure(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onDeviceEraseResponse(final AndroidEraseResponseEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDeviceEraseResponse(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onPushFilesStatusReceived(final AndroidPushFilesStatusEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onPushFilesStatusReceived(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onShowHideManualAppInstallInstructions(final ShowHideManualAppInstallEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onShowHideManualAppInstallInstructions(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidPrepareStatus(final AndroidPrepareStatusEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidPrepareStatus(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onEmergencyDialerRequest(final AndroidEmergencyDialerRequestEvent event) {
        final Device device = event.getDevice();
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onEmergencyDialerRequest(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }
    @Async
    @EventListener
    public void onUpdateUnlockStatusOnUi(final UpdateUnlockStatusOnUiEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onUpdateUnlockStatusOnUi(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidUnlockCodesDownload(final AndroidUnlockCodesPopUpEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Android unlock codes event {} received for device id {}",
                event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidUnlockCodesDownload(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onAndroidRootInfoSuccess(final AndroidDeviceRootInfoSuccessEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Android root info event {} received for device id {}",
                event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidRootInfoSuccess(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    /**
     * Returns the controller for the Android device
     *
     * @param device target device
     * @return android device box controller
     */
    private AndroidDeviceBoxController getAndroidDeviceBoxController(final Device device) {
        return (AndroidDeviceBoxController) connectedDevices.getControllerById(device.getId());
    }

    @Async
    @EventListener
    public void onAndroidDevicePinLockDetection(final AndroidDevicePinLockDetectedEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Android pin-lock detection event {} received for device id {}",
                event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onAndroidPinLockDetection(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }

    @Async
    @EventListener
    public void onDisplayXiaomiEraseActivatePopup(final ShowXiaomiAppAdminActivatePopupEvent event) {
        final Device device = event.getDevice();
        LOGGER.debug("Xiaomi Admin app event {} received for device id {}", event.getClass().getName(), device.getId());
        AndroidDeviceBoxController controller = getAndroidDeviceBoxController(device);
        if (controller != null) {
            controller.onDisplayXiaomiAppAdminActivatePopup(event);
        } else {
            LOGGER.error("Controller for device id: {} not found!", device.getId());
        }
    }
}
