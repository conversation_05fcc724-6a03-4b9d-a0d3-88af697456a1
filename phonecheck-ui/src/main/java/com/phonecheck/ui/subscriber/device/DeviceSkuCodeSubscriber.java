package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceSkuCodeMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DeviceSkuCodeSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceSkuCodeSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "sku-code", "update"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "sku-code", "update")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }
    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final DeviceSkuCodeMessage message = mapper.readValue(payload,
                    DeviceSkuCodeMessage.class);

            setDeviceIdInMDC(message.getId());
            LOGGER.debug("Device SKU code update message: {}", payload);

            final Device device = new Device() {
            };
            device.setId(message.getId());
            device.setSkuCode(message.getSkuCode());

            final DeviceSkuCodeUpdateEvent event = new DeviceSkuCodeUpdateEvent(this, device);
            eventPublisher.publishEvent(event);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload for sku code update {}", getTopics()[0], payload, e);
        }

    }
}
