package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.DeviceUninstallAppResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceUninstallAppResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DeviceUninstallAppResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceUninstallAppResponseSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "uninstall-app", "success"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "uninstall-app", "success")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final DeviceUninstallAppResponseMessage request =
                    mapper.readValue(payload, DeviceUninstallAppResponseMessage.class);
            setDeviceIdInMDC(request.getId());
            LOGGER.debug("Uninstall App: {}", payload);
            Device device = new Device() {
            };
            device.setId(request.getId());
            if (request.getStage().equalsIgnoreCase(DeviceStage.UNINSTALL_APP.getText())) {
                device.setStage(DeviceStage.UNINSTALL_APP);
            } else {
                device.setStage(DeviceStage.APP_NOT_INSTALLED);
            }

            // Notify any interested business logic listeners of this change
            final DeviceUninstallAppResponseEvent newEvent = new DeviceUninstallAppResponseEvent(this, device);
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
