package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.ios.IosPairSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosPairSuccessMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Raises a {@link IosPairSuccessEvent} when driver indicates pairing succeeded
 */
@Component
@AllArgsConstructor
public class IosPairSuccessSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosPairSuccessSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "pair", "success")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final IosPairSuccessMessage request = mapper.readValue(payload, IosPairSuccessMessage.class);

            setDeviceIdInMDC(request.getId());
            LOGGER.info("iPhone pairing succeeded: {}", payload);

            IosDevice device = new IosDevice();
            device.setId(request.getId());
            device.setStage(DeviceStage.PAIRING_SUCCEEDED);
            device.setImei(request.getImei());
            device.setImei2(request.getImei2());
            device.setSerial(request.getSerial());
            device.setRegionInfo(request.getRegionInfo());
            device.setModel(request.getModel());
            device.setModelNo(request.getModelNo());
            device.setRegulatoryModelNumber(request.getRegulatoryModelNumber());
            device.setSimStatus(request.getSimStatus());
            device.setSim1Esim(request.isSim1Esim());
            device.setSim2Esim(request.isSim2Esim());
            device.setMeid(request.getMeid());
            device.setProductType(request.getProductType());
            device.setProductVersion(request.getProductVersion());
            device.setDiskSize(request.getDiskSize());
            device.setEcid(request.getEcid());
            device.setSetupDoneStatus(request.getSetupDoneStatus());
            device.setSdCardPresent(request.isSdCardPresent());
            device.setWifiAddress(request.getWifiAddress());
            device.setOsMajorVersion(request.getOsMajorVersion());
            device.setFirmware(request.getFirmware());
            device.setIsImeiValidate(request.getIsImeiValidate());
            device.setActivationStatus(request.getActivationStatus());
            device.setEsimActive(request.isESimActive());
            device.setGuid(request.getGuid());
            device.setSimSerial(request.getSimSerial());
            device.setGrade(request.getGrade());
            device.setRam(request.getRam());

            // Notify any interested business logic listeners of this change
            final IosPairSuccessEvent newEvent = new IosPairSuccessEvent(this, device,
                    false, true);
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
