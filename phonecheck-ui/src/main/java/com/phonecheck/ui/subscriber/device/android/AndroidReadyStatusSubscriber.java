package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.android.AndroidReadyStatusEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidReadyStatusMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to a ready message coming from the backend
 */
@Component
@AllArgsConstructor
public class AndroidReadyStatusSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidReadyStatusSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "ready")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final AndroidReadyStatusMessage request = mapper.readValue(payload, AndroidReadyStatusMessage.class);
            setDeviceIdInMDC(request.getId());
            LOGGER.info("Android ready payload: {}", payload);
            AndroidDevice device = new AndroidDevice();
            device.setId(request.getId());
            device.setAndroidConnectionMode(request.getConnectionMode());
            if (request.isReady()) {
                if (AndroidConnectionMode.AT.equals(request.getConnectionMode())) {
                    device.setStage(DeviceStage.READY_IN_AT);
                } else {
                    device.setStage(DeviceStage.READY);
                }
            } else {
                device.setStage(DeviceStage.NOT_READY);
            }

            // Notify any interested business logic listeners of this change
            final AndroidReadyStatusEvent newEvent = new AndroidReadyStatusEvent(this, device);
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
