package com.phonecheck.ui.subscriber.device.iwatch;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.iwatch.IWatchInstallProfileInstructionsEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.iwatch.IWatchInstallProfileInstructionsMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when user install profile on iphone instead of iwatch
 */
@Component
@AllArgsConstructor
public class IWatchInstallProfileInstructionsSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchInstallProfileInstructionsSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS,
                "iwatch", "profile-install-instructions", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final IWatchInstallProfileInstructionsMessage request = mapper.readValue(payload,
                    IWatchInstallProfileInstructionsMessage.class);
            setDeviceIdInMDC(request.getId());
            LOGGER.info("iWatch show profile install instructions payload: {}", payload);
            IosDevice iosDevice = new IosDevice();
            iosDevice.setId(request.getId());
            final IWatchInstallProfileInstructionsEvent newEvent = new
                    IWatchInstallProfileInstructionsEvent(this, iosDevice, request.isVisible());
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
