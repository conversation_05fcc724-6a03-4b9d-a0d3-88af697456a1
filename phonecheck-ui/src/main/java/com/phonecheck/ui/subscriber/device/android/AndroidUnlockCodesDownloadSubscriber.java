package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.android.AndroidUnlockCodesPopUpEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidUnlockCodesDownloadMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AndroidUnlockCodesDownloadSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidUnlockCodesDownloadSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;
    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
       return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "unlock", "codes", "status")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            LOGGER.info("Event received to show pop-up on UI for android unlock codes");
            final AndroidUnlockCodesDownloadMessage request =
                    mapper.readValue(payload, AndroidUnlockCodesDownloadMessage.class);

            Device device = new Device() { };
            device.setId(request.getId());
            final AndroidUnlockCodesPopUpEvent popUpEvent = new AndroidUnlockCodesPopUpEvent(this,
                    device, request.isUnlockCodesDownloaded(), request.isWaitTimeOver());
            eventPublisher.publishEvent(popUpEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload for android unlock codes {}", getTopics()[0], payload, e);
        }
    }
}
