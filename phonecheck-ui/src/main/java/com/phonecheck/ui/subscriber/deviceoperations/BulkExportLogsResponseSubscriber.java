package com.phonecheck.ui.subscriber.deviceoperations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.deviceoperations.BulkExportLogsResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.BulkExportLogsResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when new bulk export logs response is raised
 */
@Component
@AllArgsConstructor
public class BulkExportLogsResponseSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(BulkExportLogsResponseSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("export-logs", "response")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Bulk export logs response received: {}", payload);
        // Make sure it is a bulk export logs response message
        try {
            final BulkExportLogsResponseMessage response =
                    mapper.readValue(payload, BulkExportLogsResponseMessage.class);
            final BulkExportLogsResponseEvent bulkExportLogsResponseEvent =
                    new BulkExportLogsResponseEvent(this);
            eventPublisher.publishEvent(bulkExportLogsResponseEvent);
        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}

