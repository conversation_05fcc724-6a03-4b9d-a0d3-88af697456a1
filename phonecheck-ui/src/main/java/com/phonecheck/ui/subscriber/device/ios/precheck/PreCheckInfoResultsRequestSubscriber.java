package com.phonecheck.ui.subscriber.device.ios.precheck;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.ios.precheck.PreCheckInfoTestRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.PreCheckInfoTestRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Raises this  when pre-check auto results succeeded
 */
@Component
@AllArgsConstructor
public class PreCheckInfoResultsRequestSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(PreCheckInfoResultsRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS,
                "pre-check", "info-results", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final PreCheckInfoTestRequestMessage
                    request = mapper.readValue(payload, PreCheckInfoTestRequestMessage.class);

            setDeviceIdInMDC(request.getId());
            LOGGER.info("Pre-Check info test request payload: {}", msg.getPayload());

            final IosDevice device = new IosDevice();
            device.setId(request.getId());
            eventPublisher.publishEvent(new PreCheckInfoTestRequestEvent(this,
                    device, request.getInfoResults()));
        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }

}
