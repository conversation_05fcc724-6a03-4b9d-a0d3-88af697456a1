package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.DeviceDbInfoRetrievedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceDbInfoRetrievedMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to db info/collection/success mqtt topic for observing
 * device db info collection success event in UI App
 */
@Component
@AllArgsConstructor
public class DeviceDbInfoRetrievedSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceDbInfoRetrievedSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "db", "info", "retrieved")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {

            final DeviceDbInfoRetrievedMessage request = mapper.readValue(payload,
                    DeviceDbInfoRetrievedMessage.class);
            setDeviceIdInMDC(request.getId());
            LOGGER.debug("iPhone Info collected: {}", payload);

            IosDevice device = new IosDevice();
            device.setId(request.getId());
            device.setNotes(request.getNotes());
            device.setRooted(request.getRooted());
            device.setBatteryStateHealth(request.getBatteryHealth());
            device.setBatteryPercentage(request.getBatteryPercentage());
            device.setSimTechnology(request.getSimTechnology());
            device.setColor(request.getColor());
            device.setCarrier(request.getCarrier());
            device.setLpn(request.getLpn());
            device.setCustom1(request.getCustom1());
            device.setCountryOfOrigin(request.getCountryOfOrigin());
            device.setSkuCode(request.getSkuCode());
            device.setFccid(request.getFccid());
            device.setImei(request.getImei());
            device.setReleaseType(request.getReleaseType());

            // Notify any interested business logic listeners of this change
            final DeviceDbInfoRetrievedEvent newEvent = new DeviceDbInfoRetrievedEvent(this, device);
            newEvent.setDeviceColorList(request.getDeviceColorList());
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
