package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.DeviceScanLpnRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceScanLpnRequestMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to the scan LPN request from the backend
 */
@Component
@AllArgsConstructor
public class DeviceScanLpnRequestSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceScanCustom1RequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "scan-lpn", "request"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "scan-lpn", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        String payload = msg.getPayload();
        LOGGER.info("Device scan lpn request received: {}", payload);
        try {
            final DeviceScanLpnRequestMessage request =
                    mapper.readValue(payload, DeviceScanLpnRequestMessage.class);
            setDeviceIdInMDC(request.getId());
            Device device = new Device() {
            };
            device.setId(request.getId());

            // Notify any interested business logic listeners of this change
            final DeviceScanLpnRequestEvent newEvent = new DeviceScanLpnRequestEvent(this, device);
            eventPublisher.publishEvent(newEvent);

        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
