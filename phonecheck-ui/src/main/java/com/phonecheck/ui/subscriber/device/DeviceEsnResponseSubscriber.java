package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.DeviceEsnResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.EsnResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * This subscriber subscribes to the EsnResponseMessage coming from the backend
 */
@Component
@AllArgsConstructor
public class DeviceEsnResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceEsnResponseSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "esn", "response"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "esn", "response")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final EsnResponseMessage response = mapper.readValue(payload, EsnResponseMessage.class);

            setDeviceIdInMDC(response.getId());
            LOGGER.info("Device ESN response {}", payload);

            Device device = new Device() {
            };
            device.setId(response.getId());
            device.setEsnStatus(response.getEsnStatus().getKey());

            // Convert MQTT message to an event and raise it
            final DeviceEsnResponseEvent newEvent = new DeviceEsnResponseEvent(this,
                    device, response.getEsnStatus(), response.getEsnFieldColor(), response.getEsnUIResponse());
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
