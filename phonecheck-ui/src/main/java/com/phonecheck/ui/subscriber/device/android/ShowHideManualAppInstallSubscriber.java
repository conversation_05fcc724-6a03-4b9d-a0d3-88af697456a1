package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.android.ShowHideManualAppInstallEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.ShowHideManualAppInstallMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to Manual/App/Instructions mqtt topic for observing
 * show Hide Manual App Install event
 */
@Component
@AllArgsConstructor
public class ShowHideManualAppInstallSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShowHideManualAppInstallSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(
                DeviceFamily.ANDROID, "manual", "app", "instructions")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final ShowHideManualAppInstallMessage request =
                    mapper.readValue(payload, ShowHideManualAppInstallMessage.class);
            setDeviceIdInMDC(request.getId());
            AndroidDevice device = new AndroidDevice();
            device.setId(request.getId());
            LOGGER.info("Android Manual App Install Instructions Message received: {}", payload);

            final ShowHideManualAppInstallEvent newEvent = new ShowHideManualAppInstallEvent(
                    this, device, request.isShowManualAppInstallInstructions());
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }

    }
}