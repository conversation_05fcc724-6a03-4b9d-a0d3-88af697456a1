package com.phonecheck.ui.subscriber;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.PasswordValidationResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.PasswordValidationResponseMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Subscriber for handling password validation responses received via MQTT.
 */
@Component
@AllArgsConstructor
public class PasswordValidationResponseSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(PasswordValidationResponseSubscriber.class);
    private static final String PAYLOAD_FIELD = "payload";

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("password-validation", "response")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        try {
            JsonNode rootNode = mapper.readTree(msg.getPayload());
            if (!rootNode.has(PAYLOAD_FIELD)) {
                LOGGER.error("Missing 'payload' field in MQTT message: {}", msg.getPayload());
                return;
            }
            String payloadString = rootNode.get(PAYLOAD_FIELD).asText();
            PasswordValidationResponseMessage response = mapper.readValue(payloadString,
                    PasswordValidationResponseMessage.class);
            eventPublisher.publishEvent(new PasswordValidationResponseEvent(this, response.isValid()));
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse password validation response: {}", msg.getPayload(), e);
        }
    }
}