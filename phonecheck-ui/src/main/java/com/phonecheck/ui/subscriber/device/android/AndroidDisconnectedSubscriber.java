package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.android.AndroidDisconnectedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidDisconnectedMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@AllArgsConstructor
public class AndroidDisconnectedSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDisconnectedSubscriber.class);
    private static final int CORE_POOL_SIZE = 20;
    private static final int MAX_POOL_SIZE = Integer.MAX_VALUE;
    private static final Long KEEP_ALIVE_TIME = 100L;
    private static final BlockingQueue<Runnable> MESSAGES_QUEUE = new SynchronousQueue<>();
    private static final ThreadPoolExecutor CONNECTION_PROCESSING_THREAD_POOL = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            MESSAGES_QUEUE
    );

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "disconnected")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object

        CONNECTION_PROCESSING_THREAD_POOL.execute(() -> {
            try {
                final AndroidDisconnectedMessage request = mapper.readValue(payload, AndroidDisconnectedMessage.class);

                setDeviceIdInMDC(request.getId());
                LOGGER.info("Android device disconnection payload: {}", msg.getPayload());

                final AndroidDevice device = new AndroidDevice();
                device.setId(request.getId());
                device.setStage(DeviceStage.DISCONNECTED);

                // Notify any interested business logic listeners of this change
                final AndroidDisconnectedEvent newEvent = new AndroidDisconnectedEvent(this, device);
                eventPublisher.publishEvent(newEvent);
            } catch (JsonProcessingException e) {
                // There's nothing we can do at this point to fix the message format
                LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
            }
        });
    }
}
