package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.DeviceMeidInfoEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.DeviceMeidInfoMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to battery/info/collection/success mqtt topic for observing
 * battery info collection success event in UI App
 */
@Component
@AllArgsConstructor
public class DeviceMeidInfoSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceMeidInfoSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS,
                "meid", "info", "collection", "success"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID,
                        "meid", "info", "collection", "success")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final DeviceMeidInfoMessage message = mapper.readValue(payload,
                    DeviceMeidInfoMessage.class);

            setDeviceIdInMDC(message.getId());
            LOGGER.debug("Device meid info success message: {}", payload);

            final Device device = new Device() {
            };
            device.setId(message.getId());
            device.setMeid(message.getMeid());
            device.setMeid2(message.getMeid2());
            device.setMeidDecimal(message.getMeidDecimal());
            device.setMeidDecimal2(message.getMeidDecimal2());
            device.setPesn(message.getPesn());
            device.setPesn2(message.getPesn2());
            device.setStage(DeviceStage.MEID_INFO_SUCCEEDED);

            final DeviceMeidInfoEvent event = new DeviceMeidInfoEvent(this, device);
            eventPublisher.publishEvent(event);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
