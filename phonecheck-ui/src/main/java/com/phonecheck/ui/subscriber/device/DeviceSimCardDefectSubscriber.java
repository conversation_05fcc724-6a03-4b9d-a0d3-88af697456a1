package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.DeviceSimCardDefectEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceSimCardDefectMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DeviceSimCardDefectSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceSimCardDefectSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "sim-card", "defect"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "sim-card", "defect")
        };
    }
    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Sim Card defect payload {}", payload);
        try {
            final DeviceSimCardDefectMessage defectMessage = mapper.readValue(payload, DeviceSimCardDefectMessage
                    .class);
            Device device = new Device() { };
            device.setId(defectMessage.getDeviceId());
            final DeviceSimCardDefectEvent newEvent = new DeviceSimCardDefectEvent(this, device);
            newEvent.setDefectNeedToBeAdded(defectMessage.isDefectToBeAdded());
            eventPublisher.publishEvent(newEvent);
        } catch (Exception e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
