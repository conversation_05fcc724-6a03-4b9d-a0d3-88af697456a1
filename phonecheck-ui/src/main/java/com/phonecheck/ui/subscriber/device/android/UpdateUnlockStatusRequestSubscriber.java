package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.android.UpdateUnlockStatusOnUiEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.UpdateUnlockStatusOnUiRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Raises a {@link UpdateUnlockStatusOnUiEvent} when unlock status needs to be updated on ui
 */
@Component
@AllArgsConstructor
public class UpdateUnlockStatusRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateUnlockStatusRequestSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(
                DeviceFamily.ANDROID, "update-unlock-status", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final UpdateUnlockStatusOnUiRequestMessage request =
                    mapper.readValue(payload, UpdateUnlockStatusOnUiRequestMessage.class);
            setDeviceIdInMDC(request.getId());
            AndroidDevice device = new AndroidDevice();
            device.setId(request.getId());
            LOGGER.info("Update android unlock status Message received: {}", payload);
            eventPublisher.publishEvent(new UpdateUnlockStatusOnUiEvent(this, device,
                    request.getUnlockStatus(), request.getDisplayMessage()));
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
