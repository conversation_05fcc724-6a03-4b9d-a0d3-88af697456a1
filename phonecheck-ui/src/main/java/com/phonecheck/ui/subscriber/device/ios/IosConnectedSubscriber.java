package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.ios.IosConnectedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosMqttConnectionMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Reacts when a new iPhone connection (initial connection) is reported
 */
@Component
@AllArgsConstructor
public class IosConnectedSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosConnectedSubscriber.class);
    private static final int CORE_POOL_SIZE = 20;
    private static final int MAX_POOL_SIZE = Integer.MAX_VALUE;
    private static final Long KEEP_ALIVE_TIME = 100L;
    private static final BlockingQueue<Runnable> MESSAGES_QUEUE = new SynchronousQueue<>();
    private static final ThreadPoolExecutor CONNECTION_PROCESSING_THREAD_POOL = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            MESSAGES_QUEUE
    );

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "connected")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Ios Device connected payload:{}", payload);
        // Perform payload parsing and device connection in background with a thread pool
        // to avoid any drop in IosMqttConnectionMessage message if multiple devices are
        // connected simultaneously
        CONNECTION_PROCESSING_THREAD_POOL.execute(() -> {
            try {
                // Marshal the payload JSON string to a Java object.
                // There are only 4 attributes we can get at this point.
                final IosMqttConnectionMessage request = mapper.readValue(payload,
                        IosMqttConnectionMessage.class);

                setDeviceIdInMDC(request.getId());
                LOGGER.info("New iPhone connected: {}", payload);

                IosDevice device = new IosDevice();
                device.setId(request.getId());
                device.setEcid(request.getEcid());
                device.setProductType(request.getProductType());
                device.setProductVersion(request.getProductVersion());
                device.setDeviceType(request.getDeviceType());
                device.setModel(request.getModel());
                device.setUsbMode(request.isUsbMode());
                device.setStage(request.getDeviceStage());
                device.setSerial(request.getSerial());
                device.setPortNumber(request.getPortNumber());
                device.setRecoveryMode(request.isDfuMode());
                device.setDeviceState(request.getDeviceState());

                // Convert MQTT message to an event and raise it
                final IosConnectedEvent newEvent =
                        new IosConnectedEvent(this, device);
                eventPublisher.publishEvent(newEvent);
            } catch (JsonProcessingException e) {
                // There's nothing we can do at this point to fix the message
                LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
            }
        });
    }
}
