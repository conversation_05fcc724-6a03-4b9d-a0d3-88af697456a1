package com.phonecheck.ui.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.android.AndroidImeiAcquisitionSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidImeiAcquisitionSuccessMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AndroidImeiAcquisitionSuccessSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidImeiAcquisitionSuccessSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID,
                "imei", "acquisition", "success")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final AndroidImeiAcquisitionSuccessMessage request =
                    mapper.readValue(payload, AndroidImeiAcquisitionSuccessMessage.class);

            setDeviceIdInMDC(request.getId());
            LOGGER.info("Android IMEI acquisition success payload: {}", msg.getPayload());

            final AndroidDevice device = new AndroidDevice();
            device.setId(request.getId());
            device.setImei(request.getImei());
            device.setImei2(request.getImei2());
            device.setStage(DeviceStage.IMEI_COLLECTION_SUCCEEDED);

            // Notify any interested business logic listeners of this change
            final AndroidImeiAcquisitionSuccessEvent newEvent =
                    new AndroidImeiAcquisitionSuccessEvent(this, device, true);
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }

}
