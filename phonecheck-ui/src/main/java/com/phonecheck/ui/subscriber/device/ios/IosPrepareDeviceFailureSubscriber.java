package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.ios.IosPrepareDeviceFailureEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosPrepareDeviceFailureMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Raises an {@link IosPrepareDeviceFailureEvent} when driver indicates prepare device failure
 */
@Component
@AllArgsConstructor
public class IosPrepareDeviceFailureSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosPrepareDeviceFailureSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "prepare-device", "failure")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final IosPrepareDeviceFailureMessage request = mapper.readValue(payload,
                    IosPrepareDeviceFailureMessage.class);
            setDeviceIdInMDC(request.getId());
            LOGGER.info("iPhone prepare device failed: {}", payload);
            IosDevice device = new IosDevice();
            device.setId(request.getId());
            if (request.isFailedDueToMdm()) {
                device.setStage(DeviceStage.PREPARE_FAILED_DUE_TO_MDM);
            } else if (request.isPrepareFailedAfterRetries()) {
                device.setStage(DeviceStage.PREPARE_FAILURE_TRY_MANUALLY);
            } else {
                device.setStage(DeviceStage.PREPARE_DEVICE_FAILURE);
            }

            // Notify any interested business logic listeners of this change
            final IosPrepareDeviceFailureEvent newEvent = new IosPrepareDeviceFailureEvent(this, device);
            newEvent.setShowManualEraseMsg(request.isShowManualEraseMsg());
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}

