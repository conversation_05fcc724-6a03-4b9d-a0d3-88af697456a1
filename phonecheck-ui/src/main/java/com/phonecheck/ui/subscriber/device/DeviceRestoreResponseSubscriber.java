package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.ios.IosDeviceRestoreResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosDeviceRestoreResponseMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@AllArgsConstructor
public class DeviceRestoreResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceRestoreResponseSubscriber.class);

    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = 10;
    private static final Long KEEP_ALIVE_TIME = 50L;
    private static final BlockingQueue<Runnable> MESSAGES_QUEUE = new LinkedBlockingQueue<>();
    private static final ThreadPoolExecutor CONNECTION_PROCESSING_THREAD_POOL = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            MESSAGES_QUEUE
    );

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "restore", "response")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Perform payload parsing and device restore in background with a thread pool
        // to avoid any drop in Restore response message if multiple devices are
        // restored simultaneously
        LOGGER.info("Received restore response. payload:{}", payload);
        CONNECTION_PROCESSING_THREAD_POOL.execute(() -> {
            try {
                final IosDeviceRestoreResponseMessage response = mapper.readValue(payload,
                        IosDeviceRestoreResponseMessage.class);

                setDeviceIdInMDC(response.getId());
                LOGGER.trace("Device restore response in subscriber: state::{}, msg::{}",
                        response.getRestoreStatus(), response.getResponseCode());

                Device device = response.getDevice();

                final IosDeviceRestoreResponseEvent newEvent = new IosDeviceRestoreResponseEvent(this,
                        device, response.getRestoreStatus(), response.getResponseCode(),
                        response.getTimeTaken());
                newEvent.setAttempt(response.getAttempt());
                eventPublisher.publishEvent(newEvent);
            } catch (JsonProcessingException e) {
                // There's nothing we can do at this point to fix the message
                LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
            }
        });
    }
}
