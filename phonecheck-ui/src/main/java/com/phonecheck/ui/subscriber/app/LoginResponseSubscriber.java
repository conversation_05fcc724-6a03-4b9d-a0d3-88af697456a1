package com.phonecheck.ui.subscriber.app;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.communicator.CommunicatorService;
import com.phonecheck.model.cloudapi.SkuSchemaResponse;
import com.phonecheck.model.event.login.LoginEvent;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.LoginResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@AllArgsConstructor
public class LoginResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginResponseSubscriber.class);

    private static final String FIRMWARE = "firmware";
    private static final String PROFILE = "profile";
    private static final String SKU_SCHEMA = "sku_schema";

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;
    private final CommunicatorService communicatorService;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("login", "response")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("User login response {}", payload);
        try {
            final LoginResponseMessage loginResponseMessage = mapper.readValue(payload, LoginResponseMessage.class);

            String labelFxmlResponseStr = communicatorService.getData(getTopics()[0]);
            Map<String, LabelFxmlResponse.Data> labelDataMap = null;
            String frimwareResponse = communicatorService.getData(FIRMWARE);
            Map<String, FirmwareModel.FirmwareResponse> firmwareModels = null;
            if (StringUtils.isNotBlank(frimwareResponse)) {
                firmwareModels = mapper.readValue(frimwareResponse,
                        new TypeReference<>() {
                        });
            }

            if (StringUtils.isNotBlank(labelFxmlResponseStr)) {
                labelDataMap = mapper.readValue(labelFxmlResponseStr,
                        new TypeReference<>() {
                        });
            }
            String profilePackage = communicatorService.getData(PROFILE);
            Map<String, SelectPackageResponse.ProfilesConfiguration> profileData = null;
            if (StringUtils.isNotBlank(profilePackage)) {
                profileData = mapper.readValue(profilePackage,
                        new TypeReference<>() {
                        });
            }
            String skuSchema = communicatorService.getData(SKU_SCHEMA);
            SkuSchemaResponse skuSchemaResponse = null;
            if (StringUtils.isNotBlank(skuSchema)) {
                skuSchemaResponse = mapper.readValue(skuSchema, SkuSchemaResponse.class);
            }

            // Convert MQTT message to an event and raise it
            final LoginEvent newEvent = new LoginEvent(
                    this,
                    loginResponseMessage.getLoginMsg(),
                    loginResponseMessage.isEuServer(),
                    loginResponseMessage.getUser(),
                    loginResponseMessage.getIosAppVersion(),
                    loginResponseMessage.getAndroidAppVersion(),
                    loginResponseMessage.getTesterName(),
                    loginResponseMessage.getTransaction(),
                    loginResponseMessage.getMasterName(),
                    loginResponseMessage.getAssignedCloudCustomization(),
                    skuSchemaResponse,
                    loginResponseMessage.getLicenseId(),
                    loginResponseMessage.getMasterToken(),
                    loginResponseMessage.getUserToken(),
                    labelDataMap,
                    profileData,
                    firmwareModels,
                    loginResponseMessage.getFirmwarePath(),
                    loginResponseMessage.getWarehouseName(),
                    loginResponseMessage.getMasterId(),
                    loginResponseMessage.getTesterId(),
                    loginResponseMessage.getDaysForImei(),
                    loginResponseMessage.getCustomClientLogoUrl(),
                    loginResponseMessage.getShopfloorGrades()
            );
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
