package com.phonecheck.ui.subscriber;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.FirmwareStatusResponseEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.FirmwareStatusMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Subscriber class for firmware status verification response
 */
@Component
@AllArgsConstructor
public class FirmwareStatusResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirmwareStatusResponseSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("firmware-status", "response")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final FirmwareStatusMessage request = mapper.readValue(payload, FirmwareStatusMessage.class);
            Map<String, FirmwareDownloadStatus> firmwareDownloadStatusMap = request.getFirmwareDownloadStatusMap();
            FirmwareStatusResponseEvent event = new FirmwareStatusResponseEvent(this, firmwareDownloadStatusMap);
            LOGGER.info("All values of firmwares are updated now");
            eventPublisher.publishEvent(event);
        } catch (Exception e) {
            LOGGER.error("Error while reading updated firmware status", e);
        }
    }
}
