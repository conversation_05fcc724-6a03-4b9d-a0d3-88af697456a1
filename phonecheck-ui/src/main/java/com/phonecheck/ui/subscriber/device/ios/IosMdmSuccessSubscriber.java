package com.phonecheck.ui.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.ios.IosMdmStatusInfoEvent;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosMdmStatusInfoMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class IosMdmSuccessSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosMdmSuccessSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "mdm-info", "received")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final IosMdmStatusInfoMessage request = mapper.readValue(payload,
                    IosMdmStatusInfoMessage.class);

            setDeviceIdInMDC(request.getId());
            LOGGER.info("Iphone MDM payload: {}", msg.getPayload());

            final IosDevice device = new IosDevice();
            device.setId(request.getId());
            device.setStage(DeviceStage.MDM_SUCCEEDED);
            device.setMdmInfo(request.getMdmInfo());

            // Notify any interested business logic listeners of this change
            final IosMdmStatusInfoEvent newEvent = new IosMdmStatusInfoEvent(this, device,
                    request.getMdmInfo() != null ? request.getMdmInfo().getMdmStatus() : MdmStatus.NA,
                    request.getMessageForUI());
            eventPublisher.publishEvent(newEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
