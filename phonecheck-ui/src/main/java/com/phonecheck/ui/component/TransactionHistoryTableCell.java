package com.phonecheck.ui.component;

import com.phonecheck.model.transaction.Transaction;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.TableCell;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.AnchorPane;
import javafx.scene.text.TextAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

public class TransactionHistoryTableCell extends TableCell<Transaction, Object> {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionHistoryTableCell.class);

    private static final String TRANSACTION_ID = "Transaction Id";
    private Label label;
    private AnchorPane anchorPane;
    private ImageView imageView;

    private final Transaction currentTransaction;

    public TransactionHistoryTableCell(final Transaction currentTransaction) {
        this.currentTransaction = currentTransaction;

        setupCellComponents();
    }

    private void setupCellComponents() {
        label = new Label();
        label.setTextAlignment(TextAlignment.CENTER);
        label.setAlignment(Pos.CENTER);
        AnchorPane.setLeftAnchor(label, 0.0);
        AnchorPane.setRightAnchor(label, 0.0);

        anchorPane = new AnchorPane();
        anchorPane.getChildren().add(label);

        imageView = new ImageView();
        imageView.setFitHeight(17);
        imageView.setFitWidth(17);
        AnchorPane.setLeftAnchor(imageView, 0.0);
    }

    @Override
    protected void updateItem(final Object item, final boolean empty) {
        super.updateItem(item, empty);

        if (item == null || empty) {
            setText(null);
            setGraphic(null);
        } else {
            String value = item instanceof String ? (String) item : String.valueOf(item);
            label.setText(value);

            if (getTableColumn().getText().equals(TRANSACTION_ID)
                    && value.equals(String.valueOf(currentTransaction.getTransactionId()))) {
                try {
                    imageView.setFitHeight(17);
                    imageView.setFitWidth(17);
                    AnchorPane.setLeftAnchor(imageView, 0.0);

                    String url = Objects.requireNonNull(getClass().getClassLoader()
                                    .getResource("com/phonecheck/image/icon/star-icon.png"))
                            .toExternalForm();

                    Image image = new Image(url);
                    imageView.setImage(image);

                    if (!anchorPane.getChildren().contains(imageView)) {
                        anchorPane.getChildren().add(imageView);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error occurred while showing star icon with the current transaction cell");
                }
            } else {
                anchorPane.getChildren().remove(imageView);
            }

            setGraphic(anchorPane);
            setText(null);
        }
    }
}
