package com.phonecheck.ui.controller.device;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.model.cloudapi.CarrierSimLockStatusResponse;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.GradingQuestionBackTraceResponse;
import com.phonecheck.model.constants.CustomizationConstants;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.constants.EsnFieldColor;
import com.phonecheck.model.constants.TestResultConstants;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.device.*;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.ui.DeviceBoxSettings;
import com.phonecheck.model.event.device.*;
import com.phonecheck.model.event.device.ios.DeviceCarrierCheckResponseEvent;
import com.phonecheck.model.event.device.ios.IosDeviceRestoreResponseEvent;
import com.phonecheck.model.event.grading.LoadGradingQuestionsRequestEvent;
import com.phonecheck.model.event.grading.SetGradingSystemGradeEvent;
import com.phonecheck.model.event.print.AutoPrintLabelEvent;
import com.phonecheck.model.grading.GradingAnswers;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.*;
import com.phonecheck.model.mqtt.messages.android.ProceedForXiaomiEraseMessage;
import com.phonecheck.model.mqtt.messages.ios.IosDeviceRestoreRequestMessage;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.status.*;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.test.*;
import com.phonecheck.model.util.DeviceCosmeticResultsUtil;
import com.phonecheck.model.util.FunctionalityStatusUtil;
import com.phonecheck.model.util.TestResultsUtil;
import com.phonecheck.model.util.TimerUtil;
import com.phonecheck.ui.controller.AbstractUiController;
import com.phonecheck.ui.controller.MainController;
import com.phonecheck.ui.controller.helper.DeviceOperationHelper;
import com.phonecheck.ui.service.CaptureCameraImageService;
import com.phonecheck.ui.service.SaveLabelImageService;
import javafx.animation.PauseTransition;
import javafx.animation.Timeline;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.Clipboard;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.Text;
import javafx.scene.text.TextAlignment;
import javafx.util.Duration;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.controlsfx.control.PopOver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.phonecheck.model.constants.TestResultConstants.MANUAL_PANIC_FULL_TEST;
import static com.phonecheck.model.constants.TestResultConstants.PANIC_FULL_TEST;
import static com.phonecheck.model.twowayapi.TwoWayApiConstants.*;

@Getter
@Setter
public abstract class DeviceBoxController extends AbstractUiController {
    public static final String STATUS_MESSAGE_KEY = "statusMessage";
    protected static final Logger LOGGER = LoggerFactory.getLogger(DeviceBoxController.class);
    protected static final int ERASE_TIME_OUT = 7; // Minutes
    public static final String BATTERY_DRAIN = "Battery Drain";
    protected static final int RESTORE_TIME_OUT = 90; // Minutes
    protected static final int APP_ADMIN_POP_UP_TIME_OUT = 1; // Minutes
    protected static final String FAILED = "Failed";
    protected static final String PASSED = "Passed";
    protected static final String PENDING = "Pending";
    protected static final String ERASE_REQUEST_SENT = "eraseRequestSent";
    protected static final String COMPLETE_REQUIRED_FIELDS = "completeRequiredFields";
    protected static final String DISCONNECTED_DURING_ERASE = "deviceDisconnectedDuringErase";
    protected static final String DISCONNECTED_DURING_RESTORE = "deviceDisconnectedDuringRestore";
    protected static final String ERASE_RESTRICTED = "eraseRestricted";
    protected static final String PRINT_RESTRICTED = "printRestricted";
    protected static final String LOCK = "LK";
    protected static final String UNLOCK = "UNLK";
    private static final String DATA_VERIFICATION = "Data Verification";
    private static final String BATTERY_DRAIN_INFO_COLLECTED = "batteryDrainInfoCollected";
    protected static final Map<DeviceStage, String> STAGE_TO_LABEL_MAPPING = new HashMap<>();
    protected static final Map<DeviceStage, String> STAGE_TO_SUB_STEP_MAPPING = new HashMap<>();
    private static final List<String> FAIL_DEVICE_REASONS = List.of("M-WiFi",
            "M-Digitizer",
            "M-Home Button",
            "M-Charge Port",
            "M-Unable to Activate",
            "M-Intermittent Issues",
            "M-LCD",
            "M-Liquid Damage",
            "M-Software");
    private static final int PAUSE_TRANSITION_TIME = 1; // Seconds
    private static final String ESIM_DETECTED = "eSimDetected";

    static {
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.INITIAL_CONNECTION, "pairingInProgress");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PUSH_WIFI_PROFILE, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PAIRING, "pairingInProgress");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PAIRING_SUCCEEDED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PAIRING_FAILED, "pairingFailedReconnect");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PREPARE_DEVICE_IN_PROGRESS, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.ACTIVATING, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.ACTIVATION_SUCCEEDED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.ACTIVATION_FAILED, "activationFailed");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.BATTERY_INFO_COLLECTION_SUCCESS, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.BATTERY_INFO_COLLECTION_FAILED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.INFO_COLLECTION_SUCCEEDED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.IMEI_COLLECTION_SUCCEEDED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PREPARE_DEVICE_FAILURE, "preparingFailedReconnect");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PREPARE_FAILURE_TRY_MANUALLY, "prepareFailedTryManually");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.PREPARE_FAILED_DUE_TO_MDM, "prepareFailedDueToMdm");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.INFO_COLLECTION_FAILED, "preparingFailedReconnect");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.IMEI_COLLECTION_FAILED, "imeiNotFound");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.OEM_DATA_COLLECTION_SUCCEEDED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.OEM_DATA_COLLECTION_FAILED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.DEV_IMAGE_MOUNT_SUCCEEDED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.TEST_CONFIG_IN_PROGRESS, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.TEST_CONFIG_SUCCESS, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.APP_INSTALL_SUCCESS, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.APP_ALREADY_INSTALLED, "preparing");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.READY, "readyStartTesting");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.READY_IN_AT, "readyInAtMode");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.READY_IN_PRE_CHECK, "readyInPreCheckMode");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.POWER_OFF, "shuttingDownPhone");
        STAGE_TO_LABEL_MAPPING.put(DeviceStage.NOT_READY, "notReady");
    }

    static {
        STAGE_TO_SUB_STEP_MAPPING.put(DeviceStage.READY, "startDeviceTesting");
        STAGE_TO_SUB_STEP_MAPPING.put(DeviceStage.NOT_READY, "appInstallFailedManualInstallReqd");
        STAGE_TO_SUB_STEP_MAPPING.put(DeviceStage.PAIRING_FAILED, "pairingFailedReconnect");
        STAGE_TO_SUB_STEP_MAPPING.put(DeviceStage.INITIAL_CONNECTION, "pairingInProgress");
    }

    private final List<String> passedTestKeys = new ArrayList<>();
    private final List<String> failedTestKeys = new ArrayList<>();
    private final List<String> passedCosmeticTestKeys = new ArrayList<>();
    private final List<String> failedCosmeticTestKeys = new ArrayList<>();
    // holds the test keys that are common among test keys and failed device reasons
    private final List<String> failDeviceReasonsCopy = new ArrayList<>(Arrays.asList("WiFi",
            "Digitizer",
            "Home Button",
            "Charge Port",
            "Unable to Activate",
            "Intermittent Issues",
            "LCD",
            "Liquid Damage",
            "Software"));
    @FXML
    private AnchorPane parentAnchor;
    @FXML
    private ScrollPane infoViewPane;
    @FXML
    private VBox workFlowInfoVBox;
    @FXML
    private AnchorPane mainContentAnchor;
    @FXML
    private AnchorPane deviceTitleAnchor;
    @FXML
    private Label deviceTitleLabel;
    @FXML
    private Label dualSimField;
    @FXML
    private Label pinLockField;
    @FXML
    private Label imeiLabel;
    @FXML
    private Label infoImeiLabel;
    @FXML
    private Label infoSerialLabel;
    @FXML
    private Label lidarSerialLabel;
    @FXML
    private Label serialLabel;
    @FXML
    private Label regulatoryModelAndModelLabel;
    @FXML
    private Label countryOfRegionLabel;
    @FXML
    private Label infoMakeLabel;
    @FXML
    private ComboBox<String> gradeComboBox;
    @FXML
    private ComboBox<String> carrierComboBox;
    @FXML
    private ComboBox<String> colorComboBox;
    @FXML
    private Label colorLabel;
    @FXML
    private Label osLabel;
    @FXML
    private VBox batteryVBox;
    @FXML
    private ImageView batteryImageView;
    @FXML
    private ImageView batteryWarningImageView;
    @FXML
    private ImageView batteryFullImageView;
    @FXML
    private Label batteryMiniHealthLabel;
    @FXML
    private Label infoBatteryMiniHealthText;
    @FXML
    private VBox deviceLockVBox;
    @FXML
    private ImageView deviceLockDefaultImageView;
    @FXML
    private ImageView deviceLockedImageView;
    @FXML
    private ImageView deviceUnlockedImageView;
    @FXML
    private Label deviceLockLabel;
    @FXML
    private VBox mdmVBox;
    @FXML
    private ImageView mdmImageView;
    @FXML
    private ImageView mdmOnImageView;
    @FXML
    private ImageView mdmOffImageView;
    @FXML
    private ImageView knoxImageView;
    @FXML
    private ImageView knoxOnImageView;
    @FXML
    private ImageView knoxOffImageView;
    @FXML
    private StackPane networkLockStatusHighlight;
    @FXML
    private ImageView networkLockStatusOn;
    @FXML
    private ImageView networkLockStatusOff;
    @FXML
    private ImageView networkLockStatus;
    @FXML
    private ImageView networkServerError;
    @FXML
    private Label mdmLabel;
    @FXML
    private Label manualPeoLabel;
    @FXML
    private Label manualWifiPushLabel;
    @FXML
    private Label syncResultsLabel;
    @FXML
    private Label printLabel;
    @FXML
    private Label autoExportLabel;
    @FXML
    private VBox esnVBox;
    @FXML
    private VBox esnVboxHighlight;
    @FXML
    private ImageView esnImageView;
    @FXML
    private ImageView esnGoodImageView;
    @FXML
    private ImageView esnBadImageView;
    @FXML
    private Label esnLabel;
    @FXML
    private VBox oemVBox;
    @FXML
    private ImageView oemImageView;
    @FXML
    private ImageView oemPassImageView;
    @FXML
    private ImageView oemFailImageView;
    @FXML
    private Label oemLabel;
    @FXML
    private AnchorPane marketPlaceCertifiedVBox;
    @FXML
    private VBox defectsVBox;
    @FXML
    private FlowPane defectsFlowPane;
    @FXML
    private HBox eraserTimerHBox;
    @FXML
    private Label eraseTimerText;
    @FXML
    private Label eraseTimeLabel;
    @FXML
    private Label deviceStatusLabel;
    @FXML
    private ImageView mainTabMenuImageView;
    @FXML
    private ImageView eraseIconImageView;
    @FXML
    private ImageView eraseCompletedImageView;
    @FXML
    private ImageView eraserIcon;
    @FXML
    private VBox cosmeticBox;
    @FXML
    private VBox cosmeticsContainer;
    @FXML
    private VBox failDeviceVBox;
    @FXML
    private VBox failDeviceContainer;
    @FXML
    private AnchorPane reconnectDeviceAnchor;
    @FXML
    private Text reconnectHeaderId;
    @FXML
    private Text reconnectErrorText;
    @FXML
    private HBox mainTabMenuHBox;
    @FXML
    private HBox eraseMenuHBox;
    @FXML
    private HBox powerOffMenuHBox;
    @FXML
    private HBox moreMenuHBox;
    @FXML
    private VBox moreMenuVbox;
    @FXML
    private FlowPane testsFlowPane;
    @FXML
    private Label networkTextField;
    @FXML
    private Label simCarrierField;
    @FXML
    private Label eSimStatusField;
    @FXML
    private Label jailBreakStatus;
    @FXML
    private Label faceIdLabel;
    @FXML
    private Label validateImeiSerial;
    @FXML
    private Label restoreLabel;
    @FXML
    private AnchorPane batteryInfoAnchorPane;
    @FXML
    private AnchorPane emergencyDialerAnchor;
    @FXML
    private VBox restoreErrorVBox;
    @FXML
    private Label restoreErrorMessage;
    @FXML
    private VBox simCardErrorVBox;
    @FXML
    private VBox sdCardErrorVBox;
    @FXML
    private VBox deviceLockErrorVBox;
    @FXML
    private Label deviceLockedErrorMessage;
    @FXML
    private VBox androidCodesDownloadVBox;
    @FXML
    private VBox loaderViewUnlockCodes;
    @FXML
    private VBox preCheckSemiAutoVBox;
    @FXML
    private VBox preCheckInfoTestVBox;
    @FXML
    private Label batteryHealthLabel;
    @FXML
    private Label batteryChargeLabel;
    @FXML
    private Label batteryCycleLabel;
    @FXML
    private Label batteryDesignedCapacityLabel;
    @FXML
    private Label batteryCurrentCapacityLabel;
    @FXML
    private Label batterySerialLabel;
    @FXML
    private Label batteryModelLabel;
    @FXML
    private Label batteryResistanceLabel;
    @FXML
    private Label touchIdLabel;
    @FXML
    private Label failDeviceLabel;
    @FXML
    private Label touchIdStatus;
    @FXML
    private Label faceIdStatus;
    @FXML
    private Label deviceSubStatusLabel;
    @FXML
    private HBox deviceSubStatusHBox;
    @FXML
    private Label manualAppInstallLabel;
    @FXML
    private Label manualPushFilesLabel;
    @FXML
    private VBox manualTouchIdView;
    @FXML
    private VBox manualFaceIdView;
    @FXML
    private VBox manualFaceIdInstructionView;
    @FXML
    private VBox manualFaceIdTimerView;
    @FXML
    private Label timerCountdown;
    @FXML
    private VBox imageGradeFoundView;
    @FXML
    private VBox imageGradeInstructionView;
    @FXML
    private Button openCameraForGradeButton;
    @FXML
    private HBox deviceTitleHbox;
    @FXML
    private Label portNumberLabel;
    @FXML
    private ImageView closeManualFaceIdView;
    @FXML
    private Pane selectionPane;
    @FXML
    private PopOver moreMenuPopOver;
    @FXML
    private TextField lpnTextField;
    @FXML
    private TextField commentsTextField;
    @FXML
    private VBox manualBatteryRetrievalView;
    @FXML
    private TextField custom1TextField;
    @FXML
    private TextField skuCodeTextField;
    @FXML
    private HBox cosmeticTabMenuHBox;
    @FXML
    private Label copyInfoToAllDevicesLabel;
    @FXML
    private VBox scanCustom1View;
    @FXML
    private TextField scanCustom1TextField;
    @FXML
    private Button scanCustom1DoneButton;
    @FXML
    private VBox scanLpnView;
    @FXML
    private TextField scanLpnTextField;
    @FXML
    private Button scanLpnDoneButton;
    @FXML
    private VBox imeiSerialValidationView;
    @FXML
    private TextField validateImeiTextField;
    @FXML
    private VBox batteryDrainInfoVBox;
    @FXML
    private Label batteryDrainPercentageLabel;
    @FXML
    private Label batteryDrainDurationLabel;
    @FXML
    private Label batteryDrainResultLabel;
    @FXML
    private VBox manualAppInstallInstructionsView;
    @FXML
    private VBox deviceReprocessView;
    @FXML
    private VBox gradingSystemVB;
    @FXML
    private VBox gradingVB;
    @FXML
    private Label profileNameLBL;
    @FXML
    private VBox bStarApiErrorVB;
    @FXML
    private Label bStarApiErrorTitleLBL;
    @FXML
    private Label bStarApiErrorInfoLBL;
    @FXML
    private Label infoSimSerialLabel;
    @FXML
    private VBox xiaomiAppAdminActivatePopupView;
    @FXML
    private Label adminPopupTimerLabel;

    /******* PreCheck-Views *******/
    @FXML
    private Label pcTestNameLabel;
    @FXML
    private ImageView pcRingerArrowImage;
    @FXML
    private ImageView pcRingerOffImage;
    @FXML
    private ImageView pcRingerOnImage;
    @FXML
    private ImageView pcVolumeUpImage;
    @FXML
    private ImageView pcVolumeDownImage;
    @FXML
    private ImageView pcHomeImage;
    @FXML
    private ImageView pcPowerImage;
    @FXML
    private ImageView pcTapScreenImage;
    @FXML
    private ImageView pcProximityImage;
    @FXML
    private Label preCheckInfoTestLabel;
    @FXML
    private ImageView preCheckInfoTestImage;
    @FXML
    private Button pcFailSemiTestButton;
    @FXML
    private AnchorPane preCheckLcdPane;
    @FXML
    private AnchorPane preCheckProximityPane;
    @FXML
    private AnchorPane preCheckButtonPane;
    /************* End ****************/

    private boolean isSelected;
    private Device device;
    private Integer portNumber;
    private TimerUtil timerUtil;
    private String carrierSimLockResponse;
    private String simLockResponse;
    private List<String> unpopulatedRequiredFieldsList;
    private Tooltip tooltip;
    private Tooltip subStatusTooltip;
    private ObjectNode bStarApiErrorObject;
    private String bStarErrorApi;
    private PrintOperation printOperation;
    private TimerUtil appAdminPopupTimer;

    @Setter
    @Getter
    private String semiTestName;
    @Setter
    @Getter
    private String infoTestName;
    @Autowired
    private SaveLabelImageService labelSaveService;
    @Autowired
    private CaptureCameraImageService cameraImageService;
    @Autowired
    private DeviceOperationHelper deviceOperationHelper;
    @Autowired
    private UiInMemoryStore uiInMemoryStore;
    private GradingSystemQuestionsController gradingSystemQuestionsController;
    private String gradingSaveState;
    private boolean gradingInProgress;
    @Autowired
    private ObjectMapper mapper;
    private boolean fromReProcess;
    private List
            <GradingAnswers.GradingAnswersData> gradingAnswers;
    private Timeline timeline;
    private boolean onSkipErased = false;

    public void setPortNumber(final Integer portNumber) {
        this.portNumber = portNumber;
        if (device != null) {
            portNumberLabel.setText(String.valueOf(portNumber + 1));
        }
    }

    /**
     * Setup device box controller once device is connected.
     *
     * @param device iphone device
     */
    public void setupDeviceBox(final Device device) {
        this.device = device;

        if (portNumber == null || portNumber == -1) {
            portNumberLabel.setVisible(false);
        } else {
            this.device.setPortNumber(portNumber);
        }
        isSelected = false;
        mainContentAnchor.setVisible(true);
        deviceTitleLabel.setText(device.getModel());
        if (device.getPortNumber() != null && device.getPortNumber() != -1) {
            portNumberLabel.setText(String.valueOf(device.getPortNumber() + 1));
        }
        setDeviceSubStatusLabel(device.getModel());

        if (device instanceof IosDevice && ((IosDevice) device).isRecoveryMode()) {
            deviceTitleLabel.setStyle("-fx-text-fill: black");
            deviceTitleLabel.setText((StringUtils.isBlank(device.getModel()) ?
                    getLocalizationService().getLanguageSpecificText("iosDevice")
                    : device.getModel())
                    .concat(" - ")
                    .concat(getLocalizationService().getLanguageSpecificText("recoveryMode")));
            deviceTitleLabel.setVisible(true);
            serialLabel.setText(device.getSerial());
            setDeviceSubStatusLabel(device.getSerial());
        }

        timerUtil = null;

        setupGradeComboBox();
        copyTextToClipboardFromLabel(imeiLabel);
        copyTextToClipboardFromLabel(serialLabel);
        copyTextToClipboardFromLabel(countryOfRegionLabel);
        setupImeiValidationFieldListener();
        setupDeviceBoxTabsMouseAction();
        if (getUiInMemoryStore().getCurrentLanguage() != null &&
                getUiInMemoryStore().getCurrentLanguage().equals("Japanese")) {
            networkLockStatusHighlight.setMouseTransparent(true);
        }
    }

    /**
     * Sets up event listeners for copying text from the provided Label to the system clipboard
     * and displaying a Tooltip to indicate copy status.
     *
     * @param label The Label from which text is to be copied.
     */
    private void copyTextToClipboardFromLabel(final Label label) {
        // Setup mouse entered event to show tooltip
        label.setOnMouseEntered(event -> {
            tooltip = new Tooltip(StringUtils.equals(Clipboard.getSystemClipboard()
                    .getString(), label.getText()) ? "Copied" : "Copy");
            Tooltip.install(label, tooltip);
        });

        // Clicked on label to copy label text
        label.setOnMouseClicked(event -> {
            ClipboardContent clipboardContent = new ClipboardContent();
            clipboardContent.putString(label.getText());
            Clipboard.getSystemClipboard().setContent(clipboardContent);

            if (!tooltip.isShowing()) {
                tooltip = new Tooltip("Copied");
                tooltip.show(label, event.getScreenX() + 10, event.getScreenY() + 10);
                tooltip.setAutoHide(true);
            }
        });

        // Hide tooltip when mouse exit
        label.setOnMouseExited(event -> {
            if (tooltip != null) {
                tooltip.hide();
            }
        });
    }

    /**
     * Sets change listener based on value change of Lpn text-field
     * This listener will send MQTT message to backend with the new lpn value of the device
     *
     * @param pauseTransition object to wait for a couple of seconds for change in lpn
     * @return ChangeListener
     */
    private ChangeListener<String> setDeviceLpnChangeListener(final PauseTransition pauseTransition) {
        return (observable, oldLpn, newLpn) -> {
            if (!StringUtils.equals(newLpn, oldLpn)) {
                if (null != this.device) {
                    pauseTransition.setOnFinished(event -> {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Lpn changed from {} to {}", oldLpn, newLpn);
                        lpnTextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        this.device.setLpn(newLpn);
                        deviceOperationHelper.notifyBackendToUpdateLpn(this.device);
                    });
                    pauseTransition.playFromStart();
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of sku code text-field
     * This listener will send MQTT message to backend with the new sku value of the device
     *
     * @param pauseTransition object to wait for a couple of seconds for change in sku code
     * @return ChangeListener
     */
    private ChangeListener<String> setSkuCodeChangeListener(final PauseTransition pauseTransition) {
        return (observable, oldSkuCode, newSkuCode) -> {
            if (!StringUtils.equals(newSkuCode, oldSkuCode)) {
                if (null != this.device) {
                    pauseTransition.setOnFinished(event -> {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Sku Code changed from {} to {}", oldSkuCode, newSkuCode);
                        skuCodeTextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        device.setSkuCode(newSkuCode);
                        deviceOperationHelper.notifyBackendToUpdateSkuCode(device);
                    });
                    pauseTransition.playFromStart();
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of comments text-field
     * This listener will send MQTT message to backend with the new comments value of the device
     *
     * @param pauseTransition object to wait for a couple of seconds for change in comments
     * @return ChangeListener
     */
    private ChangeListener<String> setCommentsChangeListener(final PauseTransition pauseTransition) {
        return (observable, oldComment, newComment) -> {
            if (!StringUtils.equals(newComment, oldComment)) {
                if (null != this.device) {
                    pauseTransition.setOnFinished(event -> {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Comment changed from {} to {}", oldComment, newComment);
                        commentsTextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        device.setNotes(newComment);
                        deviceOperationHelper.notifyBackendToUpdateComments(device);
                    });
                    pauseTransition.playFromStart();
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of custom1 text-field
     * This listener will send MQTT message to backend with the new custom1 value of the device
     *
     * @param pauseTransition object to wait for a couple of seconds for change in custom
     * @return ChangeListener
     */
    private ChangeListener<String> setDeviceCustom1ChangeListener(final PauseTransition pauseTransition) {
        return (observable, oldCustom1, newCustom1) -> {
            if (!StringUtils.equals(newCustom1, oldCustom1)) {
                if (null != this.device) {
                    pauseTransition.setOnFinished(event -> {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Custom1 changed from {} to {}", oldCustom1, newCustom1);
                        custom1TextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        device.setCustom1(newCustom1);
                        deviceOperationHelper.notifyBackendToUpdateCustom1(device);
                    });
                    pauseTransition.playFromStart();
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of scanned custom1 text-field
     * UI of Done button for scanning custom1 pop-up is changed
     *
     * @param pauseTransition object to wait for a couple of seconds for change in comments
     * @return ChangeListener
     */
    private ChangeListener<String> setDeviceScannedCustom1ChangeListener(final PauseTransition pauseTransition) {
        return (observable, oldCustom1, newCustom1) -> {
            if (!StringUtils.equals(newCustom1, oldCustom1)) {
                if (null != this.device) {
                    pauseTransition.setOnFinished(event -> {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Custom1 changed from {} to {}", oldCustom1, newCustom1);
                        if (StringUtils.isNotBlank(scanCustom1TextField.getText())) {
                            getCustom1TextField().setText(scanCustom1TextField.getText());
                            scanCustom1DoneButton.setDisable(false);
                            scanCustom1DoneButton.setStyle("-fx-background-color: #3961FB;");
                        } else {
                            scanCustom1DoneButton.setDisable(true);
                            scanCustom1DoneButton.setStyle("-fx-background-color: darkgrey;");
                        }
                    });
                    pauseTransition.playFromStart();
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of scanned LPN text-field
     * UI of Done button for scanning LPN pop-up is changed
     *
     * @param pauseTransition object to wait for a couple of seconds for change in comments
     * @return ChangeListener
     */
    private ChangeListener<String> setDeviceScannedLpnChangeListener(final PauseTransition pauseTransition) {
        return (observable, oldLpn, newLpn) -> {
            if (!StringUtils.equals(newLpn, oldLpn)) {
                if (null != this.device) {
                    pauseTransition.setOnFinished(event -> {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Scan Lpn changed from {} to {}", oldLpn, newLpn);
                        lpnTextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        if (StringUtils.isNotBlank(scanLpnTextField.getText())) {
                            getLpnTextField().setText(scanLpnTextField.getText());
                            scanLpnDoneButton.setDisable(false);
                            scanLpnDoneButton.setStyle("-fx-background-color: #3961FB;");
                        } else {
                            scanLpnDoneButton.setDisable(true);
                            scanLpnDoneButton.setStyle("-fx-background-color: darkgrey;");
                        }
                    });
                    pauseTransition.playFromStart();
                }
            }
        };
    }

    // ==================================================================
    //                Device specific FXML Action Listeners
    //  Both(ios/android) controller will override these as requirements
    // ==================================================================

    @FXML
    public void onShowBatteryInfoClicked() {
    }

    @FXML
    public void onCloseBatteryInfoClicked() {
    }

    @FXML
    public void closeManualBatteryRetrievalPopup() {
    }

    @FXML
    public void onCloseManualFaceIdViewClicked() {
    }

    @FXML
    public void onCloseManualTouchIdViewClicked() {
    }

    @FXML
    public void onShowOemPartDataClicked() {
    }

    @FXML
    public void onManualPeoClicked() {
        manualPeoLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
    }

    @FXML
    public void onManualTouchIdClicked() {
        touchIdLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
    }

    @FXML
    public void onManualFaceIdClicked() {
        faceIdLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
    }

    public void onGradeWithImageReceived() {
        imageGradeFoundView.setVisible(true);
        imageGradeFoundView.setManaged(true);
        imageGradeInstructionView.setVisible(true);
        imageGradeInstructionView.setManaged(true);
    }

    @FXML
    public void openCameraForGradeImageClicked() {
        if (getDevice() != null) {
            setDeviceIdMDC(getDevice().getId());
            final LocalCustomizations.ShopfloorSettings shopfloorSettings =
                    uiInMemoryStore.getCustomizations().getShopfloorSettings();
            if (shopfloorSettings != null) {
                LOGGER.info("Open Camera For Grade Image Clicked. Selected Camera \"{}\" and selected folder: \"{}\"",
                        shopfloorSettings.getSelectedCamera(), shopfloorSettings.getImageGradeFolderPath());
                if (StringUtils.isNotBlank(shopfloorSettings.getSelectedCamera()) &&
                        StringUtils.isNotBlank(shopfloorSettings.getImageGradeFolderPath())) {
                    final String fileName = StringUtils.isNotBlank(getDevice().getImei())
                            ? getDevice().getImei()
                            : StringUtils.isNotBlank(getDevice().getSerial())
                            ? getDevice().getSerial()
                            : "NoImeiSerial";
                    final String imageGradeFilePath = shopfloorSettings.getImageGradeFolderPath()
                            + File.separator + fileName + ".jpg";
                    final String cameraIndex = StringUtils.split(shopfloorSettings.getSelectedCamera(), ":")[0].trim();
                    LOGGER.info("Trying to open camera at index {} and save image at location: {}",
                            cameraIndex, imageGradeFilePath);
                    openCameraForGradeButton.setDisable(true);
                    new Thread(() -> {
                        setDeviceIdMDC(getDevice().getId());
                        cameraImageService.openCameraWidget(cameraIndex, imageGradeFilePath);
                        runOnFxThread(() -> openCameraForGradeButton.setDisable(false));
                    }).start();
                } else {
                    LOGGER.error("No camera for given grade is selected in shopfloor local customization");
                }
            } else {
                LOGGER.error("Shopfloor settings is not set in local customization");
            }
        }
    }

    @FXML
    public void cancelCameraForGradeImageClicked() {
        setDeviceIdMDC(getDevice().getId());
        LOGGER.info("Cancel camera for grade image clicked");
        imageGradeFoundView.setVisible(false);
        imageGradeInstructionView.setVisible(false);
    }

    @FXML
    public void manualTouchIdFailedClicked() {
    }

    @FXML
    public void manualTouchIdPassClicked() {
    }

    @FXML
    public void manualFaceIdFailedClicked() {
    }

    @FXML
    public void manualFaceIdPassClicked() {
    }

    @FXML
    public void onManualWifiPushClicked() {
        manualWifiPushLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
    }

    @FXML
    public void onEmergencyDialerDoneButton() {
    }

    @FXML
    public void cancelEmergencyDialer() {
    }

    @FXML
    public void onPreCheckSemiTestFail() {
    }

    @FXML
    public void onPreCheckInfoTestFail() {
    }

    @FXML
    public void onPreCheckInfoTestPass() {
    }

    // ==================================================================
    //                     Common FXML Action Listeners
    // ==================================================================

    /**
     * Action to perform when skip is clicked on sim-card detection warning box
     */
    @FXML
    protected void onSimCardWarningSkipClick() {
        getSimCardErrorVBox().setVisible(false);
        if (onSkipErased) {
            if (DeviceFamily.ANDROID == device.getDeviceFamily()) {
                deviceOperationHelper.requestAndroidErasePostSkip((AndroidDevice) device, DeviceSkipSource.SIM_CARD);
            } else {
                deviceOperationHelper.requestIosErasePostSkip((IosDevice) device);
            }
            deviceOperationHelper.notifyBackendForSimCardWarningAction(device, true);
            deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("warningSkip"));
        } else {
            LOGGER.info("Sim card skip clicked but not from erase flow, Skipping erase.");
            deviceOperationHelper.notifyBackendForSimCardWarningAction(device, false);
        }
    }

    /**
     * Action to perform when Done is clicked on sim-card detection warning box
     */
    @FXML
    protected void onSimCardWarningDoneClick() {
        getSimCardErrorVBox().setVisible(false);
        deviceOperationHelper.notifyBackendForSimCardWarningAction(device, onSkipErased);
    }

    /**
     * Action to perform when skip is clicked on sd-card detection warning box
     */
    @FXML
    protected void onSdCardWarningSkipClick() {
        getSdCardErrorVBox().setVisible(false);
        if (onSkipErased) {
            deviceOperationHelper.requestAndroidErasePostSkip((AndroidDevice) device, DeviceSkipSource.SD_CARD);
            deviceOperationHelper.notifyBackendForSdCardWarningAction(device, true);
            deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("warningSkip"));
        } else {
            LOGGER.info("SD card skip clicked but not from erase flow, Skipping erase");
            deviceOperationHelper.notifyBackendForSdCardWarningAction(device, false);
        }

    }

    /**
     * Action to perform when Done is clicked on sd-card detection warning box
     */
    @FXML
    protected void onSdCardWarningDoneClick() {
        getSdCardErrorVBox().setVisible(false);
        deviceOperationHelper.notifyBackendForSdCardWarningAction(device, onSkipErased);
    }

    /**
     * Action to perform when Done is clicked on sd-card detection warning box
     */
    @FXML
    protected void onDeviceLockWarningDoneClick() {
        getDeviceLockErrorVBox().setVisible(false);
        deviceStatusLabel
                .setText(getLocalizationService().getLanguageSpecificText(deviceLockedErrorMessage.getText()));
        eraserIcon.setImage(new Image("com/phonecheck/image/icon/eraser-icon.png"));
    }

    /**
     * Action to toggle selection on a device box when clicked on the device title
     */
    @FXML
    public void toggleDeviceBoxSelection() {
        selectDeviceBox(!isSelected);
    }

    /**
     * Open carrier/sim lock info dialog if ios device
     * Manually changes sim lock for android device
     */
    @FXML
    protected void openCarrierSimLockInfo() {
        setDeviceIdMDC(this.device.getId());
        LOGGER.debug("Carrier lock icon clicked");
        if (this.device.getDeviceType().equals(DeviceType.ANDROID)) {
            manualChangeSimLockStatus();
        } else {
            //Show the dialog box onto the main controller
            MainController mainController = getApplicationContext().getBean(MainController.class);
            if (StringUtils.isNotBlank(carrierSimLockResponse)) {
                showInfoDialog(mainController.getStackPane(), getLocalizationService()
                        .getLanguageSpecificText("carrierSimLock"), carrierSimLockResponse);
            } else if (StringUtils.isNotBlank(simLockResponse)) {
                showInfoDialog(mainController.getStackPane(), getLocalizationService()
                        .getLanguageSpecificText("simLockInfo"), simLockResponse);
            }
        }
    }

    /**
     * Manually change sim lock status for android devices
     */
    private void manualChangeSimLockStatus() {
        boolean newSimLock = !networkLockStatusOn.isVisible();
        // Set the updated sim lock status
        this.device.setSimLock(newSimLock);
        Runnable runnable = () -> {
            // Update the UI based on the sim lock status
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Updating simlock manually on the UI");
            networkLockStatusHighlight.setStyle("-fx-border-color: -fx-control-inner-background;");
            networkLockStatusOn.setVisible(newSimLock);
            networkLockStatusOff.setVisible(!newSimLock);
            networkLockStatus.setVisible(false);
            MDC.clear();
        };
        runOnFxThread(runnable);

        // notify backend
        deviceOperationHelper.requestSimLockStatusChange(device, newSimLock);
    }

    /**
     * Displays ESN detail when clicked on ESN VBOX on device box ui
     */
    @FXML
    protected void onShowESNDataClicked() {
        setDeviceIdMDC(this.device.getId());
        String esnResponse = this.device.getEsnRawResponse();
        LOGGER.debug("ESN data view clicked");
        //Show the dialog box onto the main controller
        MainController mainController = getApplicationContext().getBean(MainController.class);
        if (StringUtils.isNotBlank(esnResponse)) {
            showInfoDialog(mainController.getStackPane(), getLocalizationService()
                    .getLanguageSpecificText("esnInfo"), esnResponse);
        } else {
            LOGGER.info("ESN information response was invalid");
        }
    }

    /**
     * Displays main view when clicked on main view tab on device box ui
     * set style of bottom bar selected item
     */
    @FXML
    protected void onMainViewTabClicked() {
        infoViewPane.setVisible(!infoViewPane.isVisible());

        mainTabMenuHBox.setStyle("-fx-background-color: #B7B7B7 ; -fx-background-radius: 20 20 20 20");
    }

    /**
     * set style of main view tab on the bottom bar when mouse exits from it
     */
    @FXML
    protected void onMainViewTabExit() {
        if (infoViewPane.isVisible()) {
            mainTabMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0 0 0 0");
        } else {
            mainTabMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        }
    }

    /**
     * Requests backend for erasing the device when erase button clicked on device box ui
     * set style of bottom bar selected item
     */
    @FXML
    protected void onEraseButtonClicked() {
        MainController mainController = getApplicationContext().getBean(MainController.class);
        boolean confirmed = mainController.showEraseConfirmationPopup();
        if (confirmed) {
            requestBackendForErase();
        } else {
            LOGGER.info("Erase operation canceled by the user.");
        }
    }

    /**
     * Displays info view when clicked on info view tab on device box ui
     * set style of bottom bar selected item
     */
    @FXML
    public void onPowerOffButtonClicked() {
        powerOffMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        mainTabMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0 0 0 0");
        eraseMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0 0 0 0");

        if (!this.device.isEraseInProgress() || !this.device.isRestoreInProgress()) {
            this.device.setStage(DeviceStage.POWER_OFF);
            this.device.setShutdownInProgress(true);
            updateCurrentStageOnUI(this.device);
            LOGGER.info("Sending power off request for device.");
            deviceOperationHelper.requestPowerOff(this.device);
        } else {
            LOGGER.warn("Cannot power off device while erase is in progress.");
        }
    }

    /**
     * Requests to load desktop cosmetics
     */
    @FXML
    protected void onCosmeticButtonClicked() {
        CloudCustomizationResponse assignedCustomization = uiInMemoryStore.getAssignedCloudCustomization();
        if (assignedCustomization != null &&
                assignedCustomization.getCosmeticSettings() != null &&
                assignedCustomization.getCosmeticSettings().isEnableCosmetics() &&
                CloudCustomizationResponse.CosmeticType.MULTI
                        .equals(assignedCustomization.getCosmeticSettings().getCosmeticType()) &&
                assignedCustomization.getCosmeticSettings().getCosmetics() != null &&
                assignedCustomization.getCosmeticSettings().getCosmetics().length > 0) {

            Runnable runnable = () -> {
                setDeviceIdMDC(this.device.getId());
                LOGGER.info("Loading desktop cosmetics on the UI");
                cosmeticTabMenuHBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                cosmeticBox.setVisible(true);

                cosmeticsContainer.getChildren().clear();

                for (String cosmetic : assignedCustomization.getCosmeticSettings().getCosmetics()) {
                    if (StringUtils.isNotBlank(cosmetic)) {
                        CheckBox checkBox = new CheckBox();
                        checkBox.setFocusTraversable(false);
                        checkBox.setTranslateX(-11);
                        checkBox.getStyleClass().add("cosmetics-check-box");
                        checkBox.setText(cosmetic);
                        cosmeticsContainer.getChildren().add(checkBox);
                    }
                }
                if (device.getDeviceTestResult() != null) {
                    updateDesktopCosmeticsValues(device.getDeviceTestResult().getCosmeticResults());
                }

                MDC.clear();
            };
            runOnFxThread(runnable);

        }
    }

    /**
     * Action on clicking save on the cosmetics view
     */
    @FXML
    protected void saveCosmetics() {
        CloudCustomizationResponse assignedCustomization = uiInMemoryStore.getAssignedCloudCustomization();
        if (assignedCustomization != null &&
                assignedCustomization.getCosmeticSettings() != null &&
                assignedCustomization.getCosmeticSettings().isEnableCosmetics() &&
                CloudCustomizationResponse.CosmeticType.MULTI
                        .equals(assignedCustomization.getCosmeticSettings().getCosmeticType())) {
            runOnFxThread(() -> {
                setDeviceIdMDC(this.device.getId());
                LOGGER.info("Saving desktop cosmetics from the device box");
                String failedCosmetics = null;
                String passedCosmetics = null;

                for (Node c : cosmeticsContainer.getChildren()) {
                    CheckBox checkBox = (CheckBox) c;
                    if (checkBox.isSelected()) {
                        if (failedCosmetics == null) {
                            failedCosmetics = checkBox.getText();
                        } else {
                            failedCosmetics = StringUtils.joinWith(",", failedCosmetics, checkBox.getText());
                        }
                    } else {
                        if (passedCosmetics == null) {
                            passedCosmetics = checkBox.getText();
                        } else {
                            passedCosmetics = StringUtils.joinWith(",", passedCosmetics, checkBox.getText());
                        }
                    }
                }

                cosmeticBox.setVisible(false);

                CosmeticsResults cosmeticsResults = new CosmeticsResults();
                cosmeticsResults.setFailed(failedCosmetics);
                cosmeticsResults.setPassed(passedCosmetics);
                if (this.device.getDeviceTestResult() != null) {
                    this.device.getDeviceTestResult().setCosmeticResults(cosmeticsResults);
                } else {
                    this.device.setDeviceTestResult(new DeviceTestResult());
                    this.device.getDeviceTestResult().setCosmeticResults(cosmeticsResults);
                }
                MDC.clear();
            });
            updateAndNotifyBackendForResultsChange(true);
        }
    }

    /**
     * Action on clicking the cancel or cross button on the cosmetics view
     */
    @FXML
    protected void cancelCosmetics() {
        runOnFxThread(() -> cosmeticBox.setVisible(false));
    }

    /**
     * Displays more menu on main view that contains list of device actions
     */
    @FXML
    protected void onMoreMenuClicked() {
        runOnFxThread(() -> {
            // Lazy-load the popover
            if (moreMenuPopOver == null) {
                moreMenuPopOver = new PopOver(moreMenuVbox);
                moreMenuPopOver.setDetachable(false);
                moreMenuPopOver.setArrowLocation(PopOver.ArrowLocation.BOTTOM_CENTER);
                moreMenuPopOver.setAutoHide(true);
                moreMenuPopOver.setCornerRadius(15d);
            }
            // Toggle the popover visibility
            if (moreMenuPopOver.isShowing()) {
                moreMenuPopOver.hide();
            } else {
                // Show the popover
                moreMenuVbox.setVisible(true);
                moreMenuPopOver.show(moreMenuHBox, -8);
            }
        });
    }

    /**
     * Sends request to backend to install app on the target device
     */
    @FXML
    protected void onAppInstallClicked() {
        manualAppInstallLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (null != this.device) {
            runOnFxThread(() -> {
                if (moreMenuPopOver != null && moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
            });
            deviceOperationHelper.requestAppInstall(device);
        }
    }

    /**
     * Sends request to backend to push config files on the target device
     */
    @FXML
    protected void onPushFilesClicked() {
        manualPushFilesLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (null != getDevice()) {
            setDeviceIdMDC(getDevice().getId());
            if (getMoreMenuPopOver().isShowing()) {
                getMoreMenuPopOver().hide();
            }
            getDeviceOperationHelper().requestPushFiles(getDevice());

            List<String> selectorsForConfigPush = List.of("#TEST_CONFIG_FAILED_ID",
                    "#TEST_CONFIG_SUCCESS_ID",
                    "#TestList_ID",
                    "#Config_ID",
                    "#FailureReasons_ID",
                    "#GradeConfig_ID",
                    "#ClientCustomization_ID",
                    "#allSyncableData_ID",
                    "#DesktopResults_ID",
                    "#BatteryApi_ID"
            );
            runOnFxThread(() -> {
                for (String selector : selectorsForConfigPush) {
                    Node node = getWorkFlowInfoVBox().lookup(selector);
                    if (node instanceof Label label) {
                        label.setVisible(false);
                        label.setManaged(false);
                    }
                }
                if (moreMenuPopOver != null && moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
            });
        }

    }

    /**
     * Sends request to backend to start pulling test requests from the target device
     */
    @FXML
    void onPullTestResults() {
        syncResultsLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Manually sync test results clicked on UI");
            runOnFxThread(() -> {
                if (moreMenuPopOver != null && moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
            });
            deviceOperationHelper.requestPullTestResults(device);
        }
    }

    /**
     * Shows validate imei serial view
     */
    @FXML
    public void onValidateImeiSerialClicked() {
        validateImeiSerial.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Validate imei serial clicked from more menu");
            Runnable runnable = () -> {
                setDeviceIdMDC(this.device.getId());
                LOGGER.info("Showing validate imei serial view on the UI");
                if (moreMenuPopOver != null && moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
                getImeiSerialValidationView().setVisible(true);
                getImeiSerialValidationView().setManaged(true);
                MDC.clear();
            };
            runOnFxThread(runnable);
        }
    }

    /**
     * Triggered when the "Recheck Battery Info" button is clicked.
     * Ensures the UI updates correctly by running on the JavaFX thread.
     */
    @FXML
    public void onRecheckBatteryInfoClicked() {
        runOnFxThread(this::handleRecheckBatteryInfo);
    }

    /**
     * Requests print controller to open print dialogue UI to manually print labels for selected devices
     */
    @FXML
    public void onPrintClicked() {
        printLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (null != this.device) {
            runOnFxThread(() -> {
                if (moreMenuPopOver != null && moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
            });
            MainController mainController = getApplicationContext().getBean(MainController.class);
            mainController.setPrintRequestMode(PrintMode.DEVICE);
            setDeviceFieldsForLabelPrint(uiInMemoryStore, device);
            //Show the dialog box onto the main controller
            openPrintDialogue(mainController.getStackPane(), false, device, false);
        }
    }


    @FXML
    public void onAutoExportClicked() {
        autoExportLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (null != this.device) {
            runOnFxThread(() -> {
                if (moreMenuPopOver != null && moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
            });
            deviceOperationHelper.requestAutoExport(getDevice());
        }
    }


    @FXML
    public void onCloseRestoreErrorClicked() {
        runOnFxThread(() -> getRestoreErrorVBox().setVisible(false));
    }

    /**
     * Requests to load fail device view
     */
    @FXML
    protected void onManualFailDeviceClicked() {
        failDeviceLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        Runnable runnable = () -> {
            if (moreMenuPopOver.isShowing()) {
                moreMenuPopOver.hide();
            }
            failDeviceVBox.setVisible(true);
            if (failDeviceContainer.getChildren().isEmpty()) {
                for (String failedDeviceReason : FAIL_DEVICE_REASONS) {
                    CheckBox checkBox = new CheckBox();
                    checkBox.setFocusTraversable(false);
                    checkBox.setTranslateX(-11);
                    checkBox.getStyleClass().add("cosmetics-check-box");
                    checkBox.setId(failedDeviceReason);
                    checkBox.setText(failedDeviceReason.substring(2));
                    failDeviceContainer.getChildren().add(checkBox);
                }
            }
            updateFailedDeviceValues();
        };
        runOnFxThread(runnable);
    }

    /**
     * Action on clicking save on the fail device view
     */
    @FXML
    protected void saveFailDevice() {
        setDeviceIdMDC(this.device.getId());
        LOGGER.info("Saving the fail device reasons");
        for (Node c : failDeviceContainer.getChildren()) {
            CheckBox checkBox = (CheckBox) c;
            String failedReason = "M-" + checkBox.getText();
            if (checkBox.isSelected()) {
                if (!failedTestKeys.contains(failedReason) && !failedTestKeys.contains(checkBox.getText())) {
                    LOGGER.info("{} is Selected.", checkBox.isSelected());
                    failedTestKeys.add(failedReason);
                    /*
                        when this condition is met, else if in updateDefectOnDiagnosticsView will be executed.
                        This is to keep the fail device view and diagnostic view in sync
                     */
                    if (passedTestKeys.remove(failedReason) || passedTestKeys.remove(checkBox.getText())) {
                        updateDefectOnDiagnosticsView(checkBox.getText(), FAILED);
                    }
                    updateDefectOnMainView(checkBox.getText(), FAILED, false);
                }
            } else {
                /*
                 * When we unselect the failed reason which was previously selected, and it's
                 * not a part of test key, we need not save it into passed results and need not
                 * update on the diagnostic view.
                 */
                if (failedTestKeys.remove(failedReason) || failedTestKeys.remove(checkBox.getText())) {
                    LOGGER.info("{} is de-selected", checkBox.getText());
                    if (failDeviceReasonsCopy.contains(checkBox.getText())) {
                        passedTestKeys.add(failedReason);
                        updateDefectOnDiagnosticsView(checkBox.getText(), PASSED);
                    }
                }
                updateDefectOnMainView(checkBox.getText(), PASSED, false);
            }
        }
        failDeviceVBox.setVisible(false);
        updateAndNotifyBackendForResultsChange(true);
    }

    /**
     * Action on clicking the cancel or cross button on the fail device view
     */
    @FXML
    protected void cancelFailDevice() {
        runOnFxThread(() -> failDeviceVBox.setVisible(false));
    }

    /**
     * Action on clicking Label Copy Info To All Devices
     */
    @FXML
    protected void onCopyInfoToAllDevicesClick() {
        if (this.device != null) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Copy device info to all devices");
            MainController mainController = getApplicationContext().getBean(MainController.class);
            mainController.copyInfoToAllDevices(this.device);
        }
    }

    /**
     * Action on clicking the cancel or cross button on the reconnect device view
     */
    @FXML
    protected void cancelReconnectDevice() {
        runOnFxThread(() -> reconnectDeviceAnchor.setVisible(false));
    }

    /**
     * Action on clicking Done button on scan Custom1 Button View
     */
    @FXML
    public void onScanCustom1DoneButtonClicked() {
        if (StringUtils.isNotBlank(getScanCustom1TextField().getText())) {
            runOnFxThread(() -> {
                getScanCustom1View().setVisible(false);
                getScanCustom1View().setManaged(false);
            });
            // This delay to ensure that custom1 field is updated in the backend
            // before connection automation event is raised
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // do nothing
            }
            deviceOperationHelper.notifyBackendToContinueConnectionAutomation(this.device);
        }
    }

    /**
     * Action on clicking Close button on scan Custom1 Button View
     */
    @FXML
    public void onHideScanCustom1View() {
        runOnFxThread(() -> {
            getScanCustom1View().setVisible(false);
            getScanCustom1View().setManaged(false);
        });
    }

    /**
     * Action on clicking Done button on scan LPN Button View
     */
    @FXML
    public void onScanLpnDoneButtonClicked() {
        if (StringUtils.isNotBlank(getScanLpnTextField().getText())) {
            runOnFxThread(() -> {
                getScanLpnView().setVisible(false);
                getScanLpnView().setManaged(false);
            });
            // This delay to ensure that custom1 field is updated in the backend
            // before connection automation event is raised
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // do nothing
            }
            deviceOperationHelper.notifyBackendToContinueConnectionAutomation(this.device);
        }
    }

    /**
     * Action on clicking Done button on validation imei serial
     */
    @FXML
    public void onValidateImeiSerialDoneButtonClicked() {
        performImeiSerialValidation();
    }

    /**
     * Sets up a listener on the IMEI/Serial validation text field to hide the IMEI serial validation view
     * when the Enter key is pressed and the text field contains some text.
     */
    private void setupImeiValidationFieldListener() {
        validateImeiTextField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                performImeiSerialValidation();
            }
        });
    }

    @FXML
    public void bStarApiErrorCancelPressed(final MouseEvent mouseEvent) {
        bStarApiErrorVB.setVisible(false);
        bStarApiErrorVB.setManaged(false);
        bStarApiErrorObject = null;
        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("readyStartTesting"));
        setDeviceSubStatusLabel(StringUtils.EMPTY);
    }

    @FXML
    public void bStarApiErrorRetryPressed(final MouseEvent mouseEvent) {
        bStarApiErrorVB.setVisible(false);
        bStarApiErrorVB.setManaged(false);

        String errorTitle = bStarApiErrorTitleLBL.getText();
        if (bStarApiErrorTitleLBL.getText().equalsIgnoreCase(
                getLocalizationService().getLanguageSpecificText(SOURCE_API_ERROR))) {
            setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("sourceApiCalling"));
            errorTitle = SOURCE_API_ERROR;
        } else if (bStarApiErrorTitleLBL.getText().equalsIgnoreCase(
                getLocalizationService().getLanguageSpecificText(LABEL_API_ERROR))) {
            setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("labelApiCalling"));
            errorTitle = LABEL_API_ERROR;
        } else if (bStarApiErrorTitleLBL.getText().equalsIgnoreCase(
                getLocalizationService().getLanguageSpecificText(RESULTS_API_ERROR))) {
            setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("resultsApiCalling"));
            errorTitle = RESULTS_API_ERROR;
        }

        final String topic = TopicBuilder.buildGenericTopic("bStar-api-retries", "request");
        final TwoWayApiRetriesRequestMessage message = new TwoWayApiRetriesRequestMessage();
        message.setId(device.getId());
        message.setErrorTitle(errorTitle);
        message.setRequestObject(bStarApiErrorObject);
        message.setPrintOperation(printOperation);
        publishToMqttTopic(topic, message);
    }

    /**
     * Validates the IMEI/Serial input against the device's stored values.
     * If the input is valid, the validation view is hidden, and the defect is updated to 'PASSED'.
     * If the input is invalid, the defect is updated to 'FAILED'.
     * Notifies the backend of the validation result and updates the results.
     */
    private void performImeiSerialValidation() {
        String imeiSerial = getValidateImeiTextField().getText();

        if (StringUtils.isNotBlank(imeiSerial)) {
            boolean isValidImeiSerial = StringUtils.equals(imeiSerial, this.device.getImei()) ||
                    StringUtils.equals(imeiSerial, this.device.getImei2()) ||
                    StringUtils.equals(imeiSerial, this.device.getSerial()) ||
                    StringUtils.equals(imeiSerial, this.device.getMeid()) ||
                    StringUtils.equals(imeiSerial, this.device.getMeid2());

            runOnFxThread(() -> {
                getImeiSerialValidationView().setVisible(false);
                getImeiSerialValidationView().setManaged(false);
            });

            if (isValidImeiSerial) {
                getFailedTestKeys().remove(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey());
                updateDefectOnMainView(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey(), PASSED, true);
            } else {
                if (!getFailedTestKeys().contains(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey())) {
                    getFailedTestKeys().add(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey());
                    updateDefectOnMainView(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey(), FAILED, true);
                }
            }
            deviceOperationHelper.notifyBackendToValidateImei(this.device, isValidImeiSerial);
            updateAndNotifyBackendForResultsChange(false);
        } else {
            LOGGER.info("IMEI/Serial should not be empty");
        }
    }

    /**
     * Action on clicking Close button on scan LPN Button View
     */
    @FXML
    public void onHideLpnView() {
        runOnFxThread(() -> {
            getScanLpnView().setVisible(false);
            getScanLpnView().setManaged(false);
        });
    }

    /**
     * Requests to send device in recovery mode
     */
    @FXML
    protected void onRecoverClick() {
        restoreLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        if (this.device != null) {
            setDeviceIdMDC(this.device.getId());

            runOnFxThread(() -> {
                if (moreMenuPopOver.isShowing()) {
                    moreMenuPopOver.hide();
                }
            });

            LOGGER.debug("Sending device in recovery mode.");
            final String topic = TopicBuilder.build(device, "recovery", "request");
            final IosDeviceRestoreRequestMessage requestMessage = new IosDeviceRestoreRequestMessage();
            requestMessage.setId(device.getId());
            publishToMqttTopic(topic, requestMessage);
            updateDeviceLabelsForRestore();
        }
    }

    /**
     * Requests to hide Manual App Install Instructions Pop-up
     */
    @FXML
    public void onHideManualAppInstallInstructionsView() {
        runOnFxThread(() -> {
            getManualAppInstallInstructionsView().setVisible(false);
            getManualAppInstallInstructionsView().setManaged(false);
        });
    }

    /**
     * Action on clicking reprint label button from Device Reprocess pop-up
     */
    @FXML
    public void onReprintLabelClicked() {
        hideDeviceReprocessPopup();
        LOGGER.info("Re-print Label clicked from Device reprocess view.");
        final String topic = TopicBuilder.build(device, "device", "reprocess", "popup", "response");
        final DeviceReprocessResponseMessage message = new DeviceReprocessResponseMessage();
        message.setId(device.getId());
        message.setFromReprint(true);
        publishToMqttTopic(topic, message);
    }

    /**
     * Action on clicking re-process button from Device Reprocess pop-up
     */
    @FXML
    public void onReprocessDeviceClicked() {
        hideDeviceReprocessPopup();
        LOGGER.info("Re-process device clicked from Device reprocess view.");
        final String topic = TopicBuilder.build(device, "device", "reprocess", "popup", "response");
        final DeviceReprocessResponseMessage message = new DeviceReprocessResponseMessage();
        message.setId(device.getId());
        message.setFromReprint(false);
        publishToMqttTopic(topic, message);
    }

    /**
     * Hide device reprocess pop-up
     */
    private void hideDeviceReprocessPopup() {
        LOGGER.info("Hide device reprocess pop-up.");
        Runnable runnable = () -> {
            getDeviceReprocessView().setVisible(false);
            getDeviceReprocessView().setManaged(false);
        };
        runOnFxThread(runnable);
    }

    /**
     * Method when Done Xiaomi App Admin Activation Button Clicked
     */
    @FXML
    public void onDoneAdminAppActivation() {
        hideAppAdminPopupAndNotifyBackend();
    }

    // ==================================================================
    //                        UI related Methods
    // ==================================================================

    public void showSimCardDetectedMsgBox(final Device device, final boolean onSkipEraseFlow) {
        onSkipErased = onSkipEraseFlow;
        runOnFxThread(() -> {
            getEraserIcon().setImage(new Image("com/phonecheck/image/icon/eraser-icon.png"));
            getSimCardErrorVBox().setVisible(true);
        });
    }

    public void showSimCardDefectOnUI() {
        runOnFxThread(() -> {
            deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("simCardWarnMsg"));
            String simCardKey = InitialDefectKey.SIM_CARD_DETECTED.getKey();
            updateDefectOnMainView(simCardKey, FAILED, false);
            if (!failedTestKeys.contains(simCardKey)) {
                failedTestKeys.add(simCardKey);
                TestResultsUtil.updateTestResultsInDevice(device, failedTestKeys, passedTestKeys,
                        uiInMemoryStore.getAssignedCloudCustomization(), getLocalizationService());
            }
        });
    }

    public void removeSimCardDefect() {
        runOnFxThread(() -> {
            deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("ready"));
            clearDefectsForKey("Sim-Card");
            failedTestKeys.remove(InitialDefectKey.SIM_CARD_DETECTED.getKey());
            TestResultsUtil.updateTestResultsInDevice(device, failedTestKeys, passedTestKeys,
                    uiInMemoryStore.getAssignedCloudCustomization(), getLocalizationService());
        });
    }

    public void showSdCardDetectedMsgBox(final boolean onSkipEraseFlow) {
        onSkipErased = onSkipEraseFlow;
        runOnFxThread(() -> {
            getEraserIcon().setImage(new Image("com/phonecheck/image/icon/eraser-icon.png"));
            getSdCardErrorVBox().setVisible(true);
        });
    }

    public void showSdCardDefectOnUI() {
        runOnFxThread(() -> {
            deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("sdCardWarnMsg"));
            String sdCardKey = InitialDefectKey.SD_CARD_DETECTED.getKey();
            updateDefectOnMainView(sdCardKey, FAILED, false);

            if (!failedTestKeys.contains(sdCardKey)) {

                failedTestKeys.add(sdCardKey);
                TestResultsUtil.updateTestResultsInDevice(device, failedTestKeys, passedTestKeys,
                        uiInMemoryStore.getAssignedCloudCustomization(), getLocalizationService());
            }
        });
    }

    public void showDeviceLockDetectedMsgBox() {
        runOnFxThread(() -> {
            getDeviceLockErrorVBox().setVisible(true);
            if (!DeviceLock.ON.equals(getDevice().getDeviceLock())) {
                getDeviceLockedErrorMessage()
                        .setText(getLocalizationService().getLanguageSpecificText("removePinLockAndContinue"));
            }
        });
    }

    public void removeSdCardDefect() {
        runOnFxThread(() -> {
            deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("ready"));
            clearDefectsForKey("SD-Card");
            failedTestKeys.remove(InitialDefectKey.SD_CARD_DETECTED.getKey());
            TestResultsUtil.updateTestResultsInDevice(device, failedTestKeys, passedTestKeys,
                    uiInMemoryStore.getAssignedCloudCustomization(), getLocalizationService());
        });
    }

    /**
     * Sets the visibility of selection pane.
     *
     * @param selected is selected
     */
    public void selectDeviceBox(final boolean selected) {
        isSelected = selected;
        runOnFxThread(() -> selectionPane.setVisible(selected));
    }

    /**
     * Creates the list of workflow steps info under device info tab
     *
     * @param stage Device stage
     */
    public void setWorkFlowStepInfo(final DeviceStage stage) {
        // Lookup the node with the given ID in the VBox
        Node node = workFlowInfoVBox.lookup("#" + stage + "_ID");
        // Check if the node is a Label
        if (node instanceof Label label) {
            // Check if the label is not already visible
            if (!label.isVisible()) {
                // Set the label visibility to true
                label.setVisible(true);
                label.setManaged(true);
            }
        }
    }

    /**
     * Resets the workflow steps in info view
     */
    protected void resetWorkFlowStepInfo() {
        // Iterate through the workFlowInfoVBox children
        for (Node child : workFlowInfoVBox.getChildren()) {
            // Set the visibility of each child to true
            child.setVisible(false);
            child.setManaged(false);
        }
    }

    /**
     * Update the cosmetics checkboxes on the device box
     *
     * @param cosmeticResults cosmetic results
     */
    private void updateDesktopCosmeticsValues(final CosmeticsResults cosmeticResults) {
        if (cosmeticResults != null) {
            String failedCosmeticsString = cosmeticResults.getFailed();
            if (StringUtils.isNotBlank(failedCosmeticsString)) {
                String[] failedCosmetics = failedCosmeticsString.split(",");
                for (Node node : cosmeticsContainer.getChildren()) {
                    CheckBox checkBox = (CheckBox) node;
                    for (String cosmetic : failedCosmetics) {
                        if (cosmetic.trim().equals(checkBox.getText().trim())) {
                            checkBox.setSelected(true);
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * Update the fail device checkboxes on the device box
     */
    private void updateFailedDeviceValues() {
        for (Node node : failDeviceContainer.getChildren()) {
            CheckBox checkBox = (CheckBox) node;
            /*
               This check is made to update the fail device reason checkboxes, when manually passed or
               failed a device with a reason from diagnostic view / fail device in UI
             */
            checkBox.setSelected(failedTestKeys.contains(checkBox.getId()) ||
                    failedTestKeys.contains(checkBox.getId().substring(2)));
        }
    }

    /**
     * Update the data verification message on the device box
     *
     * @param isDataFound : true if data found on the device
     */
    public void updateDataVerificationStatusOnUI(final boolean isDataFound) {
        addDataVerificationResultToTestResults(isDataFound);
        runOnFxThread(() -> {
            String text = isDataFound ? getLocalizationService().getLanguageSpecificText("dataFound") :
                    getLocalizationService().getLanguageSpecificText("dataNotFound");

            defectsFlowPane.getChildren().removeAll();

            Label defectLabel = new Label(text);
            defectLabel.setId("dataVerificationMsg");
            defectLabel.setPrefHeight(21);
            if (isDataFound) {
                defectLabel.setStyle("-fx-background-color:#F41947; -fx-background-radius: 3 3 3 3;" +
                        " -fx-text-fill: white; -fx-font-weight: bold;");
            } else {
                defectLabel.setStyle("-fx-background-color:#28B463; -fx-background-radius: 3 3 3 3;" +
                        " -fx-text-fill: white; -fx-font-weight: bold;");
            }
            defectLabel.setFont(new Font("Helvetica Bold", 10));
            defectLabel.setPadding(new Insets(0, 10, 0, 30));
            defectLabel.prefWidthProperty().bind(getDefectsFlowPane().widthProperty());
            defectLabel.setTextAlignment(TextAlignment.CENTER);

            defectsFlowPane.getChildren().add(0, defectLabel);
            manualPeoLabel.setDisable(true);
        });
    }

    /**
     * Update data verification result as pass/fail in test results
     *
     * @param isDataFound
     */
    private void addDataVerificationResultToTestResults(final boolean isDataFound) {
        DeviceTestResult deviceTestResult = device.getDeviceTestResult();
        if (deviceTestResult == null) {
            deviceTestResult = new DeviceTestResult();
            deviceTestResult.setTestResults(new TestResults());
            deviceTestResult.getTestResults().setPassed(new ArrayList<>());
            deviceTestResult.getTestResults().setFailed(new ArrayList<>());
        } else if (deviceTestResult.getTestResults() == null) {
            deviceTestResult.setTestResults(new TestResults());
            deviceTestResult.getTestResults().setPassed(new ArrayList<>());
            deviceTestResult.getTestResults().setFailed(new ArrayList<>());
        } else {
            if (deviceTestResult.getTestResults().getPassed() == null) {
                deviceTestResult.getTestResults().setPassed(new ArrayList<>());
            }
            if (deviceTestResult.getTestResults().getFailed() == null) {
                deviceTestResult.getTestResults().setFailed(new ArrayList<>());
            }
        }
        if (isDataFound) {
            deviceTestResult.getTestResults().getFailed().add(DATA_VERIFICATION);
            this.failedTestKeys.add(DATA_VERIFICATION);
        } else {
            deviceTestResult.getTestResults().getPassed().add(DATA_VERIFICATION);
            this.passedTestKeys.add(DATA_VERIFICATION);
        }
        this.device.setDeviceTestResult(deviceTestResult);
    }


    /**
     * Update the current stage info on the UI.
     *
     * @param device target device
     */
    public void updateCurrentStageOnUI(final Device device) {
        if (null != this.device) {
            if (this.device.getId().equalsIgnoreCase(device.getId())) {
                this.device.setStage(device.getStage());

                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Updating device stage on UI");
                    if (DeviceStage.POWER_OFF.equals(device.getStage())) {
                        setDeviceStatusLabel(STAGE_TO_LABEL_MAPPING.get(device.getStage()));
                    } else if (DeviceStage.READY_IN_PRE_CHECK.equals(device.getStage())) {
                        setDeviceStatusLabel(STAGE_TO_LABEL_MAPPING.get(device.getStage()));
                    } else if (STAGE_TO_LABEL_MAPPING.get(device.getStage()) != null
                            && ((!deviceStatusLabel.getText().startsWith(
                            getLocalizationService().getLanguageSpecificText(
                                    DeviceStage.READY.getLocalizedKey()))
                            && !deviceStatusLabel.getText().startsWith(
                            getLocalizationService().getLanguageSpecificText(
                                    DeviceStage.NOT_READY.getLocalizedKey()))
                            && !deviceStatusLabel.getText().equalsIgnoreCase(
                            getLocalizationService().getLanguageSpecificText(
                                    DeviceStage.APP_TESTING_DONE.getLocalizedKey()))
                            && !deviceStatusLabel.getText().equalsIgnoreCase(
                            getLocalizationService().getLanguageSpecificText(
                                    ERASE_REQUEST_SENT))
                            && !this.device.isEraseInProgress()
                            && !this.device.isRestoreInProgress()
                            && !this.device.isShutdownInProgress()) ||
                            (deviceStatusLabel.getText().startsWith(
                                    getLocalizationService().getLanguageSpecificText(
                                            DeviceStage.NOT_READY.getLocalizedKey())) &&
                                    DeviceStage.READY.equals(device.getStage())) ||
                            (deviceStatusLabel.getText().startsWith(
                                    getLocalizationService().getLanguageSpecificText(
                                            DeviceStage.READY_IN_AT.getLocalizedKey())) &&
                                    DeviceStage.READY.equals(device.getStage())))) {

                        setDeviceStatusLabel(STAGE_TO_LABEL_MAPPING.get(device.getStage()));
                    }
                    setWorkFlowStepInfo(device.getStage());
                    updateDeviceSubStatusOnUI(device.getStage());
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Updates sub-status of device stage under device status
     *
     * @param deviceStage target device stage
     */
    protected void updateDeviceSubStatusOnUI(final DeviceStage deviceStage) {
        if (device != null && deviceStage != null) {
            if (STAGE_TO_SUB_STEP_MAPPING.get(deviceStage) != null) {
                showDeviceSubStatusLabel(true);
                String text = getLocalizationService().getLanguageSpecificText(
                        STAGE_TO_SUB_STEP_MAPPING.get(device.getStage()));
                setDeviceSubStatusLabel(text);
            } else if (shouldNotShowDeviceSubStep(deviceStage)) {
                showDeviceSubStatusLabel(false);
            } else {
                showDeviceSubStatusLabel(true);
                if (DeviceStage.RESTORE_IN_PROGRESS != deviceStage) {
                    setDeviceSubStatusLabel(getLocalizationService().
                            getLanguageSpecificText(deviceStage.getLocalizedKey()));
                }
            }
        }
    }

    /**
     * Hides or shows sub-status of the device
     *
     * @param visibility sub-status visibility
     */
    protected void showDeviceSubStatusLabel(final boolean visibility) {
        deviceSubStatusHBox.setVisible(visibility);
        deviceSubStatusHBox.setManaged(visibility);
    }

    /**
     * checks if device is on one of the special stages e.g during erase, restore, shutting down, app testing done
     *
     * @param deviceStage device stage
     * @return true if its on one of the special stages
     */
    protected boolean shouldNotShowDeviceSubStep(final DeviceStage deviceStage) {
        return this.device.isShutdownInProgress() || this.device.isEraseInProgress() ||
                DeviceStage.ERASE_SUCCESS.equals(deviceStage) ||
                DeviceStage.APP_TESTING_DONE.equals(deviceStage) ||
                DeviceStage.READY_IN_PRE_CHECK.equals(deviceStage) ||
                deviceStatusLabel.getText().equalsIgnoreCase(
                        getLocalizationService().getLanguageSpecificText(ERASE_REQUEST_SENT));
    }

    /**
     * Displays passed and failed results in info tab under diagnostics and
     * also displays defects in main view.
     *
     * @param testKey    test result key
     * @param testResult test result text representation
     */
    protected void updateDefectOnDiagnosticsView(final String testKey, final String testResult) {
        if (!testsFlowPane.getChildren().contains(testsFlowPane.lookup("#" +
                testKey.replaceAll(" ", "") + "_TestResult")) && !FAIL_DEVICE_REASONS.contains(testKey)) {

            // create HBox container for results label
            HBox container = new HBox();
            container.setSpacing(4);
            container.setAlignment(Pos.CENTER);
            container.setPadding(new Insets(2, 4, 2, 0));
            container.setPrefHeight(23);
            container.setPrefHeight(Pane.USE_COMPUTED_SIZE);
            container.setId(testKey.replaceAll(" ", "") + "_TestResult");

            // create test key label
            Label keyLabel = new Label(formatKey(testKey.trim()));
            keyLabel.setFont(new Font("Helvetica", 11));
            keyLabel.setMinWidth(130d);

            // create test result label
            Label resultLabel = new Label(getLocalizationService().getLanguageSpecificText(testResult.toLowerCase()));
            resultLabel.setMinWidth(30d);
            resultLabel.setFont(new Font("Helvetica", 11));
            resultLabel.setId(testKey.replaceAll(" ", "") + "_TestResultLabel");

            // Mouse click event to manually override test results from the UI
            resultLabel.setOnMouseClicked((mouseEvent) -> {
                manuallyOverrideTestResult(keyLabel, resultLabel, testKey);
            });

            // add test key(e.g Wi-Fi, digitizer) and test result(passed/failed) in container
            container.getChildren().add(keyLabel);
            container.getChildren().add(resultLabel);

            testsFlowPane.getChildren().add(container);
            LOGGER.info("{} Adding test key to the diagnostic view", testKey);
            // if result is failed display it as red
            if (testResult.equalsIgnoreCase(FAILED)) {
                keyLabel.setStyle("-fx-text-fill: red");
                resultLabel.setStyle("-fx-text-fill: red");
            }
        } else if (testsFlowPane.getChildren().contains(testsFlowPane.lookup("#" +
                testKey.replaceAll(" ", "") + "_TestResult"))) {

            /*
               This else if is added to update the diagnostic view if one of the failed
               reasons is present in it.  ex: WiFi, LCD, Digitizer..
             */
            HBox hbox = (HBox) testsFlowPane.lookup("#" +
                    testKey.replaceAll(" ", "") + "_TestResult");
            // create test result label
            Label keyLabel = (Label) hbox.getChildren().get(0);
            Label resultLabel = (Label) hbox.getChildren().get(1);
            resultLabel.setText(getLocalizationService().getLanguageSpecificText(testResult.toLowerCase()));
            String updatedTestKey = "";
            if (keyLabel.getText().startsWith("M-") || keyLabel.getText().startsWith("P-")) {
                updatedTestKey = keyLabel.getText();
            } else {
                updatedTestKey = "M-" + keyLabel.getText();
            }
            LOGGER.info("{} test is already present in the diagnostic view and updating it's " +
                    "text as {}. ", testKey, updatedTestKey);
            keyLabel.setText(updatedTestKey);
            // if result is failed display it as red
            if (testResult.equalsIgnoreCase(FAILED)) {
                keyLabel.setStyle("-fx-text-fill: red");
                resultLabel.setStyle("-fx-text-fill: red");
            } else {
                resultLabel.setStyle("-fx-text-fill: black");
                keyLabel.setStyle("-fx-text-fill: black");
            }
            hbox.getChildren().set(1, resultLabel);
        }
    }

    /**
     * Adds defects on the device's main view
     *
     * @param testKey         defects key
     * @param testResultType  test results type can be: FAILED, PASSED or PENDING
     * @param isInitialDefect flag to represent if test key is of an initial defect
     */
    protected void updateDefectOnMainView(final String testKey,
                                          final String testResultType,
                                          final boolean isInitialDefect) {
        LOGGER.info("Updating defect: {} of type: {} on UI", testKey, testResultType);

        boolean isDeviceLockKeyAndDisabledFromCloud = Objects.equals(testKey, InitialDefectKey.DEVICE_LOCK.getKey()) &&
                !TestResultsUtil.isKeyExistInTestPlan(InitialDefectKey.DEVICE_LOCK.getKey(), this.device,
                        getUiInMemoryStore().getAssignedCloudCustomization())
                && testResultType.equalsIgnoreCase(FAILED);

        boolean isMDMKeyAndDisabledFromCloud = Objects.equals(testKey, InitialDefectKey.MDM.getKey()) &&
                !TestResultsUtil.isKeyExistInTestPlan(InitialDefectKey.MDM.getKey(), this.device,
                        getUiInMemoryStore().getAssignedCloudCustomization())
                && testResultType.equalsIgnoreCase(FAILED);

        if (isDeviceLockKeyAndDisabledFromCloud || isMDMKeyAndDisabledFromCloud) {
            LOGGER.info("{} key is not part of Test plan. So not updating this defect on UI", testKey);
            return;
        }

        List<String> faceIdTouchIdKeys = Arrays.asList(
                InitialDefectKey.FACE_ID.getKey(),
                InitialDefectKey.TOUCH_ID.getKey(),
                InitialDefectKey.MANUAL_FACE_ID.getKey(),
                InitialDefectKey.MANUAL_TOUCH_ID.getKey()
        );

        List<String> japaneseConformityMarkKey = Arrays.asList(
                InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey(),
                InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey()
        );

        boolean isFaceIdTouchId = faceIdTouchIdKeys.contains(testKey);
        boolean isJapaneseConformity = japaneseConformityMarkKey.contains(testKey);

        boolean isFaceIdTouchIdDefectAdded = faceIdTouchIdKeys.stream()
                .anyMatch(key -> defectsFlowPane.getChildren()
                        .contains(defectsFlowPane
                                .lookup("#" + key.replaceAll(" ", "") + "_Defect")));
        boolean isJapaneseConformityAdded =
                japaneseConformityMarkKey.stream()
                        .anyMatch(key -> defectsFlowPane.getChildren()
                                .contains(defectsFlowPane
                                        .lookup("#" + key.replaceAll(" ", "") + "_Defect")));

        String testKeyText;
        if (testKey.startsWith("M-")) {
            testKeyText = testKey.substring(2);
        } else {
            testKeyText = testKey;
        }

        if (testResultType.equalsIgnoreCase(FAILED) &&
                (isFaceIdTouchId || isJapaneseConformity || !defectsFlowPane.getChildren().contains(defectsFlowPane
                        .lookup("#" + testKey.replaceAll(" ", "") + "_Defect")))) {
            // creates defect label in main view
            Label defectLabel = setupDefectLabel(testKey, testKeyText);

            if (isInitialDefect) {
                if (isFaceIdTouchId || isJapaneseConformity) {
                    if (isFaceIdTouchIdDefectAdded || isJapaneseConformityAdded) {
                        removeDefectFromMainView(testKey);
                    }

                    StackPane imageDefectPane =
                            setupInitialDefectImagePane(testKeyText, testResultType);
                    defectsFlowPane.getChildren().add(0, imageDefectPane);
                } else {
                    if (isFaceIdTouchIdDefectAdded ^ isJapaneseConformityAdded) {
                        defectsFlowPane.getChildren().add(1, defectLabel);
                    } else if (isFaceIdTouchIdDefectAdded) {
                        defectsFlowPane.getChildren().add(2, defectLabel);
                    } else {
                        defectsFlowPane.getChildren().add(0, defectLabel);
                    }
                }
            } else if (isJapaneseConformity) {
                removeDefectFromMainView(testKey);
                StackPane imageDefectPane =
                        setupInitialDefectImagePane(testKeyText, testResultType);
                defectsFlowPane.getChildren().add(0, imageDefectPane);
            } else if (isFaceIdTouchId) {
                removeDefectFromMainView(testKey);
                StackPane imageDefectPane =
                        setupInitialDefectImagePane(testKeyText, testResultType);
                defectsFlowPane.getChildren().add(0, imageDefectPane);
            } else if (testKey.equals(ESIM_DETECTED)) {
                removeDefectFromMainView(testKey);
                StackPane imageDefectPane =
                        setupInitialDefectImagePane(testKeyText, testResultType);
                defectsFlowPane.getChildren().add(0, imageDefectPane);
            } else {
                defectsFlowPane.getChildren().add(defectLabel);
            }
        } else if (!testResultType.equalsIgnoreCase(FAILED)) {
            if (isFaceIdTouchId || isJapaneseConformity) {
                if (isFaceIdTouchIdDefectAdded || isJapaneseConformityAdded) {
                    removeDefectFromMainView(testKey);
                }
                StackPane imageDefectPane =
                        setupInitialDefectImagePane(testKeyText, testResultType);
                defectsFlowPane.getChildren().add(0, imageDefectPane);
            } else if (testKey.equals(ESIM_DETECTED)) {
                removeDefectFromMainView(testKey);
                StackPane imageDefectPane =
                        setupInitialDefectImagePane(testKeyText, testResultType);
                defectsFlowPane.getChildren().add(0, imageDefectPane);
            } else {
                removeDefectFromMainView(testKey);
            }
        } else {
            LOGGER.warn("Did not update the main UI with defects");
        }
    }

    public Label setupDefectLabel(final String testKey, final String testKeyText) {
        Label defectLabel = new Label(formatKey(testKeyText.trim()));
        defectLabel.setId(testKey.replaceAll(" ", "") + "_Defect");
        defectLabel.setPrefHeight(21);
        defectLabel.setStyle("-fx-background-color:#F41947; -fx-background-radius: 3 3 3 3;" +
                " -fx-text-fill: white;");
        defectLabel.setFont(new Font("Helvetica Bold", 10));
        defectLabel.setPadding(new Insets(0, 7, 0, 7));

        return defectLabel;
    }

    public StackPane setupInitialDefectImagePane(final String testKey,
                                                 final String testResultType) {
        ImageView imageViewDefect = new ImageView();
        imageViewDefect.setFitHeight(17);
        imageViewDefect.setPreserveRatio(true);

        String imageIconFileName = null;
        if (testKey.equals(InitialDefectKey.FACE_ID.getKey())) {
            imageIconFileName = "face-id.png";
        } else if (testKey.equals(InitialDefectKey.TOUCH_ID.getKey())) {
            imageIconFileName = "finger-print.png";
        } else if (testKey.equals(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey())
                || testKey.equals(InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey())) {
            imageIconFileName = "japanese-conformity-mark.png";
        } else if (testKey.equals(ESIM_DETECTED)) {
            imageIconFileName = "esim-detected.png";
        }

        if (imageIconFileName != null) {
            setImageIconToImageView(DeviceBoxController.this,
                    imageViewDefect, imageIconFileName);

            // Create the StackPane and add both the ImageView and Label
            StackPane imageViewIdDefectPane = new StackPane();
            imageViewIdDefectPane.setId(testKey.replaceAll(" ", "") + "_Defect");

            imageViewIdDefectPane.getChildren().addAll(imageViewDefect);
            imageViewIdDefectPane.setPadding(new Insets(1, 5, 1, 5));

            if (testResultType.equalsIgnoreCase(FAILED)) {
                imageViewIdDefectPane.setStyle("-fx-background-color:#F41947; -fx-background-radius: 3 3 3 3;");
            } else {
                imageViewIdDefectPane.setStyle("-fx-background-color:#13C784; -fx-background-radius: 3 3 3 3;");
            }

            // Set color of FaceId label in pop-menu if performed from mobile app
            if (InitialDefectKey.FACE_ID.getKey().equals(testKey)) {
                Color color = testResultType.equalsIgnoreCase(FAILED) ? Color.RED : Color.GREEN;
                getFaceIdLabel().setTextFill(color);
            }

            return imageViewIdDefectPane;
        } else {
            LOGGER.info("Image defect is not supported for this defect key: {}", testKey);

            return null;
        }
    }

    /**
     * Methods removes spaces from provided string and returns localized string
     *
     * @param testKey testKey
     * @return flag
     */
    private String formatKey(final String testKey) {
        boolean isPreCheckMode = uiInMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.PRE_CHECK;
        String processedTestKey = isPreCheckMode && testKey.startsWith("P-") ? testKey.substring(2) : testKey;
        processedTestKey = Character.toLowerCase(processedTestKey.charAt(0)) + processedTestKey.substring(1);
        String langSpecificText = getLocalizationService().getLanguageSpecificText(processedTestKey);
        if (Objects.equals(langSpecificText, processedTestKey)) {
            return testKey;
        }
        // Append "P-" prefix back if in PreCheck mode
        return isPreCheckMode ? "P-" + langSpecificText : langSpecificText;
    }

    protected void clearBatteryDefects() {
        LOGGER.debug("Clearing battery defects");
        List<Node> nodesToBeRemoved = new ArrayList<>();
        for (Node node : defectsFlowPane.getChildren()) {
            String id = node.getId();
            if (id.startsWith("BH")) {
                LOGGER.debug("Removing battery defect : {} from the UI", id);
                nodesToBeRemoved.add(node);
            }
        }
        defectsFlowPane.getChildren().removeAll(nodesToBeRemoved);
    }

    protected void clearDefectsForKey(final String key) {
        List<Node> nodesToBeRemoved = new ArrayList<>();
        for (Node node : defectsFlowPane.getChildren()) {
            String id = node.getId();
            if (id.startsWith(key)) {
                nodesToBeRemoved.add(node);
            }
        }
        defectsFlowPane.getChildren().removeAll(nodesToBeRemoved);
    }


    /**
     * Removes defects from the device's main view
     *
     * @param testKey defects key
     */
    protected void removeDefectFromMainView(final String testKey) {
        LOGGER.info("Removing defect {} from main view", testKey);
        // remove defect label from main view
        defectsFlowPane.getChildren().remove(defectsFlowPane.lookup("#" +
                testKey.replaceAll(" ", "") + "_Defect"));
    }

    /**
     * Manually overrides the test results on the UI and send mqtt requests to backed to
     * override the results in the database
     *
     * @param keyLabel    test key label representing test key e.g. LCD, Digitizer etc.
     * @param resultLabel test result label representing test result i.e. passed/failed
     * @param testKey     test key
     */
    @FXML
    protected void manuallyOverrideTestResult(final Label keyLabel, final Label resultLabel, final String testKey) {
        setDeviceIdMDC(this.device.getId());
        LOGGER.info("Manually overriding the test results for {}", testKey);
        // if manually overriding a test then prepend M- to the test
        String testResultKey = getLocalizationService().getLanguageSpecificText(testKey);
        String updatedTestKey;
        if (keyLabel.getText().startsWith("M-")) {
            updatedTestKey = keyLabel.getText();
        } else {
            updatedTestKey = "M-" + keyLabel.getText();
        }

        if (resultLabel.getText().equalsIgnoreCase(getLocalizationService().getLanguageSpecificText("passed"))) {
            resultLabel.setText(getLocalizationService().getLanguageSpecificText("failed"));
            resultLabel.setStyle("-fx-text-fill: red");
            keyLabel.setStyle("-fx-text-fill: red");
            keyLabel.setText(updatedTestKey);

            updateDefectOnMainView(testKey, FAILED, false);

            if (passedTestKeys.contains(testResultKey) || passedTestKeys.contains(updatedTestKey)) {
                if (!passedTestKeys.remove(testResultKey)) {
                    passedTestKeys.remove(updatedTestKey);
                }
                if (!failedTestKeys.contains(updatedTestKey)) {
                    failedTestKeys.add(updatedTestKey);
                    LOGGER.info("Added test result {} to failed test keys ", testKey);
                }
            } else if (passedCosmeticTestKeys.contains(testResultKey) ||
                    passedCosmeticTestKeys.contains(updatedTestKey)) {
                if (!passedCosmeticTestKeys.remove(testResultKey)) {
                    passedCosmeticTestKeys.remove(updatedTestKey);
                }
                if (failedCosmeticTestKeys.isEmpty()) {
                    LOGGER.info("Failing the cosmetics as there is at least one failed cosmetic {} ", testKey);
                    updateDefectOnDiagnosticsView("Cosmetics", FAILED);
                    updateDefectOnMainView("Cosmetics", FAILED, false);
                    failedTestKeys.add("Cosmetics");
                    passedTestKeys.remove("Cosmetics");
                }
                if (!failedCosmeticTestKeys.contains(updatedTestKey)) {
                    failedCosmeticTestKeys.add(updatedTestKey);
                    LOGGER.info("Added cosmetic {} to failed cosmetic test keys ", testKey);
                }
            }
        } else {
            resultLabel.setText(getLocalizationService().getLanguageSpecificText("passed"));
            resultLabel.setStyle("-fx-text-fill: black");
            keyLabel.setStyle("-fx-text-fill: black");
            keyLabel.setText(updatedTestKey);
            updateDefectOnMainView(testKey, PASSED, false);
            if (failedTestKeys.contains(testResultKey) || failedTestKeys.contains(updatedTestKey)) {
                if (!failedTestKeys.remove(testResultKey)) {
                    failedTestKeys.remove(updatedTestKey);
                }
                if (!passedTestKeys.contains(updatedTestKey)) {
                    passedTestKeys.add(updatedTestKey);
                    LOGGER.info("Added test result {} to passed test keys ", testKey);
                }
            } else if (failedCosmeticTestKeys.contains(testResultKey) ||
                    failedCosmeticTestKeys.contains(updatedTestKey)) {
                if (failedCosmeticTestKeys.remove(testResultKey) || failedCosmeticTestKeys.remove(updatedTestKey)) {
                    if (failedCosmeticTestKeys.isEmpty()) {
                        LOGGER.info("Passing the cosmetics as there is no more failed cosmetics");
                        passedTestKeys.add("Cosmetics");
                        failedTestKeys.remove("Cosmetics");
                        updateDefectOnDiagnosticsView("Cosmetics", PASSED);
                        updateDefectOnMainView("Cosmetics", PASSED, false);
                    }
                }
                if (!passedCosmeticTestKeys.contains(updatedTestKey)) {
                    passedCosmeticTestKeys.add(updatedTestKey);
                    LOGGER.info("Added cosmetic {} to passed cosmetic test keys ", testKey);
                }
            }
        }

        updateFailedDeviceValues();

        // Notify backend about the manual test result override
        updateAndNotifyBackendForResultsChange(false);

        showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                getUiInMemoryStore().getAssignedCloudCustomization()));
    }

    /**
     * Method to show the functionality status with Red/ Green background color on Man tab icon
     *
     * @param functionalityStatus device functionality status
     */
    protected void showFunctionalityStatus(final String functionalityStatus) {
        if ((!functionalityStatus.equals(FunctionalityStatusUtil.PENDING) ||
                StringUtils.isNotBlank(functionalityStatus))
                && device.getDeviceTestResult() != null
                && device.getDeviceTestResult().getTestResults() != null
                && device.getDeviceTestResult().getTestResults().getTestingCompleted() != null
                && device.getDeviceTestResult().getTestResults().getTestingCompleted()) {
            if (functionalityStatus.equals(FunctionalityStatusUtil.FULLY_FUNCTIONAL)) {
                setImageIconToImageView(DeviceBoxController.this,
                        mainTabMenuImageView, "info-green.png");
                marketPlaceCertifiedVBox.setVisible(true);
                marketPlaceCertifiedVBox.setManaged(true);
            } else {
                setImageIconToImageView(DeviceBoxController.this,
                        mainTabMenuImageView, "info-red.png");
                marketPlaceCertifiedVBox.setVisible(false);
                marketPlaceCertifiedVBox.setManaged(false);
            }
        }
    }

    /**
     * Highlights required fields on UI
     *
     * @param fromErase tells if request is coming from erase
     */
    protected void updateRequiredFieldsElements(final boolean fromErase) {
        Runnable runnable = () -> {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Highlighting required fields elements on the UI");
            // Required fields reset
            gradeComboBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
            colorComboBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
            carrierComboBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
            esnVboxHighlight.setStyle("-fx-border-color: -fx-control-inner-background;");
            networkLockStatusHighlight.setStyle("-fx-border-color: -fx-control-inner-background;");
            lpnTextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
            custom1TextField.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
            cosmeticTabMenuHBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");

            // Update process steps statuses on Ui
            if (!unpopulatedRequiredFieldsList.isEmpty()) {
                setDeviceStatusLabel(COMPLETE_REQUIRED_FIELDS);
                if (fromErase) {
                    LOGGER.info("Unpopulated required fields are found during erase. " +
                            "Hence re-setting the erase loader icon");
                    eraserIcon.setImage(new Image("com/phonecheck/image/icon/eraser-icon.png"));
                    showDeviceSubStatusLabel(true);
                    setDeviceSubStatusLabel(getLocalizationService().getLanguageSpecificText(ERASE_RESTRICTED));
                } else {
                    LOGGER.info("Unpopulated required fields are found during print.");
                    setDeviceSubStatusLabel(getLocalizationService().getLanguageSpecificText(PRINT_RESTRICTED));
                }
            } else {
                LOGGER.info("Currently there is no unpopulated required fields");
                setDeviceStatusLabel(fromErase ?
                        getLocalizationService().getLanguageSpecificText("eraseRequested") :
                        getLocalizationService().getLanguageSpecificText(DeviceStage.PRINTING.getLocalizedKey()));
            }

            // Required fields highlights
            for (String field : unpopulatedRequiredFieldsList) {
                if (field.equalsIgnoreCase("esn")) {
                    esnVboxHighlight.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("carrier")) {
                    carrierComboBox.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("color")) {
                    colorComboBox.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("unlock")) {
                    networkLockStatusHighlight.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("grade")) {
                    gradeComboBox.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("lpn")) {
                    lpnTextField.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("custom1")) {
                    custom1TextField.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
                if (field.equalsIgnoreCase("cosmetics")) {
                    cosmeticTabMenuHBox.setStyle("-fx-border-color: red; -fx-border-width: 2px;");
                }
            }
            MDC.clear();
        };
        runOnFxThread(runnable);
    }

    /**
     * Adds tooltip for device title label
     *
     * @param deviceTitle A string containing device title
     */
    protected void addDeviceTitleTooltip(final String deviceTitle) {
        Tooltip deviceTitleTooltip = new Tooltip(deviceTitle);
        deviceTitleTooltip.setWrapText(true);
        deviceTitleLabel.setTooltip(deviceTitleTooltip);
    }

    /**
     * Set device status label and add tooltip if status is too long
     *
     * @param inputStatus A string containing device status
     */
    protected void setDeviceStatusLabel(final String inputStatus) {
        String deviceStatus = getLocalizationService().getLanguageSpecificText(inputStatus);
        deviceStatus = StringUtils.isBlank(deviceStatus) ? inputStatus : deviceStatus;
        deviceStatusLabel.setText(deviceStatus);

        if (StringUtils.length(deviceStatus) > 32) {
            Tooltip deviceStatusTooltip = new Tooltip(deviceStatus);
            deviceStatusTooltip.setWrapText(true);
            deviceStatusLabel.setTooltip(deviceStatusTooltip);
        } else {
            deviceStatusLabel.setTooltip(null);
        }
    }

    /**
     * Set device sub status text on the device box
     *
     * @param subStatus string containing device current status
     */
    protected void setDeviceSubStatusLabel(final String subStatus) {
        deviceSubStatusLabel.setText(subStatus);

        if (StringUtils.length(subStatus) > 32) {
            Tooltip deviceStatusTooltip = new Tooltip(subStatus);
            deviceStatusTooltip.setWrapText(true);
            deviceSubStatusLabel.setTooltip(deviceStatusTooltip);
        } else {
            deviceSubStatusLabel.setTooltip(null);
        }
    }

    /**
     * Adds tooltip for country of region label
     *
     * @param countryOfRegion A string containing country of region
     */
    protected void addCountryOfRegionTooltip(final String countryOfRegion) {
        Tooltip countryOfRegionTooltip = new Tooltip(countryOfRegion);
        countryOfRegionLabel.setTooltip(countryOfRegionTooltip);
    }

    /**
     * Setup device box tabs mouse actions .
     */
    protected void setupDeviceBoxTabsMouseAction() {
        runOnFxThread(() -> {
            mainTabMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        setupMainTabMouseActions();
        setupEraseButtonMouseActions();
        setUpPowerOffButtonMouseActions();
        setupCosmeticButtonMouseActions();
        setupMoreMenuMouseActions();
        setupMoreMenuItemsMouseActions();
    }

    /**
     * set style for bottom bar main tab mouse actions
     */
    protected void setupMainTabMouseActions() {
        mainTabMenuHBox.setOnMousePressed((MouseEvent event) -> {
            mainTabMenuHBox.setStyle("-fx-background-color: #B7B7B7 ; -fx-background-radius: 20 20 20 20");
        });

        mainTabMenuHBox.setOnMouseReleased((MouseEvent event) -> {
            mainTabMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        mainTabMenuHBox.setOnMouseEntered((MouseEvent event) -> {
            mainTabMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        mainTabMenuHBox.setOnMouseExited((MouseEvent event) -> onMainViewTabExit());
    }

    /**
     * set style for bottom bar erase button mouse actions
     */
    protected void setupEraseButtonMouseActions() {
        eraseMenuHBox.setOnMouseEntered((MouseEvent event) -> {
            eraseMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 5");
        });

        eraseMenuHBox.setOnMouseExited((MouseEvent event) -> {
            eraseMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0");
        });
    }

    /**
     * Set style for bottom bar power off button mouse actions
     */
    protected void setUpPowerOffButtonMouseActions() {
        powerOffMenuHBox.setOnMouseEntered((MouseEvent event) -> {
            powerOffMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 5");
        });

        powerOffMenuHBox.setOnMouseExited((MouseEvent event) -> {
            powerOffMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0");
        });
    }

    /**
     * set style for bottom bar cosmetic button mouse actions
     */
    protected void setupCosmeticButtonMouseActions() {
        cosmeticTabMenuHBox.setOnMouseEntered((MouseEvent event) -> {
            cosmeticTabMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        cosmeticTabMenuHBox.setOnMouseExited((MouseEvent event) -> {
            cosmeticTabMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * set style for bottom bar more menu mouse actions
     */
    protected void setupMoreMenuMouseActions() {
        moreMenuHBox.setOnMouseEntered((MouseEvent event) -> {
            moreMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        moreMenuHBox.setOnMouseExited((MouseEvent event) -> {
            moreMenuHBox.setStyle("-fx-background-color: #ffffff ; -fx-background-radius: 0 0 0 0");
        });

        moreMenuHBox.setOnMousePressed((MouseEvent event) -> {
            moreMenuHBox.setStyle("-fx-background-color: #B7B7B7 ; -fx-background-radius: 20 20 20 20");
        });

        moreMenuHBox.setOnMouseReleased((MouseEvent event) -> {
            moreMenuHBox.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });
    }

    /**
     * Set style for bottom bar more menu options mouse actions
     */
    protected void setupMoreMenuItemsMouseActions() {
        setUpManualWifiPushLabelMouseAction();
        setUpManualAppInstallLabelMouseAction();
        setUpManualPeoLabelMouseAction();
        setUpManualPushFilesLabelMouseAction();
        setUpSyncResultsLabelMouseAction();
        setUpPrintLabelMouseAction();
        setUpAutoExportMouseAction();
        setUpFaceIdLabelMouseAction();
        setUpFaceIdLabelMouseAction();
        setUpTouchIdLabelMouseAction();
        setUpFailDeviceLabelMouseAction();
        setUpRestoreLabelMouseAction();
        setUpCopyInfoToAllDevicesLabelMouseAction();
        setUpValidateImeiSerialLabelMouseAction();
    }

    /**
     * Set style for more menu's wifi push label option mouse actions
     */
    protected void setUpManualWifiPushLabelMouseAction() {

        manualWifiPushLabel.setOnMouseEntered((MouseEvent event) -> {
            manualWifiPushLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        manualWifiPushLabel.setOnMouseExited((MouseEvent event) -> {
            manualWifiPushLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's manual app install option mouse actions
     */
    protected void setUpManualAppInstallLabelMouseAction() {

        manualAppInstallLabel.setOnMouseEntered((MouseEvent event) -> {
            manualAppInstallLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        manualAppInstallLabel.setOnMouseExited((MouseEvent event) -> {
            manualAppInstallLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's manual peo label option mouse actions
     */
    protected void setUpManualPeoLabelMouseAction() {

        manualPeoLabel.setOnMouseEntered((MouseEvent event) -> {
            manualPeoLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        manualPeoLabel.setOnMouseExited((MouseEvent event) -> {
            manualPeoLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's manual push files label option mouse actions
     */
    protected void setUpManualPushFilesLabelMouseAction() {

        manualPushFilesLabel.setOnMouseEntered((MouseEvent event) -> {
            manualPushFilesLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        manualPushFilesLabel.setOnMouseExited((MouseEvent event) -> {
            manualPushFilesLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's sync results option mouse actions
     */
    protected void setUpSyncResultsLabelMouseAction() {

        syncResultsLabel.setOnMouseEntered((MouseEvent event) -> {
            syncResultsLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        syncResultsLabel.setOnMouseExited((MouseEvent event) -> {
            syncResultsLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's print label option mouse actions
     */
    protected void setUpPrintLabelMouseAction() {

        printLabel.setOnMouseEntered((MouseEvent event) -> {
            printLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        printLabel.setOnMouseExited((MouseEvent event) -> {
            printLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });

        printLabel.setOnMousePressed((MouseEvent event) -> {
            printLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's print label option mouse actions
     */
    protected void setUpAutoExportMouseAction() {

        autoExportLabel.setOnMouseEntered((MouseEvent event) -> {
            autoExportLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        autoExportLabel.setOnMouseExited((MouseEvent event) -> {
            autoExportLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });

        autoExportLabel.setOnMousePressed((MouseEvent event) -> {
            autoExportLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's face id label option mouse actions
     */
    protected void setUpFaceIdLabelMouseAction() {

        faceIdLabel.setOnMouseEntered((MouseEvent event) -> {
            faceIdLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        faceIdLabel.setOnMouseExited((MouseEvent event) -> {
            faceIdLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's touch id label option mouse actions
     */
    protected void setUpTouchIdLabelMouseAction() {

        touchIdLabel.setOnMouseEntered((MouseEvent event) -> {
            touchIdLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        touchIdLabel.setOnMouseExited((MouseEvent event) -> {
            touchIdLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 0 0 0 0");
        });
    }

    /**
     * Set style for more menu's fail device label option mouse actions
     */
    protected void setUpFailDeviceLabelMouseAction() {

        failDeviceLabel.setOnMouseEntered((MouseEvent event) -> {
            failDeviceLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        failDeviceLabel.setOnMouseExited((MouseEvent event) -> {
            failDeviceLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 20 20 20 20");
        });
    }

    /**
     * Set style for more menu's restore label option mouse actions
     */
    protected void setUpRestoreLabelMouseAction() {

        restoreLabel.setOnMouseEntered((MouseEvent event) -> {
            restoreLabel.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        restoreLabel.setOnMouseExited((MouseEvent event) -> {
            restoreLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 20 20 20 20");
        });
    }

    /**
     * Set style for more menu's copy info to all devices label option mouse actions
     */
    protected void setUpCopyInfoToAllDevicesLabelMouseAction() {

        copyInfoToAllDevicesLabel.setOnMouseEntered((MouseEvent event) -> {
            copyInfoToAllDevicesLabel.setStyle("-fx-background-color:#E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        copyInfoToAllDevicesLabel.setOnMouseExited((MouseEvent event) -> {
            copyInfoToAllDevicesLabel.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 20 20 20 20");
        });

        copyInfoToAllDevicesLabel.setOnMousePressed((MouseEvent event) -> {
            copyInfoToAllDevicesLabel.setStyle("-fx-background-color:#B7B7B7 ; -fx-background-radius: 20 20 20 20");
        });

        copyInfoToAllDevicesLabel.setOnMouseReleased((MouseEvent event) -> {
            copyInfoToAllDevicesLabel.setStyle("-fx-background-color:#E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });
    }

    /**
     * Set style for more menu's validate Imei Serial label option mouse actions
     */
    protected void setUpValidateImeiSerialLabelMouseAction() {

        validateImeiSerial.setOnMouseEntered((MouseEvent event) -> {
            validateImeiSerial.setStyle("-fx-background-color: #E9E9E9 ; -fx-background-radius: 20 20 20 20");
        });

        validateImeiSerial.setOnMouseExited((MouseEvent event) -> {
            validateImeiSerial.setStyle("-fx-background-color: #ffff ; -fx-background-radius: 20 20 20 20");
        });
    }

    /**
     * Update MDM image icon as either ON or OFF
     *
     * @param mdmStatus MdmStatus ON / OFF
     */
    protected void enableMdmImageElement(final MdmStatus mdmStatus) {
        if (MdmStatus.ON.equals(mdmStatus) || MdmStatus.PRECONFIGURED.equals(mdmStatus)) {
            mdmImageView.setVisible(false);
            mdmImageView.setManaged(false);
            mdmOnImageView.setVisible(true);
            mdmOnImageView.setManaged(true);
            mdmOffImageView.setVisible(false);
            mdmOffImageView.setManaged(false);
            mdmVBox.setStyle("-fx-background-color: #F41947; -fx-background-radius: 3");
            updateDefectOnMainView(InitialDefectKey.MDM.getKey(), FAILED, true);
        } else {
            mdmImageView.setVisible(false);
            mdmImageView.setManaged(false);
            mdmOnImageView.setVisible(false);
            mdmOnImageView.setManaged(false);
            mdmOffImageView.setVisible(true);
            mdmOffImageView.setManaged(true);
            mdmVBox.setStyle("-fx-background-color: #13C784; -fx-background-radius: 3");
            updateDefectOnMainView(InitialDefectKey.MDM.getKey(), PASSED, true);
        }
        mdmLabel.setStyle("-fx-text-fill: #FFFFFF");
        mdmVBox.setDisable(false);
        showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                getUiInMemoryStore().getAssignedCloudCustomization()));

        updateAndNotifyBackendForResultsChange(false);
    }

    /**
     * Update MDM image icon as either ON or OFF
     *
     * @param knoxStatus knox status ON/OFF
     */
    protected void enableKnoxImageElement(final KnoxStatus knoxStatus) {
        oemLabel.setText("Knox");
        oemImageView.setVisible(false);
        oemImageView.setManaged(false);
        oemPassImageView.setVisible(false);
        oemPassImageView.setManaged(false);
        oemFailImageView.setVisible(false);
        oemFailImageView.setManaged(false);
        if (KnoxStatus.KNOX_ON.equals(knoxStatus)) {
            knoxImageView.setVisible(false);
            knoxImageView.setManaged(false);
            knoxOnImageView.setVisible(true);
            knoxOnImageView.setManaged(true);
            knoxOffImageView.setVisible(false);
            knoxOffImageView.setManaged(false);
            oemVBox.setStyle("-fx-background-color: #F41947; -fx-background-radius: 3");
            updateDefectOnMainView(InitialDefectKey.KNOX.getKey(), FAILED, true);
        } else if (KnoxStatus.KNOX_OFF.equals(knoxStatus)) {
            knoxImageView.setVisible(false);
            knoxImageView.setManaged(false);
            knoxOnImageView.setVisible(false);
            knoxOnImageView.setManaged(false);
            knoxOffImageView.setVisible(true);
            knoxOffImageView.setManaged(true);
            oemVBox.setStyle("-fx-background-color: #13C784; -fx-background-radius: 3");
            updateDefectOnMainView(InitialDefectKey.KNOX.getKey(), PASSED, true);
        } else {
            return;
        }

        oemLabel.setStyle("-fx-text-fill: #FFFFFF");
        oemVBox.setDisable(false);
        showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                getUiInMemoryStore().getAssignedCloudCustomization()));

        updateAndNotifyBackendForResultsChange(false);
    }

    /**
     * Reset the device lock element when device is PEO'd
     */
    public void resetDeviceLock() {
        getDeviceLockDefaultImageView().setVisible(true);
        getDeviceLockDefaultImageView().setManaged(true);
        getDeviceUnlockedImageView().setVisible(false);
        getDeviceUnlockedImageView().setManaged(false);
        getDeviceLockedImageView().setVisible(false);
        getDeviceLockedImageView().setManaged(false);
        getDeviceLockVBox().setStyle("-fx-background-color: #F6F6F6; -fx-background-radius: 3");
        getDeviceLockLabel().setStyle("-fx-text-fill: #c0bfc3");
        getDeviceLockLabel().setText(getLocalizationService().getLanguageSpecificText("lock"));
    }

    /**
     * Set device lock status on the UI
     *
     * @param deviceLock target device lock status
     * @param lockName   target device lock name
     */
    public void enableDeviceLockImageElement(final DeviceLock deviceLock, final String lockName) {
        Runnable runnable = () -> {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Populating device lock status on the UI: {}", deviceLock);
            // If Frp or iCloud is already set as ON, then don't change it
            if (getDeviceLockedImageView().isVisible()) {
                return;
            }
            getDeviceLockLabel().setText(lockName);
            if (deviceLock == DeviceLock.ON) {
                getDeviceLockDefaultImageView().setVisible(false);
                getDeviceLockDefaultImageView().setManaged(false);
                getDeviceUnlockedImageView().setVisible(false);
                getDeviceUnlockedImageView().setManaged(false);
                getDeviceLockedImageView().setVisible(true);
                getDeviceLockedImageView().setManaged(true);
                getDeviceLockVBox().setStyle("-fx-background-color: #F41947; -fx-background-radius: 3");
                updateDefectOnMainView(InitialDefectKey.DEVICE_LOCK.getKey(), FAILED, true);
            } else if (deviceLock == DeviceLock.OFF) {
                getDeviceLockDefaultImageView().setVisible(false);
                getDeviceLockDefaultImageView().setManaged(false);
                getDeviceLockedImageView().setVisible(false);
                getDeviceLockedImageView().setManaged(false);
                getDeviceUnlockedImageView().setVisible(true);
                getDeviceUnlockedImageView().setManaged(true);
                getDeviceLockVBox().setStyle("-fx-background-color: #13C784; -fx-background-radius: 3");
                updateDefectOnMainView(InitialDefectKey.DEVICE_LOCK.getKey(), PASSED, true);
            } else {
                LOGGER.info("Invalid status of device lock");
                return;
            }
            getDeviceLockLabel().setStyle("-fx-text-fill: #FFFFFF");
            getDeviceLockVBox().setDisable(false);
            showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(getDevice(), true,
                    getUiInMemoryStore().getAssignedCloudCustomization()));
            MDC.clear();
        };
        runOnFxThread(runnable);

        updateAndNotifyBackendForResultsChange(false);
    }

    /**
     * Update battery image icon as either degraded or full
     *
     * @param batteryStateHealth battery health from syslog
     * @param isBatteryDegraded  indicates if battery is degraded or not
     */
    protected void enableBatteryImageElement(final int batteryStateHealth, final boolean isBatteryDegraded) {
        Runnable runnable = () -> {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Updating battery image icon on the UI");
            batteryVBox.setDisable(false);
            if (batteryStateHealth > 0) {
                setBatteryHealthLabels(batteryStateHealth);
            }
            batteryImageView.setVisible(false);
            batteryImageView.setManaged(false);
            if (isBatteryDegraded
                    && uiInMemoryStore.getAssignedCloudCustomization().getBatterySettings() != null
                    && uiInMemoryStore.getAssignedCloudCustomization().getBatterySettings().isAppleFail()) {
                batteryFullImageView.setVisible(false);
                batteryFullImageView.setManaged(false);
                batteryWarningImageView.setVisible(true);
                batteryWarningImageView.setManaged(true);
                batteryVBox.setStyle("-fx-background-color: #F41947; -fx-background-radius: 3");
                batteryMiniHealthLabel.setStyle("-fx-text-fill: #FFFFFF");
                updateDefectOnMainView(InitialDefectKey.BATTERY_DEGRADED.getKey(), FAILED, true);
            } else {
                batteryWarningImageView.setVisible(false);
                batteryWarningImageView.setManaged(false);
                batteryFullImageView.setVisible(true);
                batteryFullImageView.setManaged(true);
                batteryVBox.setStyle("-fx-background-color: #13C784; -fx-background-radius: 3");
                batteryMiniHealthLabel.setStyle("-fx-text-fill: #FFFFFF");
                updateDefectOnMainView(InitialDefectKey.BATTERY_DEGRADED.getKey(), PASSED, true);
            }

            showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                    getUiInMemoryStore().getAssignedCloudCustomization()));
            MDC.clear();
        };
        runOnFxThread(runnable);
    }

    /**
     * Updates the battery charging status on the main and diagnostics views.
     * If charging is false, marks "P-Charging" as failed and updates defects.
     * If charging is true, marks "P-Charging" as passed in diagnostics.
     *
     * @param isCharging {@code true} if the battery is charging, {@code false} otherwise.
     * @param percentage current percentage
     */
    public void showBatteryChargingStatusOnMainView(final Boolean isCharging, final int percentage) {
        if (isCharging == null) {
            LOGGER.info("Charging status is null no need to display Pass/Fail on UI");
            return;
        }

        final String chargingKey = formatKey(InitialDefectKey.BATTERY_CHARGING_STATUS.getKey());
        if (Boolean.TRUE.equals(isCharging) || percentage == 100) {
            if (!getPassedTestKeys().contains(chargingKey)) {
                LOGGER.info("Charging status is true display Pass in diagnostic view");
                getPassedTestKeys().add(chargingKey);
                getFailedTestKeys().remove(chargingKey);
                updateDefectOnDiagnosticsView(chargingKey, PASSED);
            }
        } else if (Boolean.FALSE.equals(isCharging)) {
            if (!getFailedTestKeys().contains(chargingKey)) {
                LOGGER.info("Charging status is false display Fail in main and diagnostic view");
                getFailedTestKeys().add(chargingKey);
                getPassedTestKeys().remove(chargingKey);
                updateDefectOnMainView(chargingKey, FAILED, false);
                updateDefectOnDiagnosticsView(chargingKey, FAILED);
            }
        }
    }

    /**
     * Update ESN image on the UI to show bad/good/na response
     *
     * @param event EsnResponseEvent
     */
    protected void enableEsnImageElement(final DeviceEsnResponseEvent event) {

        final boolean isEsnApiJ001 =
                (uiInMemoryStore.getCurrentLanguage().equalsIgnoreCase("English")
                        || uiInMemoryStore.getCurrentLanguage().equalsIgnoreCase("Japanese"))
                        && StringUtils.containsIgnoreCase(event.getEsnUIResponse(), "J001");

        if (event.getEsnStatus() == EsnStatus.ESN_GOOD) {
            esnImageView.setVisible(false);
            esnImageView.setManaged(false);
            esnBadImageView.setVisible(false);
            esnBadImageView.setManaged(false);
            esnGoodImageView.setVisible(true);
            esnGoodImageView.setManaged(true);
            esnVBox.setStyle("-fx-background-color: #13C784; -fx-background-radius: 3");
            esnLabel.setStyle("-fx-text-fill: #FFFFFF");
            esnVboxHighlight.setStyle("-fx-border-color: -fx-control-inner-background;");

            double leftMargin = 15.0;

            if (isEsnApiJ001 && event.getEsnFieldColor() == EsnFieldColor.WHITE) {
                setImageIconToImageView(DeviceBoxController.this, esnGoodImageView,
                        "imei-dash-icon.png");
                leftMargin = 12.0;
            } else if (isEsnApiJ001) {
                setImageIconToImageView(DeviceBoxController.this, esnGoodImageView,
                        "imei-circle-icon.png");
                leftMargin = 12.0;
            } else {
                setImageIconToImageView(DeviceBoxController.this, esnGoodImageView,
                        "oem-esn-icon.png");
            }
            HBox.setMargin(esnGoodImageView, new Insets(1.5, 0, 0, leftMargin));

            updateDefectOnMainView(InitialDefectKey.BAD_ESN.getKey(), PASSED, true);
        } else if (event.getEsnStatus() == EsnStatus.ESN_BAD) {
            esnImageView.setVisible(false);
            esnImageView.setManaged(false);
            esnGoodImageView.setVisible(false);
            esnGoodImageView.setManaged(false);
            esnBadImageView.setVisible(true);
            esnBadImageView.setManaged(true);
            esnVBox.setStyle("-fx-background-color: #F41947; -fx-background-radius: 3");
            esnLabel.setStyle("-fx-text-fill: #FFFFFF");
            esnVboxHighlight.setStyle("-fx-border-color: -fx-control-inner-background;");

            double leftMargin = 15.0;

            if (isEsnApiJ001) {
                setImageIconToImageView(DeviceBoxController.this, esnBadImageView,
                        "imei-cross-icon.png");
                leftMargin = 12.0;
            } else {
                setImageIconToImageView(DeviceBoxController.this, esnBadImageView,
                        "imei_fail_icon.png");
            }
            HBox.setMargin(esnBadImageView, new Insets(1.5, 0, 0, leftMargin));

            updateDefectOnMainView(InitialDefectKey.BAD_ESN.getKey(), FAILED, true);
        } else {
            esnImageView.setVisible(true);
            esnImageView.setManaged(true);
            esnGoodImageView.setVisible(false);
            esnGoodImageView.setManaged(false);
            esnBadImageView.setVisible(false);
            esnBadImageView.setManaged(false);
            esnVBox.setStyle("-fx-background-color: yellow; -fx-background-radius: 3");
            esnLabel.setStyle("-fx-text-fill: grey");

            double leftMargin = 15.0;

            if (isEsnApiJ001) {
                setImageIconToImageView(DeviceBoxController.this, esnImageView,
                        "imei-triangle-icon.png");
                leftMargin = 12.0;
            } else {
                setImageIconToImageView(DeviceBoxController.this, esnImageView,
                        "shield-icon.png");
            }
            HBox.setMargin(esnImageView, new Insets(1.5, 0, 0, leftMargin));

            updateDefectOnMainView(InitialDefectKey.BAD_ESN.getKey(), PENDING, true);
        }
        esnVBox.setDisable(false);
        showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                getUiInMemoryStore().getAssignedCloudCustomization()));
    }

    /**
     * Sets up grade combo box by setting items in it and adding change listener.
     */
    protected void setupGradeComboBox() {
        gradeComboBox.setEditable(true);

        CloudCustomizationResponse customization = uiInMemoryStore.getAssignedCloudCustomization();
        List<String> grades = (customization != null) ? customization.getGrading() : null;

        gradeComboBox.setItems(FXCollections.observableArrayList(grades != null ?
                grades : DeviceBoxSettings.DEVICE_GRADES));
        gradeComboBox.setValue(StringUtils.defaultString(customization != null ?
                customization.getDefaultGrade() : getLocalizationService().getLanguageSpecificText("grade")));
        if (customization != null) {
            // If default grade found in cloud-customizations, then set into tracker device
            if (customization.getFields() != null && StringUtils.isNotBlank(customization.getDefaultGrade())) {
                device.setGrade(customization.getDefaultGrade());
                deviceOperationHelper.requestGradeChange(device, customization.getDefaultGrade());
            }
        }
        gradeComboBox.getSelectionModel().selectedItemProperty().addListener(setDeviceGradeChangeListener());
    }

    /**
     * Sets up color combo box by setting items in it and adding change listener.
     *
     * @param deviceColors color options in which current device is available
     */
    protected void setupColorComboBox(final List<String> deviceColors) {
        try {
            colorComboBox.getItems().clear();
            CloudCustomizationResponse cloudCustomizationResponse =
                    getUiInMemoryStore().getAssignedCloudCustomization();
            boolean enableColorField = true;
            if (cloudCustomizationResponse.getFields() != null) {
                enableColorField = cloudCustomizationResponse.getFields().isAllowUserModifyColorField();
            }
            LOGGER.info("Is modifying color field enabled in customization: {}", enableColorField);

            List<String> defaultColors = DeviceBoxSettings.DEVICE_COLORS;
            if (cloudCustomizationResponse.getColors() != null && !cloudCustomizationResponse.getColors().isEmpty()) {
                defaultColors = cloudCustomizationResponse.getColors();
            }
            // Empty colors are not mis handled from cloud side so this is required on our side
            defaultColors = defaultColors.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

            if (deviceColors != null && !deviceColors.isEmpty()) {
                colorComboBox.setItems(FXCollections.observableArrayList(deviceColors));
            } else {
                colorComboBox.setItems(FXCollections.observableArrayList(defaultColors));
            }
            colorComboBox.getSelectionModel().selectedItemProperty().addListener(setDeviceColorChangeListener());
            if (!enableColorField) {
                colorComboBox.setDisable(true);
                colorLabel.setVisible(true);
                colorLabel.setManaged(true);
            } else {
                colorLabel.setVisible(false);
                colorLabel.setManaged(false);
                colorComboBox.setDisable(false);
                colorComboBox.setVisible(true);
                colorComboBox.setManaged(true);
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while setup color combobox", e);
        }
    }

    /**
     * Sets up the custom1 text field for the device
     */
    protected void setupCustom1TextField() {
        if (StringUtils.isNotEmpty(this.device.getCustom1())) {
            custom1TextField.setText(this.device.getCustom1());
        } else {
            custom1TextField.clear();
        }
    }

    /**
     * Sets up the custom1 text field listeners
     */
    protected void setupCustom1Listeners() {
        final PauseTransition pauseTransition1 = new PauseTransition(Duration.seconds(PAUSE_TRANSITION_TIME));
        custom1TextField.textProperty().addListener(setDeviceCustom1ChangeListener(pauseTransition1));

        final PauseTransition pauseTransition2 = new PauseTransition(Duration.seconds(PAUSE_TRANSITION_TIME));
        scanCustom1TextField.textProperty().addListener(setDeviceScannedCustom1ChangeListener(pauseTransition2));
        scanCustom1TextField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                onScanCustom1DoneButtonClicked();
            }
        });
    }

    /**
     * Sets up the lpn text field for the device
     */
    protected void setupLpnTextField() {
        if (StringUtils.isNotEmpty(this.device.getLpn())) {
            lpnTextField.setText(this.device.getLpn());
        } else {
            lpnTextField.clear();
        }
    }

    /**
     * Sets up the lpn text field listeners
     */
    protected void setupLpnListeners() {
        final PauseTransition pauseTransition1 = new PauseTransition(Duration.seconds(PAUSE_TRANSITION_TIME));
        lpnTextField.textProperty().addListener(setDeviceLpnChangeListener(pauseTransition1));

        final PauseTransition pauseTransition2 = new PauseTransition(Duration.seconds(PAUSE_TRANSITION_TIME));
        scanLpnTextField.textProperty().addListener(setDeviceScannedLpnChangeListener(pauseTransition2));
        scanLpnTextField.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                onScanLpnDoneButtonClicked();
            }
        });
    }

    /**
     * Sets up the comment text field listeners
     */
    protected void setupCommentListeners() {
        final PauseTransition pauseTransition = new PauseTransition(Duration.seconds(PAUSE_TRANSITION_TIME));
        commentsTextField.textProperty().addListener(setCommentsChangeListener(pauseTransition));
    }


    /**
     * Sets up the SKU Code text field for the device and add listener
     *
     * @param skuCode device sku code
     */
    protected void setSkuCodeField(final String skuCode) {
        Runnable runnable = () -> {
            skuCodeTextField.clear();
            if (StringUtils.isNotEmpty(skuCode)) {
                skuCodeTextField.setText(skuCode);
            }
            final PauseTransition pauseTransition = new PauseTransition(Duration.seconds(PAUSE_TRANSITION_TIME));
            skuCodeTextField.textProperty().addListener(setSkuCodeChangeListener(pauseTransition));
        };
        runOnFxThread(runnable);
    }

    /**
     * Sets up the comments field for the device and add listener
     */
    protected void setupCommentsTextField() {
        if (StringUtils.isNotEmpty(this.device.getNotes())) {
            commentsTextField.setText(this.device.getNotes());
        } else {
            commentsTextField.clear();
        }
    }

    /**
     * Setups Carrier combo box with the list from cloud if available or uses the static list.
     * It also sets the carrier value.
     *
     * @param carrier device carrier
     */
    protected void setupCarrierComboBox(final String carrier) {
        try {
            carrierComboBox.getItems().clear();
            carrierComboBox.setEditable(true);

            CloudCustomizationResponse customization = uiInMemoryStore.getAssignedCloudCustomization();
            // If customizations don't have list of carriers then display the hardcoded list of carriers
            List<String> carriers = customization != null && customization.getCarriers() != null ?
                    customization.getCarriers() : DeviceBoxSettings.DEVICE_CARRIERS;

            // Only update the carrier to initial carrier if there is no default carrier set in customization
            final String initialCarrier = StringUtils.isNotBlank(carrier) &&
                    !StringUtils.containsIgnoreCase(carrier, "unknown") ? carrier :
                    getLocalizationService().getLanguageSpecificText("carrier");
            String carrierToBeSet = customization != null && StringUtils.isNotBlank(customization.getDefaultCarrier())
                    ? customization.getDefaultCarrier() : initialCarrier;

            carrierComboBox.setItems(FXCollections.observableArrayList(carriers));
            carrierComboBox.getSelectionModel().selectedItemProperty().addListener(setDeviceCarrierChangeListener());
            carrierComboBox.setValue(carrierToBeSet);
            LOGGER.info("Carrier set on device box UI: {}", carrierToBeSet);
        } catch (Exception e) {
            LOGGER.error("Error occurred while setup carrier combo box", e);
        }
    }

    /**
     * Sets change listener based on value change of grade combo box
     * This listener will send MQTT message to backend with the new grade value of the device
     *
     * @return ChangeListener
     */
    protected ChangeListener<String> setDeviceGradeChangeListener() {
        return (observable, oldGrade, newGrade) -> {
            if (newGrade != null) {
                if (!newGrade.equals(oldGrade)) {
                    if (null != this.device) {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Grade changed from {} to {}", oldGrade, newGrade);
                        gradeComboBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        device.setGrade(newGrade);
                        deviceOperationHelper.requestGradeChange(device, newGrade);
                    }
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of color combo box
     * This listener will send MQTT message to backend with the new color value of the device
     *
     * @return ChangeListener
     */
    protected ChangeListener<String> setDeviceColorChangeListener() {
        return (observable, oldColor, newColor) -> {
            if (newColor != null) {
                if (!newColor.equals(oldColor)) {
                    if (null != this.device) {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Color changed from {} to {}", oldColor, newColor);
                        colorComboBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        if (newColor.equalsIgnoreCase(
                                getLocalizationService().getLanguageSpecificText("color"))) {
                            device.setColor("");
                            colorLabel.setText("");
                        } else {
                            device.setColor(newColor);
                            colorLabel.setText(newColor);
                            deviceOperationHelper.requestColorChange(device, newColor);
                        }
                    }
                }
            }
        };
    }

    /**
     * Sets change listener based on value change of carrier combo box
     * This listener will send MQTT message to backend with the new carrier value of the device
     *
     * @return ChangeListener
     */
    protected ChangeListener<String> setDeviceCarrierChangeListener() {
        return (observable, oldCarrier, newCarrier) -> {
            if (newCarrier != null) {
                if (!newCarrier.equals(oldCarrier)) {
                    if (null != this.device) {
                        setDeviceIdMDC(this.device.getId());
                        LOGGER.info("Carrier changed from {} to {}", oldCarrier, newCarrier);
                        carrierComboBox.setStyle("-fx-border-color: black;-fx-border-width: 0px;");
                        if (newCarrier.equalsIgnoreCase(
                                getLocalizationService().getLanguageSpecificText("carrier"))) {
                            device.setCarrier("");
                        } else {
                            device.setCarrier(newCarrier);
                            deviceOperationHelper.requestCarrierChange(device, newCarrier);
                        }
                    }
                }
            }
        };
    }

    /**
     * Update drain info (drain percentage, drain duration, drain result)
     * on battery info pop-up
     *
     * @param batteryDrainSummary Battery Drain Info of device
     **/
    private void updateBatteryDrainResultsOnView(final BatteryDrainSummary batteryDrainSummary) {
        getBatteryDrainPercentageLabel().setText(StringUtils.isBlank(batteryDrainSummary.getTotalDischarge()) ? "0%" :
                StringUtils.contains(batteryDrainSummary.getTotalDischarge(), "%") ?
                        batteryDrainSummary.getTotalDischarge() : batteryDrainSummary.getTotalDischarge() + "%");
        getBatteryDrainDurationLabel().setText(batteryDrainSummary.getTotalDuration());
        if (StringUtils.isNotBlank(batteryDrainSummary.getTotalDischarge()) &&
                Integer.parseInt(batteryDrainSummary.getTotalDischarge().replace("%", "")) >
                        getUiInMemoryStore().getAssignedCloudCustomization()
                                .getBatterySettings().getFailDrainIfDischargePercentage()) {
            getBatteryDrainResultLabel().setText(getLocalizationService()
                    .getLanguageSpecificText(FAILED.toLowerCase()));
            updateBatteryElementIcon(FAILED);
        } else {
            getBatteryDrainResultLabel().setText(getLocalizationService()
                    .getLanguageSpecificText(PASSED.toLowerCase()));
            updateBatteryElementIcon(PASSED);
        }
    }

    /**
     * Update Battery Icon on device box according to Battery drain Test
     *
     * @param batteryDrainResultStatus Battery Drain Passed or failed
     **/
    private void updateBatteryElementIcon(final String batteryDrainResultStatus) {
        Runnable runnable = () -> {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Updating battery element on UI");
            if (batteryDrainResultStatus.equals(FAILED)) {
                batteryFullImageView.setVisible(false);
                batteryFullImageView.setManaged(false);
                batteryWarningImageView.setVisible(true);
                batteryWarningImageView.setManaged(true);
                batteryVBox.setStyle("-fx-background-color: #F41947 ; -fx-background-radius: 3");
                batteryMiniHealthLabel.setStyle("-fx-text-fill: #FFFFFF");
            } else {
                if (getDevice().getBatteryDegraded() != null && !getDevice().getBatteryDegraded()) {
                    batteryWarningImageView.setVisible(false);
                    batteryWarningImageView.setManaged(false);
                    batteryFullImageView.setVisible(true);
                    batteryFullImageView.setManaged(true);
                    batteryVBox.setStyle("-fx-background-color: #13C784; -fx-background-radius: 3");
                    batteryMiniHealthLabel.setStyle("-fx-text-fill: #FFFFFF");
                }
            }
            MDC.clear();
        };
        runOnFxThread(runnable);
    }

    // ==================================================================
    //                         Events Listeners
    // ==================================================================

    /**
     * Business logic to execute after an error is raised
     *
     * @param event NotifyErrorEvent
     */
    public void onNotifyError(final DeviceNotifyErrorEvent event) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            final IosDevice device = (IosDevice) event.getDevice();
            String errorMsg = getLocalizationService().getLanguageSpecificText(event.getErrorMsg());

            final String localizedErrorMsg = StringUtils.isBlank(errorMsg) ? event.getErrorMsg() : errorMsg;
            LOGGER.info("Error on device processing: {}", errorMsg);

            if (this.device.getId().equalsIgnoreCase(device.getId())) {
                this.device.setStage(device.getStage());

                Runnable runnable = () -> {
                    if (!this.device.isEraseInProgress() || !this.device.isRestoreInProgress()
                            || !this.device.isShutdownInProgress()) {
                        showDeviceSubStatusLabel(true);
                        setDeviceSubStatusLabel(localizedErrorMsg);
                    }
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Business logic to execute on esim-only device status
     *
     * @param isDeviceEsimOnly
     */
    public void enableEsimOnlyStatusOnUi(final boolean isDeviceEsimOnly) {
        if (null != this.device) {
            if (this.device.getId().equalsIgnoreCase(device.getId())) {
                if (isDeviceEsimOnly) {
                    updateTestStatus(TestResultConstants.ESIM_ONLY, false);
                } else {
                    updateTestStatus(TestResultConstants.ESIM_ONLY, true);
                }
            }
        }
    }

    /**
     * Show ESN response on the device box
     *
     * @param event EsnResponseEvent
     */
    public void onEsnResponse(final DeviceEsnResponseEvent event) {
        if (null != this.device) {
            final Device eventDevice = event.getDevice();

            this.device.setEsnRawResponse(event.getEsnUIResponse());
            this.device.setEsnStatus(event.getEsnStatus().getKey());

            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Device received ESN response {}", event.getEsnUIResponse());
            if (this.device.getId().equalsIgnoreCase(eventDevice.getId())) {
                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Populating ESN response on the UI");
                    enableEsnImageElement(event);
                    setWorkFlowStepInfo(DeviceStage.ESN_SUCCESS);
                    updateDeviceSubStatusOnUI(DeviceStage.ESN_SUCCESS);
                    updateAndNotifyBackendForResultsChange(false);
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Updates Ui for required fields
     *
     * @param event DeviceRequiredFieldsResponseEvent
     */
    public void onRequiredFieldsResponse(final DeviceRequiredFieldsResponseEvent event) {
        if (null != getDevice()) {
            setDeviceIdMDC(getDevice().getId());
            setUnpopulatedRequiredFieldsList(event.getRequiredFieldsList());
            LOGGER.info("Device required fields response received for fields: {}", event.getRequiredFieldsList());
            updateRequiredFieldsElements(event.isFromErase());
        }
    }

    /**
     * Business logic to execute on app testing done on device.
     *
     * @param event AppTestingDoneEvent
     */
    public void onAppTestingDone(final DeviceAppTestingDoneEvent event) {
        if (null != this.device) {
            final Device eventDevice = event.getDevice();
            if (this.device.getId().equalsIgnoreCase(eventDevice.getId())) {
                setDeviceIdMDC(this.device.getId());
                LOGGER.info("Device tests available");
                this.device.setStage(eventDevice.getStage());
                this.device.setVendorName(eventDevice.getVendorName());
                this.device.setDeviceTestResult(eventDevice.getDeviceTestResult());
                // also save grade in the grade field
                if (eventDevice.getDeviceTestResult() != null) {
                    this.device.setGrade(eventDevice.getDeviceTestResult().getGradeResults());
                }
                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Populating Test results on the UI");
                    defectsFlowPane.getChildren().clear();
                    testsFlowPane.getChildren().clear();

                    verifyAndUpdatePanicFullResult(eventDevice);

                    // only keep the fail device reasons in the list failedTestKeys
                    failedTestKeys.retainAll(FAIL_DEVICE_REASONS);
                    List<String> failDeviceReasons = new ArrayList<>(failedTestKeys);
                    passedTestKeys.clear();

                    List<String> japaneseConformityMarkKey = Arrays.asList(
                            getLocalizationService().getLanguageSpecificText(InitialDefectKey
                                    .JAPANESE_CONFORMITY_MARK.getKey()),
                            getLocalizationService().getLanguageSpecificText(InitialDefectKey
                                    .MANUAL_JAPANESE_CONFORMITY_MARK.getKey())
                    );

                    List<String> initialDefectKeys = Arrays.stream(InitialDefectKey.values())
                            .map(defectKey -> getLocalizationService().getLanguageSpecificText(defectKey.getKey()))
                            .toList();
                    final CloudCustomizationResponse customization = uiInMemoryStore.getAssignedCloudCustomization();
                    List<String> initialDefectKeysAfterCustomization = new ArrayList<>(initialDefectKeys);
                    if (customization.getBatterySettings() != null
                            && !customization.getBatterySettings().isAppleFail()) {
                        initialDefectKeysAfterCustomization.remove(InitialDefectKey.BATTERY_DEGRADED.getKey());
                    }

                    if (eventDevice.getDeviceTestResult().getTestResults() != null) {
                        List<String> failedTestResults = eventDevice.getDeviceTestResult().getTestResults().getFailed();
                        List<String> passedTestResults = eventDevice.getDeviceTestResult().getTestResults().getPassed();

                        List<String> combinedResults = new ArrayList<>(failedTestResults);
                        combinedResults.addAll(passedTestResults);

                        failDeviceReasonsCopy.retainAll(combinedResults);

                        /*
                         Based on cosmetics test as Q&A or not, cosmetics, failed cosmetic results,
                         passed cosmetic results are being fetched
                         */
                        Triple<String, String, String> cosmeticFailedPassedResult =
                                DeviceCosmeticResultsUtil.getAllCosmeticTestResults(eventDevice.getDeviceTestResult(),
                                        customization);

                        if (cosmeticFailedPassedResult != null) {
                            /*
                            if the cosmetic is Q&A and customisation is to save failed cosmetic results
                            to the failed test results, append the failed cosmetic results to the failed test results.
                             */
                            if (StringUtils.isNotBlank(cosmeticFailedPassedResult.getMiddle())) {
                                failedCosmeticTestKeys.clear();
                                failedCosmeticTestKeys.addAll(Arrays.asList(cosmeticFailedPassedResult.getMiddle().
                                        split(",")));
                            } else {
                                failedCosmeticTestKeys.clear();
                            }
                            /*
                            if the cosmetic is Q&A and customisation is to save passed cosmetic results
                            to the passed test results, append the passed cosmetic results to the passed test results.
                             */
                            if (StringUtils.isNotBlank(cosmeticFailedPassedResult.getRight())) {
                                passedCosmeticTestKeys.clear();
                                passedCosmeticTestKeys.addAll(Arrays.asList(cosmeticFailedPassedResult.getRight().
                                        split(",")));
                            } else {
                                passedCosmeticTestKeys.clear();
                            }
                        }

                        if (failedTestResults != null && !failedTestResults.isEmpty()) {
                            for (String key : failedTestResults) {
                                key = key.trim();
                                if (StringUtils.isBlank(key) || key.equalsIgnoreCase("M-")) {
                                    continue;
                                }
                                //failed test keys might also have fail device reasons
                                if (FAIL_DEVICE_REASONS.contains("M-" + key)) {
                                    failDeviceReasons.remove("M-" + key);
                                    failDeviceReasons.add(key);
                                    if (failedTestKeys.contains("M-" + key)) {
                                        failedTestKeys.remove("M-" + key);
                                    }
                                    if (!failedTestKeys.contains(key)) {
                                        failedTestKeys.add(key);
                                    }
                                    continue;
                                }

                                if (!failedTestKeys.contains(key)) {
                                    failedTestKeys.add(key);
                                }

                                if (initialDefectKeysAfterCustomization.contains(key)) {
                                    if (japaneseConformityMarkKey.contains(key)) {
                                        key = key.contains(getLocalizationService().getLanguageSpecificText(
                                                InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey()))
                                                ? InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey()
                                                : InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey();
                                        updateDefectOnDiagnosticsView(key, FAILED);
                                        updateDefectOnMainView(key, FAILED, true);
                                    } else {
                                        if (failedTestKeys.contains(InitialDefectKey.MANUAL_FACE_ID.getKey()) ||
                                                failedTestKeys.contains(InitialDefectKey.FACE_ID.getKey())) {
                                            getFaceIdStatus().setTextFill(Color.RED);
                                        }
                                        updateDefectOnMainView(key, FAILED, true);
                                        updateDefectOnDiagnosticsView(key, FAILED);
                                    }
                                } else if (!initialDefectKeys.contains(key)) {
                                    updateDefectOnMainView(key, FAILED, false);
                                    updateDefectOnDiagnosticsView(key, FAILED);
                                }
                            }
                        }

                        if (passedTestResults != null && !passedTestResults.isEmpty()) {
                            for (String key : passedTestResults) {
                                key = key.trim();
                                if (StringUtils.isBlank(key) || key.equalsIgnoreCase("M-")) {
                                    continue;
                                }

                                if (FAIL_DEVICE_REASONS.contains("M-" + key)) {
                                    if (failDeviceReasons.remove("M-" + key)) {
                                        LOGGER.info("{} was previously failed manually and now " +
                                                "it's passed from the device", key);
                                    }
                                    if (failedTestKeys.remove("M-" + key)) {
                                        LOGGER.info("{} key is removed from failed test keys", key);
                                    }
                                }
                                if (!passedTestKeys.contains(key)) {
                                    passedTestKeys.add(key);
                                }

                                if (initialDefectKeysAfterCustomization.contains(key)) {
                                    if (japaneseConformityMarkKey.contains(key)) {
                                        key = key.contains(getLocalizationService().getLanguageSpecificText(
                                                InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK
                                                        .getKey()))
                                                ? InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey()
                                                : InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey();
                                        updateDefectOnDiagnosticsView(key, PASSED);
                                        updateDefectOnMainView(key, PASSED, true);
                                    } else {
                                        if (passedTestKeys.contains(InitialDefectKey.MANUAL_FACE_ID.getKey()) ||
                                                passedTestKeys.contains(InitialDefectKey.FACE_ID.getKey())) {
                                            getFaceIdStatus().setTextFill(Color.GREEN);
                                        }
                                        updateDefectOnMainView(key, PASSED, true);
                                        updateDefectOnDiagnosticsView(key, PASSED);
                                    }
                                } else if (!initialDefectKeys.contains(key)) {
                                    updateDefectOnMainView(key, PASSED, false);
                                    updateDefectOnDiagnosticsView(key, PASSED);
                                }
                            }
                        }

                        // display failed cosmetics on the main/diagnostic view based on customisation
                        for (String key : failedCosmeticTestKeys) {
                            updateDefectOnMainView(key, FAILED, false);
                            updateDefectOnDiagnosticsView(key, FAILED);
                        }
                        // display passed cosmetics on the main/diagnostic view based on customisation
                        for (String key : passedCosmeticTestKeys) {
                            updateDefectOnMainView(key, PASSED, false);
                            updateDefectOnDiagnosticsView(key, PASSED);
                        }
                    }

                    // display fail device reasons on ui
                    for (String key : failDeviceReasons) {
                        updateDefectOnMainView(key, FAILED, false);
                        if (failDeviceReasonsCopy.contains(key)) {
                            updateDefectOnDiagnosticsView(key, FAILED);
                        }
                    }

                    updateFailedDeviceValues();
                    /*
                      On app testing done, the esim detected icon on the UI will be removed as
                      we reset all the test keys, defectsFlowPane and testFlowPane and re-initialize them.
                      So, we have to re-calculate and display the icon on the UI.
                    */
                    if (device instanceof AndroidDevice) {
                        LOGGER.info("On app testing done, verify if esim defect icon to be displayed.");
                        verifyAndUpdateESimDetectedStatus((AndroidDevice) device);
                    }

                    // update the test results in the backend again if there is any fail device reasons
                    updateAndNotifyBackendForResultsChange(false);

                    gradeComboBox.setEditable(true);
                    if (StringUtils.isNotBlank(this.device.getGrade())) {
                        gradeComboBox.setValue(this.device.getGrade());
                    }
                    showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(getDevice(), true,
                            getUiInMemoryStore().getAssignedCloudCustomization()));

                    // We will only show the "App Testing Done" status on device box if device is not erasing and
                    // device is ready to test
                    if (!this.device.isEraseInProgress() || !this.device.isRestoreInProgress()) {
                        Node node = workFlowInfoVBox.lookup("#" + DeviceStage.READY + "_ID");
                        if (node instanceof Label label) {
                            if (label.isVisible()) {
                                setDeviceStatusLabel(eventDevice.getStage().getLocalizedKey());
                                setWorkFlowStepInfo(eventDevice.getStage());
                                updateDeviceSubStatusOnUI(eventDevice.getStage());
                            }
                        }
                    }
                    if (eventDevice.getDeviceTestResult().getBatteryResults() != null) {
                        if (eventDevice.getDeviceTestResult().getBatteryResults().getBatteryDrain() != null) {
                            updateBatteryDrainResultsOnView(eventDevice.getDeviceTestResult()
                                    .getBatteryResults().getBatteryDrain());
                        }
                    }
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Logic to display auto export status on the device box
     *
     * @param event AutoExportStatusEvent
     */
    public void onAutoExportStatus(final AutoExportStatusEvent event) {
        if (null != getDevice()) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Device auto export status: {}", event.getStatus());
                Runnable runnable = () -> {
                    if (!this.device.isEraseInProgress() || !this.device.isRestoreInProgress()
                            || !this.device.isShutdownInProgress()) {
                        showDeviceSubStatusLabel(true);
                        setDeviceSubStatusLabel(event.getStatus());
                    }
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Business logic to execute on device meid info collection succeeded
     *
     * @param event DeviceMeidInfoEvent
     */
    public void onMeidInfoCollectionSucceeded(final DeviceMeidInfoEvent event) {
        cancelReconnectDevice();
        if (null != getDevice()) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Device meid info successfully collected");
                device.setStage(eventDevice.getStage());
                device.setId(eventDevice.getId());
                device.setMeid(eventDevice.getMeid());
                device.setMeid2(eventDevice.getMeid2());
                device.setMeidDecimal(eventDevice.getMeidDecimal());
                device.setMeidDecimal2(eventDevice.getMeidDecimal2());
                device.setPesn(eventDevice.getPesn());
                device.setPesn2(eventDevice.getPesn2());
            }
        }
    }

    /**
     * Business logic to execute on device battery info collection succeeded
     *
     * @param event DeviceBatteryInfoSuccessEvent
     */
    public void onBatteryInfoCollectionSucceeded(final DeviceBatteryInfoSuccessEvent event) {
        cancelReconnectDevice();
        if (null != getDevice()) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Device Battery info successfully collected");
                device.setStage(eventDevice.getStage());
                device.setBatteryInfo(eventDevice.getBatteryInfo());
                device.setBatteryPercentage(eventDevice.getBatteryPercentage());

                int batteryHealth = eventDevice.getBatteryStateHealth() != 0 ?
                        eventDevice.getBatteryStateHealth() :
                        eventDevice.getBatteryInfo().getHealthPercentage();
                Boolean isCharging = eventDevice.getBatteryInfo().getIsCharging();
                int batteryChargePercentage = eventDevice.getBatteryInfo().getBatteryPercentage();
                device.setBatteryStateHealth(batteryHealth);

                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Updating Battery info on the UI");

                    setBatteryHealthLabels(batteryHealth);

                    getBatteryChargeLabel().setText(eventDevice.getBatteryPercentage() + "%");
                    getBatteryCycleLabel().setText(String.valueOf(eventDevice.getBatteryInfo().getCycle()));
                    getBatteryDesignedCapacityLabel().setText(String
                            .valueOf(eventDevice.getBatteryInfo().getDesignedCapacity()));
                    getBatteryCurrentCapacityLabel().setText(String
                            .valueOf(eventDevice.getBatteryInfo().getCurrentCapacity()));
                    getBatterySerialLabel().setText(eventDevice.getBatteryInfo().getSerial());
                    getBatteryModelLabel().setText(eventDevice.getBatteryInfo().getModel());
                    getBatteryResistanceLabel().setText(eventDevice.getBatteryInfo().getBatteryResistance() + "Ω");

                    // Update Ui elements for battery info if batteryDegraded
                    if (device.getBatteryDegraded() == null) {

                        boolean batteryDegraded;

                        if (DeviceFamily.IOS == device.getDeviceFamily()) {
                            batteryDegraded = FunctionalityStatusUtil
                                    .isBatteryDegraded(device, getUiInMemoryStore().getAssignedCloudCustomization());
                        } else {
                            batteryDegraded = Boolean.TRUE.equals(eventDevice.getBatteryDegraded());
                        }

                        device.setBatteryDegraded(batteryDegraded);
                        enableBatteryImageElement(batteryHealth, batteryDegraded);
                        updateAndNotifyBackendForResultsChange(false);
                    }
                    if (eventDevice.getBatteryDegraded() != null && eventDevice.getBatteryDegraded()) {
                        enableBatteryImageElement(batteryHealth, true);
                    }

                    if (eventDevice.getBatteryInfo() != null) {
                        showBatteryChargingStatusOnMainView(isCharging, batteryChargePercentage);
                    }

                    LOGGER.info("Getting battery health criteria if any");
                    InitialDefectKey batteryHealthCriteria = FunctionalityStatusUtil
                            .getBatteryHealthCriteria(device,
                                    getUiInMemoryStore().getAssignedCloudCustomization(),
                                    true);

                    clearBatteryDefects();
                    if (batteryHealthCriteria != null) {
                        updateDefectOnMainView(batteryHealthCriteria.getKey(), FAILED, true);
                        updateAndNotifyBackendForResultsChange(false);
                    }

                    setWorkFlowStepInfo(eventDevice.getStage());
                    String localizedText = getLocalizationService().getLanguageSpecificText(
                            DeviceStage.READY.getLocalizedKey());
                    if (!localizedText.equals(getDeviceStatusLabel().getText())) {
                        updateDeviceSubStatusOnUI(eventDevice.getStage());
                    }
                    MDC.clear();
                };

                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Business logic to execute on device battery info collection failed
     *
     * @param event DeviceBatteryInfoFailureEvent
     */
    public void onBatteryInfoCollectionFailed(final DeviceBatteryInfoFailureEvent event) {
        if (null != getDevice()) {
            setDeviceIdMDC(getDevice().getId());
            LOGGER.info("Device Battery info collection failed");
            updateCurrentStageOnUI(event.getDevice());
        }
    }

    /**
     * Notifies the UI that the device is powered off
     *
     * @param event DevicePowerOffResponseEvent
     */
    public void onPowerOffResponse(final DevicePowerOffResponseEvent event) {
        if (null != getDevice()) {
            setDeviceIdMDC(getDevice().getId());
            getDevice().setShutdownInProgress(true);
            LOGGER.info("Device shutting down");
            updateCurrentStageOnUI(event.getDevice());
        }
    }

    /**
     * Handles the response event triggered after attempting to uninstall an app on the device.
     *
     * @param event The DeviceUninstallAppResponseEvent representing the response event after
     *              attempting to uninstall an app.
     */
    public void onUninstallAppResponse(final DeviceUninstallAppResponseEvent event) {
        if (null != getDevice()) {
            setDeviceIdMDC(getDevice().getId());
            LOGGER.info("Uninstall app response");
            updateCurrentStageOnUI(event.getDevice());
        }
    }

    /**
     * Handles the response from an automation print label event
     * Sets the device serial in the MDC,
     * and enqueues print-operation in the label-save-service
     *
     * @param event The automation print label event containing the print operation.
     */
    public void onAutomationPrintLabelResponse(final AutoPrintLabelEvent event) {
        setDeviceIdMDC(getDevice().getId());
        final PrintOperation printOperation = event.getPrintOperation();
        final Device device = printOperation.getDevice();
        setDeviceFieldsForLabelPrint(uiInMemoryStore, device);

        LOGGER.info("Enqueuing saving label image request through automation for Label: {}",
                printOperation.getLabelName());
        getLabelSaveService().enqueueSaveLabelImageOperation(event.getPrintOperation());
    }

    /**
     * Action taken on receiving a carrier response
     *
     * @param event CarrierCheckResponseEvent
     */
    public void onCarrierCheckResponse(final DeviceCarrierCheckResponseEvent event) {
        if (null != this.device) {
            if (device.getDeviceType().equals(DeviceType.ANDROID)) {
                AndroidDevice androidDevice = (AndroidDevice) getDevice();

                setDeviceIdMDC(androidDevice.getId());
                LOGGER.info("Device carrier check response received");
                if (androidDevice.getId().equalsIgnoreCase(event.getDevice().getId())) {
                    final String carrier = event.getCarrier();
                    if (ObjectUtils.isNotEmpty(carrier)) {
                        androidDevice.setCarrier(event.getCarrier());
                        if (androidDevice.getSimLock() == null) {
                            androidDevice.setSimLock(event.isSimLocked());
                        }

                        Runnable runnable = () -> {
                            // if we get the unlocked carrier then display unlocked
                            // else display the carrier name
                            setDeviceIdMDC(this.device.getId());
                            LOGGER.info("Updating carrier on android device UI with value: {}", event.getCarrier());
                            if (StringUtils.isNotBlank(event.getCarrier())) {
                                getCarrierComboBox().setValue(event.getCarrier());
                            }

                            if (androidDevice.getSimLock() != null) {
                                boolean simLock = androidDevice.getSimLock();
                                getNetworkLockStatusHighlight()
                                        .setStyle("-fx-border-color: -fx-control-inner-background;");
                                getNetworkLockStatusOn().setVisible(simLock);
                                getNetworkLockStatusOff().setVisible(!simLock);
                                getNetworkLockStatus().setVisible(false);
                            }
                            MDC.clear();
                        };
                        runOnFxThread(runnable);
                    }
                }
            } else {
                IosDevice iosDevice = (IosDevice) getDevice();

                setDeviceIdMDC(iosDevice.getId());
                LOGGER.info("Device carrier check response received");

                if (iosDevice.getId().equalsIgnoreCase(event.getDevice().getId())) {
                    final CarrierSimLockStatusResponse.RawResponse rawResponse = event.getCarrierResponse();

                    if (StringUtils.isNotBlank(rawResponse.getResult())) {
                        // save the response to display in a pop-up
                        setCarrierSimLockResponse(rawResponse.getResult());
                    }

                    if (ObjectUtils.isNotEmpty(rawResponse.getData())) {
                        iosDevice.setCarrier(event.getCarrier());
                        iosDevice.setSimLock(event.isSimLocked());

                        Runnable runnable = () -> {
                            // if we get the unlocked carrier then display unlocked
                            // else display the carrier name
                            setDeviceIdMDC(this.device.getId());
                            LOGGER.info("Updating carrier on ios device UI with value: {}", event.getCarrier());
                            if (StringUtils.isNotBlank(event.getCarrier())) {
                                getCarrierComboBox().setValue(event.getCarrier());
                            }

                            getNetworkLockStatusHighlight().setStyle("-fx-border-color: -fx-control-inner-background;");
                            getNetworkLockStatusOn().setVisible(event.isSimLocked());
                            getNetworkLockStatusOff().setVisible(!event.isSimLocked()
                                    && !event.isErrorServerResponse());
                            getNetworkLockStatus().setVisible(false);
                            getNetworkServerError().setVisible(event.isErrorServerResponse());
                            MDC.clear();
                        };
                        runOnFxThread(runnable);
                    }
                }
            }
        }
    }

    /**
     * Logic to execute after device starts restore or reconnects after a successful restore.
     *
     * @param event DeviceRestoreResponseEvent
     */
    public void onDeviceRestoreResponse(final IosDeviceRestoreResponseEvent event) {
        if (null != getDevice()) {
            Device device = getDevice();

            setDeviceIdMDC(getDevice().getId());
            LOGGER.info("Restore state:{}, message:{}, attempt:{}", event.getRestoreStatus(), event.getResponseCode(),
                    event.getAttempt());

            LOGGER.info("Device serial :{}", device.getSerial());

            final Device eventDevice = event.getDevice();
            boolean isSameDevice = device.getId().equalsIgnoreCase(eventDevice.getId()) ||
                    (StringUtils.isNotBlank(device.getSerial()) &&
                            device.getSerial().equals(event.getDevice().getSerial()));

            if (isSameDevice) {
                device.setStage(eventDevice.getStage());
                device.setRestoreEndTime(eventDevice.getRestoreEndTime());

                DeviceRestoreStatus restoreStatus = event.getRestoreStatus();

                device.setRestoreStatus(restoreStatus);
                device.setIsErasePerformed(eventDevice.getIsErasePerformed());

                String localeMsg = getLocalizationService().getLanguageSpecificText(
                        event.getResponseCode().getLocalizedKey());

                final String message = event.getResponseCode() == RestoreResponseCode.TRYING_NEXT_ATTEMPT ?
                        localeMsg + event.getAttempt() : localeMsg;

                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Updating restore response on the UI with restore status :{}", restoreStatus);
                    if (DeviceRestoreStatus.RESTORE_INITIATED.equals(restoreStatus)
                            || DeviceRestoreStatus.RESTORE_IN_PROGRESS.equals(restoreStatus)) {
                        // if device was ready, reset the green title background
                        getPortNumberLabel().setStyle("-fx-text-fill: white");
                        getDeviceTitleLabel().setStyle("-fx-text-fill: white");
                        getDeviceTitleAnchor().setStyle("-fx-background-color: " +
                                "linear-gradient(from 41px 44px to 50px 50px, " +
                                "reflect, #13C784 30%, #28B463 47%); " +
                                "-fx-background-radius: 8 8 0 0");

                        String statusLabel = getLocalizationService().getLanguageSpecificText(restoreStatus
                                .getLocalizedKey());
                        setDeviceStatusLabel(statusLabel);
                        setDeviceSubStatusLabel(message);
                        deviceSubStatusLabel.setVisible(true);
                        device.setRestoreInProgress(true);
                        setWorkFlowStepInfo(DeviceStage.RESTORE_IN_PROGRESS);
                        getEraseIconImageView().setVisible(true);
                        getEraseIconImageView().setManaged(true);
                        getEraseTimerText().setStyle("-fx-text-fill: #000000");
                        getEraseTimeLabel().setText(getLocalizationService().getLanguageSpecificText("restoring"));
                        getEraseTimeLabel().setStyle("-fx-text-fill: #000000");
                        getEraseCompletedImageView().setVisible(false);
                        getEraseCompletedImageView().setManaged(false);

                        if (getTimerUtil() == null) {
                            // Setup and start restore timer
                            setTimerUtil(new TimerUtil(
                                    // on Update
                                    () -> runOnFxThread(() ->
                                            getEraseTimerText().setText(getTimerUtil().getTimeString())),
                                    // on Stop after Timeout
                                    () -> runOnFxThread(() -> {
                                        setDeviceStatusLabel(DISCONNECTED_DURING_RESTORE);
                                        getPortNumberLabel().setStyle("-fx-text-fill: white");
                                        getDeviceTitleAnchor().setStyle("-fx-background-color: #13C784; " +
                                                "-fx-background-radius: 8 8 0 0");
                                    }), RESTORE_TIME_OUT, false
                            ));

                            getEraserTimerHBox().setVisible(true);
                            getEraseTimerText().setText("00:00");
                            getTimerUtil().start();
                        }
                    } else if (DeviceRestoreStatus.RESTORE_SUCCESS_RECEIVED.equals(restoreStatus)) {
                        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("restoreSuccessful"));
                        setDeviceSubStatusLabel(message);
                        deviceSubStatusLabel.setVisible(true);
                        setWorkFlowStepInfo(DeviceStage.RESTORE_IN_PROGRESS);
                        deviceTitleLabel.setStyle("-fx-text-fill: white");
                        getEraseTimerText().setStyle("-fx-text-fill: #13C784");
                        getEraseTimeLabel().setText(getLocalizationService().
                                getLanguageSpecificText("restoreSuccess"));
                        getEraseTimeLabel().setStyle("-fx-text-fill: #13C784");
                        getPortNumberLabel().setStyle("-fx-text-fill: white");
                        getDeviceTitleAnchor()
                                .setStyle("-fx-background-color: #13C784; -fx-background-radius: 8 8 0 0");
                        getEraseIconImageView().setVisible(false);
                        getEraseIconImageView().setManaged(false);
                        getEraseCompletedImageView().setVisible(true);
                        getEraseCompletedImageView().setManaged(true);
                        if (getTimerUtil() != null) {
                            getTimerUtil().stop();
                        }

                        String title = eventDevice.getDiskSize() == null ?
                                String.format("%s", eventDevice.getModel()) :
                                String.format("%s (%s)", eventDevice.getModel(), eventDevice.getDiskSize());

                        getDeviceTitleLabel().setText(title);
                        clearDefectsForKey("dataVerificationMsg");

                    } else if (DeviceRestoreStatus.RESTORE_COMPLETED.equals(restoreStatus)) {
                        device.setRestoreInProgress(false);
                        setWorkFlowStepInfo(DeviceStage.RESTORE_COMPLETED);
                        setDeviceSubStatusLabel(message);
                        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("restoreCompleted"));
                        if (getTimerUtil() != null) {
                            getTimerUtil().stop();
                        }
                    } else if (DeviceRestoreStatus.RESTORE_DEVICE_DISCONNECTED.equals(restoreStatus)) {
                        device.setRestoreInProgress(false);
                        getRestoreErrorMessage().setText(message);
                        getRestoreErrorVBox().setVisible(true);
                        setDeviceSubStatusLabel(message);
                        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("deviceDisconnected"));
                        getEraseTimerText().setText("");
                        deviceSubStatusLabel.setVisible(true);
                        if (getTimerUtil() != null) {
                            getTimerUtil().stop();
                        }
                    } else if (DeviceRestoreStatus.DEVICE_OS_UPDATED == restoreStatus) {
                        device.setRestoreInProgress(false);
                        setDeviceSubStatusLabel(message);
                        deviceSubStatusLabel.setVisible(true);
                        setDeviceStatusLabel(getLocalizationService()
                                .getLanguageSpecificText(restoreStatus.getLocalizedKey()));
                        getEraseTimerText().setText("");
                        if (getTimerUtil() != null) {
                            getTimerUtil().stop();
                        }
                    } else if (DeviceRestoreStatus.RESTORE_FAILED == restoreStatus) {
                        getPortNumberLabel().setStyle("-fx-text-fill: white");
                        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("restoreFailed"));

                        setDeviceSubStatusLabel(message);
                        deviceSubStatusLabel.setVisible(true);
                        device.setRestoreInProgress(false);
                        getDeviceTitleAnchor()
                                .setStyle("-fx-background-color: #13C784; -fx-background-radius: 8 8 0 0");
                        if (getTimerUtil() != null) {
                            getTimerUtil().stop();
                        }
                        getEraserTimerHBox().setVisible(false);
                        getEraseTimerText().setText("");
                        getEraseTimeLabel().setText("");
                        getRestoreErrorMessage().setVisible(true);
                        getRestoreErrorMessage().setText(message);
                    } else {
                        String statusLabel = getLocalizationService().getLanguageSpecificText(restoreStatus
                                .getLocalizedKey());
                        statusLabel = (restoreStatus == DeviceRestoreStatus.RESTORE_FAILED_INTERMITTENT) ? statusLabel
                                .concat(String.valueOf(event.getAttempt())) : statusLabel;

                        setDeviceStatusLabel(statusLabel);
                        setDeviceSubStatusLabel(message);
                        deviceSubStatusLabel.setVisible(true);
                        device.setRestoreInProgress(true);
                        setWorkFlowStepInfo(DeviceStage.RESTORE_IN_PROGRESS);
                        if (getTimerUtil() != null) {
                            getEraseIconImageView().setVisible(true);
                            getEraseIconImageView().setManaged(true);
                            getEraserTimerHBox().setVisible(true);
                            getEraseTimerText().setVisible(true);
                        }
                    }
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Method to make pop-up visible for Custom1 text field
     *
     * @param event DeviceScanCustom1RequestEvent from Backend
     **/
    public void onScanCustom1Request(final DeviceScanCustom1RequestEvent event) {
        if (getDevice() != null) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Set device scan custom1 view visible");
                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Showing Scan Custom1 view on the UI");
                    getScanCustom1View().setVisible(true);
                    getScanCustom1View().setManaged(true);
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Method to make pop-up visible for LPN text field
     *
     * @param event DeviceScanLpnRequestEvent from Backend
     **/
    public void onScanLpnRequest(final DeviceScanLpnRequestEvent event) {
        if (getDevice() != null) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Set device scan Lpn view visible");
                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Showing Scan LPN view on the UI");
                    getScanLpnView().setVisible(true);
                    getScanLpnView().setManaged(true);
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Method to make pop-up visible for validate imei-serial text field
     *
     * @param event DeviceValidateImeiSerialRequestEvent from Backend
     **/
    public void onValidateImeiSerialRequest(final DeviceValidateImeiSerialRequestEvent event) {
        if (getDevice() != null) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Set device validate imei/serial view visible");
                Runnable runnable = () -> {
                    setDeviceIdMDC(this.device.getId());
                    LOGGER.info("Showing validate imei serial view on the UI");
                    getImeiSerialValidationView().setVisible(true);
                    getImeiSerialValidationView().setManaged(true);
                    MDC.clear();
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Business logic to execute on device sku code update event
     *
     * @param event DeviceMeidInfoEvent
     */
    public void onSkuCodeUpdateSucceeded(final DeviceSkuCodeUpdateEvent event) {
        if (getDevice() != null) {
            Device device = getDevice();

            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Device sku code update event collected successfully.");
                device.setSkuCode(eventDevice.getSkuCode());
                setSkuCodeField(eventDevice.getSkuCode());
            }
        }
    }

    /**
     * Business logic to execute on device reconnect require event
     *
     * @param event DeviceReconnectRequireEvent
     */
    public void onReconnectRequired(final DeviceReconnectRequireEvent event) {
        if (getDevice() != null) {
            Device device = getDevice();
            setDeviceIdMDC(device.getId());

            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Device reconnect require event collected successfully.");
                LOGGER.info("Show reconnect pop up");
                LOGGER.info("Device reconnect required.");
                Runnable runnable = () -> {
                    if (event.getErrorCode().equals(ErrorConstants.ICLOUD_LOCKED)) {
                        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("activationFailed"));
                    } else {
                        setDeviceStatusLabel(getLocalizationService().getLanguageSpecificText("reconnectDevice"));
                        reconnectErrorText.setText(getLocalizationService().getLanguageSpecificText("error")
                                + ": " + getLocalizationService().getLanguageSpecificText(
                                event.getErrorCode().getLocalizedKey()));
                        reconnectDeviceAnchor.setVisible(true);
                    }
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Business logic to execute on device Reprocess pop-up request event
     *
     * @param event DeviceReprocessPopupRequestEvent
     */
    public void onDeviceReprocessPopupRequest(final DeviceReprocessPopupRequestEvent event) {
        if (getDevice() != null) {
            Device device = getDevice();
            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                device.setGradePerformed(false);
                LOGGER.info("Set device reprocess view visible");
                Runnable runnable = () -> {
                    getDeviceReprocessView().setVisible(true);
                    getDeviceReprocessView().setManaged(true);
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Business logic to execute on device color changed response
     *
     * @param event DeviceColorChangeResponseEvent
     */
    public void onDeviceColorChangeResponse(final DeviceColorChangeResponseEvent event) {
        if (null != this.device) {
            Device device = getDevice();
            setDeviceIdMDC(device.getId());
            final Device eventDevice = event.getDevice();
            if (device.getId().equalsIgnoreCase(eventDevice.getId())) {
                LOGGER.info("Set color on device box");
                Runnable runnable = () -> {
                    colorComboBox.setValue(eventDevice.getColor());
                };
                runOnFxThread(runnable);
            }
        }
    }

    /**
     * Handles the decision process for BStar API error responses.
     *
     * @param event BStarErrorDecisionRequestEvent
     */
    public void bStarErrorDecision(final TwoWayApiErrorDecisionRequestEvent event) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Decision for bStar Api response object {}", event.getResponseObject());
            if (event.getResponseObject() != null) {
                printOperation = event.getPrintOperation();
                ObjectNode flattenedObject = flattenObjectNode(event.getResponseObject());
                String rawResponse = null;
                if (flattenedObject != null) {
                    if (flattenedObject.has(HTTP_STATUS_CODE_KEY)) {
                        rawResponse = event.getResponseObject().toString();
                    } else {
                        if (flattenedObject.has(ERROR_RESPONSE)) {
                            rawResponse = flattenedObject.get(ERROR_RESPONSE).toString();
                        }
                        if (flattenedObject.has(STATUS_MESSAGE_KEY)) {
                            rawResponse = rawResponse + " - " + flattenedObject.get(STATUS_MESSAGE_KEY).toString();
                        }
                    }
                }
                final String finalRawResponse = rawResponse;
                runOnFxThread(() -> {
                    bStarApiErrorTitleLBL.setText(event.getErrorTitle());
                    bStarApiErrorInfoLBL.setText(finalRawResponse);
                    bStarApiErrorVB.setVisible(true);
                    bStarApiErrorVB.setManaged(true);
                });
                if (!event.isFromRetry()) {
                    bStarApiErrorObject = event.getRequestObject();
                }
            }
        }
    }

    /**
     * This method call function required for cancel bStar api pop-up
     *
     * @param event CancelTwoWayApiRequestEvent
     */
    public void cancelTwoWayApiError(final CancelTwoWayApiRequestEvent event) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Cancel two way api error pop-up");
            bStarApiErrorCancelPressed(null);
            final String topic = TopicBuilder.build(device, "resume-two-way-api", "request");
            final ResumeTwoWayApiProcessingMessage requestMessage = new ResumeTwoWayApiProcessingMessage();
            requestMessage.setId(device.getId());
            requestMessage.setResponseObject(event.getApiResponseObject());
            requestMessage.setErrorTitle(event.getErrorTitle());
            requestMessage.setApiCallType(event.getApiCallType());
            requestMessage.setPrintOperation(event.getPrintOperation());
            publishToMqttTopic(topic, requestMessage);
        }
    }

    /**
     * This method used to set device status label
     *
     * @param event SetDeviceStatusLabelEvent
     */
    public void setDeviceStatusLabel(final SetDeviceStatusLabelEvent event) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Set device status label on device box");
            runOnFxThread(() -> {
                getDevice().setStage(event.getDeviceStage());
                setDeviceStatusLabel(
                        getLocalizationService().getLanguageSpecificText(event.getDeviceStage().getLocalizedKey()));
                updateCurrentStageOnUI(getDevice());
            });
        }
    }

    /**
     * This method used to update device grade
     *
     * @param event SetDeviceStatusLabelEvent
     */
    public void updateDeviceGrade(final DeviceGradeUpdateEvent event) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Update device grade {} on Ui", event.getGrade());
            runOnFxThread(() -> {
                gradeComboBox.setEditable(true);
                gradeComboBox.setValue(event.getGrade());
            });
        }
    }

    /**
     * This method used to update Japanese conformity status on UI
     *
     * @param event SetDeviceStatusLabelEvent
     */
    public void onJapaneseConformityCheckResponse(final DeviceJapaneseConformityResponseEvent event) {
        if (null != this.device) {
            final Device eventDevice = event.getDevice();

            this.device.setJapaneseConformityStatus(eventDevice.getJapaneseConformityStatus());
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Update Japanese conformity status {} on Ui", event.getDevice().getJapaneseConformityStatus());

            runOnFxThread(() -> {
                LOGGER.info("Showing Japanese conformity status on UI");
                updateAndNotifyBackendForResultsChange(false);
                showFunctionalityStatus(FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                        getUiInMemoryStore().getAssignedCloudCustomization()));

                if (JapaneseConformityStatus.CERTIFIED.equals(eventDevice.getJapaneseConformityStatus())) {
                    updateDefectOnMainView(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey(), PASSED, true);
                    updateDefectOnDiagnosticsView(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey(), PASSED);
                } else if (JapaneseConformityStatus.NOT_CERTIFIED.equals(eventDevice.getJapaneseConformityStatus())) {
                    updateDefectOnMainView(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey(), FAILED, true);
                    updateDefectOnDiagnosticsView(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey(), FAILED);
                }
            });
        }
    }

    // ==================================================================
    //                  Backend Requests and Notifications
    // ==================================================================

    /**
     * Method to trigger a device erase from the UI.
     */
    public void requestBackendForErase() {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());

            // This delay to ensure that the required fields
            // are updated before initiating manual erase.
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // do nothing
            }

            if (this.device instanceof IosDevice iosDevice) {
                if (ActivationStatus.ACTIVATED.equals(iosDevice.getActivationStatus())
                        && !iosDevice.isEraseInProgress() && !iosDevice.isShutdownInProgress()
                        && !iosDevice.isRestoreInProgress()) {

                    Runnable runnable = () -> {
                        eraserIcon.setImage(new Image("com/phonecheck/image/icon/erase_loading_icon.gif"));
                        setDeviceStatusLabel(ERASE_REQUEST_SENT);
                        showDeviceSubStatusLabel(false);
                    };
                    runOnFxThread(runnable);

                    LOGGER.info("Sending erase request for ios device.");
                    deviceOperationHelper.requestIosErase(iosDevice);
                } else if (iosDevice.isShutdownInProgress()) {
                    LOGGER.info("Cannot erase device when device is shutting down.");
                }
            } else if (this.device instanceof AndroidDevice androidDevice) {
                if (!Boolean.TRUE.equals(androidDevice.getIsErasePerformed())
                        && !androidDevice.isEraseInProgress()) {

                    Runnable runnable = () -> {
                        eraserIcon.setImage(new Image("com/phonecheck/image/icon/erase_loading_icon.gif"));
                        setDeviceStatusLabel(ERASE_REQUEST_SENT);
                        showDeviceSubStatusLabel(false);
                    };
                    runOnFxThread(runnable);
                    LOGGER.info("Sending erase request for android device.");
                    deviceOperationHelper.requestAndroidErase(androidDevice);
                } else {
                    LOGGER.info("Cannot erase device: Erase is already inProgress: {}, Erase performed: {}",
                            androidDevice.isEraseInProgress(), androidDevice.getIsErasePerformed());
                }
            }
        }
    }

    /**
     * Raises request to print label for the device
     *
     * @param message PrintLabelRequestMessage
     */
    public void requestBackendForPrint(final PrintLabelRequestMessage message) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            runOnFxThread(() -> {
                String textToCompare = getLocalizationService().getLanguageSpecificText(ERASE_RESTRICTED);
                setDeviceIdMDC(this.device.getId());
                if (!textToCompare.equalsIgnoreCase(deviceSubStatusLabel.getText())) {
                    LOGGER.info("Print request sent label shown on the UI");
                    setDeviceStatusLabel(DeviceStage.PRINTING.getLocalizedKey());
                    setWorkFlowStepInfo(DeviceStage.PRINTING);
                    updateDeviceSubStatusOnUI(DeviceStage.PRINTING);
                }
                MDC.clear();
            });
            final String topic = TopicBuilder.build(this.device, "print", "request");
            LOGGER.info("Sending print request for device.");
            message.setId(this.device.getId());
            publishToMqttTopic(topic, message);
        }
    }

    /**
     * Raises request to check esn for the device
     *
     * @param esnCheckType enum
     */
    public void requestBackendForEsnCheck(final EsnCheckType esnCheckType) {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            runOnFxThread(() -> {
                setWorkFlowStepInfo(DeviceStage.ESN_IN_PROGRESS);
                device.setStage(DeviceStage.ESN_IN_PROGRESS);
                updateDeviceSubStatusOnUI(device.getStage());
            });

            LOGGER.info("Sending esn check {} request for device.", esnCheckType);

            deviceOperationHelper.requestEsnCheck(device, esnCheckType);
        }
    }

    /**
     * Notifies backend for any test results change. It can be manual test change and also for initial defects
     *
     * @param shouldStartAutomation should automation triggered
     */
    protected void updateAndNotifyBackendForResultsChange(final boolean shouldStartAutomation) {
        if (null != this.device) {
            setDeviceIdMDC(device.getId());

            TestResultsUtil.updateTestResultsInDevice(device, failedTestKeys, passedTestKeys,
                    uiInMemoryStore.getAssignedCloudCustomization(), getLocalizationService());
            if (!failedCosmeticTestKeys.isEmpty() || !passedCosmeticTestKeys.isEmpty()) {
                TestResultsUtil.updateCosmeticResultsInDevice(device, failedCosmeticTestKeys, passedCosmeticTestKeys);
            }

            if (device.getDeviceTestResult() != null &&
                    device.getDeviceTestResult().getTestResults() != null &&
                    Boolean.TRUE.equals(device.getDeviceTestResult().getTestResults().getTestingCompleted())) {
                // Notify backend about the test result change
                LOGGER.info("Sending the updated test results to the backend. Should start automation: {}",
                        shouldStartAutomation);

                deviceOperationHelper.notifyTestResultsChange(device, shouldStartAutomation);
            } else {
                LOGGER.info("Device testing is not completed no need to notify backend.");
            }
        }
    }

    public void updateDeviceLabelsForRestore() {
        deviceStatusLabel.setText(getLocalizationService().getLanguageSpecificText("restoreReqSent"));
        deviceSubStatusLabel.setText("");
    }

    /*
       Battery Health will be set as "-" on UI when the device's Battery OEM status is
       NOT_GENUINE and the language is Japanese. Else set the actual value.
    */
    private void setBatteryHealthLabels(final int batteryHealth) {
        boolean isJapanese = uiInMemoryStore.getCurrentLanguage().equals("Japanese");

        if (batteryHealth > 0) {
            getBatteryMiniHealthLabel().getStyleClass().add("battery-health-bold-text");
        }

        if ((device instanceof IosDevice) && isJapanese &&
                (OemStatus.NOT_GENUINE.equals(((IosDevice) device).getOemBatteryStatus()))) {

            getBatteryMiniHealthLabel().setText("-");
            getInfoBatteryMiniHealthText().setText(getLocalizationService().
                    getLanguageSpecificText("batteryHealth") + ": " + "-");
            getBatteryHealthLabel().setText("-");
        } else {
            if (batteryHealth == 100) {
                getBatteryMiniHealthLabel().getStyleClass().add("battery-health-100-percent-bold");
            }
            getBatteryMiniHealthLabel().setText(batteryHealth + "﹪");
            getInfoBatteryMiniHealthText().setText(getLocalizationService().
                    getLanguageSpecificText("batteryHealth") + ": " + batteryHealth + "%");
            getBatteryHealthLabel().setText(batteryHealth + "%");
        }
    }

    /**
     * Handles the process of loading the grading system questions into the user interface.
     * This method is responsible for setting up the view for the grading system questions based on the
     * {@link LoadGradingQuestionsRequestEvent} provided. It initializes the necessary controller, loads
     * the FXML for the grading questions, and updates the user interface components accordingly.
     *
     * @param event LoadGradingQuestionsRequestEvent
     */
    public void loadGradingSystemQuestions(final LoadGradingQuestionsRequestEvent event) {
        if (null != this.device) {
            setDeviceIdMDC(device.getId());
            LOGGER.info("Load grading system questions request received.");
            if (!gradingSystemVB.isVisible()) {
                resetGradingView();
                try {
                    FXMLLoader loader = new FXMLLoader(
                            getClass().getResource("/com/phonecheck/ui/grading/grading-system-questions.fxml"),
                            getLocalizationService().getResourceBundle());
                    loader.load();
                    gradingSystemQuestionsController = loader.getController();
                    runOnFxThread(() -> {
                        if (!gradingSystemVB.getChildren().isEmpty()) {
                            gradingSystemVB.getChildren().clear();
                        }
                        gradingSystemVB.getChildren().add(gradingSystemQuestionsController.getRoot());
                        if (StringUtils.isBlank(gradingSaveState)) {
                            gradingSystemQuestionsController.loadGradingSystemQuestionnaire(
                                    this, event.getCategories(), event.getCategoryNames(),
                                    event.getProfileId(), uiInMemoryStore, getLocalizationService());
                        } else {
                            gradingSystemQuestionsController.fromSaveState(this,
                                    mapper.convertValue(gradingSaveState, GradingQuestionBackTraceResponse[].class),
                                    event.getCategories(), event.getCategoryNames(), event.getProfileId());
                        }
                        profileNameLBL.setText(getLocalizationService().getLanguageSpecificText("profileName")
                                + event.getProfileName());
                        gradingSystemVB.setVisible(true);
                        gradingSystemVB.setManaged(true);
                        gradingInProgress = true;
                        gradingVB.setVisible(true);
                        gradingVB.setManaged(true);
                    });
                } catch (Exception e) {
                    LOGGER.error("Grading system questions could not be loaded");
                }
            }
        }
    }

    /**
     * This method resets the grading view
     */
    public void resetGradingView() {
        LOGGER.info("Reset grading view");
        runOnFxThread(() -> {
            gradingSystemQuestionsController = null;
            gradingSystemVB.setVisible(false);
            gradingVB.setVisible(false);
            gradingVB.setManaged(false);
            gradingSystemVB.getChildren().clear();
            gradingSaveState = StringUtils.EMPTY;
        });
    }

    /**
     * Sets the grading system grade based on the information from the {@link SetGradingSystemGradeEvent} event.
     * This method updates the device's grading system grade, updates the UI to reflect the new grade.
     *
     * @param event      SetGradingSystemGradeEvent
     * @param controller GradingSystemQuestionsController
     */
    public void setGradingSystemGrade(final SetGradingSystemGradeEvent event,
                                      final GradingSystemQuestionsController controller) {
        Device device = controller.getDeviceBoxController().getDevice();
        if (null != device) {
            setDeviceIdMDC(device.getId());
            LOGGER.info("Set grading system grade {}", event.getGrade());
            device.setGradingSystemGrade(event.getGrade());
            controller.getDeviceBoxController().gradingAnswers = event.getGradingAnswersData();

            runOnFxThread(() ->
                    controller.getDeviceBoxController().gradeComboBox.setValue(getDevice().getGradingSystemGrade()));
            device.setGradePerformed(true);
            controller.getDeviceBoxController().gradingSaveState = StringUtils.EMPTY;
            if (!fromReProcess) {
                TestResultsUtil.updateTestResultsInDevice(device, controller.getDeviceBoxController().failedTestKeys,
                        controller.getDeviceBoxController().passedTestKeys,
                        uiInMemoryStore.getAssignedCloudCustomization(), getLocalizationService());
            }
            deviceOperationHelper.requestGradeChange(device,
                    controller.getDeviceBoxController().getGradeComboBox().getValue());
            hideGradingSystem(controller);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // do nothing
            }
            publishUpdateGradePerformedStatus();

            final LocalCustomizations.ShopfloorSettings shopfloorSettings =
                    uiInMemoryStore.getCustomizations().getShopfloorSettings();
            if (shopfloorSettings != null && shopfloorSettings.getSelectedImageGrades() != null &&
                    !shopfloorSettings.getSelectedImageGrades().isEmpty() &&
                    shopfloorSettings.getSelectedImageGrades()
                            .stream()
                            .anyMatch(selectedGrade -> selectedGrade.equals(CustomizationConstants.ANY_GRADE)
                                    || selectedGrade.equals(event.getGrade()))) {
                LOGGER.info("Selected grade is enabled in local customization for image capture");
                onGradeWithImageReceived();
            }
        }
    }

    /**
     * Hides the grading system user interface components
     *
     * @param controller GradingSystemQuestionsController
     */
    public void hideGradingSystem(final GradingSystemQuestionsController controller) {
        LOGGER.info("Hide grading system view");
        if (controller.getDeviceBoxController() != null) {
            DeviceBoxController boxController = controller.getDeviceBoxController();
            runOnFxThread(() -> {
                boxController.gradingSystemVB.setVisible(false);
                boxController.gradingVB.setVisible(false);
                boxController.gradingVB.setManaged(false);
                boxController.gradingInProgress = false;
                boxController.gradingSystemQuestionsController.resetDevice();
                boxController.gradingSystemQuestionsController = null;
                boxController.gradeComboBox.setEditable(false);
                boxController.gradeComboBox.setMouseTransparent(true);
                boxController.gradeComboBox.setFocusTraversable(false);
            });
        }
    }

    /**
     * Publish mqtt message to update grade performed status in db
     */
    private void publishUpdateGradePerformedStatus() {
        final String topic = TopicBuilder.build(device, "update-grade-performed", "request");
        final UpdateGradePerformedRequestMessage message = new UpdateGradePerformedRequestMessage();
        message.setId(device.getId());
        publishToMqttTopic(topic, message);
    }

    private ObjectNode flattenObjectNode(final ObjectNode root) {
        ObjectNode result = mapper.createObjectNode();
        root.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            if (value instanceof ObjectNode) {
                ObjectNode flattened = flattenObjectNode((ObjectNode) value);
                flattened.fields().forEachRemaining(nestedEntry ->
                        result.set(nestedEntry.getKey(), nestedEntry.getValue()));
            } else {
                result.set(key, value);
            }
        });
        return result;
    }

    /**
     * Hides the more menu popover (if visible) and shows the manual battery retrieval view.
     */
    private void handleRecheckBatteryInfo() {
        if (moreMenuPopOver.isShowing()) {
            moreMenuPopOver.hide();
        }
        getManualBatteryRetrievalView().setVisible(true);
    }

    /**
     * Displays the Xiaomi App Admin activation pop-up on the UI and starts a timer to track its visibility duration.
     * If a device is present, it logs the event, updates the UI elements, and initializes a countdown timer.
     * Once the timer elapses, the pop-up is automatically hidden, and a notification is sent to the backend.
     */
    public void onShowXiaomiAppAdminPopup() {
        if (null != this.device) {
            setDeviceIdMDC(this.device.getId());
            LOGGER.info("Display Xiaomi app admin pop-up on Ui");
            runOnFxThread(() -> {
                getXiaomiAppAdminActivatePopupView().setVisible(true);
                getAdminPopupTimerLabel().setText("00:00");
            });

            // Setup and start Xiaomi App admin pop-up time
            setAppAdminPopupTimer(new TimerUtil(
                    // on Update
                    () -> runOnFxThread(() ->
                            getAdminPopupTimerLabel().setText(getAppAdminPopupTimer().getTimeString())),
                    // on Stop after Timeout
                    this::hideAppAdminPopupAndNotifyBackend, APP_ADMIN_POP_UP_TIME_OUT, false
            ));
            getAppAdminPopupTimer().start();
        }
    }

    /**
     * Hides the Xiaomi App Admin activation pop-up and notifies the backend upon timeout or manual closure.
     * Stops the timer if it is running, updates the UI to hide the pop-up, and sends an MQTT message
     * to proceed with the Xiaomi erase process.
     */
    private void hideAppAdminPopupAndNotifyBackend() {
        if (getAppAdminPopupTimer() != null) {
            getAppAdminPopupTimer().stop();
        }
        runOnFxThread(() -> {
            getXiaomiAppAdminActivatePopupView().setVisible(false);
            getXiaomiAppAdminActivatePopupView().setManaged(false);
        });
        final String topic = TopicBuilder.build(device, "proceed-for-xiaomi-erase", "response");
        final ProceedForXiaomiEraseMessage proceedForXiaomiEraseMessage =
                new ProceedForXiaomiEraseMessage();
        proceedForXiaomiEraseMessage.setId(device.getId());
        publishToMqttTopic(topic, proceedForXiaomiEraseMessage);
    }

    /**
     * if Esim is detected, update it's status in main view as
     * GREEN - if esim was detected and erased
     * RED - if esim was detected and not erased.
     *
     * @param device
     */
    protected void verifyAndUpdateESimDetectedStatus(final AndroidDevice device) {
        LOGGER.info("Verify if esim detected defect should be displayed.");
        if (device.isEsimActive() && !device.isESimErased()) {
            LOGGER.info("ESim is still present and not erased");
            updateDefectOnMainView(ESIM_DETECTED, FAILED, false);
        } else if (device.isEsimActive() && device.isESimErased()) {
            LOGGER.info("Esim was present and is erased now");
            updateDefectOnMainView(ESIM_DETECTED, PASSED, false);
        }
    }

    /**
     * This method is needed in case of device re-connected in same transaction.
     * Panic full test is done each time device is paired and the
     * result is sent first to the UI and then test results from DB
     * are sent which overrides the UI test results. In order to not lose the panic full test result,
     * we verify if panic full result present in passed / failed test keys and add to the pass / fail
     * results sent from DB.
     *
     * @param eventDevice the device for which testing was done.
     */
    private void verifyAndUpdatePanicFullResult(final Device eventDevice) {
        if (eventDevice.getDeviceTestResult().getTestResults() == null) {
            return;
        }

        List<String> failedTestResults = Optional.ofNullable(eventDevice.getDeviceTestResult().
                getTestResults().getFailed()).orElseGet(ArrayList::new);
        List<String> passedTestResults = Optional.ofNullable(eventDevice.getDeviceTestResult().
                getTestResults().getPassed()).orElseGet(ArrayList::new);

        if (Stream.of(PANIC_FULL_TEST, MANUAL_PANIC_FULL_TEST).anyMatch(
                test -> failedTestResults.contains(test) || passedTestResults.contains(test))) {
            return;
        }

        if (passedTestKeys.contains(PANIC_FULL_TEST)) {
            updateTestResults(passedTestResults, PANIC_FULL_TEST, eventDevice, true);
        } else if (passedTestKeys.contains(MANUAL_PANIC_FULL_TEST)) {
            updateTestResults(passedTestResults, MANUAL_PANIC_FULL_TEST, eventDevice, true);
        } else if (failedTestKeys.contains(PANIC_FULL_TEST)) {
            updateTestResults(failedTestResults, PANIC_FULL_TEST, eventDevice, false);
        } else if (failedTestKeys.contains(MANUAL_PANIC_FULL_TEST)) {
            updateTestResults(failedTestResults, MANUAL_PANIC_FULL_TEST, eventDevice, false);
        } else {
            LOGGER.info("Panic full test is not found in the pass/failed test keys");
        }
    }

    /**
     * add test to testResults and sort the list
     *
     * @param testResults
     * @param test
     * @param eventDevice
     * @param isPassed
     */
    private void updateTestResults(final List<String> testResults, final String test,
                                   final Device eventDevice, final boolean isPassed) {
        testResults.add(test);
        Collections.sort(testResults);
        if (isPassed) {
            eventDevice.getDeviceTestResult().getTestResults().setPassed(testResults);
        } else {
            eventDevice.getDeviceTestResult().getTestResults().setFailed(testResults);
        }
    }


    /**
     * Logic to display battery drain status on the device box
     *
     * @param event AutoExportStatusEvent
     */
    public void onBatteryDrainResults(final BatteryDrainResultsEvent event) {
        if (null != this.device) {
            LOGGER.info("Updating battery results in UI's device object");
            setDeviceIdMDC(this.device.getId());
            final Device eventDevice = event.getDevice();

            DeviceTestResult deviceTestResult = this.device.getDeviceTestResult();
            if (deviceTestResult == null) {
                deviceTestResult = new DeviceTestResult();
                this.device.setDeviceTestResult(deviceTestResult);
            }
            this.device.getDeviceTestResult().setBatteryResults(
                    eventDevice.getDeviceTestResult().getBatteryResults());
            this.device.getDeviceTestResult().setTestResults(eventDevice.getDeviceTestResult().getTestResults());

            TestResults testResults = deviceTestResult.getTestResults();
            if (testResults.getPassed() != null && testResults.getPassed().contains(BATTERY_DRAIN)) {
                updateTestStatus(BATTERY_DRAIN, true);
            } else if (testResults.getFailed() != null && testResults.getFailed().contains(BATTERY_DRAIN)) {
                updateTestStatus(BATTERY_DRAIN, false);
            }

            if (this.device.getId().equalsIgnoreCase(eventDevice.getId())) {
                runOnFxThread(() -> {
                    showDeviceSubStatusLabel(true);
                    setDeviceSubStatusLabel(getLocalizationService().
                            getLanguageSpecificText(BATTERY_DRAIN_INFO_COLLECTED));
                });
            }
        }
    }

    /**
     * udate the test status in the test keys and update on UI
     *
     * @param testKey
     * @param passed
     */
    private void updateTestStatus(final String testKey, final boolean passed) {
        runOnFxThread(() -> {
            if (passed) {
                failedTestKeys.remove(testKey);
                if (passedTestKeys.add(testKey)) {
                    updateDefectOnDiagnosticsView(testKey, PASSED);
                    updateDefectOnMainView(testKey, PASSED, false);
                    Collections.sort(passedTestKeys);
                }
            } else {
                passedTestKeys.remove(testKey);
                if (failedTestKeys.add(testKey)) {
                    updateDefectOnDiagnosticsView(testKey, FAILED);
                    updateDefectOnMainView(testKey, FAILED, false);
                    Collections.sort(failedTestKeys);
                }
            }
        });
    }

    /**
     * set device guid received from backend
     *
     * @param event DeviceGuidResponseEvent
     */
    public void setDeviceGuid(final DeviceGuidResponseEvent event) {
        if (StringUtils.isNotBlank(event.getDeviceGuid())) {
            device.setGuid(event.getDeviceGuid());
        }
    }
}
