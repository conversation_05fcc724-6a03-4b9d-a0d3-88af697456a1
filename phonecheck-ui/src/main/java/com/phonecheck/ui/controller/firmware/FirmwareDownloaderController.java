package com.phonecheck.ui.controller.firmware;

import com.jfoenix.controls.JFXButton;
import com.jfoenix.controls.JFXCheckBox;
import com.phonecheck.model.event.FirmwareDownloadResponseEvent;
import com.phonecheck.model.event.FirmwareStatusResponseEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.FirmwareDownloadRequestMessage;
import com.phonecheck.model.mqtt.messages.FirmwarePathUpdateRequestMessage;
import com.phonecheck.model.mqtt.messages.FirmwareStatusMessage;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.ui.component.FirmwareStatusCell;
import com.phonecheck.ui.controller.AbstractUiController;
import com.phonecheck.ui.controller.utility.FirmwareMapperUtil;
import javafx.application.Platform;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.collections.transformation.SortedList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.DirectoryChooser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.URL;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.TreeMap;

import static com.phonecheck.model.constants.FileConstants.FIRMWARES;

@Component
public class FirmwareDownloaderController extends AbstractUiController implements Initializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirmwareDownloaderController.class);
    private static final String TABLEVIEW_STYLE = "/com/phonecheck/style/tableview.css";
    private static final String SCROLLBAR_STYLE = "/com/phonecheck/style/scrollbar.css";
    private static final String FILE_SIZE = "File Size";
    private static final String STATUS = "Status";

    @FXML
    private StackPane firmwarePane;
    @FXML
    private TextField search;
    @FXML
    private JFXCheckBox enableIpad;
    @FXML
    private JFXCheckBox enableIphone;
    @FXML
    private JFXCheckBox enableInstalled;
    @FXML
    private JFXCheckBox enableAvailable;
    @FXML
    private JFXCheckBox enableAutoDownloaded;
    @FXML
    private TextField downloadPathTextField;
    @FXML
    private JFXButton firmwareDownloadPathChangeButton;
    @FXML
    private JFXButton showDownloadInFinderButton;
    @FXML
    private TextField networkPathTextField;
    @FXML
    private JFXButton firmwareNetworkPathChangeButton;
    @FXML
    private JFXButton showNetworkInFinderButton;
    @FXML
    private HBox firmwareTableHBox;
    @Autowired
    private UiInMemoryStore uiInMemoryStore;
    @Autowired
    private SupportFilePath supportFilePath;
    @Autowired
    private OsChecker osChecker;
    private TableView<FirmwareModel.FirmwareResponse> firmwareTableView;
    private CheckBox mainHeaderCheckBox;
    private boolean headerCheckboxChangeAllowed = true;
    @FXML
    private JFXButton downloadSelected;
    @FXML
    private VBox loaderViewFirmware;

    @Override
    public void initialize(final URL location, final ResourceBundle resources) {
        manageLoaderView(true);
        LOGGER.info("Fetching update info from the backend.");
        FirmwareStatusMessage requestMessage = new FirmwareStatusMessage();
        publishToMqttTopic(TopicBuilder.buildGenericTopic("firmware-status", "request"), requestMessage);
    }

    /**
     * List of firmwares with their current status is received
     *
     * @param responseEvent FirmwareUpdateInfoEvent
     */
    @EventListener
    public void onFirmwareUpdateResponse(final FirmwareStatusResponseEvent responseEvent) {

        final Map<String, FirmwareDownloadStatus> firmwareDownloadStatusMap = responseEvent.
                getFirmwareDownloadStatusMap();

        for (Map.Entry<String, FirmwareModel.FirmwareResponse> firmware :
                uiInMemoryStore.getFirmwareModels().entrySet()) {
            FirmwareDownloadStatus status = firmwareDownloadStatusMap.get(firmware.getKey());
            firmware.getValue().setDownloadStatus(status);
        }
        LOGGER.info("Firmwares loaded from the backend.");

        Runnable runnable = () -> {
            setupFirmwaresTableView();
            addCheckBoxesForTableViewRecord();
            setupTableViewColumns();
            setupFirmwareData();
        };
        runOnFxThread(runnable);
        downloadSelected.setOnMouseClicked(event -> handleDownloadAllButton());
        firmwareDownloadPathChangeButton.setOnMouseClicked(event -> handleFilePathChangeAction());
        manageLoaderView(false);
    }

    /**
     * Initializes and configures the firmware TableView for displaying firmware information.
     */
    private void setupFirmwaresTableView() {
        firmwareTableView = new TableView<>();
        HBox.setHgrow(firmwareTableView, Priority.ALWAYS);
        String tableViewCss = Objects
                .requireNonNull(getClass().getResource(TABLEVIEW_STYLE)).toExternalForm();
        String scrollbarCss = Objects
                .requireNonNull(getClass().getResource(SCROLLBAR_STYLE)).toExternalForm();
        firmwareTableView.getStylesheets().addAll(tableViewCss, scrollbarCss);
        firmwareTableHBox.getChildren().add(firmwareTableView);
    }

    private FirmwareStatusCell.FirmwareDownloadHandler firmwareDownloadHandler = new
            FirmwareStatusCell.FirmwareDownloadHandler() {
                private final String topic = TopicBuilder.buildGenericTopic("firmware-download", "request");

                @Override
                public void downloadFirmware(final String firmwareId) {
                    LOGGER.info("Download Firmware Request received for firmwareId : {}", firmwareId);
                    FirmwareDownloadRequestMessage requestMessage = new FirmwareDownloadRequestMessage();
                    requestMessage.setFirmwareId(firmwareId);
                    publishToMqttTopic(topic, requestMessage);
                }

                @Override
                public void stopDownloading(final String firmwareId, final boolean stop) {
                    FirmwareDownloadRequestMessage requestMessage = new FirmwareDownloadRequestMessage();
                    requestMessage.setFirmwareId(firmwareId);
                    requestMessage.setStop(stop);
                    publishToMqttTopic(topic, requestMessage);
                }
            };

    /**
     * Add checkbox at the start of each record of table view
     */
    private void addCheckBoxesForTableViewRecord() {
        // Create a checkbox for the header
        mainHeaderCheckBox = new CheckBox();
        mainHeaderCheckBox.setSelected(false);
        mainHeaderCheckBox.selectedProperty().addListener((observable, oldValue, newValue) -> {
            if (headerCheckboxChangeAllowed) {
                for (FirmwareModel.FirmwareResponse firmwareInfo : firmwareTableView.getItems()) {
                    firmwareInfo.setSelected(newValue);
                }
                firmwareTableView.refresh();
            }
        });
        TableColumn<FirmwareModel.FirmwareResponse, CheckBox> checkboxColumn = new TableColumn<>();
        checkboxColumn.setCellValueFactory(
                c -> {
                    FirmwareModel.FirmwareResponse firmwareInfo = c.getValue();
                    CheckBox checkBox = new CheckBox();
                    checkBox.selectedProperty().setValue(firmwareInfo.isSelected());
                    checkBox.selectedProperty().addListener((ov, old_val, new_val) -> {
                        firmwareInfo.setSelected(new_val);
                        updateHeaderCheckboxState();
                    });
                    return new SimpleObjectProperty<>(checkBox);
                });
        checkboxColumn.setCellFactory(col -> new TableCell<>() {
            @Override
            protected void updateItem(final CheckBox item, final boolean empty) {
                super.updateItem(item, empty);

                if (empty || item == null) {
                    setGraphic(null);
                } else {
                    setGraphic(item);
                }
            }
        });
        checkboxColumn.setGraphic(mainHeaderCheckBox);
        checkboxColumn.setPrefWidth(30);
        firmwareTableView.getColumns().add(checkboxColumn);
    }

    /**
     * Update the state of the main header checkbox based on the row checkboxes.
     */
    private void updateHeaderCheckboxState() {
        boolean allSelected = true;
        for (FirmwareModel.FirmwareResponse item : firmwareTableView.getItems()) {
            if (!item.isSelected()) {
                allSelected = false;
                break;
            }
        }
        headerCheckboxChangeAllowed = false;
        mainHeaderCheckBox.setSelected(allSelected);
        headerCheckboxChangeAllowed = true;
    }

    /**
     * Configures the columns of the firmware TableView based on predefined order and properties.
     * Iterates through the column mapping, sets up TableColumn instances with appropriate properties,
     * and adds them to the TableView.
     */
    private void setupTableViewColumns() {
        Map<String, String> orderMap = FirmwareMapperUtil.getFirmwareColumnOrderMap();
        for (Map.Entry<String, String> entry : orderMap.entrySet()) {
            TableColumn<FirmwareModel.FirmwareResponse, Object> column = new TableColumn<>(entry.getValue());
            column.setCellValueFactory(new PropertyValueFactory<>(entry.getKey()));
            column.setPrefWidth(column.getPrefWidth() * 2.5);
            if (column.getText().equalsIgnoreCase(FILE_SIZE)) {
                column.setCellValueFactory(param -> new SimpleObjectProperty<>(FirmwareMapperUtil.
                        convertBytesToFileSize(Long.parseLong(param.getValue()
                                .getSize()), true)));
            }
            if (column.getText().equalsIgnoreCase(STATUS)) {
                column.setCellFactory(c -> new FirmwareStatusCell(uiInMemoryStore, firmwareDownloadHandler,
                        getLocalizationService()));
            }
            firmwareTableView.getColumns().add(column);
        }
    }

    /**
     * Sets up firmware data for display by creating a filtered list of firmware models from the in-memory store.
     * Maps essential properties to a new list of FirmwareResponse instances and populates the TableView.
     */
    private void setupFirmwareData() {
        Map<String, FirmwareModel.FirmwareResponse> filteredData = new TreeMap<>();
        if (uiInMemoryStore.getFirmwareModels() != null) {
            for (FirmwareModel.FirmwareResponse firmwareModel : uiInMemoryStore.getFirmwareModels().values()) {
                FirmwareModel.FirmwareResponse row = new FirmwareModel.FirmwareResponse();
                FirmwareDownloadStatus status = firmwareModel.getDownloadStatus();
                row.setId(firmwareModel.getId());
                row.setName(firmwareModel.getName());
                row.setVersion(firmwareModel.getVersion());
                row.setSize(firmwareModel.getSize());
                row.setFileName(firmwareModel.getFileName());
                row.setReleaseDate(firmwareModel.getReleaseDate());
                row.setPercentage(status == FirmwareDownloadStatus.SUCCESS ? 100 : 0);
                row.setDownloadStatus(status);
                firmwareModel.setDownloadStatus(row.getDownloadStatus());
                firmwareModel.setPercentage(row.getPercentage());
                String key = row.getId();
                filteredData.put(key, row);
            }
        }
        firmwareTableView.getItems().addAll(filteredData.values());
        searchAndFilterFirmware(filteredData);
    }


/*    private boolean isFileBeingDownloadedAlready(final FirmwareModel.FirmwareResponse firmware) {
        FirmwareModel.FirmwareResponse firmwareInProcess = uiInMemoryStore.getFirmwareModels().values()
                .stream().filter(s -> !firmware.getId().equals(s.getId())
                        && firmware.getFileName().equals(s.getFileName())
                        && (s.getDownloadStatus() == FirmwareDownloadStatus.IN_PROGRESS
                        ||  s.getDownloadStatus() == FirmwareDownloadStatus.QUEUED))
                .findFirst().orElse(null);
        return firmwareInProcess != null;
    }*/

    /**
     * /**
     * Searches and filters the firmware data based on the given criteria.
     * Updates the TableView with the filtered results, ensuring proper sorting and display refresh.
     *
     * @param firmwareResponseList The list of firmware responses to be displayed and searched.
     */
    private void searchAndFilterFirmware(final Map<String, FirmwareModel.FirmwareResponse> firmwareResponseList) {
        final String firmwarePath = uiInMemoryStore.getFirmwareDownloadPath();

        LOGGER.info("Setting current firmware folder path to: {}", firmwarePath);

        networkPathTextField.setText(firmwarePath);
        downloadPathTextField.setText(firmwarePath);

        FilteredList<FirmwareModel.FirmwareResponse> filteredData = new FilteredList<>(
                FXCollections.observableArrayList(firmwareResponseList.values()), p -> true);

        search.textProperty().addListener((observable, oldValue, newValue) -> {
            filteredData.setPredicate(f -> {
                if (newValue == null || newValue.isEmpty()) {
                    return true;
                }
                return StringUtils.containsIgnoreCase(f.getName(), newValue) ||
                        StringUtils.containsIgnoreCase(String.valueOf(f.getVersion()), newValue) ||
                        StringUtils.containsIgnoreCase(String.valueOf(f.getSize()), newValue) ||
                        StringUtils.containsIgnoreCase(f.getFileName(), newValue) ||
                        StringUtils.containsIgnoreCase(f.getReleaseDate(), newValue) ||
                        StringUtils.containsIgnoreCase(String.valueOf(f.getPercentage()), newValue);
            });
        });

        enableIphone.setOnAction(event -> updateFilter(filteredData));
        enableIpad.setOnAction(event -> updateFilter(filteredData));
        enableInstalled.setOnAction(event -> updateFilter(filteredData));
        enableAvailable.setOnAction(event -> updateFilter(filteredData));

        SortedList<FirmwareModel.FirmwareResponse> sortableData = new SortedList<>(filteredData);
        firmwareTableView.setItems(sortableData);
        firmwareTableView.refresh();

        sortableData.comparatorProperty().bind(firmwareTableView.comparatorProperty());
    }

    /**
     * Updates the filter predicate for the provided FilteredList based on the selected filter criteria.
     *
     * @param filteredData The FilteredList to be updated with the new filter predicate.
     */
    private void updateFilter(final FilteredList<FirmwareModel.FirmwareResponse> filteredData) {
        filteredData.setPredicate(f -> {
            boolean isNameMatch = false;
            boolean isInstalledMatch = false;
            boolean isAvailableMatch = false;

            if (enableIphone.isSelected()) {
                isNameMatch = f.getName().toLowerCase().contains("iphone");
            } else if (enableIpad.isSelected()) {
                isNameMatch = f.getName().toLowerCase().contains("ipad");
            } else if (enableInstalled.isSelected()) {
                isInstalledMatch = FirmwareDownloadStatus.SUCCESS == f.getDownloadStatus();
            } else {
                isAvailableMatch = true;
            }

            return isNameMatch || isInstalledMatch || isAvailableMatch;
        });
    }

    /**
     * Handles the action when the "Download All" button is clicked.
     * Iterates through the firmware items in the table view, checks
     * and initiates downloads for selected, not already downloaded items.
     */
    private void handleDownloadAllButton() {
        ObservableList<FirmwareModel.FirmwareResponse> firmwareItems = firmwareTableView.getItems();
        for (FirmwareModel.FirmwareResponse firmwareInfo : firmwareItems) {
            if (firmwareInfo.isSelected() && FirmwareDownloadStatus.IN_PROGRESS != firmwareInfo.getDownloadStatus()) {
                uiInMemoryStore.getFirmwareModels().get(firmwareInfo.getId())
                        .setDownloadStatus(FirmwareDownloadStatus.QUEUED);
                firmwareInfo.setDownloadStatus(FirmwareDownloadStatus.QUEUED);
                String topic = TopicBuilder.buildGenericTopic("firmware-download", "request");
                FirmwareDownloadRequestMessage requestMessage = new FirmwareDownloadRequestMessage();
                requestMessage.setFirmwareId(firmwareInfo.getId());
                publishToMqttTopic(topic, requestMessage);
                firmwareTableView.refresh();
            }
        }
    }

    /**
     * Retrieves the File object representing the download file with the specified fileName.
     *
     * @param fileName The name of the firmware file to be retrieved.
     * @return The File object representing the download file.
     */
    private File getDownloadFile(final String fileName) {
        return new File(getDownloadPath(), fileName);
    }


    /**
     * Retrieves current download path
     *
     * @return firmware path
     */
    private String getDownloadPath() {
        String firmwarePath = StringUtils.isBlank(uiInMemoryStore.getFirmwareDownloadPath()) ?
                supportFilePath.getPaths().getRootFolderPath() : uiInMemoryStore.getFirmwareDownloadPath();
        firmwarePath = firmwarePath.endsWith(FIRMWARES) ? firmwarePath : (osChecker.isMac() ?
                firmwarePath + "/" + FIRMWARES  :   firmwarePath + File.separator + FIRMWARES);
        return firmwarePath;
    }

    /**
     * Handles the FirmwareDownloadResponseEvent and updates the
     * download progress and status in the firmware table view.
     *
     * @param event The FirmwareDownloadResponseEvent triggered when updating firmware download progress.
     */
    @EventListener
    public void handleFirmwareDownloadUpdate(final FirmwareDownloadResponseEvent event) {
        final String firmwareId = event.getFirmwareId();
        final int progress = event.getProgress();
        final FirmwareDownloadStatus status = event.getFirmwareDownloadStatus();

        LOGGER.info("Received firmware response for the id:{}, progress:{}, downloading status : {}",
                firmwareId, progress, status);

        FirmwareModel.FirmwareResponse firmwareModel = uiInMemoryStore.getFirmwareModels().get(firmwareId);
        String fileName = firmwareModel.getFileName();

        for (FirmwareModel.FirmwareResponse firmware : uiInMemoryStore.getFirmwareModels().values()) {
            if (fileName.equals(firmware.getFileName())) {
                firmware.setDownloadStatus(status);
                firmware.setPercentage(progress);
            }
        }

        Platform.runLater(() -> {
            if (firmwareTableView != null) {
                firmwareTableView.refresh();
            }
        });
    }


    /**
     * Handles the Firmware file path change action
    */
    private void handleFilePathChangeAction() {
        Runnable runnable = () -> {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            directoryChooser.setTitle(getLocalizationService().getLanguageSpecificText("downloadFirmwareTo"));
            final String oldFirmwarePath = uiInMemoryStore.getFirmwareDownloadPath();
            LOGGER.info("Old firmwares folder path:{}", oldFirmwarePath);
            File initDir = new File(oldFirmwarePath);
            if (!initDir.exists()) {
                initDir.mkdir();
                initDir = new File(oldFirmwarePath);
            }
            directoryChooser.setInitialDirectory(initDir);
            File modifiedPath = directoryChooser.showDialog(firmwarePane.getScene().getWindow());
            if (modifiedPath != null) {
                String updatedPath = modifiedPath.getAbsolutePath();
                if (!updatedPath.endsWith(FIRMWARES)) {
                    updatedPath = new File(updatedPath, FIRMWARES).getAbsolutePath();
                }
                LOGGER.info("New firmwares folder path : {}", updatedPath);
                downloadPathTextField.setText(updatedPath);
                uiInMemoryStore.setFirmwareDownloadPath(updatedPath);
                FirmwarePathUpdateRequestMessage requestMessage = new FirmwarePathUpdateRequestMessage();
                requestMessage.setUpdatedPath(updatedPath);
                publishToMqttTopic(TopicBuilder.buildGenericTopic("firmware-path", "update"), requestMessage);
                updateDataPostPathChange();
            }
        };
        runOnFxThread(runnable);
    }


    /**
     * updates data post download path change
     */
    private void updateDataPostPathChange() {
        if (uiInMemoryStore.getFirmwareModels() != null) {
            for (FirmwareModel.FirmwareResponse firmwareModel : uiInMemoryStore.getFirmwareModels().values()) {
                FirmwareModel.FirmwareResponse row = new FirmwareModel.FirmwareResponse();
                File ipsw = getDownloadFile(firmwareModel.getFileName());
                ipsw = new File(ipsw.getAbsolutePath().replace(".ipsw", ""));
                row.setPercentage(ipsw.exists() ? 100 : 0);
                row.setDownloadStatus(ipsw.exists() ? FirmwareDownloadStatus.SUCCESS
                        : FirmwareDownloadStatus.NOT_INITIATED);
            }
        }
        Platform.runLater(() -> {
            if (firmwareTableView != null) {
                firmwareTableView.refresh();
            }
        });
    }


    /**
     * After successfully load content from backend,
     * hide loader view and show firmware tab view
     *
     * @param visibility : whether or not loader should be
     */
    private void manageLoaderView(final boolean visibility) {
        loaderViewFirmware.setVisible(visibility);
        loaderViewFirmware.setManaged(visibility);
    }
}
