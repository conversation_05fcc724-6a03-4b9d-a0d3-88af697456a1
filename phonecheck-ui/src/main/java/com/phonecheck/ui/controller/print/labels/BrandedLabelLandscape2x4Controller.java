package com.phonecheck.ui.controller.print.labels;

import com.phonecheck.model.device.*;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.print.brother.BrotherLabelRoll;
import com.phonecheck.model.print.dymo.DymoLabelRoll;
import com.phonecheck.model.print.label.LabelOrientation;
import com.phonecheck.model.print.label.LabelSizeUnit;
import com.phonecheck.model.print.label.LabelVariant;
import com.phonecheck.model.print.zebra.ZebraLabelRoll;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.OemStatus;
import com.phonecheck.model.util.DeviceCosmeticResultsUtil;
import com.phonecheck.model.util.FunctionalityStatusUtil;
import javafx.fxml.FXML;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.VBox;
import javafx.scene.text.Text;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Locale;

import static com.phonecheck.model.constants.LabelConstants.*;
import static com.phonecheck.model.util.DateFormatUtil.DATE_FORMAT_DASH_YMD;

@Component
@Scope("prototype")
public class BrandedLabelLandscape2x4Controller extends AbstractLabelController {

    @FXML
    private Text user;
    @FXML
    private VBox imeiElementLayout;
    @FXML
    private VBox customElementLayout;
    @FXML
    private VBox serialLayout;
    @FXML
    private HBox gradeLayout;
    @FXML
    private HBox firmwareLayout;
    @FXML
    private HBox batteryHealthPercentageLayout;
    @FXML
    private Text title;
    @FXML
    private Text carrier;
    @FXML
    private ImageView networkLockImageView;
    @FXML
    private HBox colorLayout;
    @FXML
    private HBox versionLayout;
    @FXML
    private HBox esnLayout;
    @FXML
    private HBox deviceLockLayout;
    @FXML
    private HBox functionalityLayout;
    @FXML
    private HBox dateLayout;
    @FXML
    private HBox cosmeticsLayout;
    @FXML
    private HBox userLayout;
    @FXML
    private HBox customLayout;
    @FXML
    private Text customElementText;
    @FXML
    private ImageView pcLogo;
    @FXML
    private Text serial;
    @FXML
    private Text defects;
    @FXML
    private VBox defectsLayout;
    @FXML
    private Text pcQrLabel;
    @FXML
    private ImageView pcWebQr;
    @FXML
    private HBox rootPane;
    @FXML
    private Text batteryHealthPercentage;
    @FXML
    private Text userHeading;
    @FXML
    private Text custom;
    @FXML
    private Text customHeading;
    @FXML
    private Text imeiElementText;
    @FXML
    private ImageView imeiBarcodeImageView;
    @FXML
    private ImageView modelBarcodeImageView;
    @FXML
    private ImageView serialBarcodeImageView;
    @FXML
    private Text grade;
    @FXML
    private Text gradeHeading;
    @FXML
    private Text color;
    @FXML
    private Text version;
    @FXML
    private Text esn;
    @FXML
    private Text firmware;
    @FXML
    private Text firmwareHeading;
    @FXML
    private Text cosmeticsText;
    @FXML
    private Text deviceLock;
    @FXML
    private Text functionality;
    @FXML
    private Text date;
    @FXML
    private Text versionHeading;
    @FXML
    private Text colorHeading;
    @FXML
    private Text fmiHeading;
    @FXML
    private Text esnHeading;
    @FXML
    private Text functionalityHeading;
    @FXML
    private Text batteryHeading;
    @FXML
    private Text cosmeticsHeading;
    @FXML
    private Text dateHeading;
    @FXML
    private HBox vendorInvoiceLayout;
    @FXML
    private Text vendorHeading;
    @FXML
    private Text vendorInvoice;
    @FXML
    private HBox portNumberLayout;
    @FXML
    private Text portNumberHeading;
    @FXML
    private Text portNumber;
    @FXML
    private HBox erasureLayout;
    @FXML
    private Text erasureText;
    @FXML
    private ImageView fullyFunctionalLogo;
    @FXML
    private HBox oemLayout;
    @FXML
    private Text oemHeading;
    @FXML
    private ImageView oemStatusImage;
    @FXML
    private HBox batteryCycleLayout;
    @FXML
    private Text batteryCycleText;

    @Override
    public Pane getRootPane() {
        return rootPane;
    }

    @Override
    public float getWidth() {
        return 4;
    }

    @Override
    public float getHeight() {
        return 2;
    }

    @Override
    public LabelSizeUnit getSizeUnit() {
        return LabelSizeUnit.INCH;
    }

    @Override
    public LabelOrientation getDefaultOrientation() {
        return LabelOrientation.LANDSCAPE;
    }

    @Override
    public int getXPosition() {
        return 11;
    }

    @Override
    public int getYPosition() {
        return 5;
    }

    @Override
    public String getLabelName() {
        return LabelVariant.BRANDED_LABEL_LANDSCAPE_2X4.getName();
    }

    @Override
    public String getLabelFileName() {
        return LabelVariant.BRANDED_LABEL_LANDSCAPE_2X4.getFileName();
    }

    // ==================================================================
    //                       Zebra Specific Methods
    // ==================================================================

    @Override
    public ZebraLabelRoll getLabelRollForZebra() {
        return ZebraLabelRoll.ROLL_PORTRAIT_2x4;
    }

    // ==================================================================
    //                       Dymo Specific Methods
    // ==================================================================

    @Override
    public DymoLabelRoll getLabelRollForDymo() {
        return DymoLabelRoll.ROLL_2x4_OR_4x2;
    }

    // ==================================================================
    //                       Brother Specific Methods
    // ==================================================================

    @Override
    public List<BrotherLabelRoll> getLabelRollsForBrother() {
        return List.of(BrotherLabelRoll.ROLL_2x4_OR_4x2_TAPE, BrotherLabelRoll.ROLL_2x4_OR_4x2_LABEL);
    }

    // ==================================================================
    //                     Label's UI Specific Methods
    // ==================================================================

    @Override
    public void initializeViews(final Device device, final PrintMode printMode) {
        // Setting device information to views
        setDeviceDetailOnLabel(device);
        displayDeviceTestResultsOnLabel(device, printMode);
        displayViewBasedOnLocalCustomization(device);
        displayImagesOnLabel(device);
        setPaneBorder(defectsLayout);
    }

    /**
     * Setting the device detail on label elements,
     * or hide the label elements if the given value is null or empty.
     *
     * @param device the iPhone device object containing the device detail
     */
    private void setDeviceDetailOnLabel(final Device device) {
        //TODO: Later we will set all this device info on the basis of local customization,
        // Initially we assume that all customization enabled at this stage,
        // if content of any element is empty, UI element will be hide with its container
        // this logic will implement on all UI elements

        if (device.getModel() != null) {
            StringBuilder deviceTitleBuilder =
                    new StringBuilder(String.format("%s", device.getModel().toUpperCase(Locale.ROOT)));
            if (device.getDiskSize() != null && device.getDiskSize().getSize() != null) {
                deviceTitleBuilder.append(String.format("(%s)", device.getDiskSize()));
            }
            String deviceTitle = StringUtils.abbreviate(deviceTitleBuilder.toString(), 30);
            title.setText(deviceTitle);
            title.setVisible(true);
        }
        if (device.getCarrier() != null) {
            String carrierName = StringUtils.abbreviate(device.getCarrier().toUpperCase(Locale.ROOT), 30);
            carrier.setText(carrierName);
            carrier.setVisible(true);
        }
        if (device.getSimLock() != null) {
            if (device.getSimLock()) {
                setIconToImageView(BrandedLabelLandscape2x4Controller.this,
                        networkLockImageView,
                        "lock-black.png");
            } else {
                setIconToImageView(BrandedLabelLandscape2x4Controller.this,
                        networkLockImageView,
                        "lock-black-unlocked.png");
            }
        }
        if (device.getColor() != null) {
            color.setText(device.getColor().toUpperCase(Locale.ROOT));
            colorLayout.setVisible(true);
        }
        if (device.getEsnStatus() != null) {
            esn.setText(getEsnImageTextResponseService().getEsnImageTextForJapanese(device,
                    getUiInMemoryStore().getCurrentLanguage()));
            esnLayout.setVisible(true);
        }
        if (device.getPortNumber() != null && device.getPortNumber() != -1) {
            portNumber.setText(String.valueOf(device.getPortNumber() + 1));
            portNumberLayout.setVisible(true);
        }
        if (StringUtils.isNotBlank(device.getVendorName())
                && !device.getVendorName().equalsIgnoreCase(VENDOR_NAME)) {
            vendorInvoiceLayout.setVisible(true);
            vendorInvoice.setText(device.getVendorName());
        }
        if (StringUtils.isNotBlank(device.getStationId())) {
            userLayout.setVisible(true);
            String userTesterName = "";
            if (StringUtils.isNotBlank(device.getTesterName())) {
                userTesterName = "/" + device.getTesterName();
            }
            user.setText(device.getStationId() + userTesterName);
        }
        boolean shouldErasedFlagBeVisible = DeviceRestoreStatus.RESTORE_SUCCESS_RECEIVED == device.getRestoreStatus()
                || DeviceRestoreStatus.RESTORE_COMPLETED == device.getRestoreStatus();
        final String isErased = Boolean.TRUE.equals(device.getIsErasePerformed()) ? "Yes" : (DeviceRestoreStatus
                .RESTORE_SUCCESS_RECEIVED.equals(device.getRestoreStatus()) || DeviceRestoreStatus.RESTORE_COMPLETED.
                equals(device.getRestoreStatus())) ? "Yes" : "No";

        if (Boolean.TRUE.equals(device.getIsErasePerformed())) {
            erasureLayout.setVisible(true);
            erasureText.setText(isErased);
        } else if (Boolean.FALSE.equals(device.getIsErasePerformed())) {
            erasureLayout.setVisible(true);
            erasureText.setText(isErased);
        } else {
            erasureLayout.setVisible(shouldErasedFlagBeVisible);
            erasureText.setText(isErased);
        }

        date.setText(DATE_FORMAT_DASH_YMD.format(new Date()));

        if (device instanceof IosDevice iosDevice) {
            if (iosDevice.getProductVersion() != null) {
                version.setText(iosDevice.getProductVersion());
                versionLayout.setVisible(true);
            }
            if (iosDevice.getActivationStatus() != null) {
                deviceLock.setText(iosDevice.getActivationStatus().name());
                deviceLockLayout.setVisible(true);
            }
            if (iosDevice.getBatteryInfo() != null && iosDevice.getBatteryInfo().getHealthPercentage() > 0) {
                batteryHealthPercentage.setText((int) iosDevice.getBatteryInfo().getHealthPercentage() + "%");
                batteryHealthPercentageLayout.setVisible(true);
            }
            if (iosDevice.getBatteryInfo() != null && iosDevice.getBatteryInfo().getCycle() > 0) {
                batteryCycleLayout.setVisible(true);
                batteryCycleText.setText(String.valueOf(iosDevice.getBatteryInfo().getCycle()));
            }
            if (iosDevice.getOverallOemStatus() != null) {
                oemLayout.setVisible(true);
                if (OemStatus.GENUINE.equals(iosDevice.getOverallOemStatus())) {
                    setImageToImageView(BrandedLabelLandscape2x4Controller.this,
                            oemStatusImage, "fully-functional-tick.png");
                } else if (OemStatus.NOT_GENUINE.equals(iosDevice.getOverallOemStatus())) {
                    setImageToImageView(BrandedLabelLandscape2x4Controller.this,
                            oemStatusImage, "black-close-icon.png");
                } else {
                    setImageToImageView(BrandedLabelLandscape2x4Controller.this,
                            oemStatusImage, "black-warning-icon.png");
                }
            }
        } else if (device instanceof AirpodsDevice airpodsDevice) {
            if (airpodsDevice.getOverallOemStatus() != null) {
                oemLayout.setVisible(true);
                if (OemStatus.GENUINE.equals(airpodsDevice.getOverallOemStatus())) {
                    setImageToImageView(BrandedLabelLandscape2x4Controller.this,
                            oemStatusImage, "fully-functional-tick.png");
                } else if (OemStatus.NOT_GENUINE.equals(airpodsDevice.getOverallOemStatus())) {
                    setImageToImageView(BrandedLabelLandscape2x4Controller.this,
                            oemStatusImage, "black-close-icon.png");
                } else {
                    setImageToImageView(BrandedLabelLandscape2x4Controller.this,
                            oemStatusImage, "black-warning-icon.png");
                }
            }
        } else if (device instanceof AndroidDevice androidDevice) {
            if (androidDevice.getOsMajorVersion() > 0.0) {
                version.setText(String.valueOf(androidDevice.getOsMajorVersion()));
                versionLayout.setVisible(true);
            }
            if (androidDevice.getBatteryInfo() != null && androidDevice.getBatteryInfo().getHealthPercentage() > 0) {
                batteryHealthPercentage.setText((int) androidDevice.getBatteryInfo().getHealthPercentage() + "%");
                batteryHealthPercentageLayout.setVisible(true);
            }
        }
    }

    /**
     * Displays the test results of device on respective labels.
     *
     * @param device The iPhone device object for which the test results are displayed.
     * @param printMode print mode of the print operation.
     */
    private void displayDeviceTestResultsOnLabel(final Device device,
                                                 final PrintMode printMode) {

        DeviceTestResult deviceTestResult = device.getDeviceTestResult();

        if (deviceTestResult != null) {
            // Triple <cosmetic Result, Failed cosmetic result, Passed cosmetic result>
            Triple<String, String, String> cosmeticFailedPassedResult =
                    DeviceCosmeticResultsUtil.getAllCosmeticTestResults(deviceTestResult,
                            getUiInMemoryStore().getAssignedCloudCustomization());

            if (deviceTestResult.getTestResults() != null) {
                String defectResultsToBePrinted = getDefectsResultsText(
                        cosmeticFailedPassedResult, printMode, deviceTestResult);
                defects.setText(defectResultsToBePrinted);
                defectsLayout.setVisible(true);
            }
            if (cosmeticFailedPassedResult != null) {
                cosmeticsText.setText(getValueForField(cosmeticFailedPassedResult.getLeft()));
                cosmeticsLayout.setVisible(true);
            }
        }

        // Set functionality status
        String functionalityStatus = FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                getUiInMemoryStore().getAssignedCloudCustomization());
        functionality.setText(functionalityStatus.toUpperCase(Locale.ROOT));
        // show fully functional image if device is fully functional
        if (FunctionalityStatusUtil.FULLY_FUNCTIONAL.equalsIgnoreCase(functionalityStatus)) {
            setImageToImageView(BrandedLabelLandscape2x4Controller.this, fullyFunctionalLogo,
                    "fully-functional-tick.png");
        } else if (FunctionalityStatusUtil.SEE_NOTES.equalsIgnoreCase(functionalityStatus)) {
            setImageToImageView(BrandedLabelLandscape2x4Controller.this, fullyFunctionalLogo, "black-close-icon.png");
        } else if (FunctionalityStatusUtil.PENDING.equalsIgnoreCase(functionalityStatus)) {
            setImageToImageView(BrandedLabelLandscape2x4Controller.this, fullyFunctionalLogo, "black-warning-icon.png");
        }
        fullyFunctionalLogo.setVisible(true);
    }

    /**
     * Displays the appropriate view based on local customization settings
     *
     * @param device the iPhone device object containing the device detail
     */
    private void displayViewBasedOnLocalCustomization(final Device device) {
        //TODO: assumes that all customization is enabled initially and updates
        // the visibility and contents of the UI elements based on the actual customization
        // settings.

        if (device.getGrade() != null) {
            grade.setText(device.getGrade());
            gradeLayout.setVisible(true);
        }
        if (device.getDeviceLock() != null) {
            deviceLock.setText(DeviceLock.ON.equals(device.getDeviceLock())
                    ? LOCKED : UNLOCKED);
            deviceLockLayout.setVisible(true);
        }

        if (device instanceof IosDevice iosDevice) {
            String custom1 = StringUtils.isNotBlank(iosDevice.getCustom1()) ?
                    iosDevice.getCustom1() : iosDevice.getRegulatoryModelNumber();
            if (StringUtils.isNotBlank(custom1)) {
                custom.setText(custom1);
                customLayout.setVisible(true);
            }
        } else if (device instanceof AndroidDevice androidDevice) {
            if (StringUtils.isNotBlank(androidDevice.getCustom1())) {
                custom.setText(device.getCustom1());
                customLayout.setVisible(true);
            }
        }
    }

    /**
     * generate and display barcode and qrcode on label
     *
     * @param device the iPhone device object containing the device detail
     */
    private void displayImagesOnLabel(final Device device) {
        Image image;
        int resolution = 500;
        // Setting imei barcode image
        final String resolvedString;
        // if imei is empty then display serial in its place
        if (StringUtils.isNotBlank(device.getImei())) {
            resolvedString = device.getImei();
        } else {
            resolvedString = device.getSerial();
        }
        if (StringUtils.isNotBlank(resolvedString)) {
            if (resolvedString.length() > 15) {
                image = generateBarcodeFromData(resolvedString.substring(0, 15), resolution);
            } else {
                image = generateBarcodeFromData(resolvedString, resolution);
            }
            if (image != null && !(device instanceof AirpodsDevice)) {
                imeiElementText.setText(resolvedString);
                imeiBarcodeImageView.setImage(image);
                imeiElementLayout.setVisible(true);
            }
        }

        // Setting model barcode image
        if (device.getModelNo() != null) {
            String modelInfo = device.getModelNo();

            if (device instanceof IosDevice iosDevice) {
                if (iosDevice.getRegionInfo() != null) {
                    modelInfo += iosDevice.getRegionInfo();
                }
            }

            image = generateBarcodeFromData(modelInfo, resolution);
            if (image != null) {
                customElementText.setText(modelInfo);
                modelBarcodeImageView.setImage(image);
                customElementLayout.setVisible(true);
            }
        }

        // Setting serial barcode image
        if (device.getSerial() != null) {
            image = generateBarcodeFromData(device.getSerial(), resolution);
            if (image != null) {
                serial.setText(device.getSerial());
                serialBarcodeImageView.setImage(image);
                serialLayout.setVisible(true);
            }
        }

        //Generate QR code
        Image qrCodeImage = generateQRCodeFromData(device.getGuid() == null ? PHONE_CHECK_URL : device.getGuid(),
                250);
        if (qrCodeImage != null) {
            pcWebQr.setImage(qrCodeImage);
        }

        // Setting logo
        setImageToImageView(BrandedLabelLandscape2x4Controller.this, pcLogo, "phonecheck-logo.png");
    }
}
