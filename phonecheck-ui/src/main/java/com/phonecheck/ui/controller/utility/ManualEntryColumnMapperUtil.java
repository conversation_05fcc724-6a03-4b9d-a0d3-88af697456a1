package com.phonecheck.ui.controller.utility;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ManualEntryColumnMapperUtil {
    public static final Map<String, String> MANUAL_ENTRY_COLUMN_ORDER_MAP = new LinkedHashMap<>();

    static {
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("transactionId", "Transaction Id");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("vendorName", "Vendor");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("invoiceNo", "Invoice");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("imei", "IMEI");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("serial", "Serial");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("os", "OS");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("make", "Make");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("model", "Model");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("gb", "GB");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("color", "Color");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("modelNumber", "Model No.");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("regulatoryModelNumber", "Regulatory Model");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("carrier", "Carrier");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("unlockStatus", "Unlock Status");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("esn", "ESN");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("deviceLock", "FMI/FRP");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("grade", "Grade");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("cosmetic", "Cosmetic");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("bhPercentage", "BH%");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("notes", "Notes");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("working", "Working");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("failed", "Failed");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("lpn", "LPN");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("custom1", "Custom1");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("skuCode", "SKU Code");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("firmware", "Firmware");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("ram", "Ram");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("appleId", "Apple ID");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("rooted", "Rooted");
        MANUAL_ENTRY_COLUMN_ORDER_MAP.put("defectsCode", "Defects Code");
    }

    public static final String OS_FIELD = "os";
    public static final String MAKE_FIELD = "make";
    public static final String MODEL_FIELD = "model";
    public static final String GB_FIELD = "gb";
    public static final String COLOR_FIELD = "color";
    public static final String MODEL_NO_FIELD = "modelNumber";
    public static final String REGULATORY_MODEL_FIELD = "regulatoryModelNumber";
    public static final String CARRIER_FIELD = "carrier";
    public static final String UNLOCK_STATUS_FIELD = "unlockStatus";
    public static final String ESN_FIELD = "esn";
    public static final String DEVICE_LOCK_FIELD = "deviceLock";
    public static final String GRADE_FIELD = "grade";
    public static final String COSMETIC_FIELD = "cosmetic";
    public static final String BH_FIELD = "bhPercentage";
    public static final String NOTES_FIELD = "notes";
    public static final String WORKING_FIELD = "working";
    public static final String FAILED_FIELD = "failed";
    public static final String LPN_FIELD = "lpn";
    public static final String CUSTOM1_FIELD = "custom1";
    public static final String SKU_FIELD = "skuCode";
    public static final String FIRMWARE_FIELD = "firmware";
    public static final String RAM_FIELD = "ram";
    public static final String APPLE_ID_FIELD = "appleId";
    public static final String ROOTED_FIELD = "rooted";
    public static final String DEFECTS_CODE_FIELD = "defectsCode";

    public static final List<String> TEXT_FIELDS = List.of(
            MODEL_FIELD, ESN_FIELD, MODEL_NO_FIELD, REGULATORY_MODEL_FIELD, NOTES_FIELD,
            FAILED_FIELD, LPN_FIELD, CUSTOM1_FIELD, SKU_FIELD, FIRMWARE_FIELD, APPLE_ID_FIELD, DEFECTS_CODE_FIELD);

    public static final List<String> OS_LIST = Arrays.asList(
            "Android", "iOS"
    );
    public static final List<String> MAKE_LIST = Arrays.asList(
            "Apple", "Samsung", "LG", "HTC", "Sony", "Huawei", "Blu", "Motorola", "Blackberry", "ZTE",
            "Alcatel", "Kyocera", "Nokia", "Microsoft", "Google", "OnePlus", "Xiaomi", "Amazon", "Asus"
    );
    public static final List<String> UNLOCK_STATUS_LIST = Arrays.asList(
            "LK", "UNLK"
    );
    public static final List<String> DEVICE_LOCK_STATUS_LIST = Arrays.asList(
            "On", "Off", "NA"
    );
    public static final List<String> GB_LIST = Arrays.asList(
           "4GB", "8GB", "16GB", "32GB", "64GB", "128GB", "256GB", "512GB", "1TB", "2TB", "4TB"
    );
    public static final List<String> CARRIER_LIST = Arrays.asList(
            "Unlocked", "Wifi Only", "AT&T", "Verizon", "Sprint", "T-Mobile", "Rogers"
    );
    public static final List<String> COLOR_LIST = Arrays.asList(
            "Color", "Black", "Gold", "Silver", "Blue", "Red",
            "Green", "Pink", "Brown", "Yellow", "White"
    );
    public static final List<String> BH_LIST = IntStream.rangeClosed(1, 100).mapToObj(String::valueOf)
            .collect(Collectors.toList());
    public static final List<String> WORKING_LIST = Arrays.asList(
            "Yes", "No", "Pending"
    );
    public static final List<String> RAM_LIST = Arrays.asList(
            "4", "6", "8", "10", "12", "16", "32", "64"
    );
    public static final List<String> ROOTED_LIST = Arrays.asList(
            "On", "Off"
    );
}
