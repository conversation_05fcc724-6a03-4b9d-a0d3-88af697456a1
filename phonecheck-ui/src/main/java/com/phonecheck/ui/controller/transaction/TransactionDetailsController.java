package com.phonecheck.ui.controller.transaction;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.TransactionResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.ui.DeviceBoxSettings;
import com.phonecheck.model.event.transaction.CloudDeviceLookupResponseEvent;
import com.phonecheck.model.event.transaction.TransactionContinueResponseEvent;
import com.phonecheck.model.event.transaction.TransactionDetailsResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.TransactionContinueRequestMessage;
import com.phonecheck.model.mqtt.messages.TransactionDetailsEditMessage;
import com.phonecheck.model.mqtt.messages.TransactionDetailsRequestMessage;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.event.PasswordProtectedAction;
import com.phonecheck.model.transaction.TransactionDisplayModel;
import com.phonecheck.model.transaction.TransactionDisplayModel.TransactionRecord;
import com.phonecheck.model.util.DateFormatUtil;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.LogicalModelMapper;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.ui.component.Toast;
import com.phonecheck.ui.controller.AbstractUiController;
import com.phonecheck.ui.controller.MainController;
import com.phonecheck.ui.controller.utility.ExportExcelUtil;
import com.phonecheck.ui.controller.utility.ManualEntryColumnMapperUtil;
import com.phonecheck.ui.controller.utility.TransactionColumnMapperUtil;
import javafx.animation.TranslateTransition;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.image.ImageView;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.*;
import javafx.scene.text.Text;
import javafx.stage.FileChooser;
import javafx.stage.Stage;
import javafx.util.Duration;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.*;

import static com.phonecheck.model.constants.FileConstants.TRANSACTION_COLUMN_ORDER_FILE_NAME;

@Component
public class TransactionDetailsController extends AbstractUiController implements Initializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionDetailsController.class);

    public static final String UI_STAGE_NAME = "TransactionDetails";

    @FXML
    private AnchorPane root;
    @FXML
    private StackPane loaderViewStackPane;
    @FXML
    private ImageView backButtonImageView;
    @FXML
    private VBox mainContentVBox;
    @FXML
    private VBox vendorDetailsVBox;
    @FXML
    private Label vendorName;
    @FXML
    private Label invoiceNumber;
    @FXML
    private Label boxNumber;
    @FXML
    private Label quantityNumber;
    @FXML
    private Label currentTransactions;
    @FXML
    private Label totalTransactions;
    @FXML
    private ImageView transactionMenu;
    @FXML
    private TextField searchView;
    @FXML
    private VBox menuContainer;
    @FXML
    private HBox exportHBox;
    @FXML
    private HBox tableHBox;
    @FXML
    private AnchorPane detailPaneView;
    @FXML
    private ScrollPane detailViewScrollPane;
    @FXML
    private Button continueTransactionButton;
    @FXML
    private Button changeTransactionButton;
    @FXML
    private HBox currentTransactionHBox;
    @FXML
    private VBox dataContainer;
    @FXML
    private AnchorPane scrollViewAnchorPane;
    @FXML
    private TextField imeiDetailView;
    @FXML
    private TextField serialDetailView;
    @FXML
    private Button editButton;
    @FXML
    private Button saveButton;
    @FXML
    private Button cancelButton;
    @FXML
    private Button resetColumnsButton;

    @FXML
    private ImageView previousRecord;

    @FXML
    private ImageView nextRecord;

    @Setter
    private Transaction transaction;
    @Setter
    private boolean loadedFromMenu;
    @Setter
    private Stage stage;
    @Setter
    private boolean loadedFromCloudLookup;

    @Autowired
    private ExportExcelUtil exportExcelUtil;
    @Autowired
    private MainController mainController;
    @Autowired
    private UiInMemoryStore uiInMemoryStore;
    @Autowired
    private LogicalModelMapper logicalModelMapper;
    @Autowired
    private SupportFilePath supportFilePath;
    @Autowired
    private FileUtil fileUtil;
    @Autowired
    private ObjectMapper objectMapper;

    private TableView<TransactionRecord> transactionDetailsTableView;
    private ObservableList<TransactionRecord> observableTransactionRecordList;
    private TranslateTransition detailViewSlideAnimation;
    private boolean detailViewOpen;
    private String selectedDeviceImei;
    private String selectedDeviceId;
    private String selectedTransactionId;
    private String selectedDeviceSerial;
    private String selectedDeviceColor;
    private int selectedRecordIndex;
    private boolean isManualEntry;

    private List<String> connectDevicesSerials;

    private List<TransactionRecord> selectedRecords;

    public static final List<String> EDITABLE_COMBO_BOX_IDS = Arrays.asList(
            "color", "grade", "unlockStatus", "carrier"
    );

    public static final List<String> EDITABLE_COMBO_BOX_IDS_FOR_MANUAL_ENTRY = Arrays.asList(
            "make", "deviceLock", "memory", "working", "ram", "rooted", "os", "batteryHealthPercentage"
    );

    public static final List<String> EDITABLE_TEXT_FIELDS_IDS = Arrays.asList(
            "lpn", "custom1", "notes"
    );

    public static final List<String> EDITABLE_MANUAL_ENTRY_TEXT_FIELDS_IDS = Arrays.asList(
            "model", "modelNo", "regulatoryModelNumber", "esn",
            "failed", "skuCode", "firmware", "appleId", "defectsCode", "cosmetics"
    );

    public static final List<String> COLUMNS_TO_AUTO_ADJUST = Arrays.asList(
            "Failed", "Passed", "Cosmetics"
    );

    private static final Map<String, List> COMBO_BOX_TO_LIST_MAP = new HashMap<>();

    private static final int RECORDS_PER_PAGE = 100;

    static {
        COMBO_BOX_TO_LIST_MAP.put("color", DeviceBoxSettings.DEVICE_COLORS);
        COMBO_BOX_TO_LIST_MAP.put("grade", DeviceBoxSettings.DEVICE_GRADES);
        COMBO_BOX_TO_LIST_MAP.put("carrier", DeviceBoxSettings.DEVICE_CARRIERS);
        COMBO_BOX_TO_LIST_MAP.put("unlockStatus", List.of("LK", "UNLK"));
    }

    private TransactionRecord selectedTransactionDeviceRecord;
    private int dataOffset = 0;
    private Map<String, String> columnOrderMap = new LinkedHashMap<>();


    // ==================================================================
    //                       FXML Action Listeners
    // ==================================================================

    @Override
    public void initialize(final URL location, final ResourceBundle resources) {
        if (!loadedFromCloudLookup) {
            requestBackendForTransactionDetails(dataOffset);
        }
        dataOffset = 0;
        selectedRecords = new ArrayList<>();
        connectDevicesSerials = new ArrayList<>();
        observableTransactionRecordList = FXCollections.observableArrayList();

        Runnable runnable = () -> {
            backButtonImageView.setVisible(!loadedFromMenu);
            backButtonImageView.setManaged(!loadedFromMenu);

            if (transaction == null ||
                    transaction.getTransactionId() == uiInMemoryStore.getTransaction().getTransactionId()) {
                continueTransactionButton.setVisible(false);
                continueTransactionButton.setManaged(false);
                boolean visibility = !loadedFromCloudLookup;
                vendorDetailsVBox.setVisible(visibility);
                vendorDetailsVBox.setManaged(visibility);
                currentTransactionHBox.setVisible(visibility);
                currentTransactionHBox.setManaged(visibility);
                changeTransactionButton.setVisible(visibility);
            }

            // setup views
            setupTableView();
            setupTableViewColumns();
            setupListeners();
        };
        runOnFxThread(runnable);
    }

    // ==================================================================
    //                          UI related Methods
    // ==================================================================

    /**
     * Continue transaction click listener to continue the currently selected transaction
     */
    @FXML
    public void onContinueTransactionClicked() {
        requestBackendToContinueTransaction();
    }


    /**
     * Change transaction click listener to change the currently selected transactions
     */
    @FXML
    public void onChangeTransactionClicked() {
        mainController.loadTransactionChangeController(selectedRecords);
    }

    /**
     * Handles the menu button click event to handle transactions operations.
     * Toggles the visibility of the menu.
     *
     * @param event
     */
    @FXML
    public void menuClicked(final MouseEvent event) {
        showOrHideMenu();
        event.consume();
    }

    /**
     * To go to very first page of
     * all previous transaction list
     */
    @FXML
    public void onLeastPreviousClicked() {
    }

    /**
     * To go to previous transaction page
     */
    @FXML
    public void onPreviousClicked() {
        if (dataOffset >= RECORDS_PER_PAGE) {
            dataOffset = dataOffset - RECORDS_PER_PAGE;
            requestBackendForTransactionDetails(dataOffset);
        } else {
            previousRecord.setVisible(false);
        }
    }

    /**
     * To go to last transactions page from
     * all previous transactions list
     */
    @FXML
    public void onLeastNextClicked() {
    }

    /**
     * To go to next transaction page
     */
    @FXML
    public void onNextClicked() {
        dataOffset = dataOffset + RECORDS_PER_PAGE;
        requestBackendForTransactionDetails(dataOffset);
    }

    /**
     * Export all previous transactions into a file
     *
     * @param event
     */
    @FXML
    public void onExportDetailClicked(final MouseEvent event) {
        showOrHideMenu();
        event.consume();
        requestBackendForTransactionDetails(-1);
    }

    /**
     * To close previous transaction view
     */
    @FXML
    public void onBackClicked() {
        dataOffset = 0;
        mainController.loadTransactionHistory();
    }

    /**
     * To close Detail View
     */
    @FXML
    public void onDetailViewClosed() {
        if (detailViewOpen) {
            transactionDetailsTableView.getSelectionModel().clearSelection();
            detailViewOpen = false;
            detailViewSlideAnimation.setByX(504);
            detailViewSlideAnimation.setOnFinished(event -> detailPaneView.setVisible(false));
            detailViewSlideAnimation.play();
            setEditOptionsVisibility(false);
            setControlsEditable(false);
        }
    }

    /**
     * To print the detail
     */
    @FXML
    public void onPrintClicked() {
        Runnable runnable = () -> {
            try {
                Device device = TransactionColumnMapperUtil.
                        getDeviceDetailFromTransaction(selectedTransactionDeviceRecord);
                LOGGER.info("Print requested from the transaction details page for device id: {} serial: {}",
                        device.getId(), device.getSerial());
                MainController mainController = getApplicationContext().getBean(MainController.class);
                mainController.setPrintRequestMode(PrintMode.TRANSACTION);
                openPrintDialogue(mainController.getStackPane(), false, device, true);
            } catch (Exception e) {
                LOGGER.error("Error occurred while opening print dialogue", e);
            }
        };
        runOnFxThread(runnable);
    }

    /**
     * Resets the column order to the default configuration.
     * Updates the columnOrderMap to its default order, saves it to a file,
     * and refreshes the view.
     */
    @FXML
    public void resetColumnOrderClick() {
        Runnable runnable = () -> {
            try {
                LOGGER.info("Try to reset the column order");
                // Retrieve the current items before resetting
                ObservableList<TransactionRecord> currentItems =
                        FXCollections.observableArrayList(transactionDetailsTableView.getItems());
                // Write default order to file and reopen the stage
                columnOrderMap = TransactionColumnMapperUtil.getDeviceColumnOrderMap();
                writeUpdatedColumOrderListToFile();
                // Remove older column from tableview
                transactionDetailsTableView.getColumns().clear();
                // Add rearranged columns to tableview
                setupTableViewColumns();
                // Add items/data to tableview again
                transactionDetailsTableView.setItems(currentItems);
            } catch (Exception e) {
                LOGGER.error("Error while resetting column order list", e);
            }
        };
        runOnFxThread(runnable);
    }

    /**
     * Sets up table view
     */
    private void setupTableView() {
        transactionDetailsTableView = new TableView<>();
        HBox.setHgrow(transactionDetailsTableView, Priority.ALWAYS);

        String tableViewCss = Objects
                .requireNonNull(getClass().getResource("/com/phonecheck/style/tableview.css")).toExternalForm();
        String scrollbarCss = Objects
                .requireNonNull(getClass().getResource("/com/phonecheck/style/scrollbar.css")).toExternalForm();

        transactionDetailsTableView.getStylesheets().addAll(tableViewCss, scrollbarCss);
        tableHBox.getChildren().add(transactionDetailsTableView);

        Label placeholder = new Label(getLocalizationService().
                getLanguageSpecificText("noDeviceRecordTransaction"));
        placeholder.setStyle("-fx-font-size: 15;");

        // Set the placeholder to the TableView
        transactionDetailsTableView.setPlaceholder(placeholder);
    }

    /**
     * Sets up the columns of a TableView for displaying previous transaction data.
     * Iterates through the provided list of column names and creates
     * corresponding TableColumn instances with cell.
     */
    private void setupTableViewColumns() {
        if (!loadedFromMenu) {
            addCheckBoxesForRecords();
        }
        Map<String, String> orderMap = readColumnsFromFile();
        columnOrderMap = orderMap;

        // Create a temporary list to hold the columns
        List<TableColumn<TransactionRecord, String>> newColumns = new ArrayList<>();

        for (Map.Entry<String, String> entry : orderMap.entrySet()) {
            TableColumn<TransactionRecord, String> column = new TableColumn<>(entry.getValue());
            column.setCellValueFactory(new PropertyValueFactory<>(entry.getKey()));
            column.setPrefWidth(column.getPrefWidth() * 2.5);

            // Code to automatically adjust the width of the column to it's content
            if (COLUMNS_TO_AUTO_ADJUST.contains(entry.getValue())) {
                transactionDetailsTableView.itemsProperty().addListener((observable, oldValue, newValue)
                        -> autoSizeColumn(column));
            }

            newColumns.add(column);
        }
        // Clear the existing columns and add all new columns at once
        transactionDetailsTableView.getColumns().addAll(newColumns);
    }

    /**
     * Adjust column width according to its content size
     *
     * @param column table column
     */
    private void autoSizeColumn(final TableColumn<TransactionRecord, String> column) {
        double prefWidth = 0;
        for (TransactionRecord item : transactionDetailsTableView.getItems()) {
            if (item != null && column.getCellData(item) != null) {
                Text text = new Text(column.getCellData(item));
                double width = text.getLayoutBounds().getWidth() + 10;
                if (width > prefWidth && width > (column.getPrefWidth() * 2.5)) {
                    prefWidth = width;
                }
            }
        }

        if (prefWidth != 0) {
            column.setPrefWidth(prefWidth);
        }
    }

    /**
     * Show and hide the visibility of transactions menu
     */
    private void showOrHideMenu() {
        if (menuContainer.isVisible()) {
            menuContainer.setVisible(false);
            menuContainer.setManaged(false);
        } else {
            menuContainer.setVisible(true);
            menuContainer.setManaged(true);
        }
    }

    // ==================================================================
    //                       FXML Action Listeners
    // ==================================================================

    /**
     * Transaction details root clicked listener
     */
    private final ChangeListener<Scene> rootClickedListener = new ChangeListener<>() {
        @Override
        public void changed(final ObservableValue observable, final Scene oldValue, final Scene newScene) {
            if (newScene != null) {
                newScene.addEventHandler(MouseEvent.MOUSE_CLICKED, e -> {
                    if (menuContainer.isVisible()) {
                        menuContainer.setVisible(false);
                        menuContainer.setManaged(false);
                    }
                });
            }
        }
    };

    /**
     * Search query text change listener
     */
    private final ChangeListener<String> searchQueryChangeListener = (observable, oldValue, searchAbleText)
            -> filterTableViewData(searchAbleText);

    /**
     * Sets up a listener for the search view input field.
     * The listener filters the data in the TableView based on the search input.
     */
    private void setupListeners() {
        // by default when window open detail view pane will be hide
        detailViewOpen = false;

        root.sceneProperty().addListener(rootClickedListener);

        searchView.textProperty().addListener(searchQueryChangeListener);

        transactionDetailsTableView.setRowFactory(tv -> {
            TableRow<TransactionRecord> row = new TableRow<>();
            row.setOnMousePressed(event -> {
                if (!row.isEmpty()) {
                    selectedTransactionDeviceRecord = row.getItem();
                    setUpDetailViewPosition();
                    handleSelectedItemClick(selectedTransactionDeviceRecord);
                }
            });
            return row;
        });

        // Listener to be called when rearranged table-view columns
        transactionDetailsTableView.getColumns().addListener((ListChangeListener<? super
                TableColumn<TransactionRecord, ?>>) change -> {
            while (change.next() && change.wasAdded()) {
                LOGGER.info("TableView columns rearranged");
                updateColumnOrderMap();
            }
        });

        removeListenerOnCloseStage();
    }

    /**
     * Removes listeners and clears data when the stage is closed.
     * Saves the latest column order to file if needed, removes listeners on UI components, clears items
     * and columns in the table, and resets UI elements to release resources.
     */
    private void removeListenerOnCloseStage() {
        stage.setOnHidden(event -> {
            // Create/Update latest column order list if needed
            writeUpdatedColumOrderListToFile();

            root.sceneProperty().removeListener(rootClickedListener);
            searchView.textProperty().removeListener(searchQueryChangeListener);

            transactionDetailsTableView.getItems().clear();

            transactionDetailsTableView.getColumns().clear();
            transactionDetailsTableView.setRowFactory(null);
            observableTransactionRecordList.clear();

            tableHBox.getChildren().remove(transactionDetailsTableView);
            transactionDetailsTableView = null;

            if (mainController.getCurrentlyOpenedTransactionStages().isEmpty()) {
                mainController.getStackPane().getScene().getRoot().setEffect(null);
            }

            stage = null;
        });
    }

    /**
     * Updates the column order map based on the current order in the TableView.
     * Builds a new map by matching column headers from the TableView with entries in the original map.
     * Ensures all original columns are included, appending any not reordered to maintain completeness.
     */
    private void updateColumnOrderMap() {
        Map<String, String> updatedColumnOrderMap = new LinkedHashMap<>();
        for (TableColumn<TransactionRecord, ?> column : transactionDetailsTableView.getColumns()) {
            String columnText = column.getText();
            for (Map.Entry<String, String> entry : columnOrderMap.entrySet()) {
                if (entry.getValue().equals(columnText)) {
                    updatedColumnOrderMap.put(entry.getKey(), columnText);
                    break;
                }
            }
        }

        // Add any remaining original columns that weren't reordered to maintain completeness
        for (Map.Entry<String, String> entry : columnOrderMap.entrySet()) {
            if (!updatedColumnOrderMap.containsKey(entry.getKey())) {
                updatedColumnOrderMap.put(entry.getKey(), entry.getValue());
            }
        }
        columnOrderMap.clear();
        columnOrderMap.putAll(updatedColumnOrderMap);
    }


    // ==================================================================
    //                    Methods related to data search
    // ==================================================================

    /**
     * Filters the data in the TableView based on the provided search text.
     *
     * @param searchQuery The search text
     */
    private void filterTableViewData(final String searchQuery) {
        ObservableList<TransactionRecord> filteredList = FXCollections.observableArrayList();

        for (TransactionRecord transaction : observableTransactionRecordList) {
            if (containsInAnyField(transaction, searchQuery)) {
                filteredList.add(transaction);
            }
        }
        transactionDetailsTableView.setItems(filteredList);
        exportHBox.setDisable(filteredList.size() == 0);
    }

    /**
     * Checks if the provided search text is contained
     * in any String field of the given transaction record response model.
     *
     * @param transaction The transaction to be checked.
     * @param searchQuery The search text to be searched for in the tableview fields.
     * @return True if the search text is found in any column of the tableview, false otherwise.
     */
    private boolean containsInAnyField(final TransactionRecord transaction, final String searchQuery) {
        for (Field field : TransactionRecord.class.getDeclaredFields()) {
            try {
                field.setAccessible(true);
                String fieldValue = String.valueOf(field.get(transaction));
                if (fieldValue != null && fieldValue.toLowerCase()
                        .contains(searchQuery.toLowerCase())) {
                    return true;
                }
            } catch (IllegalAccessException e) {
                LOGGER.error("Error while searching data in transaction device records");
            }
        }
        return false;
    }

    // ==================================================================
    //                  Methods related to detail view pane
    // ==================================================================

    /**
     * Handles the selection of an item by displaying its details in a detail view.
     *
     * @param selectedRecord The PreviousTransaction object representing the selected item.
     *                       Its details will be displayed in the detail view.
     */
    private void handleSelectedItemClick(final TransactionRecord selectedRecord) {
        // reset details on the detail page
        dataContainer.getChildren().clear();
        //open detail view pane with animation
        openDetailViewPane();

        isManualEntry = (selectedRecord.getManualEntry().equalsIgnoreCase("Yes"));

        //setting serial and imei values in detailView
        imeiDetailView.setText(selectedRecord.getImei());
        serialDetailView.setText(selectedRecord.getSerial());
        selectedDeviceImei = selectedRecord.getImei();
        selectedDeviceId = selectedRecord.getUdid();
        selectedTransactionId = selectedRecord.getTransactionId();
        selectedDeviceSerial = selectedRecord.getSerial();
        selectedDeviceColor = selectedRecord.getColor();
        selectedRecordIndex = observableTransactionRecordList.indexOf(selectedRecord);

        Map<String, String> orderMap = TransactionColumnMapperUtil.getDeviceColumnOrderMap();

        HBox currentRow = null;
        int i = 0;
        for (Map.Entry<String, String> entry : orderMap.entrySet()) {
            //Imei and serial already added at the top of the detail view
            if (entry.getValue().equalsIgnoreCase("IMEI") ||
                    entry.getValue().equalsIgnoreCase("Serial")) {
                continue;
            }
            if (i % 2 == 0) {
                currentRow = new HBox(10);
                currentRow.setPrefWidth(detailPaneView.getPrefWidth());
                currentRow.setAlignment(Pos.CENTER);
                dataContainer.getChildren().add(currentRow);
            }
            VBox vbox = getVBoxContainer(entry.getKey(), entry.getValue(), selectedRecord);
            currentRow.getChildren().add(vbox);
            i++;
        }
    }

    /**
     * Creates and returns a VBox container containing label and text field components for displaying a
     * heading and its associated value from a PreviousTransaction object.
     *
     * @param key            key to get value from object.
     * @param fieldHeading   The heading or label to be displayed.
     * @param selectedRecord The PreviousTransaction object from which to retrieve and display the associated value.
     * @return A VBox containing the heading label, a text field displaying the associated value,
     * and a separator to visually separate components.
     */
    private VBox getVBoxContainer(final String key, final String fieldHeading,
                                  final TransactionRecord selectedRecord) {
        VBox vbox = new VBox();
        vbox.setPrefWidth(detailPaneView.getPrefWidth() / 2);
        vbox.setPadding(new Insets(10));
        Label labelHeading = new Label(fieldHeading);
        String value = TransactionColumnMapperUtil.getPropertyValueFromRecordObject(selectedRecord, key);
        Separator separator = new Separator();
        if (EDITABLE_COMBO_BOX_IDS.contains(key) || EDITABLE_COMBO_BOX_IDS_FOR_MANUAL_ENTRY.contains(key)) {
            ComboBox<String> comboBox = new ComboBox<>();
            comboBox.setEditable(false);
            comboBox.setPromptText(key);
            comboBox.setValue(value);
            comboBox.setId(selectedRecord.getImei() + "_" + key);
            comboBox.getStyleClass().add("hide-arrow");
            comboBox.prefWidthProperty().bind(vbox.widthProperty());
            vbox.getChildren().addAll(labelHeading, comboBox, separator);
        } else {
            TextField labelValue = new TextField();
            labelValue.setStyle("-fx-background-color: transparent;");
            labelValue.setEditable(false);
            labelValue.setText(value);
            labelValue.setId(selectedRecord.getImei() + "_" + key);
            vbox.getChildren().addAll(labelHeading, labelValue, separator);
        }
        return vbox;
    }

    /**
     * Sets up the detail view container by adjusting
     * the layout and preferred height of various components.
     */
    private void setUpDetailViewPosition() {
        detailPaneView.setLayoutX(root.getWidth());
        detailPaneView.setPrefHeight(root.getHeight() - 8);
        scrollViewAnchorPane.setPrefHeight((root.getHeight()) - 130);
    }

    /**
     * Opens or closes the detail view with animation based on the current state.
     * If the detail view is closed, it will open it with a slide-in animation.
     * If the detail view is open, it will close it with a slide-out animation.
     */
    private void openDetailViewPane() {
        if (!detailViewOpen) {
            detailPaneView.setVisible(true);
            detailViewOpen = true;
            detailViewSlideAnimation = new TranslateTransition(Duration.seconds(0.5), detailPaneView);
            detailViewSlideAnimation.setByX(-504);
            detailViewSlideAnimation.play();
        }
    }

    /**
     * Called when edit button is clicked on Ui
     */
    @FXML
    private void onEditClicked() {
        if (uiInMemoryStore != null && uiInMemoryStore.getAssignedCloudCustomization() != null &&
                uiInMemoryStore.getAssignedCloudCustomization().getPasswordOptions() != null &&
                uiInMemoryStore.getAssignedCloudCustomization().getPasswordOptions().isEditTransaction()) {
            mainController.showPasswordPopup(PasswordProtectedAction.EDIT_TRANSACTION);
        } else {
            executeEditTransactionAction();
        }
    }

    public void executeEditTransactionAction() {
        setEditOptionsVisibility(true);
        setControlsEditable(true);
    }

    /**
     * Called when save button is clicked on Ui
     */
    @FXML
    private void onSaveClicked() {
        setEditOptionsVisibility(false);
        setControlsEditable(false);
        final Map<String, String> controlValues = getEditableControlValues();

        String deviceOs = getDeviceOs();

        Device device;
        if (StringUtils.containsIgnoreCase(deviceOs, "ios") ||
                StringUtils.containsIgnoreCase(deviceOs, "watchos") ||
                StringUtils.containsIgnoreCase(deviceOs, "ipados")) {
            device = new IosDevice();
        } else {
            device = new AndroidDevice();
        }

        device.setId(selectedDeviceId);

        // update the selected records edited values
        TransactionRecord transactionRecord = prepareTransactionRecord(controlValues);

        this.transaction = getTransactionFromRecord(transactionRecord);

        // refresh the view
        transactionDetailsTableView.refresh();

        final String topic = TopicBuilder.build(device, "edit-transaction", "request");

        TransactionDetailsEditMessage requestMessage = prepareRequestMessage(controlValues, transactionRecord);

        publishToMqttTopic(topic, requestMessage);
    }


    /**
     * Called when cancel button is clicked on Ui
     */
    @FXML
    private void onCancelClicked() {
        setEditOptionsVisibility(false);
        setControlsEditable(false);
    }

    /**
     * Controls visibility of edit options
     *
     * @param visibility flag
     */
    private void setEditOptionsVisibility(final boolean visibility) {
        saveButton.setVisible(visibility);
        saveButton.setManaged(visibility);
        cancelButton.setVisible(visibility);
        cancelButton.setManaged(visibility);
        editButton.setVisible(!visibility);
        editButton.setManaged(!visibility);
    }

    /**
     * Retrieves the values of editable fields
     *
     * @return map of controls and their values
     */
    @SuppressWarnings("unchecked")
    private Map<String, String> getEditableControlValues() {
        Map<String, String> controlValues = new HashMap<>();
        String value = null;
        for (String controlId : EDITABLE_COMBO_BOX_IDS) {
            ComboBox<String> comboBox = (ComboBox<String>) dataContainer.
                    lookup("#" + selectedDeviceImei + "_" + controlId);
            if (comboBox != null) {
                value = comboBox.getEditor().getText();
            }
            controlValues.put(controlId, value);
        }

        if (isManualEntry) {
            for (String controlId : EDITABLE_MANUAL_ENTRY_TEXT_FIELDS_IDS) {
                TextField textField = (TextField) dataContainer.lookup("#" + selectedDeviceImei + "_" + controlId);
                if (textField != null) {
                    value = textField.getText();
                    controlValues.put(controlId, value);
                }
            }

            for (String controlId : EDITABLE_COMBO_BOX_IDS_FOR_MANUAL_ENTRY) {
                ComboBox<String> comboBox = (ComboBox<String>) dataContainer.
                        lookup("#" + selectedDeviceImei + "_" + controlId);

                if (comboBox != null) {
                    value = comboBox.getValue();
                    controlValues.put(controlId, value);
                }
            }
        }
        for (String controlId : EDITABLE_TEXT_FIELDS_IDS) {
            TextField textField = (TextField) dataContainer.lookup("#" + selectedDeviceImei + "_" + controlId);
            if (textField != null) {
                value = textField.getText();
                controlValues.put(controlId, value);
            }
        }

        return controlValues;
    }

    /**
     * Controls edit ability of editable fields
     *
     * @param editable flag
     */
    @SuppressWarnings("unchecked")
    private void setControlsEditable(final boolean editable) {
        for (String controlId : EDITABLE_COMBO_BOX_IDS) {
            ComboBox<String> comboBox = (ComboBox<String>) dataContainer.
                    lookup("#" + selectedDeviceImei + "_" + controlId);
            if (editable) {
                if (controlId.equalsIgnoreCase("grade")) {
                    CloudCustomizationResponse customization = uiInMemoryStore.getAssignedCloudCustomization();
                    List<String> grades = (customization != null) ? customization.getGrading() : null;
                    comboBox.setItems(FXCollections.observableArrayList(grades != null ?
                            grades : DeviceBoxSettings.DEVICE_GRADES));
                } else if (controlId.equalsIgnoreCase("carrier")) {
                    CloudCustomizationResponse customization = uiInMemoryStore.getAssignedCloudCustomization();
                    List<String> carriers = (customization != null) ? customization.getCarriers() : null;

                    comboBox.setItems(FXCollections.observableArrayList(carriers != null ?
                            carriers : DeviceBoxSettings.DEVICE_CARRIERS));
                } else {
                    comboBox.setItems(FXCollections.observableArrayList(COMBO_BOX_TO_LIST_MAP.get(controlId)));
                }
                comboBox.setEditable(true);
            } else {
                // if color field is set blank change it back to its previous value
                if (StringUtils.isBlank(comboBox.getEditor().getText()) && "color".equalsIgnoreCase(controlId)) {
                    comboBox.setItems(FXCollections.observableArrayList(selectedDeviceColor));
                    comboBox.setValue(selectedDeviceColor);
                } else {
                    String comboValue = StringUtils.isBlank(comboBox.getEditor().getText()) ? comboBox.getValue()
                            : comboBox.getEditor().getText();
                    comboBox.setItems(FXCollections.observableArrayList(comboValue));
                    comboBox.setValue(comboValue);
                }
                comboBox.setEditable(false);
            }
            if (comboBox.getStyleClass().contains("hide-arrow")) {
                comboBox.getStyleClass().remove("hide-arrow");
            } else {
                comboBox.getStyleClass().add("hide-arrow");
            }
        }

        if (isManualEntry) {
            serialDetailView.setEditable(editable);
            imeiDetailView.setEditable(editable);

            for (String controlId : EDITABLE_MANUAL_ENTRY_TEXT_FIELDS_IDS) {
                TextField textField = (TextField) dataContainer.lookup("#" + selectedDeviceImei + "_" + controlId);
                textField.setEditable(editable);
            }

            for (String controlId : EDITABLE_COMBO_BOX_IDS_FOR_MANUAL_ENTRY) {

                ComboBox<String> comboBox = (ComboBox<String>) dataContainer.
                        lookup("#" + selectedDeviceImei + "_" + controlId);

                if (controlId.equalsIgnoreCase("make")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.MAKE_LIST));
                } else if (controlId.equalsIgnoreCase("os")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.OS_LIST));
                } else if (controlId.equalsIgnoreCase("deviceLock")) {
                    comboBox.setItems(FXCollections.observableArrayList(
                            ManualEntryColumnMapperUtil.DEVICE_LOCK_STATUS_LIST));
                } else if (controlId.equalsIgnoreCase("memory")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.GB_LIST));
                } else if (controlId.equalsIgnoreCase("batteryHealthPercentage")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.BH_LIST));
                } else if (controlId.equalsIgnoreCase("working")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.WORKING_LIST));
                } else if (controlId.equalsIgnoreCase("ram")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.RAM_LIST));
                } else if (controlId.equalsIgnoreCase("rooted")) {
                    comboBox.setItems(FXCollections.observableArrayList(ManualEntryColumnMapperUtil.ROOTED_LIST));
                }
                comboBox.setEditable(editable);
            }
        }
        for (String controlId : EDITABLE_TEXT_FIELDS_IDS) {
            TextField textField = (TextField) dataContainer.lookup("#" + selectedDeviceImei + "_" + controlId);
            textField.setEditable(editable);
        }
    }

    /**
     * Converts a TransactionRecord to a Transaction.
     *
     * @param transactionRecord the transaction record we get from cloud lookup
     * @return Transaction
     */
    public Transaction getTransactionFromRecord(final TransactionRecord transactionRecord) {
        return Transaction.builder()
                .transactionId(Integer.parseInt(transactionRecord.getTransactionId()))
                .qty(transactionRecord.getQty())
                .boxNo(transactionRecord.getBoxNo())
                .vendorName(transactionRecord.getVendorName())
                .invoiceNo(transactionRecord.getInvoiceNo())
                .licenseId(Integer.parseInt(transactionRecord.getLicenseId()))
                .stationId(transactionRecord.getStationId())
                .transactionDate(transactionRecord.getTransactionDate())
                .build();

    }

    // ==================================================================
    //                      MQTT messages requests / notify
    // ==================================================================

    private void requestBackendForTransactionDetails(final int dataOffset) {
        loaderViewStackPane.setVisible(true);
        loaderViewStackPane.setManaged(true);
        LOGGER.info("Request to backend to load transaction from cloud with offset :{}", dataOffset);
        final String topic = TopicBuilder.buildGenericTopic("transaction-details", "request");

        // create MQTT message and publish
        final TransactionDetailsRequestMessage message = new TransactionDetailsRequestMessage();
        message.setTransaction(transaction);
        message.setTransactionRecordsOffset(dataOffset);
        publishToMqttTopic(topic, message);
    }

    private void requestBackendToContinueTransaction() {
        LOGGER.info("Request backend to continue transaction");
        final String topic = TopicBuilder.buildGenericTopic("transaction-continue", "request");

        // create MQTT message and publish
        final TransactionContinueRequestMessage message = new TransactionContinueRequestMessage();
        message.setTransaction(transaction);
        publishToMqttTopic(topic, message);
    }

    // ==================================================================
    //                         Events Listeners
    // ==================================================================

    /**
     * Transaction details response event listener.
     *
     * @param event TransactionDetailsResponseEvent
     */
    @EventListener
    public void onTransactionDetailsResponse(final TransactionDetailsResponseEvent event) {
        LOGGER.info("Transaction detail response received from backend");
        TransactionResponse transactionResponse = event.getTransactionResponse();

        if (event.isExportResponse()) {
            createExportFile(transactionResponse);
            return;
        }

        if (event.getConnectedSerials() != null && !event.getConnectedSerials().isEmpty()) {
            LOGGER.info("Adding connected devices info to the serial list");
            connectDevicesSerials = event.getConnectedSerials();
        }
        Runnable runnable = () -> {

            int currentTransactionCount = 0;
            if ((transactionResponse == null || transactionResponse.getTransactionRecords() == null ||
                    transactionResponse.getTransactionRecords().length == 0) && dataOffset > RECORDS_PER_PAGE) {
                nextRecord.setVisible(false);
                dataOffset = dataOffset - RECORDS_PER_PAGE;
                return;
            }

            vendorName.setText(event.getTransaction().getVendorName());
            invoiceNumber.setText(event.getTransaction().getInvoiceNo());
            quantityNumber.setText(event.getTransaction().getQty());
            boxNumber.setText(event.getTransaction().getBoxNo());

            if (transactionResponse != null && transactionDetailsTableView != null
                    && transactionResponse.getTransactionRecords() != null &&
                    transactionResponse.getTransactionRecords().length > 0) {

                // Map PDM(Physical data model) to LDM(Logical data model)
                TransactionDisplayModel transactionDisplayModel =
                        logicalModelMapper.generateTransactionLogicalDataModel(transactionResponse);
                observableTransactionRecordList.clear();
                observableTransactionRecordList.addAll(transactionDisplayModel.getTransactionRecords());
                transactionDetailsTableView.setItems(observableTransactionRecordList);
                exportHBox.setDisable(observableTransactionRecordList.size() == 0);

                int noOfRecords = transactionResponse.getTransactionRecords().length;
                currentTransactionCount = dataOffset + noOfRecords;

                LOGGER.info("Total records now :{}", currentTransactionCount);
                int currentPage = (dataOffset / RECORDS_PER_PAGE) + 1;
                currentTransactions.setText(String.valueOf(currentPage).concat(" - ").concat(String.valueOf(
                        currentTransactionCount)));

                previousRecord.setVisible(dataOffset > 0);
                nextRecord.setVisible(noOfRecords >= RECORDS_PER_PAGE);
            }

            loaderViewStackPane.setVisible(false);
            loaderViewStackPane.setManaged(false);
            mainContentVBox.setVisible(true);
        };

        runOnFxThread(runnable);
    }

    /**
     * Transaction continue response event listener.
     *
     * @param event TransactionContinueResponseEvent
     */
    @EventListener
    public void onTransactionContinueResponse(final TransactionContinueResponseEvent event) {
        LOGGER.info("Continue transaction response received from backend");
        Runnable runnable;
        if (event.isContinueTransactionSuccessStatus()) {
            uiInMemoryStore.setTransaction(transaction);

            runnable = () -> {
                mainController.showTransactionDetails();

                Toast.show(mainController.getStackPane().getScene().getWindow(),
                        getLocalizationService().getLanguageSpecificText("transactionContinued"),
                        2000);

                continueTransactionButton.setVisible(false);
                continueTransactionButton.setManaged(false);
                changeTransactionButton.setVisible(false);
                changeTransactionButton.setManaged(false);
                currentTransactionHBox.setVisible(true);
                currentTransactionHBox.setManaged(true);
            };
        } else {
            runnable = () -> Toast.show(mainController.getStackPane().getScene().getWindow(),
                    getLocalizationService().getLanguageSpecificText("failedToContinueTransaction"),
                    2000);
        }
        runOnFxThread(runnable);
    }

    /**
     * Device lookup response event received from backend.
     *
     * @param event CloudDeviceLookupResponseEvent
     */
    @Async
    @EventListener
    public void onCloudDeviceLookupResponse(final CloudDeviceLookupResponseEvent event) {
        TransactionResponse transactionResponse = event.getTransactionResponse();
        LOGGER.info("Cloud device lookup transaction response : {}", transactionResponse);
        Runnable runnable = () -> {
            if (transactionResponse != null && transactionDetailsTableView != null) {
                // Map PDM(Physical data model) to LDM(Logical data model)
                TransactionDisplayModel transactionDisplayModel =
                        logicalModelMapper.generateTransactionLogicalDataModel(transactionResponse);
                observableTransactionRecordList.clear();
                observableTransactionRecordList.addAll(transactionDisplayModel.getTransactionRecords());
                transactionDetailsTableView.setItems(observableTransactionRecordList);
                exportHBox.setDisable(observableTransactionRecordList.size() == 0);
            }

            loaderViewStackPane.setVisible(false);
            loaderViewStackPane.setManaged(false);
            mainContentVBox.setVisible(true);
        };

        runOnFxThread(runnable);
    }

    private TransactionDetailsEditMessage prepareRequestMessage(final Map<String, String> controlValues,
                                                                final TransactionRecord transactionRecord) {

        TransactionDetailsEditMessage requestMessage = new TransactionDetailsEditMessage();
        if (isManualEntry) {
            requestMessage.setImei(imeiDetailView.getText());
            requestMessage.setOs(controlValues.get("os"));
            requestMessage.setMake(controlValues.get("make"));
            requestMessage.setModel(controlValues.get("model"));
            requestMessage.setMemory(controlValues.get("memory"));
            requestMessage.setModelNo(controlValues.get("modelNo"));
            requestMessage.setRegulatoryModelNumber(controlValues.get("regulatoryModelNumber"));
            requestMessage.setEsn(controlValues.get("esn"));
            requestMessage.setDeviceLock(controlValues.get("deviceLock"));
            requestMessage.setWorking(controlValues.get("working"));
            requestMessage.setFailed(controlValues.get("failed"));
            requestMessage.setCosmetic(controlValues.get("cosmetics"));
            requestMessage.setBatteryHealthPercentage(controlValues.get("batteryHealthPercentage"));
            requestMessage.setSkuCode(controlValues.get("skuCode"));
            requestMessage.setFirmware(controlValues.get("firmware"));
            requestMessage.setRam(controlValues.get("ram"));
            requestMessage.setAppleId(controlValues.get("appleId"));
            requestMessage.setRooted(controlValues.get("rooted"));
            requestMessage.setDefectsCode(controlValues.get("defectsCode"));

        }

        Transaction transactionObj = new Transaction();
        transactionObj.setTransactionId(Integer.parseInt(transactionRecord.getTransactionId()));
        transactionObj.setVendorName(transactionRecord.getVendorName());
        transactionObj.setInvoiceNo(transactionRecord.getInvoiceNo());
        transactionObj.setBoxNo(transactionRecord.getBoxNo());
        transactionObj.setQty(transactionRecord.getQty());
        transactionObj.setLicenseId(Integer.parseInt(transactionRecord.getLicenseId()));

        requestMessage.setSerial(serialDetailView.getText());
        requestMessage.setId(selectedDeviceId);
        requestMessage.setTransactionId(selectedTransactionId);
        requestMessage.setTransaction(transactionObj);
        requestMessage.setColor(controlValues.get("color"));
        requestMessage.setCarrier(controlValues.get("carrier"));
        requestMessage.setUnlockStatus(controlValues.get("unlockStatus"));
        requestMessage.setGrade(controlValues.get("grade"));
        requestMessage.setNotes(controlValues.get("notes"));
        requestMessage.setLpn(controlValues.get("lpn"));
        requestMessage.setCustom1(controlValues.get("custom1"));
        requestMessage.setManualEntry(isManualEntry);
        return requestMessage;
    }

    private TransactionRecord prepareTransactionRecord(final Map<String, String> controlValues) {

        TransactionRecord transaction = observableTransactionRecordList.get(selectedRecordIndex);

        if (isManualEntry) {
            transaction.setImei(imeiDetailView.getText());
            transaction.setSerial(serialDetailView.getText());
            transaction.setOs(controlValues.get("os"));
            transaction.setMake(controlValues.get("make"));
            transaction.setModel(controlValues.get("model"));
            transaction.setMemory(controlValues.get("memory"));
            transaction.setModelNo(controlValues.get("modelNo"));
            transaction.setRegulatoryModelNumber(controlValues.get("regulatoryModelNumber"));
            transaction.setEsn(controlValues.get("esn"));
            transaction.setDeviceLock(controlValues.get("deviceLock"));
            transaction.setWorking(controlValues.get("working"));
            transaction.setFailed(controlValues.get("failed"));
            transaction.setCosmetics(controlValues.get("cosmetics"));
            transaction.setBatteryHealthPercentage(controlValues.get("batteryHealthPercentage"));
            transaction.setSkuCode(controlValues.get("skuCode"));
            transaction.setFirmware(controlValues.get("firmware"));
            transaction.setRam(controlValues.get("ram"));
            transaction.setAppleId(controlValues.get("appleId"));
            transaction.setRooted(controlValues.get("rooted"));
            transaction.setDefectsCode(controlValues.get("defectsCode"));

        }
        transaction.setCarrier(controlValues.get("carrier"));
        transaction.setColor(controlValues.get("color"));
        transaction.setGrade(controlValues.get("grade"));
        transaction.setLpn(controlValues.get("lpn"));
        transaction.setNotes(controlValues.get("notes"));
        transaction.setCustom1(controlValues.get("custom1"));
        transaction.setUnlockStatus(controlValues.get("unlockStatus"));

        return transaction;
    }

    /**
     * Get the device os from OS field from view transaction UI
     *
     * @return os type - android or ios
     */
    private String getDeviceOs() {
        ComboBox<String> comboBox = (ComboBox<String>) dataContainer.
                lookup("#" + selectedDeviceImei + "_" + "os");
        if (comboBox != null) {
            return comboBox.getValue();
        }
        return null;
    }

    /**
     * Writes the updated column order to the TransactionColumnOrderList.json file.
     * Creates a JSON object with the current column order map and saves it to the file.
     * Logs an error if the file creation or writing process fails.
     */
    private void writeUpdatedColumOrderListToFile() {
        try {
            LOGGER.info("Creating TransactionColumnOrderList.json file");
            final File orderColumnListFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + TRANSACTION_COLUMN_ORDER_FILE_NAME);

            ObjectNode rootNode = objectMapper.createObjectNode();
            rootNode.putPOJO("Columns", columnOrderMap);
            String updatedColumnsOrderList = objectMapper.writeValueAsString(rootNode);
            fileUtil.writeStringToFileIfDifferent(orderColumnListFile, updatedColumnsOrderList);
        } catch (IOException e) {
            LOGGER.error("Failed to create TransactionColumnOrderList.json", e);
        }
    }

    /**
     * Reads the column order from the TransactionColumnOrderList.json file.
     * If the file does not exist or is empty, returns the default column order.
     * Logs an error and falls back to the default configuration if reading fails.
     *
     * @return a map containing the column order, either from the file or default values
     */
    private Map<String, String> readColumnsFromFile() {
        Map<String, String> resultMap = new LinkedHashMap<>();
        LOGGER.info("Reading TransactionColumnOrderList.json from file");
        final File orderColumnListFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                File.separator + TRANSACTION_COLUMN_ORDER_FILE_NAME);
        if (!orderColumnListFile.exists()) {
            LOGGER.info("Column order list file not found, setting default columns");
            resultMap = TransactionColumnMapperUtil.getDeviceColumnOrderMap();
            return resultMap;
        }

        try {
            Map<String, LinkedHashMap<String, String>> jsonContent = objectMapper.readValue(
                    orderColumnListFile, new TypeReference<LinkedHashMap<String, LinkedHashMap<String, String>>>() {
                    }
            );

            if (jsonContent.containsKey("Columns")) {
                resultMap.putAll(jsonContent.get("Columns"));
            }

            if (resultMap.isEmpty()) {
                LOGGER.info("Found order list empty in file, setting default values");
                resultMap = TransactionColumnMapperUtil.getDeviceColumnOrderMap();
            }

        } catch (IOException e) {
            LOGGER.error("Failed to read content from TransactionColumnOrderList.json file", e);
            resultMap = TransactionColumnMapperUtil.getDeviceColumnOrderMap();
        }
        return resultMap;
    }

    private void addCheckBoxesForRecords() {
        List<TableColumn<TransactionRecord, CheckBox>> checkboxColumnList = new ArrayList<>();

        TableColumn<TransactionRecord, CheckBox> checkboxColumn = new TableColumn<>();
        checkboxColumn.setCellValueFactory(
                c -> {
                    TransactionRecord transaction = c.getValue();
                    CheckBox checkBox = new CheckBox();
                    checkBox.selectedProperty().setValue(transaction.isSelected());
                    if (connectDevicesSerials.contains(transaction.getSerial())) {
                        checkBox.setDisable(true);
                    }
                    checkBox.selectedProperty().addListener((ov, old_val, new_val) -> {
                        changeTransactionButton.setDisable(false);
                        transaction.setSelected(new_val);
                        if (new_val) {
                            selectedRecords.add(transaction);
                        } else {
                            selectedRecords.remove(transaction);
                        }
                    });
                    return new SimpleObjectProperty<>(checkBox);
                });
        checkboxColumn.setCellFactory(col -> new TableCell<>() {
            @Override
            protected void updateItem(final CheckBox item, final boolean empty) {
                super.updateItem(item, empty);

                if (empty || item == null) {
                    setGraphic(null);
                } else {
                    setGraphic(item);
                }
            }
        });
        checkboxColumn.setPrefWidth(25);
        checkboxColumnList.add(checkboxColumn);
        transactionDetailsTableView.getColumns().setAll(checkboxColumnList);
    }


    /**
     * Creates excel file from the transaction records from the backend
     *
     * @param transactionResponse from the backend
     */
    private void createExportFile(final TransactionResponse transactionResponse) {
        Runnable runnable = () -> {
            if (transactionResponse != null && transactionResponse.getTransactionRecords() != null
                    && transactionDetailsTableView != null) {
                FileChooser fileChooser = new FileChooser();
                fileChooser.setInitialDirectory(new File(System.getProperty("user.home") + "/Desktop"));
                fileChooser.setInitialFileName(String.format("%s_%s.xlsx", mainController.getLoggedInUserId(),
                        DateFormatUtil.LOGS_DATE_TIME_FORMAT.format(new Date())));
                final File file = fileChooser.showSaveDialog(stage);
                if (file != null) {
                    try {
                        TransactionDisplayModel transactionDisplayModel =
                                logicalModelMapper.generateTransactionLogicalDataModel(transactionResponse);
                        exportExcelUtil.exportTransactionRecords(transactionDetailsTableView.getColumns(),
                                Arrays.asList(transactionDisplayModel.getTransactionRecords()), file);
                    } catch (IOException e) {
                        LOGGER.error("Exception occurred when tried to export previousTransaction records", e);
                    }
                }
            }
        };
        runOnFxThread(runnable);
    }
}
