package com.phonecheck.ui;

import com.phonecheck.model.store.UiInMemoryStore;
import javafx.application.Application;
import javafx.application.Platform;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.Properties;

import static com.phonecheck.model.util.ISupportFilePathsStrategy.CLASS_PATH;
import static com.phonecheck.model.util.ISupportFilePathsStrategy.J_PACKAGE_APP_PATH;

@SpringBootApplication(scanBasePackages = "com.phonecheck")
@AllArgsConstructor
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class,
        JpaRepositoriesAutoConfiguration.class,
        FlywayAutoConfiguration.class
})
public class MultiModuleApplication implements CommandLineRunner {
    private static final Logger LOGGER = LoggerFactory.getLogger(MultiModuleApplication.class);
    private static final String PRE_UI_OPERATIONS_SCRIPT_FILE = "exec-pre-ui-ops";
    private static final String SPLASH_IMAGE_PATH = "/com/phonecheck/image/splash.jpg";

    private static boolean isStartedFromInstaller;
    private final UiInMemoryStore uiInMemoryStore;
    private static JFrame splashFrame;

    public static void main(final String[] args) {
        Properties systemProperties = System.getProperties();

        isStartedFromInstaller = StringUtils.isNotBlank(System.getProperty(J_PACKAGE_APP_PATH));
        boolean isMac = StringUtils.containsIgnoreCase(systemProperties.getProperty("os.name"), "mac");
        boolean isWindows = StringUtils.containsIgnoreCase(systemProperties.getProperty("os.name"), "windows");
        if (isWindows) {
            preventMultipleInstances();
        }

        try {
            if (isStartedFromInstaller) {
                String classPath = System.getProperty(CLASS_PATH);
                String workingDirectoryPath;
                String command;
                if (isMac) {
                    // Sample classPath value
                    // /Users/<USER>/.../PhoneCheck3.app/Contents/app/phonecheck-ui-0.0.1-SNAPSHOT.jar/
                    Path contentsPath = Paths.get(classPath).getParent().getParent();
                    Path resourcesPath = contentsPath.resolve("Resources");

                    workingDirectoryPath = resourcesPath.toString();
                    command = workingDirectoryPath + File.separator + PRE_UI_OPERATIONS_SCRIPT_FILE + ".sh";
                } else {
                    // Sample classPath value
                    // C:\Program Files\PhoneCheck\app\phonecheck-ui-0.0.1-SNAPSHOT.jar
                    Path appPath = Paths.get(classPath).getParent();

                    workingDirectoryPath = appPath.toString();
                    command = workingDirectoryPath + File.separator + PRE_UI_OPERATIONS_SCRIPT_FILE + ".bat";
                }

                try {
                    LOGGER.info("Pre ui operations command: {}", command);
                    ProcessBuilder processBuilder = new ProcessBuilder(command, workingDirectoryPath);
                    Process process = processBuilder.start();
                    process.waitFor();
                    process.destroy();
                } catch (IOException e) {
                    LOGGER.error("Error occurred while executing pre ui operations.", e);
                }
            }
            //TODO read clientCustomization file to know the preferredLang and set it in UiInMemoryStores
        } catch (Exception e) {
            LOGGER.error("Error occurred while launching daemon and backend.", e);
        }

        LOGGER.info("Starting UI...");
        Application.launch(MainUIApplication.class, args);
    }

    @Override
    public void run(final String[] args) {
        Platform.runLater(this::showSplashScreen);
        if (isStartedFromInstaller) {
            uiInMemoryStore.setAppStartedFromInstaller(true);
            LOGGER.debug("UI Application started from Installer.");
        } else {
            LOGGER.debug("UI Application started from IDE.");
        }
    }

    /**
     * Checks for the presence of a lock file to determine if another instance of the application is already running.
     * If the lock file does not exist, it creates one and writes the current process ID to it.
     * If the lock file exists, it reads the stored process ID and checks if the corresponding process is still running.
     * If the process is running, displays an error message and exits the application.
     * If the process is not running, updates the lock file with the current process ID.
     *
     * @throws IOException If an error occurs during the creation, reading, or writing of the lock file.
     */
    private static void preventMultipleInstances() {
        Path lockFilePath = Path.of(System.getenv("LOCALAPPDATA"), "PhoneCheck3", ".app_lock");
        try {
            if (!Files.exists(lockFilePath)) {
                Files.createDirectories(lockFilePath.getParent());
                Files.writeString(lockFilePath, String.valueOf(ProcessHandle.current().pid()),
                        StandardOpenOption.CREATE_NEW);
            } else {
                String processId = Files.readString(lockFilePath);
                long pid = Long.parseLong(processId);
                boolean isRunning = ProcessHandle.of(pid).isPresent();
                if (isRunning) {
                    JOptionPane.showMessageDialog(null,
                            "Another instance of PhoneCheck 3.0 is already running.");
                    System.exit(0);
                } else {
                    Files.writeString(lockFilePath, String.valueOf(ProcessHandle.current().pid()),
                            StandardOpenOption.TRUNCATE_EXISTING);
                }
            }
        } catch (IOException e) {
            LOGGER.error("Error occurred while creating the lock file.", e);
            System.exit(1);
        }
    }


    /**
     * Displays a splash screen until all processes are running
     * and the login screen appears.
     * The splash screen remains visible while background processes
     * are being initialized. Once the application is ready and the
     * login screen is about to be displayed, the splash screen is hidden.
     */
    private void showSplashScreen() {
        splashFrame = new JFrame();
        splashFrame.setUndecorated(true);
        splashFrame.setAlwaysOnTop(false);
        splashFrame.setSize(400, 700);
        splashFrame.getContentPane().setBackground(Color.white);

        final Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        splashFrame.setLocation(screenSize.width / 2 - splashFrame.getSize().width / 2,
                screenSize.height / 2 - splashFrame.getSize().height / 2);

        JLabel splashLabel = new JLabel(new ImageIcon(getClass().getResource(SPLASH_IMAGE_PATH)));
        splashFrame.add(splashLabel);
        splashFrame.setVisible(true);
    }

    /**
     * Hides and disposes of the splash screen if it is currently visible.
     * This method checks if the splash screen is present.
     * If found, it hides the splash screen and releases its resources.
     * If no splash screen is available, it logs that no action is taken.
     */
    public static void hideSplashScreen() {
        SwingUtilities.invokeLater(() -> {
            if (splashFrame != null) {
                LOGGER.info("Splash found, going to hide and start application");
                splashFrame.setVisible(false);
                splashFrame.dispose();
            } else {
                LOGGER.info("No splash screen found to hide");
            }
        });
    }
}
