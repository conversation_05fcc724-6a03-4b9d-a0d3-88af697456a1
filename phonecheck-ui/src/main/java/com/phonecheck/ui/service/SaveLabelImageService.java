package com.phonecheck.ui.service;

import com.phonecheck.command.print.mac.GetMacPrinterDpiCommand;
import com.phonecheck.command.print.windows.GetWindowsPrinterDpiCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.mqtt.messages.PrintLabelRequestMessage;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.print.Printer;
import com.phonecheck.model.print.label.LabelImageFormat;
import com.phonecheck.model.print.label.LabelOrientation;
import com.phonecheck.model.print.label.LabelSizeUnit;
import com.phonecheck.model.print.label.LabelVariant;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.print.mac.MacPrinterDpiParser;
import com.phonecheck.parser.print.windows.WindowsPrinterDpiParser;
import com.phonecheck.ui.controller.MainController;
import com.phonecheck.ui.controller.print.labels.AbstractLabelController;
import com.phonecheck.ui.controller.print.labels.LabelMakerController;
import com.phonecheck.ui.controller.utility.DeviceTestResultsLabelConfigUtil;
import com.phonecheck.ui.controller.utility.FXMLLoaderUtil;
import javafx.application.Platform;
import javafx.embed.swing.SwingFXUtils;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.SnapshotParameters;
import javafx.scene.image.WritableImage;
import javafx.scene.layout.Pane;
import javafx.stage.Stage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static com.phonecheck.model.constants.FileConstants.TEMP_DIR;
import static com.phonecheck.model.print.label.LabelVariant.ANDROID_PROVISION_QR_LABEL;

/**
 * Service to save label image and then initiate print in backend-module
 * queuing save label image operations
 */
@Service
public class SaveLabelImageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SaveLabelImageService.class);
    private static final int PRINT_OPERATION_PROCESSING_INTERVAL = 1500; // Milliseconds
    private static final float SCALE_FACTOR = 2.5F;
    private final ApplicationContext applicationContext;
    private final UiInMemoryStore uiInMemoryStore;
    private final boolean keepProcessingSaveOperations = true;
    private final FXMLLoaderUtil fxmlLoaderUtil;
    private final BlockingQueue<PrintOperation> operationQueue;
    private final OsChecker osChecker;
    private final CommandExecutor executor;
    private final MacPrinterDpiParser macPrinterDpiParser;
    private final WindowsPrinterDpiParser windowsPrinterDpiParser;
    private final LocalizationService localizationService;
    private final DeviceTestResultsLabelConfigUtil testResultsLabelConfigUtil;
    private final EsnImageTextResponseService esnImageTextResponseService;
    private final FileUtil fileUtil;


    public SaveLabelImageService(final FXMLLoaderUtil fxmlLoaderUtil,
                                 final OsChecker osChecker,
                                 final CommandExecutor executor,
                                 final MacPrinterDpiParser macPrinterDpiParser,
                                 final WindowsPrinterDpiParser windowsPrinterDpiParser,
                                 final ApplicationContext applicationContext,
                                 final UiInMemoryStore uiInMemoryStore,
                                 final LocalizationService localizationService,
                                 final DeviceTestResultsLabelConfigUtil testResultsLabelConfigUtil,
                                 final EsnImageTextResponseService esnImageTextResponseService,
                                 final FileUtil fileUtil) {
        this.fxmlLoaderUtil = fxmlLoaderUtil;
        this.osChecker = osChecker;
        this.esnImageTextResponseService = esnImageTextResponseService;
        this.executor = executor;
        this.macPrinterDpiParser = macPrinterDpiParser;
        this.windowsPrinterDpiParser = windowsPrinterDpiParser;
        this.applicationContext = applicationContext;
        this.uiInMemoryStore = uiInMemoryStore;
        this.localizationService = localizationService;
        this.testResultsLabelConfigUtil = testResultsLabelConfigUtil;

        operationQueue = new LinkedBlockingQueue<>();
        startProcessingSaveLabelOperations();
        this.fileUtil = fileUtil;
    }

    private void startProcessingSaveLabelOperations() {
        new Thread(() -> {
            while (keepProcessingSaveOperations) {
                try {
                    PrintOperation printOperation = operationQueue.take();
                    saveLabelImageOperation(printOperation);

                    Thread.sleep(PRINT_OPERATION_PROCESSING_INTERVAL);
                } catch (Exception e) {
                    LOGGER.error("Exception occurred while processing print operations from queue", e);
                }
            }
        }).start();
    }

    /**
     * Enqueues a print operation to be saved as a label image.
     *
     * @param printOperation The print operation to be enqueued for saving as a label image.
     */
    public void enqueueSaveLabelImageOperation(final PrintOperation printOperation) {
        operationQueue.add(printOperation);
    }

    /**
     * Method that performs the operations to save label image i.e.
     * loads label controller -> saves label image -> notify backend-module
     *
     * @param printOperation object that contains the details of label
     *                       print operation e.g. print to pdf etc.
     */
    private void saveLabelImageOperation(final PrintOperation printOperation) {
        MDC.clear();
        MDC.put("id", (printOperation.getDevice() != null ? printOperation.getDevice().getId() : null));

        LOGGER.info("Saving label image for label name: {}",
                printOperation.getLabelName());
        Pane labelRootPane;
        LabelVariant label = null;

        if (printOperation.getLabelName() != null) {
            label = LabelVariant.fromName(printOperation.getLabelName());
        }
        String labelFileName = (label != null) ? label.getFileName() : printOperation.getLabelName();
        if (StringUtils.isNotBlank(labelFileName)) {
            try {
                FXMLLoader loader = fxmlLoaderUtil.getFxmlLoader(labelFileName);

                LabelFxmlResponse.Data labelData = uiInMemoryStore.getLabelDataMap() != null
                        ? uiInMemoryStore.getLabelDataMap().get(labelFileName) : null;

                if (labelData != null && StringUtils.contains(labelData.getName(), labelFileName)) {
                    LabelFxmlResponse.Data fxmlData = uiInMemoryStore.getLabelDataMap().get(labelFileName);
                    boolean isFxmlV2 = StringUtils.isNotEmpty(fxmlData.getFxmlV2());
                    String fxml = isFxmlV2 ? fxmlData.getFxmlV2() : fxmlData.getFxml();
                    labelRootPane = fxmlLoaderUtil.loadLayoutPaneFromFxml(isFxmlV2, fxml, loader);
                    if (StringUtils.isNotEmpty(labelData.getOrientation())) {
                        printOperation.setCloudRollOrientation(
                                labelData.getOrientation().equalsIgnoreCase("vertical") ?
                                        LabelOrientation.PORTRAIT :
                                        LabelOrientation.LANDSCAPE
                        );
                    }
                } else {
                    labelRootPane = loader.load();
                }

                AbstractLabelController controller = loader.getController();

                if (controller instanceof LabelMakerController labelMakerController) {
                    labelMakerController.setLabelName(labelFileName);
                    labelMakerController.setRootPane(labelRootPane);
                    labelMakerController.setTestResultsLabelConfigUtil(testResultsLabelConfigUtil);

                    printOperation.setPrintForLabelMaker(true);
                }
                setPrintMode(printOperation);
                Runnable runnable = () -> {
                    try {
                        controller.setUiInMemoryStore(uiInMemoryStore);
                        controller.setLocalizationService(localizationService);
                        controller.setEsnImageTextResponseService(esnImageTextResponseService);
                        // Populating data in label fields
                        controller.initializeViews(printOperation.getDevice(), printOperation.getPrintMode());

                        if (!printOperation.isPrintToPdf() &&
                                (StringUtils.isBlank(printOperation.getPrinterName())
                                        || printOperation.getPrinterName().equalsIgnoreCase("none"))) {
                            LOGGER.warn("Printer assigned to the print operation is {}. Printing to PDF as fallback.",
                                    printOperation.getPrinterName());
                            printOperation.setPrintToPdf(true);
                        }

                        // Remove spacing from label name to create image file path
                        String labelName = StringUtils.remove(controller.getLabelFileName(), " ");
                        Printer printer = Printer.getByName(printOperation.getPrinterName());
                        float labelWidth = controller.getWidth();
                        float labelHeight = controller.getHeight();
                        if (Printer.BROTHER.equals(printer)) {
                            if (labelWidth == 2.0 && labelHeight == 4.0) {
                                labelWidth = 2.4f;
                                labelHeight = 3.9f;
                            } else if (labelWidth == 4.0 && labelHeight == 2.0) {
                                labelWidth = 3.9f;
                                labelHeight = 2.4f;
                            }
                        }
                        LabelSizeUnit labelSizeUnit = controller.getSizeUnit();
                        String imagePath;

                        if (printOperation.isPrintToPdf() || printOperation.isPrintToPreview()
                                || !Printer.ZEBRA.equals(printer)) {
                            imagePath = labelName.equals(ANDROID_PROVISION_QR_LABEL.getFileName())
                                    ? Paths.get(TEMP_DIR,
                                    labelName
                                            + "-" + System.currentTimeMillis()
                                            + "." + LabelImageFormat.PNG.getValue()).toString()
                                    : Paths.get(TEMP_DIR,
                                    labelName
                                            + "-" +
                                            printOperation.getDevice().getSerial().replace("/", "")
                                            + "-" + System.currentTimeMillis()
                                            + "." + LabelImageFormat.PNG.getValue()).toString();

                            saveLabelImageInPng(
                                    labelRootPane,
                                    labelSizeUnit,
                                    labelHeight,
                                    labelWidth,
                                    printer != null ? printer.getDefaultResolution() : 96,
                                    imagePath
                            );
                            if (printOperation.isPrintToPreview() && StringUtils.isNotBlank(imagePath)) {
                                fileUtil.openImageFile(imagePath);
                                Thread.sleep(1000);
                                fileUtil.deleteFile(printOperation.getLabelImagePath());
                                LOGGER.info("Return flow for as view operation is completed here");
                                return;
                            }
                        } else {
                            int printerDpi = gePrinterDpiCommand(printOperation.getPrinterName());
                            LOGGER.info("Printer {} DPI is {}", printOperation.getPrinterName(), printerDpi);

                            imagePath = Paths.get(TEMP_DIR,
                                    labelName
                                            + "-" + printOperation.getDevice().getSerial()
                                            + "-" + System.currentTimeMillis()
                                            + "." + LabelImageFormat.BMP).toString();

                            if (osChecker.isMac()) {
                                saveLabelImageInBitmap(
                                        labelRootPane,
                                        labelSizeUnit,
                                        labelHeight,
                                        labelWidth,
                                        printerDpi,
                                        imagePath
                                );
                            } else {
                                saveLabelImageInBitmapForWindows(
                                        labelRootPane,
                                        imagePath
                                );
                            }
                        }

                        // Setup print operation for label image printing
                        printOperation.setXPosition(controller.getXPosition());
                        printOperation.setYPosition(controller.getYPosition());
                        printOperation.setLabelOrientation(controller.getDefaultOrientation());
                        printOperation.setWidth(labelWidth);
                        printOperation.setHeight(labelHeight);
                        printOperation.setLabelSizeUnit(labelSizeUnit);
                        printOperation.setLabelImagePath(imagePath);

                        // Dymo specific fields
                        printOperation.setLpCommandOptionsDymo(controller.getLpCommandOptionsForDymo(
                                printOperation.getDymoPaperTray(),
                                printOperation.getCloudRollOrientation()
                        ));
                        printOperation.setDymoLabelConfigFileName(controller.getLabelConfigFileNameDymo(
                                printOperation.getCloudRollOrientation()
                        ));

                        // Brother specific fields
                        printOperation.setLpCommandOptionsBrother(
                                controller.getLpCommandOptionsForBrother(
                                        printOperation.getRollType(),
                                        printOperation.getCloudRollOrientation()
                                )
                        );
                        printOperation.setBrotherLabelConfigFileName(
                                controller.getLabelConfigFileNameBrother(
                                        printOperation.getPrinterName(),
                                        printOperation.getRollType(),
                                        printOperation.getPaperType(),
                                        printOperation.getCloudRollOrientation())
                        );

                        // Zebra specific fields
                        printOperation.setLpCommandOptionsZebra(controller.getLpCommandOptionsForZebra(
                                printOperation.getCloudRollOrientation()
                        ));

                        notifyBackendToPrint(printOperation);
                    } catch (Exception e) {
                        LOGGER.error("Exception occurred while saving label image operation", e);
                    }
                };

                if (Platform.isFxApplicationThread()) {
                    runnable.run();
                } else {
                    Platform.runLater(runnable);
                }

            } catch (Exception e) {
                LOGGER.error("Exception occurred while creating label image", e);
            }
        } else {
            LOGGER.error("No label exists with the name '{}'", printOperation.getLabelName());
        }
    }

    /**
     * Method to save the label image in PNG format to the temp directory
     * path retrieved from "java.io.tmpdir"
     *
     * @param labelRootPane label controller fxml root pane
     * @param labelSizeUnit label size unit
     * @param labelHeight   label height
     * @param labelWidth    label width
     * @param printerDpi    dpi for label
     * @param imagePath     label image save path
     */
    private void saveLabelImageInPng(final Pane labelRootPane,
                                     final LabelSizeUnit labelSizeUnit,
                                     final float labelHeight,
                                     final float labelWidth,
                                     final int printerDpi,
                                     final String imagePath) {
        Stage renderStage = new Stage();
        Scene renderScene = new Scene(labelRootPane);
        renderStage.setScene(renderScene);
        renderStage.sizeToScene();
        renderStage.show();
        renderStage.hide();
        labelRootPane.setScaleX(SCALE_FACTOR);
        labelRootPane.setScaleY(SCALE_FACTOR);

        float widthInInches = labelSizeUnit == LabelSizeUnit.INCH ?
                labelWidth :
                labelWidth / 25.4f;   // Convert millimeters to inches
        float heightInInches = labelSizeUnit == LabelSizeUnit.INCH ?
                labelHeight :
                labelHeight / 25.4f;  // Convert millimeters to inches

        // Retrieve label height and width in pixel by multiplying size in inches with printer resolution
        float bufWidth = printerDpi * widthInInches;
        float bufHeight = printerDpi * heightInInches;

        final WritableImage snapshot = labelRootPane.snapshot(new SnapshotParameters(), null);
        BufferedImage image = new BufferedImage((int) bufWidth, (int) bufHeight, BufferedImage.TYPE_INT_RGB);
        image = SwingFXUtils.fromFXImage(snapshot, image);

        // Write image to file
        try {
            File imageFile = new File(imagePath);
            ImageIO.write(image,
                    LabelImageFormat.PNG.getValue(),
                    imageFile);
        } catch (IOException e) {
            LOGGER.error("Exception occurred while saving label image for dymo printer.", e);
        }
    }

    /**
     * Method to save the label image in Bitmap format to the temp directory
     * path retrieved from "java.io.tmpdir"
     *
     * @param labelRootPane label controller fxml root pane
     * @param labelSizeUnit label size unit
     * @param labelHeight   label height
     * @param labelWidth    label width
     * @param printerDpi    dpi for label
     * @param imagePath     label image save path
     */
    private void saveLabelImageInBitmap(final Pane labelRootPane,
                                        final LabelSizeUnit labelSizeUnit,
                                        final float labelHeight,
                                        final float labelWidth,
                                        final int printerDpi,
                                        final String imagePath) {
        Stage renderStage = new Stage();
        Scene renderScene = new Scene(labelRootPane);
        renderStage.setScene(renderScene);
        renderStage.sizeToScene();
        renderStage.show();
        renderStage.hide();

        final float scaleFactor = 2.5F;

        final int width = (int) (labelRootPane.getPrefWidth() * scaleFactor);
        final int height = (int) (labelRootPane.getPrefHeight() * scaleFactor);

        labelRootPane.setScaleX(scaleFactor);
        labelRootPane.setScaleY(scaleFactor);

        final WritableImage snapshot = labelRootPane.snapshot(new SnapshotParameters(), null);
        BufferedImage tempImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);
        tempImage = SwingFXUtils.fromFXImage(snapshot, tempImage);

        float widthInInches = labelSizeUnit == LabelSizeUnit.INCH ?
                labelWidth :
                labelWidth / 25.4f;   // Convert millimeters to inches
        float heightInInches = labelSizeUnit == LabelSizeUnit.INCH ?
                labelHeight :
                labelHeight / 25.4f;  // Convert millimeters to inches

        // Retrieve label height and width in pixel by multiplying size in inches with printer resolution
        float bufWidth = printerDpi * widthInInches;
        float bufHeight = printerDpi * heightInInches;

        // maintain the aspect ratio of image when migrating fxml to image
        tempImage = adjustImageSizeBasedOnPaper(tempImage, bufWidth, bufHeight, 15);

        BufferedImage newImage = new BufferedImage((int) bufWidth, (int) bufHeight, BufferedImage.TYPE_BYTE_BINARY);
        newImage.getGraphics().drawImage(tempImage, 0, 0, (int) bufWidth, (int) bufHeight, null);
        // Convert new image to Type 3 Byte as binary image support is deprecated in 12.6.3
        BufferedImage convertedImage = new BufferedImage(newImage.getWidth(), newImage.getHeight(),
                BufferedImage.TYPE_3BYTE_BGR);

        // Convert 2-bit bitmap to 24-bit bitmap
        for (int y = 0; y < newImage.getHeight(); y++) {
            for (int x = 0; x < newImage.getWidth(); x++) {
                int pixel = newImage.getRGB(x, y);
                convertedImage.setRGB(x, y, pixel);
            }
        }

        // Write image to file
        try {
            File imageFile = new File(imagePath);
            ImageIO.write(convertedImage,
                    LabelImageFormat.BMP.getValue(),
                    imageFile);
        } catch (IOException e) {
            LOGGER.error("Exception occurred while saving label image for zebra printer.", e);
        }
    }

    /**
     * Method to save the label image in Bitmap format to the temp directory
     * path retrieved from "java.io.tmpdir"
     *
     * @param labelRootPane label controller fxml root pane
     * @param imagePath     label image save path
     */
    private void saveLabelImageInBitmapForWindows(final Pane labelRootPane,
                                                  final String imagePath) {
        Stage renderStage = new Stage();
        Scene renderScene = new Scene(labelRootPane);
        renderStage.setScene(renderScene);
        renderStage.sizeToScene();
        renderStage.show();
        renderStage.hide();

        // Define scale factor
        final float scaleFactor = 5.0F;

        // Calculate scaled width and height
        final int scaledWidth = (int) (labelRootPane.getPrefWidth() * scaleFactor);
        final int scaledHeight = (int) (labelRootPane.getPrefHeight() * scaleFactor);

        // Apply scaling to the pane
        labelRootPane.setScaleX(scaleFactor);
        labelRootPane.setScaleY(scaleFactor);

        // Take a snapshot of the label pane
        WritableImage snapshot = new WritableImage(scaledWidth, scaledHeight);
        SnapshotParameters snapshotParameters = new SnapshotParameters();
        labelRootPane.snapshot(snapshotParameters, snapshot);

        // Convert the snapshot to a BufferedImage
        BufferedImage tempImage = SwingFXUtils.fromFXImage(snapshot, null);

        // Adjust the image size based on the paper, ensuring no black lines
        BufferedImage adjustedImage = adjustImageSizeBasedOnPaperForWindows(tempImage, scaledWidth, scaledHeight);

        // Convert to binary image format
        BufferedImage binaryImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_BYTE_BINARY);
        Graphics2D g2dBinary = binaryImage.createGraphics();
        g2dBinary.drawImage(adjustedImage, 0, 0, null);
        g2dBinary.dispose();

        // Write the binary image to a file
        try {
            File imageFile = new File(imagePath);
            ImageIO.write(binaryImage, LabelImageFormat.BMP.getValue(), imageFile);
        } catch (IOException e) {
            LOGGER.error("Exception occurred while saving label image for zebra printer.", e);
        }
    }

    /**
     * Method to retrieve the printer dpi / resolution
     *
     * @param printerName name of a printer
     * @return int value for printer dpi
     */
    private int gePrinterDpiCommand(final String printerName) {
        try {
            if (osChecker.isMac()) {
                final String dpiCommandOutput =
                        executor.execute(new GetMacPrinterDpiCommand(printerName));
                return macPrinterDpiParser.parse(dpiCommandOutput);
            } else {
                final String dpiCommandOutput =
                        executor.execute(new GetWindowsPrinterDpiCommand(printerName));
                return windowsPrinterDpiParser.parse(dpiCommandOutput);
            }
        } catch (Exception e) {
            LOGGER.warn("Exception occurred while detecting zebra printer resolution", e);

            Printer printer = Printer.getByName(printerName);
            return printer != null ? printer.getDefaultResolution() : 100; // Default Resolution
        }
    }

    /**
     * Method to adjust the image size according to provided
     * width and height
     *
     * @param image   buffered image  object
     * @param width   buffed width for image
     * @param height  buffed height for image
     * @param padding padding for image
     * @return adjusted image
     */
    private BufferedImage adjustImageSizeBasedOnPaper(final BufferedImage image,
                                                      final double width,
                                                      final double height,
                                                      final double padding) {
        final double ratioW = image.getWidth() / width;
        final double ratioH = image.getHeight() / height;
        double newWidth = width;
        double newHeight = height;

        if (ratioW < ratioH) {
            newWidth = image.getWidth() / ratioH;
            newHeight = image.getHeight() / ratioH;
            newWidth += padding;
        } else if (ratioH < ratioW) {
            newWidth = image.getWidth() / ratioW;
            newHeight = image.getHeight() / ratioW;
            newWidth += padding;
        }

        Image resize = image.getScaledInstance((int) newWidth, (int) newHeight, Image.SCALE_SMOOTH);
        BufferedImage resultImage = new BufferedImage((int) width, (int) height, image.getType());
        Graphics2D resultImageGraphics = resultImage.createGraphics();

        Map<RenderingHints.Key, Object> hints = new HashMap<>();
        hints.put(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        hints.put(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        hints.put(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
        hints.put(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        hints.put(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        hints.put(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_DISABLE);
        hints.put(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);

        resultImageGraphics.setRenderingHints(hints);
        resultImageGraphics.setColor(Color.WHITE);
        resultImageGraphics.fillRect(0, 0, (int) width, (int) height);
        resultImageGraphics.drawImage(resize, 0, 0, null);
        resultImageGraphics.dispose();

        return resultImage;
    }

    /**
     * Method to adjust the image size according to provided
     * width and height
     *
     * @param image  buffered image  object
     * @param width  buffed width for image
     * @param height buffed height for image
     * @return adjusted image
     */
    private BufferedImage adjustImageSizeBasedOnPaperForWindows(final BufferedImage image,
                                                                final double width,
                                                                final double height) {
        // Calculate aspect ratio adjustment
        final double ratioW = image.getWidth() / width;
        final double ratioH = image.getHeight() / height;
        double newWidth = width;
        double newHeight = height;

        if (ratioW < ratioH) {
            newWidth = image.getWidth() / ratioH; // Removed padding
            newHeight = image.getHeight() / ratioH;
        } else if (ratioH < ratioW) {
            newWidth = image.getWidth() / ratioW; // Removed padding
            newHeight = image.getHeight() / ratioW;
        }

        // Create resized image with high-quality rendering hints
        BufferedImage resizedImage = new BufferedImage((int) width, (int) height, BufferedImage.TYPE_BYTE_BINARY);
        Graphics2D g2d = resizedImage.createGraphics();

        // Set rendering hints for better quality
        Map<RenderingHints.Key, Object> hints = new HashMap<>();
        hints.put(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        hints.put(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        hints.put(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        hints.put(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
        hints.put(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        hints.put(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_DISABLE);
        hints.put(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
        g2d.setRenderingHints(hints);

        // Draw the resized image
        g2d.drawImage(image, 0, 0, (int) newWidth, (int) newHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * Notifies the backend to perform a print operation.
     *
     * @param printOperation The print operation to be performed.
     */
    private void notifyBackendToPrint(final PrintOperation printOperation) {
        final PrintLabelRequestMessage message = new PrintLabelRequestMessage();

        message.setLabelName(printOperation.getLabelName());
        message.setPrinterName(printOperation.getPrinterName());
        message.setCloudRollOrientation(printOperation.getCloudRollOrientation());
        message.setLabelOrientation(printOperation.getLabelOrientation());
        message.setDymoPaperTray(printOperation.getDymoPaperTray());
        message.setDymoLabelConfigFileName(printOperation.getDymoLabelConfigFileName());
        message.setXPosition(printOperation.getXPosition());
        message.setYPosition(printOperation.getYPosition());
        message.setWidth(printOperation.getWidth());
        message.setHeight(printOperation.getHeight());
        message.setLabelSizeUnit(printOperation.getLabelSizeUnit());
        message.setPrintToPdf(printOperation.isPrintToPdf());
        message.setPrintToPreview(printOperation.isPrintToPreview());
        message.setLabelPath(printOperation.getLabelImagePath());
        message.setPaperType(printOperation.getPaperType());
        message.setLpCommandOptionsZebra(printOperation.getLpCommandOptionsZebra());
        message.setLpCommandOptionsDymo(printOperation.getLpCommandOptionsDymo());
        message.setLpCommandOptionsBrother(printOperation.getLpCommandOptionsBrother());
        message.setRollType(printOperation.getRollType());
        message.setBrotherLabelConfigFileName(printOperation.getBrotherLabelConfigFileName());
        message.setPrintForLabelMaker(printOperation.isPrintForLabelMaker());
        message.setManualPrint(printOperation.isManualPrint());

        MainController controller = applicationContext.getBean(MainController.class);

        message.setPrintMode(printOperation.getPrintMode());

        if (printOperation.getPrintMode().equals(PrintMode.PROVISION)) {
            controller.printRequestForProvisionQr(message);
        } else if (PrintMode.TRANSACTION.equals(controller.getPrintRequestMode())) {
            message.setPrintMode(PrintMode.TRANSACTION);
            controller.printRequestForRecord(message, printOperation.getDevice());
        } else if (PrintMode.MANUAL_ENTRY.equals(controller.getPrintRequestMode())) {
            message.setPrintMode(PrintMode.MANUAL_ENTRY);
            controller.printRequestForRecord(message, printOperation.getDevice());
        } else if (PrintMode.AIRPOD.equals(controller.getPrintRequestMode())) {
            message.setPrintMode(PrintMode.AIRPOD);
            message.setDevice(printOperation.getDevice());
            controller.printRequestForAirpod(message, printOperation.getDevice().getId());
        } else if (printOperation.getPrintMode().equals(PrintMode.DEVICE)) {
            message.setPrintMode(PrintMode.DEVICE);
            controller.printRequestForDevice(message, printOperation.getDevice().getId());
        } else if (printOperation.getPrintMode().equals(PrintMode.IWATCH)) {
            controller.printRequestForIWatch(message, printOperation.getDevice());
        } else {
            controller.printRequestForRecord(message, printOperation.getDevice());
        }
    }

    /**
     * set the print mode for the print operation
     *
     * @param printOperation print operation
     */
    private void setPrintMode(final PrintOperation printOperation) {

        MainController controller = applicationContext.getBean(MainController.class);

        if (printOperation.getLabelName().equals(ANDROID_PROVISION_QR_LABEL.getName())) {
            printOperation.setPrintMode(PrintMode.PROVISION);
        } else if (PrintMode.TRANSACTION.equals(controller.getPrintRequestMode())) {
            printOperation.setPrintMode(PrintMode.TRANSACTION);
        } else if (PrintMode.MANUAL_ENTRY.equals(controller.getPrintRequestMode())) {
            printOperation.setPrintMode(PrintMode.MANUAL_ENTRY);
        } else if (PrintMode.IWATCH.equals(controller.getPrintRequestMode())) {
            printOperation.setPrintMode(PrintMode.IWATCH);
        } else {
            printOperation.setPrintMode(PrintMode.DEVICE);
        }
    }
}
