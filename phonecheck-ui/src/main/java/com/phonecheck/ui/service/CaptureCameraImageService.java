package com.phonecheck.ui.service;

import com.phonecheck.command.system.mac.OpenMacCameraCommand;
import com.phonecheck.command.system.windows.OpenWindowsCameraCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.util.OsChecker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class CaptureCameraImageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CaptureCameraImageService.class);
    // Delay after which image on the camera is automatically captured
    private static final String CAPTURE_IMAGE_DELAY = "3";
    private final CommandExecutor executor;
    private final OsChecker osChecker;

    public CaptureCameraImageService(final CommandExecutor executor, final OsChecker osChecker) {
        this.executor = executor;
        this.osChecker = osChecker;
    }

    /**
     * Logic to open selected camera in mac or windows and save the image in the destination file
     *
     * @param selectedCameraIndex          index of the selected camera
     * @param capturedImageDestinationFile destination file to save the captured image
     */
    public void openCameraWidget(final String selectedCameraIndex, final String capturedImageDestinationFile) {
        try {
            final String output;
            if (osChecker.isMac()) {
                output = executor.execute(new OpenMacCameraCommand(selectedCameraIndex,
                        CAPTURE_IMAGE_DELAY, capturedImageDestinationFile));
            } else {
                output = executor.execute(new OpenWindowsCameraCommand(selectedCameraIndex,
                        CAPTURE_IMAGE_DELAY, capturedImageDestinationFile));
            }
            LOGGER.info("Open selected camera widget command output: {}", output);
        } catch (Exception e) {
            LOGGER.warn("Exception occurred while opening camera widget", e);
        }
    }
}
