package com.phonecheck.ui.data.oem;

import com.jfoenix.controls.datamodels.treetable.RecursiveTreeObject;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PartEntry extends RecursiveTreeObject<PartEntry> {

    private final StringProperty part;
    private final StringProperty current;
    private final StringProperty factory;
    private final StringProperty status;
    private final StringProperty notice;

    public PartEntry(final String part, final String current, final String factory, final String status,
                     final String notice) {
        this.part = new SimpleStringProperty(part);
        this.current = new SimpleStringProperty(current);
        this.factory = new SimpleStringProperty(factory);
        this.status = new SimpleStringProperty(status);
        this.notice = new SimpleStringProperty(notice);
    }

    public StringProperty partProperty() {
        return part;
    }

    public StringProperty currentProperty() {
        return current;
    }

    public StringProperty factoryProperty() {
        return factory;
    }

    public StringProperty statusProperty() {
        return status;
    }

    public StringProperty noticeProperty() {
        return notice;
    }

}
