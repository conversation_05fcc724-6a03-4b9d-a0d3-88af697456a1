package com.phonecheck.ui;

import com.phonecheck.ui.controller.device.PortBoxController;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * This component will serve as an in memory store of the ports and their corresponding controllers.
 */
@Component
@Getter
@Setter
public class PortMappedDeviceBoxesMapping {

    private Map<Integer, PortBoxController> portControllerMap;

    public PortMappedDeviceBoxesMapping() {
        portControllerMap = new HashMap<>();
    }

    /**
     * Add port number to controller mapping in memory
     *
     * @param portNo     port number
     * @param controller IosDeviceBoxController
     */
    public void addPortControllerMapping(final Integer portNo, final PortBoxController controller) {
        portControllerMap.put(portNo, controller);
    }

    /**
     * Remove entry from controller map.
     *
     * @param portNo port number
     */
    public void removeDeviceControllerMapping(final Integer portNo) {
        portControllerMap.remove(portNo);
    }

    /**
     * Get the controller given a port number
     *
     * @param portNo port number
     * @return port box controller
     */
    public PortBoxController getControllerByPortNumber(final Integer portNo) {
        return portControllerMap.get(portNo);
    }

    /**
     * Get port number for a given controller
     *
     * @param controller PortBoxController
     * @return port number
     */
    public Integer getPortNumberForController(final PortBoxController controller) {
        for (Map.Entry<Integer, PortBoxController> entry : portControllerMap.entrySet()) {
            if (controller.getPortNumber() != null
                    && controller.getPortNumber().equals(entry.getValue().getPortNumber())) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * Remove all ios-device-box-controllers.
     */
    public void removeAllDeviceControllerMapping() {
        portControllerMap.clear();
    }
}
