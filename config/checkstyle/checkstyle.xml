<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
  Checkstyle is very configurable. Be sure to read the documentation at
  http://checkstyle.sf.net (or in your downloaded distribution).

  To completely disable a check, just comment it out or delete it from the file.
-->

<module name="Checker">
    <module name="SuppressionFilter">
        <property name="file" value="${config_loc}/suppressions.xml"/>
    </module>

    <!-- Checks that property files contain the same keys.         -->
    <module name="Translation"/>

    <!-- Checks for Size Violations.                    -->
    <!--    <module name="FileLength"/>-->

    <!-- Checks for whitespace                               -->
    <module name="FileTabCharacter"/>

    <!-- Miscellaneous other checks.                   -->
    <module name="RegexpSingleline">
        <property name="format" value="\s+$"/>
        <property name="minimum" value="0"/>
        <property name="maximum" value="0"/>
        <property name="message" value="Line has trailing spaces."/>
    </module>

    <module name="LineLength">
        <property name="max" value="120"/>
    </module>

    <module name="TreeWalker">
        <!-- Checks for Javadoc comments.                     -->
        <module name="JavadocMethod"/>
        <module name="JavadocType"/>
        <module name="JavadocStyle">
            <property name="checkFirstSentence" value="false"/>
            <property name="checkHtml" value="false"/>
        </module>

        <!-- Checks for Naming Conventions.                  -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <!-- Checks for imports                              -->
        <module name="IllegalImport"/> <!-- defaults to sun.* packages -->
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>

        <!-- Checks for Size Violations.                    -->
<!--        <module name="MethodLength"/>-->
<!--        <module name="ParameterNumber"/>-->

        <!-- Checks for whitespace                               -->
        <module name="EmptyForIteratorPad"/>
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>

        <!-- Modifier Checks                                    -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>

        <!-- Checks for blocks. You know, those {}'s         -->
        <!-- See http://checkstyle.sf.net/config_blocks.html -->
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock"/>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>

        <!-- Checks for common coding problems               -->
        <!--        <module name="AvoidInlineConditionals"/>-->
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <!--        <module name="MagicNumber"/>-->
        <module name="MissingSwitchDefault"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>

        <!-- Checks for class design                         -->
        <module name="FinalClass"/>
        <module name="InterfaceIsType"/>
        <module name="VisibilityModifier"/>

        <!-- Miscellaneous other checks.                   -->
        <module name="ArrayTypeStyle"/>
        <module name="FinalParameters"/>
        <module name="UpperEll"/>

        <!-- Allows explicit rule exceptions to be made by annotating the offending code with @SuppressWarnings("checkstyle:ruleName")-->
        <module name="SuppressWarningsHolder"/>
    </module>

</module>