package com.phonecheck.device.activation;

import com.phonecheck.command.device.ios.activation.IosActivateDeviceCommand;
import com.phonecheck.command.device.ios.activation.IosDeactivateDeviceCommand;
import com.phonecheck.command.device.ios.activation.IosIsActivatedCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.ActivationStatus;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.ios.activation.IosActivateDeviceParser;
import com.phonecheck.parser.device.ios.activation.IosActivationStatusParser;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosActivateDeviceServiceTest {
    @Mock
    private CommandExecutor executor;
    @Mock
    private IosActivateDeviceParser iosActivateDeviceParser;
    @Mock
    private IosActivationStatusParser iosActivationStatusParser;
    @InjectMocks
    private IosActivateDeviceService iosActivateDeviceService;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;

    @Test
    public void testIsActivated() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("id");
        when(executor.execute(any(IosIsActivatedCommand.class))).thenReturn("some value");
        when(iosActivationStatusParser.parse(any())).thenReturn(ActivationStatus.ACTIVATED);

        Boolean result = iosActivateDeviceService.isActivated(device);

        Assertions.assertEquals(true, result);
        verify(executor).execute(any(IosIsActivatedCommand.class));
        verify(iosActivationStatusParser).parse(eq("some value"));
    }

    @Test
    public void testActivateDevice() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("id");
        when(executor.execute(any(IosActivateDeviceCommand.class), anyLong())).thenReturn("some value");
        when(iosActivateDeviceParser.parse(any())).thenReturn(ActivationStatus.ACTIVATED);

        ActivationStatus result = iosActivateDeviceService.activate(device);

        Assertions.assertEquals(ActivationStatus.ACTIVATED, result);
        verify(executor).execute(any(IosActivateDeviceCommand.class), anyLong());
        verify(iosActivateDeviceParser).parse(eq("some value"));
    }

    @Test
    public void testDeactivateDevice() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("id");
        when(executor.execute(any(IosDeactivateDeviceCommand.class), anyLong())).thenReturn("Successfully" +
                " deactivated device");

        ActivationStatus result = iosActivateDeviceService.deactivate(device);

        Assertions.assertEquals(ActivationStatus.DEACTIVATED, result);
        verify(executor).execute(any(IosDeactivateDeviceCommand.class), anyLong());
    }
}
