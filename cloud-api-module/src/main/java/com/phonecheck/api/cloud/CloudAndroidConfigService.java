package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.AndroidConfigResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudAndroidConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudAndroidConfigService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get the Android Config from the cloud api
     *
     * @param brand android device brand
     * @param model android device model
     * @return an Android color info list
     */
    public AndroidConfigResponse getAndroidConfig(final String brand, final String model) {
        try {
            return cloudApiRestClient.getAndroidConfig(brand, model);
        } catch (final RestClientException e) {
            LOGGER.error("Error while getting android config from cloud", e);
            return null;
        }
    }
}
