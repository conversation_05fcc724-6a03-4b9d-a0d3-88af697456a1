package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class LabelFxmlService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LabelFxmlService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Get the Label fxml list from the cloud by label Ids.
     * Return all labels in master if empty list of ids passed.
     *
     * @param masterToken master token received after user login
     * @param labelIds    label ids
     * @return Map of labelName to LabelFxmlResponse.Data
     */
    public Map<String, LabelFxmlResponse.Data> getLabelsFromCloud(final String masterToken,
                                                                  final List<String> labelIds) {
        Map<String, LabelFxmlResponse.Data> response = null;
        try {
            LabelFxmlResponse output = phonecheckApiRestClient.getLabelFxmlList(masterToken);
            if (output != null && output.getDataList() != null) {
                if (labelIds == null || labelIds.isEmpty()) {
                    response = output.getDataList().stream()
                            .collect(Collectors.toMap(LabelFxmlResponse.Data::getName, data ->
                                    data, (existing, replacement) -> existing));
                } else {
                    response = output.getDataList().stream()
                            .filter(data -> {
                                String idStr = String.valueOf(data.getId());
                                return labelIds.contains(idStr);
                            })
                            .collect(Collectors.toMap(LabelFxmlResponse.Data::getName, data ->
                                    data, (existing, replacement) -> existing));
                }
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get label fxml response from the cloud", e);
        }
        return response;
    }
}
