package com.phonecheck.api.cloud;

import com.phonecheck.api.client.JapaneseTelephonyApiRestClient;
import com.phonecheck.model.status.JapaneseConformityStatus;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class JapaneseConformityCheckService {
    private static final Logger LOGGER = LoggerFactory.getLogger(JapaneseConformityCheckService.class);

    private final JapaneseTelephonyApiRestClient japaneseTelephonyApiRestClient;

    /**
     * Check if device has japanese conformity certification
     *
     * @param modelNo device model no
     * @return JapaneseConformityStatus
     */
    public JapaneseConformityStatus checkDeviceJapaneseConformityCertified(final String modelNo) {
        try {
            return japaneseTelephonyApiRestClient.checkDeviceJapaneseConformityCertified(modelNo);
        } catch (final RestClientException e) {
            LOGGER.error("Error when calling Japanese Conformity check endpoint", e);
            return null;
        }
    }
}
