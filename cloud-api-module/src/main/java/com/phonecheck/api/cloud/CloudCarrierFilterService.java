package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CarrierFilterInfoResponse;
import com.phonecheck.model.service.CarrierFilterDb;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudCarrierFilterService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudCarrierFilterService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get the carrier filter database from the cloud api
     *
     * @param version
     * @return a carrier filter info list
     */
    public CarrierFilterDb getCarrierFilterDb(final String version) {
        try {
            final CarrierFilterInfoResponse response = cloudApiRestClient.getLatestCarrierFilterInfo(version);

            return CarrierFilterDb.builder()
                    .carrierFilterInfo(response.getCarrierFilterInfo())
                    .build();
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get carrier filter db", e);
            return null;
        }
    }
}
