package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.LatestNetworkInfoResponse;
import com.phonecheck.model.cloudapi.SimTechnologyResponse;
import com.phonecheck.model.service.SimTechnologyDb;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudNetworkService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudNetworkService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get the Sim technology Database from the cloud api
     *
     * @param version
     * @return a sim technology db object
     */
    public SimTechnologyDb getSimTechnologyDb(final String version) {
        try {
            final LatestNetworkInfoResponse response = cloudApiRestClient
                    .getLatestNetworkInfo(version);

            return SimTechnologyDb.builder()
                    .networkInfo(response.getNetworkInfo())
                    .build();
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get sim technology db", e);
            return null;
        }
    }

    /**
     * Get the SimTechnology type for a particular model number and regulatory number
     *
     * @param modelNo
     * @param regulatoryNo
     * @return SimTechnologyResponse
     */
    public SimTechnologyResponse getSimTechnologyFromCloudByModelAndRegulatoryNo(
            final String modelNo, final String regulatoryNo) {
        try {
            return cloudApiRestClient.getSimTechnologyByModelAndRegulatoryNo(modelNo, regulatoryNo);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get sim tech by model and regulatory no.", e);
            return null;
        }
    }
}