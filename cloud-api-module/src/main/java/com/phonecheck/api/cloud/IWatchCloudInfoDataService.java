package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.GetIWatchInfoResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class IWatchCloudInfoDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchCloudInfoDataService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Get iWatch info data from cloud
     *
     * @param userToken      user token
     * @param masterToken    master token
     * @param iWatchSerialNo iWatch serial number
     * @return GetIWatchInfoResponse
     */
    public GetIWatchInfoResponse getIWatchInfoData(final String userToken, final String masterToken,
                                                   final String iWatchSerialNo) {
        try {
            GetIWatchInfoResponse response =
                    phonecheckApiRestClient.getIWatchInfoData(userToken, masterToken, iWatchSerialNo);
            LOGGER.info("getIWatchInfoData Response from cloud: {}", response);
            if (response != null && response.getData() != null) {
                return response;
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get iWatch info data from cloud", e);
        }
        return null;
    }
}
