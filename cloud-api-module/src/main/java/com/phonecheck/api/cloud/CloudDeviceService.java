package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.SyncedCloudDevice;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudDeviceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudDeviceService.class);
    private final CloudApiRestClient cloudApiRestClient;
    private static final int RETRIES_TO_GET_CLOUD_DEVICE = 3;

    /**
     * Get device data by serial number from cloud
     *
     * @param serial device serial number
     * @param licenseId license id
     * @return url
     */
    public SyncedCloudDevice getDeviceBySerial(final String serial, final Integer licenseId) {
        int attempt = 0;
        while (attempt < RETRIES_TO_GET_CLOUD_DEVICE) {
            try {
                final SyncedCloudDevice response = cloudApiRestClient.getDeviceBySerial(serial, licenseId);
                if (response != null) {
                    return response;
                }
                LOGGER.warn("Attempt {}: Response is null. Retrying...", attempt);
            } catch (final RestClientException e) {
                LOGGER.error("Attempt {}: Error while calling get device info by serial", attempt, e);
            }

            try {
                Thread.sleep(1000);
            } catch (Exception e) {
                // do nothing
            }

            attempt++;
        }
        return null;
    }
}
