package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class SamsungImeiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SamsungImeiService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Service to perform samsung imei check, and return the carrier
     *
     * @param userToken user token
     * @param imei      imei
     * @return carrier
     */
    public String getCarrierFromSamsungImeiCheck(final String userToken, final String imei) {
        try {
            return phonecheckApiRestClient.getSamsungImeiResponse(userToken, imei);
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get samsung imei response from cloud", e);
        }
        return null;
    }
}
