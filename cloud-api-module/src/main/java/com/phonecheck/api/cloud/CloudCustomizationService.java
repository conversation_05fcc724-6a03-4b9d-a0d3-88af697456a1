package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CustomizationPackageDataResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;

@Service
@AllArgsConstructor
public class CloudCustomizationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudCustomizationService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get the customization packages given the master id and warehouse id
     *
     * @param masterId    master id
     * @param warehouseId warehouse id
     * @return customization packages
     */
    public List<CustomizationPackageDataResponse.CustomizationPackage> getCustomizationPackages(
            final String masterId, final String warehouseId) {
        try {
            CustomizationPackageDataResponse response = cloudApiRestClient
                    .getCustomizationPackages(masterId, warehouseId);
            if (response != null) {
                return response.getCustomizationPackages();
            }
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get customization packages endpoint", e);
        }
        return null;
    }
}
