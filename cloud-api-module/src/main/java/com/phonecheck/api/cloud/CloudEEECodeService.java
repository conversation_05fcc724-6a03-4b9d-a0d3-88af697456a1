package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.EEECodeResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@AllArgsConstructor
public class CloudEEECodeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudEEECodeService.class);
    private final CloudApiRestClient cloudApiRestClient;
    private static final Cipher CIPHER = getCipher();
    private static final String SECRET_KEY = "23AB8H9J9B3B378N";
    private static final String ALGO_PARAMETER_INIT_VECTOR = "1A2B3H4U5U6I7I8D";
    private static final String ALGORITHM = "AES";

    /**
     * Get EEE codes from cloud and convert decrypted codes to a hashmap
     *
     * @return EEECodes hashmap
     */
    public Set<String> getEEECodeSet() {
        EEECodeResponse response = getEEECodes();
        if (response != null) {
            Set<String> eeeCodeSet = new HashSet<>();
            List<EEECodeResponse.OemPart> oemParts = response.getOemParts();
            for (EEECodeResponse.OemPart oemPart : oemParts) {
                List<EEECodeResponse.Device> devices = oemPart.getDevices();
                for (EEECodeResponse.Device device : devices) {
                    // TODO: look into separating EEE codes by part and device names
                    String eeeCode = device.getEee();
                    //e.g. decrypted EEECode: RSxrczpTU5Lelv9YbFuvhQ==
                    //e.g. encrypted EEECode: MFGR
                    String decryptedEEECode = decryptEEECode(eeeCode);
                    if (StringUtils.isNotEmpty(decryptedEEECode)) {
                        String[] arrOfStr = decryptedEEECode.split(",");
                        eeeCodeSet.addAll(Arrays.asList(arrOfStr));
                    }
                }
            }
            return eeeCodeSet;
        }
        return null;
    }

    /**
     * Get EEE codes for OEM components of each iphone devices from cloud
     *
     * @return EEECodeResponse
     */
    private EEECodeResponse getEEECodes() {
        try {
            return cloudApiRestClient.getEEECodes();
        } catch (final RestClientException e) {
            LOGGER.error("Error while retrieving EEE Codes", e);
            return null;
        }
    }

    /**
     * Method to decrypt given iphone devices OEM component EEE codes fetched from cloud
     *
     * @param eeeCode encrypted
     * @return decrypted eee code
     */
    private String decryptEEECode(final String eeeCode) {
        try {
            if (CIPHER != null) {
                byte[] original = CIPHER.doFinal(Base64.decodeBase64(eeeCode));
                return new String(original);
            }
        } catch (Exception ex) {
            LOGGER.error("Exception while decrypting EEE code", ex);
        }
        return null;
    }

    /**
     * Creates an instance of Cipher with given operation mode, secret key and algorithm parameter
     * This cipher is used to decrypt EEE codes from the cloud
     *
     * @return Cipher instance
     */
    private static Cipher getCipher() {
        try {
            SecretKeySpec sKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ALGO_PARAMETER_INIT_VECTOR.getBytes(StandardCharsets.UTF_8));
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec, ivSpec);
            return cipher;
        } catch (Exception ex) {
            LOGGER.error("Exception while initializing cipher", ex);
            return null;
        }
    }
}
