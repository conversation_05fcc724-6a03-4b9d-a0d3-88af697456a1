package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.phonecheckapi.UserLoginTokenResponse;
import com.phonecheck.model.util.NetworkCheckUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;

@Service
@AllArgsConstructor
public class UserLoginService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserLoginService.class);
    private static final int API_RETRIES = 3;

    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Get the User token after authentication
     *
     * @param username username
     * @param password password
     * @return token
     */
    public Map<String, String> getUserToken(final String username, final String password) {
        int attempt = 0;
        Map<String, String> result = new HashMap<>();
        do {
            try {
                UserLoginTokenResponse response = phonecheckApiRestClient
                        .getUserTokenAfterLogin(username, password);
                if (response != null) {
                    // Remove any error that might have been added in the previous attempts
                    if (result.get("error") != null) {
                        result.remove("error");
                    }
                    result.put("token", response.getToken());
                    break;
                }
            } catch (final RestClientException e) {
                LOGGER.error("Error while calling get user token", e);
                if (e.getCause() != null &&
                        StringUtils.containsIgnoreCase(e.getCause().getMessage(), "Network is unreachable")) {
                    LOGGER.info("Network is unreachable to get the user token");
                    result.put("error", ErrorConstants.CHECK_INTERNET_CONNECTION.getLocalizedKey());
                } else if (e.getCause() instanceof UnknownHostException) {
                    if (!NetworkCheckUtil.isInternetAvailable()) {
                        LOGGER.error("Unknown host, Network unreachable");
                        result.put("error", ErrorConstants.CHECK_INTERNET_CONNECTION.getLocalizedKey());
                    } else {
                        LOGGER.error("Unknown host, Cloud unreachable");
                        result.put("error", ErrorConstants.CLOUD_UNREACHABLE.getLocalizedKey());
                    }
                } else {
                    result.put("error", ErrorConstants.NO_USER_TOKEN.getLocalizedKey());
                }
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                // do nothing
            }

            attempt++;
        } while (attempt < API_RETRIES);

        return result;
    }

}
