package com.phonecheck.api.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.status.JapaneseConformityStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class JapaneseTelephonyApiRestClient extends AbstractCloudClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(JapaneseTelephonyApiRestClient.class);

    protected static final String MATCHED_CERTIFIED_RECORDS_LIST = "list";
    private static final String START_COUNT = "SC";
    private static final String DEVICE_RECORDS_COUNT = "DC";
    private static final String OUTPUT_FORMAT = "OF";
    private static final String DEVICE_MODEL = "TN";

    private final ObjectMapper mapper;

    public JapaneseTelephonyApiRestClient(final @Qualifier("japaneseTelephonyRestTemplate") RestTemplate restTemplate,
                                          final ObjectMapper objectMapper) {
        super(restTemplate);
        this.mapper = objectMapper;
    }

    /**
     * Checks if device is Japanese conformity certified
     *
     * @param deviceModelNo model no
     * @return JapaneseConformityStatus
     */
    public synchronized JapaneseConformityStatus checkDeviceJapaneseConformityCertified(final String deviceModelNo) {
        LOGGER.info("Calling the checkDeviceJapaneseConformityCertified endpoint");
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(MATCHED_CERTIFIED_RECORDS_LIST)
                .queryParam(START_COUNT, 9)
                .queryParam(DEVICE_RECORDS_COUNT, 7)
                .queryParam(OUTPUT_FORMAT, 2) // JSON
                .queryParam(DEVICE_MODEL, deviceModelNo);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<String> request = new HttpEntity<>(headers);
        String response = getRestTemplate()
                .exchange(builder.toUriString(), HttpMethod.GET, request, String.class)
                .getBody();

        // Parse the JSON response
        try {
            JsonNode jsonNode = mapper.readTree(response);
            JsonNode gitekiArray = jsonNode.get("giteki");
            JsonNode gitekiInfo = jsonNode.get("gitekiInformation");

            // Check if the "giteki" array size is greater than 0
            // i.e. There is a certification record found in the Japanese Telephony API
            if (gitekiArray != null && !gitekiArray.isEmpty()) {
                return JapaneseConformityStatus.CERTIFIED;
            } else if (gitekiInfo != null) {
                return JapaneseConformityStatus.NOT_CERTIFIED;
            } else {
                return JapaneseConformityStatus.NA;
            }
        } catch (Exception e) {
            LOGGER.error("Response for checkDeviceJapaneseConformityCertified api could not be processed.", e);
        }

        return JapaneseConformityStatus.FAILED_TO_RETRIEVE;
    }
}
