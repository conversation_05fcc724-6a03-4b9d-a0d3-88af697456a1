package com.phonecheck.api.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.cloudapi.*;
import com.phonecheck.model.phonecheckapi.StationLoginInfoResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.TestKeys;
import com.phonecheck.model.transaction.Transaction;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CloudApiRestClient extends AbstractCloudClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudApiRestClient.class);
    private static final Logger LICENSE_LOGGER = LoggerFactory.getLogger("LicenseLogger");

    //API Endpoints
    protected static final String CARRIER_AND_SIM_LOCK_CHECK_API = "CarrierLockLicenseManager/APICall";
    protected static final String CHARGE_LICENSE_API = "LicenseManager";
    protected static final String CLOUD_DB_SYNC_API = "CloudDbSync";
    protected static final String CLOUD_DB_PREVIOUS_TRANSACTION_API = "CloudDbSync/previousTransactionData";
    protected static final String CLOUD_DEVICE_LOOKUP_API = "cloud_db_sync/search_device_lookup";
    protected static final String DEVICE_FEATURES_API = "IOSModelsDB/IOSConfig";
    protected static final String DEVICE_RESTORE_FILTER_API = "Cloud_db_sync/get_restore_devices";
    protected static final String ESN_CHECK_API = "EsnLicenseManager/APIcall";
    protected static final String ESN_CHECK_MEND_API = "EsnLicenseManager/CheckMendApiAll";
    protected static final String GET_BATTERY_PROFILE_URL_API = "API/BatteryLifeFile";
    protected static final String GET_CLIENT_CUSTOMIZATION_API = "AssignTestCustomization/customizationplan_api";
    protected static final String GET_COLOR_CONFIGS_API = "DeviceCosmetics/getCustomColorsSettings";
    protected static final String GET_CUSTOMIZATION_PAKAGE_DATA_API = "CustomizationPackage/getPackages";
    protected static final String GET_CUSTOM_TEST_PLAN_API = "CustomTestPlan/customTestPlans";
    protected static final String GET_DEVICE_FAILURE_REASONS_API = "DeviceFailureReasons/APICall";
    protected static final String GET_DEV_DISK_IMAGES_API = "API/GetDevDiskImages";
    protected static final String GET_GRADE_CONFIGS_API = "DeviceCosmetics/getCustomGradesSettings";
    protected static final String GET_LATEST_ANDROID_COLOR_INFO_API = "AndroidColorsManagement/getLatestColorInfo";
    protected static final String GET_LATEST_CARRIER_FILTER_INFO_API = "CarrierFilter/getLatestCarrierFilterInfo";
    protected static final String GET_LATEST_CARRIER_INFO_API = "CarrierManagement/getLatestCarrierInfo";
    protected static final String GET_LATEST_FIRMWARE_LIST_API = "FirmwareList/ApiCall";
    protected static final String UPDATE_PENDING_COLOR_INFO_API = "ColorManagement/ApiCall";
    protected static final String GET_LATEST_IOS_COLOR_INFO_API = "ColorManagement/getLatestColorInfo";
    protected static final String GET_LATEST_IOS_MODEL_API = "IOSModelsDB/getLatestIOSModels";
    protected static final String GET_LATEST_NETWORK_INFO_API = "MobileNetworks/getLatestNetworkInfo";
    protected static final String GET_LOGO_API = "Customization/getLogos";
    protected static final String GET_MIC_THRESHOLD_API = "MicThreshold/getUserTest";
    protected static final String GET_SIM_TECHNOLOGY_API = "MobileNetworks/ApiCall";
    protected static final String GET_SOFTWARE_VERSION_API = "SWVersion/SWVersions/";
    protected static final String GET_TEST_KEYS_API = "AssignTestPlan/getTestKeys";
    protected static final String GET_VPP_TOKEN_API = "VppToken/ApiCall";
    protected static final String GET_VERSIONS_API = "APIRecords/getVersions";
    protected static final String SIM_LOCK_LICENSE_MANAGER_API_CALL = "SimLockLicenseManager/APIcall";
    protected static final String UPDATE_BY_COLUMN_API = "CloudDbSync/updateByColumn";
    protected static final String CREATE_TRANSACTION_API = "CloudDbSync/previousTransactionData";
    protected static final String GET_EEE_API = "EEECodes/ApiCall";
    protected static final String GET_VENDOR_INVOICE = "vendorInvoice/get_vendorInvoice";
    protected static final String TESTER_LOGIN_PHONE_CHECK = "TesterLogin";
    protected static final String GET_DEVICE_BY_SERIAL = "CloudDbSync/GetDeviceBySerial";
    protected static final String GET_ANDROID_CONFIG = "DeviceNameManagement/AndroidConfig";
    protected static final String GET_SKU_SCHEMA = "CustomSKU/getSkuSchema";
    protected static final String GET_INVENTORY_SKU_SCHEMA = "customSKU/InventorySkuSchema";
    protected static final String UPDATE_TRANSACTION = "CloudDbSync/UpdateTransactionId";

    //Request Parameters
    protected static final String API_KEY = "ApiKey";
    protected static final String BOX_NO = "BoxNo";
    protected static final String BUILD_NO = "BuildNo";
    protected static final String CARRIER = "Carrier";
    protected static final String CARRIER2 = "carrier";
    protected static final String COLOR = "color";
    protected static final String COLOR_CODE = "color_code";
    protected static final String CHECK_ALL_ESN = "checkAll";
    protected static final String CHECK_ALL_ESN_CHECK_MEND = "CheckAll";
    private static final String DEV_DISK_TOKEN_PAYLOAD_KEY = "\"key\"";
    private static final String DEV_DISK_TOKEN_PAYLOAD_VALUE = "\"Unfj397UHNDjdy678Husb66jbsj78ABDGs\"";
    protected static final String DEVICE_ID = "DeviceId";
    protected static final String DEVICE_IDENTITY = "DeviceIdentity";
    protected static final String DEVICE_TITLE = "DeviceTitle";
    protected static final String DEVICE_TYPE = "DeviceType";
    protected static final String GRADE = "grade";
    protected static final String IMEI = "Imei";
    protected static final String IMEI_1 = "IMEI1";
    protected static final String IMEI_2 = "IMEI2";
    protected static final String INVOICE_NO = "InvoiceNo";
    protected static final String IS_MEID = "isMEID";
    protected static final String KEY = "key";
    protected static final String TRANSACTION_ID = "TransactionID";
    protected static final String LICENSE_ID = "user_id";
    protected static final String LICENSE_ID1 = "LicenseID";
    protected static final String LICENSE_TYPE = "LicenseType";
    protected static final String MASTER_ID = "masterId";
    protected static final String MASTER_ID_2 = "master_id";
    protected static final String MEID = "MEID";
    protected static final String METHOD = "Method";
    protected static final String MODEL = "Model";
    protected static final String MODEL_NO = "model_no";
    protected static final String MODEL_NAME = "model_name";
    protected static final String NEW_TRANSACTION = "newTransaction";
    protected static final String OS = "OS";
    protected static final String PRODUCT_TYPE = "productType";
    protected static final String QTY = "QTY";
    protected static final String REGULATORY_MODEL = "RegulatoryModel";
    protected static final String SERIAL = "Serial";
    protected static final String SERVICE_ID = "ServiceId";
    protected static final String STATION_ID = "StationID";
    protected static final String STATION_NAME = "station_name";
    protected static final String STORAGE = "storage";
    protected static final String TESTER_ID = "TesterId";
    protected static final String TITLE = "title";
    protected static final String TOUCH_ID = "touch_id";
    protected static final String UDID = "Udid";
    protected static final String USER_ID1 = "UserId";
    protected static final String USER_ID2 = "userId";
    protected static final String USER_ID3 = "user_id";
    protected static final String USER_IDENTITY = "UserIdentity";
    protected static final String VENDOR_NAME = "VendorName";
    protected static final String VERSION = "version";
    private static final String VPP_TOKEN_PAYLOAD_KEY = "key";
    private static final String VPP_TOKEN_PAYLOAD_VALUE = "Ato3tuskoP3e8gTyKLeetovpkennc";
    protected static final String WAREHOUSE_ID = "warehouseId";
    protected static final String WAREHOUSE_ID_ESN = "warehouse_id";
    protected static final String TESTER_NAME_KEY = "tester_name";
    protected static final String PASSWORD_KEY = "password";
    protected static final String PC_ID_KEY = "pc_id";
    protected static final String OFFSET = "offset";
    protected static final String LIMIT = "limit";
    protected static final String BRAND = "brand";
    protected static final String TYPE = "type";
    protected static final String LOOKUP_ID = "lookup_id";

    protected static final String OLD_TRANSACTION_ID = "old_transactionId";

    protected static final String NEW_TRANSACTION_ID = "newTransactionId";
    protected static final String TRANSACTION_DATE = "TransactionDate";
    //Dependency objects
    private final InMemoryStore inMemoryStore;
    private final ObjectMapper mapper;

    public CloudApiRestClient(
            @Qualifier("cloudRestTemplate") final RestTemplate restTemplate,
            @Qualifier("euCloudRestTemplate") final RestTemplate euRestTemplate,
            final InMemoryStore inMemoryStore, final ObjectMapper mapper) {
        super(restTemplate, euRestTemplate);
        this.inMemoryStore = inMemoryStore;
        this.mapper = mapper;
    }

    /**
     * Get test keys information by license id.
     *
     * @param licenseId
     * @return test keys response
     */
    public synchronized TestKeys getTestKeys(final int licenseId) {
        LOGGER.info("Calling the get test keys by license id endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(LICENSE_ID, String.valueOf(licenseId));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);
        // This endpoint returns with a content type text/html and so
        // restTemplate cannot deserialize it into a response object.
        String strResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_TEST_KEYS_API, request,
                        String.class);
        // Manually deserialize the response into an object
        TestKeys response = null;
        try {
            response = mapper.readValue(strResponse, TestKeys.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for get test keys api could not be processed.", e);
        }
        return response;
    }

    /**
     * Get carrier sim lock status
     *
     * @param carrierSimLockStatusRequest
     * @return sim lock status response
     */
    public synchronized CarrierSimLockStatusResponse getCarrierAndSimLockStatus(
            final CarrierSimLockStatusRequest carrierSimLockStatusRequest) {
        LOGGER.info("Calling the get carrier and sim lock status endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(DEVICE_ID, carrierSimLockStatusRequest.getDeviceId());
        payload.add(USER_ID1, carrierSimLockStatusRequest.getUserId());
        payload.add(API_KEY, carrierSimLockStatusRequest.getApiKey());
        payload.add(CARRIER, carrierSimLockStatusRequest.getCarrier());
        payload.add(DEVICE_TYPE, carrierSimLockStatusRequest.getDeviceType());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        // This endpoint returns with a content type text/html and so
        // restTemplate cannot deserialize it into a response object.
        String strResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CARRIER_AND_SIM_LOCK_CHECK_API, request,
                        String.class);

        // Manually deserialize the response into an object
        CarrierSimLockStatusResponse response = null;
        try {
            response = mapper.readValue(strResponse, CarrierSimLockStatusResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for carrier and sim lock api could not be processed.", e);
        }
        return response;
    }

    /**
     * Get the latest network information from the cloud API
     *
     * @param version
     * @return a LatestNetworkInfoResponse
     */
    public synchronized LatestNetworkInfoResponse getLatestNetworkInfo(
            final String version) {
        LOGGER.info("Calling the get latest network info endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VERSION, version);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LATEST_NETWORK_INFO_API, request,
                        LatestNetworkInfoResponse.class);
    }

    /**
     * Get the SimTechnology response for a particular model number and regulatory number from cloud
     *
     * @param modelNo
     * @param regulatoryNo
     * @return SimTechnologyResponse
     */
    public synchronized SimTechnologyResponse getSimTechnologyByModelAndRegulatoryNo(final String modelNo,
                                                                                     final String regulatoryNo) {
        LOGGER.info("Calling the get sim technology endpoint for model: {} and regulatory no: {}",
                modelNo, regulatoryNo);
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MODEL, modelNo);
        payload.add(REGULATORY_MODEL, regulatoryNo);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        String strResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_SIM_TECHNOLOGY_API, request,
                        String.class);
        SimTechnologyResponse response = null;
        try {
            response = mapper.readValue(strResponse, SimTechnologyResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for sim technology api could not be processed.", e);
        }
        return response;

    }

    /**
     * Get Latest IOS Models for the given version
     *
     * @param version IOS model version
     * @return IosModelResponse
     */
    public synchronized IosModelResponse getLatestIosModels(final String version) {
        LOGGER.info("Calling the get latest IOS models endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VERSION, version);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LATEST_IOS_MODEL_API, request, IosModelResponse.class);
    }

    /**
     * Get the iOS color information from the cloud API
     *
     * @param version
     * @return a IosColorInfoResponse
     */
    public synchronized IosColorInfoResponse getLatestIosColorInfo(
            final String version) {
        LOGGER.info("Calling the get latest IOS colors endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VERSION, version);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LATEST_IOS_COLOR_INFO_API, request,
                        IosColorInfoResponse.class);
    }

    /**
     * Send the pending color information to the cloud API
     *
     * @param pendingColorInfoRequest
     * @return a response in String
     */
    public synchronized String syncPendingColorInfoToCloud(final PendingColorInfoRequest pendingColorInfoRequest) {
        LOGGER.info("Calling the pending color api endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MODEL.toLowerCase(), pendingColorInfoRequest.getModelNo());
        payload.add(TITLE, pendingColorInfoRequest.getModel());
        payload.add(IMEI.toLowerCase(), pendingColorInfoRequest.getImei());
        payload.add(SERIAL.toLowerCase(), pendingColorInfoRequest.getSerial());
        payload.add(COLOR_CODE, pendingColorInfoRequest.getColorCode());
        payload.add(USER_ID3, pendingColorInfoRequest.getUserId());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(UPDATE_PENDING_COLOR_INFO_API, request, String.class);
    }

    /**
     * Get the android color information from the cloud API
     *
     * @param version
     * @return a AndroidColorInfoResponse
     */
    public synchronized AndroidColorInfoResponse getLatestAndroidColorInfo(
            final String version) {
        LOGGER.info("Calling the get latest android colors endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VERSION, version);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LATEST_ANDROID_COLOR_INFO_API, request,
                        AndroidColorInfoResponse.class);
    }

    /**
     * Get the carrier filter information from the cloud API
     *
     * @param version
     * @return a CarrierFilterInfoResponse
     */
    public synchronized CarrierFilterInfoResponse getLatestCarrierFilterInfo(
            final String version) {
        LOGGER.info("Calling the get latest carrier filter endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VERSION, version);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LATEST_CARRIER_FILTER_INFO_API, request,
                        CarrierFilterInfoResponse.class);
    }

    /**
     * Get the customization packages from the cloud API
     *
     * @param masterId
     * @param warehouseId
     * @return a CustomizationPackageDataResponse
     */
    public synchronized CustomizationPackageDataResponse getCustomizationPackages(
            final String masterId, final String warehouseId) {
        LOGGER.info("Calling the get customization packages endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MASTER_ID, masterId);
        payload.add(WAREHOUSE_ID, warehouseId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_CUSTOMIZATION_PAKAGE_DATA_API, request,
                        CustomizationPackageDataResponse.class);
    }

    /**
     * Get logos for the given master Id
     *
     * @param masterId Logo master Id
     * @return LogoResponse
     */
    public synchronized LogoResponse getLogos(final String masterId) {
        LOGGER.info("Calling the get logos endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MASTER_ID, masterId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LOGO_API, request, LogoResponse.class);

    }

    /**
     * Get the latest carrier info from the cloud API
     *
     * @param version Carrier info version
     * @return CarrierInfoResponse
     */
    public synchronized CarrierInfoResponse getLatestCarrierInfo(final String version) {
        LOGGER.info("Calling the get latest carrier info endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VERSION, version);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LATEST_CARRIER_INFO_API, request, CarrierInfoResponse.class);
    }

    /**
     * Get the latest firmware information from the cloud API
     *
     * @return a LatestFirmwareListResponse
     */
    public synchronized LatestFirmwareListResponse getLatestFirmwareListInfo() {
        LOGGER.info("Calling the get latest firmware list endpoint");

        // restTemplate cannot deserialize it into a response object.
        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .getForObject(GET_LATEST_FIRMWARE_LIST_API,
                        String.class);
        LatestFirmwareListResponse.LatestFirmwareInfo[] latestFirmwareInfo = null;

        // map response string to firmware object array
        try {
            latestFirmwareInfo = mapper.readValue(stringResponse,
                    LatestFirmwareListResponse.LatestFirmwareInfo[].class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for firmware api could not be processed.", e);
        }
        return LatestFirmwareListResponse.builder().latestFirmwareInfo(latestFirmwareInfo).build();
    }

    public synchronized EsnResponse getEsnLicenseInfo(final EsnLicenseCheckRequest licenseCheckRequest) {
        LOGGER.info("Calling the get ESN info endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(API_KEY, licenseCheckRequest.getApiKey());
        payload.add(DEVICE_ID, licenseCheckRequest.getImei());
        payload.add(MEID, licenseCheckRequest.getMeid());
        payload.add(IS_MEID, licenseCheckRequest.getIsMeid().toString());
        payload.add(USER_ID1, licenseCheckRequest.getUserId());
        payload.add(CARRIER, licenseCheckRequest.getCarrier());
        payload.add(SERVICE_ID, licenseCheckRequest.getServiceId());
        payload.add(DEVICE_TYPE, licenseCheckRequest.getDeviceType());
        payload.add(CHECK_ALL_ESN, licenseCheckRequest.getCheckAll().toString());
        payload.add(TESTER_ID, licenseCheckRequest.getTesterId());
        payload.add(WAREHOUSE_ID_ESN, licenseCheckRequest.getWarehouseId());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        // restTemplate cannot deserialize it into a response object.
        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(ESN_CHECK_API, request,
                        String.class);

        EsnResponse esnResponse;

        try {
            // if check all is set to 1, then response is an array of JSONs, else a single object
            if (licenseCheckRequest.getCheckAll().equals(1)) {
                EsnResponse.EsnApiResponse[] esnApiResponses = mapper.readValue(stringResponse,
                        EsnResponse.EsnApiResponse[].class);

                esnResponse = EsnResponse
                        .builder()
                        .esnApiResults(esnApiResponses)
                        .build();
            } else {
                EsnResponse.EsnApiResponse esnApiResponse = mapper.readValue(stringResponse,
                        EsnResponse.EsnApiResponse.class);

                esnResponse = EsnResponse
                        .builder()
                        .esnApiResults(new EsnResponse.EsnApiResponse[]{esnApiResponse})
                        .build();
            }
        } catch (JsonProcessingException e) {
            // id ESN api response parsing failed try to parse the esn license expired response
            try {
                EsnResponse.EsnLicensesExpiredResponse esnLicensesExpiredResponse = mapper.readValue(stringResponse,
                        EsnResponse.EsnLicensesExpiredResponse.class);

                esnResponse = EsnResponse
                        .builder()
                        .licensesExpiredResponse(esnLicensesExpiredResponse)
                        .build();
            } catch (JsonProcessingException ex) {
                LOGGER.error("Failed to parse response for ESN check.", ex);
                return null;
            }
        }

        return esnResponse;
    }


    /**
     * Third party Api to check an esn for a device
     *
     * @param licenseCheckRequest esnLicenseCheck Request with all required params
     * @return Esn Response from checkmend API
     */
    public EsnResponse getEsnInfoByCheckMend(final EsnLicenseCheckRequest licenseCheckRequest) {
        LOGGER.info("Calling the get ESN info from checkmend endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(IMEI_1, licenseCheckRequest.getImei());
        payload.add(IMEI_2, licenseCheckRequest.getImei2());
        payload.add(SERIAL, licenseCheckRequest.getSerial());
        payload.add(CARRIER, licenseCheckRequest.getCarrier());
        payload.add(USER_ID1, licenseCheckRequest.getUserId());
        payload.add(SERVICE_ID, licenseCheckRequest.getServiceId());
        payload.add(DEVICE_TYPE, licenseCheckRequest.getDeviceType());
        payload.add(CHECK_ALL_ESN_CHECK_MEND, String.valueOf((licenseCheckRequest.getCheckAll() == 1)));
        payload.add(TESTER_ID, licenseCheckRequest.getTesterId());
        payload.add(WAREHOUSE_ID_ESN, licenseCheckRequest.getWarehouseId());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        // restTemplate cannot deserialize it into a response object.
        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(ESN_CHECK_MEND_API, request,
                        String.class);

        EsnResponse esnResponse;

        try {
            EsnResponse.EsnApiResponse[] esnApiResponses = mapper.readValue(stringResponse,
                    EsnResponse.EsnApiResponse[].class);

            esnResponse = EsnResponse
                    .builder()
                    .esnApiResults(esnApiResponses)
                    .build();
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse response for CheckMend ESN check.", e);
            return null;
        }

        return esnResponse;
    }

    /**
     * Cloud API to charge license for given licenseType and user
     *
     * @param deviceIdentity
     * @param imei
     * @param licenseType
     * @param deviceType
     * @param deviceTitle
     * @param userName
     * @param buildNo
     * @param testerId
     * @param warehouseId
     * @return String remaining Licenses for given license type and user
     */
    public synchronized String chargeLicense(final String deviceIdentity, final String imei, final String licenseType,
                                             final String deviceType, final String deviceTitle, final String userName,
                                             final String buildNo, final String testerId, final String warehouseId) {
        LOGGER.info("Calling the charge license endpoint with deviceIdentity: {}, imei: {}, licenseType: {}," +
                        " deviceType: {}, deviceTitle: {}, userName: {}, buildNo: {}, testerId: {}, warehouseId: {}",
                deviceIdentity, imei, licenseType, deviceType, deviceTitle, userName, buildNo, testerId, warehouseId);
        LICENSE_LOGGER.info("Calling the charge license endpoint with deviceIdentity: {}, imei: {}, licenseType: {}," +
                        " deviceType: {}, deviceTitle: {}, userName: {}, buildNo: {}, testerId: {}, warehouseId: {}",
                deviceIdentity, imei, licenseType, deviceType, deviceTitle, userName, buildNo, testerId, warehouseId);
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(DEVICE_IDENTITY, deviceIdentity);
        payload.add(IMEI, imei);
        payload.add(LICENSE_TYPE, licenseType);
        payload.add(DEVICE_TYPE, deviceType);
        payload.add(DEVICE_TITLE, deviceTitle);
        payload.add(USER_IDENTITY, userName);
        payload.add(BUILD_NO, buildNo);
        payload.add(TESTER_ID, testerId);
        payload.add(WAREHOUSE_ID_ESN, warehouseId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CHARGE_LICENSE_API, request, String.class);
    }

    /**
     * Retrieves the vpp token from the cloud
     *
     * @return VppTokenResponse
     */
    public synchronized VppTokenResponse getVppToken() {
        LOGGER.info("Calling the get VPP token endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(VPP_TOKEN_PAYLOAD_KEY, VPP_TOKEN_PAYLOAD_VALUE);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String strResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_VPP_TOKEN_API, request, String.class);
        try {
            return mapper.readValue(strResponse, VppTokenResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for getVppToken api could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to get device restore/erase filter
     *
     * @param udid
     * @param imei
     * @param serial
     * @return String value that whether given device is filtered for restore/erase
     */
    public synchronized String getDeviceRestoreFilter(final String udid, final String imei, final String serial) {
        LOGGER.info("Calling the get device restore filter endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(UDID, udid);
        payload.add(IMEI, imei);
        payload.add(SERIAL, serial);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(DEVICE_RESTORE_FILTER_API, request, String.class);
    }

    /**
     * Cloud API to get Dev Disk Images
     *
     * @return DevDiskImagesResponse object that contains DevDiskImages
     */
    public DevDiskImagesResponse getDevDiskImages() {
        LOGGER.info("Calling the get dev disk images endpoint");
        String payload = String.format("{%s:%s}",
                DEV_DISK_TOKEN_PAYLOAD_KEY,
                DEV_DISK_TOKEN_PAYLOAD_VALUE
        );

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> request = new HttpEntity<>(payload, headers);
        String strResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_DEV_DISK_IMAGES_API, request, String.class);

        try {
            return mapper.readValue(strResponse, DevDiskImagesResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for getDevDiskImages api could not be processed.", e);
        }
        return null;
    }

    /**
     * Syncs device data to cloud
     *
     * @param cloudDbSyncRequest
     * @return "success" if data successfully syncs
     */
    public synchronized String syncDataToCloudDb(final CloudDbSyncRequest cloudDbSyncRequest) {
        LOGGER.info("Calling the cloud sync endpoint");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        HttpEntity<String> request;
        try {
            request = new HttpEntity<>(mapper.writeValueAsString(cloudDbSyncRequest), headers);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for syncDataToCloudDb api could not be processed.", e);
            return null;
        }
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CLOUD_DB_SYNC_API, request,
                        String.class);
    }

    /**
     * Retrieves previous transaction device records from cloud
     *
     * @param transactionDevicesRequest request object to retrieve devices data by transaction
     * @return PreviousTransaction
     */
    public synchronized TransactionResponse getTransactionDevices(
            final TransactionDevicesRequest transactionDevicesRequest
    ) {
        LOGGER.debug("Calling the previous transaction endpoint");

        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(LICENSE_ID1, String.valueOf(transactionDevicesRequest.getLicenseId()));
        payload.add(TRANSACTION_ID, String.valueOf(transactionDevicesRequest.getTransactionId()));

        if (transactionDevicesRequest.getOffset() != -1) {
            payload.add(OFFSET, String.valueOf(transactionDevicesRequest.getOffset()));
            payload.add(LIMIT, String.valueOf(transactionDevicesRequest.getLimit()));
        }
        payload.add(METHOD, "getDevices");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CLOUD_DB_PREVIOUS_TRANSACTION_API, request,
                        String.class);
        try {
            TransactionResponse.TransactionRecordResponse[] previousTransactionRecords =
                    mapper.readValue(response, TransactionResponse.TransactionRecordResponse[].class);

            return TransactionResponse.builder()
                    .transactionRecords(previousTransactionRecords)
                    .build();
        } catch (JsonProcessingException e) {
            LOGGER.error("Response from previous transaction api could not be processed.", e);
        }

        return null;
    }

    /**
     * Retrieves device lookup records from cloud
     *
     * @param licenseId   license id
     * @param type        search type
     * @param lookupValue value
     * @return response
     */
    public TransactionResponse getCloudDeviceLookup(
            final String licenseId, final String type, final String lookupValue) {
        LOGGER.info("Calling the cloud device lookup endpoint");

        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(LICENSE_ID1, licenseId);
        payload.add(TYPE, type);
        payload.add(LOOKUP_ID, lookupValue);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CLOUD_DEVICE_LOOKUP_API, request,
                        String.class);
        LOGGER.info("Response cloud device lookup endpoint : {}", response);
        try {
            TransactionResponse.TransactionRecordResponse[] deviceLookupRecords =
                    mapper.readValue(response, TransactionResponse.TransactionRecordResponse[].class);

            return TransactionResponse.builder()
                    .transactionRecords(deviceLookupRecords)
                    .build();
        } catch (JsonProcessingException e) {
            LOGGER.error("Response from cloud device lookup api could not be processed.", e);
        }

        return null;
    }

    /**
     * Retrieves battery profile url from cloud
     *
     * @return String content for the file
     */
    public synchronized String getBatteryProfileUrl() {
        LOGGER.info("Calling the get battery profile endpoint");
        return getRestTemplate(inMemoryStore.isEuServer())
                .getForEntity(GET_BATTERY_PROFILE_URL_API,
                        String.class).getBody();
    }

    /**
     * Retrieves data for FailureReasons.json file
     *
     * @param masterID
     * @param apiKey
     * @return String data
     */
    public synchronized String getDeviceFailureReasons(final String masterID, final String apiKey) {
        LOGGER.info("Calling the get device failure reasons endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MASTER_ID_2, masterID);
        payload.add(API_KEY, apiKey);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_DEVICE_FAILURE_REASONS_API, request,
                        String.class);
    }

    /**
     * Cloud API to get grade configurations
     *
     * @param licenceId license id
     * @return object containing the grade configuration
     */
    public synchronized GradeConfigResponse getGradeConfigs(final int licenceId) {
        LOGGER.info("Calling the get grade configs endpoint");
        MultiValueMap<String, Integer> payload = new LinkedMultiValueMap<>();
        payload.add(USER_ID1, licenceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, Integer>> request = new HttpEntity<>(payload, headers);

        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_GRADE_CONFIGS_API, request, String.class);
        try {
            return mapper.readValue(response, GradeConfigResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for grade configs api could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to get color configurations
     *
     * @param licenceId license id
     * @return object containing the color configuration
     */
    public synchronized ColorConfigResponse getColorConfigs(final int licenceId) {
        LOGGER.info("Calling the get color configs endpoint");
        MultiValueMap<String, Integer> payload = new LinkedMultiValueMap<>();
        payload.add(USER_ID1, licenceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, Integer>> request = new HttpEntity<>(payload, headers);

        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_COLOR_CONFIGS_API, request, String.class);
        try {
            return mapper.readValue(response, ColorConfigResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for color configs api could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to get device features
     *
     * @param productType product type
     * @return object containing the device features
     */
    public synchronized DeviceFeaturesResponse getDeviceFeatures(final String productType) {
        LOGGER.info("Calling the get device features endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(PRODUCT_TYPE, productType);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(DEVICE_FEATURES_API, request, String.class);

        LOGGER.info("Received device features from cloud: {}", response);

        try {
            return mapper.readValue(response, DeviceFeaturesResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for device features api could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to get client customization
     *
     * @param licenceId license id
     * @return json string of client customization as we don't know the response
     */
    public synchronized String getClientCustomization(final int licenceId) {
        LOGGER.info("Calling the get client customizations endpoint");
        MultiValueMap<String, Integer> payload = new LinkedMultiValueMap<>();
        payload.add(LICENSE_ID, licenceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, Integer>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_CLIENT_CUSTOMIZATION_API, request, String.class);
    }

    /**
     * Cloud API to get custom test plans for client customization
     *
     * @param masterId masterId
     * @return CustomTestPlansResponse array
     */
    public synchronized CustomTestPlansResponse[] getCustomTestPlans(final String masterId) {
        LOGGER.info("Calling the get custom test plans endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MASTER_ID, masterId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_CUSTOM_TEST_PLAN_API, request, String.class);
        try {
            return mapper.readValue(response, CustomTestPlansResponse[].class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for custom test plans api could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to get software versions for client customization
     *
     * @param masterId masterId
     * @return SoftwareVersionsResponse array
     */
    public synchronized SoftwareVersionsResponse[] getSoftwareVersions(final String masterId) {
        LOGGER.info("Calling the get software versions endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(MASTER_ID, masterId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_SOFTWARE_VERSION_API, request, String.class);
        try {
            return mapper.readValue(response, SoftwareVersionsResponse[].class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for software versions api could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to get mic threshold for client customization
     *
     * @param licenceId licenceId
     * @return MicThresholdResponse
     */
    public synchronized MicThresholdResponse getMicThreshold(final int licenceId) {
        LOGGER.info("Calling the get mic threshold endpoint");
        MultiValueMap<String, Integer> payload = new LinkedMultiValueMap<>();
        payload.add(USER_ID2, licenceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, Integer>> request = new HttpEntity<>(payload, headers);
        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_MIC_THRESHOLD_API, request, String.class);
        try {
            return mapper.readValue(response, MicThresholdResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for mic threshold api could not be processed.", e);
        }
        return null;
    }

    /**
     * Call cloud sync api to update column info
     *
     * @param updateByColumnRequest UpdateByColumnRequest
     * @return a String i,e: "Post data is missing" / "Success"/ "Fail"
     */
    public synchronized String syncColumnInfoToCloudDb(
            final UpdateByColumnRequest updateByColumnRequest) {
        LOGGER.info("Calling the update by column db sync endpoint LicenseID: {}",
                updateByColumnRequest.getLicenseId());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        HttpEntity<String> request;
        try {
            request = new HttpEntity<>(mapper.writeValueAsString(updateByColumnRequest), headers);
        } catch (JsonProcessingException jsonProcessingException) {
            LOGGER.error("Response for updateByColumn api could not be processed.", jsonProcessingException);
            return null;
        }
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(UPDATE_BY_COLUMN_API, request,
                        String.class);
    }

    /**
     * Call update by column api to update devcice disconnect info
     *
     * @param updateDeviceDisconnectRequest updateDeviceDisconnectRequest
     * @return a String i,e: "Post data is missing" / "Success"/ "Fail"
     */
    public synchronized String syncDeviceDisconnectToCloudDb(
            final UpdateDeviceDisconnectRequest updateDeviceDisconnectRequest) {
        LOGGER.info("Calling the update by column (for disconnect) endpoint LicenseID: {}",
                updateDeviceDisconnectRequest.getLicenseId());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        HttpEntity<String> request;
        try {
            request = new HttpEntity<>(mapper.writeValueAsString(updateDeviceDisconnectRequest), headers);
        } catch (JsonProcessingException jsonProcessingException) {
            LOGGER.error("Response for updateByColumn api (for disconnect) could not be processed.",
                    jsonProcessingException);
            return null;
        }
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(UPDATE_BY_COLUMN_API, request,
                        String.class);
    }

    /**
     * Call the update by column api to update transaction info
     *
     * @param editTransactionSyncRequest editTransactionSyncRequest
     * @return a String i,e: "Post data is missing" / "Success"/ "Fail"
     */
    public synchronized String editTransactionInCloudDb(final EditTransactionSyncRequest editTransactionSyncRequest) {
        LOGGER.info("Calling the edit transaction sync endpoint LicenseID: {}",
                editTransactionSyncRequest.getLicenseId());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        HttpEntity<String> request;
        try {
            request = new HttpEntity<>(mapper.writeValueAsString(editTransactionSyncRequest), headers);
        } catch (JsonProcessingException jsonProcessingException) {
            LOGGER.error("Response for edit transaction api could not be processed.", jsonProcessingException);
            return null;
        }
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(UPDATE_BY_COLUMN_API, request,
                        String.class);
    }

    /**
     * Get API Records Versions for phonecheck cloud endpoints
     *
     * @param key GetVersionsRequest tokenKey
     * @return GetVersionsResponse object with versions details"
     */
    public synchronized GetVersionsResponse getApiVersions(
            final String key) {
        LOGGER.info("Calling the get API Versions endpoint");
        final Map<String, String> map = new HashMap<>();
        map.put(KEY, key);
        final String payload;
        try {
            payload = mapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse payload for getting versions", e);
            return null;
        }
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        final HttpEntity<String> request = new HttpEntity<>(payload, headers);

        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_VERSIONS_API, request, String.class);
        GetVersionsResponse getVersionsResponse;
        try {
            mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
            getVersionsResponse = mapper.readValue(response, GetVersionsResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for APIRecordsVersions api could not be processed.", e);
            return null;
        }

        return getVersionsResponse;
    }

    /**
     * Cloud api to create the transaction
     *
     * @param transaction to be created
     * @return CloudTransactionResponse
     */
    public synchronized CloudTransactionResponse createTransactionFromCloud(final Transaction transaction) {
        LOGGER.info("Calling the create transaction endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(LICENSE_ID1, String.valueOf(transaction.getLicenseId()));
        payload.add(STATION_ID, transaction.getStationId());
        payload.add(VENDOR_NAME, transaction.getVendorName());
        payload.add(BOX_NO, transaction.getBoxNo());
        payload.add(QTY, transaction.getQty());
        payload.add(INVOICE_NO, transaction.getInvoiceNo());
        payload.add(METHOD, NEW_TRANSACTION);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CREATE_TRANSACTION_API, request, String.class);
        try {
            return mapper.readValue(response, CloudTransactionResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for create transaction api could not be processed.", e);
        }
        return null;
    }

    /**
     * Call cloud api to get SimLock License Manager details
     *
     * @param simLockLicenseManagerRequest SimLockLicenseManagerRequest
     * @return SimLockLicenseManagerResponse, a domain object for SimLock License Manager details
     */
    public synchronized SimLockLicenseManagerResponse getSimLockLicenseManagerResponse(
            final SimLockLicenseManagerRequest simLockLicenseManagerRequest) {
        LOGGER.info("Calling the get sim lock info endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(DEVICE_ID, simLockLicenseManagerRequest.getDeviceId());
        payload.add(USER_ID1, simLockLicenseManagerRequest.getUserId());
        payload.add(API_KEY, simLockLicenseManagerRequest.getApiKey());
        payload.add(CARRIER, simLockLicenseManagerRequest.getCarrier());
        payload.add(DEVICE_TYPE, simLockLicenseManagerRequest.getDeviceType());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(SIM_LOCK_LICENSE_MANAGER_API_CALL, request,
                        String.class);

        SimLockLicenseManagerResponse simLockLicenseManagerResponse;
        try {
            simLockLicenseManagerResponse =
                    mapper.readValue(stringResponse, SimLockLicenseManagerResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse response for SimLock License Manager.", e);
            simLockLicenseManagerResponse = null;
        }

        return simLockLicenseManagerResponse;
    }

    /**
     * Call cloud api to get EEE Codes
     *
     * @return EEECodeResponse, a domain object for EEECodes
     */
    public synchronized EEECodeResponse getEEECodes() {
        LOGGER.info("Calling the get EEE codes endpoint");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(headers);

        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_EEE_API, request, String.class);

        EEECodeResponse eeeCodeResponse = null;
        try {
            eeeCodeResponse = mapper.readValue(stringResponse, EEECodeResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse response for SimLock License Manager.", e);
        }
        return eeeCodeResponse;
    }

    /**
     * Cloud api to fetch vendor and invoice no. list
     *
     * @param stationId
     * @param masterId
     * @return VendorInvoiceInfoResponse
     */
    public synchronized VendorInvoiceInfoResponse getVendorInvoiceList(final String stationId, final String masterId) {
        LOGGER.info("Calling the get vendor and invoice no. endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(STATION_NAME, stationId);
        payload.add(MASTER_ID_2, masterId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_VENDOR_INVOICE, request,
                        String.class);

        VendorInvoiceInfoResponse vendorInvoiceInfoResponse;
        try {
            vendorInvoiceInfoResponse = mapper.readValue(stringResponse, VendorInvoiceInfoResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse response for vendor invoice.", e);
            vendorInvoiceInfoResponse = null;
        }
        return vendorInvoiceInfoResponse;
    }

    /**
     * Get tester login response for the given username and password
     *
     * @param testerName Tester login name
     * @param password   Tester login password
     * @param pcId       Station pcId/uuId
     * @return StationLoginResponse
     */
    public synchronized StationLoginInfoResponse getTesterLoginInfo(
            final String testerName, final String password, final String pcId) {
        LOGGER.info("Calling tester login api for user {}", testerName);
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(TESTER_NAME_KEY, testerName);
        payload.add(PASSWORD_KEY, password);
        payload.add(PC_ID_KEY, pcId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.TEXT_HTML));
        getRestTemplate(inMemoryStore.isEuServer()).getMessageConverters().add(converter);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(TESTER_LOGIN_PHONE_CHECK, request, StationLoginInfoResponse.class);
    }

    /**
     * Get device info by serial from the cloud api
     *
     * @param deviceSerial
     * @param licenseId
     * @return GetDeviceBySerialResponse
     */
    public synchronized SyncedCloudDevice getDeviceBySerial(
            final String deviceSerial, final Integer licenseId) {
        LOGGER.info("Calling get device by serial api for device: {} licenseId: {}", deviceSerial, licenseId);
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(SERIAL, deviceSerial);
        payload.add(LICENSE_ID1, Integer.toString(licenseId));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.TEXT_HTML));
        getRestTemplate(inMemoryStore.isEuServer()).getMessageConverters().add(converter);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);

        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_DEVICE_BY_SERIAL, request, String.class);

        SyncedCloudDevice syncedCloudDevice = null;
        if (StringUtils.isNotBlank(stringResponse) && stringResponse.contains("FALSE")) {
            syncedCloudDevice = new SyncedCloudDevice();
            syncedCloudDevice.setErrorResponse("Device Not Found");
            return syncedCloudDevice;
        }

        try {
            syncedCloudDevice = mapper.readValue(stringResponse, SyncedCloudDevice.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse response for getDeviceBySerial", e);
        }

        return syncedCloudDevice;
    }


    /**
     * Cloud api to fetch android config for the given brand model of the android device
     *
     * @param brand android device brand
     * @param model android device model
     * @return AndroidConfigResponse
     */
    public synchronized AndroidConfigResponse getAndroidConfig(final String brand, final String model) {
        LOGGER.info("Calling the get android config endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(BRAND, brand);
        payload.add(MODEL.toLowerCase(), model);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String stringResponse = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_ANDROID_CONFIG, request, String.class);

        AndroidConfigResponse androidConfigResponse = null;
        try {
            androidConfigResponse = mapper.readValue(stringResponse, AndroidConfigResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Failed to parse response for android config.", e);
        }
        return androidConfigResponse;
    }

    /**
     * Cloud API to fetch SKU Schema
     *
     * @param licenceId transaction license id
     * @return SKUSchemaResponse
     */
    public synchronized SkuSchemaResponse getSKUSchema(final String licenceId) {
        LOGGER.info("Calling the getSKUSchema endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(USER_ID1, licenceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_SKU_SCHEMA, request, String.class);
        try {
            return mapper.readValue(response, SkuSchemaResponse.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("Response for getSKUSchema API call could not be processed.", e);
        }
        return null;
    }

    /**
     * Cloud API to fetch Inventory SKU Schema
     *
     * @param licenceId transaction license id
     * @param modelName device model name
     * @param storage   device disk size
     * @param carrier   device carrier
     * @param color     device color
     * @param grade     device grade
     * @param modelNo   device model number
     * @param touchID   device touch id
     * @return String Inventory SKU Code
     */
    public synchronized String getInventorySKUSchema(
            final String licenceId, final String modelName, final String storage, final String carrier,
            final String color, final String grade, final String modelNo, final String touchID
    ) {
        LOGGER.info("Calling the getInventorySKUSchema endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(USER_ID1, licenceId);
        payload.add(MODEL_NAME, modelName);
        payload.add(STORAGE, storage);
        payload.add(CARRIER2, carrier);
        payload.add(COLOR, color);
        payload.add(GRADE, grade);
        payload.add(MODEL_NO, modelNo);
        payload.add(TOUCH_ID, touchID);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_INVENTORY_SKU_SCHEMA, request, String.class);
    }

    /**
     * Get device info by serial from the cloud api
     *
     * @param deviceSerial Device serial
     * @param licenseId    License Id
     * @return GetDeviceBySerialResponse
     */
    public synchronized UnlockStatusBySerialResponse getDeviceUnlockStatusBySerial(
            final String deviceSerial, final Integer licenseId) {
        LOGGER.info("Calling get device by serial api for device: {} licenseId: {}", deviceSerial, licenseId);
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(SERIAL, deviceSerial);
        payload.add(LICENSE_ID1, Integer.toString(licenseId));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.TEXT_HTML));
        getRestTemplate(inMemoryStore.isEuServer()).getMessageConverters().add(converter);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_DEVICE_BY_SERIAL, request, UnlockStatusBySerialResponse.class);
    }


    public String updateTransactionDetails(final int licenseId,
                                  final String serial,
                                  final String oldTransactionId,
                                  final Transaction transaction) {
        LOGGER.info("Calling update transaction");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();

        payload.add("licenseId", Integer.toString(licenseId));
        payload.add("serial", serial);
        payload.add(OLD_TRANSACTION_ID, oldTransactionId);
        payload.add(NEW_TRANSACTION_ID, String.valueOf(transaction.getTransactionId()));
        payload.add("vendorName", transaction.getVendorName());
        payload.add("InvoiceNo", transaction.getInvoiceNo());
        payload.add(BOX_NO, transaction.getBoxNo());
        payload.add(QTY, transaction.getQty());
        payload.add(STATION_ID, transaction.getStationId());
        payload.add(TRANSACTION_DATE, transaction.getTransactionDate());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request
                = new HttpEntity<>(payload, headers);

       return getRestTemplate(inMemoryStore.isEuServer()).postForObject(UPDATE_TRANSACTION, request,
                String.class);
    }
}
