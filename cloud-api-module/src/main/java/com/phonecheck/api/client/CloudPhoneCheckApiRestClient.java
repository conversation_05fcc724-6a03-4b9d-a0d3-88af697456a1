package com.phonecheck.api.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.cloudapi.DesktopLicenseResponse;
import com.phonecheck.model.phonecheckapi.ChangeLogResponse;
import com.phonecheck.model.phonecheckapi.GetLicenseBuildResponse;
import com.phonecheck.model.phonecheckapi.StationLoginInfoResponse;
import com.phonecheck.model.store.InMemoryStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;

@Component
public class CloudPhoneCheckApiRestClient extends AbstractCloudClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudPhoneCheckApiRestClient.class);
    private static final Logger LICENSE_LOGGER = LoggerFactory.getLogger("LicenseLogger");

    protected static final String STATION_LOGIN_PHONE_CHECK = "includes/api/StationLogin.php";
    protected static final String CHANGE_LOG_PHONE_CHECK = "includes/api/buildChangeLog.php";
    protected static final String GET_LICENSE_BUILDS = "includes/api/get_license_build.php";
    protected static final String USERNAME_KEY = "username";
    protected static final String PASSWORD_KEY = "password";
    protected static final String PC_ID_KEY = "pc_id";
    protected static final String PLATFORM = "platform";
    protected static final String VERSION = "version";
    protected static final String GET_LICENSE_DETAIL_API = "includes/api/GetLicenseDetailsForUser.php";
    protected static final String AUTHORIZATION = "Authorization";
    private final ObjectMapper mapper;
    private final InMemoryStore inMemoryStore;

    public CloudPhoneCheckApiRestClient(
            @Qualifier("cloudPhoneCheckRestTemplate") final RestTemplate restTemplate,
            @Qualifier("euCloudPhoneCheckRestTemplate") final RestTemplate euRestTemplate,
            final ObjectMapper mapper,
            final InMemoryStore inMemoryStore) {
        super(restTemplate, euRestTemplate);
        this.mapper = mapper;
        this.inMemoryStore = inMemoryStore;
    }

    /**
     * Get station login response for the given username and password
     *
     * @param username Station login username
     * @param password Station login password
     * @param pcId     Station pcId/uuId
     * @return StationLoginResponse
     */
    public synchronized StationLoginInfoResponse getStationLoginInfo(
            final String username, final String password, final String pcId) {
        LOGGER.info("Calling station login api for user {}", username);
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(USERNAME_KEY, username);
        payload.add(PASSWORD_KEY, password);
        payload.add(PC_ID_KEY, pcId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(STATION_LOGIN_PHONE_CHECK, request, StationLoginInfoResponse.class);
    }

    /**
     * Get change logs for the given platform and build version
     *
     * @param platform     OS platform of the machine
     * @param buildVersion Current app build version
     * @return ChangeLogResponse
     */
    public synchronized ChangeLogResponse getChangeLog(final String platform, final String buildVersion) {
        LOGGER.info("Calling change log endpoint");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(PLATFORM, platform);
        payload.add(VERSION, buildVersion);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(CHANGE_LOG_PHONE_CHECK, request, ChangeLogResponse.class);
    }

    /**
     * Get the License build info for the user
     *
     * @param userName Station login username
     * @return GetLicenseBuildResponse
     */
    public synchronized GetLicenseBuildResponse getLicenseBuild(final String userName) {
        LOGGER.info("Calling get license build endpoint for assigned builds");
        MultiValueMap<String, String> payload = new LinkedMultiValueMap<>();
        payload.add(USERNAME_KEY, userName);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(payload, headers);
        return getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LICENSE_BUILDS, request, GetLicenseBuildResponse.class);
    }

    /**
     * Get the desktop license details for given user from the cloud API
     *
     * @return DesktopLicenseResponse
     */
    public synchronized DesktopLicenseResponse getLicenseDetailsForUser() throws JsonProcessingException {
        LOGGER.info("Calling get license details for user {}", inMemoryStore.getUserName());
        LICENSE_LOGGER.info("Calling get license details for user {}", inMemoryStore.getUserName());
        String auth = Base64.getEncoder().encodeToString((inMemoryStore.getUserName()).getBytes());
        HttpHeaders headers = new HttpHeaders();
        headers.set(AUTHORIZATION, auth);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(headers);

        final String response = getRestTemplate(inMemoryStore.isEuServer())
                .postForObject(GET_LICENSE_DETAIL_API, request, String.class);
        LOGGER.info("Get license details api response: {}", response);
        LICENSE_LOGGER.info("Get license details api response: {}", response);
        return mapper.readValue(response, DesktopLicenseResponse.class);
    }
}
