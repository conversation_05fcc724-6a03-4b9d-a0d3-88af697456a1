package com.phonecheck.api.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.cloudapi.*;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.ios.LicenseType;
import com.phonecheck.model.phonecheckapi.StationLoginInfoResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.TestKeys;
import com.phonecheck.model.transaction.Transaction;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static com.phonecheck.api.client.CloudApiRestClient.*;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CloudApiRestClientTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private RestTemplate cloudRestTemplate;
    @Mock
    private RestTemplate euCloudRestTemplate;
    @Mock
    private InMemoryStore inMemoryStore;

    private CloudApiRestClient cloudApiRestClient;

    @BeforeEach
    public void setup() {
        cloudApiRestClient = new CloudApiRestClient(cloudRestTemplate, euCloudRestTemplate, inMemoryStore, mapper);
    }

    @Test
    public void testGetCarrierAndSimLockStatusFromEuServer() throws JsonProcessingException {
        when(inMemoryStore.isEuServer()).thenReturn(true);
        CarrierSimLockStatusRequest request =
                new CarrierSimLockStatusRequest("devId", "user", "key", "type", "carrier");

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "{\"API\":\"3DC\",\"Remarks\":\"N\\/A\",\"FieldColor\":\"Yellow\","
                + "\"deviceid\":\"221E230FA8326\",\"Carrier\":\"\",\"RawResponse\":"
                + "\"IMEI or SN is Invalid\",\"fallbackcall\":\"Yes\",\"chargeStatus\":\"No\"}";

        CarrierSimLockStatusResponse testResponse = mapper.readValue(responseStr,
                CarrierSimLockStatusResponse.class);

        when(euCloudRestTemplate.postForObject(eq(CARRIER_AND_SIM_LOCK_CHECK_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);

        CarrierSimLockStatusResponse response = cloudApiRestClient.getCarrierAndSimLockStatus(request);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(DEVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));
        Assertions.assertTrue(mvMap.containsKey(API_KEY));
        Assertions.assertTrue(mvMap.containsKey(CARRIER));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TYPE));

        // Assert the response fields match
        Assertions.assertEquals(testResponse, response);
    }

    @Test
    public void testGetCarrierAndSimLockStatus() throws JsonProcessingException {
        CarrierSimLockStatusRequest request =
                new CarrierSimLockStatusRequest("devId", "user", "key", "type", "carrier");

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "{\"API\":\"3DC\",\"Remarks\":\"N\\/A\",\"FieldColor\":\"Yellow\","
                + "\"deviceid\":\"221E230FA8326\",\"Carrier\":\"\",\"RawResponse\":"
                + "\"IMEI or SN is Invalid\",\"fallbackcall\":\"Yes\",\"chargeStatus\":\"No\"}";

        CarrierSimLockStatusResponse testResponse = mapper.readValue(responseStr,
                CarrierSimLockStatusResponse.class);

        when(cloudRestTemplate.postForObject(eq(CARRIER_AND_SIM_LOCK_CHECK_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);

        CarrierSimLockStatusResponse response = cloudApiRestClient.getCarrierAndSimLockStatus(request);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(DEVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));
        Assertions.assertTrue(mvMap.containsKey(API_KEY));
        Assertions.assertTrue(mvMap.containsKey(CARRIER));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TYPE));

        // Assert the response fields match
        Assertions.assertEquals(testResponse, response);
    }

    @Test
    public void testGetLatestIosModel() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String version = "1212";
        IosModelResponse response = new IosModelResponse();
        response.setVersion(version);
        response.setIosModels(List.of(new IosModelResponse.IosModel()));

        when(cloudRestTemplate.postForObject(eq(GET_LATEST_IOS_MODEL_API),
                captor.capture(), eq(IosModelResponse.class))).thenReturn(response);

        IosModelResponse result = cloudApiRestClient.getLatestIosModels(version);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertEquals(1, mvMap.keySet().size());
        Assertions.assertTrue(mvMap.containsKey(VERSION));

        // Assert the response fields match
        Assertions.assertEquals(response, result);
    }

    @Test
    public void testGetLatestNetworkInfo() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        LatestNetworkInfoResponse response = new LatestNetworkInfoResponse();
        response.setVersion("Version");
        response.setNetworkInfo(List.of(new LatestNetworkInfoResponse.NetworkInfo()));

        when(cloudRestTemplate.postForObject(eq(GET_LATEST_NETWORK_INFO_API),
                captor.capture(), eq(LatestNetworkInfoResponse.class))).thenReturn(response);

        LatestNetworkInfoResponse result = cloudApiRestClient.getLatestNetworkInfo("Version");

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(VERSION));

        // Assert the response fields match
        Assertions.assertEquals(result, response);
    }

    @Test
    public void testGetIosColorInfo() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        IosColorInfoResponse response = new IosColorInfoResponse();
        response.setVersion("Version");
        response.setIosColorInfos(List.of(new IosColorInfoResponse.IosColorInfo()));

        when(cloudRestTemplate.postForObject(eq(GET_LATEST_IOS_COLOR_INFO_API),
                captor.capture(), eq(IosColorInfoResponse.class))).thenReturn(response);

        IosColorInfoResponse result = cloudApiRestClient.getLatestIosColorInfo("Version");

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(VERSION));

        // Assert the response fields match
        Assertions.assertEquals(result, response);
    }

    @Test
    public void testGetSimTechnologyInfo() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "{\"Model\":\"model\",\"RegulatoryModel\":\"rm\"," +
                "\"Type\":\"GSM\",\"CountryOfOrigin\":\"\"}";

        SimTechnologyResponse responseMapping = mapper.readValue(responseStr,
                SimTechnologyResponse.class);

        when(cloudRestTemplate.postForObject(eq(GET_SIM_TECHNOLOGY_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);
        SimTechnologyResponse result = cloudApiRestClient.getSimTechnologyByModelAndRegulatoryNo("model", "rm");

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(MODEL));
        Assertions.assertTrue(mvMap.containsKey(REGULATORY_MODEL));

        // Assert the response fields match
        Assertions.assertEquals(result.getType(), responseMapping.getType());
        Assertions.assertEquals(result.getCountryOfOrigin(), responseMapping.getCountryOfOrigin());
    }

    @Test
    public void testPendingColorInfo() {
        String userId = "11845";
        AndroidDevice androidDevice = new AndroidDevice();
        androidDevice.setModelNo("SM-S9280");
        androidDevice.setModel("Galaxy S24 Ultra Dual");
        androidDevice.setSerial("R5CX20DJQ9D");
        androidDevice.setColorCode("K");

        PendingColorInfoRequest pendingColorInfoRequest = PendingColorInfoRequest.builder()
                .modelNo(StringUtils.isNotBlank(androidDevice.getModelNo()) ?
                        androidDevice.getModelNo() : StringUtils.EMPTY)
                .model(StringUtils.isNotBlank(androidDevice.getModel()) ?
                        androidDevice.getModel() : StringUtils.EMPTY)
                .imei(StringUtils.isNotBlank(androidDevice.getImei()) ?
                        androidDevice.getImei() : StringUtils.EMPTY)
                .serial(StringUtils.isNotBlank(androidDevice.getSerial()) ?
                        androidDevice.getSerial() : StringUtils.EMPTY)
                .colorCode(StringUtils.isNotBlank(androidDevice.getColorCode()) ?
                        androidDevice.getColorCode() : StringUtils.EMPTY)
                .userId(userId)
                .build();

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        when(cloudRestTemplate.postForObject(eq(UPDATE_PENDING_COLOR_INFO_API),
                captor.capture(), eq(String.class))).thenReturn("Record updated successfully.");

        String response = cloudApiRestClient.syncPendingColorInfoToCloud(pendingColorInfoRequest);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);

        // Assert the response fields match
        Assertions.assertEquals("Record updated successfully.", response);
    }

    @Test
    public void testGetAndroidColorInfo() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        AndroidColorInfoResponse response = new AndroidColorInfoResponse();
        response.setVersion("Version");
        response.setColorInfoList(List.of(new AndroidColorInfoResponse.AndroidColorInfo()));

        when(cloudRestTemplate.postForObject(eq(GET_LATEST_ANDROID_COLOR_INFO_API),
                captor.capture(), eq(AndroidColorInfoResponse.class))).thenReturn(response);

        AndroidColorInfoResponse result = cloudApiRestClient.getLatestAndroidColorInfo("Version");

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(VERSION));

        // Assert the response fields match
        Assertions.assertEquals(result, response);
    }

    @Test
    public void testGetCustomizationPackages() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        CustomizationPackageDataResponse response = new CustomizationPackageDataResponse();
        response.setCustomizationPackages(List.of(new CustomizationPackageDataResponse.CustomizationPackage()));

        when(cloudRestTemplate.postForObject(eq(GET_CUSTOMIZATION_PAKAGE_DATA_API),
                captor.capture(), eq(CustomizationPackageDataResponse.class))).thenReturn(response);

        CustomizationPackageDataResponse result = cloudApiRestClient.getCustomizationPackages("masterId", "");

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(MASTER_ID));
        Assertions.assertTrue(mvMap.containsKey(WAREHOUSE_ID));

        // Assert the response fields match
        Assertions.assertEquals(result, response);
    }

    @Test
    public void testGetLogos() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String masterId = "136";
        LogoResponse response = new LogoResponse();
        response.setSmall(new LogoResponse.LogoData());
        response.setLarge(new LogoResponse.LogoData());
        response.setLabel(new LogoResponse.LogoData());

        when(cloudRestTemplate.postForObject(eq(GET_LOGO_API),
                captor.capture(), eq(LogoResponse.class))).thenReturn(response);

        LogoResponse result = cloudApiRestClient.getLogos(masterId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertEquals(1, mvMap.keySet().size());
        Assertions.assertTrue(mvMap.containsKey(MASTER_ID));

        // Assert the response fields match
        Assertions.assertEquals(result, response);
    }

    @Test
    public void testGetLatestCarrierInfo() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String version = "1234";
        CarrierInfoResponse response = new CarrierInfoResponse();
        response.setVersion(version);
        response.setCarrierInfoList(List.of(new CarrierInfoResponse.CarrierInfo()));

        when(cloudRestTemplate.postForObject(eq(GET_LATEST_CARRIER_INFO_API),
                captor.capture(), eq(CarrierInfoResponse.class))).thenReturn(response);

        CarrierInfoResponse result = cloudApiRestClient.getLatestCarrierInfo(version);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertEquals(1, mvMap.keySet().size());
        Assertions.assertTrue(mvMap.containsKey(VERSION));

        // Assert the response fields match
        Assertions.assertEquals(result, response);
    }

    @Test
    public void testGetEsnLicenseInfoSingle() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "{\"API\":\"G001\",\"Remarks\":\"Good\",\"FieldColor\":\"Green\",\"deviceid\":" +
                "\"355330086461755\",\"RawResponse\":{\"refcode\":\"28102022023903\",\"devicetype\":\"Smartphone\"" +
                ",\"marketingname\":\"Apple " +
                "iPhone 7 (A1778)\",\"brandname\":\"Apple\",\"manufacturer\":\"Apple " +
                "Inc\",\"responsestatus\":\"success\",\"blackliststatus\":\"No\",\"imeihistory\":[{\"action\":\"NA\"," +
                "\"date\":\"NA\",\"by\":\"NA\",\"Country\":\"NA\"}],\"greyliststatus\":\"No\"," +
                "\"operatingsys\":\"iOS\",\"modelname\":\"iPhone " +
                "7 (A1778)\"},\"fallbackcall\":\"No\",\"chargeStatus\":\"No\"}";

        EsnResponse.EsnApiResponse testResponse = mapper.readValue(responseStr,
                EsnResponse.EsnApiResponse.class);

        when(cloudRestTemplate.postForObject(eq(ESN_CHECK_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);

        EsnLicenseCheckRequest request = EsnLicenseCheckRequest.builder()
                .apiKey("b75-4e08-428c-a557-e5e1e11a83c5")
                .carrier("")
                .checkAll(0)
                .imei("**********")
                .deviceType("IOS")
                .meid("35533008675")
                .isMeid(false)
                .serviceId("-1")
                .testerId("")
                .warehouseId("")
                .userId("a").build();

        EsnResponse response = cloudApiRestClient.getEsnLicenseInfo(request);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(API_KEY));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TYPE));
        Assertions.assertTrue(mvMap.containsKey(CARRIER));
        Assertions.assertTrue(mvMap.containsKey(SERVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(CHECK_ALL_ESN));
        Assertions.assertTrue(mvMap.containsKey(MEID));
        Assertions.assertTrue(mvMap.containsKey(IS_MEID));
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));
        Assertions.assertTrue(mvMap.containsKey(TESTER_ID));
        Assertions.assertTrue(mvMap.containsKey(WAREHOUSE_ID_ESN));

        // Assert the response fields match
        Assertions.assertEquals(testResponse, response.getEsnApiResults()[0]);
    }

    @Test
    public void testGetEsnLicenseInfoAll() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "[{\"API\":\"S001\",\"Remarks\":\"Not" +
                "Compatible\",\"FieldColor\":\"Black\",\"deviceid\":\"355330086461755\",\"Carrier\"" +
                ":\"Verizon\",\"RawResponse\":\"IMEI: 355330086461755" +
                "ESN Status: The phone associated with the Device ID you entered is not compatible with " +
                "the Verizon Wireless network.Error Code: Device Not Found Carrier:" +
                "Verizon\",\"fallbackcall\":\"No\",\"chargeStatus\":\"No\"},{\"API\":\"S001\",\"Remarks\"" +
                ":\"Good\",\"FieldColor\":\"Green\",\"deviceid\":\"355330086461755\",\"Carrier\":\"T-Mobile\"" +
                ",\"RawResponse\":\"IMEI: 355330086461755Model Name: iPhone 7Model Brand: AppleeSIM Supported: " +
                "No ESN Status: Clean Carrier:" +
                "T-Mobile\",\"fallbackcall\":\"No\",\"chargeStatus\":\"No\"},{\"API\":\"G001\",\"Remarks\":\"Good\"" +
                ",\"FieldColor\":\"Green\",\"deviceid\":\"355330086461755\",\"RawResponse\":{\"refcode\":\"" +
                "28102022023903\",\"devicetype\":\"Smartphone\",\"marketingname\":\"Apple" +
                "iPhone 7 (A1778)\",\"brandname\":\"Apple\",\"manufacturer\":\"Apple" +
                "Inc\",\"responsestatus\":\"success\",\"blackliststatus\":\"No\",\"imeihistory\":[{\"action\":\"NA\"," +
                "\"date\":\"NA\",\"by\":\"NA\",\"Country\":\"NA\"}]" +
                ",\"greyliststatus\":\"No\",\"operatingsys\":\"iOS\"" +
                ",\"modelname\":\"iPhone 7 (A1778)\"},\"fallbackcall\":\"No\",\"chargeStatus\":\"No\"},{\"Remarks\"" +
                ":\"Good\",\"FieldColor\":\"Green\"}]";

        EsnResponse.EsnApiResponse[] testResponse = mapper.readValue(responseStr,
                EsnResponse.EsnApiResponse[].class);

        when(cloudRestTemplate.postForObject(eq(ESN_CHECK_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);

        EsnLicenseCheckRequest request = EsnLicenseCheckRequest.builder()
                .apiKey("b75-4e08-428c-a557-e5e1e11a83c5")
                .carrier("")
                .checkAll(1)
                .imei("**********")
                .deviceType("IOS")
                .meid("35533008675")
                .isMeid(false)
                .serviceId("-1")
                .testerId("")
                .warehouseId("")
                .userId("a").build();

        EsnResponse response = cloudApiRestClient.getEsnLicenseInfo(request);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(API_KEY));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TYPE));
        Assertions.assertTrue(mvMap.containsKey(CARRIER));
        Assertions.assertTrue(mvMap.containsKey(SERVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(CHECK_ALL_ESN));
        Assertions.assertTrue(mvMap.containsKey(MEID));
        Assertions.assertTrue(mvMap.containsKey(IS_MEID));
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));
        Assertions.assertTrue(mvMap.containsKey(TESTER_ID));
        Assertions.assertTrue(mvMap.containsKey(WAREHOUSE_ID_ESN));

        // Assert the response fields match
        Assertions.assertEquals(testResponse[0], response.getEsnApiResults()[0]);
        Assertions.assertEquals(testResponse[1], response.getEsnApiResults()[1]);
        Assertions.assertEquals(testResponse[2], response.getEsnApiResults()[2]);
    }

    @Test
    public void testGetEsnLicenseExpired() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "{\"FieldColor\":\"Orange\",\"message\":" +
                "\"User has exceeded esn limit\",\"isLicenseExpired\":true}";

        EsnResponse.EsnLicensesExpiredResponse testResponse = mapper.readValue(responseStr,
                EsnResponse.EsnLicensesExpiredResponse.class);

        when(cloudRestTemplate.postForObject(eq(ESN_CHECK_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);

        EsnLicenseCheckRequest request = EsnLicenseCheckRequest.builder()
                .apiKey("b75-4e08-428c-a557-e5e1e11a83c5")
                .carrier("")
                .checkAll(0)
                .imei("**********")
                .deviceType("IOS")
                .meid("35533008675")
                .isMeid(false)
                .serviceId("-1")
                .testerId("")
                .warehouseId("")
                .userId("a").build();

        EsnResponse response = cloudApiRestClient.getEsnLicenseInfo(request);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(API_KEY));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TYPE));
        Assertions.assertTrue(mvMap.containsKey(CARRIER));
        Assertions.assertTrue(mvMap.containsKey(SERVICE_ID));
        Assertions.assertTrue(mvMap.containsKey(CHECK_ALL_ESN));
        Assertions.assertTrue(mvMap.containsKey(MEID));
        Assertions.assertTrue(mvMap.containsKey(IS_MEID));
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));
        Assertions.assertTrue(mvMap.containsKey(TESTER_ID));
        Assertions.assertTrue(mvMap.containsKey(WAREHOUSE_ID_ESN));

        // Assert the response fields match
        Assertions.assertNotNull(response.getLicensesExpiredResponse());
        Assertions.assertEquals(testResponse, response.getLicensesExpiredResponse());
    }

    @Test
    public void testChargeLicense() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        final String apiReturn = "96";
        final String deviceIdentity = "6316763021716518";
        final String imei = "353316070918126";
        final String deviceType = "IPHONE";
        final String deviceTitle = "";

        final String username = "username";
        final String buildNo = "3221.1";
        final String testerId = "";
        final String warehouseId = "";
        final LicenseType licenseType = LicenseType.APP_INSTALL;

        when(cloudRestTemplate.postForObject(eq(CHARGE_LICENSE_API),
                captor.capture(), eq(String.class))).thenReturn(apiReturn);

        String result = cloudApiRestClient.chargeLicense(
                deviceIdentity, imei, licenseType.name(), deviceType, deviceTitle,
                username, buildNo, testerId, warehouseId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertEquals(9, mvMap.keySet().size());
        Assertions.assertTrue(mvMap.containsKey(DEVICE_IDENTITY));
        Assertions.assertTrue(mvMap.containsKey(IMEI));
        Assertions.assertTrue(mvMap.containsKey(LICENSE_TYPE));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TYPE));
        Assertions.assertTrue(mvMap.containsKey(DEVICE_TITLE));
        Assertions.assertTrue(mvMap.containsKey(USER_IDENTITY));
        Assertions.assertTrue(mvMap.containsKey(BUILD_NO));
        Assertions.assertTrue(mvMap.containsKey(TESTER_ID));
        Assertions.assertTrue(mvMap.containsKey(WAREHOUSE_ID_ESN));

        // Assert the response fields match
        Assertions.assertEquals(apiReturn, result);
    }

    @Test
    public void getVppToken() throws JsonProcessingException {
        VppTokenResponse testResponse = mapper.readValue(getVppTokenResponse(),
                VppTokenResponse.class);
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        when(cloudRestTemplate.postForObject(eq(GET_VPP_TOKEN_API),
                captor.capture(), eq(String.class))).thenReturn(getVppTokenResponse());
        VppTokenResponse result = cloudApiRestClient.getVppToken();
        Assertions.assertEquals(testResponse, result);
    }

    public String getVppTokenResponse() {
        return "{\n" +
                "    \"status\": true,\n" +
                "    \"msg\": \"verified\",\n" +
                "    \"data\": {\n" +
                "        \"token\": \"eyJleHBEYXRlIjoiMjAyMy0xMC0xMlQwOToyNT" +
                "o0NyswMDAwIiwidG9rZW4iOiJ5UnRuSHZzTXJ6c" +
                "FNEZkJXY0ZORzFjWklyVElaci9nNGZuWnpZL0FHZmFx" +
                "amtmc280d0hVYXpyM3FGSU80WWFBdDgrYTR0K3JKRHc4U" +
                "2FYcUE3cFFISm1DTFFuZDl2UTJzbjdBNXhRNVBNODBkaHhaQlA5akh" +
                "lZ3UrcmZybUxSZ0J2MytyTGdrWXhHcmZ6cisya2xDald1a2xTcDhQRDU4Vy" +
                "9iUWJldVJSdnNFczMweWtuZ0o1a2VIdFFQZUNXMCsiLCJvcmdOYW1lIjoiQ2VydG" +
                "lmaWVkIFNvbHV0aW9ucyBHcm91cCBMLkwuQy4ifQ==\"\n" +
                "    }\n" +
                "}";
    }

    @Test
    public void testDeviceRestoreFilter() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final String apiReturn = "Sorry No Record Found";
        final String udid = "6316763021716518";
        final String imei = "353316070918126";
        final String serial = "1346547453";

        when(cloudRestTemplate.postForObject(eq(DEVICE_RESTORE_FILTER_API),
                captor.capture(), eq(String.class))).thenReturn(apiReturn);

        String result = cloudApiRestClient.getDeviceRestoreFilter(udid, imei, serial);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertEquals(3, mvMap.keySet().size());
        Assertions.assertTrue(mvMap.containsKey(UDID));
        Assertions.assertTrue(mvMap.containsKey(IMEI));
        Assertions.assertTrue(mvMap.containsKey(SERIAL));

        // Assert the response fields match
        Assertions.assertEquals(apiReturn, result);
    }

    @Test
    public void testGetBatteryProfileUrl() {
        String batteryProfileUrl = "http://battery.profile.url";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(batteryProfileUrl, HttpStatus.OK);

        when(cloudRestTemplate.getForEntity(eq(GET_BATTERY_PROFILE_URL_API), eq(String.class)))
                .thenReturn(responseEntity);

        String result = cloudApiRestClient.getBatteryProfileUrl();

        // Assert the response fields match
        Assertions.assertEquals(batteryProfileUrl, result);
    }


    @DisplayName("Test for grade configuration API")
    @Test
    public void testGetGradeConfigs() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final int licenseId = 12149;
        final String gradeConfigs = """
                {
                  "Grades": ["A", "B", "C", "D", "E", "F", "G ", "H"],
                  "Additional": ["AA", "BB", "CC", "DD", "EE", "FF", "GG", "HH"]
                }
                """;
        GradeConfigResponse expectedOutput = mapper.readValue(gradeConfigs, GradeConfigResponse.class);

        when(cloudRestTemplate.postForObject(eq(GET_GRADE_CONFIGS_API),
                captor.capture(), eq(String.class))).thenReturn(gradeConfigs);

        GradeConfigResponse result = cloudApiRestClient.getGradeConfigs(licenseId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput, result);
    }

    @Test
    @DisplayName("Test for color configuration API")
    public void testGetColorConfigs() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final int licenseId = 12149;
        final String colorConfigs = """
                {
                    "Colors": ["Gold", "Silver", "Blue", "Red", "Green", "Pink"],
                    "Additional": [
                      "Amber Sunrise",
                      "Coral ",
                      "Aurora",
                      "Breathing Crystal",
                      "Flamingo Pink"
                    ]
                }
                """;
        ColorConfigResponse expectedOutput = mapper.readValue(colorConfigs, ColorConfigResponse.class);

        when(cloudRestTemplate.postForObject(eq(GET_COLOR_CONFIGS_API),
                captor.capture(), eq(String.class))).thenReturn(colorConfigs);

        ColorConfigResponse result = cloudApiRestClient.getColorConfigs(licenseId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(USER_ID1));

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput, result);
    }

    @Test
    public void testGetClientCustomization() {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final int licenseId = 12149;
        final String clientCustomizationResponse = "{}";

        when(cloudRestTemplate.postForObject(eq(GET_CLIENT_CUSTOMIZATION_API),
                captor.capture(), eq(String.class))).thenReturn(clientCustomizationResponse);

        String result = cloudApiRestClient.getClientCustomization(licenseId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(LICENSE_ID));

        // Assert the response fields match
        Assertions.assertEquals(clientCustomizationResponse, result);
    }

    @Test
    public void testGetCustomTestPlans() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final String masterId = "1361";
        final String customTestPlansResponse = """
                [
                    {
                        "ctp_id": "262",
                        "ctp_title": "Testing",
                        "ctp_platform": "IOS",
                        "ctp_model_no": "",
                        "ctp_description": "Testingggggggggg",
                        "ctp_added_on": "2023-01-18 10:05:33",
                        "ctp_status": "1"
                    }
                ]""";
        CustomTestPlansResponse[] expectedOutput = mapper.readValue(customTestPlansResponse,
                CustomTestPlansResponse[].class);

        when(cloudRestTemplate.postForObject(eq(GET_CUSTOM_TEST_PLAN_API),
                captor.capture(), eq(String.class))).thenReturn(customTestPlansResponse);

        CustomTestPlansResponse[] result = cloudApiRestClient.getCustomTestPlans(masterId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(MASTER_ID));

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput[0], result[0]);
    }

    @Test
    public void testGetSoftwareVersions() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final String masterId = "1361";
        final String softwareVersionsJson = """
                [
                    {
                        "swv_id": "382",
                        "swv_model_no": "MX9R2B/A",
                        "swv_version_list": "17",
                        "swv_master_id": "1361",
                        "swv_status": "1",
                        "swv_added_on": "2023-01-03 08:27:27"
                    },
                    {
                        "swv_id": "383",
                        "swv_model_no": "MKR22LL/A",
                        "swv_version_list": "14.7.1",
                        "swv_master_id": "1361",
                        "swv_status": "1",
                        "swv_added_on": "2021-09-10 11:04:56"
                    }
                ]""";
        SoftwareVersionsResponse[] expectedOutput = mapper.readValue(softwareVersionsJson,
                SoftwareVersionsResponse[].class);

        when(cloudRestTemplate.postForObject(eq(GET_SOFTWARE_VERSION_API),
                captor.capture(), eq(String.class))).thenReturn(softwareVersionsJson);

        SoftwareVersionsResponse[] result = cloudApiRestClient.getSoftwareVersions(masterId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(MASTER_ID));

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput[0], result[0]);
        Assertions.assertEquals(expectedOutput[1], result[1]);
    }

    @Test
    public void testGetMicThreshold() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final int licenseId = 12149;
        final String micThresholdJson = """
                {
                            "status": "success",
                            "details": {
                                "inherited": "yes",
                                "plans": {
                                    "android": [
                                {
                                    "testName": "Kamran Android",
                                        "deviceType": "android",
                                        "deviceModel": "ZX70,G51S",
                                        "thresholdValueFM": "1000000",
                                        "thresholdValueVM": "1000000",
                                        "thresholdValueBM": "1000000",
                                        "thresholdValueBES": "1000000",
                                        "thresholdValueRES": "1000000"
                                }
                                    ],
                                    "ios": [
                                        {
                                            "testName": "iPhone Default",
                                            "deviceType": "ios",
                                            "deviceModel": "iPod Touch 7th Gen,iPhone SE (2nd Gen)",
                                            "thresholdValueFM": "0.29",
                                            "thresholdValueVM": "0.29",
                                            "thresholdValueBM": "0.29"
                                        },
                                        {
                                            "testName": "Custom iPad Default",
                                            "deviceType": "ios",
                                            "deviceModel": "iPad Air 4 (Wifi),iPad Air 4 (Cellular)",
                                            "thresholdValueFM": "0.12",
                                            "thresholdValueVM": "0.12",
                                            "thresholdValueBM": "0.12"
                                        },
                                        {
                                            "testName": "Brightstar iPhone 12",
                                            "deviceType": "ios",
                                            "deviceModel": "iPhone 12 mini,iPhone 12,iPhone 12 Pro,iPhone 12 Pro Max",
                                            "thresholdValueFM": "0.24",
                                            "thresholdValueVM": "0.15",
                                            "thresholdValueBM": "0.24"
                                        }
                                    ]
                                }
                            }
                        }""";
        MicThresholdResponse expectedOutput = mapper.readValue(micThresholdJson, MicThresholdResponse.class);

        when(cloudRestTemplate.postForObject(eq(GET_MIC_THRESHOLD_API),
                captor.capture(), eq(String.class))).thenReturn(micThresholdJson);

        MicThresholdResponse result = cloudApiRestClient.getMicThreshold(licenseId);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(USER_ID2));

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput, result);
    }

    @Test
    public void testGetDeviceFeatures() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final String deviceFeatures = """
                {
                     "ProductType": "iPad16,4",
                     "ModelName": "iPad Pro 11\\" (M4)",
                     "BDID": "0A",
                     "CPID": "8132",
                     "HeadSet": false,
                     "HeadsetLightPort": false,
                     "TelePhoto": false,
                     "UltraWide": false,
                     "DigitizerNotch": false,
                     "FlipSwitch": false,
                     "SkipHomeButton": true,
                     "WirelessCharging": false,
                     "EarPiece": false,
                     "LiDAR": "",
                     "model_id": "237"
                 }
                """;
        DeviceFeaturesResponse expectedOutput = mapper.readValue(deviceFeatures, DeviceFeaturesResponse.class);

        when(cloudRestTemplate.postForObject(eq(DEVICE_FEATURES_API),
                captor.capture(), eq(String.class))).thenReturn(deviceFeatures);

        DeviceFeaturesResponse result = cloudApiRestClient.getDeviceFeatures("productType");

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(PRODUCT_TYPE));
        Assertions.assertFalse(result.isLidar());

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput, result);
    }

    @Test
    public void syncDeviceDataToCloudTest() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<String>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        String input = "{\"grade_profile_id\":0,\"FMic\":\"\",\"RegulatoryModelNumber\":" +
                "\"A1687\",\"Platform\":\"PhoneCheckmacOS\",\"Memory\":\"64 GB\",\"EraseType\"" +
                ":\"\",\"SIM2MNC\":\"\",\"TransactionID\":576,\"PCCarrier\":\"Verizon\",\"Network\":\"\"," +
                "\"BatteryDrainDuration\":\"\",\"BatteryChargeStart\":\"\",\"" +
                "BatteryDrainInfo\":\"\",\"Firmware\":\"7.70.01\",\"Rooted\":\"Off\",\"UnlockStatus\":\"\"," +
                "\"WareHouse\":\"good\",\"ESNResponse\":\"[{\\\"Remarks\\\":\\\"Good\\\",\\\"API\\\":\\\"S001\\\"," +
                "\\\"Carrier\\\":\\\"Verizon\\\",\\\"fallbackcall\\\":\\\"No\\\",\\\"" +
                "chargeStatus\\\":\\\"Yes\\\",\\\"" + "deviceid\\\":\\\"353283071864768\\\"," +
                "\\\"FieldColor\\\":\\\"Green\\\",\\\"RawResponse\\\":\\\"Model: " +
                "iPhone 6S Plus 64GB Rose GoldIMEI: 353283071864768 ESN Status: CleanYour " +
                "device is eligible.Part Number: " +
                "CLNRAPL6SP64RGProduct ID: DEV5160003Device SKU: SKU2160026 Carrier: " +
                "Verizon\\\"}]\",\"EID\":\"\",\"ebay_grade_id\":" + "-1,\"VendorName\":\"Vendor " +
                "Name\",\"AppleID\":\"\",\"ProductCode\":\"\",\"WifiMacAddress\":\"7c:01:91:" +
                "12:7f:c7\",\"Make\":\"Apple\",\"ManualFailure\":\"No\",\"SIMSERIAL2\":\"\",\"" +
                "device_shutdown\":\"\",\"Working" + "\":\"Pending\",\"PESN\":\"80BCFCCA\",\"VMic" +
                "\":\"\",\"BatteryModel\":\"0003-F\",\"iCloudInfo\":\"\\n\",\"BatterySerial" +
                "\":\"F8Y54210SUGG8H8A6\",\"IFT_Codes\":\"RTIAPA116-0037\",\"IMEI2\":\"\"," +
                "\"endHeat\":\"\",\"DecimalMEID\":\"" + "089250279101598582\",\"LPN\":\"\"," +
                "\"DefectsCode\":\"\",\"CarrierLockResponse\":\"\",\"Pre_check_working\":\"\",\"" +
                "Pre_check_Failed\":\"\",\"SIMSERIAL\":\"8901260752785701444\",\"SimTechnology\"" +
                ":\"GSM/CDMA\",\"BuildNo\":\"3221.1\"," + "\"SIM1MCC\":\"\",\"ManualEntry\":\"No\"," +
                "\"OEMBatteryHealth\":\"0.0\",\"TesterName\":\"\",\"TransactionDate\":\"2022-12" +
                "-23 11:20:48\",\"ESN\":\"Good\",\"startHeat\":\"\",\"esim_present\":\"No\",\"" +
                "BatterySource\":\"BS01\",\"InvoiceNo\":\"" + "Invoice#\",\"esim_erased\":\"No\"," +
                "\"EraseEndTime\":\"\",\"Serial\":\"C39QGVZHGRX5\",\"DeviceState\":\"Home\",\"SimLock\":" +
                "\"\",\"SIM2Name\":\"\",\"RestoreCode\":\"\",\"Model\":\"iPhone 6S+\",\"Passed\":\"\"," +
                "\"MEID2\":\"\",\"BatteryDrain\":\"\",\"" + "BatteryCurrentMaxCapacity\":\"2334\",\"" +
                "MDMResponse\":\"{\\\"dict\\\":\\\"CloudConfiguration  AllowPairing : " + "true " +
                "CloudConfigurationUIComplete : true ConfigurationWasApplied : true IsMDMUnremovable " +
                ": false IsMandatory : " + "false IsSupervised : false \\\",\\\"dict1\\\":\\\"" +
                "OrganizationName : PhoneCheck Status : Acknowledged " + "\\\",\\\"dict2\\\":\\\"" +
                "\\\"}\",\"BatteryCycle\":\"575\",\"CountryOfOrigin\":\"US\",\"ScreenTime\":\"\",\"Model#\":" +
                "\"MKVE2LL/A\",\"Knox\":\"\",\"Pre_check_Pending\":\"\",\"Version\":\"13.6.1\",\"MEID\":" +
                "\"35328307186476\",\"QTY\":" + "\"QTY\",\"MDM\":\"No\",\"CocoCurrentCapacity\":\"2334\"," +
                "\"Carrier\":\"Verizon\",\"TestPlanName\":\"Bamboo  -" + " w/ Headset & Lightning Port\"," +
                "\"DeviceLock\":\"OFF\",\"BatteryTemperature\":\"24\",\"SIM1MNC\":\"\",\"PESN2\":" +
                "\"\",\"BatteryPercentage\":\"9\",\"Failed\":\"\",\"Color\":\"Rose Gold\",\"Grade\":\"\"," +
                "\"Erased\":\"\",\"" + "transaction_type\":\"\",\"LicenseID\":\"12149\",\"BatterChargeEnd\"" +
                ":\"\",\"Custom1\":\"A1687\",\"eBayRefurbished\":" +
                "\"Not Qualified\",\"BatteryDrainType\":\"\",\"SIM2MCC\":\"\"," +
                "\"BMic\":\"\",\"BatteryDesignMaxCapacity\":\"2725\",\"" +
                "PortNumber\":\"1\",\"SimLockResponse\":\"\",\"Network2\":\"\",\"" +
                "EraseStartTime\":\"\",\"Network1\":\"\",\"BatteryRes" +
                "istance\":\"4\",\"UDID\":\"bc61728cc203c7ec800646695efafeaafae9d2e1\"" +
                ",\"SimErased\":\"\",\"CompatibleSim\":\"\"," +
                "\"AppVersion\":\"3.0.611\",\"grade_route_id\":0,\"final_price\":\"0.0\"," +
                "\"NotCompatibleSim\":\"\",\"Cosmetics\":" +
                "\"\",\"CocoBatteryHealth\":\"0.0\",\"Notes\":\"\",\"SIM1Name\":\"\",\"" +
                "LicenseIdentifier\":\"7746A5A0F48\"," +
                "\"BatteryHealthPercentage\":\"85\",\"eBayRejection\":\"Not Tested, " +
                "Missing OEM\",\"OS\":\"iOS\",\"SKUCode" +
                "\":\"AP-iPhone 6S+-V-064-RG-A1687-\",\"DecimalMEID2\":\"\",\"IMEI\":" +
                "\"353283071864768\"," + "\"CocoDesignCapacity\":\"0\",\"Pre_check_Passed\":\"\",\"ErrorCode\":" +
                "\"Device Processing \",\"Pending\":\"Front Camera,Rear Camera,Front Microphone\",\"Ram\":\"\"}";

        CloudDbSyncRequest cloudDbSyncRequest = mapper.readValue(input, CloudDbSyncRequest.class);
        when(cloudRestTemplate.postForObject(eq(CLOUD_DB_SYNC_API),
                captor.capture(), eq(String.class))).thenReturn("success");
        String result = cloudApiRestClient.syncDataToCloudDb(cloudDbSyncRequest);
        Assertions.assertEquals("success", result);
    }

    @Test
    public void previousTransactionRequestTest() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<String>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        String response = """
                [{
                  "InfoID": "82324593","TransactionID": "2912","master_id": "1361",
                  "WareHouse": "496","MasterName": "Phonecheck Engineering ","Model": "iPhone SE (2nd Gen)",
                  "Memory": "64GB","IMEI": "356780113473091","Carrier": "",
                  "PCCarrier": "N/A","Serial": "FFPD80XAPLJQ","UDID": "00008030-001430213CD0402E",
                  "LicenseIdentifier": "5682418846548014","DeviceLock": "Off","AppleID": "",
                  "Rooted": "Not Rooted","Color": "Black","Grade": "","Version": "16.5",
                  "OS": "iOS","Make": "Apple","Firmware": "","Notes": "",
                  "ESN": "","LicenseID": "12840","DeviceCreatedDate": "2023-08-11 10:42:06",
                  "DeviceUpdatedDate": "2023-08-11 11:41:48","BatteryPercentage": "90","BatteryCycle": "234",
                  "BatteryHealthPercentage": "85.0","BatteryDesignMaxCapacity": "1810",
                  "BatteryCurrentMaxCapacity": "1528","BatterySerial": "N/A","BatteryModel": "N/A",
                  "BatterySource": "BS01","ModelNo": "MX9R2B/A","UnlockStatus": "",
                  "TesterName": "",
                  "Cosmetics": "",
                  "BuildNo": "6248.1","AppVersion": "",
                  "ManualEntry": "No","ESNResponse": "",
                  "SimLockResponse": "",
                  "LPN": "",
                  "Custom1": "",
                  "SKUCode": "N/A",
                  "Platform": "Mac OS X",
                  "RegulatoryModelNumber": "A2296",
                  "CosmeticsFailed": "",
                  "CosmeticsPassed": "",
                  "CosmeticsPending": "",
                  "CosmeticsWorking": "",
                  "Network": "","Network1": "","Network2": "",
                  "SIM1MCC": "","SIM1MNC": "","SIM2MCC": "",
                  "SIM2MNC": "","SIM1Name": "","SIM2Name": "",
                  "IMEI2": "356780113389594","SimTechnology": "GSM/CDMA",
                  "BatteryTemperature": "0.0","AvgBatteryTemperature": "",
                  "MaxBatteryTemperature": "","MinBatteryTemperature": "",
                  "BatteryResistance": "81.2","MEID": "35678011347309",
                  "MEID2": "","PESN": "",
                  "PESN2": "","SIMSERIAL": "",
                  "SIMSERIAL2": "",
                  "DecimalMEID": "35678011347309",
                  "DecimalMEID2": "","SimErased": "",
                  "MDM": "Off","ResolvedMake": "",
                  "ResolvedModelNo": "","ResolvedModel": "",
                  "ResolvedCarrier": "","ResolvedMemory": "",
                  "ResolvedColor": "","CountryOfOrigin": "UK and Ireland",
                  "BatteryDrainDuration": "","BatteryChargeStart": "",
                  "BatterChargeEnd": "","BatteryDrain": "", "WifiMacAddress": "",
                  "SimHistory": "","iCloudInfo": "Off",
                  "BatteryDrainInfo": "","CompatibleSim": "",
                  "NotCompatibleSim": "","DeviceState": "Results Override",
                  "BatteryDrainType": "","CocoCurrentCapacity": "0",
                  "CocoDesignCapacity": "","OEMBatteryHealth": "",
                  "CocoBatteryHealth": "","PortNumber": "",
                  "startHeat": "","endHeat": "",
                  "ProductCode": "","device_shutdown": "",
                  "SimLock": "","BMic": "",
                  "VMic": "","FMic": "","DefectsCode": "",
                  "ManualFailure": "","ErrorCode": "",
                  "ScreenTime": "","testerDeviceTime": "",
                  "gradePerformed": "0","isLabelPrint": "0",
                  "Knox": "",
                  "GUID": "https://historyreport.phonecheck.com/report-qr/dab143c2-3809-11ee-94ff-0692f2fbc775",
                  "Erased": "No","Type": "iOS Secure Erase",
                  "StartTime": "","EndTime": "",
                  "RestoreCode": "","erasedSD": "",
                  "erasedNotes": "","EraserDiff": "",
                  "id": "49592549","Working": "No",
                  "Passed": "Loud Speaker,Bluetooth,Ear Speaker,WiFi,M-Accelerometer,M-Flashlight,\
                   M-Home Button,M-Sim Reader,M-Front Camera Quality",
                  "Failed": "Touch Id,Bad ESN,Flip Switch,Rear Camera,LCD,Glass Condition\
                  ,Rear Camera Quality,Network Connectivity,Front Camera,Power Button,\
                  Proximity Sensor,Digitizer,Volume Down Button,Volume Up Button,\
                  Vibration,M-Microphone,M-Video Microphone,M-Front Microphone",
                  "Pending": "",
                  "Pre_check_working": "",
                  "Pre_check_Passed": "",
                  "Pre_check_Failed": "",
                  "Pre_check_Pending": "",
                  "VendorName": "Vendor Name",
                  "InvoiceNo": "Invoice",
                  "TransactionDate": "2023-08-11 10:41:40",
                  "isCloudTransaction": "0",
                  "StationID": "umerali",
                  "BoxNo": "Box No",
                  "QTY": "QTY",
                  "EraseStartTime": "",
                  "EraseEndTime": "",
                  "EraseType": "iOS Secure Erase",
                  "Model#": "MX9R2B/A",
                  "BatteryShutdown": "",
                  "TestPlanName": "",
                  "CarrierLockResponse": "",
                  "Parts": "",
                  "Ram": "",
                  "IFT_Codes": "",
                  "GradingResults": "",
                  "transaction_type": "",
                  "final_price": "",
                  "MDMResponse": "{\\"allowPairing\\":true,\\"cloudConfigurationUiComplete\\":true\
                  ,\\"configurationWasApplied\\":true,\\"organizationName\\":\\"PhoneCheck\\",\
                  \\"status\\":\\"Acknowledged\\",\\"configurationSource\\":\\"2\\",\
                  \\"postSetupProfileWasInstalled\\":true,\\"mdmStatus\\":\\"ON_FOR_PHONECHECK\\",\
                  \\"mandatory\\":false,\\"removable\\":true,\\"supervised\\":false}",
                  "BatteryHealthGrade": "",
                  "EID": "",
                  "esim_present": "No",
                  "esim_erased": "",
                  "eBayRefurbished": "",
                  "eBayRejection": "",
                  "amazonRenewed": "",
                  "amazonRenewedRejection": "",
                  "androidCarrierId": "",
                  "swappaQualified": "",
                  "swappaRejection": "",
                  "backMarketQualified": "",
                  "backMarketRejection": "",
                  "isMobileCosmetics": "",
                  "data_verification": "",
                  "start_battery_charge": "",
                  "end_battery_charge": "",
                  "total_battery_drain": "",
                  "warranty": ""
                }]
                """;

        TransactionDevicesRequest transactionDevicesRequest = new TransactionDevicesRequest();
        transactionDevicesRequest.setTransactionId(123);
        transactionDevicesRequest.setLicenseId(234);
        transactionDevicesRequest.setOffset(1);
        transactionDevicesRequest.setLimit(100);

        when(cloudRestTemplate.postForObject(eq(CLOUD_DB_PREVIOUS_TRANSACTION_API),
                captor.capture(), eq(String.class))).thenReturn(response);

        TransactionResponse.TransactionRecordResponse[] expectedOutput =
                mapper.readValue(response, TransactionResponse.TransactionRecordResponse[].class);

        TransactionResponse result =
                cloudApiRestClient.getTransactionDevices(transactionDevicesRequest);

        Assertions.assertNotNull(result.getTransactionRecords());
        Assertions.assertEquals(expectedOutput.length, result.getTransactionRecords().length);
        Assertions.assertEquals(expectedOutput[0].getTransactionId(),
                result.getTransactionRecords()[0].getTransactionId());
        Assertions.assertEquals(expectedOutput[0].getPassed(),
                result.getTransactionRecords()[0].getPassed());
        Assertions.assertEquals(expectedOutput[0].getFailed(),
                result.getTransactionRecords()[0].getFailed());
    }

    @Test
    public void getUpdateByColumnInfoTest() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<String>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        String input = """
                {
                  "BuildNo": "1.3.664.22",
                  "Carrier": "Wi-Fi + Cellular",
                  "Color": "Space Gray",
                  "DeviceState": "Home",
                  "EraseStartTime": "2023-03-31 07:07:22.727",
                  "EraseType": "iOS Secure Erase",
                  "Erased": "Yes",
                  "Grade": "",
                  "IMEI": "356750110340973",
                  "LicenseID": 14467,
                  "LicenseIdentifier": "E55AA3EE2402E",
                  "PortNumber": "2",
                  "Serial": "F6MDX7VBQ1KV",
                  "TransactionID": 872,
                  "erasedSD": ""
                }""";

        UpdateByColumnRequest updateByColumnRequest = mapper.readValue(input, UpdateByColumnRequest.class);
        when(cloudRestTemplate.postForObject(eq(UPDATE_BY_COLUMN_API),
                captor.capture(), eq(String.class))).thenReturn("Success");
        String result = cloudApiRestClient.syncColumnInfoToCloudDb(updateByColumnRequest);
        Assertions.assertEquals("Success", result);
    }

    @Test
    public void getUpdateDeviceDisconnectInfoTest() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<String>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        String input = """
                {
                  "LicenseID": 14467,
                  "Serial": "F6MDX7VBQ1KV",
                  "TransactionID": 872,
                  "deviceDisconnect": "2"
                }""";

        UpdateDeviceDisconnectRequest request = mapper.readValue(input, UpdateDeviceDisconnectRequest.class);
        when(cloudRestTemplate.postForObject(eq(UPDATE_BY_COLUMN_API),
                captor.capture(), eq(String.class))).thenReturn("Success");
        String result = cloudApiRestClient.syncDeviceDisconnectToCloudDb(request);
        Assertions.assertEquals("Success", result);
    }

    @Test
    public void getVersionsTest() {

        String responseStr = "{\"status\":true,\"result\":[{\"name\":\"EEE Codes\",\"url\":\"EEECodes\\/ApiCall\"," +
                "\"version\":\"3.9\",\"updatedDate\":\"2023-04-04 21:31:52\"}]}";

        when(cloudRestTemplate.postForObject(eq(GET_VERSIONS_API), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseStr);
        GetVersionsResponse response = cloudApiRestClient.getApiVersions("TestKey");
        Assertions.assertTrue(response.getStatus());
        Assertions.assertEquals(3.9f, response.getResult().get(0).getVersion());
        Assertions.assertEquals("EEE Codes", response.getResult().get(0).getName());
        verify(cloudRestTemplate).postForObject(eq(GET_VERSIONS_API), any(HttpEntity.class), eq(String.class));

    }


    @Test
    public void testCreateTransaction() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final String transactionResponse = """
                {
                    "VendorName": "New",
                    "InvoiceNo": "1",
                    "TransactionDate": "2023-04-16 06:54:42",
                    "TransactionID": "3989",
                    "isCloudTransaction": "0",
                    "LicenseID": "1710",
                    "StationID": "adityab1",
                    "BoxNo": "box",
                    "QTY": "qty"
                }
                """;

        Transaction transaction = new Transaction();
        transaction.setVendorName("adityab1");
        transaction.setLicenseId(1710);
        transaction.setInvoiceNo("1");

        CloudTransactionResponse expectedOutput = mapper.readValue(transactionResponse, CloudTransactionResponse.class);

        when(cloudRestTemplate.postForObject(eq(CREATE_TRANSACTION_API),
                captor.capture(), eq(String.class))).thenReturn(transactionResponse);

        CloudTransactionResponse result = cloudApiRestClient.createTransactionFromCloud(transaction);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(VENDOR_NAME));
        Assertions.assertTrue(mvMap.containsKey(LICENSE_ID1));
        Assertions.assertTrue(mvMap.containsKey(INVOICE_NO));
        Assertions.assertTrue(mvMap.containsKey(BOX_NO));
        Assertions.assertTrue(mvMap.containsKey(QTY));
        Assertions.assertTrue(mvMap.containsKey(VENDOR_NAME));
        Assertions.assertTrue(mvMap.containsKey(STATION_ID));
        Assertions.assertTrue(mvMap.containsKey(METHOD));

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput, result);
    }

    @Test
    public void testGetSimLockLicenseManagerResponse() throws JsonProcessingException {
        SimLockLicenseManagerRequest request = SimLockLicenseManagerRequest.builder()
                .deviceId("TestDeviceId").userId("TestUserId").apiKey("TestKey").deviceType("TestDeviceType")
                .carrier("TestCarrier").build();

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String jsonResponse = "{\"API\":\"S001SL\",\"Remarks\":\"Off\"," +
                "\"FieldColor\":\"Green\",\"deviceid\":\"353222105349669\",\"Carrier\":null," +
                "\"RawResponse\":\"Description: IPHONE 8 GOLD 64GB-USA" +
                "Model: iPhone 8 64GB Gold Cellular [A1863] [iPhone10,1]IMEI: 353222105349669" +
                "MEID: 35322210534966Serial Number: FFMZM0HSJC6F" +
                "Warranty Status: Out Of WarrantyPurchase Date: 2019-12-18" +
                "SIM-Lock: Unlocked\",\"fallbackcall\":\"No\",\"chargeStatus\":\"No\"}";

        SimLockLicenseManagerResponse testResponse = mapper.readValue(jsonResponse,
                SimLockLicenseManagerResponse.class);

        when(cloudRestTemplate.postForObject(eq(SIM_LOCK_LICENSE_MANAGER_API_CALL),
                captor.capture(), eq(String.class))).thenReturn(jsonResponse);


        SimLockLicenseManagerResponse response =
                cloudApiRestClient.getSimLockLicenseManagerResponse(request);

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(DEVICE_ID));
        Assertions.assertTrue(multiValueMap.containsKey(USER_ID1));
        Assertions.assertTrue(multiValueMap.containsKey(API_KEY));
        Assertions.assertTrue(multiValueMap.containsKey(DEVICE_TYPE));
        Assertions.assertTrue(multiValueMap.containsKey(CARRIER));

        Assertions.assertEquals(testResponse, response);
    }


    @Test
    @DisplayName("Test for EEE codes API")
    public void testGetEEECodes() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        final String eeeCodes = """
                {
                    "OEM PARTS": [
                        {
                            "Part Name": "Back Camera",
                            "Devices": [
                                {
                                    "Name": "iPhone 12",
                                    "EEE": "RSxrczpTU5Lelv9YbFuvhQ==",
                                    "Exception_keys": []
                                },
                                {
                                    "Name": "iPhone 12 Pro Max",
                                    "EEE": "tSfwTKXeXjAcs2GE91+d2w==",
                                    "Exception_keys": []
                                }
                            ]
                        },
                        {
                            "Part Name": "Battery",
                            "Devices": [
                                {
                                    "Name": "iPhone 11 Pro Max",
                                    "EEE": "0nbN82SUarPD8hBX5o8HAGkH3lau2ktcIBZl0R2A2j8=",
                                    "Exception_keys": []
                                },
                                {
                                    "Name": "iPhone 11 Pro",
                                    "EEE": "pvY3PGyeUe0nISyaMobXaL1Dsoh75nArjPiaGhoaUY4=",
                                    "Exception_keys": []
                                }
                            ]
                        }
                    ]
                }
                """;
        EEECodeResponse expectedOutput = mapper.readValue(eeeCodes, EEECodeResponse.class);

        when(cloudRestTemplate.postForObject(eq(GET_EEE_API),
                captor.capture(), eq(String.class))).thenReturn(eeeCodes);

        EEECodeResponse result = cloudApiRestClient.getEEECodes();

        // Assert the response fields match
        Assertions.assertEquals(expectedOutput, result);
    }

    @Test
    public void testGetTestKeys() throws JsonProcessingException {

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String responseStr = "{\"Android\":[\"Accelerometer\",\"Digitizer\",\"Fingerprint Sensor\"," +
                "\"Front Camera\",\"Glass Cracked\",\"Headset Port\",\"Headset-Left\"," +
                "\"Headset-Right\",\"Home Button\",\"LCD\",\"Loud Speaker\"," +
                "\"Menu Button\",\"Microphone\",\"Network Connectivity\"," +
                "\"Power Button\"],\"iOS\":[\"Accelerometer\",\"Digitizer\"," +
                "\"Fingerprint Sensor\",\"Front Camera\",\"Glass Cracked\"," +
                "\"Headset Port\",\"Headset-Left\",\"Headset-Right\",\"Home Button\"," +
                "\"LCD\",\"Loud Speaker\",\"Menu Button\",\"Microphone\"," +
                "\"Network Connectivity\",\"Power Button\"]}";

        TestKeys testKeysResponse = mapper.readValue(responseStr,
                TestKeys.class);

        when(cloudRestTemplate.postForObject(eq(GET_TEST_KEYS_API),
                captor.capture(), eq(String.class))).thenReturn(responseStr);

        TestKeys response = cloudApiRestClient.getTestKeys(18495);

        // Assert that all fields were set in form data
        MultiValueMap<String, String> mvMap = captor.getValue().getBody();
        Assertions.assertNotNull(mvMap);
        Assertions.assertTrue(mvMap.containsKey(LICENSE_ID));

        // Assert the response fields match
        Assertions.assertEquals(testKeysResponse, response);
    }

    @Test
    public void testGetVendorListResponse() throws JsonProcessingException {

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String jsonResponse = "{\n" +
                "    \"msg\": \"success\",\n" +
                "    \"response\": {\n" +
                "        \"All Vendor Invoices\": [\n" +
                "            {\n" +
                "                \"vendor\": \"abc\",\n" +
                "                \"invoice\": \"111\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"anu\",\n" +
                "                \"invoice\": \"fahad\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"Fazal\",\n" +
                "                \"invoice\": \"1234\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"Sabir\",\n" +
                "                \"invoice\": \"Sabir\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"GG%1!\",\n" +
                "                \"invoice\": \"B_3!\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"123\",\n" +
                "                \"invoice\": \"123\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"345\",\n" +
                "                \"invoice\": \"345\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"tara123\",\n" +
                "                \"invoice\": \"123456\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"dffd\",\n" +
                "                \"invoice\": \"448\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"vendor\": \"Muaaz\",\n" +
                "                \"invoice\": \"555\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"Assigned Vendor Invoice To Station\": {\n" +
                "            \"vendor\": \"tara123\",\n" +
                "            \"invoice\": \"123456\"\n" +
                "        }\n" +
                "    }\n" +
                "}";

        VendorInvoiceInfoResponse testResponse = mapper.readValue(jsonResponse, VendorInvoiceInfoResponse.class);

        when(cloudRestTemplate.postForObject(eq(GET_VENDOR_INVOICE), captor.capture(),
                eq(String.class))).thenReturn(jsonResponse);


        VendorInvoiceInfoResponse response = cloudApiRestClient.
                getVendorInvoiceList("taramac", "1361");

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(STATION_NAME));
        Assertions.assertTrue(multiValueMap.containsKey(MASTER_ID_2));

        Assertions.assertEquals(testResponse, response);
    }

    @Test
    @DisplayName("Test to get tester login info error message")
    public void testGetTesterLoginInfo() throws JsonProcessingException {

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);

        String jsonResponse = "{\"errors\":\"Credentials not verified or Mac ID is not registered\"}";

        StationLoginInfoResponse testResponse = mapper.readValue(jsonResponse, StationLoginInfoResponse.class);

        when(cloudRestTemplate.postForObject(eq(TESTER_LOGIN_PHONE_CHECK), captor.capture(),
                eq(StationLoginInfoResponse.class))).thenReturn(testResponse);

        StationLoginInfoResponse response = cloudApiRestClient.
                getTesterLoginInfo("testerName1", "password", "pc_id");

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(TESTER_NAME_KEY));
        Assertions.assertTrue(multiValueMap.containsKey(PASSWORD_KEY));
        Assertions.assertTrue(multiValueMap.containsKey(PC_ID_KEY));

        Assertions.assertEquals(testResponse.getErrors(), response.getErrors());
    }

    @Test
    @DisplayName("Test to get device by serial")
    public void testGetDeviceBySerial() throws JsonProcessingException {

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor =
                ArgumentCaptor.forClass(HttpEntity.class);

        String jsonResponse = "{\n" +
                "    \"InfoID\": \"84175743\",\n" +
                "    \"TransactionID\": \"1123\",\n" +
                "    \"master_id\": \"1361\",\n" +
                "    \"WareHouse\": \"\",\n" +
                "    \"MasterName\": \"Phonecheck Engineering \",\n" +
                "    \"Model\": \"\",\n" +
                "    \"Memory\": \"\",\n" +
                "    \"IMEI\": \"356469108294585\",\n" +
                "    \"Carrier\": \"Verizon\",\n" +
                "    \"PCCarrier\": \"\",\n" +
                "    \"Serial\": \"F17D9AEYPLJN\",\n" +
                "    \"UDID\": \"00008030-000519580C50402E\",\n" +
                "    \"LicenseIdentifier\": \"\",\n" +
                "    \"DeviceLock\": \"\",\n" +
                "    \"AppleID\": \"\",\n" +
                "    \"Rooted\": \"Not Rooted\",\n" +
                "    \"Color\": \"White\",\n" +
                "    \"Grade\": null,\n" +
                "    \"Version\": \"14.5.1\",\n" +
                "    \"OS\": \"iOS\",\n" +
                "    \"Make\": \"\",\n" +
                "    \"Firmware\": null,\n" +
                "    \"Notes\": null,\n" +
                "    \"ESN\": null,\n" +
                "    \"LicenseID\": \"14494\",\n" +
                "    \"DeviceCreatedDate\": \"2023-09-13 09:19:52\",\n" +
                "    \"DeviceUpdatedDate\": \"2023-09-13 10:00:16\",\n" +
                "    \"BatteryPercentage\": \"\",\n" +
                "    \"BatteryCycle\": \"\",\n" +
                "    \"BatteryHealthPercentage\": \"\",\n" +
                "    \"BatteryDesignMaxCapacity\": \"\",\n" +
                "    \"BatteryCurrentMaxCapacity\": \"\",\n" +
                "    \"BatterySerial\": \"\",\n" +
                "    \"BatteryModel\": \"\",\n" +
                "    \"BatterySource\": \"\",\n" +
                "    \"ModelNo\": \"\",\n" +
                "    \"UnlockStatus\": null,\n" +
                "    \"TesterName\": \"\",\n" +
                "    \"Cosmetics\": \"\",\n" +
                "    \"BuildNo\": \"\",\n" +
                "    \"AppVersion\": \"\",\n" +
                "    \"ManualEntry\": \"No\",\n" +
                "    \"ESNResponse\": null,\n" +
                "    \"SimLockResponse\": \"\",\n" +
                "    \"LPN\": null,\n" +
                "    \"Custom1\": null,\n" +
                "    \"SKUCode\": \"N/A\",\n" +
                "    \"Platform\": \"Mac OS X\",\n" +
                "    \"RegulatoryModelNumber\": \"A2275\",\n" +
                "    \"CosmeticsFailed\": \"\",\n" +
                "    \"CosmeticsPassed\": \"\",\n" +
                "    \"CosmeticsPending\": \"\",\n" +
                "    \"CosmeticsWorking\": \"\",\n" +
                "    \"Network\": null,\n" +
                "    \"Network1\": \"\",\n" +
                "    \"Network2\": \"\",\n" +
                "    \"SIM1MCC\": \"\",\n" +
                "    \"SIM1MNC\": \"\",\n" +
                "    \"SIM2MCC\": \"\",\n" +
                "    \"SIM2MNC\": \"\",\n" +
                "    \"SIM1Name\": \"\",\n" +
                "    \"SIM2Name\": \"\",\n" +
                "    \"IMEI2\": \"356469108941219\",\n" +
                "    \"SimTechnology\": \"\",\n" +
                "    \"BatteryTemperature\": \"\",\n" +
                "    \"AvgBatteryTemperature\": \"\",\n" +
                "    \"MaxBatteryTemperature\": \"\",\n" +
                "    \"MinBatteryTemperature\": \"\",\n" +
                "    \"BatteryResistance\": \"\",\n" +
                "    \"MEID\": \"35646910829458\",\n" +
                "    \"MEID2\": \"\",\n" +
                "    \"PESN\": \"\",\n" +
                "    \"PESN2\": \"\",\n" +
                "    \"SIMSERIAL\": \"\",\n" +
                "    \"SIMSERIAL2\": \"\",\n" +
                "    \"DecimalMEID\": \"35646910829458\",\n" +
                "    \"DecimalMEID2\": \"\",\n" +
                "    \"SimErased\": \"\",\n" +
                "    \"MDM\": \"Off\",\n" +
                "    \"ResolvedMake\": \"\",\n" +
                "    \"ResolvedModelNo\": \"\",\n" +
                "    \"ResolvedModel\": \"\",\n" +
                "    \"ResolvedCarrier\": \"\",\n" +
                "    \"ResolvedMemory\": \"\",\n" +
                "    \"ResolvedColor\": \"\",\n" +
                "    \"CountryOfOrigin\": \"\",\n" +
                "    \"BatteryDrainDuration\": \"\",\n" +
                "    \"BatteryChargeStart\": \"\",\n" +
                "    \"BatterChargeEnd\": \"\",\n" +
                "    \"BatteryDrain\": \"\",\n" +
                "    \"WifiMacAddress\": \"\",\n" +
                "    \"SimHistory\": null,\n" +
                "    \"iCloudInfo\": \"Off\",\n" +
                "    \"BatteryDrainInfo\": \"\",\n" +
                "    \"CompatibleSim\": \"\",\n" +
                "    \"NotCompatibleSim\": \"\",\n" +
                "    \"DeviceState\": \"Results Override\",\n" +
                "    \"BatteryDrainType\": \"\",\n" +
                "    \"CocoCurrentCapacity\": \"0\",\n" +
                "    \"CocoDesignCapacity\": \"\",\n" +
                "    \"OEMBatteryHealth\": \"\",\n" +
                "    \"CocoBatteryHealth\": \"\",\n" +
                "    \"PortNumber\": \"1\",\n" +
                "    \"startHeat\": \"\",\n" +
                "    \"endHeat\": \"\",\n" +
                "    \"ProductCode\": \"\",\n" +
                "    \"device_shutdown\": \"\",\n" +
                "    \"SimLock\": \"\",\n" +
                "    \"BMic\": \"\",\n" +
                "    \"VMic\": \"\",\n" +
                "    \"FMic\": \"\",\n" +
                "    \"DefectsCode\": \"\",\n" +
                "    \"ManualFailure\": \"\",\n" +
                "    \"ErrorCode\": \"\",\n" +
                "    \"ScreenTime\": \"\",\n" +
                "    \"testerDeviceTime\": \"\",\n" +
                "    \"gradePerformed\": \"0\",\n" +
                "    \"isLabelPrint\": \"0\",\n" +
                "    \"Knox\": \"\",\n" +
                "    \"GUID\": " +
                "\"https://historyreport.phonecheck.com/report-qr/e3aeb9e6-51ec-11ee-b532-0692f2fbc775\",\n" +
                "    \"Erased\": \"\",\n" +
                "    \"Type\": \"iOS Secure Erase\",\n" +
                "    \"StartTime\": \"\",\n" +
                "    \"EndTime\": \"\",\n" +
                "    \"RestoreCode\": \"\",\n" +
                "    \"erasedSD\": null,\n" +
                "    \"erasedNotes\": \"\",\n" +
                "    \"EraserDiff\": \"00:01:32\",\n" +
                "    \"id\": \"51453512\",\n" +
                "    \"Working\": \"\",\n" +
                "    \"Passed\": \"Touch Id,Gyroscope,Home Button,Volume Up Button," +
                "Volume Down Button,WiFi,Ear Speaker,Brightness,Screen Rotation,Flip Switch," +
                "Wireless Charging,LCD,Power Button,Loud Speaker,Glass Condition,Proximity " +
                "Sensor,Bluetooth,Accelerometer\",\n" +
                "    \"Failed\": \"Grading,NFC,Rear Mic Quality,Barcode Scan,Earpiece " +
                "Quality,C-Anas,Microphone,Front Mic Quality,Front Video Camera,Rear Camera,Headset" +
                " Light_Port,C-new test,GPS,Rear Cam to Gallery,Headset-Right,Rear Camera Quality," +
                "Flashlight,Rear Video Camera,Front Microphone,Vibration,Cosmetics,Multi Touch,Video" +
                " Microphone,SW Version,Sim Remove,Digitizer N Pattern,Network Connectivity,Bottom " +
                "Mic Quality,Sim Reader,Headset-Left\",\n" +
                "    \"Pending\": null,\n" +
                "    \"Pre_check_working\": null,\n" +
                "    \"Pre_check_Passed\": null,\n" +
                "    \"Pre_check_Failed\": null,\n" +
                "    \"Pre_check_Pending\": null,\n" +
                "    \"VendorName\": \"\",\n" +
                "    \"InvoiceNo\": \"\",\n" +
                "    \"TransactionDate\": \"2023-09-13 09:17:48\",\n" +
                "    \"isCloudTransaction\": \"0\",\n" +
                "    \"StationID\": \"adityab1\",\n" +
                "    \"BoxNo\": \"\",\n" +
                "    \"QTY\": \" \",\n" +
                "    \"EraseStartTime\": \"2023-09-13 09:22:24\",\n" +
                "    \"EraseEndTime\": \"2023-09-13 09:23:56\",\n" +
                "    \"EraseType\": \"iOS Secure Erase\",\n" +
                "    \"Model#\": \"MX9L2LL/A\",\n" +
                "    \"BatteryShutdown\": \"\",\n" +
                "    \"TestPlanName\": \"\",\n" +
                "    \"CarrierLockResponse\": null,\n" +
                "    \"Parts\": null,\n" +
                "    \"Ram\": null,\n" +
                "    \"IFT_Codes\": null,\n" +
                "    \"GradingResults\": null,\n" +
                "    \"transaction_type\": null,\n" +
                "    \"final_price\": null,\n" +
                "    \"MDMResponse\": null,\n" +
                "    \"EID\": null,\n" +
                "    \"eBayRefurbished\": null,\n" +
                "    \"eBayRejection\": null,\n" +
                "    \"amazonRenewed\": null,\n" +
                "    \"amazonRenewedRejection\": null,\n" +
                "    \"androidCarrierId\": null,\n" +
                "    \"swappaQualified\": null,\n" +
                "    \"swappaRejection\": null,\n" +
                "    \"backMarketQualified\": null,\n" +
                "    \"backMarketRejection\": null,\n" +
                "    \"testingCompleted\": \"1\",\n" +
                "    \"isMobileCosmetics\": null,\n" +
                "    \"data_verification\": null,\n" +
                "    \"start_battery_charge\": null,\n" +
                "    \"end_battery_charge\": null,\n" +
                "    \"total_battery_drain\": null,\n" +
                "    \"warranty\": \"\"\n" +
                "}";

        SyncedCloudDevice testResponse = mapper.readValue(jsonResponse, SyncedCloudDevice.class);

        when(cloudRestTemplate.postForObject(eq(GET_DEVICE_BY_SERIAL), captor.capture(),
                eq(String.class))).thenReturn(jsonResponse);

        SyncedCloudDevice response = cloudApiRestClient.
                getDeviceBySerial("serial", 1234);

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(SERIAL));
        Assertions.assertTrue(multiValueMap.containsKey(LICENSE_ID1));

        Assertions.assertEquals(testResponse.getGuid(), response.getGuid());
    }

    @Test
    @DisplayName("Test to get device by serial returns FALSE")
    public void testGetDeviceBySerialFailed() {

        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor =
                ArgumentCaptor.forClass(HttpEntity.class);

        String jsonResponse = "\"FALSE\"";

        when(cloudRestTemplate.postForObject(eq(GET_DEVICE_BY_SERIAL), captor.capture(),
                eq(String.class))).thenReturn(jsonResponse);

        SyncedCloudDevice response = cloudApiRestClient.
                getDeviceBySerial("serial", 1234);

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(SERIAL));
        Assertions.assertTrue(multiValueMap.containsKey(LICENSE_ID1));

        Assertions.assertEquals("Device Not Found", response.getErrorResponse());
        Assertions.assertNull(response.getGuid());
    }

    @Test
    @DisplayName("Get the firmware list")
    public void testGetFirmwareList() throws JsonProcessingException {

        String expectedJsonResponse = "[\n" +
                "    {\n" +
                "        \"ID\": \"iPad1,1\",\n" +
                "        \"Name\": \"iPad 1\",\n" +
                "        \"Firmwares\": [\n" +
                "            {\n" +
                "                \"BuildId\": \"9B206\",\n" +
                "                \"Filename\": \"iPad1,1_5.1.1_9B206_Restore.ipsw\",\n" +
                "                \"Md5Sum\": \"859a6ded3129af1ed0d56f3e25c873a2\",\n" +
                "                \"ReleaseDate\": \"\",\n" +
                "                \"Sha1Sum\": \"ad9b607439250f2337fe132890dadc4c487beca8\",\n" +
                "                \"Signed\": true,\n" +
                "                \"Size\": 761323675,\n" +
                "                \"Uploaddate\": \"2012-04-27T21:16:02Z\",\n" +
                "                \"Url\": \"http://appldnld.apple.com/iOS5.1.1/041-4292.02120427.Tkk0d/" +
                "iPad1,1_5.1.1_9B206_Restore.ipsw\",\n" +
                "                \"Version\": \"5.1.1\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"ID\": \"iPad11,1\",\n" +
                "        \"Name\": \"iPad mini 5 (WiFi)\",\n" +
                "        \"Firmwares\": [\n" +
                "            {\n" +
                "                \"BuildId\": \"21B91\",\n" +
                "                \"Filename\": \"iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Md5Sum\": \"fa7ad7613bbe2dd952a8f1e83c02534d\",\n" +
                "                \"ReleaseDate\": \"2023-11-08T18:58:26Z\",\n" +
                "                \"Sha1Sum\": \"a0517f00bd79f34953a8126af863bbd8ff1a2145\",\n" +
                "                \"Signed\": true,\n" +
                "                \"Size\": 7410067707,\n" +
                "                \"Uploaddate\": \"2023-11-03T16:20:48Z\",\n" +
                "                \"Url\": \"https://updates.cdn-apple.com/2023FallFCS/fullrestores/042-96217/" +
                "0CFC2F18-41AB-411F-BEE7-A357F452A776/iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Version\": \"17.1.1\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"ID\": \"iPad11,2\",\n" +
                "        \"Name\": \"iPad mini 5 (Cellular)\",\n" +
                "        \"Firmwares\": [\n" +
                "            {\n" +
                "                \"BuildId\": \"21B91\",\n" +
                "                \"Filename\": \"iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Md5Sum\": \"fa7ad7613bbe2dd952a8f1e83c02534d\",\n" +
                "                \"ReleaseDate\": \"2023-11-08T18:58:26Z\",\n" +
                "                \"Sha1Sum\": \"a0517f00bd79f34953a8126af863bbd8ff1a2145\",\n" +
                "                \"Signed\": true,\n" +
                "                \"Size\": 7410067707,\n" +
                "                \"Uploaddate\": \"2023-11-03T16:20:48Z\",\n" +
                "                \"Url\": \"https://updates.cdn-apple.com/2023FallFCS/fullrestores/" +
                "042-96217/0CFC2F18-41AB-411F-BEE7-A357F452A776/iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Version\": \"17.1.1\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"ID\": \"iPad11,3\",\n" +
                "        \"Name\": \"iPad Air 3 (WiFi)\",\n" +
                "        \"Firmwares\": [\n" +
                "            {\n" +
                "                \"BuildId\": \"21B91\",\n" +
                "                \"Filename\": \"iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Md5Sum\": \"fa7ad7613bbe2dd952a8f1e83c02534d\",\n" +
                "                \"ReleaseDate\": \"2023-11-08T18:58:24Z\",\n" +
                "                \"Sha1Sum\": \"a0517f00bd79f34953a8126af863bbd8ff1a2145\",\n" +
                "                \"Signed\": true,\n" +
                "                \"Size\": 7410067707,\n" +
                "                \"Uploaddate\": \"2023-11-03T16:20:48Z\",\n" +
                "                \"Url\": \"https://updates.cdn-apple.com/2023FallFCS/fullrestores/" +
                "042-96217/0CFC2F18-41AB-411F-BEE7-A357F452A776/iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Version\": \"17.1.1\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"ID\": \"iPad11,4\",\n" +
                "        \"Name\": \"iPad Air 3 (Cellular)\",\n" +
                "        \"Firmwares\": [\n" +
                "            {\n" +
                "                \"BuildId\": \"21B91\",\n" +
                "                \"Filename\": \"iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Md5Sum\": \"fa7ad7613bbe2dd952a8f1e83c02534d\",\n" +
                "                \"ReleaseDate\": \"2023-11-08T18:58:25Z\",\n" +
                "                \"Sha1Sum\": \"a0517f00bd79f34953a8126af863bbd8ff1a2145\",\n" +
                "                \"Signed\": true,\n" +
                "                \"Size\": 7410067707,\n" +
                "                \"Uploaddate\": \"2023-11-03T16:20:48Z\",\n" +
                "                \"Url\": \"https://updates.cdn-apple.com/2023FallFCS/fullrestores/" +
                "042-96217/0CFC2F18-41AB-411F-BEE7-A357F452A776/iPad_Spring_2019_17.1.1_21B91_Restore.ipsw\",\n" +
                "                \"Version\": \"17.1.1\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }]";
        List<LatestFirmwareListResponse.LatestFirmwareInfo> expectedResponse = null;
        JavaType type = mapper.getTypeFactory().constructCollectionType(List.class,
                LatestFirmwareListResponse.LatestFirmwareInfo.class);
        expectedResponse = mapper.readValue(expectedJsonResponse, type);

        // Mock the restTemplate's behavior
        when(cloudRestTemplate.getForObject(eq(GET_LATEST_FIRMWARE_LIST_API),
                eq(String.class))).thenReturn(expectedJsonResponse);
        // Call the method to be tested
        LatestFirmwareListResponse response = cloudApiRestClient.getLatestFirmwareListInfo();
        List<LatestFirmwareListResponse.LatestFirmwareInfo> actualResponse = List.of(response.getLatestFirmwareInfo());

        // Assert that the actual response matches the expected response
        assertArrayEquals(expectedResponse.toArray(), actualResponse.toArray());
    }

    @Test
    @DisplayName("Device device unlock status by serial")
    public void testGetDeviceUnlockStatusBySerial() throws JsonProcessingException {
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        String jsonResponse = "{\"UnlockStatus\":\"UNLK\"}";
        UnlockStatusBySerialResponse testResponse = mapper.readValue(jsonResponse, UnlockStatusBySerialResponse.class);
        when(cloudRestTemplate.postForObject(eq(GET_DEVICE_BY_SERIAL), captor.capture(),
                eq(UnlockStatusBySerialResponse.class))).thenReturn(testResponse);

        UnlockStatusBySerialResponse response = cloudApiRestClient.
                getDeviceUnlockStatusBySerial("serial", 1234);

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(SERIAL));
        Assertions.assertTrue(multiValueMap.containsKey(LICENSE_ID1));
        Assertions.assertEquals(testResponse.getUnlockStatus(), response.getUnlockStatus());
    }


    @Test
    @DisplayName("Device ESN Info when checkmend is enabled")
    public void testGetEsnInfoForCheckmend() throws JsonProcessingException {
        when(inMemoryStore.isEuServer()).thenReturn(false);
        ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> captor = ArgumentCaptor.forClass(HttpEntity.class);
        EsnLicenseCheckRequest licenseCheckRequest = new EsnLicenseCheckRequest();
        licenseCheckRequest.setImei("TestIMEI1");
        licenseCheckRequest.setImei2("TestIMEI2");
        licenseCheckRequest.setSerial("TestSerial");
        licenseCheckRequest.setCarrier("TestCarrier");
        licenseCheckRequest.setDeviceType("Android");
        licenseCheckRequest.setUserId("testUser");
        licenseCheckRequest.setServiceId("TestServiceId");
        licenseCheckRequest.setTesterId("Tester1");
        licenseCheckRequest.setWarehouseId("Warehouse1");
        licenseCheckRequest.setCheckAll(1);
        String jsonResponse = "[\n" +
                "    {\n" +
                "        \"API\": \"C001\",\n" +
                "        \"Remarks\": \"Good\",\n" +
                "        \"FieldColor\": \"Green\",\n" +
                "        \"deviceid\": \"352946241446796\",\n" +
                "        \"RawResponse\": {\n" +
                "            \"result\": \"passed\",\n" +
                "            \"certid\": \"8-FB72A2BB71B-547:E55A56FA\",\n" +
                "            \"IMEI1\": \"352946241446796\"\n" +
                "        },\n" +
                "        \"chargeStatus\": \"Yes\"\n" +
                "    }\n" +
                "]";

        EsnResponse.EsnApiResponse[] testResponse = mapper.readValue(jsonResponse, EsnResponse.EsnApiResponse[].class);
        when(cloudRestTemplate.postForObject(eq(ESN_CHECK_MEND_API), captor.capture(),
                eq(String.class))).thenReturn(jsonResponse);

        EsnResponse response = cloudApiRestClient.getEsnInfoByCheckMend(licenseCheckRequest);

        MultiValueMap<String, String> multiValueMap = captor.getValue().getBody();
        Assertions.assertNotNull(multiValueMap);
        Assertions.assertTrue(multiValueMap.containsKey(SERIAL));
        Assertions.assertTrue(multiValueMap.containsKey(CHECK_ALL_ESN_CHECK_MEND));
        Assertions.assertNotNull(response);
        EsnResponse.EsnApiResponse expected = testResponse[0];
        EsnResponse.EsnApiResponse actual = response.getEsnApiResults()[0];
        Assertions.assertEquals(testResponse.length, response.getEsnApiResults().length);
        Assertions.assertEquals(expected.getImei(), actual.getImei());
        Assertions.assertEquals(expected.getApi(), actual.getApi());
        Assertions.assertEquals(expected.getRawResponse(), actual.getRawResponse());
    }


}
