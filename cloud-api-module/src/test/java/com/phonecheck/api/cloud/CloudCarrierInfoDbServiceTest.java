package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CarrierInfoResponse;
import com.phonecheck.model.service.CarrierInfoDb;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CloudCarrierInfoDbServiceTest {

    @Mock
    private CloudApiRestClient cloudApiRestClient;
    private CloudCarrierInfoService carrierInfoService;

    @BeforeEach
    public void setup() {
        carrierInfoService = new CloudCarrierInfoService(cloudApiRestClient);
    }

    @Test
    public void testGetCarrierInfo() {

        String version = "1234";
        CarrierInfoResponse testResponse = new CarrierInfoResponse();
        testResponse.setVersion(version);
        testResponse.setCarrierInfoList(List.of(new CarrierInfoResponse.CarrierInfo()));

        when(cloudApiRestClient.getLatestCarrierInfo(anyString())).thenReturn(testResponse);
        CarrierInfoDb result = carrierInfoService.getCarrierInfo(version);
        Assertions.assertEquals(version, testResponse.getVersion());
        Assertions.assertEquals(testResponse.getCarrierInfoList(), result.getCarrierInfo());
    }

}
