package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.dao.model.PendingSyncRecord;
import com.phonecheck.dao.service.PendingSyncRecordsDBService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.CloudDbSyncRequest;
import com.phonecheck.model.cloudapi.UpdateByColumnRequest;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.CosmeticsResults;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.TestResults;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.OsChecker;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CloudDeviceDataSyncServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    @Mock
    private PendingSyncRecordsDBService pendingSyncRecordsDbService;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private OsChecker osChecker;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private LocalizationService localizationService;
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;

    @BeforeEach
    public void setup() {
        Transaction transaction = new Transaction();
        transaction.setTransactionId(12);
        when(inMemoryStore.getTransaction()).thenReturn(transaction);
        cloudDeviceDataSyncService = new CloudDeviceDataSyncService(
                cloudApiRestClient,
                pendingSyncRecordsDbService,
                inMemoryStore,
                osChecker,
                deviceConnectionTracker, localizationService, inMemoryStore);
    }

    @Test
    public void cloudDbSyncRequestSuccessTest1()  {
        Device device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.READY);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().id("123")
                .build());
        when(cloudApiRestClient.syncDataToCloudDb(any(CloudDbSyncRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncDataToCloudDb(any(CloudDbSyncRequest.class));
        verify(pendingSyncRecordsDbService, never()).insertPendingSyncRecord(any(PendingSyncRecord.class));
    }

    @Test
    @DisplayName("Dont update cosmetic results if cosmetic type is 'Multi' in cloud customization")
    public void cloudDbSyncRequestSuccessTest2()  {
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .id("123")
                .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings.builder()
                        .enableCosmetics(true)
                        .cosmeticType(CloudCustomizationResponse.CosmeticType.MULTI)
                        .saveCosmetics(CloudCustomizationResponse.SaveCosmetics.COSMETIC_AND_PASSED)
                        .preferredFormat(CloudCustomizationResponse.PreferredCosmeticFormat.NAME_AND_FAILED_RESPONSE)
                        .build())
                .build());

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("Volume Down Button,Volume Up Button")));
        testResults.setPassed(new ArrayList<>(List.of("Loud Speaker,Screen Rotation")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);

        Device device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.TEST_CONFIG_SUCCESS);
        device.setDeviceTestResult(deviceTestResult);


        when(cloudApiRestClient.syncDataToCloudDb(any(CloudDbSyncRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncDataToCloudDb(argThat((CloudDbSyncRequest arg) -> {
            String cosmeticResult = arg.getCosmetics();
            String failedTests = arg.getFailed();
            String passedTests = arg.getPassed();

            Assertions.assertEquals("Does phone look good-no", cosmeticResult);
            Assertions.assertEquals("Volume Down Button,Volume Up Button", failedTests);
            Assertions.assertEquals("Loud Speaker,Screen Rotation", passedTests);
            return true;
        }));
    }

    @Test
    @DisplayName("Update only cosmetic test results with only cosmetic name")
    public void cloudDbSyncRequestSuccessTest3() {
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .id("123")
                .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings.builder()
                        .enableCosmetics(true)
                        .cosmeticType(CloudCustomizationResponse.CosmeticType.QA)
                        .saveCosmetics(CloudCustomizationResponse.SaveCosmetics.COSMETIC_ONLY)
                        .preferredFormat(CloudCustomizationResponse.PreferredCosmeticFormat.NAME)
                        .build())
                .build());

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("Volume Down Button,Volume Up Button")));
        testResults.setPassed(new ArrayList<>(List.of("Loud Speaker,Screen Rotation")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no$not at all,Is phone clean?-No$Don't know");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);

        Device device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.TEST_CONFIG_SUCCESS);
        device.setDeviceTestResult(deviceTestResult);

        when(cloudApiRestClient.syncDataToCloudDb(any(CloudDbSyncRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());
        verify(cloudApiRestClient).syncDataToCloudDb(argThat((CloudDbSyncRequest arg) -> {
            String cosmeticResult = arg.getCosmetics();
            String failedTests = arg.getFailed();
            String passedTests = arg.getPassed();

            Assertions.assertEquals("Does phone look good,Is phone clean?", cosmeticResult);
            Assertions.assertEquals("Volume Down Button,Volume Up Button", failedTests);
            Assertions.assertEquals("Loud Speaker,Screen Rotation", passedTests);
            return true;
        }));
    }

    @Test
    @DisplayName("Update cosmetic results, passed tests and failed tests with both cosmetic name and response")
    public void cloudDbSyncRequestSuccessTest4() {
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .id("123")
                .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings.builder()
                        .enableCosmetics(true)
                        .cosmeticType(CloudCustomizationResponse.CosmeticType.QA)
                        .saveCosmetics(CloudCustomizationResponse.SaveCosmetics.COSMETIC_AND_PASSED_OR_FAILED)
                        .preferredFormat(CloudCustomizationResponse.PreferredCosmeticFormat.
                                COSMETIC_AND_BOTH_RESPONSE)
                        .build())
                .build());

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("Volume Down Button,Volume Up Button")));
        testResults.setPassed(new ArrayList<>(List.of("Loud Speaker,Screen Rotation")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no$not at all,Is phone clean?-No$Don't know");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);

        Device device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.TEST_CONFIG_SUCCESS);
        device.setDeviceTestResult(deviceTestResult);

        when(cloudApiRestClient.syncDataToCloudDb(any(CloudDbSyncRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncDataToCloudDb(argThat((CloudDbSyncRequest arg) -> {
            String cosmeticResult = arg.getCosmetics();
            String failedTests = arg.getFailed();
            String passedTests = arg.getPassed();

            Assertions.assertEquals("Does phone look good-no$not at all,Is phone clean?-No$Don't know," +
                    "Is screen ok?-yes", cosmeticResult);
            Assertions.assertEquals("Does phone look good-no$not at all,Is phone clean?-No$Don't know," +
                    "Volume Down Button,Volume Up Button", failedTests);
            Assertions.assertEquals("Is screen ok?-yes,Loud Speaker,Screen Rotation", passedTests);
            return true;
        }));
    }

    @Test
    @DisplayName("Update data verification result in cloud customization")
    public void cloudDbSyncRequestSuccessTest5() {
        Device device = new IosDevice();
        device.setId("test123");
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.PAIRING_SUCCEEDED);
        device.setDataVerification("Data detected in the device");


        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().id("123")
                .build());
        when(cloudApiRestClient.syncDataToCloudDb(any(CloudDbSyncRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncDataToCloudDb(argThat((CloudDbSyncRequest arg) -> {
            Assertions.assertEquals("Data detected in the device", arg.getDataVerification());
            return true;
        }));
    }

    @Test
    public void cloudDbSyncRequestFailureTest() {
        IosDevice device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.DISCONNECTED);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().id("123")
                .build());
        when(cloudApiRestClient.syncDataToCloudDb(any(CloudDbSyncRequest.class))).thenReturn("Fail");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncDataToCloudDb(any(CloudDbSyncRequest.class));
        verify(pendingSyncRecordsDbService, times(1)).insertPendingSyncRecord(any(PendingSyncRecord.class));
    }

    @Test
    public void updateByColumnRequestSuccessTest1() {
        IosDevice device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.PUSH_WIFI_PROFILE);
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(assignedCustomization);
        when(cloudApiRestClient.syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class));
        verify(pendingSyncRecordsDbService, never()).insertPendingSyncRecord(any(PendingSyncRecord.class));
    }

    @Test
    @DisplayName("Update device erase notes column")
    public void updateByColumnRequestSuccessTest2() {
        IosDevice device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.ERASE_SUCCESS);
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(assignedCustomization);
        when(cloudApiRestClient.syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class));
        verify(pendingSyncRecordsDbService, never()).insertPendingSyncRecord(any(PendingSyncRecord.class));
    }

    @Test
    @DisplayName("Update only cosmetic results with only cosmetic name")
    public void updateByColumnRequestSuccessTest3() {
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .id("123")
                .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings.builder()
                        .enableCosmetics(true)
                        .cosmeticType(CloudCustomizationResponse.CosmeticType.QA)
                        .saveCosmetics(CloudCustomizationResponse.SaveCosmetics.COSMETIC_ONLY)
                        .preferredFormat(CloudCustomizationResponse.PreferredCosmeticFormat.NAME)
                        .build())
                .build());

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("Volume Down Button,Volume Up Button")));
        testResults.setPassed(new ArrayList<>(List.of("Loud Speaker,Screen Rotation")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);

        IosDevice device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.TEST_CONFIG_SUCCESS);
        device.setDeviceTestResult(deviceTestResult);

        when(cloudApiRestClient.syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());
        verify(cloudApiRestClient).syncColumnInfoToCloudDb(argThat((UpdateByColumnRequest arg) -> {
            String cosmeticResult = arg.getCosmetics();
            String failedTests = arg.getFailed();
            String passedTests = arg.getPassed();

            Assertions.assertEquals("Does phone look good", cosmeticResult);
            Assertions.assertEquals("Volume Down Button,Volume Up Button", failedTests);
            Assertions.assertEquals("Loud Speaker,Screen Rotation", passedTests);
            return true;
        }));
    }

    @Test
    @DisplayName("Update cosmetic results, passed tests and failed tests with both cosmetic name and response")
    public void updateByColumnRequestSuccessTest4() {
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .id("123")
                .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings.builder()
                        .enableCosmetics(true)
                        .cosmeticType(CloudCustomizationResponse.CosmeticType.QA)
                        .saveCosmetics(CloudCustomizationResponse.SaveCosmetics.COSMETIC_AND_PASSED_OR_FAILED)
                        .preferredFormat(CloudCustomizationResponse.PreferredCosmeticFormat.
                                COSMETIC_AND_BOTH_RESPONSE)
                        .build())
                .build());

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("Volume Down Button,Volume Up Button")));
        testResults.setPassed(new ArrayList<>(List.of("Loud Speaker,Screen Rotation")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);

        IosDevice device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.TEST_CONFIG_SUCCESS);
        device.setDeviceTestResult(deviceTestResult);

        when(cloudApiRestClient.syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncColumnInfoToCloudDb(argThat((UpdateByColumnRequest arg) -> {
            String cosmeticResult = arg.getCosmetics();
            String failedTests = arg.getFailed();
            String passedTests = arg.getPassed();

            Assertions.assertEquals("Does phone look good-no,Is screen ok?-yes", cosmeticResult);
            Assertions.assertEquals("Does phone look good-no,Volume Down Button,Volume Up Button", failedTests);
            Assertions.assertEquals("Is screen ok?-yes,Loud Speaker,Screen Rotation", passedTests);
            return true;
        }));
    }

    @Test
    @DisplayName("Update data verification results in cloud DB")
    public void updateByColumnRequestSuccessTest5() {
        IosDevice device = new IosDevice();
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.ERASE_SUCCESS);
        device.setDataVerification("Data was not detected in the device");
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(assignedCustomization);
        when(cloudApiRestClient.syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class))).thenReturn("Success");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(pendingSyncRecordsDbService, never()).insertPendingSyncRecord(any(PendingSyncRecord.class));
        verify(cloudApiRestClient).syncColumnInfoToCloudDb(argThat((UpdateByColumnRequest arg) -> {
            Assertions.assertEquals("Data was not detected in the device", arg.getDataVerification());
            return true;
        }));

    }

    @Test
    public void updateByColumnRequestFailureTest() {
        Device device = new IosDevice();
        device.setId("testId");
        device.setSerial("ABC1EFG");
        device.setStage(DeviceStage.ERASE_SUCCESS);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().id("123")
                .build());
        when(cloudApiRestClient.syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class))).thenReturn("Fail");

        cloudDeviceDataSyncService.syncDeviceRecordToCloud(device, inMemoryStore.getTransaction());

        verify(cloudApiRestClient).syncColumnInfoToCloudDb(any(UpdateByColumnRequest.class));
        verify(pendingSyncRecordsDbService, times(1)).insertPendingSyncRecord(any(PendingSyncRecord.class));
    }
}
