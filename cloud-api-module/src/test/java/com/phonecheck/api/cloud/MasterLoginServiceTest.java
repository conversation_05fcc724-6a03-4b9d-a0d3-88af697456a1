package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.ServerApiTokenResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MasterLoginServiceTest {
    @Mock
    private PhonecheckApiRestClient phonecheckApiRestClient;

    private MasterLoginService masterLoginService;

    @BeforeEach
    public void setup() {
        masterLoginService = new MasterLoginService(phonecheckApiRestClient);
    }

    @Test
    public void testGetServerApiToken() {
        ServerApiTokenResponse testResponse = new ServerApiTokenResponse();
        testResponse.setToken("test token");

        when(phonecheckApiRestClient.getServerApiToken("user", "pass")).
                thenReturn(testResponse);
        String result = masterLoginService.getServerApiToken("user", "pass");
        Assertions.assertEquals(testResponse.getToken(), result);
    }
}

