package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.cloudapi.EsnLicenseCheckRequest;
import com.phonecheck.model.cloudapi.EsnResponse;
import com.phonecheck.model.service.EsnCheckInfo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CloudEsnCheckServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    private CloudEsnCheckService cloudEsnCheckService;
    @Mock
    private PhonecheckApiRestClient phonecheckApiRestClient;

    @BeforeEach
    public void setup() {
        cloudEsnCheckService = new CloudEsnCheckService(cloudApiRestClient, phonecheckApiRestClient);
    }

    @Test
    public void testGetEsnInfo() {
        EsnResponse.EsnApiResponse esnLicenseCheckResponse = new EsnResponse.EsnApiResponse();
        EsnResponse.EsnApiResponse[] responses = new EsnResponse.EsnApiResponse[]{esnLicenseCheckResponse};

        EsnResponse response = EsnResponse
                .builder()
                .esnApiResults(responses)
                .build();

        when(cloudApiRestClient
                .getEsnLicenseInfo(any())).thenReturn(response);

        EsnCheckInfo result = cloudEsnCheckService.getEsnInfo(new EsnLicenseCheckRequest(), false);
        verify(cloudApiRestClient).getEsnLicenseInfo(any());
        Assertions.assertEquals(esnLicenseCheckResponse, result.getEsnApiResults().get(0));
    }

    @Test
    void testGetEsnBlacklistInfo() {
        String deviceImei = "123456789012345";
        String userToken = "validToken";
        EsnResponse.UsInsuranceBlackListInfo usInsuranceBlacklistApiResponse =
                new EsnResponse.UsInsuranceBlackListInfo();
        EsnResponse esnResponse = EsnResponse.builder()
                .usInsuranceBlackListInfo(usInsuranceBlacklistApiResponse)
                .build();

        when(phonecheckApiRestClient.getEsnUsInsuranceBlackList(deviceImei, userToken)).thenReturn(esnResponse);
        EsnCheckInfo result = cloudEsnCheckService.getEsnBlacklistInfo(deviceImei, userToken);

        assertNotNull(result);
    }
}
