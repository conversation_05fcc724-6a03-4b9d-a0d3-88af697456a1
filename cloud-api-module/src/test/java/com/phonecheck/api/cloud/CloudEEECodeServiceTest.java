package com.phonecheck.api.cloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.EEECodeResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CloudEEECodeServiceTest {

    @Mock
    private CloudApiRestClient cloudApiRestClient;
    private CloudEEECodeService eeeCodeService;
    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        eeeCodeService = new CloudEEECodeService(cloudApiRestClient);
    }

    @Test
    public void testGetEsnInfo() throws JsonProcessingException {

        String input = """
                {
                    "OEM PARTS": [
                        {
                            "Part Name": "Back Camera",
                            "Devices": [
                                {
                                    "Name": "iPhone 12",
                                    "EEE": "RSxrczpTU5Lelv9YbFuvhQ==",
                                    "Exception_keys": []
                                },
                                {
                                    "Name": "iPhone 12 Pro Max",
                                    "EEE": "tSfwTKXeXjAcs2GE91+d2w==",
                                    "Exception_keys": []
                                }
                            ]
                        },
                        {
                            "Part Name": "Battery",
                            "Devices": [
                                {
                                    "Name": "iPhone 11 Pro Max",
                                    "EEE": "0nbN82SUarPD8hBX5o8HAGkH3lau2ktcIBZl0R2A2j8=",
                                    "Exception_keys": []
                                },
                                {
                                    "Name": "iPhone 11 Pro",
                                    "EEE": "pvY3PGyeUe0nISyaMobXaL1Dsoh75nArjPiaGhoaUY4=",
                                    "Exception_keys": []
                                }
                            ]
                        }
                    ]
                }
                """;

        EEECodeResponse response = mapper.readValue(input, EEECodeResponse.class);

        when(cloudApiRestClient.getEEECodes()).thenReturn(response);

        Set<String> resulSet = eeeCodeService.getEEECodeSet();
        verify(cloudApiRestClient).getEEECodes();
        Assertions.assertEquals(12, resulSet.size());
        Assertions.assertTrue(resulSet.contains("MFGR"));
    }
}
