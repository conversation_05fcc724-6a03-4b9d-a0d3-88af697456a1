package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.SyncedCloudDevice;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestClientException;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CloudDeviceServiceTest {

    @Mock
    private CloudApiRestClient cloudApiRestClient;
    private CloudDeviceService cloudDeviceService;

    @BeforeEach
    public void setup() {
        cloudDeviceService = new CloudDeviceService(cloudApiRestClient);
    }

    @Test
    public void testGetGuid() {
        SyncedCloudDevice testResponse =
                new SyncedCloudDevice();
        testResponse.setGuid("guid");

        when(cloudApiRestClient
                .getDeviceBySerial(
                        anyString(), anyInt())).thenReturn(testResponse);
        SyncedCloudDevice result = cloudDeviceService
                .getDeviceBySerial(
                        "serial", 1234);
        Assertions.assertEquals("guid", result.getGuid());
    }

    @Test
    public void testGetDhrUrlThrowRestClientException() {
        when(cloudApiRestClient.getDeviceBySerial("serial", 1234)).
                thenThrow(new RestClientException("RestClientException"));

        SyncedCloudDevice result = cloudDeviceService
                .getDeviceBySerial(
                        "serial", 1234);

        Assertions.assertNull(result);
    }
}
