package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.GetVersionsResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ApiVersionsServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;

    private ApiVersionsService apiVersionsService;

    @BeforeEach
    public void setup() {
        apiVersionsService =
                new ApiVersionsService(cloudApiRestClient);
    }


    @Test
    public void testGetAPIRecordsVersions() {
        GetVersionsResponse.GetVersionsSubResultResponse getVersionsSubResultResponse =
                new GetVersionsResponse.GetVersionsSubResultResponse();
        getVersionsSubResultResponse.setVersion(1.0f);
        getVersionsSubResultResponse.setName("TestName");
        getVersionsSubResultResponse.setUrl("https://testurl.com/");
        getVersionsSubResultResponse.setUpdatedDate(new Timestamp(System.currentTimeMillis()));
        List<GetVersionsResponse.GetVersionsSubResultResponse> list =
                new ArrayList<>();
        list.add(getVersionsSubResultResponse);
        GetVersionsResponse getVersionsResponse = GetVersionsResponse.builder()
                .status(true)
                .result(list)
                .build();

        when(cloudApiRestClient.getApiVersions(any(String.class))).thenReturn(getVersionsResponse);

        Assertions.assertEquals(apiVersionsService.getAPIRecordsVersions(),
                list);

    }
}
