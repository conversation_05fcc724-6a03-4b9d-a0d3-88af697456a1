package com.phonecheck.peo.ios;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.wifi.WifiConfig;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class WifiConfigurationServiceTest {
    @Mock
    private InMemoryStore inMemoryStore;
    @InjectMocks
    private WifiConfigurationService wifiConfigurationService;


    @Test
    public void validWifiConfigTest() {
        CloudCustomizationResponse.WifiSettings wifiSettings = new CloudCustomizationResponse.WifiSettings();
        wifiSettings.setName("ssid");
        wifiSettings.setPassword("pass");
        CloudCustomizationResponse responseConfig = new CloudCustomizationResponse();
        responseConfig.setWifiSettings(wifiSettings);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(responseConfig);

        WifiConfig response = wifiConfigurationService.getWifiConfig();
        Assertions.assertNotNull(response);
        Assertions.assertEquals(responseConfig.getWifiSettings().getName(), response.getSsid());
        Assertions.assertEquals(responseConfig.getWifiSettings().getPassword(), response.getPassword());

        verify(inMemoryStore).getAssignedCloudCustomization();
    }

}
