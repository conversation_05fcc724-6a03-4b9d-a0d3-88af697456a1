package com.phonecheck.peo.ios;

import com.phonecheck.app.profile.ConfigProfileService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.ios.IosConfigProfile;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.MacSupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.model.wifi.WifiConfig;
import com.phonecheck.peo.ios.util.IosPeoUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WifiServiceTest {
    @Mock
    private WifiConfigurationService wifiConfigurationService;
    private WifiService wifiService;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private IosPeoUtil iosPeoUtil;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private ConfigProfileService configProfileService;

    @BeforeEach
    public void setup() {
        wifiService = new WifiService(supportFilePath,
                iosPeoUtil,
                fileUtil,
                wifiConfigurationService,
                configProfileService);
    }

    @Test
    @DisplayName("Push wifi profile when wifi not installed and try with retries")
    void pushWifiProfileWhenWifiIsNotAlreadyInstalledTest() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("123");
        device.setSerial("P69PNQD3QW");
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        WifiConfig wifiConfig = new WifiConfig("ssid",
                "None",
                "pass",
                false);
        File testProfileFile = new File("testFile");

        when(wifiConfigurationService.getWifiConfig()).thenReturn(wifiConfig);
        when(supportFilePath.getPaths()).thenReturn(new MacSupportFilePathsStrategy(false));
        when(fileUtil.createFile(anyString())).thenReturn(testProfileFile);
        when(configProfileService.iosPushProfile(any(IosDevice.class), any(File.class), any(IosConfigProfile.class)))
                .thenReturn(false);

        boolean status = wifiService.pushWifiProfile(device);
        verify(wifiConfigurationService, times(2)).getWifiConfig();
        verify(supportFilePath, times(2)).getPaths();
        verify(configProfileService, times(2))
                .iosPushProfile(any(IosDevice.class), any(File.class), any(IosConfigProfile.class));
        verify(fileUtil, times(2)).createFile(anyString());
        Assertions.assertFalse(status);

        testProfileFile.delete();
    }

    @Test
    @DisplayName("Push wifi profile when wifi not installed and installed during retries")
    void pushWifiProfileWhenWifiGetsInstalledDuringRetriesTest() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("123");
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        device.setSerial("P69PNQD3QW");
        WifiConfig wifiConfig = new WifiConfig("ssid",
                "None",
                "pass",
                false);
        File testProfileFile = new File("testFile");

        when(wifiConfigurationService.getWifiConfig()).thenReturn(wifiConfig);
        when(supportFilePath.getPaths()).thenReturn(new MacSupportFilePathsStrategy(false));
        when(configProfileService.isConfigProfileInstalled(eq(device), eq(IosConfigProfile.WIFI)))
                .thenReturn(false)
                .thenReturn(true);

        when(fileUtil.createFile(anyString())).thenReturn(testProfileFile);
        when(configProfileService.iosPushProfile(device, testProfileFile, IosConfigProfile.WIFI))
                .thenReturn(true);

        boolean status = wifiService.pushWifiProfile(device);

        verify(wifiConfigurationService, times(2)).getWifiConfig();
        verify(supportFilePath, times(2)).getPaths();
        verify(configProfileService, times(2)).iosPushProfile(eq(device),
                any(File.class), eq(IosConfigProfile.WIFI));
        verify(fileUtil, times(2)).createFile(anyString());
        verify(configProfileService, times(2))
                .isConfigProfileInstalled(eq(device), eq(IosConfigProfile.WIFI));
        Assertions.assertTrue(status);

        testProfileFile.delete();
    }

    @Test
    void pushWifiProfileWhenWifiIsAlreadyInstalledTest() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("123");
        device.setSerial("P69PNQD3QW");
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        WifiConfig wifiConfig = new WifiConfig("ssid",
                "None",
                "pass",
                false);
        File testProfileFile = new File("testFile");

        when(wifiConfigurationService.getWifiConfig()).thenReturn(wifiConfig);
        when(supportFilePath.getPaths()).thenReturn(new MacSupportFilePathsStrategy(false));
        when(configProfileService.isConfigProfileInstalled(any(IosDevice.class), any(IosConfigProfile.class)))
                .thenReturn(true);
        when(configProfileService.iosPushProfile(device, testProfileFile, IosConfigProfile.WIFI))
                .thenReturn(true);

        when(fileUtil.createFile(anyString())).thenReturn(testProfileFile);

        wifiService.pushWifiProfile(device);

        verify(wifiConfigurationService).getWifiConfig();
        verify(supportFilePath).getPaths();
        verify(configProfileService, times(1))
                .isConfigProfileInstalled(any(IosDevice.class), any(IosConfigProfile.class));
        verify(configProfileService, times(1)).iosPushProfile(eq(device),
                any(File.class),
                eq(IosConfigProfile.WIFI));
        verify(fileUtil).createFile(anyString());
        Assertions.assertTrue(wifiService.pushWifiProfile(device));

        testProfileFile.delete();
    }

}