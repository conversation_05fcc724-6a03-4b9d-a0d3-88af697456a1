package com.phonecheck.peo.ios;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.wifi.WifiConfig;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class WifiConfigurationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WifiConfigurationService.class);
    private InMemoryStore inMemoryStore;

    /**
     * Provides Wifi Configurations to be used to create Wifi Profile for
     * IOS Device
     *
     * @return WifiConfig object
     */
    public WifiConfig getWifiConfig() {
        CloudCustomizationResponse customizationResponse = inMemoryStore.getAssignedCloudCustomization();

        String ssid = customizationResponse.getWifiSettings() != null ?
                customizationResponse.getWifiSettings().getName() : null;
        String password = customizationResponse.getWifiSettings() != null ?
                customizationResponse.getWifiSettings().getPassword() : null;
        String encryptionType = StringUtils.isBlank(password) ? "Open" : "WPA";

        if (ssid != null && password != null) {
            return WifiConfig.builder()
                    .ssid(ssid)
                    .password(password)
                    .encryptionType(encryptionType)
                    .autoDisconnectWifi(customizationResponse.getWifiSettings().isDisconnect())
                    .build();
        } else {
            LOGGER.error("Cannot set Wifi as Wifi Configuration is empty");
            return null;
        }
    }
}
