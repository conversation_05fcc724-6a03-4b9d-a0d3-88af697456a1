package com.phonecheck.peo.ios;

import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.peo.ios.util.IosPeoUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.phonecheck.model.constants.FileConstants.IOS_FILES_FOLDER;
import static com.phonecheck.model.constants.FileConstants.PREPARE_LIST_FILE_NAME;

@Service
@AllArgsConstructor
public class IosPreparePeoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosPreparePeoService.class);

    private final IosDeviceInfoService iosDeviceInfoService;
    private final IosPeoUtil iosPeoUtil;
    private final LocalizationService localizationService;
    private final SupportFilePath supportFilePath;
    private final InMemoryStore inMemoryStore;

    /**
     * Get the path for prepare list file
     *
     * @return prepare list file path
     */
    public String getPrepareListFilePath() {
        return supportFilePath.getPaths().getRootFolderPath() +
                File.separator + IOS_FILES_FOLDER + File.separator + PREPARE_LIST_FILE_NAME;
    }

    /**
     * Get a list of options to skip during prepare
     * For now, we will create a static list without customizations
     *
     * @return list of options
     */
    private List<String> getPrepareList() {
        // Create a set to store unique setup options to skip
        Set<String> prepareSet = new LinkedHashSet<>();

        // Get workflow configuration from in-memory store
        CloudCustomizationResponse.WorkflowSettings workflow =
                inMemoryStore.getAssignedCloudCustomization().getWorkflow();

        /*
            This field isn't present in the cloud customization response,
            if the selected customization isn't updated after 2025-03-11.
         */
        if (workflow.getSkipSetupAssistant() == null) {
            Map<String, Boolean> skipSetupAssistant = new HashMap<String, Boolean>() {{
                put("AccessibilityAppearance", true);
                put("ActionButton", true);
                put("Android", true);
                put("Appearance", true);
                put("AppleID", true);
                put("AppStore", true);
                put("Avatar", true);
                put("Biometric", true);
                put("CameraButton", true);
                put("CloudStorage", true);
                put("DataSubtitle", true);
                put("DeviceProtection", true);
                put("DeviceToDeviceMigration", true);
                put("Diagnostics", true);
                put("Display", true);
                put("DisplayTone", true);
                put("ExpressLanguage", true);
                put("FileVault", true);
                put("HomeButtonSensitivity", true);
                put("iCloudDiagnostics", true);
                put("iCloudStorage", true);
                put("iMessageAndFaceTime", true);
                put("Intelligence", true);
                put("Keyboard", true);
                put("Key", true);
                put("Language", true);
                put("Location", true);
                put("LockdownMode", true);
                put("MessagingActivationUsingPhoneNumber", true);
                put("Multitasking", true);
                put("OnBoarding", true);
                put("OnBoardingSubtitle", true);
                put("Passcode", true);
                put("Payment", true);
                put("PreferredLanguage", true);
                put("Privacy", true);
                put("PrivacySubtitle", true);
                put("Region", true);
                put("Registration", true);
                put("Restore", true);
                put("RestoreCompleted", true);
                put("Safety", true);
                put("ScreenSaver", true);
                put("ScreenTime", true);
                put("SecuritySubtitle", true);
                put("SIMSetup", true);
                put("Siri", true);
                put("SoftwareUpdate", true);
                put("SpokenLanguage", true);
                put("TapToSetup", true);
                put("TermsOfAddress", true);
                put("TOS", true);
                put("TrueToneDisplay", true);
                put("TVHomeScreenSync", true);
                put("TVProviderSignIn", true);
                put("TVRoom", true);
                put("UnlockWithWatch", true);
                put("UpdateCompleted", true);
                put("Wallpaper", true);
                put("WatchMigration", true);
                put("WebContentFiltering", true);
                put("Welcome", true);
                put("Wifi", true);
                put("Zoom", true);
            }};

            inMemoryStore.getAssignedCloudCustomization().getWorkflow().setSkipSetupAssistant(skipSetupAssistant);
            inMemoryStore.getAssignedCloudCustomization().getWorkflow().setSkipAllSetupAssistant(true);

        }
        if (workflow.isSkipAllSetupAssistant()) {
            // Add all default options plus "All" and "N/A" if skipping everything

            prepareSet.addAll(workflow.getSkipSetupAssistant().keySet());
            prepareSet.add("All");
            prepareSet.add("N/A");
        } else {
            // Process individual skip settings
            Map<String, Boolean> skipConfigs = workflow.getSkipSetupAssistant();

            // Add options that should be skipped
            for (Map.Entry<String, Boolean> entry : skipConfigs.entrySet()) {
                if (Boolean.TRUE.equals(entry.getValue())) {
                    prepareSet.add(entry.getKey());
                }
            }
        }

        return new ArrayList<>(prepareSet);
    }

    /**
     * Write prepare list to the preparelist.txt file
     */
    public void writePrepareList() {
        try {
            final List<String> prepareListOptions = getPrepareList();

            File path = new File(getPrepareListFilePath());
            PrintWriter writer = new PrintWriter(path, StandardCharsets.UTF_8);
            for (int i = 0; i < prepareListOptions.size(); i++) {
                if (i == prepareListOptions.size() - 1) {
                    writer.print(prepareListOptions.get(i));
                } else {
                    writer.println(prepareListOptions.get(i));
                }
            }
            writer.close();
        } catch (IOException e) {
            LOGGER.error("Failed to create preparelist.txt", e);
        }
    }

    /**
     * Prepares an IOS device
     *
     * @param device ios device
     * @return return code to determine the prepare status
     * @throws Exception if failed to run command
     */
    public int prepareDevice(final IosDevice device) throws Exception {
        // if setup is already done return true
        if (iosDeviceInfoService.getDeviceSetupDoneStatus(device) == SetupDoneStatus.DONE) {
            LOGGER.info("No need for prepare attempt as device is already on Home Screen.");

            return 0;
        }

        CloudCustomizationResponse.WorkflowSettings workflowSettings =
                inMemoryStore.getAssignedCloudCustomization().getWorkflow();

        String currentLanguage = inMemoryStore.getCurrentLanguage();
        Pair<String, String> localeLangPair = localizationService.getLocaleLangPair(currentLanguage);

        String language = Boolean.FALSE.equals(workflowSettings.getSkipSetupAssistant().entrySet().stream()
                .filter(e -> "Language".equalsIgnoreCase(e.getKey()))
                .map(Map.Entry::getValue)
                .findFirst().orElse(Boolean.TRUE))
                ? "N" : localeLangPair.getValue();
        String region = Boolean.FALSE.equals(workflowSettings.getSkipSetupAssistant().entrySet().stream()
                .filter(e -> "Region".equalsIgnoreCase(e.getKey()))
                .map(Map.Entry::getValue)
                .findFirst().orElse(Boolean.TRUE))
                ? "N" : localeLangPair.getValue();

        return iosPeoUtil.prepareDevice(
                device,
                getPrepareListFilePath(),
                language,
                region
        );
    }
}
