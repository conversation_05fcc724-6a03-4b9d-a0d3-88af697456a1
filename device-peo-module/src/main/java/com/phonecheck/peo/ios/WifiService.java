package com.phonecheck.peo.ios;

import com.phonecheck.app.profile.ConfigProfileService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.ios.IosConfigProfile;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.model.wifi.WifiConfig;
import com.phonecheck.peo.ios.util.IosPeoUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Objects;

@Service
@AllArgsConstructor
@Getter
@Setter
public class WifiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WifiService.class);
    private static final int WIFI_INSTALL_RETRIES = 2;
    private static final String PAYLOAD_UUID = "venom";
    private static final String PAYLOAD_VERSION = "1";
    private static final String PAYLOAD_IDENTIFIER_PREFIX = "com.phonecheck.WiFi.venom.";
    private static final String WIFI_CONFIG_XML = """
            <?xml version="1.0" encoding="UTF-8"?>
            <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
            <plist version="1.0">
            <dict>
            \t<key>DurationUntilRemoval</key>
            \t<integer>86400</integer>
            \t<key>HasRemovalPasscode</key>
            \t<false/>
            \t<key>PayloadContent</key>
            \t<array>
            \t\t<dict>
            \t\t\t<key>AutoJoin</key>
            \t\t\t<true/>
            \t\t\t<key>CaptiveBypass</key>
            \t\t\t<false/>
            \t\t\t<key>EncryptionType</key>
            \t\t\t<string>%s</string>
            \t\t\t<key>HIDDEN_NETWORK</key>
            \t\t\t<false/>
            \t\t\t<key>IsHotspot</key>
            \t\t\t<false/>
            \t\t\t<key>Password</key>
            \t\t\t<string>%s</string>
            \t\t\t<key>PayloadDescription</key>
            \t\t\t<string>Configures Wi-Fi settings</string>
            \t\t\t<key>PayloadDisplayName</key>
            \t\t\t<string>Wi-Fi</string>
            \t\t\t<key>PayloadIdentifier</key>
            \t\t\t<string>%s</string>
            \t\t\t<key>PayloadType</key>
            \t\t\t<string>com.apple.wifi.managed</string>
            \t\t\t<key>PayloadUUID</key>
            \t\t\t<string>%s</string>
            \t\t\t<key>PayloadVersion</key>
            \t\t\t<integer>%s</integer>
            \t\t\t<key>ProxyType</key>
            \t\t\t<string>None</string>
            \t\t\t<key>SSID_STR</key>
            \t\t\t<string>%s</string>
            \t\t\t<key>ServiceProviderRoamingEnabled</key>
            \t\t\t<false/>
            \t\t</dict>
            \t</array>
            \t<key>PayloadDisplayName</key>
            \t<string>Wifi</string>
            \t<key>PayloadIdentifier</key>
            \t<string>%s</string>
            \t<key>PayloadRemovalDisallowed</key>
            \t<false/>
            \t<key>PayloadType</key>
            \t<string>Configuration</string>
            \t<key>PayloadUUID</key>
            \t<string>%s</string>
            \t<key>PayloadVersion</key>
            \t<integer>%s</integer>
            \t<key>ProfileWasEncrypted</key>
            \t<false/>
            \t<key>PayloadOrganization</key>
            \t<string>Apple</string>
            \t<key>TargetDeviceType</key>
            \t<integer>1</integer>
            </dict>
            </plist>
            """;
    private final SupportFilePath supportFilePath;
    private final IosPeoUtil iosPeoUtil;
    private final FileUtil fileUtil;
    private WifiConfigurationService wifiConfigurationService;
    private ConfigProfileService configProfileService;

    /**
     * Creates Wi-Fi profile
     *
     * @return File "wifiprofile.mobileconfig"
     */
    public File createWifiProfile() {
        WifiConfig wifiConfig = wifiConfigurationService.getWifiConfig();

        if (wifiConfig == null || StringUtils.isBlank(wifiConfig.getSsid())) {
            return null;
        }
        String encryption = "WPA";
        if (wifiConfig.getEncryptionType() != null) {
            if (wifiConfig.getEncryptionType().equals("Open")) {
                encryption = "None";
            }
        }
        String payloadIdentifier = PAYLOAD_IDENTIFIER_PREFIX + wifiConfig.getSsid();
        String xml = WIFI_CONFIG_XML.formatted(encryption, wifiConfig.getPassword(), payloadIdentifier,
                PAYLOAD_UUID, PAYLOAD_VERSION, wifiConfig.getSsid(), payloadIdentifier, PAYLOAD_UUID, PAYLOAD_VERSION);
        try {
            File wiFiProfileFile = getWiFiProfileFile();
            BufferedWriter writer = new BufferedWriter(new FileWriter(wiFiProfileFile, false));
            writer.append(xml);
            writer.close();
            return wiFiProfileFile;
        } catch (IOException e) {
            LOGGER.error("Exception occurred while creating wifi profile file", e);
        }
        return null;
    }

    /**
     * Retrieves the Wi-Fi profile file from the system after creating a file
     *
     * @return Wi-FI profile file
     */
    private File getWiFiProfileFile() throws IOException {
        String wifiProfilePath = supportFilePath.getPaths().getRootFolderPath()
                + "/wifiprofile.mobileconfig";
        return fileUtil.createFile(wifiProfilePath);
    }

    /**
     * Pushes Wi-Fi profile to the target device
     *
     * @param device target device
     * @return true if profile is pushed successfully else false
     */
    public boolean pushWifiProfile(final IosDevice device) {
        boolean pushedProfile = true;
        for (int retryCounter = 0; retryCounter < WIFI_INSTALL_RETRIES; retryCounter++) {
            File wifiProfile = createWifiProfile();
            if (Objects.nonNull(wifiProfile)) {
                // Push profile to device
                if (Boolean.TRUE.equals(configProfileService.iosPushProfile(device,
                        wifiProfile,
                        IosConfigProfile.WIFI))) {
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        // do nothing
                    }
                    // Check if profile installed or not only if setup is not done
                    if (SetupDoneStatus.NOT_DONE.equals(device.getSetupDoneStatus())) {
                        if (isWifiProfileInstalled(device)) {
                            break;
                        }
                    } else {
                        break;
                    }
                } else {
                    LOGGER.info("Push wifi profile failed in attempt #{}", retryCounter + 1);
                    pushedProfile = false;
                }
            } else {
                LOGGER.warn("Wifi configuration not set for the session! Won't push wifi profile.");
            }
        }
        return pushedProfile;
    }

    /**
     * Checks whether the Wi-Fi configuration profile is installed on the specified iOS device.
     * This method retries the check for Wi-Fi profile installation with a delay of 1 second
     *
     * @param device iOS device
     * @return status
     */
    private boolean isWifiProfileInstalled(final IosDevice device) {
        return Boolean.TRUE.equals(configProfileService.isConfigProfileInstalled(device, IosConfigProfile.WIFI));
    }
}
