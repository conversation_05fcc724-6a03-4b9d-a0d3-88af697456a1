package com.phonecheck.peo.ios.util;

import com.phonecheck.command.device.ios.peo.IosPreparePeoCommand;
import com.phonecheck.command.device.ios.peo.IosPushFilesCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.ios.peo.IosPeoResponseParser;
import com.phonecheck.parser.device.ios.peo.IosPushFilesParser;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;

@Service
@AllArgsConstructor
public class IosPeoUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosPeoUtil.class);
    private final CommandExecutor executor;
    private final IosPeoResponseParser iosPeoResponseParser;
    private final IosPushFilesParser iosPushFilesParser;
    private final TimerLoggerUtil timerLoggerUtil;

    /**
     * Checks if prepared for PEO
     *
     * @param device          target device
     * @param prepareListPath file path for prepareList
     * @param locale          locale
     * @param lang            language
     * @return true/false
     * @throws IOException when the output couldn't be read
     */
    public int prepareDevice(final IosDevice device, final String prepareListPath,
                             final String locale, final String lang) throws Exception {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Device preparing exe (ideviceconfiguration) start", exeStartTime);
        LOGGER.info("Calling prepare command for device");
        final String output = executor.execute(new IosPreparePeoCommand(device.getId(), prepareListPath, locale, lang));
        if (output != null) {
            timerLoggerUtil.printTimerLog(device.getId(),
                    "Device preparing exe (ideviceconfiguration) end", exeStartTime);
            timerLoggerUtil.printTimerLog(device.getId(), output.trim(), exeStartTime);
            return iosPeoResponseParser.parse(output);
        }
        // code only will reach this point if peo command returns no device found even after retries.
        return -6;
    }

    /**
     * Pushes multiple files to the target IOS device.
     *
     * @param device   target device.
     * @param bundleId app bundle identifier
     * @param files    multiple files path appended together.
     * @return status of push files.
     * @throws IOException when the output couldn't be read
     */
    public String iosPushFiles(final IosDevice device, final String bundleId, final String files)
            throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(), "Push files exe (idevicefuse) start", exeStartTime);
        final String output = executor.execute(new IosPushFilesCommand(device.getId(), bundleId, files), 20);
        if (output != null) {
            timerLoggerUtil.printTimerLog(device.getId(), "Push files exe (idevicefuse) end", exeStartTime);
            timerLoggerUtil.printTimerLog(device.getId(), output.trim(), exeStartTime);

            return iosPushFilesParser.parse(output);
        } else {
            return null;
        }
    }
}
