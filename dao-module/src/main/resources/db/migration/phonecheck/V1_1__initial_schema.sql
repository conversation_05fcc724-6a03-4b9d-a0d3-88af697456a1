CREATE TABLE IF NOT EXISTS "TBLDeviceInfo" (
    "TransactionID" varchar(1000) DEFAULT NULL,
    "Model" varchar(1000) DEFAULT NULL,
    "Memory" varchar(100) DEFAULT NULL,
    "IMEI" varchar(100) DEFAULT NULL,
    "IMEI2" varchar(100) DEFAULT NULL,
    "MEID" varchar(100) DEFAULT NULL,
    "MEID2" varchar(100) DEFAULT NULL,
    "DecimalMEID" varchar(100) DEFAULT NULL,
    "DecimalMEID2" varchar(100) DEFAULT NULL,
    "PESN" varchar(100) DEFAULT NULL,
    "PESN2" varchar(100) DEFAULT NULL,
    "EID" varchar(100) DEFAULT NULL,
    "Carrier" varchar(200) DEFAULT NULL,
    "Serial" varchar(100) DEFAULT NULL,
    "UDID" varchar(100) DEFAULT NULL,
    "LicenseIdentifier" varchar(100) DEFAULT NULL,
    "DeviceLock" varchar(100) DEFAULT NULL,
    "AppleID" varchar(100) DEFAULT NULL,
    "Rooted" varchar(100) DEFAULT NULL,
    "Color" varchar(100) DEFAULT NULL,
    "Grade" varchar(100) DEFAULT NULL,
    "Version" varchar(100) DEFAULT NULL,
    "OS" varchar(100) DEFAULT NULL,
    "Make" varchar(100) DEFAULT NULL,
    "Firmware" varchar(100) DEFAULT NULL,
    "Notes" text DEFAULT NULL,
    "ESN" varchar(100) DEFAULT NULL,
    "PhoneCheckID" varchar(100) DEFAULT NULL,
    "LicenseID" varchar(100) DEFAULT NULL,
    "IsAppInstall" varchar(1000) DEFAULT NULL,
    "DeviceCreatedDate" varchar(1000) DEFAULT NULL,
    "DeviceUpdatedDate" varchar(1000) DEFAULT NULL,
    "BatteryDegraded" boolean DEFAULT 0,
    "BatteryCycle" varchar(100) DEFAULT NULL,
    "BatteryHealthPercentage" varchar(100) DEFAULT NULL,
    "BatteryPercentage" varchar(100) DEFAULT NULL,
    "BatteryCurrentMaxCapacity" varchar(100) DEFAULT NULL,
    "BatteryDesignMaxCapacity" varchar(100) DEFAULT NULL,
    "BatterySerial" varchar(100) DEFAULT NULL,
    "BatteryModel" varchar(100) DEFAULT NULL,
    "BatterySource" varchar(100) DEFAULT NULL,
    "BatteryTemperature" varchar(100) DEFAULT NULL,
    "AvgBatteryTemperature" varchar(100) DEFAULT NULL,
    "MaxBatteryTemperature" varchar(100) DEFAULT NULL,
    "MinBatteryTemperature" varchar(100) DEFAULT NULL,
    "BatteryResistance" varchar(100) DEFAULT NULL,
    "Model#" varchar(100) DEFAULT NULL,
    "UnlockStatus" varchar(100) DEFAULT NULL,
    "Network" varchar(100) DEFAULT NULL,
    "Network1" varchar(100) DEFAULT NULL,
    "Network2" varchar(100) DEFAULT NULL,
    "SIM1MCC" varchar(100) DEFAULT NULL,
    "SIM1MNC" varchar(100) DEFAULT NULL,
    "SIM2MCC" varchar(100) DEFAULT NULL,
    "SIM2MNC" varchar(100) DEFAULT NULL,
    "SIM1Name" varchar(100) DEFAULT NULL,
    "SIM2Name" varchar(100) DEFAULT NULL,
    "SimTechnology" varchar(100) DEFAULT NULL,
    "TesterName" varchar(100) DEFAULT NULL,
    "Cosmetics" text DEFAULT NULL,
    "ESNResponse" text DEFAULT NULL,
    "LPN" text DEFAULT NULL,
    "Custom1" text DEFAULT NULL,
    "SKUCode" text DEFAULT NULL,
    "ManualEntry" text DEFAULT NULL,
    "BuildNo" text DEFAULT NULL,
    "CosmeticsFailed" text DEFAULT NULL,
    "CosmeticsPassed" text DEFAULT NULL,
    "CosmeticsPending" text DEFAULT NULL,
    "CosmeticsWorking" varchar(100) DEFAULT NULL,
    "RegulatoryModelNumber" varchar(100) DEFAULT NULL,
    "SIMSERIAL" varchar(100) DEFAULT NULL,
    "MDM" varchar(100) DEFAULT NULL,
    "MDMResponse" text NULL,
    "MDMUnRemovable" varchar(100) NULL,
    "SIMSERIAL2" varchar(100) DEFAULT NULL,
    "CountryOfOrigin" varchar(1000) NULL,
    "RestoreCode" varchar(100) DEFAULT NULL,
    "BatteryDrainDuration" varchar(100) NULL,
    "BatteryChargeStart" varchar(100) NULL,
    "BatterChargeEnd" varchar(100) NULL,
    "BatteryDrain" varchar(100) NULL,
    "WifiMacAddress" varchar(100) NULL,
    "SimHistory" varchar(5000) NULL,
    "iCloudInfo" varchar(5000) NULL,
    "CompatibleSim" varchar(5000) NULL,
    "NotCompatibleSim" varchar(5000) NULL,
    "ESimPresent" boolean DEFAULT 0,
    "DeviceState" varchar(100) NULL,
    "BatteryDrainType" varchar(100) NULL,
    "BatteryDrainInfo" varchar(1000) NULL,
    "PCCarrier" varchar(100) NULL,
    "CocoCurrentCapacity" varchar(100) NULL,
    "CocoDesignCapacity" varchar(100) NULL,
    "OEMBatteryHealth" varchar(100) NULL,
    "CocoBatteryHealth" varchar(100) NULL,
    "PortNumber" varchar(100) NULL,
    "startHeat" varchar(100) NULL,
    "endHeat" varchar(100) NULL,
    "ProductCode" varchar(100) NULL,
    "BMic" varchar(100) NULL,
    "VMic" varchar(100) NULL,
    "FMic" varchar(100) NULL,
    "gradePerformed" varchar(100) DEFAULT NULL,
    "SimCardDeviceStatePrint" varchar(100) DEFAULT NULL,
    "SimCardDeviceStateErase" varchar(100) DEFAULT NULL,
    "gradingState" text NULL,
    "SimLock" varchar(100) NULL,
    "SimLockResponse" text DEFAULT NULL,
    "serialImeiMismatched" varchar(100) DEFAULT NULL,
    "sourceApiResponse1" text NULL,
    "sourceApiResponse2" text NULL,
    "PackageName" text NULL,
    "TestPlanName" varchar(100) NULL,
    "grade_profile_id" varchar(100) DEFAULT NULL,
    "grade_route_id" varchar(100) DEFAULT NULL,
    "Knox" text NULL,
    "ScreenTime" varchar(100) NULL,
    "gradingAnswer" text NULL,
    "BatteryHealthGrade" text NULL,
    "FCCID" text NULL,
    "OEMPart" text NULL,
    "Ram" varchar(100) DEFAULT NULL,
    "OEMStatus" varchar(100) DEFAULT NULL,
    "IFT_Codes" varchar(100) DEFAULT NULL,
    "CarrierLockResponse" text DEFAULT NULL,
    "transaction_type" varchar(100) DEFAULT NULL,
    "final_price" text DEFAULT NULL,
    "eBayRefurbished" varchar(255) DEFAULT NULL,
    "eBayRejection" text DEFAULT NULL,
    "Deleted" boolean DEFAULT 0
);

CREATE TABLE IF NOT EXISTS "TBLDeviceCounts" (
	"TransactionID" VARCHAR(1000) DEFAULT NULL,
	"LicenseID" VARCHAR(100) DEFAULT NULL,
	"Serial" VARCHAR(100) DEFAULT NULL,
	"DeviceCreatedDate" VARCHAR(1000) DEFAULT NULL,
	"DeviceUpdatedDate" VARCHAR(1000) DEFAULT NULL,
	"Working" VARCHAR(2000) DEFAULT NULL,
	"Passed" CLOB DEFAULT NULL,
	"Failed" CLOB DEFAULT NULL,
	"Pending" CLOB DEFAULT NULL,
	"DefectsCode" CLOB DEFAULT NULL,
	"ManualFailure" CLOB DEFAULT NULL,
	"Carrier" CLOB DEFAULT NULL,
	"IMEI" CLOB DEFAULT NULL,
	"SimErased" CLOB DEFAULT NULL,
	"Color" CLOB DEFAULT NULL,
	"Grade" CLOB DEFAULT NULL,
	"Erased" VARCHAR(20) DEFAULT NULL,
	"StartTime" VARCHAR(1000) DEFAULT NULL,
	"EndTime" VARCHAR(1000) DEFAULT NULL,
	"TotalTime" VARCHAR(1000) DEFAULT NULL,
	"OS" VARCHAR(100) DEFAULT NULL,
	"Pre_check_working" VARCHAR(2000) DEFAULT NULL,
	"Pre_check_Passed" CLOB DEFAULT NULL,
	"Pre_check_Failed" CLOB DEFAULT NULL,
	"Pre_check_Pending" CLOB DEFAULT NULL,
	"Deleted" BOOLEAN DEFAULT 0
);

CREATE TABLE IF NOT EXISTS "TBLDeviceColor" (
    "color_id" VARCHAR(1000) DEFAULT NULL,
    "make" VARCHAR(3000) DEFAULT NULL,
    "device_title" VARCHAR(3000) DEFAULT NULL,
    "imei" VARCHAR(1000) DEFAULT NULL,
    "serial_number" VARCHAR(1000) DEFAULT NULL,
    "model_number" VARCHAR(1000) DEFAULT NULL,
    "model_name" VARCHAR(1000) DEFAULT NULL,
    "color" VARCHAR(1000) DEFAULT NULL,
    "color_code" VARCHAR(1000) DEFAULT NULL,
    "Deleted" BOOLEAN DEFAULT 0
    );

CREATE TABLE IF NOT EXISTS "TBLSimTechnology" (
    "ModelId" varchar(3000) DEFAULT NULL,
    "Model" varchar(1000) DEFAULT NULL,
    "RegulatoryModel" varchar(1000) DEFAULT NULL,
    "Type" varchar(1000) DEFAULT NULL,
    "CountryOfOrigin" varchar(1000) DEFAULT NULL,
    "created_on" varchar(1000) DEFAULT NULL,
    "updated_on" varchar(1000) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS "TBLModels" (
    "product_type" varchar(3000) DEFAULT NULL,
    "model_name" varchar(1000) DEFAULT NULL,
    "cpid" varchar(1000) DEFAULT NULL,
    "bdid" varchar(1000) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS "TBLSimCarrier" (
    "id" VARCHAR(1000) DEFAULT NULL,
    "Country_Code" VARCHAR(1000) DEFAULT NULL,
    "Network_Code" VARCHAR(1000) DEFAULT NULL,
    "Status" VARCHAR(1000) DEFAULT NULL,
    "Communications_Provider" VARCHAR(1000) DEFAULT NULL,
    "Deleted" BOOLEAN DEFAULT 0
);

CREATE TABLE IF NOT EXISTS "TBLCarrier" (
    "id" VARCHAR(1000) DEFAULT NULL,
    "AppleSubFamily" VARCHAR(1000) DEFAULT NULL,
    "Gb" VARCHAR(1000) DEFAULT NULL,
    "ModelNo" VARCHAR(1000) DEFAULT NULL,
    "Carrier" VARCHAR(1000) DEFAULT NULL,
    "Color" VARCHAR(1000) DEFAULT NULL,
    "Rules" VARCHAR(1000) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS "TBLCarrierFilter" (
    "id" VARCHAR(1000) DEFAULT NULL,
    "carrier" VARCHAR(1000) DEFAULT NULL,
    "resolved_carrier" VARCHAR(1000) DEFAULT NULL,
    "Deleted" BOOLEAN DEFAULT 0
);


CREATE TABLE IF NOT EXISTS "TBLVendorMain" (
    "VendorName" VARCHAR(1000) DEFAULT NULL,
    "InvoiceNo" VARCHAR(1000) DEFAULT NULL,
    "TransactionDate" VARCHAR(1000) DEFAULT NULL,
    "TransactionID" VARCHAR(1000) DEFAULT NULL,
    "LicenseID" VARCHAR(1000) DEFAULT NULL,
    "StationID" VARCHAR(1000) DEFAULT NULL,
    "BoxNo" VARCHAR(1000) DEFAULT NULL,
    "QTY" VARCHAR(1000) DEFAULT NULL,
    "Deleted" BOOLEAN DEFAULT 0
    );

CREATE TABLE IF NOT EXISTS "TBLLastVendorDetail" (
    "LicenseID" VARCHAR(1000) DEFAULT NULL,
    "TransactionID" VARCHAR(1000) DEFAULT NULL,
    "TransactionStartTime" VARCHAR(1000) DEFAULT NULL,
    "Deleted" BOOLEAN DEFAULT 0
    );

CREATE TABLE IF NOT EXISTS "TBLEraserInfo" (
	"TransactionID" VARCHAR(1000) DEFAULT NULL,
	"LicenseID" VARCHAR(100) DEFAULT NULL,
	"Serial" VARCHAR(100) DEFAULT NULL,
	"Type" VARCHAR(100) DEFAULT NULL,
	"DeviceState" varchar(100) NULL,
	"Carrier" varchar(200) DEFAULT NULL,
	"IMEI" varchar(100) DEFAULT NULL,
	"SimErased" BOOLEAN DEFAULT 0,
	"ESimErased" BOOLEAN DEFAULT 0,
	"Color" varchar(100) DEFAULT NULL,
	"Grade" varchar(100) DEFAULT NULL,
	"Erased" BOOLEAN DEFAULT 0,
	"StartTime" VARCHAR(1000) DEFAULT NULL,
	"EndTime" VARCHAR(1000) DEFAULT NULL,
	"TotalTime" VARCHAR(1000) DEFAULT NULL,
	"Deleted" BOOLEAN DEFAULT 0
);

CREATE TABLE IF NOT EXISTS "TBLApiRecordsVersions" (
	"Name" VARCHAR(100) DEFAULT NULL,
	"URL" VARCHAR(1000) DEFAULT NULL,
	"Version" FLOAT DEFAULT NULL,
	"UpdatedDate" TIMESTAMP DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS "TBLFaceIdKeys" (
	"KeyType" VARCHAR(100) DEFAULT NULL,
	"KeyName" VARCHAR(1000) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS "LocalCustomizations" (
	"userId" VARCHAR(50) PRIMARY KEY,
	"enableSimLockCheck" BOOLEAN DEFAULT 0,
	"enableCarrierLockCheck" BOOLEAN DEFAULT 0,
	"enableESNCheck" BOOLEAN DEFAULT 0,
	"enableESNCheckAll" BOOLEAN DEFAULT 0,
	"enableWorkflowOnFullyFunctional" BOOLEAN DEFAULT 0,
	"enableWorkflowOnFailedTests" BOOLEAN DEFAULT 0,
	"defaultLabel" VARCHAR(256) DEFAULT NULL,
	"defaultPrinter" VARCHAR(256) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS "EEECodes" (
    "eeeCode" VARCHAR(100) DEFAULT NULL
);