package com.phonecheck.dao.jpa;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import javax.sql.DataSource;
import java.util.Properties;


/**
 * This configuration class is used for configuring JPA repositories and entities scanning.
 * It enables the scanning of JPA repositories in the "com.phonecheck.dao.jpa" package
 * and entity classes in the "com.phonecheck.dao.jpa.entities" package.
 * We can provide additional configuration settings or beans specific to JPA here.
 * We import this class in backend-module
 */

@Configuration
@EnableJpaRepositories("com.phonecheck.dao.jpa.repository")
@EntityScan("com.phonecheck.dao.jpa.entities")
public class JpaConfiguration {
    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier("phonecheckDataSource") final DataSource dataSource
    ) {
        LocalContainerEntityManagerFactoryBean managerFactoryBean = new LocalContainerEntityManagerFactoryBean();
        managerFactoryBean.setDataSource(dataSource);
        managerFactoryBean.setPackagesToScan("com.phonecheck.dao.jpa.entities");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        managerFactoryBean.setJpaVendorAdapter(vendorAdapter);

        Properties properties = new Properties();
        properties.setProperty("hibernate.show_sql", "true");
        properties.setProperty("hibernate.hbm2ddl.auto", "create");
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.H2Dialect");

        managerFactoryBean.setJpaProperties(properties);

        return managerFactoryBean;
    }
}
