package com.phonecheck.dao;

import com.phonecheck.dao.mapper.TblLastVendorDetailRowMapper;
import com.phonecheck.dao.model.TblLastVendorDetail;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TblLastVendorDetailDao extends AbstractDao {
    private static final int NO_OF_COLUMNS = 4;
    private static final String DELETE_LAST_VENDOR_DETAIL_SQL = "DELETE FROM \"TBLLastVendorDetail\" " +
            "WHERE \"LicenseID\" = ?";
    private static final String INSERT_LAST_VENDOR_DETAIL_SQL = appendQuestionMarkToSql(
            new StringBuilder("INSERT INTO \"TBLLastVendorDetail\" VALUES ("),
            NO_OF_COLUMNS);

    private static final TblLastVendorDetailRowMapper ROW_MAPPER = new TblLastVendorDetailRowMapper();

    private static final String SELECT_LAST_VENDOR_RECORD_BY_LICENSE_ID_SQL = "SELECT * FROM " +
            "\"TBLLastVendorDetail\"" +
            " WHERE \"LicenseID\" = ?";

    public TblLastVendorDetailDao(@Qualifier("phonecheckJdbcTemplate") final JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * Get last vendor record from database by licenseId
     *
     * @param licenseId license id
     * @return TblLastVendorDetail
     */
    public TblLastVendorDetail getLastVendorRecordByLicenseId(final Integer licenseId) {
        List<TblLastVendorDetail> lastVendorDetails = getJdbcTemplate()
                .query(
                        SELECT_LAST_VENDOR_RECORD_BY_LICENSE_ID_SQL,
                        ROW_MAPPER, String.valueOf(licenseId)
                );

        if (lastVendorDetails.size() > 0) {
            return lastVendorDetails.get(0);
        }

        return null;
    }

    /**
     * Insert data into TBLLastVendorDetail
     *
     * @param tblLastVendorDetail
     */
    public synchronized  void insert(final TblLastVendorDetail tblLastVendorDetail) {
        getJdbcTemplate().update(INSERT_LAST_VENDOR_DETAIL_SQL,
                ps -> {
                    int index = 0;
                    ps.setString(++index, tblLastVendorDetail.getLicenseID());
                    ps.setString(++index, tblLastVendorDetail.getTransactionId());
                    ps.setString(++index, tblLastVendorDetail.getTransactionStartTime());
                    ps.setBoolean(++index, tblLastVendorDetail.isDeleted());
                });
    }

    /**
     * Delete transaction from last vendor detail
     *
     * @param licenseId to be deleted
     */
    public void deleteTransaction(final String licenseId) {
        getJdbcTemplate().update(DELETE_LAST_VENDOR_DETAIL_SQL, licenseId);
    }
}