package com.phonecheck.dao;

import com.phonecheck.dao.mapper.TblApiRecordsVersionsMapper;
import com.phonecheck.dao.model.TblApiRecordsVersions;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Component
public class TblApiRecordsVersionsDao extends AbstractDao {
    private static final int NO_OF_COLUMNS = 4;
    private static final TblApiRecordsVersionsMapper ROW_MAPPER = new TblApiRecordsVersionsMapper();
    private static final String INSERT_API_RECORDS_VERSIONS_QUERY =
            appendQuestionMarkToSql(
                    new StringBuilder("INSERT INTO \"TBLApiRecordsVersions\" VALUES ("),
                    NO_OF_COLUMNS);
    private static final String TRUNCATE_FACE_ID_KEYS_TABLE_QUERY = "TRUNCATE TABLE \"TBLApiRecordsVersions\"";
    private static final String SELECT_API_RECORDS_VERSIONS_QUERY =
            "SELECT * from \"TBLApiRecordsVersions\" WHERE \"Name\" = ?";

    public TblApiRecordsVersionsDao(@Qualifier("phonecheckJdbcTemplate") final JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * Truncate table TblAPIRecordsVersions
     */
    public void truncate() {
        getJdbcTemplate().update(TRUNCATE_FACE_ID_KEYS_TABLE_QUERY);
    }

    /**
     * Retrieve/Get data from TblApiRecordsVersions
     *
     * @param apiName String API Name
     * @return resultList list containing version record for given api name
     */
    public List<TblApiRecordsVersions> getApiRecordsVersions(final String apiName) {
        return getJdbcTemplate()
                .query(SELECT_API_RECORDS_VERSIONS_QUERY, ROW_MAPPER, apiName);
    }

    /**
     * Insert data into TblAPIRecordsVersions
     *
     * @param tblAPIRecordsVersions TblApiRecordsVersions
     */
    public void saveApiRecordVersions(final List<TblApiRecordsVersions> tblAPIRecordsVersions) {
        // Use Batch update to mass insert the data
        getJdbcTemplate().batchUpdate(INSERT_API_RECORDS_VERSIONS_QUERY,
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(final PreparedStatement ps, final int i) throws SQLException {
                        int index = 0;
                        ps.setString(++index, tblAPIRecordsVersions.get(i).getName());
                        ps.setString(++index, tblAPIRecordsVersions.get(i).getUrl());
                        ps.setFloat(++index, tblAPIRecordsVersions.get(i).getVersion());
                        ps.setTimestamp(++index, tblAPIRecordsVersions.get(i).getUpdatedDate());
                    }

                    public int getBatchSize() {
                        return tblAPIRecordsVersions.size();
                    }
                });
    }
}
