package com.phonecheck.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entity class for the table TblFirmwareInfo
 * This table stores firmware key MD5 hash od ipsw file and
 * MD5 hash of extracted folder
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TblFirmwareInfo {
    private String firmwareId;
    private String fileHash;
    private String folderHash;
    private String fileName;
    private String version;
}
