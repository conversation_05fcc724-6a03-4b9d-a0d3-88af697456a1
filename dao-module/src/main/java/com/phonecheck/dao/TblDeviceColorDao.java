package com.phonecheck.dao;

import com.phonecheck.dao.mapper.TblDeviceColorRowMapper;
import com.phonecheck.dao.model.TblDeviceColor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Component
public class TblDeviceColorDao extends AbstractDao {
    // TblDeviceColor has 10 columns
    private static final int NO_OF_COLUMNS = 10;
    private static final TblDeviceColorRowMapper ROW_MAPPER = new TblDeviceColorRowMapper();
    private static final String SELECT_DEVICE_ALL_POSSIBLE_COLOR_SQL =
            "SELECT DISTINCT \"color\" FROM \"TBLDeviceColor\" WHERE \"device_title\" = ? ";
    private static final String SELECT_DEVICE_COLOR_BY_MODEL_NO_SQL =
            "SELECT * FROM \"TBLDeviceColor\" WHERE \"model_number\" = ? ";
    private static final String SELECT_LIKE_DEVICE_COLOR_SQL =
            "SELECT * FROM \"TBLDeviceColor\" WHERE \"model_number\" LIKE ? ";
    // Query to delete all the data from TBLDeviceColor
    private static final String TRUNCATE_DEVICE_COLOR_SQL = "TRUNCATE TABLE \"TBLDeviceColor\"";
    private static final String SELECT_COLOR_BY_MODEL_NO_AND_COLOR_CODE_SQL =
            "SELECT * FROM \"TBLDeviceColor\" WHERE \"model_number\" = ? AND \"color_code\" = ?";
    private static final String COUNT_RECORDS_SQL =
            "SELECT COUNT(*) FROM \"TBLDeviceColor\"";

    // Query to insert device color data into TBLDeviceColor
    private static final String INSERT_DEVICE_COLOR_SQL = appendQuestionMarkToSql(
            new StringBuilder("INSERT INTO \"TBLDeviceColor\" VALUES ("),
            NO_OF_COLUMNS);

    public TblDeviceColorDao(@Qualifier("phonecheckJdbcTemplate") final JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * Get possible Device Colors
     *
     * @param deviceTitle Device Title
     * @return TblDeviceColor object
     */
    public List<String> getAllPossibleColorsOfDevice(final String deviceTitle) {
        return getJdbcTemplate()
                .query(SELECT_DEVICE_ALL_POSSIBLE_COLOR_SQL,
                        (rs, rowNum) -> rs.getString("color"),
                        deviceTitle);
    }

    /**
     * Get possible Device Colors
     *
     * @param modelNo model number
     * @return TblDeviceColor object
     */
    public List<String> getAllPossibleColorsByModelNumber(final String modelNo) {
        return getJdbcTemplate()
                .query(SELECT_DEVICE_COLOR_BY_MODEL_NO_SQL,
                        (rs, rowNum) -> rs.getString("color"),
                        modelNo);
    }

    /**
     * Get exact Device Color based on the following params
     *
     * @param modelNumber Device model number
     * @return TblDeviceColor object
     */
    public TblDeviceColor getExactDeviceColorFromDB(final String modelNumber) {

        List<TblDeviceColor> resultList = getJdbcTemplate()
                .query(SELECT_DEVICE_COLOR_BY_MODEL_NO_SQL, ROW_MAPPER, modelNumber);

        if (resultList.size() >= 1) {
            return resultList.get(0);
        }
        return null;
    }

    /**
     * Get similar Device Color Object based on the following params
     *
     * @param modelNumber Device model number
     * @return TblDeviceColor object
     */
    public TblDeviceColor getLikeDeviceColorFromDB(final String modelNumber) {

        List<TblDeviceColor> resultList = getJdbcTemplate()
                .query(SELECT_LIKE_DEVICE_COLOR_SQL, ROW_MAPPER, "%" + modelNumber + "%");

        if (resultList.size() >= 1) {
            return resultList.get(0);
        }
        return null;
    }

    /**
     * Mass insert data into TblDeviceColor
     *
     * @param tblDeviceColorList
     */
    public void insertDataIntoTblDeviceColor(final List<TblDeviceColor> tblDeviceColorList) {
        // Use Batch update to mass insert the data
        getJdbcTemplate().batchUpdate(
                INSERT_DEVICE_COLOR_SQL,
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(final PreparedStatement ps, final int i) throws SQLException {
                        int index = 0;
                        ps.setString(++index, tblDeviceColorList.get(i).getColorId());
                        ps.setString(++index, tblDeviceColorList.get(i).getMake());
                        ps.setString(++index, tblDeviceColorList.get(i).getDeviceTitle());
                        ps.setString(++index, tblDeviceColorList.get(i).getImei());
                        ps.setString(++index, tblDeviceColorList.get(i).getSerialNumber());
                        ps.setString(++index, tblDeviceColorList.get(i).getModelNumber());
                        ps.setString(++index, tblDeviceColorList.get(i).getModelName());
                        ps.setString(++index, tblDeviceColorList.get(i).getColor());
                        ps.setString(++index, tblDeviceColorList.get(i).getColorCode());
                        ps.setBoolean(++index, tblDeviceColorList.get(i).isDeleted());
                    }

                    public int getBatchSize() {
                        return tblDeviceColorList.size();
                    }
                });
    }

    /**
     * Count the tblDeviceColor records
     *
     * @return count
     */
    public int getRecordCount() {
        Integer recordCount = getJdbcTemplate().queryForObject(
                COUNT_RECORDS_SQL, Integer.class);
        return recordCount != null ? recordCount : 0;
    }

    /**
     * Delete everything from the tblDeviceColor
     */
    public void deleteDataFromTblDeviceColor() {
        getJdbcTemplate().update(TRUNCATE_DEVICE_COLOR_SQL);
    }

    /**
     * Get color for the android devie by model no and color code.
     *
     * @param modelNo model number
     * @param colorCode color code
     * @return List color matching given criteria
     */
    public List<String> getColorByModelNumberAndColorCode(final String modelNo, final String colorCode) {
        return getJdbcTemplate()
                .query(SELECT_COLOR_BY_MODEL_NO_AND_COLOR_CODE_SQL,
                        (rs, rowNum) -> rs.getString("color"),
                        modelNo, colorCode);
    }
}