package com.phonecheck.dao.mapper;

import com.phonecheck.dao.model.TblCarrierFilter;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class TblCarrierFilterRowMapper implements RowMapper<TblCarrierFilter> {

    @Override
    public TblCarrierFilter mapRow(final ResultSet resultSet, final int rowNum) throws SQLException {
        return TblCarrierFilter.builder()
                .id(resultSet.getString("id"))
                .carrier(resultSet.getString("carrier"))
                .resolvedCarrier(resultSet.getString("resolved_carrier"))
                .deleted(resultSet.getBoolean("Deleted"))
                .build();
    }
}
