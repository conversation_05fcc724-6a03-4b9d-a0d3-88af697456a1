package com.phonecheck.dao.mapper;

import com.phonecheck.dao.model.TblLastVendorDetail;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class TblLastVendorDetailRowMapper implements RowMapper<TblLastVendorDetail> {
    @Override
    public TblLastVendorDetail mapRow(final ResultSet resultSet, final int rowNum) throws SQLException {
        return TblLastVendorDetail.builder()
                .transactionId(resultSet.getString("transactionId"))
                .licenseID(resultSet.getString("LicenseID"))
                .deleted(resultSet.getBoolean("Deleted"))
                .transactionStartTime(resultSet.getString("TransactionStartTime"))
                .build();
    }
}

