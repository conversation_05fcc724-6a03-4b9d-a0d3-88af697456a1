package com.phonecheck.dao.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.model.TblDeviceInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class TblDeviceInfoRowMapper implements RowMapper<TblDeviceInfo> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TblDeviceInfoRowMapper.class);
    private final ObjectMapper mapper;

    public TblDeviceInfoRowMapper(final ObjectMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public TblDeviceInfo mapRow(final ResultSet resultSet, final int rowNum) throws SQLException {
        List<String> failedCosmetics = null;
        List<String> passedCosmetics = null;
        try {
            if (resultSet.getString("CosmeticsFailed") != null) {
                failedCosmetics = mapper.readValue(resultSet.getString("CosmeticsFailed"), List.class);
            }
            if (resultSet.getString("CosmeticsPassed") != null) {
                passedCosmetics = mapper.readValue(resultSet.getString("CosmeticsPassed"), List.class);
            }
        } catch (JsonProcessingException e) {
            LOGGER.error("Error while mapping Cosmetics from the db to object", e);
        }


        return TblDeviceInfo.builder()
                .transactionId(resultSet.getString("TransactionID"))
                .model(resultSet.getString("Model"))
                .memory(resultSet.getLong("Memory"))
                .memoryUnit(resultSet.getString("memoryUnit"))
                .imei(resultSet.getString("IMEI"))
                .imei2(resultSet.getString("IMEI2"))
                .meid(resultSet.getString("MEID"))
                .meid2(resultSet.getString("MEID2"))
                .decimalMeid(resultSet.getString("DecimalMEID"))
                .decimalMeid2(resultSet.getString("DecimalMEID2"))
                .pesn(resultSet.getString("PESN"))
                .pesn2(resultSet.getString("PESN2"))
                .eid(resultSet.getString("EID"))
                .carrier(resultSet.getString("Carrier"))
                .serial(resultSet.getString("Serial"))
                .udid(resultSet.getString("UDID"))
                .licenseIdentifier(resultSet.getString("LicenseIdentifier"))
                .deviceLock(resultSet.getString("DeviceLock"))
                .appleId(resultSet.getString("AppleID"))
                .rooted(resultSet.getString("Rooted"))
                .color(resultSet.getString("Color"))
                .grade(resultSet.getString("Grade"))
                .version(resultSet.getString("Version"))
                .os(resultSet.getString("OS"))
                .make(resultSet.getString("Make"))
                .firmware(resultSet.getString("Firmware"))
                .notes(resultSet.getString("Notes"))
                .esn(resultSet.getString("ESN"))
                .phonecheckId(resultSet.getString("PhoneCheckID"))
                .licenseId(resultSet.getString("LicenseID"))
                .isAppInstall(resultSet.getString("IsAppInstall"))
                .deviceCreatedDate(resultSet.getString("DeviceCreatedDate"))
                .deviceUpdatedDate(resultSet.getString("DeviceUpdatedDate"))
                .batteryCycle(resultSet.getString("BatteryCycle"))
                .batteryHealthPercentage(resultSet.getString("BatteryHealthPercentage"))
                .batteryPercentage(resultSet.getString("BatteryPercentage"))
                .batteryCurrentMaxCapacity(resultSet.getString("BatteryCurrentMaxCapacity"))
                .batteryDesignMaxCapacity(resultSet.getString("BatteryDesignMaxCapacity"))
                .batterySerial(resultSet.getString("BatterySerial"))
                .batteryModel(resultSet.getString("BatteryModel"))
                .batterySource(resultSet.getString("BatterySource"))
                .batteryTemperature(resultSet.getString("BatteryTemperature"))
                .avgBatteryTemperature(resultSet.getString("AvgBatteryTemperature"))
                .maxBatteryTemperature(resultSet.getString("MaxBatteryTemperature"))
                .minBatteryTemperature(resultSet.getString("MinBatteryTemperature"))
                .batteryResistance(resultSet.getString("BatteryResistance"))
                .batteryDegraded((Boolean) resultSet.getObject("BatteryDegraded"))
                .modelNo(resultSet.getString("Model#"))
                .unlockStatus(resultSet.getString("UnlockStatus"))
                .network(resultSet.getString("Network"))
                .network1(resultSet.getString("Network1"))
                .network2(resultSet.getString("Network2"))
                .sim1Mcc(resultSet.getString("SIM1MCC"))
                .sim1Mnc(resultSet.getString("SIM1MNC"))
                .sim2Mcc(resultSet.getString("SIM2MCC"))
                .sim2Mnc(resultSet.getString("SIM2MNC"))
                .sim1Name(resultSet.getString("SIM1Name"))
                .sim2Name(resultSet.getString("SIM2Name"))
                .simTechnology(resultSet.getString("SimTechnology"))
                .testerName(resultSet.getString("TesterName"))
                .cosmetics(resultSet.getString("Cosmetics"))
                .esnResponse(resultSet.getString("ESNResponse"))
                .lpn(resultSet.getString("LPN"))
                .custom1(resultSet.getString("Custom1"))
                .skuCode(resultSet.getString("SKUCode"))
                .manualEntry(resultSet.getString("ManualEntry"))
                .buildNo(resultSet.getString("BuildNo"))
                .cosmeticsFailed(failedCosmetics == null ? "" : StringUtils.join(failedCosmetics, ","))
                .cosmeticsPassed(passedCosmetics == null ? "" : StringUtils.join(passedCosmetics, ","))
                .cosmeticsPending(resultSet.getString("CosmeticsPending"))
                .cosmeticsWorking(resultSet.getString("CosmeticsWorking"))
                .regulatoryModelNumber(resultSet.getString("RegulatoryModelNumber"))
                .simSerial(resultSet.getString("SIMSERIAL"))
                .mdm(resultSet.getString("MDM"))
                .mdmResponse(resultSet.getString("MDMResponse"))
                .mdmUnRemovable(resultSet.getString("MDMUnRemovable"))
                .simSerial2(resultSet.getString("SIMSERIAL2"))
                .countryOfOrigin(resultSet.getString("CountryOfOrigin"))
                .restoreCode(resultSet.getString("RestoreCode"))
                .batteryDrainDuration(resultSet.getString("BatteryDrainDuration"))
                .batteryChargeStart(resultSet.getString("BatteryChargeStart"))
                .batteryChargeEnd(resultSet.getString("BatterChargeEnd"))
                .batteryDrain(resultSet.getString("BatteryDrain"))
                .wifiMacAddress(resultSet.getString("WifiMacAddress"))
                .simHistory(resultSet.getString("SimHistory"))
                .iCloudInfo(resultSet.getString("iCloudInfo"))
                .compatibleSim(resultSet.getString("CompatibleSim"))
                .notCompatibleSim(resultSet.getString("NotCompatibleSim"))
                .eSimPresent((Boolean) resultSet.getObject("ESimPresent"))
                .deviceState(resultSet.getString("DeviceState"))
                .batteryDrainType(resultSet.getString("BatteryDrainType"))
                .batteryDrainInfo(resultSet.getString("BatteryDrainInfo"))
                .pcCarrier(resultSet.getString("PCCarrier"))
                .cocoCurrentCapacity(resultSet.getString("CocoCurrentCapacity"))
                .cocoDesignCapacity(resultSet.getString("CocoDesignCapacity"))
                .oemBatteryHealth(resultSet.getString("OEMBatteryHealth"))
                .cocoBatteryHealth(resultSet.getString("CocoBatteryHealth"))
                .portNumber(resultSet.getString("PortNumber"))
                .startHeat(resultSet.getString("startHeat"))
                .endHeat(resultSet.getString("endHeat"))
                .productCode(resultSet.getString("ProductCode"))
                .bMic(resultSet.getString("BMic"))
                .vMic(resultSet.getString("VMic"))
                .fMic(resultSet.getString("FMic"))
                .gradePerformed(resultSet.getString("gradePerformed"))
                .simCardDeviceStatePrint(resultSet.getString("SimCardDeviceStatePrint"))
                .simCardDeviceStateErase(resultSet.getString("SimCardDeviceStateErase"))
                .gradingState(resultSet.getString("gradingState"))
                .simLock(resultSet.getString("SimLock"))
                .simLockResponse(resultSet.getString("SimLockResponse"))
                .serialImeiMismatched(resultSet.getString("serialImeiMismatched"))
                .packageName(resultSet.getString("PackageName"))
                .testPlanName(resultSet.getString("TestPlanName"))
                .gradeProfileId(resultSet.getString("grade_profile_id"))
                .gradeRouteId(resultSet.getString("grade_route_id"))
                .knox(resultSet.getString("Knox"))
                .screenTime(resultSet.getString("ScreenTime"))
                .gradingAnswer(resultSet.getString("gradingAnswer"))
                .batteryHealthGrade(resultSet.getString("BatteryHealthGrade"))
                .fccId(resultSet.getString("FCCID"))
                .oemPart(resultSet.getString("OEMPart"))
                .ram(resultSet.getString("Ram"))
                .oemStatus(resultSet.getString("OEMStatus"))
                .iftCodes(resultSet.getString("IFT_Codes"))
                .carrierLockResponse(resultSet.getString("CarrierLockResponse"))
                .transactionType(resultSet.getString("transaction_type"))
                .finalPrice(resultSet.getString("final_price"))
                .eBayRefurbished(resultSet.getString("eBayRefurbished"))
                .eBayRejection(resultSet.getString("eBayRejection"))
                .deleted(resultSet.getBoolean("Deleted"))
                .isRestartedFor17(resultSet.getBoolean("isRestartedFor17"))
                .isErasedForMDM(resultSet.getBoolean("isErasedForMDM"))
                .isImeiValidate((Boolean) resultSet.getObject("isImeiValidate"))
                .dataVerification(resultSet.getString("DataVerification"))
                .isGradePerformed(resultSet.getBoolean("isGradePerformed"))
                .usInsuranceResponse(resultSet.getString("usInsuranceResponse"))
                .swappaRejection(resultSet.getString("swappaRejection"))
                .swappaQualified(resultSet.getString("swappaQualified"))
                .amazonRenewed(resultSet.getString("amazonRenewed"))
                .amazonRenewedRejection(resultSet.getString("amazonRenewedRejection"))
                .backMarketRejection(resultSet.getString("backMarketRejection"))
                .backMarketQualified(resultSet.getString("backMarketQualified"))
                .build();
    }
}
