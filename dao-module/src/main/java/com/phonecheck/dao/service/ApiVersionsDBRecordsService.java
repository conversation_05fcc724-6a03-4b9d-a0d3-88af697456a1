package com.phonecheck.dao.service;

import com.phonecheck.dao.TblApiRecordsVersionsDao;
import com.phonecheck.dao.model.TblApiRecordsVersions;
import com.phonecheck.model.cloudapi.GetVersionsResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class ApiVersionsDBRecordsService {
    private final TblApiRecordsVersionsDao tblApiRecordsVersionsDao;
    private static final String FACE_ID = "FaceID";

    /**
     * Get the API Record Version for faceId api
     *
     * @return Float version of the FaceID api record
     */
    public Float getApiRecordsVersionForFaceIDKeys() {
        List<TblApiRecordsVersions> apiRecordsVersions =
                tblApiRecordsVersionsDao.getApiRecordsVersions(FACE_ID);
        if (apiRecordsVersions == null || apiRecordsVersions.size() == 0) {
            return 0.0f;
        }
        return apiRecordsVersions.get(0).getVersion();
    }

    /**
     * Truncate TblAPIRecordsVersions table and insert all the keys into it
     *
     * @param apiVersionsRecords List of version responses for apis
     */
    public void updateApiVersionsRecords(
            final List<GetVersionsResponse.GetVersionsSubResultResponse> apiVersionsRecords) {

        List<TblApiRecordsVersions> tblApiRecordsVersionsList = new ArrayList<>();

        for (GetVersionsResponse.GetVersionsSubResultResponse response : apiVersionsRecords) {

            TblApiRecordsVersions tblAPIRecordsVersion = TblApiRecordsVersions.builder()
                    .name(response.getName())
                    .url(response.getUrl())
                    .updatedDate(response.getUpdatedDate())
                    .version(response.getVersion())
                    .build();

            tblApiRecordsVersionsList.add(tblAPIRecordsVersion);
        }
        tblApiRecordsVersionsDao.truncate();
        tblApiRecordsVersionsDao.saveApiRecordVersions(tblApiRecordsVersionsList);
    }


}
