package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblCarrierFilterDao;
import com.phonecheck.dao.model.TblCarrierFilter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class CarrierFilterDBLookupService {

    private final TblCarrierFilterDao tblCarrierFilterDao;

    /**
     * Get CarrierFilter based on the following params
     *
     * @param carrier
     * @return TblCarrierFilter object
     */
    public TblCarrierFilter getCarrierFilter(final String carrier) {
        return tblCarrierFilterDao.getCarrierFilterFromDB(carrier);
    }

    /**
     * Calls the TblCarrierFilterDao's insert data method to insert carrier filter list into database
     *
     * @param tblCarrierFilterList to insert into database table "TBLCarrierFilter"
     */
    public void insertCarrierFilter(final List<TblCarrierFilter> tblCarrierFilterList) {
        tblCarrierFilterDao.insertDataIntoTblCarrierFilter(tblCarrierFilterList);
    }

    /**
     * Returns the number of records in the table
     * @return no. of records
     */
    public int getRecordCount() {
        return tblCarrierFilterDao.getRecordCount();
    }

    /**
     * Calls the TblCarrierFilterDao's method to delete all the carrier filter data from "TBLCarrierFilter"
     */
    public void deleteCarrierFilters() {
        tblCarrierFilterDao.deleteDataFromTblCarrierFilter();
    }
}
