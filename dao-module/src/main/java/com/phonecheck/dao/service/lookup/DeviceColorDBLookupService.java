package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblDeviceColorDao;
import com.phonecheck.dao.model.TblDeviceColor;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class DeviceColorDBLookupService {
    private final TblDeviceColorDao tblDeviceColorDao;

    /**
     * Method to get all possible color names for a device title
     *
     * @param deviceTitle device tile
     * @return list of color string
     */
    public List<String> getAllPossibleColorsOfDevice(final String deviceTitle) {
        return tblDeviceColorDao.getAllPossibleColorsOfDevice(deviceTitle);
    }

    /**
     * Get all colors by model number
     *
     * @param modelNo
     * @return color list
     */
    public List<String> getAllPossibleColorByModelNumber(final String modelNo) {
        return tblDeviceColorDao.getAllPossibleColorsByModelNumber(modelNo);
    }

    /**
     * Inserts device color data in lookup table after login
     *
     * @param tblDeviceColorList list of TblDeviceColor objects
     */
    public void insertDeviceColor(final List<TblDeviceColor> tblDeviceColorList) {
        tblDeviceColorDao.insertDataIntoTblDeviceColor(tblDeviceColorList);
    }

    /**
     * Deletes old device colors data from lookup table
     */
    public void deleteDeviceColors() {
        tblDeviceColorDao.deleteDataFromTblDeviceColor();
    }

    /**
     * Returns the number of records in the table
     * @return no. of records
     */
    public int getRecordCount() {
        return tblDeviceColorDao.getRecordCount();
    }

    /**
     * Fetches Color for the device on the basis of Model No and Color Code from TblDeviceColor table
     *
     * @param modelNo
     * @param colorCode
     * @return String representing color of device
     */
    public String getColorByModelNoAndColorCode(final String modelNo, final String colorCode) {
        List<String> result = tblDeviceColorDao.getColorByModelNumberAndColorCode(modelNo, colorCode);
        return result != null && !result.isEmpty() ? result.get(0) : null;
    }
}
