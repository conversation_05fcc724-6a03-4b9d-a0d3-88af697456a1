package com.phonecheck.dao.service;

import com.phonecheck.dao.TblLastVendorDetailDao;
import com.phonecheck.dao.TblVendorMainDao;
import com.phonecheck.dao.model.TblLastVendorDetail;
import com.phonecheck.dao.model.TblVendorMain;
import com.phonecheck.model.transaction.Transaction;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class VendorDetailDBService {
    private final TblLastVendorDetailDao lastVendorDetailDao;
    private final TblVendorMainDao tblVendorMainDao;

    /**
     * Sets the current transaction
     *
     * @param transaction object
     */
    public void setCurrentTransaction(final Transaction transaction) {

        lastVendorDetailDao.deleteTransaction(String.valueOf(transaction.getLicenseId()));

        TblLastVendorDetail tblLastVendorDetail = TblLastVendorDetail.builder().
                transactionStartTime(transaction.getTransactionDate()).
                transactionId(String.valueOf(transaction.getTransactionId())).
                licenseID(String.valueOf(transaction.getLicenseId())).build();
        lastVendorDetailDao.insert(tblLastVendorDetail);
    }

    /**
     * Creates a new transaction
     *
     * @param transaction object
     */
    public void createTransaction(final Transaction transaction) {
        tblVendorMainDao.deleteTransaction(String.valueOf(transaction.getTransactionId()));

        TblVendorMain tblVendorMain = TblVendorMain.builder().
                vendorName(transaction.getVendorName()).
                invoiceNo(transaction.getInvoiceNo()).
                transactionDate(transaction.getTransactionDate()).
                transactionId(String.valueOf(transaction.getTransactionId())).
                licenseID(String.valueOf(transaction.getLicenseId())).
                stationID(transaction.getStationId()).
                boxNo(transaction.getBoxNo()).
                qty(transaction.getQty()).build();
        tblVendorMainDao.insert(tblVendorMain);
    }

    /**
     * Get transaction by Id
     * @param transactionId
     * @return transaction model
     */
    public Transaction getTransactionById(final Integer transactionId) {
        final TblVendorMain tblVendorMainRecord = tblVendorMainDao.getVendorMainRecordByTransactionId(transactionId);
        return Transaction.builder()
                .transactionId(transactionId)
                .qty(tblVendorMainRecord.getQty())
                .boxNo(tblVendorMainRecord.getBoxNo())
                .vendorName(tblVendorMainRecord.getVendorName())
                .invoiceNo(tblVendorMainRecord.getInvoiceNo())
                .licenseId(Integer.parseInt(tblVendorMainRecord.getLicenseID()))
                .stationId(tblVendorMainRecord.getStationID())
                .transactionDate(tblVendorMainRecord.getTransactionDate())
                .build();
    }

    /**
     * Get last vendor info by licenseId
     * @param licenseId
     * @return transaction model
     */
    public TblLastVendorDetail getLastVendorByLicenseId(final Integer licenseId) {
        return lastVendorDetailDao.getLastVendorRecordByLicenseId(licenseId);
    }

    /**
     * Updates vendor details
     *
     * @param transaction object
     */
    public void updateVendorInvoice(final Transaction transaction) {
        tblVendorMainDao.updateVendorInvoice(transaction);
    }

    /**
     * Get all the processed transaction records from database
     *
     * @return all previously processes transactions list
     */
    public List<Transaction> getAllProcessedTransactions() {
        List<TblVendorMain> vendorMainRecords = tblVendorMainDao.getVendorMainRecords();
        List<Transaction> transactionList = null;

        if (vendorMainRecords != null && !vendorMainRecords.isEmpty()) {
            transactionList = new ArrayList<>();

            for (TblVendorMain vendorMain : vendorMainRecords) {
                Transaction transaction = Transaction
                        .builder()
                        .transactionId(Integer.parseInt(vendorMain.getTransactionId()))
                        .transactionDate(vendorMain.getTransactionDate())
                        .stationId(vendorMain.getStationID())
                        .licenseId(Integer.parseInt(vendorMain.getLicenseID()))
                        .vendorName(vendorMain.getVendorName())
                        .invoiceNo(vendorMain.getInvoiceNo())
                        .qty(vendorMain.getQty())
                        .boxNo(vendorMain.getBoxNo())
                        .build();
                transactionList.add(transaction);
            }
        }

        return transactionList;
    }
}
