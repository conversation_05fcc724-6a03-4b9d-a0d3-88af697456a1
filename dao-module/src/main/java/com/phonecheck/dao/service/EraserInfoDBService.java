package com.phonecheck.dao.service;

import com.phonecheck.dao.TblEraserInfoDao;
import com.phonecheck.dao.model.TblEraserInfo;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.DateFormatUtil;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.phonecheck.model.util.DateFormatUtil.DATE_TIME_FORMAT;

@Service
@AllArgsConstructor
public class EraserInfoDBService {
    private final TblEraserInfoDao tblEraserInfoDao;
    private final InMemoryStore inMemoryStore;

    /**
     * Converts device to TblEraserInfo model and inserts into table TblEraserInfo and table PendingSyncRecords
     *
     * @param device IphoneDevice
     */
    public void insertEraserInfo(final Device device) {
        insertEraserInfo(deviceToEraserInfoMapper(device, device.getStage()));
    }

    /**
     * Converts disconnected device to TblEraserInfo model with device stage as 'Disconnected'
     * and inserts into table TblEraserInfo and table PendingSyncRecords
     *
     * @param device IphoneDevice
     */
    public void insertEraserInfoForDisconnectedDevice(final Device device) {
        insertEraserInfo(deviceToEraserInfoMapper(device, DeviceStage.DISCONNECTED));
    }

    /**
     * Inserts tblEraserInfo data into table TblEraserInfo
     *
     * @param tblEraserInfo object
     */
    public void insertEraserInfo(final TblEraserInfo tblEraserInfo) {
        tblEraserInfoDao.insert(tblEraserInfo);
    }

    /**
     * Retrieves device eraser info from the database
     *
     * @param transactionId
     * @param deviceSerial
     * @return TblEraserInfo
     */
    public TblEraserInfo getEraserInfoFromDb(final String transactionId, final String deviceSerial) {
        return tblEraserInfoDao.getErasedInfo(transactionId, deviceSerial);
    }

    /**
     * Deletes data from TblEraserInfo table
     */
    public void truncateEraserInfo() {
        tblEraserInfoDao.truncate();
    }

    private TblEraserInfo deviceToEraserInfoMapper(final Device device, final DeviceStage deviceStage) {
        return TblEraserInfo.builder()
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .licenseId(String.valueOf(inMemoryStore.getLicenseId()))
                .serial(device.getSerial())
                .type(device instanceof IosDevice &&
                        StringUtils.isNotBlank(((IosDevice) device).getEraserType()) ?
                        ((IosDevice) device).getEraserType() : "")
                .deviceState(deviceStage.getText())
                .carrier(device.getCarrier())
                .imei(device.getImei())
                .simErased(DeviceStage.ERASE_SUCCESS.equals(device.getStage()) ||
                        DeviceStage.ERASE_IN_PROGRESS.equals(device.getStage()))
                .eSimErased(device.isESimErased()
                        && (DeviceStage.ERASE_SUCCESS.equals(device.getStage()) ||
                        DeviceStage.ERASE_IN_PROGRESS.equals(device.getStage())))
                .color(device.getColor())
                .grade("")
                .erased(DeviceStage.ERASE_SUCCESS.equals(device.getStage()) ||
                        DeviceStage.ERASE_IN_PROGRESS.equals(device.getStage()) ||
                        DeviceRestoreStatus.RESTORE_SUCCESS_RECEIVED.equals(device.getRestoreStatus()) ||
                        DeviceRestoreStatus.RESTORE_COMPLETED.equals(device.getRestoreStatus()))
                .startTime(device.getEraseStartTime() != null
                        ? DateFormatUtil.millisToUTCDateTime(device.getEraseStartTime(), DATE_TIME_FORMAT)
                        : null)
                .endTime(device.getEraseEndTime() != null
                        ? DateFormatUtil.millisToUTCDateTime(device.getEraseEndTime(), DATE_TIME_FORMAT)
                        : null)
                .totalTime(device.getEraseEndTime() != null && device.getEraseStartTime() != null
                        ? TimeUnit.MILLISECONDS.toSeconds(
                        device.getEraseEndTime() - device.getEraseStartTime()) + " s"
                        : null)
                .deleted(false)
                .build();
    }



    /**
     * Retrieves device restore info from the database
     *
     * @param transactionId
     * @param deviceSerial
     * @return TblEraserInfo
     */
    public TblEraserInfo getRestoreInfoFromDb(final String transactionId, final String deviceSerial) {
        return tblEraserInfoDao.getRestoreInfo(transactionId, deviceSerial);
    }
}