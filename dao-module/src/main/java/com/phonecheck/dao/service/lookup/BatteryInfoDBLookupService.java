package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblDeviceInfoDao;
import com.phonecheck.dao.model.TblDeviceInfo;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.battery.BatterySource;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class BatteryInfoDBLookupService {
    private final TblDeviceInfoDao tblDeviceInfoDao;

    public BatteryInfo getBatteryInfoFromDatabase(final int transactionId,
                                                  final String deviceId) {
        TblDeviceInfo deviceInfo = tblDeviceInfoDao.getDeviceFromDB(String.valueOf(transactionId), deviceId);
        if (deviceInfo != null) {
            BatteryInfo batteryInfo = new BatteryInfo();

            batteryInfo.setCycle(StringUtils.isNotBlank(deviceInfo.getBatteryCycle()) ?
                    Integer.parseInt(deviceInfo.getBatteryCycle()) :
                    0
            );
            // Need to parse below fields as float to maintain backward compatibility
            // as these fields were earlier float and now are integers. We can parse these as integers later on.
            batteryInfo.setHealthPercentage(StringUtils.isNotBlank(deviceInfo.getBatteryHealthPercentage()) ?
                    (int) Float.parseFloat(deviceInfo.getBatteryHealthPercentage()) :
                    0
            );
            batteryInfo.setOemHealthPercentage(StringUtils.isNotBlank(deviceInfo.getOemBatteryHealth()) ?
                    (int) Float.parseFloat(deviceInfo.getOemBatteryHealth()) :
                    0
            );
            batteryInfo.setCocoHealthPercentage(StringUtils.isNotBlank(deviceInfo.getCocoBatteryHealth()) ?
                    (int) Float.parseFloat(deviceInfo.getCocoBatteryHealth()) :
                    0
            );
            batteryInfo.setCurrentCapacity(StringUtils.isNotBlank(deviceInfo.getBatteryCurrentMaxCapacity()) ?
                    Integer.parseInt(deviceInfo.getBatteryCurrentMaxCapacity()) :
                    0
            );
            batteryInfo.setDesignedCapacity(StringUtils.isNotBlank(deviceInfo.getBatteryDesignMaxCapacity()) ?
                    Integer.parseInt(deviceInfo.getBatteryDesignMaxCapacity()) :
                    0
            );
            batteryInfo.setCocoCurrentCapacity(StringUtils.isNotBlank(deviceInfo.getCocoCurrentCapacity()) ?
                    Integer.parseInt(deviceInfo.getCocoCurrentCapacity()) :
                    0
            );
            batteryInfo.setCocoDesignedCapacity(StringUtils.isNotBlank(deviceInfo.getCocoDesignCapacity()) ?
                    Integer.parseInt(deviceInfo.getCocoDesignCapacity()) :
                    0
            );
            batteryInfo.setSerial(StringUtils.isNotBlank(deviceInfo.getBatterySerial()) ?
                    deviceInfo.getBatterySerial() :
                    "N/A"
            );
            batteryInfo.setModel(StringUtils.isNotBlank(deviceInfo.getBatteryModel()) ?
                    deviceInfo.getBatteryModel() :
                    "N/A"
            );
            batteryInfo.setBatteryResistance(StringUtils.isNotBlank(deviceInfo.getBatteryResistance()) ?
                    Double.parseDouble(deviceInfo.getBatteryResistance()) :
                    0.0
            );
            batteryInfo.setTemperature(StringUtils.isNotBlank(deviceInfo.getBatteryTemperature()) ?
                    Float.parseFloat(deviceInfo.getBatteryTemperature()) :
                    0
            );
            batteryInfo.setSource(StringUtils.isNotBlank(deviceInfo.getBatterySource()) ?
                    BatterySource.fromKey(deviceInfo.getBatterySource()) :
                    null
            );
            return batteryInfo;
        }

        return null;
    }
}
