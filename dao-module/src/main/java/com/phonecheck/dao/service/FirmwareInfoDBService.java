package com.phonecheck.dao.service;

import com.phonecheck.dao.TblFirmwareDao;
import com.phonecheck.dao.model.TblFirmwareInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Class to call DAO methods for the table TblFirmwareInfo
 */
@Component
@AllArgsConstructor
public class FirmwareInfoDBService {
    private TblFirmwareDao firmwareDao;

    public void saveFirmwareInfo(final TblFirmwareInfo firmwareInfo) {
        firmwareDao.insert(firmwareInfo);
    }

    public void deleteFirmwareInfo(final TblFirmwareInfo firmwareInfo) {
        firmwareDao.delete(firmwareInfo);
    }

    public TblFirmwareInfo getFirmwareInfo(final String key) {
        return firmwareDao.getFirmwareInfoByKey(key);
    }

    public List<TblFirmwareInfo> getAllFirmwares() {
        return firmwareDao.getAllFirmwares();
    }
}
