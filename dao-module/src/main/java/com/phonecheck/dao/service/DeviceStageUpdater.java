package com.phonecheck.dao.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.phonecheck.dao.DeviceStageDao;
import com.phonecheck.dao.TestResultDao;
import com.phonecheck.model.device.stage.*;
import com.phonecheck.model.test.*;
import com.phonecheck.model.util.TestResultsUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Manages a device's movement between stages of its lifecycle
 */
@Component
public class DeviceStageUpdater implements IStageUpdater {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceStageUpdater.class);

    private final DeviceStageDao stageDao;
    private final TestResultDao testResultDao;
    private final BlockingQueue<IStage> updateQueue;
    private final boolean keepGoing = true;

    public DeviceStageUpdater(final DeviceStageDao stageDao,
                              final TestResultDao testResultDao) {
        this.stageDao = stageDao;
        this.testResultDao = testResultDao;

        updateQueue = new LinkedBlockingQueue<>();
        startStageUpdateThread();
    }

    private void startStageUpdateThread() {
        new Thread(() -> {
            while (keepGoing) {
                try {
                    IStage stage = updateQueue.take();
                    stage.updateStage(this);

                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        // do nothing
                    }
                } catch (Exception e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }).start();
    }

    public void updateStage(final IStage stage) {
        updateQueue.add(stage);
    }

    @Override
    public void update(final InitialConnectionStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final PairFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final PairSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final InfoCollectionSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final InfoCollectionFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final BatteryInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final BatteryInfoFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final ActivationFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final ActivationSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final AppInstallFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final AppInstallSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final MountImageSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final MountImageFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final StartTestsSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final StartTestsFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final TestConfigSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final MdmStatusSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final OemDataCollectionSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final IcloudInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final EsnInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final CarrierLockInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final SimLockInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final ReadyStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final NotReadyStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final AppTestingDoneStage stage) {
        // Test results are distributed across several columns in two different tables and the inbound message may
        // not have results for every component

        // The device's serial number is required because the table that holds test result pass/fail data
        // is keyed by serial number instead of UDID
        if (StringUtils.isEmpty(stage.getSerial())) {
            throw new IllegalArgumentException("Serial number is required");
        }

        // Update the stage info
        stageDao.updateTestingComplete(stage);
        // Save the general test results
        TestResults testResults = stage.getDeviceTestResult().getTestResults();
        if (testResults != null) {
            final TestResultSummary testResultSummary = TestResultSummary.builder()
                    .id(stage.getId())
                    .passedTests(TestResultsUtil.listToCommaSeparatedString(testResults.getPassed()))
                    .failedTests(TestResultsUtil.listToCommaSeparatedString(testResults.getFailed()))
                    .testingCompleted(testResults.getTestingCompleted())
                    .build();
            try {
                testResultDao.updateGeneralResults(stage.getSerial(), stage.getDeviceType(), stage.getTransactionId(),
                        stage.getLicenseId(), testResultSummary);
            } catch (JsonProcessingException e) {
                LOGGER.error("Exception occurred while parsing results in AppTestingDoneStage", e);
            }
        }

        // Save battery drain results if we have them
        final BatteryResults batteryResults = stage.getDeviceTestResult().getBatteryResults();
        if (batteryResults != null) {
            final BatteryDrainSummary summary = batteryResults.getBatteryDrain();
            if (summary != null) {
                testResultDao.updateBatteryDrainSummary(stage.getId(), stage.getTransactionId(), summary);
            }

            final Map<String, String> detail = batteryResults.getBatteryDrainInfo();
            if (null != detail) {
                try {
                    testResultDao.updateBatteryDrainDetail(stage.getId(), stage.getTransactionId(), detail);
                } catch (JsonProcessingException e) {
                    LOGGER.error("Exception occurred while parsing battery details in AppTestingDoneStage", e);
                }
            }
        }

        // Save cosmetics grading results if we have them
        final CosmeticsResults cosmeticsResults = stage.getDeviceTestResult().getCosmeticResults();
        if (cosmeticsResults != null) {
            final List<String> cosmeticsPassed = cosmeticsResults.getPassed() != null
                    ? Arrays.stream(cosmeticsResults.getPassed().split(",")).toList()
                    : null;
            final List<String> cosmeticsFailed = cosmeticsResults.getFailed() != null
                    ? Arrays.stream(cosmeticsResults.getFailed().split(",")).toList()
                    : null;
            if (!CollectionUtils.isEmpty(cosmeticsPassed) || !CollectionUtils.isEmpty(cosmeticsFailed)) {
                try {
                    testResultDao.updateCosmetics(stage.getId(), stage.getTransactionId(),
                            cosmeticsPassed, cosmeticsFailed);
                } catch (JsonProcessingException e) {
                    LOGGER.error("Exception occurred while parsing cosmetics in AppTestingDoneStage", e);
                }
            }
        }

        // Save microphone test results if we have them
        final MicrophoneResults micResult = stage.getDeviceTestResult().getMicrophoneResults();
        if (null != micResult) {
            testResultDao.updateMicThresholds(stage.getId(), stage.getTransactionId(), micResult);
        }

        // Save grade test result if we have it
        final String grade = stage.getDeviceTestResult().getGradeResults();
        if (StringUtils.isNotEmpty(grade)) {
            testResultDao.updateDeviceGrade(stage.getId(), stage.getTransactionId(), grade);
        }
    }

    @Override
    public void update(final FrpInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final MeidInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final PcUtilityInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final KnoxInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final ImeiCollectionSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final ImeiCollectionFailureStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final AndroidMdmStatusSuccessStage stage) {
        stageDao.updateStage(stage);
    }

    @Override
    public void update(final RootInfoSuccessStage stage) {
        stageDao.updateStage(stage);
    }
}
