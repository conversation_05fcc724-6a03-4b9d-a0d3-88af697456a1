package com.phonecheck.dao.service;

import com.phonecheck.dao.TblEEECodesDao;
import com.phonecheck.dao.model.TblEEECodesModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
@AllArgsConstructor
public class EEECodesDBService {
    private final TblEEECodesDao tblEEECodesDao;

    /**
     * inserts EEE codes to database
     *
     * @param eeeCodesSet
     */
    public void insertEEECodes(final Set<String> eeeCodesSet) {
        List<TblEEECodesModel> eeeCodesModelList = new ArrayList<>();

        for (String eeeCode : eeeCodesSet) {
            TblEEECodesModel eeeCodesModel = TblEEECodesModel.builder()
                    .eeeCode(eeeCode)
                    .build();
            eeeCodesModelList.add(eeeCodesModel);
        }
        truncateEEECodes();
        tblEEECodesDao.insert(eeeCodesModelList);
    }

    /**
     * retrieves EEE codes from database
     *
     * @return set of EEE codes string
     */
    public Set<String> getEEECodes() {
        return tblEEECodesDao.getEEECodes();
    }

    /**
     * truncates EEECodes table
     */
    private void truncateEEECodes() {
        tblEEECodesDao.truncate();
    }
}
