package com.phonecheck.dao;

import com.phonecheck.dao.mapper.UphInfoRowMapper;
import com.phonecheck.model.status.WorkingStatus;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
public class UphInfoDao extends AbstractDao {
    private static final UphInfoRowMapper ROW_MAPPER = new UphInfoRowMapper();
    private static final String UPSERT_UPH_INFO_ON_CONNECT =
            "MERGE INTO \"uph_info\" (\"device_serial\", \"connected_time\", \"disconnected_time\") VALUES (?, " +
                    "CURRENT_TIMESTAMP(), null)";

    private static final String UPSERT_UPH_INFO_ON_DISCONNECT =
            "MERGE INTO \"uph_info\" (\"device_serial\", \"working\", \"erased\",\"disconnected_time\")" +
                    " VALUES (?,?,?,CURRENT_TIMESTAMP())";

    private static final String SELECT_COUNT_BY_START_TIME =
            "SELECT COUNT(*) as count FROM \"uph_info\" WHERE \"disconnected_time\" > ? AND "
                    + "\"disconnected_time\" <= CURRENT_TIMESTAMP()";

    private static final String SELECT_COUNT_BY_TIME_RANGE =
            "SELECT COUNT(*) as count FROM \"uph_info\" WHERE \"disconnected_time\" > ? AND "
                    + "\"disconnected_time\" <= ?";

    private static final String SELECT_COUNT_BY_WORKING_STATUS =
            "SELECT COUNT(*) as count FROM \"uph_info\" WHERE \"working\" = ? AND \"disconnected_time\" > ? AND "
                    + "\"disconnected_time\" <= CURRENT_TIMESTAMP()";

    private static final String SELECT_COUNT_BY_ERASED =
            "SELECT COUNT(*) as count FROM \"uph_info\" WHERE \"erased\" = TRUE AND \"disconnected_time\" > ? AND "
                    + "\"disconnected_time\" <= CURRENT_TIMESTAMP()";
    private static final String TRUNCATE_TABLE_UPH_INFO = "TRUNCATE TABLE \"uph_info\"";

    public UphInfoDao(@Qualifier("phonecheckJdbcTemplate") final JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * Truncate table uph_info
     */
    public void truncate() {
        getJdbcTemplate().update(TRUNCATE_TABLE_UPH_INFO);
    }

    /**
     * Upsert time on connect
     *
     * @param deviceSerial serial
     */
    public synchronized void upsertUphInfoOnConnect(final String deviceSerial) {
        getJdbcTemplate().update(UPSERT_UPH_INFO_ON_CONNECT,
                deviceSerial);
    }

    /**
     * Upsert time on disconnect
     *
     * @param deviceSerial  serial
     * @param workingStatus
     * @param isErased
     */
    public synchronized void upsertUphInfoOnDisconnect(final String deviceSerial,
                                          final WorkingStatus workingStatus, final Boolean isErased) {
        getJdbcTemplate().update(UPSERT_UPH_INFO_ON_DISCONNECT,
                deviceSerial, workingStatus.getKey(), isErased == null ? false : isErased);
    }

    /**
     * Get device counts given a start time
     *
     * @param startTime start time of range, end time is always current time
     * @return device count
     */
    public Integer getDeviceCountsByStartTime(final Timestamp startTime) {
        Integer count = getJdbcTemplate()
                .queryForObject(SELECT_COUNT_BY_START_TIME, Integer.class,
                        startTime);

        return count == null ? 0 : count;
    }

    /**
     * Get device counts given a start time, working status
     *
     * @param startTime
     * @param workingStatus
     * @return device count
     */
    public Integer getDeviceCountsByWorkingStatus(final Timestamp startTime,
                                                  final WorkingStatus workingStatus) {
        Integer count = getJdbcTemplate()
                .queryForObject(SELECT_COUNT_BY_WORKING_STATUS, Integer.class,
                        workingStatus.getKey(), startTime);

        return count == null ? 0 : count;
    }

    /**
     * Get device counts by erased
     *
     * @param startTime
     * @return device count
     */
    public Integer getDeviceCountsByErased(final Timestamp startTime) {
        Integer count = getJdbcTemplate()
                .queryForObject(SELECT_COUNT_BY_ERASED, Integer.class, startTime);

        return count == null ? 0 : count;
    }

    /**
     * Get device counts given a start time and end time
     *
     * @param startTime start time of range
     * @param endTime   end time of range
     * @return device count
     */
    public Integer getDeviceCountsByTimeRange(final Timestamp startTime, final Timestamp endTime) {
        Integer count = getJdbcTemplate()
                .queryForObject(SELECT_COUNT_BY_TIME_RANGE, Integer.class,
                        startTime, endTime);

        return count == null ? 0 : count;
    }
}
