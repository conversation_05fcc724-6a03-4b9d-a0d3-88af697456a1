package com.phonecheck.dao;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.jdbc.core.JdbcTemplate;

@Getter
@AllArgsConstructor
public abstract class AbstractDao {
    private JdbcTemplate jdbcTemplate;

    /**
     * Appends "?" to the Insert query.
     * Example: INSERT INTO TBLDeviceColor VALUES (?, ?, ?, ?)
     *
     * @param sqlBuilder  query we want to append "?" to.
     * @param noOfColumns we use this to determine no. of "?" to append.
     * @return Sql query
     */
    protected static String appendQuestionMarkToSql(final StringBuilder sqlBuilder, final int noOfColumns) {
        for (int i = 0; i < noOfColumns; i++) {
            sqlBuilder.append("?");
            if (i < noOfColumns - 1) {
                sqlBuilder.append(", ");
            } else {
                sqlBuilder.append(")");
            }
        }

        return sqlBuilder.toString();
    }
}
