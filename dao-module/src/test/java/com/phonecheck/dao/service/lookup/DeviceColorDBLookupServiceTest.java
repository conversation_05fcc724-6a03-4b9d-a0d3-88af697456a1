package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblDeviceColorDao;
import com.phonecheck.dao.model.TblDeviceColor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
public class DeviceColorDBLookupServiceTest {
    @Mock
    private TblDeviceColorDao tblDeviceColorDao;

    private DeviceColorDBLookupService deviceColorDBLookupService;
    private List<TblDeviceColor> tblDeviceColorList;

    @BeforeEach
    void beforeEach() {
        deviceColorDBLookupService = new DeviceColorDBLookupService(tblDeviceColorDao);
        tblDeviceColorList = List.of(
                TblDeviceColor.builder().
                        colorId("color_id").
                        deviceTitle("device_title").
                        make("make").
                        modelName("model_name").
                        modelNumber("model_number").
                        serialNumber("serial_number").
                        imei("imei").
                        color("color").
                        colorCode("color_code").
                        deleted(false).build(),
                TblDeviceColor.builder().
                        colorId("color_id2").
                        deviceTitle("device_title2").
                        make("make2").
                        modelName("model_name2").
                        modelNumber("model_number2").
                        serialNumber("serial_number2").
                        imei("imei2").
                        color("color2").
                        colorCode("color_code2").
                        deleted(false).build());
    }

    @Test
    public void insertExpectedDataIntoTblDeviceColor() {
        deviceColorDBLookupService.insertDeviceColor(tblDeviceColorList);
        Mockito.verify(tblDeviceColorDao).insertDataIntoTblDeviceColor(tblDeviceColorList);
    }

    @Test
    public void insertNullIntoTblDeviceColor() {
        deviceColorDBLookupService.insertDeviceColor(null);
        Mockito.verify(tblDeviceColorDao).insertDataIntoTblDeviceColor(null);
    }

    @Test
    public void deleteAllDataFromTblDeviceColor() {
        deviceColorDBLookupService.deleteDeviceColors();
        Mockito.verify(tblDeviceColorDao).deleteDataFromTblDeviceColor();
    }

    @Test
    public void testGetColorByModelNoAndColorCode() {
        Mockito.when(tblDeviceColorDao.getColorByModelNumberAndColorCode(anyString(), anyString()))
                .thenReturn(List.of("Blue"));
        String output = deviceColorDBLookupService.getColorByModelNoAndColorCode(anyString(), anyString());
        assertEquals("Blue", output);
        Mockito.verify(tblDeviceColorDao).getColorByModelNumberAndColorCode(anyString(), anyString());
    }
}
