package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblModelsDao;
import com.phonecheck.dao.model.TblModels;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ModelDBLookupServiceTest {
    @Mock
    private TblModelsDao tblModelsDao;
    private ModelDBLookupService modelDBLookupService;
    private List<TblModels> tblModelsList;

    @BeforeEach
    void beforeEach() {
        modelDBLookupService = new ModelDBLookupService(tblModelsDao);
        tblModelsList = List.of(
                TblModels.builder()
                        .modelName("model")
                        .productType("type")
                        .bdid("id")
                        .cpid("id")
                        .build(),
                TblModels.builder()
                        .modelName("model2")
                        .productType("type2")
                        .bdid("id2")
                        .cpid("id2")
                        .build());
    }

    @Test
    public void testInsertModels() {
        modelDBLookupService.insertIosModels(tblModelsList);
        Mockito.verify(tblModelsDao).insert(tblModelsList);
    }

    @Test
    public void testPurgeModelInfo() {
        modelDBLookupService.truncateIosModels();
        Mockito.verify(tblModelsDao).truncate();
    }

    @Test
    public void testGetModelName() {
        String input = "type";
        modelDBLookupService.getIosModel(input);
        Mockito.verify(tblModelsDao).getModelByProductType(input);
    }

    @Test
    public void testGetIosModelByCpidAndBdid() {
        String cpid = "test CPID";
        String edid = "test EDID";

        TblModels models = new TblModels();
        models.setModelName("Test model");
        models.setProductType("Test product");
        when(tblModelsDao.getModelByCpidAndBdid(cpid, edid)).thenReturn(models);

        Pair<String, String> res =  modelDBLookupService.getModelByCpidAndBdid(cpid, edid);
        Mockito.verify(tblModelsDao).getModelByCpidAndBdid(cpid, edid);
        Assertions.assertEquals(models.getModelName(), res.getFirst());
        Assertions.assertEquals(models.getProductType(), res.getSecond());
    }
}
