package com.phonecheck.dao.jpa;

import com.phonecheck.dao.jpa.entities.Employees;
import com.phonecheck.dao.jpa.repository.EmployeeRepository;
import com.phonecheck.dao.jpa.service.EmployeeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EmployeeServiceTest {
    @Mock
    private EmployeeRepository employeeRepository;
    private EmployeeService employeeService;
    private Employees employee;

    @BeforeEach
    void setUp() {
        employeeService = new EmployeeService(employeeRepository);
        employee = new Employees();
        employee.setId(1);
        employee.setName("Emp");
        employee.setAddress("Lahore");

    }

    @Test
    void saveEmployeeTest() {
        employeeService.saveEmp(employee);
        // Assert
        verify(employeeRepository).save(employee);
    }

    @Test
    void deleteEmployeeTest() {
        employeeService.deleteEmployee(employee);
        verify(employeeRepository).delete(employee);
    }

    @Test
    public void getAllEmployeeTest() {
        List<Employees> employeeList = new ArrayList<>();
        employeeList.add(employee);

        when(employeeRepository.findAll()).thenReturn(employeeList);
        List<Employees> result = employeeService.getAllEmp();
        verify(employeeRepository).findAll();
        assertEquals(employeeList, result);
    }

    @Test
    public void testUpdateEmployeeExist() {
        Employees updatedEmployee = new Employees();
        updatedEmployee.setId(1);
        updatedEmployee.setName("John");
        updatedEmployee.setName("USA");

        when(employeeRepository.findById(employee.getId())).thenReturn(Optional.of(employee));

        employeeService.updateEmployee(updatedEmployee);
        verify(employeeRepository).findById(employee.getId());
        verify(employeeRepository).save(updatedEmployee);
    }

    @Test
    public void testUpdateEmployeeNotExist() {
        Employees updatedEmployee = new Employees();
        updatedEmployee.setId(1);
        updatedEmployee.setName("John");
        updatedEmployee.setName("USA");

        when(employeeRepository.findById(updatedEmployee.getId())).thenReturn(Optional.empty());

        employeeService.updateEmployee(updatedEmployee);

        verify(employeeRepository).findById(updatedEmployee.getId());
        verify(employeeRepository, never()).save(any());
    }


}
