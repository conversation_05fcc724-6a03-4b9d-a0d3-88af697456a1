package com.phonecheck.mount.image;

import com.phonecheck.model.util.MacSupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.phonecheck.model.constants.FileConstants.DEV_DISK_IMAGES_FOLDER;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class DiskImageFileServiceTest {
    private final String devDiskImageVersion = "10.3";
    private DiskImageFileService diskImageFileService;
    @Mock
    private MacSupportFilePathsStrategy macSupportFilePathsStrategy;
    @Mock
    private SupportFilePath supportFilePath;

    @BeforeEach
    void beforeEach() {
        diskImageFileService = new DiskImageFileService(supportFilePath);
    }

    @Test
    @DisplayName("Verifies disk image file path")
    public void testDiskImageFilePath() {
        when(macSupportFilePathsStrategy.getRootFolderPath()).thenReturn("root");
        when(supportFilePath.getPaths()).thenReturn(macSupportFilePathsStrategy);

        String filePath = diskImageFileService.getDiskImageFilePath(devDiskImageVersion);
        Assertions.assertNotNull(filePath);
        Assertions.assertEquals(
                "root/" + DEV_DISK_IMAGES_FOLDER + "/" + devDiskImageVersion + "/DeveloperDiskImage.dmg",
                filePath
        );
    }

    @Test
    @DisplayName("Verifies disk image signature file path")
    public void testAppFilePathForOsLessThan8() {
        when(macSupportFilePathsStrategy.getRootFolderPath()).thenReturn("root");
        when(supportFilePath.getPaths()).thenReturn(macSupportFilePathsStrategy);

        String filePath = diskImageFileService.getDiskImageSignatureFilePath(devDiskImageVersion);
        Assertions.assertNotNull(filePath);
        Assertions.assertEquals(
                "root/" + DEV_DISK_IMAGES_FOLDER + "/" + devDiskImageVersion + "/DeveloperDiskImage.dmg.signature",
                filePath
        );
    }
}