package com.phonecheck.mount.image;

import com.phonecheck.model.util.SupportFilePath;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import static com.phonecheck.model.constants.FileConstants.*;

@Service
@AllArgsConstructor
public class DiskImageFileService {
    private final SupportFilePath supportFilePath;

    public String getDiskImageFilePath(final String diskImageVersion) {
        return String.join("/",
                supportFilePath.getPaths().getRootFolderPath(),
                DEV_DISK_IMAGES_FOLDER,
                diskImageVersion,
                DEV_DISK_IMAGE_NAME);
    }

    public String getDiskImageSignatureFilePath(final String diskImageVersion) {
        return String.join("/",
                supportFilePath.getPaths().getRootFolderPath(),
                DEV_DISK_IMAGES_FOLDER,
                diskImageVersion,
                DEV_DISK_IMAGE_SIGNATURE_NAME);
    }
}
