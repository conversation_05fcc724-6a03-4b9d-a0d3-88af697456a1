package com.phonecheck.license;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.CloudPhoneCheckApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.dao.model.LicenseCharge;
import com.phonecheck.dao.service.LicenseDBService;
import com.phonecheck.model.cloudapi.DesktopLicenseResponse;
import com.phonecheck.model.cloudapi.IWatchLicenseChargeResponse;
import com.phonecheck.model.constants.LicenseChargeStatus;
import com.phonecheck.model.constants.LicenseErrorConstants;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.ios.LicenseType;
import com.phonecheck.model.store.InMemoryStore;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class LicenseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseService.class);
    private static final Logger LICENSE_LOGGER = LoggerFactory.getLogger("LicenseLogger");
    private static final int LICENSE_CHECK_RETRIES = 5;

    private final InMemoryStore inMemoryStore;
    private final LicenseDBService licenseDBService;
    private final CloudApiRestClient cloudApiRestClient;
    private final CloudPhoneCheckApiRestClient cloudPhoneCheckApiRestClient;
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Fetch all remaining licenses from cloud and set in inMemoryStore
     *
     * @return DesktopLicenseResponse
     */
    public boolean fetchAndSetRemainingLicensesFromCloud() {
        DesktopLicenseResponse desktopLicenseResponse = null;
        int retryCounter = 0;
        do {
            try {
                desktopLicenseResponse = cloudPhoneCheckApiRestClient.getLicenseDetailsForUser();
            } catch (Exception e) {
                LOGGER.error("Error occurred when trying to get license", e);
                LICENSE_LOGGER.error("Error occurred when trying to get license", e);
            }
            if (desktopLicenseResponse != null) {
                break;
            }
            retryCounter++;
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                // do nothing
            }
        } while (retryCounter < LICENSE_CHECK_RETRIES);


        if (desktopLicenseResponse == null) {
            LOGGER.error("License response was empty");
            LICENSE_LOGGER.error("License response was empty");
            return false;
        } else {
            inMemoryStore.getDesktopLicenseResponse().copyFrom(desktopLicenseResponse);
            LOGGER.info("Retrieved license details: {} in retry: {}", desktopLicenseResponse, retryCounter + 1);
            LICENSE_LOGGER.info("Retrieved license details: {} in retry: {}", desktopLicenseResponse, retryCounter + 1);
            return true;
        }
    }

    /**
     * Delete old licenses that were successfully charged
     */
    public void deleteOldChargedLicensesFromDB() {
        final int deletedOldLicensesCount = licenseDBService.deleteOldChargedLicenses();
        LOGGER.info("Deleted {} old license requests from DB that were successfully charged", deletedOldLicensesCount);
        LICENSE_LOGGER.info("Deleted {} old license requests from DB that were successfully charged",
                deletedOldLicensesCount);
    }

    /**
     * Check whether device license are remaining for given user and saves the license charge in local DB
     *
     * @param device target device
     * @return LicenseChargeStatus
     */
    public LicenseChargeStatus checkAndSaveDeviceLicence(final Device device) {
        return checkAndSaveLicenseChargeInDB(device, LicenseType.DEVICE);
    }

    /**
     * Check whether OEM license are remaining for given user and saves the license charge in local DB
     *
     * @param device target device
     * @return LicenseChargeStatus
     */
    public LicenseChargeStatus checkAndSaveOEMLicence(final IosDevice device) {
        return checkAndSaveLicenseChargeInDB(device, LicenseType.OEM);
    }

    /**
     * Check whether App Install license are remaining for given user and saves the license charge in local DB
     *
     * @param device target device
     * @return LicenseChargeStatus
     */
    public LicenseChargeStatus checkAndSaveAppInstallLicence(final Device device) {
        return checkAndSaveLicenseChargeInDB(device, LicenseType.APP_INSTALL);
    }

    /**
     * Check whether Erase license are remaining for given user and saves the license charge in local DB
     *
     * @param device target device
     * @return LicenseChargeStatus
     */
    public LicenseChargeStatus checkAndSaveDeviceEraseLicense(final Device device) {
        return checkAndSaveLicenseChargeInDB(device, LicenseType.ERASE);
    }

    /**
     * Check whether iWatch licenses are remaining for given user and saves the license charge in local DB
     *
     * @param device iWatch license for this device
     * @return number of pending device licenses for given user
     */
    public LicenseChargeStatus checkAndSaveIWatchLicence(final Device device) {
        return checkAndSaveLicenseChargeInDB(device, LicenseType.IWATCH);
    }

    /**
     * Checks whether licenses are pending for the given license type and saves the license charge request in local DB
     *
     * @param device      target device
     * @param licenseType license type
     * @return LicenseChargeStatus
     */
    private LicenseChargeStatus checkAndSaveLicenseChargeInDB(final Device device, final LicenseType licenseType) {
        LOGGER.info("Check and save license charge request for licenseType: {}", licenseType.name());
        LICENSE_LOGGER.info("Check and save license charge request for licenseType: {}", licenseType.name());
        if (checkAndDecrementRemainingLicenseLocally(licenseType)) {
            String model = device.getModel();
            if (StringUtils.isBlank(model)) {
                model = device.getModelNo();
                LOGGER.info("Device model was empty, so using device model number for charging license: {}", model);
                LICENSE_LOGGER.info("Device model was empty, so using device model number for charging license: {}",
                        model);
            }

            // TODO check this
            String licenseIdentifier = device.getLicenseIdentifier();
            if (licenseType == LicenseType.IWATCH) {
                licenseIdentifier = ((IosDevice) device).getIWatchInfo().getSerial();
            }

            licenseDBService.createLicenseChargeInDB(device.getId(), licenseIdentifier, device.getImei(),
                    licenseType, device.getDeviceType(), model);
            return LicenseChargeStatus.PENDING;
        } else {
            return LicenseChargeStatus.LICENSE_EXPIRED;
        }
    }

    /**
     * Checks whether given license type has remaining licenses in inMemory object
     * If remaining licenses are found, then decrement the count
     *
     * @param licenseType given license type
     * @return boolean true: if given licenses are remaining
     */
    private boolean checkAndDecrementRemainingLicenseLocally(final LicenseType licenseType) {
        DesktopLicenseResponse.DesktopLicenseType license =
                getGivenLicenseObject(inMemoryStore.getDesktopLicenseResponse(), licenseType);
        if (license != null) {
            LOGGER.info("Remaining licenses for licenseType: {} are: {}", licenseType, license.getRemaining());
            LICENSE_LOGGER.info("Remaining licenses for licenseType: {} are: {}",
                    licenseType, license.getRemaining());
            return license.checkAndDecrementRemaining();
        }
        return false;
    }

    /**
     * Get license object according to the given licenseType
     *
     * @param desktopLicense overall desktop license object
     * @param licenseType    given license type
     * @return license object
     */
    private DesktopLicenseResponse.DesktopLicenseType getGivenLicenseObject(final DesktopLicenseResponse desktopLicense,
                                                                            final LicenseType licenseType) {
        switch (licenseType) {
            case DEVICE -> {
                return desktopLicense.getLicenseDetailForDevicesChecked();
            }
            case APP_INSTALL -> {
                return desktopLicense.getLicenseDetailForApplicationInstalled();
            }
            case ERASE -> {
                return desktopLicense.getLicenseDetailForDevicesErased();
            }
            case ESN -> {
                return desktopLicense.getLicenseDetailForEsnPerformed();
            }
            case ICLOUD -> {
                return desktopLicense.getLicenseDetailForiCloud();
            }
            case OEM -> {
                return desktopLicense.getLicenseDetailForOEMPerformed();
            }
            case IWATCH -> {
                return desktopLicense.getLicenseDetailForIWatch();
            }
            default -> {
                LOGGER.error("Illegal LicenseType passed with value: {}", licenseType);
                LICENSE_LOGGER.error("Illegal LicenseType passed with value: {}", licenseType);
                return null;
            }
        }
    }

    /**
     * Checks and charges the given license charge request fetched from DB
     *
     * @param pendingLicenseCharge pending license charge
     * @return LicenseChargeStatus
     */
    public LicenseChargeStatus checkAndChargeLicense(final LicenseCharge pendingLicenseCharge) {
        LOGGER.info("Going to charge license for license request: {}", pendingLicenseCharge);
        LICENSE_LOGGER.info("Going to charge license for license request: {}", pendingLicenseCharge);

        DesktopLicenseResponse.DesktopLicenseType desktopLicenseType =
                getGivenLicenseObject(inMemoryStore.getDesktopLicenseResponse(),
                        LicenseType.valueOf(pendingLicenseCharge.getLicenseType()));

        if (desktopLicenseType == null || desktopLicenseType.getRemaining() <= 0) {
            LOGGER.info("License type: {} has no remaining licenses", desktopLicenseType);
            LICENSE_LOGGER.info("License type: {} has no remaining licenses", desktopLicenseType);
            return LicenseChargeStatus.LICENSE_EXPIRED;
        } else {
            return chargeLicense(pendingLicenseCharge);
        }
    }

    /**
     * Charges license by making cloud api call for the given licenseType
     *
     * @param pendingLicenseCharge pending license charge request
     * @return LicenseChargeStatus
     */
    private LicenseChargeStatus chargeLicense(final LicenseCharge pendingLicenseCharge) {
        try {
            if (LicenseType.IWATCH == LicenseType.licenseTypeFromString(pendingLicenseCharge.getLicenseType())) {
                IWatchLicenseChargeResponse licenseChargeResponse =
                        phonecheckApiRestClient.chargeIWatchLicense(
                                inMemoryStore.getUserToken(),
                                pendingLicenseCharge.getLicenseIdentifier(),
                                pendingLicenseCharge.getTransactionId());
                LOGGER.info("Successfully called license charge api for licenseType: {} and " +
                        "received license response: {}", pendingLicenseCharge.getLicenseType(), licenseChargeResponse);
                LICENSE_LOGGER.info("Successfully called license charge api for licenseType: {} and " +
                        "received license response: {}", pendingLicenseCharge.getLicenseType(), licenseChargeResponse);
                //TODO check this condition for license expire
                if (licenseChargeResponse != null &&
                        licenseChargeResponse.isSuccess() &&
                        StringUtils.containsIgnoreCase(licenseChargeResponse.getMessage(), "License Charged") &&
                        licenseChargeResponse.getLicenseBalance() >= 0) {
                    return LicenseChargeStatus.SUCCESS;
                } else {
                    return LicenseChargeStatus.LICENSE_EXPIRED;
                }
            } else {
                final String licenseChargeResponse =
                        cloudApiRestClient.chargeLicense(
                                pendingLicenseCharge.getLicenseIdentifier(),
                                pendingLicenseCharge.getImei(),
                                pendingLicenseCharge.getLicenseType(),
                                pendingLicenseCharge.getDeviceType(),
                                pendingLicenseCharge.getModel(),
                                pendingLicenseCharge.getUserName(),
                                pendingLicenseCharge.getBuildNo(),
                                pendingLicenseCharge.getTesterId(),
                                pendingLicenseCharge.getWarehouseId()
                        );
                LOGGER.info("Successfully called license charge api for licenseType: {} and " +
                        "received license response: {}", pendingLicenseCharge.getLicenseType(), licenseChargeResponse);
                LICENSE_LOGGER.info("Successfully called license charge api for licenseType: {} and " +
                        "received license response: {}", pendingLicenseCharge.getLicenseType(), licenseChargeResponse);
                //TODO check this condition for license expire
                if (StringUtils.isNotBlank(licenseChargeResponse) &&
                        !StringUtils.containsIgnoreCase(licenseChargeResponse, "not found") &&
                        isNumeric(licenseChargeResponse)) {
                    return LicenseChargeStatus.SUCCESS;
                } else {
                    return LicenseChargeStatus.LICENSE_EXPIRED;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred when charging license", e);
            LICENSE_LOGGER.error("Error occurred when charging license", e);
            return LicenseChargeStatus.FAILED;
        }
    }

    /**
     * Returns true if a string is numeric
     *
     * @param str input
     * @return true/false
     */
    private boolean isNumeric(final String str) {
        return str != null && str.matches("-?\\d+");
    }

    /**
     * Retrieves specific message from license count
     *
     * @param licenseCount target int
     * @return error message string
     */
    // TODO We might need to show these errors to UI when required.
    public LicenseErrorConstants getLicenseErrorByCount(final int licenseCount) {
        return switch (licenseCount) {
            case -10 -> LicenseErrorConstants.MASTER_DATE_NOT_STARTED;
            case -20 -> LicenseErrorConstants.MASTER_EXPIRED;
            case -30 -> LicenseErrorConstants.USER_DATE_NOT_STARTED;
            case -40 -> LicenseErrorConstants.USER_EXPIRED;
            default -> LicenseErrorConstants.LICENSE_EXPIRED;
        };
    }
}
