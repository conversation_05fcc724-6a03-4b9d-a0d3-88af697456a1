using System;
using System.Globalization;

namespace printer_util
{
    internal class Program
    {
        static void Main(string[] args)
        {
            // Check for the minimum number of arguments for any command
            if (args.Length < 1)
            {
                Console.WriteLine("Usage: -dpi [PrinterName] or -print [PrinterName] [ImagePath] [Width] [Height] [SizeUnit] [Orientation]");
                return;
            }

            string command = args[0].ToLower();

            // Check the number of arguments based on the command
            switch (command)
            {
                case "-dpi":
                    if (args.Length != 2)
                    {
                        Console.WriteLine("Invalid arguments. Usage for DPI check: -dpi [PrinterName]");
                        return;
                    }
                    HandleDpiCheck(args[1]);
                    break;
                case "-print":
                    if (args.Length != 7)
                    {
                        Console.WriteLine("Invalid arguments. Usage for print: -print [PrinterName] [ImagePath] [Width] [Height] [SizeUnit] [Orientation]");
                        return;
                    }
                    HandlePrintOperation(args);
                    break;
                default:
                    Console.WriteLine("Invalid command. Use -dpi or -print.");
                    break;
            }
        }

        static void HandleDpiCheck(string printerName)
        {
            int dpi = PrinterHelper.GetDPI(printerName);
            if (dpi == -1)
            {
                Console.WriteLine("Custom DPI setting not found for the printer.");
            }
            else
            {
                Console.WriteLine($"DPI of {printerName}: {dpi}");
            }
        }

        static void HandlePrintOperation(string[] args)
        {
            if (args.Length < 7)
            {
                Console.WriteLine("Usage for print: -print [PrinterName] [ImagePath] [Width] [Height] [SizeUnit] [Orientation]");
                return;
            }

            string printerName = args[1];
            string imagePath = args[2];
            float width = float.Parse(args[3], CultureInfo.InvariantCulture);
            float height = float.Parse(args[4], CultureInfo.InvariantCulture);
            string sizeUnit = args[5];
            string orientation = args[6];

            try
            {
                PrinterHelper.Print(printerName, imagePath, width, height, sizeUnit, orientation);
                Console.WriteLine("Image printed successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error while printing: {ex.Message}");
            }
        }
    }
}
