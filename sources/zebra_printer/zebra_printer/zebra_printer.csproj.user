<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartArguments>-print "Zebra GK420t - ZPL" "C:\Users\<USER>\AppData\Local\Temp\label_lpn_portrait-FFYD97KVPLJM-1729698036490.BMP" "2" "4" "inch" "Portrait"</StartArguments>
  </PropertyGroup>
  <PropertyGroup>
    <PublishUrlHistory />
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup>
    <EnableSecurityDebugging>false</EnableSecurityDebugging>
  </PropertyGroup>
</Project>