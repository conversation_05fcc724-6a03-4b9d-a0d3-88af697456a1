using System;
using System.Linq;
using System.Management;
using System.Reflection;
using System.Text;

class Program
{
    static void Main(string[] args)
    {
        // Validate command line arguments and display usage instructions
        if (args.Length < 1)
        {
            Console.WriteLine("Usage: \n" +
                              "PhonecheckUsbInfo -p <Device Udid> \n" +
                              "PhonecheckUsbInfo -l <apple_device_type m, r, w or a (for apple device only)>");
            return;
        }
        else if (!args[0].Contains("-p") && !args[0].Contains("-l"))
        {
            Console.WriteLine("Invalid option. \n" +
                              "Usage: \n" +
                              "PhonecheckUsbInfo -p <Device Udid> \n" +
                              "PhonecheckUsbInfo -l <apple_device_type m, r, w or a (for apple device only)>");
            return;
        }
        else if (args[0].Contains("-p") && args.Length < 2)
        {
            Console.WriteLine("Device Udid required for -p option. \n" +
                              "Usage: \n" +
                              "PhonecheckUsbInfo -p <Device Udid>");
            return;
        }
        else if (args[0].Contains("-l") && args.Length < 2)
        {
            Console.WriteLine("Apple device type (m, r, w or a) required for -l option. \n" +
                              "Usage: \n" +
                              "PhonecheckUsbInfo -l <apple_device_type m, r, w or a (for apple device only)>");
            return;
        }


        if (args[0].Contains("-p"))
        {
            string usbSerialNumber = args[1];

            // Query to retrieve information about the USB device
            string query = $"SELECT * FROM Win32_PnPEntity WHERE DeviceID LIKE '%{usbSerialNumber}%'";

            // Execute query and process results
            foreach (var device in new ManagementObjectSearcher(query).Get().OfType<ManagementObject>())
            {
                string deviceName = device["Name"] as string;

                // Retrieve specific device properties
                var propertyArgs = new object[] { new string[] { "DEVPKEY_Device_LocationPaths" }, null };
                device.InvokeMethod("GetDeviceProperties", propertyArgs);

                var propertyObjects = (ManagementBaseObject[])propertyArgs[1];
                if (propertyObjects.Length > 0)
                {
                    var locationPathData = propertyObjects[0].Properties.OfType<PropertyData>().FirstOrDefault(p => p.Name == "Data");
                    if (locationPathData != null)
                    {
                        Console.WriteLine(((string[])locationPathData.Value)[0]);
                    }
                }
            }
        }
        else if (args[0].Contains("-l"))
        {
            string deviceType = args[1];

            // Handle list device commands
            if (deviceType.Contains("m"))
            {
                PrintDevicesList("Mobile");
            }
            else if (deviceType.Contains("r"))
            {
                PrintDevicesList("Recovery");
            }
            else if (deviceType.Contains("w"))
            {
                PrintDevicesList("Watch");
            }
            else if (deviceType.Contains("a"))
            {
                PrintDevicesList("All");
            }
        }
    }

    /// <summary>
    /// Prints a list of devices based on the specified device type.
    /// </summary>
    /// <param name="requiredDeviceType">Type of the device to list (e.g., 'Mobile', 'Recovery', 'Watch')</param>
    static void PrintDevicesList(string requiredDeviceType)
    {
        string query;

        if (requiredDeviceType.Equals("All"))
        {
            query = $"SELECT * FROM Win32_PnPEntity WHERE Name LIKE '%Apple%'";
        }
        else
        {
            query = $"SELECT * FROM Win32_PnPEntity WHERE Name LIKE '%Apple%' AND Name LIKE '%{requiredDeviceType}%'";
        }

        foreach (var device in new ManagementObjectSearcher(query).Get().OfType<ManagementObject>())
        {
            string name = device["Name"] as string;
            string deviceId = device["DeviceID"] as string;
            string[] idParts = deviceId.Split('\\');

            string deviceIdSuffix = idParts.Last();
            if (!requiredDeviceType.Equals("Recovery") && deviceIdSuffix.Contains("&"))
            {
                continue;
            }

            string derivedAppleDeviceType = null;
            if (name.Contains("Mobile"))
            {
                derivedAppleDeviceType = "mobile";
            }
            else if (name.Contains("Recovery"))
            {
                derivedAppleDeviceType = "recovery";
            }
            else if (name.Contains("Watch"))
            {
                derivedAppleDeviceType = "watch";
            }

            if (derivedAppleDeviceType != null)
            {              
                if(deviceIdSuffix.Length == 24)
                {
                    StringBuilder deviceIdBuilder = new StringBuilder(deviceIdSuffix);
                    deviceIdBuilder.Insert(8, "-");
                    deviceId = deviceIdBuilder.ToString();
                }
                else
                {
                    if(derivedAppleDeviceType == "recovery")
                    {
                        deviceId = deviceIdSuffix;
                    }
                    else
                    {
                        deviceId = deviceIdSuffix.ToLower();
                    }
                }


                Console.WriteLine(derivedAppleDeviceType + ":" + deviceId);
            }
        }
    }
}
