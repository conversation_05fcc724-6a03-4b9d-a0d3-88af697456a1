using System;

namespace BrotherPrinter
{
    class Program
    {
        static void Main(string[] args)
        {
            // Check for the minimum number of arguments for any command
            if (args.Length < 1)
            {
                Console.WriteLine("Usage: -print [PrinterName] [ImagePath] [ConfigFile] [Mono/RB]");
                return;
            }

            string command = args[0].ToLower();

            switch (command)
            {
                case "-print":
                    if (args.Length != 5)
                    {
                        Console.WriteLine("Invalid arguments. Usage for print: -print [PrinterName] [ImagePath] [ConfigFile] [Mono/RB]");
                        return;
                    }
                    HandlePrintOperation(args);
                    break;
                default:
                    Console.WriteLine("Invalid command. Use -print.");
                    break;
            }
        }

        static void HandlePrintOperation(string[] args)
        {
            if (args.Length < 5)
            {
                Console.WriteLine("Usage for print: -print [PrinterName] [ImagePath] [ConfigFile] [Mono/RB]");
                return;
            }

            string printerName = args[1];
            string imagePath = args[2];
            string configFile = args[3];
            bool isMonochrome = string.Equals(args[4], "Mono");

            try
            {
                // Perform the print operation
                bool printStatus = PrinterHelper.Print(printerName, imagePath, configFile, isMonochrome);
                if (printStatus)
                {
                    Console.WriteLine("Image printed successfully.");

                } else
                {
                    Console.WriteLine("Image print failed.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error while printing: {ex.Message}");
            }
        }
    }
}
