<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartArguments>-print "Brother QL-800" "C:\Users\<USER>\Desktop\label_branded_landscape-F2LTX77GHX9G-1708950577954.png" "E:\Projects\Desktop3\res\tools\windows\print\brother\config\4x2-landscape-label.lbx"</StartArguments>
  </PropertyGroup>
  <PropertyGroup>
    <PublishUrlHistory>C:\Users\<USER>\Desktop\</PublishUrlHistory>
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup>
    <EnableSecurityDebugging>false</EnableSecurityDebugging>
  </PropertyGroup>
</Project>