MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L $��        � 0           �:       @    @                       �          `�                           �:  O    @  �                   `     �9  8                                                             H           .text                               `.rsrc   �   @                    @  @.reloc      `      "              @  B                �:      H      #  �                                                       0 R     r  p�  (  
(  
  r)  ps  
(  
(  
t  
o
  9�    rs  p(  
 r�  po  �, r�  p(  
 r�  ps  
z�   o  &rD p(  
 o  &rv p(  
(  
 o  o  o  &,
     
 + 
 ~  
	o  &	o  r� p�  (  
(  
 o  &, �5 +
 r� p(  
   �	 r p	o  
(  
(  
  � + *  A           -       "(  
 *   0 i      �i�, rB p(  
 +O�o   


	r� p(!  
-+&�i��, r� p(  
 +(   +
r� p(  
 + *   0 �      �i�, r� p(  
 +j�
���rT p("  

 	(  , r^ p(  
  +
 r� p(  
   � r� po  
(  
(  
  � *      5 3h   "(  
 *   BSJB         v4.0.30319     l      #~  �     #Strings    �
  �  #US �     #GUID   �  H  #Blob         W?�	    �3                         "      ;                          S      �� )� ��    � v W  � �  �� �� : !f s� -� l� K� �� �� �� \� �� �� �� �� � � � �� ��  �    ^          a�A      ��A   �  ��     �  ��     �  h�     �  M�     �  ��     �  o�     �  s�       8� e   �  ��     x � V�z� V��� V�P� V��� V��� V��� V�X� V��� V��� V��� V�� V�<� V�� � V�>� V�� � V�P� V��� V��� V�� � V�G� P     � ��  �!    ��  �!    � �  P"    � �  �"    ��               �
x�      �
�      Q       ���             ��p      E  
     ���
     ���     ���      ���$      ,       ��� *      8       ��� 0      
       ���7       �   �    %   ,   ,   �            	   7   �   7      �    �    �    �    �    �     
   $ 	 �  �  �
 ) � 1 � 9 � A � I � Q � Y � a � i � q � y � � � � � � � � �  � � � �% � �- � aD � 2J � � � g O � � V � � � Z\ � b � � e � � � �e � q � 1q   �   �   �   �   �   �    �  $ �  ( �  , �  0 �  4 �  8 �  < �  @ �  D �  H �  L �  P �  T � ) � /.  @.  I.  h. # q. + �. 3 �. ; �. C q. K �. S �. [ �. c �. k �. s �� { � � c �� � �� � � � { � � c �� � � � � � { � � � � c � � � � � /� { � � c D� � � � �  � /{ � � n� �#{ � #� #c �#� � @� 8C{ � Cc C� �C� � c{ � c� �� A�{ � �� ��c ��� � �� J�� S � \ � e`� n�� w�� � �  �  �  �  � ' � ) � 3 i w     �;      �               �             � �        _VtblGap3_1 _VtblGap1_12 Int32 _VtblGap1_2 _VtblGap5_2 _VtblGap1_43 _VtblGap4_3 _VtblGap2_24 <Module> GetTypeFromCLSID value__ SetData data mscorlib bpac SetMediaById bpoHighSpeed bpoRfid Guid bpoCutAtEnd kind CreateInstance fitPage get_Message configFile Console docName objName printerName isMonochrome WriteLine bpoSpecialTape Type bpoCutPause DispIdAttribute CompilerGeneratedAttribute GuidAttribute ComEventInterfaceAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute InterfaceTypeAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute TypeIdentifierAttribute CompilationRelaxationsAttribute CoClassAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute bpoContinue BrotherPrinter.exe System.Runtime.Versioning bpoMirroring String imagePath filePath GetPrintedTapeLength bpoCutMark Marshal bpoIdLabel param Program System Enum Boolean Open Main HandlePrintOperation System.Reflection Exception option bpoHighResolution bpoMono bpoStamp PrinterHelper IPrinter get_Printer set_Printer BrotherPrinter SetPrinter ToLower bpoColor Activator .ctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes args Equals PrintOptionConstants IPrintEvents Concat Format IObject GetObject bpoDefault IDocument IPrintEvents_Event EndPrint bpoChainPrint StartPrint copyCount bpoHalfCut bpoTailCut bpoNoCut bpoAutoCut PrintOut bpoQuality op_Equality Empty    'I s   M o n o c h r o m e   :   { 0 }  IB 9 4 0 C 1 0 5 - 7 F 0 1 - 4 6 F E - B F 4 1 - E 0 4 0 B 9 B D A 8 3 D =L o a d e d   c o n f i g F i l e   s u c c e s s f u l l y  i m a g e  ��P r i n t P a g e   E r r o r :   C o u l d   n o t   f i n d   ' i m a g e '   o b j e c t   i n   t h e   c o n f i g   f i l e . 1S e t   i m a g e   i n   i m a g e O b j e c t  %P r i n t e r   n a m e   s e t :    #P r i n t   s t a t u s :   { 0 }  ]P r i n t P a g e   E r r o r :   C o u l d   n o t   l o a d   t h e   c o n f i g F i l e  #P r i n t P a g e   E r r o r :    }U s a g e :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ C o n f i g F i l e ]   [ M o n o / R B ] 
- p r i n t ��I n v a l i d   a r g u m e n t s .   U s a g e   f o r   p r i n t :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ C o n f i g F i l e ]   [ M o n o / R B ] 9I n v a l i d   c o m m a n d .   U s e   - p r i n t . ��U s a g e   f o r   p r i n t :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ C o n f i g F i l e ]   [ M o n o / R B ] 	M o n o  7I m a g e   p r i n t e d   s u c c e s s f u l l y .  'I m a g e   p r i n t   f a i l e d .  -E r r o r   w h i l e   p r i n t i n g :    �����C����?�        M  MM 
(,E   Mu M    E�z\V4��                �                                     @,    0 0  ( , ,        0        TWrapNonExceptionThrows      pc_usb_info      ! Copyright © PhoneCheck 2023  ) $8096c939-dd84-4209-a7be-571b787d4126   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2                                                             ) $6C3849F8-7608-4804-A9BB-241F954A3AA4  _ ZSystem.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089      ) $2274BE1C-597A-4460-A542-BA3741AEDE88  ) $D02892DA-2FB4-42C7-AA62-1EA5A96DAF56  ( bpac.IPrintEventsbpac.IPrintEvents  A $90359d74-b7d9-467f-b938-3883f4cab582bpac.IPrintEvents_Event  ) $7DDC77BD-9916-413C-BB3A-AF6FF5F1150C  C $90359d74-b7d9-467f-b938-3883f4cab582bpac.PrintOptionConstants       ��M�          (:  (                             RSDS�IwZ�:F�9?���e�   C:\Users\<USER>\Workspace\Desktop3\sources\brother_printer\brother_printer\obj\Debug\BrotherPrinter.pdb �:          �:                          �:            _CorExeMain mscoree.dll      �%  @                                                                                                                                                                                                                                                                                     �                  0  �                   H   X@  P          P4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   �   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     p c _ u s b _ i n f o   0   F i l e V e r s i o n     1 . 0 . 0 . 0   F   I n t e r n a l N a m e   B r o t h e r P r i n t e r . e x e     \   L e g a l C o p y r i g h t   C o p y r i g h t   �   P h o n e C h e c k   2 0 2 3   *   L e g a l T r a d e m a r k s         N   O r i g i n a l F i l e n a m e   B r o t h e r P r i n t e r . e x e     8   P r o d u c t N a m e     p c _ u s b _ i n f o   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0                                                                                            0     �:                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      