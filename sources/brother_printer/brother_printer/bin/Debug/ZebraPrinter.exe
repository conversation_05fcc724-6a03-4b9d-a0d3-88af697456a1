MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L Ps��        � " 0           *=       @    @                       �          `�                           �<  O    @  �                   `     4<  8                                                             H           .text   0                           `.rsrc   �   @                     @  @.reloc      `      $              @  B                
=      H     T'  �                                                      0 �      s  

o  
  o  
o  
+;o  
t   o  
�
	,  o  
o  
X[�% o  
-��u  ,o  
 �+ *    Gc     0 �     s  
}  }   s  }   (  
}  s  
{  {  {  {  (  
{  	{  
}  {  	{  
}  �  s  
o  
 s   
o!  
 o"  
o  
 o#  
  � r  po$  
(%  
(&  
  � *        ��   0 :      r%  p('  

, "33�A[(   "33�A[(   s(  
+ *  0 *      ~  r+  p�#  �#  ()  
 o*  
&+ *  0       0r;  p+rM  p
+ *  0 $      (+  

, o,  
 NNVV *0 �      


"  �@7
"  �@�+, 
 +N"  �@�
	,B "  �@Y(-  
"  �BZ"   @[iX
"   @3
"  �?�+, 
 +   s.  
+ *   0 '      l(/  
k
��, l(0  
k++ *"(1  
 *0       s2  
%ra  prM  po3  
 %ri  prM  po3  
 %rq  prM  po3  
 %ry  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  prM  po3  
 %r�  pr;  po3  
 %r�  pr;  po3  
 %r�  pr;  po3  
 %r�  pr;  po3  
 �  * 0 �   	   �i�, r�  p(&  
 8�   �o4  


	r� p('  
-r� p('  
-*+N�i��, r� p(&  
 +>�(   +3�i��, r@ p(&  
 +(
   +
r p(&  
 + *   0 9   
   (  
�, rM p(&  
  + r� p�&  ()  
(&  
  *   0 }      �i�, r� p(&  
 +a�
��(5  
(6  
�(5  
(6  

� 	(   rf p(&  
  � r� po$  
(%  
(&  
  � *       D _   "(1  
 *"(1  
 *"(1  
 * 0 �      {  {  {  {  (  
{  {  {  {  (  {  {  |  {  |  (   {  {  {  {  (  %{7  
{8  

o9  
�,f o9  
o:  
 o9  
o;  
 o9  
o<  
 o9  
{  	{  {  "  �BZi{  {  "  �BZis=  
o>  
  * BSJB         v4.0.30319     l   �  #~  �  T  #Strings    L
  �  #US      #GUID   (  �  #Blob         W	   �3      -               >                       �      8 n8 5 X   ]� �� �� U� !� :� t� I ' �� �� �@ ` � 
 k� ��
 "� }@
 �� S @ @
 e� 8
 {�K �  
 �
 i�
 ��
 �� �@ �@ �@
 �� �@ M @ 4� R@
 ��
 | 
 -| 
 C| 
 ��    s          �+A      8+A       A    "   A   1 � i q �q kt = xP     � � | �     � � �!    � �� "    � a�
 T"    � �� x"    � "� �"    � � 8#    � �  k#    ��  t#    ��� �$    � G� L%    � �  �%    � L� 0&    ��  9&    ��  B&    ��  L&    �  �    �   �   �      �   �      �   �      �      �   k      �   y   �      �   K   �   �   �   b   �	 �  �  �
 ) � 1 � 9 � A � I � Q � Y � a � i � q � y � � � � � � � � �* � �/ � 4 � � 8 � � = � � = � A � � � �V � �  7 c  m g � �k � Sq � � � w � g~ �  � q� 	�� �� 	9�  �� 	��  �� 	E� � @� )��  �� )� )�� � �  �  � � 	�� 9�� 7 c  m g � �+I1I)8I??i�FIaN.  �.  �.  �. # �. + . 3 . ; . C �. K . S . [ . c .. k X. s e� { �� { � E � � � � � � � 
#\ � � �               �             W�                `�           <>c__DisplayClass2_0 <Print>b__0 <>c__DisplayClass2_1 Item1 CS$<>8__locals1 Int32 ValueTuple`2 Dictionary`2 Item2 <Module> System.Drawing.Drawing2D GetDPI get_X ZEBRA_PAPER_ROLL_ORIENTATION_DICTIONARY get_Y mscorlib System.Collections.Generic Add get_Kind PrinterResolutionKind set_SmoothingMode set_InterpolationMode set_PixelOffsetMode add_PrintPage DrawImage image get_Message IDisposable Rectangle Single FromFile Console set_PrinterName printerName WriteLine RotateFlipType get_InvariantCulture Dispose Parse Truncate CompilerGeneratedAttribute GuidAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute TryGetValue ZebraPrinter.exe Ceiling System.Runtime.Versioning String System.Drawing.Printing System.Drawing Math imagePath width HandleDpiCheck CeilIfDecimal printer_util Program System Main HandlePrintOperation GetPaperRollOrientation paperRollOrientation GetDocumentOrientation printDocumentOrientation System.Globalization System.Reflection PrinterResolutionCollection CalculatePrintPosition Exception PrinterResolution CultureInfo RotateFlip number IFormatProvider sender PrintPageEventHandler set_PrintController StandardPrintController PrinterHelper ZebraPrinter ToLower IEnumerator GetEnumerator .ctor .cctor Abs get_Graphics System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes get_PrinterSettings PrintPageEventArgs args ConvertDimensions System.Collections get_PrinterResolutions Concat Format Object height sizeUnit unit PrintDocument get_Current Print MoveNext RotateImageIfNecessary op_Equality op_Inequality   #P r i n t P a g e   E r r o r :    m m  { 0 } x { 1 }  p o r t r a i t  l a n d s c a p e  4 x 2  2 x 4  3 x 2  2 x 3  1 x 3  3 x 1  
4 x 1 . 2 5  
1 . 2 5 x 4  3 . 5 x 1 . 5  1 . 5 x 3 . 5  1 x 2  2 x 1  4 x 6  6 x 4  4 x 1  1 x 4  ��U s a g e :   - d p i   [ P r i n t e r N a m e ]   o r   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ W i d t h ]   [ H e i g h t ]   [ S i z e U n i t ] 	- d p i 
- p r i n t uI n v a l i d   a r g u m e n t s .   U s a g e   f o r   D P I   c h e c k :   - d p i   [ P r i n t e r N a m e ] ��I n v a l i d   a r g u m e n t s .   U s a g e   f o r   p r i n t :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ W i d t h ]   [ H e i g h t ]   [ S i z e U n i t ] II n v a l i d   c o m m a n d .   U s e   - d p i   o r   - p r i n t . [C u s t o m   D P I   s e t t i n g   n o t   f o u n d   f o r   t h e   p r i n t e r .  D P I   o f   { 0 } :   { 1 }  ��U s a g e   f o r   p r i n t :   - p r i n t   [ P r i n t e r N a m e ]   [ I m a g e P a t h ]   [ W i d t h ]   [ H e i g h t ]   [ S i z e U n i t ] 7I m a g e   p r i n t e d   s u c c e s s f u l l y .  -E r r o r   w h i l e   p r i n t i n g :    F���O�I�#�U�]       	IMQU  q  M    u    Y]a e]   y ��  I    	]    E   ��
] ] 


a  �� ��  �� �� �� ��  e���z\V4���?_�
:Ee   ]  e
 ]    m        TWrapNonExceptionThrows      pc_usb_info      ! Copyright © PhoneCheck 2023  ) $8096c939-dd84-4209-a7be-571b787d4126   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2       �<R�       j   l<  l                             RSDSO쉿TPN�\4��A�R   E:\Projects\Desktop3\sources\printer_util\printer_util\obj\Debug\ZebraPrinter.pdb �<          =                          
=            _CorExeMain mscoree.dll       �%  @                                                                                                                                                                                                                                     �                  0  �                   H   X@  H          H4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   �   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     p c _ u s b _ i n f o   0   F i l e V e r s i o n     1 . 0 . 0 . 0   B   I n t e r n a l N a m e   Z e b r a P r i n t e r . e x e     \   L e g a l C o p y r i g h t   C o p y r i g h t   �   P h o n e C h e c k   2 0 2 3   *   L e g a l T r a d e m a r k s         J   O r i g i n a l F i l e n a m e   Z e b r a P r i n t e r . e x e     8   P r o d u c t N a m e     p c _ u s b _ i n f o   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0                                                                                                    0     ,=                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      