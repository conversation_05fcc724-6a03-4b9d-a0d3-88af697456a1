<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Configuration.ConfigurationManager</name>
    </assembly>
    <members>
        <member name="T:System.Configuration.DictionarySectionHandler">
             <summary>
             Simple dictionary config factory
             config is a dictionary mapping key-&gt;value
            
                 &lt;add key="name" value="text"&gt;  sets key=text
                 &lt;remove key="name"&gt;            removes the definition of key
                 &lt;clear&gt;                        removes all definitions
            
             </summary>
        </member>
        <member name="M:System.Configuration.DictionarySectionHandler.Create(System.Object,System.Object,System.Xml.XmlNode)">
            <summary>
            Given a partially composed config object (possibly null)
            and some input from the config system, return a
            further partially composed config object
            </summary>
        </member>
        <member name="P:System.Configuration.DictionarySectionHandler.KeyAttributeName">
            <summary>
            Make the name of the key attribute configurable by derived classes.
            </summary>
        </member>
        <member name="P:System.Configuration.DictionarySectionHandler.ValueAttributeName">
            <summary>
            Make the name of the value attribute configurable by derived classes.
            </summary>
        </member>
        <member name="T:System.Configuration.Internal.DummyDisposable">
            <summary>
            Used to satisfy legacy interfaces.
            </summary>
        </member>
        <member name="M:System.Configuration.Internal.WriteFileContext.ReplaceFile(System.String,System.String)">
            <summary>
            Replace one file with another, retrying if locked.
            </summary>
        </member>
        <member name="T:System.Configuration.IPersistComponentSettings">
            <summary>
            Components should implement this interface if they want to persist custom settings
            in a hosting application. This interface allows the application author to tell a control
            whether to persist, when to load, save etc.
            </summary>
        </member>
        <member name="P:System.Configuration.IPersistComponentSettings.SaveSettings">
            <summary>
            Indicates to the implementor that settings should be persisted.
            </summary>
        </member>
        <member name="P:System.Configuration.IPersistComponentSettings.SettingsKey">
            <summary>
            Unique key that identifies an individual instance of a settings group(s). This key is needed
            to identify which instance of a component owns a given group(s) of settings. Usually, the component
            will frame its own key, but this property allows the hosting application to override it if necessary.
            </summary>
        </member>
        <member name="M:System.Configuration.IPersistComponentSettings.LoadComponentSettings">
            <summary>
            Tells the component to load its settings.
            </summary>
        </member>
        <member name="M:System.Configuration.IPersistComponentSettings.SaveComponentSettings">
            <summary>
            Tells the component to save its settings.
            </summary>
        </member>
        <member name="M:System.Configuration.IPersistComponentSettings.ResetComponentSettings">
            <summary>
            Tells the component to reset its settings. Typically, the component can call Reset on its settings class(es).
            </summary>
        </member>
        <member name="T:System.Configuration.ApplicationSettingsBase">
            <summary>
            Base settings class for client applications.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.#ctor">
            <summary>
            Default constructor without a concept of "owner" component.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.#ctor(System.ComponentModel.IComponent)">
            <summary>
            Constructor that takes an IComponent. The IComponent acts as the "owner" of this settings class. One
            of the things we do is query the component's site to see if it has a SettingsProvider service. If it
            does, we allow it to override the providers specified in the metadata.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.#ctor(System.String)">
            <summary>
            Convenience overload that takes the settings key
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.#ctor(System.ComponentModel.IComponent,System.String)">
            <summary>
            Convenience overload that takes the owner component and settings key.
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.Context">
            <summary>
            The Context to pass on to the provider. Currently, this will just contain the settings group name.
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.Properties">
            <summary>
            The SettingsBase class queries this to get the collection of SettingsProperty objects. We reflect over
            the properties defined on the current object's type and use the metadata on those properties to form
            this collection.
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.PropertyValues">
            <summary>
            Just overriding to add attributes.
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.Providers">
            <summary>
            Provider collection
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.SettingsKey">
            <summary>
            Derived classes should use this to uniquely identify separate instances of settings classes.
            </summary>
        </member>
        <member name="E:System.Configuration.ApplicationSettingsBase.PropertyChanged">
            <summary>
            Fires when the value of a setting is changed. (INotifyPropertyChanged implementation.)
            </summary>
        </member>
        <member name="E:System.Configuration.ApplicationSettingsBase.SettingChanging">
            <summary>
            Fires when the value of a setting is about to change. This is a cancellable event.
            </summary>
        </member>
        <member name="E:System.Configuration.ApplicationSettingsBase.SettingsLoaded">
            <summary>
            Fires when settings are retrieved from a provider. It fires once for each provider.
            </summary>
        </member>
        <member name="E:System.Configuration.ApplicationSettingsBase.SettingsSaving">
            <summary>
            Fires when Save() is called. This is a cancellable event.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.GetPreviousVersion(System.String)">
            <summary>
            Used in conjunction with Upgrade - retrieves the previous value of a setting from the provider.
            Provider must implement IApplicationSettingsProvider to support this.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.OnPropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Fires the PropertyChanged event.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.OnSettingChanging(System.Object,System.Configuration.SettingChangingEventArgs)">
            <summary>
            Fires the SettingChanging event.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.OnSettingsLoaded(System.Object,System.Configuration.SettingsLoadedEventArgs)">
            <summary>
            Fires the SettingsLoaded event.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.OnSettingsSaving(System.Object,System.ComponentModel.CancelEventArgs)">
            <summary>
            Fires the SettingsSaving event.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.Reload">
            <summary>
            Causes a reload to happen on next setting access, by clearing the cached values.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.Reset">
            <summary>
            Calls Reset on the providers.
            Providers must implement IApplicationSettingsProvider to support this.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.Save">
            <summary>
            Overridden from SettingsBase to support validation event.
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.Item(System.String)">
            <summary>
            Overridden from SettingsBase to support validation event.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.Upgrade">
            <summary>
            Called when the app is upgraded so that we can instruct the providers to upgrade their settings.
            Providers must implement IApplicationSettingsProvider to support this.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.CreateSetting(System.Reflection.PropertyInfo)">
            <summary>
            Creates a SettingsProperty object using the metadata on the given property
            and returns it.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.EnsureInitialized">
            <summary>
            Ensures this class is initialized. Initialization involves reflecting over properties and building
            a list of SettingsProperty's.
            </summary>
        </member>
        <member name="P:System.Configuration.ApplicationSettingsBase.Initializer">
            <summary>
            Returns a SettingsProperty used to initialize settings. We initialize a setting with values
            derived from class level attributes, if present. Otherwise, we initialize to
            reasonable defaults.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.GetPropertiesForProvider(System.Configuration.SettingsProvider)">
            <summary>
            Gets all the settings properties for this provider.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.GetPropertyValue(System.String)">
            <summary>
            Retrieves the value of a setting. We need this method so we can fire the SettingsLoaded event
            when settings are loaded from the providers.Ideally, this should be fired from SettingsBase,
            but unfortunately that will not happen in Whidbey. Instead, we check to see if the value has already
            been retrieved. If not, we fire the load event, since we expect SettingsBase to load all the settings
            from this setting's provider.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.IsFirstRunOfClickOnceApp">
            <summary>
            Returns true if this is a clickonce deployed app and this is the first run of the app
            since deployment or last upgrade.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.IsClickOnceDeployed(System.AppDomain)">
            <summary>
            Returns true if this is a clickonce deployed app.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.SettingsFilter(System.Reflection.PropertyInfo[])">
            <summary>
            Only those settings class properties that have a SettingAttribute on them are
            treated as settings. This routine filters out other properties.
            </summary>
        </member>
        <member name="M:System.Configuration.ApplicationSettingsBase.ResetProviders">
            <summary>
            Resets the provider collection. This needs to be called when providers change after
            first being set.
            </summary>
        </member>
        <member name="T:System.Configuration.ApplicationScopedSettingAttribute">
            <summary>
            Indicates that a setting is to be stored on a per-application basis.
            </summary>
        </member>
        <member name="T:System.Configuration.AppSettingsReader">
            <summary>
                The AppSettingsReader class provides a wrapper for System.Configuration.ConfigurationManager.AppSettings
                which provides a single method for reading values from the config file of a particular type.
            </summary>
        </member>
        <member name="M:System.Configuration.AppSettingsReader.GetValue(System.String,System.Type)">
            <summary>
            Gets the value for specified key from ConfigurationManager.AppSettings, and returns
            an object of the specified type containing the value from the config file.  If the key
            isn't in the config file, or if it is not a valid value for the given type, it will
            throw an exception with a descriptive message so the user can make the appropriate
            change
            </summary>
        </member>
        <member name="M:System.Configuration.BaseConfigurationRecord.AllowExeDefinitionToEnum(System.String,System.Configuration.XmlUtil)">
            <summary>
            Translate an ExeDefinition string from the Declaration in a file
            to the appropriate enumeration
            </summary>
            <param name="allowExeDefinition">string representation of value</param>
            <param name="xmlUtil">[optional] - can provide better error</param>
        </member>
        <member name="M:System.Configuration.BaseConfigurationRecord.AddImplicitSections(System.Collections.Hashtable)">
            <summary>
            Add implicit sections to the specified factory list.
            </summary>
            <param name="factoryList">The factory list to add to. If null, adds to the current record's factory list.</param>
        </member>
        <member name="T:System.Configuration.ClientSettingsSection">
            <summary>
            ConfigurationSection class for sections that store client settings.
            </summary>
        </member>
        <member name="T:System.Configuration.ClientSettingsStore">
             <summary>
             This class abstracts the details of config system away from the LocalFileSettingsProvider. It talks to
             the configuration API and the relevant Sections to read and write settings.
             It understands sections of type ClientSettingsSection.
            
             NOTE: This API supports reading from app.exe.config and user.config, but writing only to
                   user.config.
             </summary>
        </member>
        <member name="T:System.Configuration.ClientSettingsStore.ClientSettingsConfigurationHost">
            <summary>
            A private configuration host that we use to write settings to config. We need this so we
            can enforce a quota on the size of stuff written out.
            </summary>
        </member>
        <member name="P:System.Configuration.ClientSettingsStore.ClientSettingsConfigurationHost.ClientHost">
            <summary>
            ClientConfigurationHost implements this - a way of getting some info from it without
            depending too much on its internals.
            </summary>
        </member>
        <member name="M:System.Configuration.ClientSettingsStore.ClientSettingsConfigurationHost.InitForConfiguration(System.String@,System.String@,System.String@,System.Configuration.Internal.IInternalConfigRoot,System.Object[])">
            <summary>
            We delegate this to the ClientConfigurationHost. The only thing we need to do here is to
            build a configPath from the ConfigurationUserLevel we get passed in.
            </summary>
        </member>
        <member name="M:System.Configuration.ClientSettingsStore.ClientSettingsConfigurationHost.OpenStreamForRead(System.String)">
            <summary>
            If the stream we are asked for represents a config file that we know about, we ask
            the host to assert appropriate permissions.
            </summary>
        </member>
        <member name="M:System.Configuration.ClientSettingsStore.ClientSettingsConfigurationHost.WriteCompleted(System.String,System.Boolean,System.Object)">
            <summary>
            If this is a stream that represents a user.config file that we know about, we ask
            the host to assert appropriate permissions.
            </summary>
        </member>
        <member name="T:System.Configuration.StoredSetting">
            <summary>
            The ClientSettingsStore talks to the LocalFileSettingsProvider through a dictionary which maps from
            setting names to StoredSetting structs. This struct contains the relevant information.
            </summary>
        </member>
        <member name="T:System.Configuration.ConfigurationCollectionAttribute">
            <summary>
            Used on classes derived from ConfigurationElementCollection. Specifies the collection item type and
            verbs used for add/remove/clear.
            </summary>
        </member>
        <member name="T:System.Configuration.ConfigurationException">
            <summary>
                A config exception can contain a filename (of a config file) and a line number (of the location in the file in
                which a
                problem was encountered). Section handlers should throw this exception (or subclasses) together with filename and
                line
                number information where possible.
            </summary>
        </member>
        <member name="T:System.Configuration.ConfigurationFileMap">
            <summary>
            Holds the configuration file mapping for machine.config. It is the base class for
            ExeConfigurationFileMap and WebConfigurationFileMap.
            </summary>
        </member>
        <member name="M:System.Configuration.ConfigurationManager.PreloadConfiguration(System.Configuration.Configuration)">
            <summary>
                Recursively loads configuration section groups and sections belonging to a configuration object.
            </summary>
        </member>
        <member name="T:System.Configuration.DefaultSettingValueAttribute">
            <summary>
            Indicates to the provider what default value to use for this setting when no stored value
            is found. The value should be encoded into a string and is interpreted based on the SerializeAs
            value for this setting. For example, if SerializeAs is Xml, the default value will be
            "stringified" Xml.
            </summary>
        </member>
        <member name="M:System.Configuration.DefaultSettingValueAttribute.#ctor(System.String)">
            <summary>
            Constructor takes the default value as string.
            </summary>
        </member>
        <member name="P:System.Configuration.DefaultSettingValueAttribute.Value">
            <summary>
            Default value.
            </summary>
        </member>
        <member name="P:System.Configuration.ElementInformation.LineNumber">
            <summary>
            The line number or 0 if no source.
            </summary>
        </member>
        <member name="F:System.Configuration.ExceptionAction.NonSpecific">
            <summary>
            Not specific to a particular section, nor a global schema error
            </summary>
        </member>
        <member name="F:System.Configuration.ExceptionAction.Local">
            <summary>
            Error specific to a particular section
            </summary>
        </member>
        <member name="F:System.Configuration.ExceptionAction.Global">
            <summary>
            Error in the global (file) schema
            </summary>
        </member>
        <member name="T:System.Configuration.IApplicationSettingsProvider">
            <summary>
            This interface is an extension to SettingsProvider that a provider can implement
            to support additional functionality for settings classes that derive from ApplicationSettingsBase.
            </summary>
        </member>
        <member name="M:System.Configuration.IApplicationSettingsProvider.GetPreviousVersion(System.Configuration.SettingsContext,System.Configuration.SettingsProperty)">
            <summary>
            Retrieves the previous value of a given SettingsProperty. This is used in conjunction with Upgrade.
            </summary>
        </member>
        <member name="M:System.Configuration.IApplicationSettingsProvider.Reset(System.Configuration.SettingsContext)">
            <summary>
            Resets all settings to their "default" values.
            </summary>
        </member>
        <member name="M:System.Configuration.IApplicationSettingsProvider.Upgrade(System.Configuration.SettingsContext,System.Configuration.SettingsPropertyCollection)">
            <summary>
            Indicates to the provider that the app has been upgraded. This is a chance for the provider to upgrade
            its stored settings as appropriate.
            </summary>
        </member>
        <member name="T:System.Configuration.IConfigurationSectionHandler">
            <summary>
                The IConfigurationSectionHandler interface defines the contract that all configuration section handlers
                must implement in order to participate in the resolution of configuration settings.
                Composes and creates config objects.
                This interface is implemented by config providers. Classes implementing IConfigurationSectionHandler
                define the rules for cooking XML config into usable objects. The cooked objects can be of arbitrary
                type.
                Configuration is composable (e.g., config in a child directory is layered over config in a parent
                directory), so, IConfigurationSectionHandler is supplied with the parent config as well as any number of
                XML fragments.
            </summary>
        </member>
        <member name="M:System.Configuration.IConfigurationSectionHandler.Create(System.Object,System.Object,System.Xml.XmlNode)">
            <summary>
                The function is responsible for inspecting "section", "context", and "parent", and creating
                a config object.
                Note that "parent" is guaranteed to be an object that was returned from a Create call on the
                same IConfigurationSectionHandler implementation. (E.g., if Create returns a Hashtable, then "parent"
                is always a Hashtable if it's non-null.)
                Returned objects must be immutable. In particular, it's important that the "parent" object
                being passed in is not altered: if a modification must be made, then it must be cloned before
                it is modified.
            </summary>
            <param name="parent">the object inherited from parent path</param>
            <param name="configContext">reserved, in ASP.NET used to convey virtual path of config being evaluated</param>
            <param name="section">the xml node rooted at the section to handle</param>
            <returns>a new config object</returns>
        </member>
        <member name="M:System.Configuration.IgnoreSectionHandler.Create(System.Object,System.Object,System.Xml.XmlNode)">
            <summary>
                Given a partially composed config object (possibly null) and some input from the config system,
                return a further partially composed config object.
            </summary>
        </member>
        <member name="T:System.Configuration.ISettingsProviderService">
            <summary>
            The IPersistComponentSettings interface enables components hosted in an application to persist their
            settings in a manner transparent to the application. However, in some cases, the application may want to
            override the provider(s) specified by a component. For example, at design time, we may want to persist
            settings differently. This service enables this scenario. The ApplicationSettingsBase class queries this
            service from the owner component's site.
            </summary>
        </member>
        <member name="M:System.Configuration.ISettingsProviderService.GetSettingsProvider(System.Configuration.SettingsProperty)">
            <summary>
            Queries the service whether it wants to override the provider for the given SettingsProperty. If it
            doesn't want to, it should return null, in which the provider will remain unchanged.
            </summary>
        </member>
        <member name="T:System.Configuration.LocalFileSettingsProvider">
            <summary>
            This is a provider used to store configuration settings locally for client applications.
            </summary>
        </member>
        <member name="P:System.Configuration.LocalFileSettingsProvider.ApplicationName">
            <summary>
            Abstract SettingsProvider property.
            </summary>
        </member>
        <member name="P:System.Configuration.LocalFileSettingsProvider.Store">
            <summary>
            We maintain a single instance of the ClientSettingsStore per instance of provider.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Abstract ProviderBase method.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.GetPropertyValues(System.Configuration.SettingsContext,System.Configuration.SettingsPropertyCollection)">
            <summary>
            Abstract SettingsProvider method
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.SetPropertyValues(System.Configuration.SettingsContext,System.Configuration.SettingsPropertyValueCollection)">
            <summary>
                Abstract SettingsProvider method
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.Reset(System.Configuration.SettingsContext)">
            <summary>
                Implementation of IClientSettingsProvider.Reset. Resets user scoped settings to the values
                in app.exe.config, does nothing for app scoped settings.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.Upgrade(System.Configuration.SettingsContext,System.Configuration.SettingsPropertyCollection)">
            <summary>
               Implementation of IClientSettingsProvider.Upgrade.
               Tries to locate a previous version of the user.config file. If found, it migrates matching settings.
               If not, it does nothing.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.GetPreviousVersion(System.Configuration.SettingsContext,System.Configuration.SettingsProperty)">
            <summary>
            Implementation of IClientSettingsProvider.GetPreviousVersion.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.GetPreviousConfigFileName(System.Boolean)">
            <summary>
            Locates the previous version of user.config, if present. The previous version is determined
            by walking up one directory level in the *UserConfigPath and searching for the highest version
            number less than the current version.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.GetSectionName(System.Configuration.SettingsContext)">
            <summary>
            Gleans information from the SettingsContext and determines the name of the config section.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.GetSettingValuesFromFile(System.String,System.String,System.Boolean,System.Configuration.SettingsPropertyCollection)">
            <summary>
            Retrieves the values of settings from the given config file (as opposed to using
            the configuration for the current context)
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.IsRoamingSetting(System.Configuration.SettingsProperty)">
            <summary>
            Indicates whether a setting is roaming or not.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.IsUserSetting(System.Configuration.SettingsProperty)">
            <summary>
            This provider needs settings to be marked with either the UserScopedSettingAttribute or the
            ApplicationScopedSettingAttribute. This method determines whether this setting is user-scoped
            or not. It will throw if none or both of the attributes are present.
            </summary>
        </member>
        <member name="M:System.Configuration.LocalFileSettingsProvider.Upgrade(System.Configuration.SettingsContext,System.Configuration.SettingsPropertyCollection,System.Boolean)">
            <summary>
            Private version of upgrade that uses isRoaming to determine which config file to use.
            </summary>
        </member>
        <member name="T:System.Configuration.NameValueFileSectionHandler">
            <summary>
            This section handler allows &lt;appSettings file="user.config" /&gt;
            The file pointed to by the file= attribute is read as if it is
            an appSettings section in the config file.
            Note: the user.config file must have its root element match the
            section referring to it.  So if appSettings has a file="user.config"
            attribute the root element in user.config must also be named appSettings.
            </summary>
        </member>
        <member name="T:System.Configuration.NameValueSectionHandler">
            <summary>
            Simple dictionary config factory
            </summary>
        </member>
        <member name="P:System.Configuration.NameValueSectionHandler.KeyAttributeName">
            <summary>
               <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="P:System.Configuration.NameValueSectionHandler.ValueAttributeName">
            <summary>
               <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="P:System.Configuration.PropertyInformation.LineNumber">
            <summary>
            Line number or 0 if there is no source.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingAttribute">
            <summary>
            Use this attribute to mark properties on a settings class that are to be treated
            as settings. ApplicationSettingsBase will ignore all properties not marked with
            this or a derived attribute.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingChangingEventArgs">
            <summary>
            Event args for the SettingChanging event.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingChangingEventHandler">
            <summary>
            Event handler for the SettingChanging event.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsDescriptionAttribute">
            <summary>
            Description for a particular setting.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsDescriptionAttribute.#ctor(System.String)">
            <summary>
            Constructor takes the description string.
            </summary>
        </member>
        <member name="P:System.Configuration.SettingsDescriptionAttribute.Description">
            <summary>
            Description string.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsGroupDescriptionAttribute">
            <summary>
            Description for a particular settings group.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsGroupDescriptionAttribute.#ctor(System.String)">
            <summary>
            Constructor takes the description string.
            </summary>
        </member>
        <member name="P:System.Configuration.SettingsGroupDescriptionAttribute.Description">
            <summary>
            Description string.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsGroupNameAttribute">
            <summary>
            Name of a particular settings group.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsGroupNameAttribute.#ctor(System.String)">
            <summary>
            Constructor takes the group name.
            </summary>
        </member>
        <member name="P:System.Configuration.SettingsGroupNameAttribute.GroupName">
            <summary>
            Name of the settings group.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsLoadedEventArgs">
            <summary>
            Event args for the SettingLoaded event.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsLoadedEventHandler">
            <summary>
            Event handler for the SettingsLoaded event.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsManageabilityAttribute">
            <summary>
            Indicates the SettingsManageability for a group of/individual setting.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsManageabilityAttribute.#ctor(System.Configuration.SettingsManageability)">
            <summary>
            Constructor takes a SettingsManageability enum value.
            </summary>
        </member>
        <member name="P:System.Configuration.SettingsManageabilityAttribute.Manageability">
            <summary>
            SettingsManageability value to use
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsProviderAttribute">
            <summary>
            Indicates the provider associated with a group of/individual setting.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsProviderAttribute.#ctor(System.String)">
            <summary>
            Constructor takes the provider's assembly qualified type name.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsProviderAttribute.#ctor(System.Type)">
            <summary>
            Constructor takes the provider's type.
            </summary>
        </member>
        <member name="P:System.Configuration.SettingsProviderAttribute.ProviderTypeName">
            <summary>
            Type name of the provider
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsSavingEventHandler">
            <summary>
            Event handler for the SettingsSaving event.
            </summary>
        </member>
        <member name="T:System.Configuration.SettingsSerializeAsAttribute">
            <summary>
            Indicates the SettingsSerializeAs for a group of/individual setting.
            </summary>
        </member>
        <member name="M:System.Configuration.SettingsSerializeAsAttribute.#ctor(System.Configuration.SettingsSerializeAs)">
            <summary>
            Constructor takes a SettingsSerializeAs enum value.
            </summary>
        </member>
        <member name="P:System.Configuration.SettingsSerializeAsAttribute.SerializeAs">
            <summary>
            SettingsSerializeAs value to use
            </summary>
        </member>
        <member name="T:System.Configuration.SingleTagSectionHandler">
             Single-tag dictionary config factory
            
             Use for tags of the form: &lt;MySingleTag key1="value1" ... keyN="valueN"/&gt;
        </member>
        <member name="M:System.Configuration.SingleTagSectionHandler.Create(System.Object,System.Object,System.Xml.XmlNode)">
             Create
            
             Given a partially composed config object (possibly null)
             and some input from the config system, return a
             further partially composed config object
        </member>
        <member name="T:System.Configuration.SpecialSetting">
            <summary>
            Indicates settings that are to be treated "specially".
            </summary>
        </member>
        <member name="T:System.Configuration.SpecialSettingAttribute">
            <summary>
            Indicates the SpecialSetting for a group of/individual settings.
            </summary>
        </member>
        <member name="P:System.Configuration.SpecialSettingAttribute.SpecialSetting">
            <summary>
            SpecialSetting value to use
            </summary>
        </member>
        <member name="M:System.Configuration.TypeUtil.GetImplicitType(System.String)">
            <summary>
            Find type references that used to be found without assembly names
            </summary>
        </member>
        <member name="T:System.Configuration.UserScopedSettingAttribute">
            <summary>
                Indicates that a setting is to be stored on a per-user basis.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.SkipToNextElement">
            <summary>
            Skip this element and its children, then read to next start element,
            or until we hit end of file.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.StrictReadToNextElement(System.Configuration.ExceptionAction)">
            <summary>
            Read to the next start element, and verify that all XML nodes read are permissible.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.StrictSkipToNextElement(System.Configuration.ExceptionAction)">
            <summary>
            Skip this element and its children, then read to next start element, or until we hit
            end of file. Verify that nodes that are read after the skipped element are permissible.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.StrictSkipToOurParentsEndElement(System.Configuration.ExceptionAction)">
            <summary>
            Skip until we hit the end element for our parent, and verify that nodes at the
            parent level are permissible.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.VerifyIgnorableNodeType(System.Configuration.ExceptionAction)">
            <summary>
            Add an error if the node type is not permitted by the configuration schema.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.VerifyNoUnrecognizedAttributes(System.Configuration.ExceptionAction)">
            <summary>
            Add an error if there are attributes that have not been examined, and are therefore unrecognized.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.VerifyRequiredAttribute(System.Object,System.String,System.Configuration.ExceptionAction)">
            <summary>
            Add an error if the retrieved attribute is null, and therefore not present.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.VerifyAndGetBooleanAttribute(System.Configuration.ExceptionAction,System.Boolean,System.Boolean@)">
            <summary>
            Verify and Retrieve the Boolean Attribute.  If it is not
            a valid value then log an error and set the value to a given default.
            </summary>
        </member>
        <member name="M:System.Configuration.XmlUtil.FormatXmlElement(System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Format an Xml element to be written to the config file.
            </summary>
            <param name="xmlElement">the element</param>
            <param name="linePosition">start position of the element</param>
            <param name="indent">indent for each depth</param>
            <param name="skipFirstIndent">skip indent for the first element?</param>
            <returns></returns>
        </member>
        <member name="T:System.Configuration.NoSettingsVersionUpgradeAttribute">
            <summary>
            Indicates that the provider should disable any logic that gets invoked when an application
            upgrade is detected.
            </summary>
        </member>
        <member name="T:System.Security.IdentityHelper">
            <summary>
            Helper class for getting identity hashes for types that used
            to live in Assembly Evidence.
            </summary>
        </member>
        <member name="M:System.Security.IdentityHelper.GetNormalizedUriHash(System.Uri)">
            <summary>
            Gives a hash equivalent to what Url.Normalize() gives.
            </summary>
        </member>
        <member name="M:System.Security.IdentityHelper.GetNormalizedStrongNameHash(System.Reflection.AssemblyName)">
            <summary>
            Uses the AssemblyName's public key to generate a hash equivalent to what
            StrongName.Normalize() gives.
            </summary>
        </member>
        <member name="P:System.SR.Parameter_Invalid">
            <summary>The parameter '{0}' is invalid.</summary>
        </member>
        <member name="P:System.SR.Parameter_NullOrEmpty">
            <summary>The string parameter '{0}' cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.Property_NullOrEmpty">
            <summary>The value assigned to property '{0}' cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.Property_Invalid">
            <summary>The value assigned to property '{0}' is invalid.</summary>
        </member>
        <member name="P:System.SR.Unexpected_Error">
            <summary>An unexpected error occurred in '{0}'.</summary>
        </member>
        <member name="P:System.SR.Wrapped_exception_message">
            <summary>{0}: {1}</summary>
        </member>
        <member name="P:System.SR.Config_error_loading_XML_file">
            <summary>An error occurred loading a configuration file</summary>
        </member>
        <member name="P:System.SR.Config_exception_creating_section_handler">
            <summary>An error occurred creating the configuration section handler for {0}</summary>
        </member>
        <member name="P:System.SR.Config_exception_creating_section">
            <summary>An error occurred creating the configuration section for {0}</summary>
        </member>
        <member name="P:System.SR.Config_tag_name_invalid">
            <summary>Invalid format for a section or section group name</summary>
        </member>
        <member name="P:System.SR.Config_add_configurationsection_already_added">
            <summary>Cannot add a ConfigurationSection that already belongs to the Configuration.</summary>
        </member>
        <member name="P:System.SR.Config_add_configurationsection_already_exists">
            <summary>Cannot add a ConfigurationSection with the same name that already exists.</summary>
        </member>
        <member name="P:System.SR.Config_add_configurationsection_in_location_config">
            <summary>Cannot add a ConfigurationSection to a Configuration with a location.</summary>
        </member>
        <member name="P:System.SR.Config_add_configurationsectiongroup_already_added">
            <summary>Cannot add a ConfigurationSectionGroup that already belongs to the Configuration.</summary>
        </member>
        <member name="P:System.SR.Config_add_configurationsectiongroup_already_exists">
            <summary>Cannot add a ConfigurationSectionGroup with the same name that already exists.</summary>
        </member>
        <member name="P:System.SR.Config_add_configurationsectiongroup_in_location_config">
            <summary>Cannot add a ConfigurationSectionGroup to a Configuration with a location.</summary>
        </member>
        <member name="P:System.SR.Config_allow_exedefinition_error_application">
            <summary>It is an error to use a section registered as allowExeDefinition='MachineToApplication' beyond the application, in the user's config.  (This is the default behavior if not specified)</summary>
        </member>
        <member name="P:System.SR.Config_allow_exedefinition_error_machine">
            <summary>It is an error to use a section registered as allowExeDefinition='MachineOnly' beyond machine.config.</summary>
        </member>
        <member name="P:System.SR.Config_allow_exedefinition_error_roaminguser">
            <summary>It is an error to use a section registered as allowExeDefinition='MachineToRoamingUser' beyond the roaming user config, in the local user config.</summary>
        </member>
        <member name="P:System.SR.Config_appsettings_declaration_invalid">
            <summary>The configuration section 'appSettings' has an unexpected declaration.</summary>
        </member>
        <member name="P:System.SR.Config_base_attribute_locked">
            <summary>The attribute '{0}' has been locked in a higher level configuration.</summary>
        </member>
        <member name="P:System.SR.Config_base_collection_item_locked_cannot_clear">
            <summary>A collection item has been locked in a higher level configuration. The collection may not be cleared.</summary>
        </member>
        <member name="P:System.SR.Config_base_collection_item_locked">
            <summary>The collection item has been locked in a higher level configuration and may not be changed.</summary>
        </member>
        <member name="P:System.SR.Config_base_cannot_add_items_above_inherited_items">
            <summary>This collection does not permit items to be added in or above the inherited items.</summary>
        </member>
        <member name="P:System.SR.Config_base_cannot_add_items_below_inherited_items">
            <summary>This collection does not permit items to be added in or below the inherited items.</summary>
        </member>
        <member name="P:System.SR.Config_base_cannot_remove_inherited_items">
            <summary>Inherited items may not be removed.</summary>
        </member>
        <member name="P:System.SR.Config_base_collection_elements_may_not_be_removed">
            <summary>Elements of this collection may not be removed.</summary>
        </member>
        <member name="P:System.SR.Config_base_collection_entry_already_exists">
            <summary>The entry '{0}' has already been added.</summary>
        </member>
        <member name="P:System.SR.Config_base_collection_entry_already_removed">
            <summary>Entry already removed.</summary>
        </member>
        <member name="P:System.SR.Config_base_collection_entry_not_found">
            <summary>The entry '{0}' is not in the collection.</summary>
        </member>
        <member name="P:System.SR.Config_base_element_cannot_have_multiple_child_elements">
            <summary>The element &lt;{0}&gt; may only appear once in this section.</summary>
        </member>
        <member name="P:System.SR.Config_base_element_locked">
            <summary>The element '{0}' has been locked in a higher level configuration.</summary>
        </member>
        <member name="P:System.SR.Config_base_expected_to_find_element">
            <summary>Expected to find an element.</summary>
        </member>
        <member name="P:System.SR.Config_base_invalid_attribute_to_lock">
            <summary>The attribute '{0}' is not valid in the locked list for this section. The following attributes can be locked: {1}. Multiple attributes may be listed separated by commas.</summary>
        </member>
        <member name="P:System.SR.Config_base_invalid_attribute_to_lock_by_add">
            <summary>The attribute '{0}' is not valid in the locked list for this section. The following attributes can be locked: {1}.</summary>
        </member>
        <member name="P:System.SR.Config_base_invalid_element_key">
            <summary>Invalid key value.</summary>
        </member>
        <member name="P:System.SR.Config_base_invalid_element_to_lock">
            <summary>The element '{0}' is not valid in the locked list for this section. The following elements can be locked: {1}. Multiple elements may be listed separated by commas.</summary>
        </member>
        <member name="P:System.SR.Config_base_invalid_element_to_lock_by_add">
            <summary>The element '{0}' is not valid in the locked list for this section. The following elements can be locked: {1}.</summary>
        </member>
        <member name="P:System.SR.Config_base_property_is_not_a_configuration_element">
            <summary>Property '{0}' is not a ConfigurationElement.</summary>
        </member>
        <member name="P:System.SR.Config_base_read_only">
            <summary>The configuration is read only.</summary>
        </member>
        <member name="P:System.SR.Config_base_required_attribute_locked">
            <summary>The attribute '{0}' is required and is locked at a higher level configuration. The configuration with the lock should lock the entire element if it needs to lock required attributes.</summary>
        </member>
        <member name="P:System.SR.Config_base_required_attribute_lock_attempt">
            <summary>The attribute '{0}' is required and cannot be locked. The configuration should lock the entire element if it needs to lock required attributes.</summary>
        </member>
        <member name="P:System.SR.Config_base_required_attribute_missing">
            <summary>Required attribute '{0}' not found.</summary>
        </member>
        <member name="P:System.SR.Config_base_section_invalid_content">
            <summary>The configuration section cannot contain a CDATA or text element.</summary>
        </member>
        <member name="P:System.SR.Config_base_unrecognized_attribute">
            <summary>Unrecognized attribute '{0}'. Note that attribute names are case-sensitive.</summary>
        </member>
        <member name="P:System.SR.Config_base_unrecognized_element">
            <summary>Unrecognized element.</summary>
        </member>
        <member name="P:System.SR.Config_base_unrecognized_element_name">
            <summary>Unrecognized element '{0}'.</summary>
        </member>
        <member name="P:System.SR.Config_base_value_cannot_contain">
            <summary>The value may not contain the '{0}' character.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_in_location_config">
            <summary>ConfigurationSection properties for a &lt;section&gt; declaration cannot be edited in a Configuration with a location.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_parentsection">
            <summary>ConfigurationSection properties can not be edited for the parent section of another section (ie. a section retrieved by calling GetParentSection on a section)</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_when_location_locked">
            <summary>ConfigurationSection properties for a location section cannot be edited when allowLocation=false.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_when_locked">
            <summary>ConfigurationSection properties cannot be edited when locked.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_when_not_attached">
            <summary>ConfigurationSection cannot be edited before being added to a section group belonging to an instance of class Configuration.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_when_it_is_implicit">
            <summary>ConfigurationSection cannot be edited because it is a built-in section.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsection_when_it_is_undeclared">
            <summary>ConfigurationSection cannot be edited because it is not declared.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsectiongroup_in_location_config">
            <summary>ConfigurationSectionGroup properties for a &lt;sectionGroup&gt; declaration cannot be edited in a Configuration with a location.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_configurationsectiongroup_when_not_attached">
            <summary>ConfigurationSectionGroup cannot be edited before being added to a section group belonging to an instance of class Configuration.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_edit_locationattriubtes">
            <summary>AllowOverride and InheritInChildApplications can not be set in the exe configuration, these settings have no meaning there.</summary>
        </member>
        <member name="P:System.SR.Config_cannot_open_config_source">
            <summary>Unable to open configSource file '{0}'.</summary>
        </member>
        <member name="P:System.SR.Config_client_config_init_error">
            <summary>Configuration system failed to initialize</summary>
        </member>
        <member name="P:System.SR.Config_client_config_too_many_configsections_elements">
            <summary>Only one &lt;configSections&gt; element allowed per config file and if present must be the first child of the root &lt;configuration&gt; element.</summary>
        </member>
        <member name="P:System.SR.Config_configmanager_open_noexe">
            <summary>exePath must be specified when not running inside a stand alone exe.</summary>
        </member>
        <member name="P:System.SR.Config_configsection_parentnotvalid">
            <summary>This is not valid for the parent section of another section (ie. a section retrieved by calling GetParentSection on a section)</summary>
        </member>
        <member name="P:System.SR.Config_connectionstrings_declaration_invalid">
            <summary>The configuration section 'connectionStrings' has an unexpected declaration.</summary>
        </member>
        <member name="P:System.SR.Config_data_read_count_mismatch">
            <summary>Data read count is not equal to data available.</summary>
        </member>
        <member name="P:System.SR.Config_element_no_context">
            <summary>This element is not currently associated with any context</summary>
        </member>
        <member name="P:System.SR.Config_empty_lock_attributes_except">
            <summary>The '{0}' attribute cannot be an empty string. {1}="*" may be used to lock all attributes.</summary>
        </member>
        <member name="P:System.SR.Config_empty_lock_element_except">
            <summary>The '{0}' attribute cannot be an empty string. {1}="*" may be used to lock all elements.</summary>
        </member>
        <member name="P:System.SR.Config_exception_in_config_section_handler">
            <summary>An error occurred executing the configuration section handler for {0}.</summary>
        </member>
        <member name="P:System.SR.Config_file_doesnt_have_root_configuration">
            <summary>Configuration file {0} does not have root &lt;configuration&gt; tag</summary>
        </member>
        <member name="P:System.SR.Config_file_has_changed">
            <summary>The configuration file has been changed by another program.</summary>
        </member>
        <member name="P:System.SR.Config_getparentconfigurationsection_first_instance">
            <summary>GetParentSection can only be applied to ConfigurationSections from the first instance of a Configuration.</summary>
        </member>
        <member name="P:System.SR.Config_inconsistent_location_attributes">
            <summary>Error in configuration section "{0}": AllowLocation is false and either AllowOverride or InheritInChildApplications is true.</summary>
        </member>
        <member name="P:System.SR.Config_invalid_attributes_for_write">
            <summary>Unable to open file '{0}' for writing because it is read-only or hidden.</summary>
        </member>
        <member name="P:System.SR.Config_invalid_boolean_attribute">
            <summary>The property '{0}' must have value 'true' or 'false'.</summary>
        </member>
        <member name="P:System.SR.Config_invalid_node_type">
            <summary>Invalid node type.</summary>
        </member>
        <member name="P:System.SR.Config_location_location_not_allowed">
            <summary>&lt;location&gt; sections are allowed only within &lt;configuration&gt; sections.</summary>
        </member>
        <member name="P:System.SR.Config_location_path_invalid_character">
            <summary>&lt;location&gt; path attribute must be a relative virtual path.  It cannot contain any of '?' ':' '\\' '*' '"' '&lt;' '&gt;' or '|'.</summary>
        </member>
        <member name="P:System.SR.Config_location_path_invalid_first_character">
            <summary>&lt;location&gt; path attribute must be a relative virtual path.  It cannot start with any of ' ' '.' '/' or '\\'.</summary>
        </member>
        <member name="P:System.SR.Config_location_path_invalid_last_character">
            <summary>&lt;location&gt; path attribute must be a relative virtual path.  It cannot end with any of ' ' '.' '/' or '\\'.</summary>
        </member>
        <member name="P:System.SR.Config_missing_required_attribute">
            <summary>The '{0}' attribute must be specified on the '{1}' tag.</summary>
        </member>
        <member name="P:System.SR.Config_more_data_than_expected">
            <summary>More data than expected.</summary>
        </member>
        <member name="P:System.SR.Config_name_value_file_section_file_invalid_root">
            <summary>The root element must match the name of the section referencing the file, '{0}'</summary>
        </member>
        <member name="P:System.SR.Config_namespace_invalid">
            <summary>Namespace of '{0}' on configuration element is invalid, only '{1}' is valid.</summary>
        </member>
        <member name="P:System.SR.Config_no_stream_to_write">
            <summary>A configuration file cannot be created for the requested Configuration object.</summary>
        </member>
        <member name="P:System.SR.Config_not_allowed_to_encrypt_this_section">
            <summary>This configuration section cannot be encrypted.</summary>
        </member>
        <member name="P:System.SR.Config_object_is_null">
            <summary>Configuration section handler returned a null object.</summary>
        </member>
        <member name="P:System.SR.Config_operation_not_runtime">
            <summary>This operation does not apply at runtime.</summary>
        </member>
        <member name="P:System.SR.Config_properties_may_not_be_derived_from_configuration_section">
            <summary>The Configuration property '{0}' may not be derived from ConfigurationSection.</summary>
        </member>
        <member name="P:System.SR.Config_provider_must_implement_type">
            <summary>Provider must implement the class '{0}'.</summary>
        </member>
        <member name="P:System.SR.Config_root_section_group_cannot_be_edited">
            <summary>The RootSectionGroup cannot be edited</summary>
        </member>
        <member name="P:System.SR.Config_section_allow_definition_attribute_invalid">
            <summary>The 'allowDefinition' attribute must be one of the following values: Everywhere, MachineOnly, MachineToWebRoot, MachineToApplication.</summary>
        </member>
        <member name="P:System.SR.Config_section_allow_exe_definition_attribute_invalid">
            <summary>The 'allowExeDefinition' attribute must be one of the following values: MachineOnly, MachineToApplication, MachineToRoamingUser, MachineToLocalUser.</summary>
        </member>
        <member name="P:System.SR.Config_section_cannot_be_used_in_location">
            <summary>This section is not allowed in &lt;location&gt; elements.  This section has been marked allowLocation="false".</summary>
        </member>
        <member name="P:System.SR.Config_section_locked">
            <summary>This configuration section cannot be used at this path.  This happens when the site administrator has locked access to this section using &lt;location allowOverride="false"&gt; from an inherited configuration file.</summary>
        </member>
        <member name="P:System.SR.Config_sections_must_be_unique">
            <summary>Sections must only appear once per config file.  See the help topic &lt;location&gt; for exceptions.</summary>
        </member>
        <member name="P:System.SR.Config_source_cannot_be_shared">
            <summary>The configSource file '{0}' can only be used by one type of section, and may not be the same as the configuration file.</summary>
        </member>
        <member name="P:System.SR.Config_source_parent_conflict">
            <summary>The configSource file '{0}' is also used in a parent, this is not allowed.</summary>
        </member>
        <member name="P:System.SR.Config_source_file_format">
            <summary>The format of a configSource file must be an element containing the name of the section.</summary>
        </member>
        <member name="P:System.SR.Config_source_invalid_format">
            <summary>The configSource attribute must be a relative physical path.</summary>
        </member>
        <member name="P:System.SR.Config_source_invalid_chars">
            <summary>The configSource attribute must be a relative physical path, so the '/' character is not allowed.</summary>
        </member>
        <member name="P:System.SR.Config_source_requires_file">
            <summary>The 'configSource' property may not be set in a configuration that has no associated file.</summary>
        </member>
        <member name="P:System.SR.Config_source_syntax_error">
            <summary>A section using 'configSource' may contain no other attributes or elements.</summary>
        </member>
        <member name="P:System.SR.Config_system_already_set">
            <summary>The configuration system has already been initialized.</summary>
        </member>
        <member name="P:System.SR.Config_tag_name_already_defined">
            <summary>Section or group name '{0}' is already defined. Updates to this may only occur at the configuration level where it is defined.</summary>
        </member>
        <member name="P:System.SR.Config_tag_name_already_defined_at_this_level">
            <summary>Section or group name '{0}' is already defined. This can not be defined multiple times.</summary>
        </member>
        <member name="P:System.SR.Config_tag_name_cannot_be_location">
            <summary>The section name 'location' is reserved for &lt;location&gt; sections.</summary>
        </member>
        <member name="P:System.SR.Config_tag_name_cannot_begin_with_config">
            <summary>Section names beginning with config are reserved.</summary>
        </member>
        <member name="P:System.SR.Config_type_doesnt_inherit_from_type">
            <summary>Type '{0}' does not inherit from '{1}'.</summary>
        </member>
        <member name="P:System.SR.Config_unexpected_element_end">
            <summary>Unexpected end of element {0}.</summary>
        </member>
        <member name="P:System.SR.Config_unexpected_element_name">
            <summary>Unexpected element name {0}.</summary>
        </member>
        <member name="P:System.SR.Config_unexpected_node_type">
            <summary>expected XmlNodeType.Element, type is {0}.</summary>
        </member>
        <member name="P:System.SR.Config_unrecognized_configuration_section">
            <summary>Unrecognized configuration section {0}.</summary>
        </member>
        <member name="P:System.SR.Config_write_failed">
            <summary>Unable to save config to file '{0}'.</summary>
        </member>
        <member name="P:System.SR.Converter_timespan_not_in_second">
            <summary>The expected format is an integer value in seconds.</summary>
        </member>
        <member name="P:System.SR.Converter_unsupported_value_type">
            <summary>The converter cannot convert values with type '{0}'.</summary>
        </member>
        <member name="P:System.SR.Decryption_failed">
            <summary>Failed to decrypt using provider '{0}'. Error message from the provider: {1}</summary>
        </member>
        <member name="P:System.SR.Default_value_conversion_error_from_string">
            <summary>The default value of the property '{0}' cannot be parsed. The error is: {1}</summary>
        </member>
        <member name="P:System.SR.Default_value_wrong_type">
            <summary>The default value for the property '{0}' has different type than the one of the property itself.</summary>
        </member>
        <member name="P:System.SR.DPAPI_bad_data">
            <summary>The data specified for decryption is bad.</summary>
        </member>
        <member name="P:System.SR.Empty_attribute">
            <summary>The '{0}' attribute cannot be an empty string.</summary>
        </member>
        <member name="P:System.SR.EncryptedNode_not_found">
            <summary>The section is marked as being protected. However, the &lt;EncryptedData&gt; child node was not found in the section's node. Make sure that the section is correctly encrypted.</summary>
        </member>
        <member name="P:System.SR.EncryptedNode_is_in_invalid_format">
            <summary>The section is marked as being protected, but it does not have the correct format. It should contain only the &lt;EncryptedData&gt; child node.</summary>
        </member>
        <member name="P:System.SR.Encryption_failed">
            <summary>Failed to encrypt the section '{0}' using provider '{1}'. Error message from the provider: {2}</summary>
        </member>
        <member name="P:System.SR.IndexOutOfRange">
            <summary>Index {0} is out of range.</summary>
        </member>
        <member name="P:System.SR.Invalid_enum_value">
            <summary>The enumeration value must be one of the following: {0}.</summary>
        </member>
        <member name="P:System.SR.Must_add_to_config_before_protecting_it">
            <summary>The configuration section must be added to a configuration hierarchy before you can protect it.</summary>
        </member>
        <member name="P:System.SR.No_converter">
            <summary>Unable to find a converter that supports conversion to/from string for the property '{0}' of type '{1}'.</summary>
        </member>
        <member name="P:System.SR.No_exception_information_available">
            <summary>No information about the exception is available.</summary>
        </member>
        <member name="P:System.SR.Property_name_reserved">
            <summary>A configuration property cannot have the name '{0}' because it starts with the reserved prefix 'config' or 'lock'.</summary>
        </member>
        <member name="P:System.SR.Item_name_reserved">
            <summary>A configuration item alias for '{0}' cannot have the name '{1}' because it starts with the reserved prefix 'config' or 'lock'.</summary>
        </member>
        <member name="P:System.SR.Basicmap_item_name_reserved">
            <summary>A configuration item cannot have the name '{0}' because it starts with the reserved prefix 'config' or 'lock'.</summary>
        </member>
        <member name="P:System.SR.ProtectedConfigurationProvider_not_found">
            <summary>The protection provider '{0}' was not found.</summary>
        </member>
        <member name="P:System.SR.Regex_validator_error">
            <summary>The value does not conform to the validation regex string '{0}'.</summary>
        </member>
        <member name="P:System.SR.String_null_or_empty">
            <summary>The string cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.Subclass_validator_error">
            <summary>The type '{0}' must be derived from the type '{1}'.</summary>
        </member>
        <member name="P:System.SR.Top_level_conversion_error_from_string">
            <summary>The value of the property '{0}' cannot be parsed. The error is: {1}</summary>
        </member>
        <member name="P:System.SR.Top_level_conversion_error_to_string">
            <summary>The value of the property '{0}' cannot be converted to string. The error is: {1}</summary>
        </member>
        <member name="P:System.SR.Top_level_validation_error">
            <summary>The value for the property '{0}' is not valid. The error is: {1}</summary>
        </member>
        <member name="P:System.SR.Type_cannot_be_resolved">
            <summary>The type '{0}' cannot be resolved. Please verify the spelling is correct or that the full type name is provided.</summary>
        </member>
        <member name="P:System.SR.TypeNotPublic">
            <summary>Unable to load type '{0}' because it is not public.</summary>
        </member>
        <member name="P:System.SR.Unrecognized_initialization_value">
            <summary>The configuration setting '{0}' was not recognized.</summary>
        </member>
        <member name="P:System.SR.Validation_scalar_range_violation_not_different">
            <summary>The value must be different than {0}.</summary>
        </member>
        <member name="P:System.SR.Validation_scalar_range_violation_not_equal">
            <summary>The value must be equal to {0}.</summary>
        </member>
        <member name="P:System.SR.Validation_scalar_range_violation_not_in_range">
            <summary>The value must be inside the range {0}-{1}.</summary>
        </member>
        <member name="P:System.SR.Validation_scalar_range_violation_not_outside_range">
            <summary>The value must not be in the range {0}-{1}.</summary>
        </member>
        <member name="P:System.SR.Validator_Attribute_param_not_validator">
            <summary>Only types derived from {0} are valid validator types.</summary>
        </member>
        <member name="P:System.SR.Validator_does_not_support_elem_type">
            <summary>The supplied validator does not support validating the configuration element type {0}.</summary>
        </member>
        <member name="P:System.SR.Validator_does_not_support_prop_type">
            <summary>The supplied validator does not support the type of the property '{0}'.</summary>
        </member>
        <member name="P:System.SR.Validator_element_not_valid">
            <summary>The configuration element '{0}' is not valid. The error is: {1}</summary>
        </member>
        <member name="P:System.SR.Validator_method_not_found">
            <summary>The supplied method name '{0}' was not found. The callback method must be a public static void method with one object parameter.</summary>
        </member>
        <member name="P:System.SR.Validator_min_greater_than_max">
            <summary>The upper range limit value must be greater than the lower range limit value.</summary>
        </member>
        <member name="P:System.SR.Validator_scalar_resolution_violation">
            <summary>The value must have a resolution of {0}.</summary>
        </member>
        <member name="P:System.SR.Validator_string_invalid_chars">
            <summary>The string cannot contain any of the following characters: '{0}'.</summary>
        </member>
        <member name="P:System.SR.Validator_string_max_length">
            <summary>The string must be no more than {0} characters long.</summary>
        </member>
        <member name="P:System.SR.Validator_string_min_length">
            <summary>The string must be at least {0} characters long.</summary>
        </member>
        <member name="P:System.SR.Validator_value_type_invalid">
            <summary>The supplied value is not of type which the validator can process.</summary>
        </member>
        <member name="P:System.SR.Validator_multiple_validator_attributes">
            <summary>Multiple validator attributes are not currently supported. The property '{0}' has more than one validator attribute associated with it.</summary>
        </member>
        <member name="P:System.SR.Validator_timespan_value_must_be_positive">
            <summary>The time span value must be positive.</summary>
        </member>
        <member name="P:System.SR.WrongType_of_Protected_provider">
            <summary>The type specified does not extend ProtectedConfigurationProvider class.</summary>
        </member>
        <member name="P:System.SR.Config_element_locking_not_supported">
            <summary>Locking of elements or attributes within a configuration section is not supported for legacy section '{0}'.</summary>
        </member>
        <member name="P:System.SR.Protection_provider_syntax_error">
            <summary>A section using 'configProtectionProvider' may contain no other attributes.</summary>
        </member>
        <member name="P:System.SR.Protection_provider_invalid_format">
            <summary>The configProtectionProvider attribute cannot be an empty string.</summary>
        </member>
        <member name="P:System.SR.Cannot_declare_or_remove_implicit_section">
            <summary>The section '{0}' is a built-in section.  It cannot be declared by the user.</summary>
        </member>
        <member name="P:System.SR.Config_reserved_attribute">
            <summary>The attribute '{0}' cannot be specified because its name starts with the reserved prefix 'config' or 'lock'.</summary>
        </member>
        <member name="P:System.SR.Filename_in_SaveAs_is_used_already">
            <summary>The file name '{0}' is invalid because the same file name is already referenced by the configuration hierarchy you have opened.</summary>
        </member>
        <member name="P:System.SR.Provider_Already_Initialized">
            <summary>This provider instance has already been initialized.</summary>
        </member>
        <member name="P:System.SR.Config_provider_name_null_or_empty">
            <summary>Provider name cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.CollectionReadOnly">
            <summary>Collection is read-only.</summary>
        </member>
        <member name="P:System.SR.Config_source_not_under_config_dir">
            <summary>The configSource '{0}' is invalid. It must refer to a file in the same directory or in a subdirectory as the configuration file.</summary>
        </member>
        <member name="P:System.SR.Config_source_invalid">
            <summary>The configSource attribute is invalid.</summary>
        </member>
        <member name="P:System.SR.Location_invalid_inheritInChildApplications_in_machine_or_root_web_config">
            <summary>InheritInChildApplications cannot be set to "false" if the location path is referring to machine.config or the root web.config.</summary>
        </member>
        <member name="P:System.SR.Cannot_change_both_AllowOverride_and_OverrideMode">
            <summary>Changing both AllowOverride and OverrideMode is not supported for compatibility reasons. Please use only one or the other.</summary>
        </member>
        <member name="P:System.SR.Config_section_override_mode_attribute_invalid">
            <summary>The 'overrideMode' and 'overrideModeDefault' attributes must be one of the following values: Inherited, Allow, Deny.</summary>
        </member>
        <member name="P:System.SR.Invalid_override_mode_declaration">
            <summary>A &lt;location&gt; tag may contain only one of the 'allowOverride' or 'overrideMode' attributes.</summary>
        </member>
        <member name="P:System.SR.Machine_config_file_not_found">
            <summary>The machine.config file '{0}' was not found.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed_StreamClosed">
            <summary>Cannot access a closed stream.</summary>
        </member>
        <member name="P:System.SR.Unable_to_convert_type_from_string">
            <summary>Could not find a type-converter to convert object if type '{0}' from string.</summary>
        </member>
        <member name="P:System.SR.Unable_to_convert_type_to_string">
            <summary>Could not find a type-converter to convert object if type '{0}' to string.</summary>
        </member>
        <member name="P:System.SR.Could_not_create_from_default_value">
            <summary>The property '{0}' could not be created from it's default value. Error message: {1}</summary>
        </member>
        <member name="P:System.SR.Could_not_create_from_default_value_2">
            <summary>The property '{0}' could not be created from it's default value because the default value is of a different type.</summary>
        </member>
        <member name="P:System.SR.UserSettingsNotSupported">
            <summary>The current configuration system does not support user-scoped settings.</summary>
        </member>
        <member name="P:System.SR.SettingsSaveFailed">
            <summary>Failed to save settings: {0}</summary>
        </member>
        <member name="P:System.SR.SettingsSaveFailedNoSection">
            <summary>Failed to save settings: unable to access the configuration section.</summary>
        </member>
        <member name="P:System.SR.UnknownUserLevel">
            <summary>Unknown ConfigurationUserLevel specified.</summary>
        </member>
        <member name="P:System.SR.BothScopeAttributes">
            <summary>The setting {0} has both an ApplicationScopedSettingAttribute and a UserScopedSettingAttribute.</summary>
        </member>
        <member name="P:System.SR.NoScopeAttributes">
            <summary>The setting {0} does not have either an ApplicationScopedSettingAttribute or UserScopedSettingAttribute.</summary>
        </member>
        <member name="P:System.SR.SettingsPropertyNotFound">
            <summary>The settings property '{0}' was not found.</summary>
        </member>
        <member name="P:System.SR.SettingsPropertyReadOnly">
            <summary>The settings property '{0}' is read-only.</summary>
        </member>
        <member name="P:System.SR.SettingsPropertyWrongType">
            <summary>The settings property '{0}' is of a non-compatible type.</summary>
        </member>
        <member name="P:System.SR.ProviderInstantiationFailed">
            <summary>Failed to instantiate provider: {0}.</summary>
        </member>
        <member name="P:System.SR.ProviderTypeLoadFailed">
            <summary>Failed to load provider type: {0}.</summary>
        </member>
        <member name="P:System.SR.AppSettingsReaderNoKey">
            <summary>The key '{0}' does not exist in the appSettings configuration section.</summary>
        </member>
        <member name="P:System.SR.AppSettingsReaderCantParse">
            <summary>The value '{0}' was found in the appSettings configuration section for key '{1}', and this value is not a valid {2}.</summary>
        </member>
        <member name="P:System.SR.AppSettingsReaderEmptyString">
            <summary>(empty string)</summary>
        </member>
        <member name="P:System.SR.Config_invalid_integer_attribute">
            <summary>The '{0}' attribute must be set to an integer value.</summary>
        </member>
        <member name="P:System.SR.Config_base_required_attribute_empty">
            <summary>Required attribute '{0}' cannot be empty.</summary>
        </member>
        <member name="P:System.SR.Config_base_elements_only">
            <summary>Only elements allowed.</summary>
        </member>
        <member name="P:System.SR.Config_base_no_child_nodes">
            <summary>Child nodes not allowed.</summary>
        </member>
        <member name="P:System.SR.InvalidNullEmptyArgument">
            <summary>Argument {0} cannot be null or zero-length.</summary>
        </member>
        <member name="P:System.SR.DuplicateFileName">
            <summary>The file name '{0}' was already in the collection.</summary>
        </member>
    </members>
</doc>
