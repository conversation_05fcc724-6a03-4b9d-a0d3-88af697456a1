<?xml version="1.0" encoding="utf-8"?>
<doc>
    <assembly>
        <name>System.Security.AccessControl</name>
    </assembly>
    <members>
        <member name="T:System.Security.AccessControl.AccessControlActions">
            <summary>Specifies the actions that are permitted for securable objects.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlActions.Change">
            <summary>Specifies write-only access.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlActions.None">
            <summary>Specifies no access.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlActions.View">
            <summary>Specifies read-only access.</summary>
        </member>
        <member name="T:System.Security.AccessControl.AccessControlModification">
            <summary>Specifies the type of access control modification to perform. This enumeration is used by methods
                of the
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                class and its descendants.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlModification.Add">
            <summary>Add the specified authorization rule to the access control list (ACL).</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlModification.Remove">
            <summary>Remove authorization rules that contain the same security identifier (SID) and access mask as the
                specified authorization rule from the ACL.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlModification.RemoveAll">
            <summary>Remove authorization rules that contain the same SID as the specified authorization rule from the
                ACL.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlModification.RemoveSpecific">
            <summary>Remove authorization rules that exactly match the specified authorization rule from the ACL.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlModification.Reset">
            <summary>Remove authorization rules that contain the same SID as the specified authorization rule from the
                ACL, and then add the specified authorization rule to the ACL.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlModification.Set">
            <summary>Remove all authorization rules from the ACL, then add the specified authorization rule to the
                ACL.
            </summary>
        </member>
        <member name="T:System.Security.AccessControl.AccessControlSections">
            <summary>Specifies which sections of a security descriptor to save or load.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlSections.Access">
            <summary>The discretionary access control list (DACL).</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlSections.All">
            <summary>The entire security descriptor.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlSections.Audit">
            <summary>The system access control list (SACL).</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlSections.Group">
            <summary>The primary group.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlSections.None">
            <summary>No sections.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlSections.Owner">
            <summary>The owner.</summary>
        </member>
        <member name="T:System.Security.AccessControl.AccessControlType">
            <summary>Specifies whether an
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object is used to allow or deny access. These values are not flags, and they cannot be combined.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlType.Allow">
            <summary>The
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object is used to allow access to a secured object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AccessControlType.Deny">
            <summary>The
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object is used to deny access to a secured object.
            </summary>
        </member>
        <member name="T:System.Security.AccessControl.AccessRule">
            <summary>Represents a combination of a user's identity, an access mask, and an access control type (allow or
                deny). An
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object also contains information about the how the rule is inherited by child objects and how that
                inheritance is propagated.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.AccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which the access rule applies. This parameter must be an object that
                can be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">
                <see langword="true"/>
                if this rule is inherited from a parent container.
            </param>
            <param name="inheritanceFlags">The inheritance properties of the access rule.</param>
            <param name="propagationFlags">Whether inherited access rules are automatically propagated. The propagation
                flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="type">The valid access control type.</param>
            <exception cref="T:System.ArgumentException">The value of the
                <paramref name="identity"/>
                parameter cannot be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>, or the
                <paramref name="type"/>
                parameter contains an invalid value.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="accessMask"/>
                parameter is zero, or the
                <paramref name="inheritanceFlags"/>
                or
                <paramref name="propagationFlags"/>
                parameters contain unrecognized flag values.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.AccessRule.AccessControlType">
            <summary>Gets the
                <see cref="T:System.Security.AccessControl.AccessControlType"/>
                value associated with this
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object.
            </summary>
            <returns>The
                <see cref="T:System.Security.AccessControl.AccessControlType"/>
                value associated with this
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.AccessRule`1">
            <summary>Represents a combination of a user's identity, an access mask, and an access control type (allow or
                deny). An AccessRule`1 object also contains information about the how the rule is inherited by child
                objects and how that inheritance is propagated.
            </summary>
            <typeparam name="T">The access rights type for the access rule.</typeparam>
        </member>
        <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the AccessRule'1 class by using the specified values.</summary>
            <param name="identity">The identity to which the access rule applies.</param>
            <param name="rights">The rights of the access rule.</param>
            <param name="type">The valid access control type.</param>
        </member>
        <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the AccessRule'1 class by using the specified values.</summary>
            <param name="identity">The identity to which the access rule applies.</param>
            <param name="rights">The rights of the access rule.</param>
            <param name="inheritanceFlags">The inheritance properties of the access rule.</param>
            <param name="propagationFlags">Whether inherited access rules are automatically propagated. The propagation
                flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="type">The valid access control type.</param>
        </member>
        <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the AccessRule'1 class by using the specified values.</summary>
            <param name="identity">The identity to which the access rule applies.</param>
            <param name="rights">The rights of the access rule.</param>
            <param name="type">The valid access control type.</param>
        </member>
        <member name="M:System.Security.AccessControl.AccessRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the AccessRule'1 class by using the specified values.</summary>
            <param name="identity">The identity to which the access rule applies.</param>
            <param name="rights">The rights of the access rule.</param>
            <param name="inheritanceFlags">The inheritance properties of the access rule.</param>
            <param name="propagationFlags">Whether inherited access rules are automatically propagated. The propagation
                flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="type">The valid access control type.</param>
        </member>
        <member name="P:System.Security.AccessControl.AccessRule`1.Rights">
            <summary>Gets the rights of the current instance.</summary>
            <returns>The rights, cast as type &lt;T&gt;, of the current instance.</returns>
        </member>
        <member name="T:System.Security.AccessControl.AceEnumerator">
            <summary>Provides the ability to iterate through the access control entries (ACEs) in an access control list
                (ACL).
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.AceEnumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                collection.
            </summary>
            <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was
                created.
            </exception>
            <returns>
                <see langword="true"/>
                if the enumerator was successfully advanced to the next element;
                <see langword="false"/>
                if the enumerator has passed the end of the collection.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.AceEnumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                collection.
            </summary>
            <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was
                created.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.AceEnumerator.Current">
            <summary>Gets the current element in the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                collection. This property gets the type-friendly version of the object.
            </summary>
            <returns>The current element in the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                collection.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.AceEnumerator.System#Collections#IEnumerator#Current">
            <summary>Gets the current element in the collection.</summary>
            <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was
                created.
            </exception>
            <returns>The current element in the collection.</returns>
        </member>
        <member name="T:System.Security.AccessControl.AceFlags">
            <summary>Specifies the inheritance and auditing behavior of an access control entry (ACE).</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.AuditFlags">
            <summary>All access attempts are audited.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.ContainerInherit">
            <summary>The access mask is propagated to child container objects.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.FailedAccess">
            <summary>Failed access attempts are audited.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.InheritanceFlags">
            <summary>A logical
                <see langword="OR"/>
                of<see cref="F:System.Security.AccessControl.AceFlags.ObjectInherit"/>,<see
                        cref="F:System.Security.AccessControl.AceFlags.ContainerInherit"/>,<see
                        cref="F:System.Security.AccessControl.AceFlags.NoPropagateInherit"/>, and<see
                        cref="F:System.Security.AccessControl.AceFlags.InheritOnly"/>.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.Inherited">
            <summary>An ACE is inherited from a parent container rather than being explicitly set for an object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.InheritOnly">
            <summary>The access mask is propagated only to child objects. This includes both container and leaf child
                objects.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.None">
            <summary>No ACE flags are set.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.NoPropagateInherit">
            <summary>The access checks do not apply to the object; they only apply to its children.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.ObjectInherit">
            <summary>The access mask is propagated onto child leaf objects.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceFlags.SuccessfulAccess">
            <summary>Successful access attempts are audited.</summary>
        </member>
        <member name="T:System.Security.AccessControl.AceQualifier">
            <summary>Specifies the function of an access control entry (ACE).</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceQualifier.AccessAllowed">
            <summary>Allow access.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceQualifier.AccessDenied">
            <summary>Deny access.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceQualifier.SystemAlarm">
            <summary>Cause a system alarm.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceQualifier.SystemAudit">
            <summary>Cause a system audit.</summary>
        </member>
        <member name="T:System.Security.AccessControl.AceType">
            <summary>Defines the available access control entry (ACE) types.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessAllowed">
            <summary>Allows access to an object for a specific trustee identified by an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallback">
            <summary>Allows access to an object for a specific trustee identified by an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object. This ACE type may contain optional callback data. The callback data is a resource
                manager-specific BLOB that is not interpreted.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessAllowedCallbackObject">
            <summary>Allows access to an object, property set, or property. The ACE contains a set of access rights, a
                GUID that identifies the type of object, and an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object that identifies the trustee to whom the system will grant access. The ACE also contains a GUID
                and a set of flags that control inheritance of the ACE by child objects. This ACE type may contain
                optional callback data. The callback data is a resource manager-specific BLOB that is not interpreted.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessAllowedCompound">
            <summary>Defined but never used. Included here for completeness.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessAllowedObject">
            <summary>Allows access to an object, property set, or property. The ACE contains a set of access rights, a
                GUID that identifies the type of object, and an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object that identifies the trustee to whom the system will grant access. The ACE also contains a GUID
                and a set of flags that control inheritance of the ACE by child objects.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessDenied">
            <summary>Denies access to an object for a specific trustee identified by an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallback">
            <summary>Denies access to an object for a specific trustee identified by an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object. This ACE type can contain optional callback data. The callback data is a resource
                manager-specific BLOB that is not interpreted.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessDeniedCallbackObject">
            <summary>Denies access to an object, property set, or property. The ACE contains a set of access rights, a
                GUID that identifies the type of object, and an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object that identifies the trustee to whom the system will grant access. The ACE also contains a GUID
                and a set of flags that control inheritance of the ACE by child objects. This ACE type can contain
                optional callback data. The callback data is a resource manager-specific BLOB that is not interpreted.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.AccessDeniedObject">
            <summary>Denies access to an object, property set, or property. The ACE contains a set of access rights, a
                GUID that identifies the type of object, and an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object that identifies the trustee to whom the system will grant access. The ACE also contains a GUID
                and a set of flags that control inheritance of the ACE by child objects.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.MaxDefinedAceType">
            <summary>Tracks the maximum defined ACE type in the enumeration.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAlarm">
            <summary>Reserved for future use.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallback">
            <summary>Reserved for future use.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAlarmCallbackObject">
            <summary>Reserved for future use.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAlarmObject">
            <summary>Reserved for future use.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAudit">
            <summary>Causes an audit message to be logged when a specified trustee attempts to gain access to an object.
                The trustee is identified by an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAuditCallback">
            <summary>Causes an audit message to be logged when a specified trustee attempts to gain access to an object.
                The trustee is identified by an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object. This ACE type can contain optional callback data. The callback data is a resource
                manager-specific BLOB that is not interpreted.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAuditCallbackObject">
            <summary>Causes an audit message to be logged when a specified trustee attempts to gain access to an object
                or subobjects such as property sets or properties. The ACE contains a set of access rights, a GUID that
                identifies the type of object or subobject, and an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object that identifies the trustee for whom the system will audit access. The ACE also contains a GUID
                and a set of flags that control inheritance of the ACE by child objects. This ACE type can contain
                optional callback data. The callback data is a resource manager-specific BLOB that is not interpreted.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.AceType.SystemAuditObject">
            <summary>Causes an audit message to be logged when a specified trustee attempts to gain access to an object
                or subobjects such as property sets or properties. The ACE contains a set of access rights, a GUID that
                identifies the type of object or subobject, and an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                object that identifies the trustee for whom the system will audit access. The ACE also contains a GUID
                and a set of flags that control inheritance of the ACE by child objects.
            </summary>
        </member>
        <member name="T:System.Security.AccessControl.AuditFlags">
            <summary>Specifies the conditions for auditing attempts to access a securable object.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AuditFlags.Failure">
            <summary>Failed access attempts are to be audited.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AuditFlags.None">
            <summary>No access attempts are to be audited.</summary>
        </member>
        <member name="F:System.Security.AccessControl.AuditFlags.Success">
            <summary>Successful access attempts are to be audited.</summary>
        </member>
        <member name="T:System.Security.AccessControl.AuditRule">
            <summary>Represents a combination of a user's identity and an access mask. An
                <see cref="T:System.Security.AccessControl.AuditRule"/>
                object also contains information about how the rule is inherited by child objects, how that inheritance
                is propagated, and for what conditions it is audited.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.AuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which the audit rule applies. It must be an object that can be cast
                as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">
                <see langword="true"/>
                to inherit this rule from a parent container.
            </param>
            <param name="inheritanceFlags">The inheritance properties of the audit rule.</param>
            <param name="propagationFlags">Whether inherited audit rules are automatically propagated. The propagation
                flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="auditFlags">The conditions for which the rule is audited.</param>
            <exception cref="T:System.ArgumentException">The value of the
                <paramref name="identity"/>
                parameter cannot be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>, or the
                <paramref name="auditFlags"/>
                parameter contains an invalid value.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="accessMask"/>
                parameter is zero, or the
                <paramref name="inheritanceFlags"/>
                or
                <paramref name="propagationFlags"/>
                parameters contain unrecognized flag values.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.AuditRule.AuditFlags">
            <summary>Gets the audit flags for this audit rule.</summary>
            <returns>A bitwise combination of the enumeration values. This combination specifies the audit conditions
                for this audit rule.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.AuditRule`1">
            <summary>Represents a combination of a user's identity and an access mask.</summary>
            <typeparam name="T">The type of the audit rule.</typeparam>
        </member>
        <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule`1"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which this audit rule applies.</param>
            <param name="rights">The rights of the audit rule.</param>
            <param name="flags">The conditions for which the rule is audited.</param>
        </member>
        <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.Security.Principal.IdentityReference,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule`1"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which the audit rule applies.</param>
            <param name="rights">The rights of the audit rule.</param>
            <param name="inheritanceFlags">The inheritance properties of the audit rule.</param>
            <param name="propagationFlags">Whether inherited audit rules are automatically propagated.</param>
            <param name="flags">The conditions for which the rule is audited.</param>
        </member>
        <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule`1"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which the audit rule applies.</param>
            <param name="rights">The rights of the audit rule.</param>
            <param name="flags">The properties of the audit rule.</param>
        </member>
        <member name="M:System.Security.AccessControl.AuditRule`1.#ctor(System.String,`0,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule`1"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which the audit rule applies.</param>
            <param name="rights">The rights of the audit rule.</param>
            <param name="inheritanceFlags">The inheritance properties of the audit rule.</param>
            <param name="propagationFlags">Whether inherited audit rules are automatically propagated.</param>
            <param name="flags">The conditions for which the rule is audited.</param>
        </member>
        <member name="P:System.Security.AccessControl.AuditRule`1.Rights">
            <summary>Gets the rights of the audit rule.</summary>
            <returns>The rights of the audit rule.</returns>
        </member>
        <member name="T:System.Security.AccessControl.AuthorizationRule">
            <summary>Determines access to securable objects. The derived classes
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                and
                <see cref="T:System.Security.AccessControl.AuditRule"/>
                offer specializations for access and audit functionality.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.AuthorizationRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                class by using the specified values.
            </summary>
            <param name="identity">The identity to which the access rule applies. This parameter must be an object that
                can be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">
                <see langword="true"/>
                to inherit this rule from a parent container.
            </param>
            <param name="inheritanceFlags">The inheritance properties of the access rule.</param>
            <param name="propagationFlags">Whether inherited access rules are automatically propagated. The propagation
                flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <exception cref="T:System.ArgumentException">The value of the
                <paramref name="identity"/>
                parameter cannot be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="accessMask"/>
                parameter is zero, or the
                <paramref name="inheritanceFlags"/>
                or
                <paramref name="propagationFlags"/>
                parameters contain unrecognized flag values.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.AuthorizationRule.AccessMask">
            <summary>Gets the access mask for this rule.</summary>
            <returns>The access mask for this rule.</returns>
        </member>
        <member name="P:System.Security.AccessControl.AuthorizationRule.IdentityReference">
            <summary>Gets the
                <see cref="T:System.Security.Principal.IdentityReference"/>
                to which this rule applies.
            </summary>
            <returns>The
                <see cref="T:System.Security.Principal.IdentityReference"/>
                to which this rule applies.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.AuthorizationRule.InheritanceFlags">
            <summary>Gets the value of flags that determine how this rule is inherited by child objects.</summary>
            <returns>A bitwise combination of the enumeration values.</returns>
        </member>
        <member name="P:System.Security.AccessControl.AuthorizationRule.IsInherited">
            <summary>Gets a value indicating whether this rule is explicitly set or is inherited from a parent container
                object.
            </summary>
            <returns>
                <see langword="true"/>
                if this rule is not explicitly set but is instead inherited from a parent container.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.AuthorizationRule.PropagationFlags">
            <summary>Gets the value of the propagation flags, which determine how inheritance of this rule is propagated
                to child objects. This property is significant only when the value of the
                <see cref="T:System.Security.AccessControl.InheritanceFlags"/>
                enumeration is not<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </summary>
            <returns>A bitwise combination of the enumeration values.</returns>
        </member>
        <member name="T:System.Security.AccessControl.AuthorizationRuleCollection">
            <summary>Represents a collection of
                <see cref="T:System.Security.AccessControl.AuthorizationRule"/>
                objects.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.#ctor">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuthorizationRuleCollection"/>
                class.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.AddRule(System.Security.AccessControl.AuthorizationRule)">
            <summary>Adds an
                <see cref="T:System.Security.AccessControl.AuthorizationRule"/>
                object to the collection.
            </summary>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.AuthorizationRule"/>
                object to add to the collection.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.AuthorizationRuleCollection.CopyTo(System.Security.AccessControl.AuthorizationRule[],System.Int32)">
            <summary>Copies the contents of the collection to an array.</summary>
            <param name="rules">An array to which to copy the contents of the collection.</param>
            <param name="index">The zero-based index from which to begin copying.</param>
        </member>
        <member name="P:System.Security.AccessControl.AuthorizationRuleCollection.Item(System.Int32)">
            <summary>Gets the
                <see cref="T:System.Security.AccessControl.AuthorizationRule"/>
                object at the specified index of the collection.
            </summary>
            <param name="index">The zero-based index of the
                <see cref="T:System.Security.AccessControl.AuthorizationRule"/>
                object to get.
            </param>
            <returns>The
                <see cref="T:System.Security.AccessControl.AuthorizationRule"/>
                object at the specified index.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.CommonAce">
            <summary>Represents an access control entry (ACE).</summary>
        </member>
        <member name="M:System.Security.AccessControl.CommonAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Boolean,System.Byte[])">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                class.
            </summary>
            <param name="flags">Flags that specify information about the inheritance, inheritance propagation, and
                auditing conditions for the new access control entry (ACE).
            </param>
            <param name="qualifier">The use of the new ACE.</param>
            <param name="accessMask">The access mask for the ACE.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                associated with the new ACE.
            </param>
            <param name="isCallback">
                <see langword="true"/>
                to specify that the new ACE is a callback type ACE.
            </param>
            <param name="opaque">Opaque data associated with the new ACE. Opaque data is allowed only for callback ACE
                types. The length of this array must not be greater than the return value of the
                <see cref="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)"/>
                method.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                object is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                to be copied into the
                <paramref name="binaryForm"/>
                array.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.CommonAce.MaxOpaqueLength(System.Boolean)">
            <summary>Gets the maximum allowed length of an opaque data BLOB for callback access control entries
                (ACEs).
            </summary>
            <param name="isCallback">
                <see langword="true"/>
                to specify that the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                object is a callback ACE type.
            </param>
            <returns>The allowed length of an opaque data BLOB.</returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAce.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                object. Use this length with the
                <see cref="M:System.Security.AccessControl.CommonAce.GetBinaryForm(System.Byte[],System.Int32)"/>
                method before marshaling the ACL into a binary array.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.CommonAcl">
            <summary>Represents an access control list (ACL) and is the base class for the
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                and
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                classes.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonAcl.Purge(System.Security.Principal.SecurityIdentifier)">
            <summary>Removes all access control entries (ACEs) contained by this
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object that are associated with the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object to check for.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonAcl.RemoveInheritedAces">
            <summary>Removes all inherited access control entries (ACEs) from this
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object.
            </summary>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object. This length should be used before marshaling the access control list (ACL) into a binary array
                by using the
                <see cref="M:System.Security.AccessControl.CommonAcl.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.Count">
            <summary>Gets the number of access control entries (ACEs) in the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object.
            </summary>
            <returns>The number of ACEs in the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.IsCanonical">
            <summary>Gets a Boolean value that specifies whether the access control entries (ACEs) in the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object are in canonical order.
            </summary>
            <returns>
                <see langword="true"/>
                if the ACEs in the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object are in canonical order; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.IsContainer">
            <summary>Sets whether the
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object is a container.
            </summary>
            <returns>
                <see langword="true"/>
                if the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object is a container.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.IsDS">
            <summary>Sets whether the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object is a directory object access control list (ACL).
            </summary>
            <returns>
                <see langword="true"/>
                if the current
                <see cref="T:System.Security.AccessControl.CommonAcl"/>
                object is a directory object ACL.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.Item(System.Int32)">
            <summary>Gets or sets the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                at the specified index.
            </summary>
            <param name="index">The zero-based index of the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                to get or set.
            </param>
            <returns>The
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                at the specified index.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonAcl.Revision">
            <summary>Gets the revision level of the<see cref="T:System.Security.AccessControl.CommonAcl"/>.
            </summary>
            <returns>A byte value that specifies the revision level of the<see
                    cref="T:System.Security.AccessControl.CommonAcl"/>.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.CommonObjectSecurity">
            <summary>Controls access to objects without direct manipulation of access control lists (ACLs). This class
                is the abstract base class for the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.#ctor(System.Boolean)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                class.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new object is a container object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAccessRule(System.Security.AccessControl.AccessRule)">
            <summary>Adds the specified access rule to the Discretionary Access Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The access rule to add.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.AddAuditRule(System.Security.AccessControl.AuditRule)">
            <summary>Adds the specified audit rule to the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The audit rule to add.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAccessRules(System.Boolean,System.Boolean,System.Type)">
            <summary>Gets a collection of the access rules associated with the specified security identifier.</summary>
            <param name="includeExplicit">
                <see langword="true"/>
                to include access rules explicitly set for the object.
            </param>
            <param name="includeInherited">
                <see langword="true"/>
                to include inherited access rules.
            </param>
            <param name="targetType">Specifies whether the security identifier for which to retrieve access rules is of
                type
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                or type<see cref="T:System.Security.Principal.NTAccount"/>. The value of this parameter must be a type
                that can be translated to the
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                type.
            </param>
            <returns>The collection of access rules associated with the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.GetAuditRules(System.Boolean,System.Boolean,System.Type)">
            <summary>Gets a collection of the audit rules associated with the specified security identifier.</summary>
            <param name="includeExplicit">
                <see langword="true"/>
                to include audit rules explicitly set for the object.
            </param>
            <param name="includeInherited">
                <see langword="true"/>
                to include inherited audit rules.
            </param>
            <param name="targetType">The security identifier for which to retrieve audit rules. This must be an object
                that can be cast as a
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </param>
            <returns>The collection of audit rules associated with the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
            <summary>Applies the specified modification to the Discretionary Access Control List (DACL) associated with
                this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="modification">The modification to apply to the DACL.</param>
            <param name="rule">The access rule to modify.</param>
            <param name="modified">
                <see langword="true"/>
                if the DACL is successfully modified; otherwise,<see langword="false"/>.
            </param>
            <returns>
                <see langword="true"/>
                if the DACL is successfully modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
            <summary>Applies the specified modification to the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="modification">The modification to apply to the SACL.</param>
            <param name="rule">The audit rule to modify.</param>
            <param name="modified">
                <see langword="true"/>
                if the SACL is successfully modified; otherwise,<see langword="false"/>.
            </param>
            <returns>
                <see langword="true"/>
                if the SACL is successfully modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRule(System.Security.AccessControl.AccessRule)">
            <summary>Removes access rules that contain the same security identifier and access mask as the specified
                access rule from the Discretionary Access Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The access rule to remove.</param>
            <returns>
                <see langword="true"/>
                if the access rule was successfully removed; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule)">
            <summary>Removes all access rules that have the same security identifier as the specified access rule from
                the Discretionary Access Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The access rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule)">
            <summary>Removes all access rules that exactly match the specified access rule from the Discretionary Access
                Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The access rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRule(System.Security.AccessControl.AuditRule)">
            <summary>Removes audit rules that contain the same security identifier and access mask as the specified
                audit rule from the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The audit rule to remove.</param>
            <returns>
                <see langword="true"/>
                if the audit rule was successfully removed; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule)">
            <summary>Removes all audit rules that have the same security identifier as the specified audit rule from the
                System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The audit rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule)">
            <summary>Removes all audit rules that exactly match the specified audit rule from the System Access Control
                List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object.
            </summary>
            <param name="rule">The audit rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.ResetAccessRule(System.Security.AccessControl.AccessRule)">
            <summary>Removes all access rules in the Discretionary Access Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object and then adds the specified access rule.
            </summary>
            <param name="rule">The access rule to reset.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAccessRule(System.Security.AccessControl.AccessRule)">
            <summary>Removes all access rules that contain the same security identifier and qualifier as the specified
                access rule in the Discretionary Access Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object and then adds the specified access rule.
            </summary>
            <param name="rule">The access rule to set.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonObjectSecurity.SetAuditRule(System.Security.AccessControl.AuditRule)">
            <summary>Removes all audit rules that contain the same security identifier and qualifier as the specified
                audit rule in the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                object and then adds the specified audit rule.
            </summary>
            <param name="rule">The audit rule to set.</param>
        </member>
        <member name="T:System.Security.AccessControl.CommonSecurityDescriptor">
            <summary>Represents a security descriptor. A security descriptor includes an owner, a primary group, a
                Discretionary Access Control List (DACL), and a System Access Control List (SACL).
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Byte[],System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                class from the specified array of byte values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new security descriptor is associated with a container object.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new security descriptor is associated with a directory object.
            </param>
            <param name="binaryForm">The array of byte values from which to create the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
            <param name="offset">The offset in the
                <paramref name="binaryForm"/>
                array at which to begin copying.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.SystemAcl,System.Security.AccessControl.DiscretionaryAcl)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                class from the specified information.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new security descriptor is associated with a container object.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new security descriptor is associated with a directory object.
            </param>
            <param name="flags">Flags that specify behavior of the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
            <param name="owner">The owner for the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
            <param name="group">The primary group for the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
            <param name="systemAcl">The System Access Control List (SACL) for the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
            <param name="discretionaryAcl">The Discretionary Access Control List (DACL) for the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawSecurityDescriptor)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                class from the specified
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new security descriptor is associated with a container object.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new security descriptor is associated with a directory object.
            </param>
            <param name="rawSecurityDescriptor">The
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object from which to create the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.#ctor(System.Boolean,System.Boolean,System.String)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                class from the specified Security Descriptor Definition Language (SDDL) string.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new security descriptor is associated with a container object.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new security descriptor is associated with a directory object.
            </param>
            <param name="sddlForm">The SDDL string from which to create the new
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddDiscretionaryAcl(System.Byte,System.Int32)">
            <summary>Sets the
                <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl"/>
                property for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                instance and sets the
                <see cref="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent"/>
                flag.
            </summary>
            <param name="revision">The revision level of the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </param>
            <param name="trusted">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object can contain. This number is to be used only as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.AddSystemAcl(System.Byte,System.Int32)">
            <summary>Sets the
                <see cref="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl"/>
                property for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                instance and sets the
                <see cref="F:System.Security.AccessControl.ControlFlags.SystemAclPresent"/>
                flag.
            </summary>
            <param name="revision">The revision level of the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object.
            </param>
            <param name="trusted">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object can contain. This number should only be used as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAccessControl(System.Security.Principal.SecurityIdentifier)">
            <summary>Removes all access rules for the specified security identifier from the Discretionary Access
                Control List (DACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </summary>
            <param name="sid">The security identifier for which to remove access rules.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.PurgeAudit(System.Security.Principal.SecurityIdentifier)">
            <summary>Removes all audit rules for the specified security identifier from the System Access Control List
                (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </summary>
            <param name="sid">The security identifier for which to remove audit rules.</param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetDiscretionaryAclProtection(System.Boolean,System.Boolean)">
            <summary>Sets the inheritance protection for the Discretionary Access Control List (DACL) associated with
                this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object. DACLs that are protected do not inherit access rules from parent containers.
            </summary>
            <param name="isProtected">
                <see langword="true"/>
                to protect the DACL from inheritance.
            </param>
            <param name="preserveInheritance">
                <see langword="true"/>
                to keep inherited access rules in the DACL;
                <see langword="false"/>
                to remove inherited access rules from the DACL.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CommonSecurityDescriptor.SetSystemAclProtection(System.Boolean,System.Boolean)">
            <summary>Sets the inheritance protection for the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object. SACLs that are protected do not inherit audit rules from parent containers.
            </summary>
            <param name="isProtected">
                <see langword="true"/>
                to protect the SACL from inheritance.
            </param>
            <param name="preserveInheritance">
                <see langword="true"/>
                to keep inherited audit rules in the SACL;
                <see langword="false"/>
                to remove inherited audit rules from the SACL.
            </param>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.ControlFlags">
            <summary>Gets values that specify behavior of the
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </summary>
            <returns>One or more values of the
                <see cref="T:System.Security.AccessControl.ControlFlags"/>
                enumeration combined with a logical OR operation.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.DiscretionaryAcl">
            <summary>Gets or sets the discretionary access control list (DACL) for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object. The DACL contains access rules.
            </summary>
            <returns>The DACL for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Group">
            <summary>Gets or sets the primary group for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </summary>
            <returns>The primary group for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsContainer">
            <summary>Gets a Boolean value that specifies whether the object associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is a container object.
            </summary>
            <returns>
                <see langword="true"/>
                if the object associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is a container object; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDiscretionaryAclCanonical">
            <summary>Gets a Boolean value that specifies whether the Discretionary Access Control List (DACL) associated
                with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is in canonical order.
            </summary>
            <returns>
                <see langword="true"/>
                if the DACL associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is in canonical order; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsDS">
            <summary>Gets a Boolean value that specifies whether the object associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is a directory object.
            </summary>
            <returns>
                <see langword="true"/>
                if the object associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is a directory object; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.IsSystemAclCanonical">
            <summary>Gets a Boolean value that specifies whether the System Access Control List (SACL) associated with
                this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is in canonical order.
            </summary>
            <returns>
                <see langword="true"/>
                if the SACL associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object is in canonical order; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.Owner">
            <summary>Gets or sets the owner of the object associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </summary>
            <returns>The owner of the object associated with this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CommonSecurityDescriptor.SystemAcl">
            <summary>Gets or sets the System Access Control List (SACL) for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object. The SACL contains audit rules.
            </summary>
            <returns>The SACL for this
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.CompoundAce">
            <summary>Represents a compound Access Control Entry (ACE).</summary>
        </member>
        <member name="M:System.Security.AccessControl.CompoundAce.#ctor(System.Security.AccessControl.AceFlags,System.Int32,System.Security.AccessControl.CompoundAceType,System.Security.Principal.SecurityIdentifier)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                class.
            </summary>
            <param name="flags">Contains flags that specify information about the inheritance, inheritance propagation,
                and auditing conditions for the new Access Control Entry (ACE).
            </param>
            <param name="accessMask">The access mask for the ACE.</param>
            <param name="compoundAceType">A value from the
                <see cref="T:System.Security.AccessControl.CompoundAceType"/>
                enumeration.
            </param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                associated with the new ACE.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.CompoundAce.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.CompoundAce.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CompoundAce.CompoundAceType">
            <summary>Gets or sets the type of this
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object.
            </summary>
            <returns>The type of this
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.CompoundAceType">
            <summary>Specifies the type of a
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.CompoundAceType.Impersonation">
            <summary>The
                <see cref="T:System.Security.AccessControl.CompoundAce"/>
                object is used for impersonation.
            </summary>
        </member>
        <member name="T:System.Security.AccessControl.ControlFlags">
            <summary>These flags affect the security descriptor behavior.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInherited">
            <summary>Specifies that the Discretionary Access Control List (DACL) has been automatically inherited from
                the parent. Set by resource managers only.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclAutoInheritRequired">
            <summary>Ignored.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclDefaulted">
            <summary>Specifies that the DACL was obtained by a defaulting mechanism. Set by resource managers only.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclPresent">
            <summary>Specifies that the DACL is not<see langword="null"/>. Set by resource managers or users.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclProtected">
            <summary>Specifies that the resource manager prevents auto-inheritance. Set by resource managers or users.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.DiscretionaryAclUntrusted">
            <summary>Ignored.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.GroupDefaulted">
            <summary>Specifies that the group
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                was obtained by a defaulting mechanism. Set by resource managers only; should not be set by callers.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.None">
            <summary>No control flags.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.OwnerDefaulted">
            <summary>Specifies that the owner
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                was obtained by a defaulting mechanism. Set by resource managers only; should not be set by callers.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.RMControlValid">
            <summary>Specifies that the contents of the Reserved field are valid.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.SelfRelative">
            <summary>Specifies that the security descriptor binary representation is in the self-relative format. This
                flag is always set.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.ServerSecurity">
            <summary>Ignored.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInherited">
            <summary>Specifies that the System Access Control List (SACL) has been automatically inherited from the
                parent. Set by resource managers only.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.SystemAclAutoInheritRequired">
            <summary>Ignored.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.SystemAclDefaulted">
            <summary>Specifies that the SACL was obtained by a defaulting mechanism. Set by resource managers only.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.SystemAclPresent">
            <summary>Specifies that the SACL is not<see langword="null"/>. Set by resource managers or users.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ControlFlags.SystemAclProtected">
            <summary>Specifies that the resource manager prevents auto-inheritance. Set by resource managers or users.
            </summary>
        </member>
        <member name="T:System.Security.AccessControl.CustomAce">
            <summary>Represents an Access Control Entry (ACE) that is not defined by one of the members of the
                <see cref="T:System.Security.AccessControl.AceType"/>
                enumeration.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength">
            <summary>Returns the maximum allowed length of an opaque data blob for this
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.CustomAce.#ctor(System.Security.AccessControl.AceType,System.Security.AccessControl.AceFlags,System.Byte[])">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                class.
            </summary>
            <param name="type">Type of the new Access Control Entry (ACE). This value must be greater than<see
                    cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType"/>.
            </param>
            <param name="flags">Flags that specify information about the inheritance, inheritance propagation, and
                auditing conditions for the new ACE.
            </param>
            <param name="opaque">An array of byte values that contains the data for the new ACE. This value can be<see
                    langword="null"/>. The length of this array must not be greater than the value of the
                <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength"/>
                field, and must be a multiple of four.
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="type"/>
                parameter is not greater than
                <see cref="F:System.Security.AccessControl.AceType.MaxDefinedAceType"/>
                or the length of the
                <paramref name="opaque"/>
                array is either greater than the value of the
                <see cref="F:System.Security.AccessControl.CustomAce.MaxOpaqueLength"/>
                field or not a multiple of four.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.CustomAce.GetOpaque">
            <summary>Returns the opaque data associated with this
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </summary>
            <returns>An array of byte values that represents the opaque data associated with this
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.CustomAce.SetOpaque(System.Byte[])">
            <summary>Sets the opaque callback data associated with this
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </summary>
            <param name="opaque">An array of byte values that represents the opaque callback data for this
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </param>
        </member>
        <member name="P:System.Security.AccessControl.CustomAce.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.CustomAce.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.CustomAce.OpaqueLength">
            <summary>Gets the length of the opaque data associated with this
                <see cref="T:System.Security.AccessControl.CustomAce"/>
                object.
            </summary>
            <returns>The length of the opaque callback data.</returns>
        </member>
        <member name="T:System.Security.AccessControl.DiscretionaryAcl">
            <summary>Represents a Discretionary Access Control List (DACL).</summary>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                class with the specified values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object is a container.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object is a directory object Access Control List (ACL).
            </param>
            <param name="revision">The revision level of the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </param>
            <param name="capacity">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object can contain. This number is to be used only as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                class with the specified values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object is a container.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object is a directory object Access Control List (ACL).
            </param>
            <param name="capacity">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object can contain. This number is to be used only as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                class with the specified values from the specified
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object is a container.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object is a directory object Access Control List (ACL).
            </param>
            <param name="rawAcl">The underlying
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object for the new
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object. Specify
                <see langword="null"/>
                to create an empty ACL.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Adds an Access Control Entry (ACE) with the specified settings to the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to add.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to add an ACE.
            </param>
            <param name="accessMask">The access rule for the new ACE.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new ACE.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new ACE.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Adds an Access Control Entry (ACE) with the specified settings to the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type for the new ACE.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to add.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to add an ACE.
            </param>
            <param name="accessMask">The access rule for the new ACE.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new ACE.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new ACE.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the new ACE applies.</param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the new
                ACE.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.AddAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
            <summary>Adds an Access Control Entry (ACE) with the specified settings to the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to add.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to add an ACE.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                for the new access.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Removes the specified access control rule from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an access control rule.
            </param>
            <param name="accessMask">The access mask for the rule to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the rule to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the rule to be
                removed.
            </param>
            <returns>
                <see langword="true"/>
                if this method successfully removes the specified access; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Removes the specified access control rule from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an access control rule.
            </param>
            <param name="accessMask">The access mask for the access control rule to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the access control rule to
                be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the access
                control rule to be removed.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the removed access control rule
                applies.
            </param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the removed
                access control rule.
            </param>
            <returns>
                <see langword="true"/>
                if this method successfully removes the specified access; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
            <summary>Removes the specified access control rule from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an access control rule.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                for which to remove access.
            </param>
            <returns>Returns<see cref="T:System.Boolean"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Removes the specified Access Control Entry (ACE) from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an ACE.
            </param>
            <param name="accessMask">The access mask for the ACE to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the ACE to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the ACE to be
                removed.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Removes the specified Access Control Entry (ACE) from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type for the ACE to be removed.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an ACE.
            </param>
            <param name="accessMask">The access mask for the ACE to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the ACE to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the ACE to be
                removed.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the removed ACE applies.</param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the removed
                ACE.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.RemoveAccessSpecific(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
            <summary>Removes the specified Access Control Entry (ACE) from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an ACE.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                for which to remove access.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Sets the specified access control for the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to set.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to set an ACE.
            </param>
            <param name="accessMask">The access rule for the new ACE.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new ACE.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new ACE.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Sets the specified access control for the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to set.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to set an ACE.
            </param>
            <param name="accessMask">The access rule for the new ACE.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new ACE.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new ACE.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the new ACE applies.</param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the new
                ACE.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.DiscretionaryAcl.SetAccess(System.Security.AccessControl.AccessControlType,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAccessRule)">
            <summary>Sets the specified access control for the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <param name="accessType">The type of access control (allow or deny) to set.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to set an ACE.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                for which to set access.
            </param>
        </member>
        <member name="T:System.Security.AccessControl.GenericAce">
            <summary>Represents an Access Control Entry (ACE), and is the base class for all other ACE classes.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.Copy">
            <summary>Creates a deep copy of this Access Control Entry (ACE).</summary>
            <returns>The
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object that this method creates.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.CreateFromBinaryForm(System.Byte[],System.Int32)">
            <summary>Creates a
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object from the specified binary data.
            </summary>
            <param name="binaryForm">The binary data from which to create the new
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </param>
            <param name="offset">The offset at which to begin unmarshaling.</param>
            <returns>The
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object this method creates.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.Equals(System.Object)">
            <summary>Determines whether the specified
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object is equal to the current
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </summary>
            <param name="o">The
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object to compare to the current
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </param>
            <returns>
                <see langword="true"/>
                if the specified
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object is equal to the current
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.GetHashCode">
            <summary>Serves as a hash function for the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                class. The
                <see cref="M:System.Security.AccessControl.GenericAce.GetHashCode"/>
                method is suitable for use in hashing algorithms and data structures like a hash table.
            </summary>
            <returns>A hash code for the current
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.op_Equality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
            <summary>Determines whether the specified
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                objects are considered equal.
            </summary>
            <param name="left">The first
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object to compare.
            </param>
            <param name="right">The second
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                to compare.
            </param>
            <returns>
                <see langword="true"/>
                if the two
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                objects are equal; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericAce.op_Inequality(System.Security.AccessControl.GenericAce,System.Security.AccessControl.GenericAce)">
            <summary>Determines whether the specified
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                objects are considered unequal.
            </summary>
            <param name="left">The first
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object to compare.
            </param>
            <param name="right">The second
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                to compare.
            </param>
            <returns>
                <see langword="true"/>
                if the two
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                objects are unequal; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.AceFlags">
            <summary>Gets or sets the
                <see cref="T:System.Security.AccessControl.AceFlags"/>
                associated with this
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </summary>
            <returns>The
                <see cref="T:System.Security.AccessControl.AceFlags"/>
                associated with this
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.AceType">
            <summary>Gets the type of this Access Control Entry (ACE).</summary>
            <returns>The type of this ACE.</returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.AuditFlags">
            <summary>Gets the audit information associated with this Access Control Entry (ACE).</summary>
            <returns>The audit information associated with this Access Control Entry (ACE).</returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.GenericAce.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.InheritanceFlags">
            <summary>Gets flags that specify the inheritance properties of this Access Control Entry (ACE).</summary>
            <returns>Flags that specify the inheritance properties of this ACE.</returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.IsInherited">
            <summary>Gets a Boolean value that specifies whether this Access Control Entry (ACE) is inherited or is set
                explicitly.
            </summary>
            <returns>
                <see langword="true"/>
                if this ACE is inherited; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAce.PropagationFlags">
            <summary>Gets flags that specify the inheritance propagation properties of this Access Control Entry
                (ACE).
            </summary>
            <returns>Flags that specify the inheritance propagation properties of this ACE.</returns>
        </member>
        <member name="T:System.Security.AccessControl.GenericAcl">
            <summary>Represents an access control list (ACL) and is the base class for the<see
                    cref="T:System.Security.AccessControl.CommonAcl"/>,<see
                    cref="T:System.Security.AccessControl.DiscretionaryAcl"/>,<see
                    cref="T:System.Security.AccessControl.RawAcl"/>, and
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                classes.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.GenericAcl.AclRevision">
            <summary>The revision level of the current<see cref="T:System.Security.AccessControl.GenericAcl"/>. This
                value is returned by the
                <see cref="P:System.Security.AccessControl.GenericAcl.Revision"/>
                property for Access Control Lists (ACLs) that are not associated with Directory Services objects.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.GenericAcl.AclRevisionDS">
            <summary>The revision level of the current<see cref="T:System.Security.AccessControl.GenericAcl"/>. This
                value is returned by the
                <see cref="P:System.Security.AccessControl.GenericAcl.Revision"/>
                property for Access Control Lists (ACLs) that are associated with Directory Services objects.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.GenericAcl.MaxBinaryLength">
            <summary>The maximum allowed binary length of a
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                object.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.GenericAcl.#ctor">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                class.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.GenericAcl.CopyTo(System.Security.AccessControl.GenericAce[],System.Int32)">
            <summary>Copies each
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                of the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                into the specified array.
            </summary>
            <param name="array">The array into which copies of the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                objects contained by the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                are placed.
            </param>
            <param name="index">The zero-based index of
                <paramref name="array"/>
                where the copying begins.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.GenericAcl.GetEnumerator">
            <summary>Retrieves an object that you can use to iterate through the access control entries (ACEs) in an
                access control list (ACL).
            </summary>
            <returns>An enumerator object.</returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
            <summary>Copies each
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                of the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                into the specified array.
            </summary>
            <param name="array">The array into which copies of the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                objects contained by the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                are placed.
            </param>
            <param name="index">The zero-based index of
                <paramref name="array"/>
                where the copying begins.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.GenericAcl.System#Collections#IEnumerable#GetEnumerator">
            <summary>Returns a new instance of the
                <see cref="T:System.Security.AccessControl.AceEnumerator"/>
                class cast as an instance of the
                <see cref="T:System.Collections.IEnumerator"/>
                interface.
            </summary>
            <returns>A new
                <see cref="T:System.Security.AccessControl.AceEnumerator"/>
                object, cast as an instance of the
                <see cref="T:System.Collections.IEnumerator"/>
                interface.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAcl.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.GenericAcl.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAcl.Count">
            <summary>Gets the number of access control entries (ACEs) in the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                object.
            </summary>
            <returns>The number of ACEs in the current
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAcl.IsSynchronized">
            <summary>This property is always set to<see langword="false"/>. It is implemented only because it is
                required for the implementation of the
                <see cref="T:System.Collections.ICollection"/>
                interface.
            </summary>
            <returns>Always<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAcl.Item(System.Int32)">
            <summary>Gets or sets the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                at the specified index.
            </summary>
            <param name="index">The zero-based index of the
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                to get or set.
            </param>
            <returns>The
                <see cref="T:System.Security.AccessControl.GenericAce"/>
                at the specified index.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAcl.Revision">
            <summary>Gets the revision level of the<see cref="T:System.Security.AccessControl.GenericAcl"/>.
            </summary>
            <returns>A byte value that specifies the revision level of the<see
                    cref="T:System.Security.AccessControl.GenericAcl"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericAcl.SyncRoot">
            <summary>This property always returns<see langword="null"/>. It is implemented only because it is required
                for the implementation of the
                <see cref="T:System.Collections.ICollection"/>
                interface.
            </summary>
            <returns>Always returns<see langword="null"/>.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.GenericSecurityDescriptor">
            <summary>Represents a security descriptor. A security descriptor includes an owner, a primary group, a
                Discretionary Access Control List (DACL), and a System Access Control List (SACL).
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Returns an array of byte values that represents the information contained in this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.GetSddlForm(System.Security.AccessControl.AccessControlSections)">
            <summary>Returns the Security Descriptor Definition Language (SDDL) representation of the specified sections
                of the security descriptor that this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object represents.
            </summary>
            <param name="includeSections">Specifies which sections (access rules, audit rules, primary group, owner) of
                the security descriptor to get.
            </param>
            <returns>The SDDL representation of the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.GenericSecurityDescriptor.IsSddlConversionSupported">
            <summary>Returns a boolean value that specifies whether the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object can be converted to the Security Descriptor Definition Language (SDDL) format.
            </summary>
            <returns>
                <see langword="true"/>
                if the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object can be converted to the Security Descriptor Definition Language (SDDL) format; otherwise,<see
                    langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.GenericSecurityDescriptor.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.ControlFlags">
            <summary>Gets values that specify behavior of the
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </summary>
            <returns>One or more values of the
                <see cref="T:System.Security.AccessControl.ControlFlags"/>
                enumeration combined with a logical OR operation.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Group">
            <summary>Gets or sets the primary group for this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </summary>
            <returns>The primary group for this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Owner">
            <summary>Gets or sets the owner of the object associated with this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </summary>
            <returns>The owner of the object associated with this
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.GenericSecurityDescriptor.Revision">
            <summary>Gets the revision level of the
                <see cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>
                object.
            </summary>
            <returns>A byte value that specifies the revision level of the<see
                    cref="T:System.Security.AccessControl.GenericSecurityDescriptor"/>.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.InheritanceFlags">
            <summary>Inheritance flags specify the semantics of inheritance for access control entries (ACEs).</summary>
        </member>
        <member name="F:System.Security.AccessControl.InheritanceFlags.ContainerInherit">
            <summary>The ACE is inherited by child container objects.</summary>
        </member>
        <member name="F:System.Security.AccessControl.InheritanceFlags.None">
            <summary>The ACE is not inherited by child objects.</summary>
        </member>
        <member name="F:System.Security.AccessControl.InheritanceFlags.ObjectInherit">
            <summary>The ACE is inherited by child leaf objects.</summary>
        </member>
        <member name="T:System.Security.AccessControl.KnownAce">
            <summary>Encapsulates all Access Control Entry (ACE) types currently defined by Microsoft Corporation. All
                <see cref="T:System.Security.AccessControl.KnownAce"/>
                objects contain a 32-bit access mask and a
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
        </member>
        <member name="P:System.Security.AccessControl.KnownAce.AccessMask">
            <summary>Gets or sets the access mask for this
                <see cref="T:System.Security.AccessControl.KnownAce"/>
                object.
            </summary>
            <returns>The access mask for this
                <see cref="T:System.Security.AccessControl.KnownAce"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.KnownAce.SecurityIdentifier">
            <summary>Gets or sets the
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object associated with this
                <see cref="T:System.Security.AccessControl.KnownAce"/>
                object.
            </summary>
            <returns>The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object associated with this
                <see cref="T:System.Security.AccessControl.KnownAce"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.NativeObjectSecurity">
            <summary>Provides the ability to control access to native objects without direct manipulation of Access
                Control Lists (ACLs). Native object types are defined by the
                <see cref="T:System.Security.AccessControl.ResourceType"/>
                enumeration.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class with the specified values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class with the specified values. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="handle">The handle of the securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to include in this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class with the specified values. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="handle">The handle of the securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to include in this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object.
            </param>
            <param name="exceptionFromErrorCode">A delegate implemented by integrators that provides custom
                exceptions.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class by using the specified values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="exceptionFromErrorCode">A delegate implemented by integrators that provides custom
                exceptions.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class with the specified values. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="name">The name of the securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to include in this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                class with the specified values. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="name">The name of the securable object with which the new
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to include in this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object.
            </param>
            <param name="exceptionFromErrorCode">A delegate implemented by integrators that provides custom
                exceptions.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object to permanent storage. We recommend.persist that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="handle">The handle of the securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
            <exception cref="T:System.IO.FileNotFoundException">The securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated is either a directory or a file, and that directory or file could not be found.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Object)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object to permanent storage. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="handle">The handle of the securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
            <exception cref="T:System.IO.FileNotFoundException">The securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated is either a directory or a file, and that directory or file could not be found.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object to permanent storage. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="name">The name of the securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
            <exception cref="T:System.IO.FileNotFoundException">The securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated is either a directory or a file, and that directory or file could not be found.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.NativeObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections,System.Object)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object to permanent storage. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="name">The name of the securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
            <exception cref="T:System.IO.FileNotFoundException">The securable object with which this
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated is either a directory or a file, and that directory or file could not be found.
            </exception>
        </member>
        <member name="T:System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode">
            <summary>Provides a way for integrators to map numeric error codes to specific exceptions that they
                create.
            </summary>
            <param name="errorCode">The numeric error code.</param>
            <param name="name">The name of the securable object with which the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="handle">The handle of the securable object with which the
                <see cref="T:System.Security.AccessControl.NativeObjectSecurity"/>
                object is associated.
            </param>
            <param name="context">An object that contains contextual information about the source or destination of the
                exception.
            </param>
            <returns>The
                <see cref="T:System.Exception"/>
                this delegate creates.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.ObjectAccessRule">
            <summary>Represents a combination of a user's identity, an access mask, and an access control type (allow or
                deny). An
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                object also contains information about the type of object to which the rule applies, the type of child
                object that can inherit the rule, how the rule is inherited by child objects, and how that inheritance
                is propagated.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                class with the specified values.
            </summary>
            <param name="identity">The identity to which the access rule applies. It must be an object that can be cast
                as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">
                <see langword="true"/>
                if this rule is inherited from a parent container.
            </param>
            <param name="inheritanceFlags">Specifies the inheritance properties of the access rule.</param>
            <param name="propagationFlags">Specifies whether inherited access rules are automatically propagated. The
                propagation flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="objectType">The type of object to which the rule applies.</param>
            <param name="inheritedObjectType">The type of child object that can inherit the rule.</param>
            <param name="type">Specifies whether this rule allows or denies access.</param>
            <exception cref="T:System.ArgumentException">The value of the
                <paramref name="identity"/>
                parameter cannot be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>, or the
                <paramref name="type"/>
                parameter contains an invalid value.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="accessMask"/>
                parameter is 0, or the
                <paramref name="inheritanceFlags"/>
                or
                <paramref name="propagationFlags"/>
                parameters contain unrecognized flag values.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType">
            <summary>Gets the type of child object that can inherit the
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                object.
            </summary>
            <returns>The type of child object that can inherit the
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectFlags">
            <summary>Gets flags that specify if the
                <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType"/>
                and
                <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType"/>
                properties of the
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                object contain valid values.
            </summary>
            <returns>
                <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent"/>
                specifies that the
                <see cref="P:System.Security.AccessControl.ObjectAccessRule.ObjectType"/>
                property contains a valid value.
                <see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent"/>
                specifies that the
                <see cref="P:System.Security.AccessControl.ObjectAccessRule.InheritedObjectType"/>
                property contains a valid value. These values can be combined with a logical OR.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAccessRule.ObjectType">
            <summary>Gets the type of object to which the
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                applies.
            </summary>
            <returns>The type of object to which the
                <see cref="T:System.Security.AccessControl.ObjectAccessRule"/>
                applies.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.ObjectAce">
            <summary>Controls access to Directory Services objects. This class represents an Access Control Entry (ACE)
                associated with a directory object.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectAce.#ctor(System.Security.AccessControl.AceFlags,System.Security.AccessControl.AceQualifier,System.Int32,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid,System.Boolean,System.Byte[])">
            <summary>Initiates a new instance of the
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                class.
            </summary>
            <param name="aceFlags">The inheritance, inheritance propagation, and auditing conditions for the new Access
                Control Entry (ACE).
            </param>
            <param name="qualifier">The use of the new ACE.</param>
            <param name="accessMask">The access mask for the ACE.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                associated with the new ACE.
            </param>
            <param name="flags">Whether the
                <paramref name="type"/>
                and
                <paramref name="inheritedType"/>
                parameters contain valid object GUIDs.
            </param>
            <param name="type">A GUID that identifies the object type to which the new ACE applies.</param>
            <param name="inheritedType">A GUID that identifies the object type that can inherit the new ACE.</param>
            <param name="isCallback">
                <see langword="true"/>
                if the new ACE is a callback type ACE.
            </param>
            <param name="opaque">Opaque data associated with the new ACE. This is allowed only for callback ACE types.
                The length of this array must not be greater than the return value of the
                <see cref="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)"/>
                method.
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException">The qualifier parameter contains an invalid value or
                the length of the value of the opaque parameter is greater than the return value of the
                <see cref="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)"/>
                method.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.ObjectAce.MaxOpaqueLength(System.Boolean)">
            <summary>Returns the maximum allowed length, in bytes, of an opaque data BLOB for callback Access Control
                Entries (ACEs).
            </summary>
            <param name="isCallback">True if the
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                is a callback ACE type.
            </param>
            <returns>The maximum allowed length, in bytes, of an opaque data BLOB for callback Access Control Entries
                (ACEs).
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAce.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.ObjectAce.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType">
            <summary>Gets or sets the GUID of the object type that can inherit the Access Control Entry (ACE) that this
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object represents.
            </summary>
            <returns>The GUID of the object type that can inherit the Access Control Entry (ACE) that this
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object represents.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceFlags">
            <summary>Gets or sets flags that specify whether the
                <see cref="P:System.Security.AccessControl.ObjectAce.ObjectAceType"/>
                and
                <see cref="P:System.Security.AccessControl.ObjectAce.InheritedObjectAceType"/>
                properties contain values that identify valid object types.
            </summary>
            <returns>On or more members of the
                <see cref="T:System.Security.AccessControl.ObjectAceFlags"/>
                enumeration combined with a logical OR operation.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAce.ObjectAceType">
            <summary>Gets or sets the GUID of the object type associated with this
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object.
            </summary>
            <returns>The GUID of the object type associated with this
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.ObjectAceFlags">
            <summary>Specifies the presence of object types for Access Control Entries (ACEs).</summary>
        </member>
        <member name="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent">
            <summary>The type of object that can inherit the ACE.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ObjectAceFlags.None">
            <summary>No object types are present.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent">
            <summary>The type of object that is associated with the ACE is present.</summary>
        </member>
        <member name="T:System.Security.AccessControl.ObjectAuditRule">
            <summary>Represents a combination of a user's identity, an access mask, and audit conditions. An
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                object also contains information about the type of object to which the rule applies, the type of child
                object that can inherit the rule, how the rule is inherited by child objects, and how that inheritance
                is propagated.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Guid,System.Guid,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                class.
            </summary>
            <param name="identity">The identity to which the access rule applies. It must be an object that can be cast
                as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">
                <see langword="true"/>
                if this rule is inherited from a parent container.
            </param>
            <param name="inheritanceFlags">Specifies the inheritance properties of the access rule.</param>
            <param name="propagationFlags">Whether inherited access rules are automatically propagated. The propagation
                flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="objectType">The type of object to which the rule applies.</param>
            <param name="inheritedObjectType">The type of child object that can inherit the rule.</param>
            <param name="auditFlags">The audit conditions.</param>
            <exception cref="T:System.ArgumentException">The value of the
                <paramref name="identity"/>
                parameter cannot be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>, or the
                <paramref name="type"/>
                parameter contains an invalid value.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="accessMask"/>
                parameter is 0, or the
                <paramref name="inheritanceFlags"/>
                or
                <paramref name="propagationFlags"/>
                parameters contain unrecognized flag values.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType">
            <summary>Gets the type of child object that can inherit the
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                object.
            </summary>
            <returns>The type of child object that can inherit the
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectFlags">
            <summary>
                <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType"/>
                and
                <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType"/>
                properties of the
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                object contain valid values.
            </summary>
            <returns>
                <see cref="F:System.Security.AccessControl.ObjectAceFlags.ObjectAceTypePresent"/>
                specifies that the
                <see cref="P:System.Security.AccessControl.ObjectAuditRule.ObjectType"/>
                property contains a valid value.
                <see cref="F:System.Security.AccessControl.ObjectAceFlags.InheritedObjectAceTypePresent"/>
                specifies that the
                <see cref="P:System.Security.AccessControl.ObjectAuditRule.InheritedObjectType"/>
                property contains a valid value. These values can be combined with a logical OR.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectAuditRule.ObjectType">
            <summary>Gets the type of object to which the
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                applies.
            </summary>
            <returns>The type of object to which the
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                applies.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.ObjectSecurity">
            <summary>Provides the ability to control access to objects without direct manipulation of Access Control
                Lists (ACLs). This class is the abstract base class for the
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                and
                <see cref="T:System.Security.AccessControl.DirectoryObjectSecurity"/>
                classes.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                class.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Boolean,System.Boolean)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                class.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is a container object.
            </param>
            <param name="isDS">True if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is a directory object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.#ctor(System.Security.AccessControl.CommonSecurityDescriptor)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                class.
            </summary>
            <param name="securityDescriptor">The
                <see cref="T:System.Security.AccessControl.CommonSecurityDescriptor"/>
                of the new
                <see cref="T:System.Security.AccessControl.CommonObjectSecurity"/>
                instance.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                class with the specified values.
            </summary>
            <param name="identityReference">The identity to which the access rule applies. It must be an object that can
                be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">true if this rule is inherited from a parent container.</param>
            <param name="inheritanceFlags">Specifies the inheritance properties of the access rule.</param>
            <param name="propagationFlags">Specifies whether inherited access rules are automatically propagated. The
                propagation flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="type">Specifies the valid access control type.</param>
            <returns>The
                <see cref="T:System.Security.AccessControl.AccessRule"/>
                object that this method creates.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule"/>
                class with the specified values.
            </summary>
            <param name="identityReference">The identity to which the audit rule applies. It must be an object that can
                be cast as a<see cref="T:System.Security.Principal.SecurityIdentifier"/>.
            </param>
            <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous
                bits, the meaning of which is defined by the individual integrators.
            </param>
            <param name="isInherited">
                <see langword="true"/>
                if this rule is inherited from a parent container.
            </param>
            <param name="inheritanceFlags">Specifies the inheritance properties of the audit rule.</param>
            <param name="propagationFlags">Specifies whether inherited audit rules are automatically propagated. The
                propagation flags are ignored if
                <paramref name="inheritanceFlags"/>
                is set to<see cref="F:System.Security.AccessControl.InheritanceFlags.None"/>.
            </param>
            <param name="flags">Specifies the conditions for which the rule is audited.</param>
            <returns>The
                <see cref="T:System.Security.AccessControl.AuditRule"/>
                object that this method creates.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.GetGroup(System.Type)">
            <summary>Gets the primary group associated with the specified owner.</summary>
            <param name="targetType">The owner for which to get the primary group.</param>
            <returns>The primary group associated with the specified owner.</returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.GetOwner(System.Type)">
            <summary>Gets the owner associated with the specified primary group.</summary>
            <param name="targetType">The primary group for which to get the owner.</param>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="targetType"/>
                is<see langword="null"/>.
            </exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="targetType"/>
                is not an
                <see cref="T:System.Security.Principal.IdentityReference"/>
                type.
            </exception>
            <exception cref="T:System.Security.Principal.IdentityNotMappedException">Some or all identity references
                could not be translated.
            </exception>
            <exception cref="T:System.SystemException">A Win32 error code was returned.</exception>
            <returns>The owner associated with the specified group.</returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorBinaryForm">
            <summary>Returns an array of byte values that represents the security descriptor information for this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <returns>An array of byte values that represents the security descriptor for this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object. This method returns
                <see langword="null"/>
                if there is no security information in this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.GetSecurityDescriptorSddlForm(System.Security.AccessControl.AccessControlSections)">
            <summary>Returns the Security Descriptor Definition Language (SDDL) representation of the specified sections
                of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="includeSections">Specifies which sections (access rules, audit rules, primary group, owner) of
                the security descriptor to get.
            </param>
            <returns>The SDDL representation of the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.IsSddlConversionSupported">
            <summary>Returns a Boolean value that specifies whether the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object can be converted to the Security Descriptor Definition Language (SDDL) format.
            </summary>
            <returns>
                <see langword="true"/>
                if the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object can be converted to the Security Descriptor Definition Language (SDDL) format; otherwise,<see
                    langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccess(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
            <summary>Applies the specified modification to the Discretionary Access Control List (DACL) associated with
                this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="modification">The modification to apply to the DACL.</param>
            <param name="rule">The access rule to modify.</param>
            <param name="modified">
                <see langword="true"/>
                if the DACL is successfully modified; otherwise,<see langword="false"/>.
            </param>
            <returns>
                <see langword="true"/>
                if the DACL is successfully modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAccessRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AccessRule,System.Boolean@)">
            <summary>Applies the specified modification to the Discretionary Access Control List (DACL) associated with
                this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="modification">The modification to apply to the DACL.</param>
            <param name="rule">The access rule to modify.</param>
            <param name="modified">
                <see langword="true"/>
                if the DACL is actually modified; otherwise,<see langword="false"/>.
            </param>
            <returns>
                <see langword="true"/>
                if the DACL is successfully modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAudit(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
            <summary>Applies the specified modification to the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="modification">The modification to apply to the SACL.</param>
            <param name="rule">The audit rule to modify.</param>
            <param name="modified">
                <see langword="true"/>
                if the SACL is actually modified; otherwise,<see langword="false"/>.
            </param>
            <returns>
                <see langword="true"/>
                if the SACL is successfully modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.ModifyAuditRule(System.Security.AccessControl.AccessControlModification,System.Security.AccessControl.AuditRule,System.Boolean@)">
            <summary>Applies the specified modification to the System Access Control List (SACL) associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="modification">The modification to apply to the SACL.</param>
            <param name="rule">The audit rule to modify.</param>
            <param name="modified">
                <see langword="true"/>
                if the SACL is successfully modified; otherwise,<see langword="false"/>.
            </param>
            <returns>
                <see langword="true"/>
                if the SACL is successfully modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Boolean,System.String,System.Security.AccessControl.AccessControlSections)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object to permanent storage. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="enableOwnershipPrivilege">
                <see langword="true"/>
                to enable the privilege that allows the caller to take ownership of the object.
            </param>
            <param name="name">The name used to retrieve the persisted information.</param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object to permanent storage. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="handle">The handle used to retrieve the persisted information.</param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.Persist(System.String,System.Security.AccessControl.AccessControlSections)">
            <summary>Saves the specified sections of the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object to permanent storage. We recommend that the values of the
                <paramref name="includeSections"/>
                parameters passed to the constructor and persist methods be identical.
            </summary>
            <param name="name">The name used to retrieve the persisted information.</param>
            <param name="includeSections">One of the
                <see cref="T:System.Security.AccessControl.AccessControlSections"/>
                enumeration values that specifies the sections of the security descriptor (access rules, audit rules,
                owner, primary group) of the securable object to save.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAccessRules(System.Security.Principal.IdentityReference)">
            <summary>Removes all access rules associated with the specified<see
                    cref="T:System.Security.Principal.IdentityReference"/>.
            </summary>
            <param name="identity">The
                <see cref="T:System.Security.Principal.IdentityReference"/>
                for which to remove all access rules.
            </param>
            <exception cref="T:System.InvalidOperationException">All access rules are not in canonical order.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.PurgeAuditRules(System.Security.Principal.IdentityReference)">
            <summary>Removes all audit rules associated with the specified<see
                    cref="T:System.Security.Principal.IdentityReference"/>.
            </summary>
            <param name="identity">The
                <see cref="T:System.Security.Principal.IdentityReference"/>
                for which to remove all audit rules.
            </param>
            <exception cref="T:System.InvalidOperationException">All audit rules are not in canonical order.</exception>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.ReadLock">
            <summary>Locks this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object for read access.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.ReadUnlock">
            <summary>Unlocks this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object for read access.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetAccessRuleProtection(System.Boolean,System.Boolean)">
            <summary>Sets or removes protection of the access rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object. Protected access rules cannot be modified by parent objects through inheritance.
            </summary>
            <param name="isProtected">
                <see langword="true"/>
                to protect the access rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object from inheritance;
                <see langword="false"/>
                to allow inheritance.
            </param>
            <param name="preserveInheritance">
                <see langword="true"/>
                to preserve inherited access rules;
                <see langword="false"/>
                to remove inherited access rules. This parameter is ignored if
                <paramref name="isProtected"/>
                is<see langword="false"/>.
            </param>
            <exception cref="T:System.InvalidOperationException">This method attempts to remove inherited rules from a
                non-canonical Discretionary Access Control List (DACL).
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetAuditRuleProtection(System.Boolean,System.Boolean)">
            <summary>Sets or removes protection of the audit rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object. Protected audit rules cannot be modified by parent objects through inheritance.
            </summary>
            <param name="isProtected">
                <see langword="true"/>
                to protect the audit rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object from inheritance;
                <see langword="false"/>
                to allow inheritance.
            </param>
            <param name="preserveInheritance">
                <see langword="true"/>
                to preserve inherited audit rules;
                <see langword="false"/>
                to remove inherited audit rules. This parameter is ignored if
                <paramref name="isProtected"/>
                is<see langword="false"/>.
            </param>
            <exception cref="T:System.InvalidOperationException">This method attempts to remove inherited rules from a
                non-canonical System Access Control List (SACL).
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetGroup(System.Security.Principal.IdentityReference)">
            <summary>Sets the primary group for the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="identity">The primary group to set.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetOwner(System.Security.Principal.IdentityReference)">
            <summary>Sets the owner for the security descriptor associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <param name="identity">The owner to set.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[])">
            <summary>Sets the security descriptor for this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object from the specified array of byte values.
            </summary>
            <param name="binaryForm">The array of bytes from which to set the security descriptor.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorBinaryForm(System.Byte[],System.Security.AccessControl.AccessControlSections)">
            <summary>Sets the specified sections of the security descriptor for this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object from the specified array of byte values.
            </summary>
            <param name="binaryForm">The array of bytes from which to set the security descriptor.</param>
            <param name="includeSections">The sections (access rules, audit rules, owner, primary group) of the security
                descriptor to set.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String)">
            <summary>Sets the security descriptor for this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object from the specified Security Descriptor Definition Language (SDDL) string.
            </summary>
            <param name="sddlForm">The SDDL string from which to set the security descriptor.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.SetSecurityDescriptorSddlForm(System.String,System.Security.AccessControl.AccessControlSections)">
            <summary>Sets the specified sections of the security descriptor for this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object from the specified Security Descriptor Definition Language (SDDL) string.
            </summary>
            <param name="sddlForm">The SDDL string from which to set the security descriptor.</param>
            <param name="includeSections">The sections (access rules, audit rules, owner, primary group) of the security
                descriptor to set.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.WriteLock">
            <summary>Locks this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object for write access.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity.WriteUnlock">
            <summary>Unlocks this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object for write access.
            </summary>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRightType">
            <summary>Gets the
                <see cref="T:System.Type"/>
                of the securable object associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </summary>
            <returns>The type of the securable object associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRulesModified">
            <summary>Gets or sets a Boolean value that specifies whether the access rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object have been modified.
            </summary>
            <returns>
                <see langword="true"/>
                if the access rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object have been modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AccessRuleType">
            <summary>Gets the
                <see cref="T:System.Type"/>
                of the object associated with the access rules of this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object. The
                <see cref="T:System.Type"/>
                object must be an object that can be cast as a
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <returns>The type of the object associated with the access rules of this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesCanonical">
            <summary>Gets a Boolean value that specifies whether the access rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object are in canonical order.
            </summary>
            <returns>
                <see langword="true"/>
                if the access rules are in canonical order; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AreAccessRulesProtected">
            <summary>Gets a Boolean value that specifies whether the Discretionary Access Control List (DACL) associated
                with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is protected.
            </summary>
            <returns>
                <see langword="true"/>
                if the DACL is protected; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesCanonical">
            <summary>Gets a Boolean value that specifies whether the audit rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object are in canonical order.
            </summary>
            <returns>
                <see langword="true"/>
                if the audit rules are in canonical order; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AreAuditRulesProtected">
            <summary>Gets a Boolean value that specifies whether the System Access Control List (SACL) associated with
                this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is protected.
            </summary>
            <returns>
                <see langword="true"/>
                if the SACL is protected; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRulesModified">
            <summary>Gets or sets a Boolean value that specifies whether the audit rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object have been modified.
            </summary>
            <returns>
                <see langword="true"/>
                if the audit rules associated with this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object have been modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.AuditRuleType">
            <summary>Gets the
                <see cref="T:System.Type"/>
                object associated with the audit rules of this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object. The
                <see cref="T:System.Type"/>
                object must be an object that can be cast as a
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <returns>The type of the object associated with the audit rules of this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.GroupModified">
            <summary>Gets or sets a Boolean value that specifies whether the group associated with the securable object
                has been modified.
            </summary>
            <returns>
                <see langword="true"/>
                if the group associated with the securable object has been modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.IsContainer">
            <summary>Gets a Boolean value that specifies whether this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is a container object.
            </summary>
            <returns>
                <see langword="true"/>
                if the
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is a container object; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.IsDS">
            <summary>Gets a Boolean value that specifies whether this
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is a directory object.
            </summary>
            <returns>
                <see langword="true"/>
                if the
                <see cref="T:System.Security.AccessControl.ObjectSecurity"/>
                object is a directory object; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.OwnerModified">
            <summary>Gets or sets a Boolean value that specifies whether the owner of the securable object has been
                modified.
            </summary>
            <returns>
                <see langword="true"/>
                if the owner of the securable object has been modified; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity.SecurityDescriptor">
            <summary>Gets the security descriptor for this instance.</summary>
            <returns>The security descriptor for this instance.</returns>
        </member>
        <member name="T:System.Security.AccessControl.ObjectSecurity`1">
            <summary>Provides the ability to control access to objects without direct manipulation of Access Control
                Lists (ACLs); also grants the ability to type-cast access rights.
            </summary>
            <typeparam name="T">The access rights for the object.</typeparam>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType)">
            <summary>Initializes a new instance of the ObjectSecurity`1 class.</summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of resource.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections)">
            <summary>Initializes a new instance of the ObjectSecurity`1 class.</summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of resource.</param>
            <param name="safeHandle">A handle.</param>
            <param name="includeSections">The sections to include.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.Runtime.InteropServices.SafeHandle,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
            <summary>Initializes a new instance of the ObjectSecurity`1 class.</summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of resource.</param>
            <param name="safeHandle">A handle.</param>
            <param name="includeSections">The sections to include.</param>
            <param name="exceptionFromErrorCode">A delegate implemented by integrators that provides custom
                exceptions.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections)">
            <summary>Initializes a new instance of the ObjectSecurity`1 class.</summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of resource.</param>
            <param name="name">The name of the securable object with which the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is associated.
            </param>
            <param name="includeSections">The sections to include.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.#ctor(System.Boolean,System.Security.AccessControl.ResourceType,System.String,System.Security.AccessControl.AccessControlSections,System.Security.AccessControl.NativeObjectSecurity.ExceptionFromErrorCode,System.Object)">
            <summary>Initializes a new instance of the ObjectSecurity`1 class.</summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is a container object.
            </param>
            <param name="resourceType">The type of resource.</param>
            <param name="name">The name of the securable object with which the new
                <see cref="T:System.Security.AccessControl.ObjectSecurity`1"/>
                object is associated.
            </param>
            <param name="includeSections">The sections to include.</param>
            <param name="exceptionFromErrorCode">A delegate implemented by integrators that provides custom
                exceptions.
            </param>
            <param name="exceptionContext">An object that contains contextual information about the source or
                destination of the exception.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
            <summary>Initializes a new instance of the ObjectAccessRule class that represents a new access control rule
                for the associated security object.
            </summary>
            <param name="identityReference">Represents a user account.</param>
            <param name="accessMask">The access type.</param>
            <param name="isInherited">
                <see langword="true"/>
                if the access rule is inherited; otherwise,<see langword="false"/>.
            </param>
            <param name="inheritanceFlags">Specifies how to propagate access masks to child objects.</param>
            <param name="propagationFlags">Specifies how to propagate Access Control Entries (ACEs) to child objects.
            </param>
            <param name="type">Specifies whether access is allowed or denied.</param>
            <returns>Represents a new access control rule for the specified user, with the specified access rights,
                access control, and flags.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAccessRule(System.Security.AccessControl.AccessRule{`0})">
            <summary>Adds the specified access rule to the Discretionary Access Control List (DACL) associated with this
                ObjectSecurity`1 object.
            </summary>
            <param name="rule">The rule to add.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.AddAuditRule(System.Security.AccessControl.AuditRule{`0})">
            <summary>Adds the specified audit rule to the System Access Control List (SACL) associated with this
                ObjectSecurity`1 object.
            </summary>
            <param name="rule">The audit rule to add.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.AuditRule"/>
                class representing the specified audit rule for the specified user.
            </summary>
            <param name="identityReference">Represents a user account.</param>
            <param name="accessMask">An integer that specifies an access type.</param>
            <param name="isInherited">
                <see langword="true"/>
                if the access rule is inherited; otherwise,<see langword="false"/>.
            </param>
            <param name="inheritanceFlags">Specifies how to propagate access masks to child objects.</param>
            <param name="propagationFlags">Specifies how to propagate Access Control Entries (ACEs) to child objects.
            </param>
            <param name="flags">Describes the type of auditing to perform.</param>
            <returns>The specified audit rule for the specified user.</returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.Runtime.InteropServices.SafeHandle)">
            <summary>Saves the security descriptor associated with this ObjectSecurity`1 object to permanent storage,
                using the specified handle.
            </summary>
            <param name="handle">The handle of the securable object with which this ObjectSecurity`1 object is
                associated.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.Persist(System.String)">
            <summary>Saves the security descriptor associated with this ObjectSecurity`1 object to permanent storage,
                using the specified name.
            </summary>
            <param name="name">The name of the securable object with which this ObjectSecurity`1 object is associated.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRule(System.Security.AccessControl.AccessRule{`0})">
            <summary>Removes access rules that contain the same security identifier and access mask as the specified
                access rule from the Discretionary Access Control List (DACL) associated with this ObjectSecurity`1
                object.
            </summary>
            <param name="rule">The rule to remove.</param>
            <returns>
                <see langword="true"/>
                if the access rule was successfully removed; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleAll(System.Security.AccessControl.AccessRule{`0})">
            <summary>Removes all access rules that have the same security identifier as the specified access rule from
                the Discretionary Access Control List (DACL) associated with this ObjectSecurity`1 object.
            </summary>
            <param name="rule">The access rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAccessRuleSpecific(System.Security.AccessControl.AccessRule{`0})">
            <summary>Removes all access rules that exactly match the specified access rule from the Discretionary Access
                Control List (DACL) associated with this ObjectSecurity`1 object.
            </summary>
            <param name="rule">The access rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRule(System.Security.AccessControl.AuditRule{`0})">
            <summary>Removes audit rules that contain the same security identifier and access mask as the specified
                audit rule from the System Access Control List (SACL) associated with this ObjectSecurity`1 object.
            </summary>
            <param name="rule">The audit rule to remove.</param>
            <returns>
                <see langword="true"/>
                if the object was removed; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleAll(System.Security.AccessControl.AuditRule{`0})">
            <summary>Removes all audit rules that have the same security identifier as the specified audit rule from the
                System Access Control List (SACL) associated with this ObjectSecurity`1 object.
            </summary>
            <param name="rule">The audit rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.RemoveAuditRuleSpecific(System.Security.AccessControl.AuditRule{`0})">
            <summary>Removes all audit rules that exactly match the specified audit rule from the System Access Control
                List (SACL) associated with this ObjectSecurity`1 object.
            </summary>
            <param name="rule">The audit rule to remove.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.ResetAccessRule(System.Security.AccessControl.AccessRule{`0})">
            <summary>Removes all access rules in the Discretionary Access Control List (DACL) associated with this
                ObjectSecurity`1 object and then adds the specified access rule.
            </summary>
            <param name="rule">The access rule to reset.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAccessRule(System.Security.AccessControl.AccessRule{`0})">
            <summary>Removes all access rules that contain the same security identifier and qualifier as the specified
                access rule in the Discretionary Access Control List (DACL) associated with this ObjectSecurity`1 object
                and then adds the specified access rule.
            </summary>
            <param name="rule">The access rule to set.</param>
        </member>
        <member name="M:System.Security.AccessControl.ObjectSecurity`1.SetAuditRule(System.Security.AccessControl.AuditRule{`0})">
            <summary>Removes all audit rules that contain the same security identifier and qualifier as the specified
                audit rule in the System Access Control List (SACL) associated with this ObjectSecurity`1 object and
                then adds the specified audit rule.
            </summary>
            <param name="rule">The audit rule to set.</param>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRightType">
            <summary>Gets the Type of the securable object associated with this ObjectSecurity`1 object.</summary>
            <returns>The type of the securable object associated with the current instance.</returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity`1.AccessRuleType">
            <summary>Gets the Type of the object associated with the access rules of this ObjectSecurity`1 object.
            </summary>
            <returns>The Type of the object associated with the access rules of the current instance.</returns>
        </member>
        <member name="P:System.Security.AccessControl.ObjectSecurity`1.AuditRuleType">
            <summary>Gets the Type object associated with the audit rules of this ObjectSecurity`1 object.</summary>
            <returns>The Type object associated with the audit rules of the current instance.</returns>
        </member>
        <member name="T:System.Security.AccessControl.PrivilegeNotHeldException">
            <summary>The exception that is thrown when a method in the
                <see cref="N:System.Security.AccessControl"/>
                namespace attempts to enable a privilege that it does not have.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException"/>
                class.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException"/>
                class by using the specified privilege.
            </summary>
            <param name="privilege">The privilege that is not enabled.</param>
        </member>
        <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.PrivilegeNotHeldException"/>
                class by using the specified exception.
            </summary>
            <param name="privilege">The privilege that is not enabled.</param>
            <param name="inner">The exception that is the cause of the current exception. If the <c>innerException</c> parameter
                is not a null reference (
                <see langword="Nothing"/>
                in Visual Basic), the current exception is raised in a
                <see langword="catch"/>
                block that handles the inner exception.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.PrivilegeNotHeldException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Sets the
                <paramref name="info"/>
                parameter with information about the exception.
            </summary>
            <param name="info">The
                <see cref="T:System.Runtime.Serialization.SerializationInfo"/>
                that holds the serialized object data about the exception being thrown.
            </param>
            <param name="context">The
                <see cref="T:System.Runtime.Serialization.StreamingContext"/>
                that contains contextual information about the source or destination.
            </param>
        </member>
        <member name="P:System.Security.AccessControl.PrivilegeNotHeldException.PrivilegeName">
            <summary>Gets the name of the privilege that is not enabled.</summary>
            <returns>The name of the privilege that the method failed to enable.</returns>
        </member>
        <member name="T:System.Security.AccessControl.PropagationFlags">
            <summary>Specifies how Access Control Entries (ACEs) are propagated to child objects. These flags are
                significant only if inheritance flags are present.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.PropagationFlags.InheritOnly">
            <summary>Specifies that the ACE is propagated only to child objects. This includes both container and leaf
                child objects.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.PropagationFlags.None">
            <summary>Specifies that no inheritance flags are set.</summary>
        </member>
        <member name="F:System.Security.AccessControl.PropagationFlags.NoPropagateInherit">
            <summary>Specifies that the ACE is not propagated to child objects.</summary>
        </member>
        <member name="T:System.Security.AccessControl.QualifiedAce">
            <summary>Represents an Access Control Entry (ACE) that contains a qualifier. The qualifier, represented by
                an
                <see cref="T:System.Security.AccessControl.AceQualifier"/>
                object, specifies whether the ACE allows access, denies access, causes system audits, or causes system
                alarms. The
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                class is the abstract base class for the
                <see cref="T:System.Security.AccessControl.CommonAce"/>
                and
                <see cref="T:System.Security.AccessControl.ObjectAce"/>
                classes.
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.QualifiedAce.GetOpaque">
            <summary>Returns the opaque callback data associated with this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object.
            </summary>
            <returns>An array of byte values that represents the opaque callback data associated with this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.QualifiedAce.SetOpaque(System.Byte[])">
            <summary>Sets the opaque callback data associated with this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object.
            </summary>
            <param name="opaque">An array of byte values that represents the opaque callback data for this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object.
            </param>
        </member>
        <member name="P:System.Security.AccessControl.QualifiedAce.AceQualifier">
            <summary>Gets a value that specifies whether the ACE allows access, denies access, causes system audits, or
                causes system alarms.
            </summary>
            <returns>A value that specifies whether the ACE allows access, denies access, causes system audits, or
                causes system alarms.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.QualifiedAce.IsCallback">
            <summary>Specifies whether this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object contains callback data.
            </summary>
            <returns>
                <see langword="true"/>
                if this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object contains callback data; otherwise, false.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.QualifiedAce.OpaqueLength">
            <summary>Gets the length of the opaque callback data associated with this
                <see cref="T:System.Security.AccessControl.QualifiedAce"/>
                object. This property is valid only for callback Access Control Entries (ACEs).
            </summary>
            <returns>The length of the opaque callback data.</returns>
        </member>
        <member name="T:System.Security.AccessControl.RawAcl">
            <summary>Represents an Access Control List (ACL).</summary>
        </member>
        <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte,System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                class with the specified revision level.
            </summary>
            <param name="revision">The revision level of the new Access Control List (ACL).</param>
            <param name="capacity">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object can contain. This number is to be used only as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.RawAcl.#ctor(System.Byte[],System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                class from the specified binary form.
            </summary>
            <param name="binaryForm">An array of byte values that represent an Access Control List (ACL).</param>
            <param name="offset">The offset in the
                <paramref name="binaryForm"/>
                parameter at which to begin unmarshaling data.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)">
            <summary>Marshals the contents of the
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object into the specified byte array beginning at the specified offset.
            </summary>
            <param name="binaryForm">The byte array into which the contents of the
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                is marshaled.
            </param>
            <param name="offset">The offset at which to start marshaling.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.RawAcl.InsertAce(System.Int32,System.Security.AccessControl.GenericAce)">
            <summary>Inserts the specified Access Control Entry (ACE) at the specified index.</summary>
            <param name="index">The position at which to add the new ACE. Specify the value of the
                <see cref="P:System.Security.AccessControl.RawAcl.Count"/>
                property to insert an ACE at the end of the
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object.
            </param>
            <param name="ace">The ACE to insert.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/>
                is negative or too high to allow the entire
                <see cref="T:System.Security.AccessControl.GenericAcl"/>
                to be copied into<paramref name="array"/>.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.RawAcl.RemoveAce(System.Int32)">
            <summary>Removes the Access Control Entry (ACE) at the specified location.</summary>
            <param name="index">The zero-based index of the ACE to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">The value of the
                <paramref name="index"/>
                parameter is higher than the value of the
                <see cref="P:System.Security.AccessControl.RawAcl.Count"/>
                property minus one or is negative.
            </exception>
        </member>
        <member name="P:System.Security.AccessControl.RawAcl.BinaryLength">
            <summary>Gets the length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object. This length should be used before marshaling the ACL into a binary array with the
                <see cref="M:System.Security.AccessControl.RawAcl.GetBinaryForm(System.Byte[],System.Int32)"/>
                method.
            </summary>
            <returns>The length, in bytes, of the binary representation of the current
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawAcl.Count">
            <summary>Gets the number of access control entries (ACEs) in the current
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object.
            </summary>
            <returns>The number of ACEs in the current
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawAcl.Item(System.Int32)">
            <summary>Gets or sets the Access Control Entry (ACE) at the specified index.</summary>
            <param name="index">The zero-based index of the ACE to get or set.</param>
            <returns>The ACE at the specified index.</returns>
        </member>
        <member name="P:System.Security.AccessControl.RawAcl.Revision">
            <summary>Gets the revision level of the<see cref="T:System.Security.AccessControl.RawAcl"/>.
            </summary>
            <returns>A byte value that specifies the revision level of the<see
                    cref="T:System.Security.AccessControl.RawAcl"/>.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.RawSecurityDescriptor">
            <summary>Represents a security descriptor. A security descriptor includes an owner, a primary group, a
                Discretionary Access Control List (DACL), and a System Access Control List (SACL).
            </summary>
        </member>
        <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Byte[],System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                class from the specified array of byte values.
            </summary>
            <param name="binaryForm">The array of byte values from which to create the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
            <param name="offset">The offset in the
                <paramref name="binaryForm"/>
                array at which to begin copying.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.Security.AccessControl.ControlFlags,System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.RawAcl,System.Security.AccessControl.RawAcl)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                class with the specified values.
            </summary>
            <param name="flags">Flags that specify behavior of the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
            <param name="owner">The owner for the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
            <param name="group">The primary group for the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
            <param name="systemAcl">The System Access Control List (SACL) for the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
            <param name="discretionaryAcl">The Discretionary Access Control List (DACL) for the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.RawSecurityDescriptor.#ctor(System.String)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                class from the specified Security Descriptor Definition Language (SDDL) string.
            </summary>
            <param name="sddlForm">The SDDL string from which to create the new
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </param>
            <exception cref="T:System.ArgumentException">The SDDL form of a security descriptor object is invalid.
            </exception>
        </member>
        <member name="M:System.Security.AccessControl.RawSecurityDescriptor.SetFlags(System.Security.AccessControl.ControlFlags)">
            <summary>Sets the
                <see cref="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags"/>
                property of this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object to the specified value.
            </summary>
            <param name="flags">One or more values of the
                <see cref="T:System.Security.AccessControl.ControlFlags"/>
                enumeration combined with a logical OR operation.
            </param>
        </member>
        <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ControlFlags">
            <summary>Gets values that specify behavior of the
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </summary>
            <returns>One or more values of the
                <see cref="T:System.Security.AccessControl.ControlFlags"/>
                enumeration combined with a logical OR operation.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawSecurityDescriptor.DiscretionaryAcl">
            <summary>Gets or sets the Discretionary Access Control List (DACL) for this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object. The DACL contains access rules.
            </summary>
            <returns>The DACL for this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Group">
            <summary>Gets or sets the primary group for this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </summary>
            <returns>The primary group for this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawSecurityDescriptor.Owner">
            <summary>Gets or sets the owner of the object associated with this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </summary>
            <returns>The owner of the object associated with this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawSecurityDescriptor.ResourceManagerControl">
            <summary>Gets or sets a byte value that represents the resource manager control bits associated with this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </summary>
            <returns>A byte value that represents the resource manager control bits associated with this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="P:System.Security.AccessControl.RawSecurityDescriptor.SystemAcl">
            <summary>Gets or sets the System Access Control List (SACL) for this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object. The SACL contains audit rules.
            </summary>
            <returns>The SACL for this
                <see cref="T:System.Security.AccessControl.RawSecurityDescriptor"/>
                object.
            </returns>
        </member>
        <member name="T:System.Security.AccessControl.ResourceType">
            <summary>Specifies the defined native object types.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.DSObject">
            <summary>A directory service (DS) object or a property set or property of a directory service object.
            </summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.DSObjectAll">
            <summary>A directory service object and all of its property sets and properties.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.FileObject">
            <summary>A file or directory.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.KernelObject">
            <summary>A local kernel object.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.LMShare">
            <summary>A network share.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.Printer">
            <summary>A printer.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.ProviderDefined">
            <summary>An object defined by a provider.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.RegistryKey">
            <summary>A registry key.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.RegistryWow6432Key">
            <summary>An object for a registry entry under WOW64.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.Service">
            <summary>A Windows service.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.Unknown">
            <summary>An unknown object type.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.WindowObject">
            <summary>A window station or desktop object on the local computer.</summary>
        </member>
        <member name="F:System.Security.AccessControl.ResourceType.WmiGuidObject">
            <summary>A Windows Management Instrumentation (WMI) object.</summary>
        </member>
        <member name="T:System.Security.AccessControl.SecurityInfos">
            <summary>Specifies the section of a security descriptor to be queried or set.</summary>
        </member>
        <member name="F:System.Security.AccessControl.SecurityInfos.DiscretionaryAcl">
            <summary>Specifies the discretionary access control list (DACL).</summary>
        </member>
        <member name="F:System.Security.AccessControl.SecurityInfos.Group">
            <summary>Specifies the primary group identifier.</summary>
        </member>
        <member name="F:System.Security.AccessControl.SecurityInfos.Owner">
            <summary>Specifies the owner identifier.</summary>
        </member>
        <member name="F:System.Security.AccessControl.SecurityInfos.SystemAcl">
            <summary>Specifies the system access control list (SACL).</summary>
        </member>
        <member name="T:System.Security.AccessControl.SystemAcl">
            <summary>Represents a System Access Control List (SACL).</summary>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Byte,System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                class with the specified values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object is a container.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object is a directory object Access Control List (ACL).
            </param>
            <param name="revision">The revision level of the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object.
            </param>
            <param name="capacity">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object can contain. This number is to be used only as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Int32)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                class with the specified values.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object is a container.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object is a directory object Access Control List (ACL).
            </param>
            <param name="capacity">The number of Access Control Entries (ACEs) this
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object can contain. This number is to be used only as a hint.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.#ctor(System.Boolean,System.Boolean,System.Security.AccessControl.RawAcl)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                class with the specified values from the specified
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object.
            </summary>
            <param name="isContainer">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object is a container.
            </param>
            <param name="isDS">
                <see langword="true"/>
                if the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object is a directory object Access Control List (ACL).
            </param>
            <param name="rawAcl">The underlying
                <see cref="T:System.Security.AccessControl.RawAcl"/>
                object for the new
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object. Specify
                <see langword="null"/>
                to create an empty ACL.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Adds an audit rule to the current
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object.
            </summary>
            <param name="auditFlags">The type of audit rule to add.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to add an audit rule.
            </param>
            <param name="accessMask">The access mask for the new audit rule.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new audit rule.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new audit
                rule.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Adds an audit rule with the specified settings to the current
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type for the new audit rule.
            </summary>
            <param name="auditFlags">The type of audit rule to add.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to add an audit rule.
            </param>
            <param name="accessMask">The access mask for the new audit rule.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new audit rule.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new audit
                rule.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the new audit rule applies.</param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the new audit
                rule.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.AddAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
            <summary>Adds an audit rule to the current
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object.
            </summary>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to add an audit rule.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                for the new audit rule.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Removes the specified audit rule from the current
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object.
            </summary>
            <param name="auditFlags">The type of audit rule to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an audit rule.
            </param>
            <param name="accessMask">The access mask for the rule to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the rule to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the rule to be
                removed.
            </param>
            <returns>
                <see langword="true"/>
                if this method successfully removes the specified audit rule; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Removes the specified audit rule from the current
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type.
            </summary>
            <param name="auditFlags">The type of audit rule to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an audit rule.
            </param>
            <param name="accessMask">The access mask for the rule to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the rule to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the rule to be
                removed.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the removed audit control rule
                applies.
            </param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the removed
                audit rule.
            </param>
            <returns>
                <see langword="true"/>
                if this method successfully removes the specified audit rule; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.RemoveAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
            <summary>Removes the specified audit rule from the current
                <see cref="T:System.Security.AccessControl.SystemAcl"/>
                object.
            </summary>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an audit rule.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                for which to remove an audit rule.
            </param>
            <returns>
                <see langword="true"/>
                if this method successfully removes the specified audit rule; otherwise,<see langword="false"/>.
            </returns>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Removes the specified audit rule from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="auditFlags">The type of audit rule to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an audit rule.
            </param>
            <param name="accessMask">The access mask for the rule to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the rule to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the rule to be
                removed.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Removes the specified audit rule from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type.
            </summary>
            <param name="auditFlags">The type of audit rule to remove.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an audit rule.
            </param>
            <param name="accessMask">The access mask for the rule to be removed.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the rule to be removed.
            </param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the rule to be
                removed.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the removed audit control rule
                applies.
            </param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the removed
                audit rule.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.RemoveAuditSpecific(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
            <summary>Removes the specified audit rule from the current
                <see cref="T:System.Security.AccessControl.DiscretionaryAcl"/>
                object.
            </summary>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to remove an audit rule.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                for the rule to be removed.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags)">
            <summary>Sets the specified audit rule for the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <param name="auditFlags">The audit condition to set.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to set an audit rule.
            </param>
            <param name="accessMask">The access mask for the new audit rule.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new audit rule.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new audit
                rule.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.AccessControl.AuditFlags,System.Security.Principal.SecurityIdentifier,System.Int32,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.ObjectAceFlags,System.Guid,System.Guid)">
            <summary>Sets the specified audit rule for the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object. Use this method for directory object Access Control Lists (ACLs) when specifying the object type
                or the inherited object type.
            </summary>
            <param name="auditFlags">The audit condition to set.</param>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to set an audit rule.
            </param>
            <param name="accessMask">The access mask for the new audit rule.</param>
            <param name="inheritanceFlags">Flags that specify the inheritance properties of the new audit rule.</param>
            <param name="propagationFlags">Flags that specify the inheritance propagation properties for the new audit
                rule.
            </param>
            <param name="objectFlags">Flags that specify if the
                <paramref name="objectType"/>
                and
                <paramref name="inheritedObjectType"/>
                parameters contain non-
                <see langword="null"/>
                values.
            </param>
            <param name="objectType">The identity of the class of objects to which the new audit rule applies.</param>
            <param name="inheritedObjectType">The identity of the class of child objects which can inherit the new audit
                rule.
            </param>
        </member>
        <member name="M:System.Security.AccessControl.SystemAcl.SetAudit(System.Security.Principal.SecurityIdentifier,System.Security.AccessControl.ObjectAuditRule)">
            <summary>Sets the specified audit rule for the specified
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                object.
            </summary>
            <param name="sid">The
                <see cref="T:System.Security.Principal.SecurityIdentifier"/>
                for which to set an audit rule.
            </param>
            <param name="rule">The
                <see cref="T:System.Security.AccessControl.ObjectAuditRule"/>
                for which to set an audit rule.
            </param>
        </member>
        <member name="T:System.Security.Policy.Evidence">
            <summary>Defines the set of information that constitutes input to security policy decisions. This class
                cannot be inherited.
            </summary>
        </member>
        <member name="M:System.Security.Policy.Evidence.#ctor">
            <summary>Initializes a new empty instance of the
                <see cref="T:System.Security.Policy.Evidence"/>
                class.
            </summary>
        </member>
        <member name="M:System.Security.Policy.Evidence.#ctor(System.Object[],System.Object[])">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.Policy.Evidence"/>
                class from multiple sets of host and assembly evidence.
            </summary>
            <param name="hostEvidence">The host evidence from which to create the new instance.</param>
            <param name="assemblyEvidence">The assembly evidence from which to create the new instance.</param>
        </member>
        <member name="M:System.Security.Policy.Evidence.#ctor(System.Security.Policy.Evidence)">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.Policy.Evidence"/>
                class from a shallow copy of an existing one.
            </summary>
            <param name="evidence">The
                <see cref="T:System.Security.Policy.Evidence"/>
                instance from which to create the new instance. This instance is not deep-copied.
            </param>
            <exception cref="T:System.ArgumentException">The
                <paramref name="evidence"/>
                parameter is not a valid instance of<see cref="T:System.Security.Policy.Evidence"/>.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.#ctor(System.Security.Policy.EvidenceBase[],System.Security.Policy.EvidenceBase[])">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.Policy.Evidence"/>
                class from multiple sets of host and assembly evidence.
            </summary>
            <param name="hostEvidence">The host evidence from which to create the new instance.</param>
            <param name="assemblyEvidence">The assembly evidence from which to create the new instance.</param>
        </member>
        <member name="M:System.Security.Policy.Evidence.AddAssembly(System.Object)">
            <summary>Adds the specified assembly evidence to the evidence set.</summary>
            <param name="id">Any evidence object.</param>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="id"/>
                is null.
            </exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="id"/>
                is not serializable.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.AddAssemblyEvidence``1(``0)">
            <summary>Adds an evidence object of the specified type to the assembly-supplied evidence list.</summary>
            <param name="evidence">The assembly evidence to add.</param>
            <typeparam name="T">The type of the object in<paramref name="evidence"/>.
            </typeparam>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="evidence"/>
                is<see langword="null"/>.
            </exception>
            <exception cref="T:System.InvalidOperationException">Evidence of type
                <paramref name="T"/>
                is already in the list.
            </exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="evidence"/>
                is not serializable.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.AddHost(System.Object)">
            <summary>Adds the specified evidence supplied by the host to the evidence set.</summary>
            <param name="id">Any evidence object.</param>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="id"/>
                is null.
            </exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="id"/>
                is not serializable.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.AddHostEvidence``1(``0)">
            <summary>Adds host evidence of the specified type to the host evidence collection.</summary>
            <param name="evidence">The host evidence to add.</param>
            <typeparam name="T">The type of the object in<paramref name="evidence"/>.
            </typeparam>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="evidence"/>
                is<see langword="null"/>.
            </exception>
            <exception cref="T:System.InvalidOperationException">Evidence of type
                <paramref name="T"/>
                is already in the list.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.Clear">
            <summary>Removes the host and assembly evidence from the evidence set.</summary>
        </member>
        <member name="M:System.Security.Policy.Evidence.Clone">
            <summary>Returns a duplicate copy of this evidence object.</summary>
            <returns>A duplicate copy of this evidence object.</returns>
        </member>
        <member name="M:System.Security.Policy.Evidence.CopyTo(System.Array,System.Int32)">
            <summary>Copies evidence objects to an<see cref="T:System.Array"/>.
            </summary>
            <param name="array">The target array to which to copy evidence objects.</param>
            <param name="index">The zero-based position in the array to which to begin copying evidence objects.</param>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="array"/>
                is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="index"/>
                is outside the range of the target array.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.GetAssemblyEnumerator">
            <summary>Enumerates evidence provided by the assembly.</summary>
            <returns>An enumerator for evidence added by the
                <see cref="M:System.Security.Policy.Evidence.AddAssembly(System.Object)"/>
                method.
            </returns>
        </member>
        <member name="M:System.Security.Policy.Evidence.GetAssemblyEvidence``1">
            <summary>Gets assembly evidence of the specified type from the collection.</summary>
            <typeparam name="T">The type of the evidence to get.</typeparam>
            <returns>Evidence of type
                <paramref name="T"/>
                in the assembly evidence collection.
            </returns>
        </member>
        <member name="M:System.Security.Policy.Evidence.GetEnumerator">
            <summary>Enumerates all evidence in the set, both that provided by the host and that provided by the
                assembly.
            </summary>
            <returns>An enumerator for evidence added by both the
                <see cref="M:System.Security.Policy.Evidence.AddHost(System.Object)"/>
                method and the
                <see cref="M:System.Security.Policy.Evidence.AddAssembly(System.Object)"/>
                method.
            </returns>
        </member>
        <member name="M:System.Security.Policy.Evidence.GetHostEnumerator">
            <summary>Enumerates evidence supplied by the host.</summary>
            <returns>An enumerator for evidence added by the
                <see cref="M:System.Security.Policy.Evidence.AddHost(System.Object)"/>
                method.
            </returns>
        </member>
        <member name="M:System.Security.Policy.Evidence.GetHostEvidence``1">
            <summary>Gets host evidence of the specified type from the collection.</summary>
            <typeparam name="T">The type of the evidence to get.</typeparam>
            <returns>Evidence of type
                <paramref name="T"/>
                in the host evidence collection.
            </returns>
        </member>
        <member name="M:System.Security.Policy.Evidence.Merge(System.Security.Policy.Evidence)">
            <summary>Merges the specified evidence set into the current evidence set.</summary>
            <param name="evidence">The evidence set to be merged into the current evidence set.</param>
            <exception cref="T:System.ArgumentException">The
                <paramref name="evidence"/>
                parameter is not a valid instance of<see cref="T:System.Security.Policy.Evidence"/>.
            </exception>
            <exception cref="T:System.Security.SecurityException">
                <see cref="P:System.Security.Policy.Evidence.Locked"/>
                is<see langword="true"/>, the code that calls this method does not have<see
                    cref="F:System.Security.Permissions.SecurityPermissionFlag.ControlEvidence"/>, and the
                <paramref name="evidence"/>
                parameter has a host list that is not empty.
            </exception>
        </member>
        <member name="M:System.Security.Policy.Evidence.RemoveType(System.Type)">
            <summary>Removes the evidence for a given type from the host and assembly enumerations.</summary>
            <param name="t">The type of the evidence to be removed.</param>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="t"/>
                is null.
            </exception>
        </member>
        <member name="P:System.Security.Policy.Evidence.Count">
            <summary>Gets the number of evidence objects in the evidence set.</summary>
            <returns>The number of evidence objects in the evidence set.</returns>
        </member>
        <member name="P:System.Security.Policy.Evidence.IsReadOnly">
            <summary>Gets a value indicating whether the evidence set is read-only.</summary>
            <returns>Always<see langword="false"/>, because read-only evidence sets are not supported.
            </returns>
        </member>
        <member name="P:System.Security.Policy.Evidence.IsSynchronized">
            <summary>Gets a value indicating whether the evidence set is thread-safe.</summary>
            <returns>Always
                <see langword="false"/>
                because thread-safe evidence sets are not supported.
            </returns>
        </member>
        <member name="P:System.Security.Policy.Evidence.Locked">
            <summary>Gets or sets a value indicating whether the evidence is locked.</summary>
            <returns>
                <see langword="true"/>
                if the evidence is locked; otherwise,<see langword="false"/>. The default is<see langword="false"/>.
            </returns>
        </member>
        <member name="P:System.Security.Policy.Evidence.SyncRoot">
            <summary>Gets the synchronization root.</summary>
            <returns>Always
                <see langword="this"/>
                (
                <see langword="Me"/>
                in Visual Basic), because synchronization of evidence sets is not supported.
            </returns>
        </member>
        <member name="T:System.Security.Policy.EvidenceBase">
            <summary>Provides a base class from which all objects to be used as evidence must derive.</summary>
        </member>
        <member name="M:System.Security.Policy.EvidenceBase.#ctor">
            <summary>Initializes a new instance of the
                <see cref="T:System.Security.Policy.EvidenceBase"/>
                class.
            </summary>
            <exception cref="T:System.InvalidOperationException">An object to be used as evidence is not serializable.
            </exception>
        </member>
        <member name="M:System.Security.Policy.EvidenceBase.Clone">
            <summary>Creates a new object that is a complete copy of the current instance.</summary>
            <returns>A duplicate copy of this evidence object.</returns>
        </member>
    </members>
</doc>