<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Sockets</name>
  </assembly>
  <members>
    <member name="T:System.Net.Sockets.ProtocolType">
      <summary>Задает протокол, поддерживающий класс <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Tcp">
      <summary>Протокол TCP.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Udp">
      <summary>Протокол UDP.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unknown">
      <summary>Неизвестный протокол.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unspecified">
      <summary>Неуказанный протокол.</summary>
    </member>
    <member name="T:System.Net.Sockets.Socket">
      <summary>Реализует интерфейс сокетов Berkeley.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Sockets.Socket" />, используя заданные семейство адресов, тип сокета и протокол.</summary>
      <param name="addressFamily">Одно из значений <see cref="T:System.Net.Sockets.AddressFamily" />. </param>
      <param name="socketType">Одно из значений <see cref="T:System.Net.Sockets.SocketType" />. </param>
      <param name="protocolType">Одно из значений <see cref="T:System.Net.Sockets.ProtocolType" />. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Сочетание параметров <paramref name="addressFamily" />, <paramref name="socketType" /> и <paramref name="protocolType" /> приводит к неработоспособному сокету. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Sockets.Socket" />, используя указанный тип сокетов и протокол.</summary>
      <param name="socketType">Одно из значений <see cref="T:System.Net.Sockets.SocketType" />.</param>
      <param name="protocolType">Одно из значений <see cref="T:System.Net.Sockets.ProtocolType" />.</param>
      <exception cref="T:System.Net.Sockets.SocketException">Сочетание параметров <paramref name="socketType" /> и <paramref name="protocolType" /> приводит к недопустимому сокету. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Начинает асинхронную операцию, чтобы принять попытку входящего подключения.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.Событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не произойдет и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentException">Аргумент является недопустимым.Это исключение возникает, если обеспечиваемый буфер имеет недостаточный размер.Буфер должен иметь размер, равный, по крайней мере, 2 * (размер(SOCKADDR_STORAGE + 16) байт.Это исключение также возникает, если задано несколько буферов, свойство <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> не имеет значение "null".</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Аргумент вне диапазона.Исключение возникает, если объект <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Count" /> имеет значение меньше 0.</exception>
      <exception cref="T:System.InvalidOperationException">Предпринят запрос выполнения недопустимой операции.Это исключение возникает, если принимающий объект <see cref="T:System.Net.Sockets.Socket" /> не производит прослушивание подключений или принимающий сокет является связанным.Требуется вызвать объект <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> и метод <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> перед вызовом метода <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />.Это исключение также происходит, если сокет уже подключен или работа с сокетом уже выполнялась с использованием указанного параметра <paramref name="e" />. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.AddressFamily">
      <summary>Получает семейство адресов объекта <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Одно из значений <see cref="T:System.Net.Sockets.AddressFamily" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)">
      <summary>Связывает объект <see cref="T:System.Net.Sockets.Socket" /> с локальной конечной точкой.</summary>
      <param name="localEP">Локальный объект <see cref="T:System.Net.EndPoint" />, который необходимо связать с объектом <see cref="T:System.Net.Sockets.Socket" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="localEP" /> имеет значение null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего оператора, находящегося в начале стека вызовов, нет разрешения для запрашиваемой операции. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.SocketPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.CancelConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Отменяет выполнение асинхронного запроса для подключения к удаленному узлу.</summary>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, используемый для запроса соединения с удаленным узлом путем вызова одного из методов <see cref="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)" />.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="e" /> и <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не могут иметь значение NULL.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету. </exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего оператора, находящегося в начале стека вызовов, нет разрешения для запрашиваемой операции.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Начинает выполнение асинхронного запроса для подключения к удаленному узлу.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.В данном случае событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не будет создано и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentException">Аргумент является недопустимым.Это исключение возникает, если задано несколько буферов, свойство <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> не имеет значение "null".</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="e" /> и <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не могут иметь значение NULL.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.Socket" /> ведет прослушивание или работа с сокетом уже выполняется с использованием объекта <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, указанного параметром <paramref name="e" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.Это исключение возникает также в том случае, если локальная конечная точка и объект <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не принадлежат к одному семейству адресов.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего оператора, находящегося в начале стека вызовов, нет разрешения для запрашиваемой операции.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Начинает выполнение асинхронного запроса для подключения к удаленному узлу.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.В данном случае событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не будет создано и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="socketType">Одно из значений <see cref="T:System.Net.Sockets.SocketType" />.</param>
      <param name="protocolType">Одно из значений <see cref="T:System.Net.Sockets.ProtocolType" />.</param>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentException">Аргумент является недопустимым.Это исключение возникает, если задано несколько буферов, свойство <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> не имеет значение "null".</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="e" /> и <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не могут иметь значение NULL.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.Socket" /> ведет прослушивание или работа с сокетом уже выполняется с использованием объекта <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, указанного параметром <paramref name="e" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.Это исключение возникает также в том случае, если локальная конечная точка и объект <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не принадлежат к одному семейству адресов.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего оператора, находящегося в начале стека вызовов, нет разрешения для запрашиваемой операции.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Connected">
      <summary>Получает значение, указывающее, подключается ли объект <see cref="T:System.Net.Sockets.Socket" /> к удаленному узлу в результате последней операции <see cref="Overload:System.Net.Sockets.Socket.Send" /> или <see cref="Overload:System.Net.Sockets.Socket.Receive" />.</summary>
      <returns>Значение true, если объект <see cref="T:System.Net.Sockets.Socket" /> в результате последней операции был подключен к удаленному ресурсу; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose">
      <summary>Освобождает все ресурсы, используемые текущим экземпляром класса <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Sockets.Socket" />, и по возможности — управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов. </param>
    </member>
    <member name="M:System.Net.Sockets.Socket.Finalize">
      <summary>Освобождает ресурсы, используемые классом <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Listen(System.Int32)">
      <summary>Устанавливает объект <see cref="T:System.Net.Sockets.Socket" /> в состояние прослушивания.</summary>
      <param name="backlog">Максимальная длина очереди ожидающих подключений. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.LocalEndPoint">
      <summary>Возвращает локальную конечную точку.</summary>
      <returns>Объект <see cref="T:System.Net.EndPoint" />, который объект <see cref="T:System.Net.Sockets.Socket" /> использует для взаимодействий.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.NoDelay">
      <summary>Возвращает или задает значение <see cref="T:System.Boolean" />, указывающее, используется ли поток <see cref="T:System.Net.Sockets.Socket" /> в алгоритме Nagle.</summary>
      <returns>Значение false, если объект <see cref="T:System.Net.Sockets.Socket" /> использует алгоритм Nagle; в противном случае — значение true.Значение по умолчанию — false.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к объекту <see cref="T:System.Net.Sockets.Socket" />.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv4">
      <summary>Указывает, поддерживают ли основная операционная система и сетевые адаптеры протокол IPv4.</summary>
      <returns>Значение true, если основная операционная система и сетевые адаптеры поддерживают протокол IPv4; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv6">
      <summary>Указывает, поддерживают ли основная операционная система и сетевые адаптеры протокол IPv6.</summary>
      <returns>Значение true, если основная операционная система и сетевые адаптеры поддерживают протокол IPv6; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.ProtocolType">
      <summary>Получает тип протокола объекта <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Одно из значений <see cref="T:System.Net.Sockets.ProtocolType" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Начинает выполнение асинхронного запроса, чтобы получить данные из подключенного объекта <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.В данном случае событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не будет создано и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentException">Аргумент был недопустимым.Свойства <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> или <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> на параметре <paramref name="e" /> должны ссылаться на допустимые буферы.Может быть установлено одно из этих свойств, но нельзя одновременно устанавливать оба свойства.</exception>
      <exception cref="T:System.InvalidOperationException">Операция сокета уже выполнялась с использованием объекта <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, указанного в параметре <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveBufferSize">
      <summary>Получает или задает значение, задающее размер приемного буфера объекта <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Объект <see cref="T:System.Int32" />, который содержит значение размера приемного буфера в байтах.Значение по умолчанию — 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение, указанное для операции установки, меньше 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Начинает выполнение асинхронного приема данных с указанного сетевого устройства.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.В данном случае событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не будет создано и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentNullException">Объект <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не может иметь значение "null".</exception>
      <exception cref="T:System.InvalidOperationException">Операция сокета уже выполнялась с использованием объекта <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, указанного в параметре <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.RemoteEndPoint">
      <summary>Возвращает удаленную конечную точку.</summary>
      <returns>Объект <see cref="T:System.Net.EndPoint" />, с которым взаимодействует объект <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Выполняет асинхронную передачу данных на подключенный объект <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.В данном случае событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не будет создано и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentException">Свойства <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> или <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> на параметре <paramref name="e" /> должны ссылаться на допустимые буферы.Может быть установлено одно из этих свойств, но нельзя одновременно устанавливать оба свойства.</exception>
      <exception cref="T:System.InvalidOperationException">Операция сокета уже выполнялась с использованием объекта <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, указанного в параметре <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Объект <see cref="T:System.Net.Sockets.Socket" /> уже не подключен или он был получен посредством метода <see cref="M:System.Net.Sockets.Socket.Accept" />, <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> или <see cref="Overload:System.Net.Sockets.Socket.BeginAccept" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendBufferSize">
      <summary>Получает или задает значение, определяющее размер буфера передачи объекта <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Объект <see cref="T:System.Int32" />, который содержит значение размера буфера передачи в байтах.Значение по умолчанию — 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение, указанное для операции установки, меньше 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Выполняет асинхронную передачу данных в указанный удаленный узел.</summary>
      <returns>Возвращает значение true, если операция ввода-вывода находится в состоянии ожидания.По завершении операции создается событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> в параметре <paramref name="e" />.Возвращает значение false, если операция ввода-вывода завершена синхронно.В данном случае событие <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> на параметре <paramref name="e" /> не будет создано и объект <paramref name="e" />, передаваемый как параметр, можно изучить сразу после получения результатов вызова метода для извлечения результатов операции.</returns>
      <param name="e">Объект <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> для использования в данной асинхронной операции сокета.</param>
      <exception cref="T:System.ArgumentNullException">Объект <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> не может иметь значение "null".</exception>
      <exception cref="T:System.InvalidOperationException">Операция сокета уже выполнялась с использованием объекта <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, указанного в параметре <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Этот метод доступен только в Windows XP и более поздних версиях.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Указанный протокол работает с установлением соединения, но объект <see cref="T:System.Net.Sockets.Socket" /> еще не подключен.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)">
      <summary>Блокирует передачу и получение данных для объекта <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="how">Одно из значений <see cref="T:System.Net.Sockets.SocketShutdown" />, указывающее на то, что операция более не разрешена. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Дополнительные сведения см. в разделе "Примечания".</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.Ttl">
      <summary>Получает или задает значение, задающее время существования (TTL) IP-пакетов, отправленных объектом <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Значение времени существования TTL.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">В качестве величины срока жизни нельзя задать отрицательное число.</exception>
      <exception cref="T:System.NotSupportedException">Это свойство может быть установлено только для сокетов в семействах <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> или <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Произошла ошибка при попытке доступа к сокету.Эта ошибка также возвращается при попытке задать срок жизни больше, чем 255.</exception>
      <exception cref="T:System.ObjectDisposedException">Объект <see cref="T:System.Net.Sockets.Socket" /> закрыт. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncEventArgs">
      <summary>Представляет асинхронную операцию сокета.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.#ctor">
      <summary>Создает пустой экземпляр класса <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />.</summary>
      <exception cref="T:System.NotSupportedException">Платформа не поддерживается. </exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.AcceptSocket">
      <summary>Возвращает или задает сокет для применения или сокет, созданный для принятия запроса на подключения, с помощью асинхронного метода сокета.</summary>
      <returns>Объект <see cref="T:System.Net.Sockets.Socket" /> для применения (сокет, созданный для принятия запроса на подключения с помощью асинхронного метода сокета).</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer">
      <summary>Получает буфер данных для применения в асинхронном методе сокета.</summary>
      <returns>Массив <see cref="T:System.Byte" />, представляющий буфер данных для применения в асинхронном методе сокета.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList">
      <summary>Возвращает или задает массив буферов данных для применения в асинхронном методе сокета.</summary>
      <returns>Объект <see cref="T:System.Collections.IList" />, представляющий массив буферов данных для применения в асинхронном методе сокета.</returns>
      <exception cref="T:System.ArgumentException">Неоднозначное указание буферов для заданной операции.Это исключение возникает, если для свойства <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> задано значение, отличное от NULL, и была предпринята попытка задать отличное от NULL значение для свойства <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BytesTransferred">
      <summary>Получает количество байтов, переданных в операции сокета.</summary>
      <returns>Объект <see cref="T:System.Int32" />, содержащий количество байтов, переданных в операции сокета.</returns>
    </member>
    <member name="E:System.Net.Sockets.SocketAsyncEventArgs.Completed">
      <summary>Событие, используемое для завершения асинхронной операции.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectByNameError">
      <summary>Получает исключение в случае сбоя соединения при использовании <see cref="T:System.Net.DnsEndPoint" />.</summary>
      <returns>Объект <see cref="T:System.Exception" />, указывающий причину ошибки соединения, если значение <see cref="T:System.Net.DnsEndPoint" /> было задано для свойства <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectSocket">
      <summary>Созданный и подключенный объект <see cref="T:System.Net.Sockets.Socket" /> после успешного выполнения метода <see cref="Overload:System.Net.Sockets.Socket.ConnectAsync" />.</summary>
      <returns>Подключенный объект <see cref="T:System.Net.Sockets.Socket" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Count">
      <summary>Получает значение, равное максимальному количеству данных (в байтах), которое может быть отправлено или получено в асинхронной операции.</summary>
      <returns>Объект <see cref="T:System.Int32" />, содержащий значение, равное максимальному количеству данных (в байтах), которое может быть отправлено или получено.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Dispose">
      <summary>Освобождает неуправляемые ресурсы, используемые экземпляром класса <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />, и при необходимости удаляет управляемые ресурсы.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Finalize">
      <summary>Освобождает ресурсы, используемые классом <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.LastOperation">
      <summary>Получает тип операции сокета, которая была выполнена последней с этим объектом контекста.</summary>
      <returns>Экземпляр класса <see cref="T:System.Net.Sockets.SocketAsyncOperation" />, указывающий тип операции сокета, которая была выполнена последней с этим объектом контекста.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Offset">
      <summary>Получает смещение (в байтах) в буфере данных, на который ссылается свойство <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.</summary>
      <returns>Объект <see cref="T:System.Int32" />, содержащий смещение (в байтах) в буфере данных, на который ссылается свойство <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.OnCompleted(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Представляет метод, вызываемый после завершения асинхронной операции.</summary>
      <param name="e">Сигнализирующее событие.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint">
      <summary>Возвращает или задает удаленную конечную точка IP для асинхронной операции.</summary>
      <returns>Объект <see cref="T:System.Net.EndPoint" />, представляющий удаленную конечную точка IP для асинхронной операции.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Задает буфер данных для применения в асинхронном методе сокета.</summary>
      <param name="buffer">Буфер данных для применения в асинхронном методе сокета.</param>
      <param name="offset">Смещение (в байтах) в буфере данных, при котором начинается операция.</param>
      <param name="count">Максимальное количество данных (в байтах), которое может быть отправлено или получено в буфере.</param>
      <exception cref="T:System.ArgumentException">Неоднозначное указание буферов.Это исключение возникает, если значения свойств <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> и <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> одновременно отличны от null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Аргумент вне диапазона.Это исключение возникает, если значение параметра <paramref name="offset" /> меньше нуля или больше длины массива, указанной в свойстве <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.Это исключение возникает также, если значение параметра <paramref name="count" /> меньше нуля или больше разницы между длиной массива, указанной в свойстве <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />, и значением параметра <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Int32,System.Int32)">
      <summary>Задает буфер данных для применения в асинхронном методе сокета.</summary>
      <param name="offset">Смещение (в байтах) в буфере данных, при котором начинается операция.</param>
      <param name="count">Максимальное количество данных (в байтах), которое может быть отправлено или получено в буфере.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Аргумент вне диапазона.Это исключение возникает, если значение параметра <paramref name="offset" /> меньше нуля или больше длины массива, указанной в свойстве <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.Это исключение возникает также, если значение параметра <paramref name="count" /> меньше нуля или больше разницы между длиной массива, указанной в свойстве <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />, и значением параметра <paramref name="offset" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketError">
      <summary>Возвращает или задает результат асинхронной операции сокета.</summary>
      <returns>Объект <see cref="T:System.Net.Sockets.SocketError" />, представляющий результат асинхронной операции сокета. </returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.UserToken">
      <summary>Возвращает или задает объект пользователя или приложения, связанный с данной асинхронной операцией сокета.</summary>
      <returns>Объект, который представляет объект пользователя или приложения, связанный с данной асинхронной операцией сокета.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncOperation">
      <summary>Тип асинхронной операции сокета, которая была выполнена последней с этим объектом контекста.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Accept">
      <summary>Операция Accept сокета. </summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Connect">
      <summary>Операция Connect сокета.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.None">
      <summary>Ни одна из операций сокета.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Receive">
      <summary>Операция Receive сокета.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveFrom">
      <summary>Операция ReceiveFrom сокета.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Send">
      <summary>Операция Send сокета.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendTo">
      <summary>Операция SendTo сокета.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketShutdown">
      <summary>Определяет константы, используемые методом <see cref="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Both">
      <summary>Отключает объект <see cref="T:System.Net.Sockets.Socket" /> как от приема, так и от передачи.Это поле является константой.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Receive">
      <summary>Отключает объект <see cref="T:System.Net.Sockets.Socket" /> от приема.Это поле является константой.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Send">
      <summary>Отключает объект <see cref="T:System.Net.Sockets.Socket" /> от передачи.Это поле является константой.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketType">
      <summary>Указывает тип сокета, являющегося экземпляром класса <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Dgram">
      <summary>Поддерживает датаграммы — ненадежные сообщения с фиксированной (обычно малой) максимальной длиной, передаваемые без установления подключения.Возможны потеря и дублирование сообщений, а также их получение не в том порядке, в котором они отправлены.Объект <see cref="T:System.Net.Sockets.Socket" /> типа <see cref="F:System.Net.Sockets.SocketType.Dgram" /> не требует установки подключения до приема и передачи данных и может обеспечивать связь со множеством одноранговых узлов.<see cref="F:System.Net.Sockets.SocketType.Dgram" /> использует протокол Datagram (<see cref="F:System.Net.Sockets.ProtocolType.Udp" />) и <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /><see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Stream">
      <summary>Поддерживает надежные двусторонние байтовые потоки в режиме с установлением подключения, без дублирования данных и без сохранения границ данных.Объект Socket этого типа взаимодействует с одним узлом и требует установления подключения к удаленному узлу перед началом передачи данных.<see cref="F:System.Net.Sockets.SocketType.Stream" /> использует протокол TCP (<see cref="F:System.Net.Sockets.ProtocolType.Tcp" />) <see cref="T:System.Net.Sockets.ProtocolType" /> и InterNetwork<see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Unknown">
      <summary>Задает неизвестный тип Socket.</summary>
    </member>
  </members>
</doc>