<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Management</name>
    </assembly>
    <members>
        <member name="T:System.Management.TextFormat">
            <summary>
            <para>Describes the possible text formats that can be used with <see cref='M:System.Management.ManagementBaseObject.GetText(System.Management.TextFormat)'/>.</para>
            </summary>
        </member>
        <member name="F:System.Management.TextFormat.Mof">
            <summary>
            Managed Object Format
            </summary>
        </member>
        <member name="F:System.Management.TextFormat.CimDtd20">
            <summary>
            XML DTD that corresponds to CIM DTD version 2.0
            </summary>
        </member>
        <member name="F:System.Management.TextFormat.WmiDtd20">
            <summary>
            XML WMI DTD that corresponds to CIM DTD version 2.0.
            Using this value enables a few WMI-specific extensions, like embedded objects.
            </summary>
        </member>
        <member name="T:System.Management.CimType">
            <summary>
               <para>Describes the possible CIM types for properties, qualifiers, or method parameters.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.None">
            <summary>
               <para>Invalid Type</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.SInt8">
            <summary>
               <para>A signed 8-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.UInt8">
            <summary>
               <para>An unsigned 8-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.SInt16">
            <summary>
               <para>A signed 16-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.UInt16">
            <summary>
               <para>An unsigned 16-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.SInt32">
            <summary>
               <para>A signed 32-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.UInt32">
            <summary>
               <para>An unsigned 32-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.SInt64">
            <summary>
               <para>A signed 64-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.UInt64">
            <summary>
               <para>An unsigned 64-bit integer.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.Real32">
            <summary>
               <para>A floating-point 32-bit number.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.Real64">
            <summary>
               <para>A floating point 64-bit number.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.Boolean">
            <summary>
               <para> A boolean.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.String">
            <summary>
               <para>A string.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.DateTime">
            <summary>
               <para> A date or time value, represented in a string in DMTF
                  date/time format: yyyymmddHHMMSS.mmmmmmsUUU</para>
               <para>where:</para>
               <para>yyyymmdd - is the date in year/month/day</para>
               <para>HHMMSS - is the time in hours/minutes/seconds</para>
               <para>mmmmmm - is the number of microseconds in 6 digits</para>
               <para>sUUU - is a sign (+ or -) and a 3-digit UTC offset</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.Reference">
            <summary>
               <para>A reference to another object. This is represented by a
                  string containing the path to the referenced object</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.Char16">
            <summary>
               <para> A 16-bit character.</para>
            </summary>
        </member>
        <member name="F:System.Management.CimType.Object">
            <summary>
               <para>An embedded object.</para>
               <para>Note that embedded objects differ from references in that the embedded object
                  doesn't have a path and its lifetime is identical to the lifetime of the
                  containing object.</para>
            </summary>
        </member>
        <member name="T:System.Management.ComparisonSettings">
            <summary>
            <para>Describes the object comparison modes that can be used with <see cref='M:System.Management.ManagementBaseObject.CompareTo(System.Management.ManagementBaseObject,System.Management.ComparisonSettings)'/>.
               Note that these values may be combined.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IncludeAll">
            <summary>
               <para>A mode that compares all elements of the compared objects.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IgnoreQualifiers">
            <summary>
               <para>A mode that compares the objects, ignoring qualifiers.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IgnoreObjectSource">
            <summary>
               <para> A mode that ignores the source of the objects, namely the server
                  and the namespace they came from, in comparison to other objects.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IgnoreDefaultValues">
            <summary>
               <para> A mode that ignores the default values of properties.
                  This value is only meaningful when comparing classes.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IgnoreClass">
            <summary>
               <para>A mode that assumes that the objects being compared are instances of
                  the same class. Consequently, this value causes comparison
                  of instance-related information only. Use this flag to optimize
                  performance. If the objects are not of the same class, the results are undefined.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IgnoreCase">
            <summary>
               <para> A mode that compares string values in a case-insensitive
                  manner. This applies to strings and to qualifier values. Property and qualifier
                  names are always compared in a case-insensitive manner whether this flag is
                  specified or not.</para>
            </summary>
        </member>
        <member name="F:System.Management.ComparisonSettings.IgnoreFlavor">
            <summary>
               <para>A mode that ignores qualifier flavors. This flag still takes
                  qualifier values into account, but ignores flavor distinctions such as
                  propagation rules and override restrictions.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementBaseObject">
            <summary>
               <para> Contains the basic elements of a management
                  object. It serves as a base class to more specific management object classes.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementBaseObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementBaseObject'/> class that is serializable.</para>
            </summary>
            <param name='info'>The <see cref='T:System.Runtime.Serialization.SerializationInfo'/> to populate with data.</param>
            <param name='context'>The destination (see <see cref='T:System.Runtime.Serialization.StreamingContext'/> ) for this serialization.</param>
        </member>
        <member name="M:System.Management.ManagementBaseObject.op_Explicit(System.Management.ManagementBaseObject)~System.IntPtr">
            <summary>
               <para>Provides the internal WMI object represented by a ManagementObject.</para>
               <para>See remarks with regard to usage.</para>
            </summary>
            <param name='managementObject'>The <see cref='T:System.Management.ManagementBaseObject'/> that references the requested WMI object. </param>
            <returns>
            <para>An <see cref='T:System.IntPtr'/> representing the internal WMI object.</para>
            </returns>
            <remarks>
               <para>This operator is used internally by instrumentation code. It is not intended
                  for direct use by regular client or instrumented applications.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementBaseObject.GetBaseObject(System.Management.IWbemClassObjectFreeThreaded,System.Management.ManagementScope)">
            <summary>
            Factory for various types of base object
            </summary>
            <param name="wbemObject"> IWbemClassObject </param>
            <param name="scope"> The scope</param>
        </member>
        <member name="M:System.Management.ManagementBaseObject.Clone">
            <summary>
               <para>Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The new cloned object.</para>
            </returns>
        </member>
        <member name="P:System.Management.ManagementBaseObject.Properties">
            <summary>
            <para>Gets or sets a collection of <see cref='T:System.Management.PropertyData'/> objects describing the properties of the
               management object.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.PropertyDataCollection'/> that represents the
               properties of the management object.</para>
            </value>
            <seealso cref='T:System.Management.PropertyData'/>
        </member>
        <member name="P:System.Management.ManagementBaseObject.SystemProperties">
            <summary>
               <para>Gets or sets the collection of WMI system properties of the management object (for example, the
                  class name, server, and namespace). WMI system property names begin with
                  "__".</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.PropertyDataCollection'/> that represents the system properties of the management object.</para>
            </value>
            <seealso cref='T:System.Management.PropertyData'/>
        </member>
        <member name="P:System.Management.ManagementBaseObject.Qualifiers">
            <summary>
            <para>Gets or sets the collection of <see cref='T:System.Management.QualifierData'/> objects defined on the management object.
               Each element in the collection holds information such as the qualifier name,
               value, and flavor.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.QualifierDataCollection'/> that represents the qualifiers
               defined on the management object.</para>
            </value>
            <seealso cref='T:System.Management.QualifierData'/>
        </member>
        <member name="P:System.Management.ManagementBaseObject.ClassPath">
            <summary>
               <para>Gets or sets the path to the management object's class.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementPath'/> that represents the path to the management object's class.</para>
            </value>
            <example>
               <para>For example, for the \\MyBox\root\cimv2:Win32_LogicalDisk=
                  'C:' object, the class path is \\MyBox\root\cimv2:Win32_LogicalDisk
                  .</para>
            </example>
        </member>
        <member name="P:System.Management.ManagementBaseObject.Item(System.String)">
            <summary>
               <para> Gets access to property values through [] notation.</para>
            </summary>
            <param name='propertyName'>The name of the property of interest. </param>
            <value>
               An <see cref='T:System.Object'/> containing the
               value of the requested property.
            </value>
        </member>
        <member name="M:System.Management.ManagementBaseObject.GetPropertyValue(System.String)">
            <summary>
               <para>Gets an equivalent accessor to a property's value.</para>
            </summary>
            <param name='propertyName'>The name of the property of interest. </param>
            <returns>
               <para>The value of the specified property.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.GetQualifierValue(System.String)">
            <summary>
               <para>Gets the value of the specified qualifier.</para>
            </summary>
            <param name='qualifierName'>The name of the qualifier of interest. </param>
            <returns>
               <para>The value of the specified qualifier.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.SetQualifierValue(System.String,System.Object)">
            <summary>
               <para>Sets the value of the named qualifier.</para>
            </summary>
            <param name='qualifierName'>The name of the qualifier to set. This parameter cannot be null.</param>
            <param name='qualifierValue'>The value to set.</param>
        </member>
        <member name="M:System.Management.ManagementBaseObject.GetPropertyQualifierValue(System.String,System.String)">
            <summary>
               <para>Returns the value of the specified property qualifier.</para>
            </summary>
            <param name='propertyName'>The name of the property to which the qualifier belongs. </param>
            <param name='qualifierName'>The name of the property qualifier of interest. </param>
            <returns>
               <para>The value of the specified qualifier.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.SetPropertyQualifierValue(System.String,System.String,System.Object)">
            <summary>
               <para>Sets the value of the specified property qualifier.</para>
            </summary>
            <param name='propertyName'>The name of the property to which the qualifier belongs.</param>
            <param name='qualifierName'>The name of the property qualifier of interest.</param>
            <param name='qualifierValue'>The new value for the qualifier.</param>
        </member>
        <member name="M:System.Management.ManagementBaseObject.GetText(System.Management.TextFormat)">
            <summary>
               <para>Returns a textual representation of the object in the specified format.</para>
            </summary>
            <param name='format'>The requested textual format. </param>
            <returns>
               <para>The textual representation of the
                  object in the specified format.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.Equals(System.Object)">
            <summary>
               <para>Compares two management objects.</para>
            </summary>
            <param name='obj'>An object to compare with this instance.</param>
            <returns>
            <see langword='true'/> if
            <paramref name="obj"/> is an instance of <see cref='T:System.Management.ManagementBaseObject'/> and represents
               the same object as this instance; otherwise, <see langword='false'/>.
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.GetHashCode">
            <summary>
                <para>Serves as a hash function for a particular type, suitable for use in hashing algorithms and data structures like a hash table.</para>
                   <para>The hash code for ManagementBaseObjects is based on the MOF for the WbemObject that this instance is based on.  Two different ManagementBaseObject instances pointing to the same WbemObject in WMI will have the same mof and thus the same hash code.  Changing a property value of an object will change the hash code. </para>
            </summary>
            <returns>
                <para>A hash code for the current object. </para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.CompareTo(System.Management.ManagementBaseObject,System.Management.ComparisonSettings)">
            <summary>
               <para>Compares this object to another, based on specified options.</para>
            </summary>
            <param name='otherObject'>The object to which to compare this object. </param>
            <param name='settings'>Options on how to compare the objects. </param>
            <returns>
            <para><see langword='true'/> if the objects compared are equal
               according to the given options; otherwise, <see langword='false'/>
               .</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementBaseObject.SetPropertyValue(System.String,System.Object)">
            <summary>
               <para>Sets the value of the named property.</para>
            </summary>
            <param name='propertyName'>The name of the property to be changed.</param>
            <param name='propertyValue'>The new value for this property.</param>
        </member>
        <member name="T:System.Management.ManagementClass">
             <summary>
                <para> Represents a CIM management class from WMI. CIM (Common Information Model) classes
                        represent management information including hardware, software, processes, etc.
                        For more information about the CIM classes available in Windows search for "win32 classes".</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates getting information about a class using the ManagementClass object
             class Sample_ManagementClass
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("Win32_LogicalDisk");
                     diskClass.Get();
                     Console.WriteLine("Logical Disk class has " + diskClass.Properties.Count + " properties");
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates getting information about a class using the ManagementClass object
             Class Sample_ManagementClass
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim diskClass As New ManagementClass("Win32_LogicalDisk")
                     diskClass.Get()
                     Console.WriteLine(("Logical Disk class has " &amp; _
                                        diskClass.Properties.Count.ToString() &amp; _
                                        " properties"))
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetManagementClass(System.Management.IWbemClassObjectFreeThreaded,System.Management.ManagementClass)">
            <summary>
            Internal factory for classes, used when deriving a class
            or cloning a class. For these purposes we always mark
            the class as "bound".
            </summary>
            <param name="wbemObject">The underlying WMI object</param>
            <param name="mgObj">Seed class from which we will get initialization info</param>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.ManagementClass'/> class.
            </overload>
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class. This is the
               default constructor.</para>
            </summary>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass();
               </code>
               <code lang='VB'>Dim c As New ManagementClass()
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor(System.Management.ManagementPath)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class initialized to the
               given path.</para>
            </summary>
            <param name='path'>A <see cref='T:System.Management.ManagementPath'/> specifying which WMI class to bind to.</param>
            <remarks>
            <para>The <paramref name="path"/> parameter must specify a WMI class
               path.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass(
                new ManagementPath("Win32_LogicalDisk"));
               </code>
               <code lang='VB'>Dim c As New ManagementClass( _
                New ManagementPath("Win32_LogicalDisk"))
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor(System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class initialized to the given path.</para>
            </summary>
            <param name='path'>The path to the WMI class.</param>
            <example>
               <code lang='C#'>ManagementClass c = new
                  ManagementClass("Win32_LogicalDisk");
               </code>
               <code lang='VB'>Dim c As New ManagementClass("Win32_LogicalDisk")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor(System.Management.ManagementPath,System.Management.ObjectGetOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class initialized to the
               given WMI class path using the specified options.</para>
            </summary>
            <param name='path'>A <see cref='T:System.Management.ManagementPath'/> representing the WMI class path.</param>
            <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> representing the options to use when retrieving this class.</param>
            <example>
               <code lang='C#'>ManagementPath p = new ManagementPath("Win32_Process");
            //Options specify that amended qualifiers are to be retrieved along with the class
            ObjectGetOptions o = new ObjectGetOptions(null, true);
            ManagementClass c = new ManagementClass(p,o);
               </code>
               <code lang='VB'>Dim p As New ManagementPath("Win32_Process")
            ' Options specify that amended qualifiers are to be retrieved along with the class
            Dim o As New ObjectGetOptions(Null, True)
            Dim c As New ManagementClass(p,o)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor(System.String,System.Management.ObjectGetOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class initialized to the given WMI class path
               using the specified options.</para>
            </summary>
            <param name='path'>The path to the WMI class.</param>
            <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> representing the options to use when retrieving the WMI class.</param>
            <example>
               <code lang='C#'>//Options specify that amended qualifiers should be retrieved along with the class
            ObjectGetOptions o = new ObjectGetOptions(null, true);
            ManagementClass c = new ManagementClass("Win32_ComputerSystem",o);
               </code>
               <code lang='VB'>' Options specify that amended qualifiers should be retrieved along with the class
            Dim o As New ObjectGetOptions(Null, True)
            Dim c As New ManagementClass("Win32_ComputerSystem",o)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor(System.Management.ManagementScope,System.Management.ManagementPath,System.Management.ObjectGetOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class for the specified
               WMI class in the specified scope and with the specified options.</para>
            </summary>
            <param name='scope'>A <see cref='T:System.Management.ManagementScope'/> that specifies the scope (server and namespace) where the WMI class resides. </param>
            <param name=' path'>A <see cref='T:System.Management.ManagementPath'/> that represents the path to the WMI class in the specified scope.</param>
            <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> that specifies the options to use when retrieving the WMI class.</param>
            <remarks>
               <para> The path can be specified as a full
                  path (including server and namespace). However, if a scope is specified, it will
                  override the first portion of the full path.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementScope s = new ManagementScope("\\\\MyBox\\root\\cimv2");
            ManagementPath p = new ManagementPath("Win32_Environment");
            ObjectGetOptions o = new ObjectGetOptions(null, true);
            ManagementClass c = new ManagementClass(s, p, o);
               </code>
               <code lang='VB'>Dim s As New ManagementScope("\\MyBox\root\cimv2")
            Dim p As New ManagementPath("Win32_Environment")
            Dim o As New ObjectGetOptions(Null, True)
            Dim c As New ManagementClass(s, p, o)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.#ctor(System.String,System.String,System.Management.ObjectGetOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementClass'/> class for the specified WMI class, in the
               specified scope, and with the specified options.</para>
            </summary>
            <param name='scope'>The scope in which the WMI class resides.</param>
            <param name=' path'>The path to the WMI class within the specified scope.</param>
            <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> that specifies the options to use when retrieving the WMI class.</param>
            <remarks>
               <para> The path can be specified as a full
                  path (including server and namespace). However, if a scope is specified, it will
                  override the first portion of the full path.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass("\\\\MyBox\\root\\cimv2",
                                                    "Win32_Environment",
                                                    new ObjectGetOptions(null, true));
               </code>
               <code lang='VB'>Dim c As New ManagementClass("\\MyBox\root\cimv2", _
                                         "Win32_Environment", _
                                         new ObjectGetOptions(Null, True))
               </code>
            </example>
        </member>
        <member name="P:System.Management.ManagementClass.Path">
            <summary>
               <para>Gets or sets the path of the WMI class to
                  which the <see cref='T:System.Management.ManagementClass'/>
                  object is bound.</para>
            </summary>
            <value>
               <para>The path of the object's class.</para>
            </value>
            <remarks>
               <para> When the property is set to a new value,
                  the <see cref='T:System.Management.ManagementClass'/>
                  object will be
                  disconnected from any previously-bound WMI class. Reconnect to the new WMI class path.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass();
            c.Path = "Win32_Environment";
               </code>
               <code lang='VB'>Dim c As New ManagementClass()
            c.Path = "Win32_Environment"
               </code>
            </example>
        </member>
        <member name="P:System.Management.ManagementClass.Derivation">
            <summary>
               <para> Gets or sets an array containing all WMI classes in the
                  inheritance hierarchy from this class to the top.</para>
            </summary>
            <value>
               A string collection containing the
               names of all WMI classes in the inheritance hierarchy of this class.
            </value>
            <remarks>
               <para>This property is read-only.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass("Win32_LogicalDisk");
            foreach (string s in c.Derivation)
                Console.WriteLine("Further derived from : ", s);
               </code>
               <code lang='VB'>Dim c As New ManagementClass("Win32_LogicalDisk")
            Dim s As String
            For Each s In c.Derivation
                Console.WriteLine("Further derived from : " &amp; s)
            Next s
               </code>
            </example>
        </member>
        <member name="P:System.Management.ManagementClass.Methods">
            <summary>
            <para>Gets or sets a collection of <see cref='T:System.Management.MethodData'/> objects that
               represent the methods defined in the WMI class.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.MethodDataCollection'/> representing the methods defined in the WMI class.</para>
            </value>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass("Win32_Process");
            foreach (Method m in c.Methods)
                Console.WriteLine("This class contains this method : ", m.Name);
               </code>
               <code lang='VB'>Dim c As New ManagementClass("Win32_Process")
            Dim m As Method
            For Each m in c.Methods
                 Console.WriteLine("This class contains this method : " &amp; m.Name)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetInstances">
            <overload>
               Returns the collection of
               all instances of the class.
            </overload>
            <summary>
               <para>Returns the collection of all instances of the class.</para>
            </summary>
            <returns>
            <para>A collection of the <see cref='T:System.Management.ManagementObject'/> objects
               representing the instances of the class.</para>
            </returns>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass("Win32_Process");
            foreach (ManagementObject o in c.GetInstances())
                 Console.WriteLine("Next instance of Win32_Process : ", o.Path);
               </code>
               <code lang='VB'>Dim c As New ManagementClass("Win32_Process")
            Dim o As ManagementObject
            For Each o In c.GetInstances()
                 Console.WriteLine("Next instance of Win32_Process : " &amp; o.Path)
            Next o
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetInstances(System.Management.EnumerationOptions)">
            <summary>
               <para>Returns the collection of all instances of the class using the specified options.</para>
            </summary>
            <param name='options'>The additional operation options.</param>
            <returns>
            <para>A collection of the <see cref='T:System.Management.ManagementObject'/> objects
               representing the instances of the class, according to the specified options.</para>
            </returns>
            <example>
               <code lang='C#'>EnumerationOptions opt = new EnumerationOptions();
            //Will enumerate instances of the given class and any subclasses.
            o.enumerateDeep = true;
            ManagementClass c = new ManagementClass("CIM_Service");
            foreach (ManagementObject o in c.GetInstances(opt))
                Console.WriteLine(o["Name"]);
               </code>
               <code lang='VB'>Dim opt As New EnumerationOptions()
            'Will enumerate instances of the given class and any subclasses.
            o.enumerateDeep = True
            Dim c As New ManagementClass("CIM_Service")
            Dim o As ManagementObject
            For Each o In c.GetInstances(opt)
                Console.WriteLine(o["Name"])
            Next o
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetInstances(System.Management.ManagementOperationObserver)">
             <summary>
                <para>Returns the collection of all instances of the class, asynchronously.</para>
             </summary>
             <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
             <example>
                <code lang='C#'>ManagementClass c = new ManagementClass("Win32_Share");
             MyHandler h = new MyHandler();
             ManagementOperationObserver ob = new ManagementOperationObserver();
             ob.ObjectReady += new ObjectReadyEventHandler (h.NewObject);
             ob.Completed += new CompletedEventHandler (h.Done);
            
             c.GetInstances(ob);
            
             while (!h.Completed)
                 System.Threading.Thread.Sleep (1000);
            
             //Here you can use the object
             Console.WriteLine(o["SomeProperty"]);
            
             public class MyHandler
             {
                 private bool completed = false;
            
                 public void NewObject(object sender, ObjectReadyEventArgs e) {
                     Console.WriteLine("New result arrived !", ((ManagementObject)(e.NewObject))["Name"]);
                 }
            
                 public void Done(object sender, CompletedEventArgs e) {
                     Console.WriteLine("async Get completed !");
                     completed = true;
                 }
            
                 public bool Completed {
                     get {
                         return completed;
                     }
                 }
             }
                </code>
                <code lang='VB'>Dim c As New ManagementClass("Win32_Share")
             Dim h As New MyHandler()
             Dim ob As New ManagementOperationObserver()
             ob.ObjectReady += New ObjectReadyEventHandler(h.NewObject)
             ob.Completed += New CompletedEventHandler(h.Done)
            
             c.GetInstances(ob)
            
             While Not h.Completed
                 System.Threading.Thread.Sleep(1000)
             End While
            
             'Here you can use the object
             Console.WriteLine(o("SomeProperty"))
            
             Public Class MyHandler
                 Private completed As Boolean = false
            
                 Public Sub Done(sender As Object, e As EventArrivedEventArgs)
                     Console.WriteLine("async Get completed !")
                 completed = True
                 End Sub
            
                 Public ReadOnly Property Completed() As Boolean
                     Get
                         Return completed
                 End Get
                 End Property
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetInstances(System.Management.ManagementOperationObserver,System.Management.EnumerationOptions)">
            <summary>
               <para>Returns the collection of all instances of the class, asynchronously, using
                  the specified options.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
            <param name=' options'>The specified additional options for getting the instances.</param>
        </member>
        <member name="M:System.Management.ManagementClass.GetSubclasses">
            <overload>
               Returns the collection of
               all derived classes for the class.
            </overload>
            <summary>
               <para>Returns the collection of all subclasses for the class.</para>
            </summary>
            <returns>
            <para>A collection of the <see cref='T:System.Management.ManagementObject'/> objects that
               represent the subclasses of the WMI class.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementClass.GetSubclasses(System.Management.EnumerationOptions)">
             <summary>
                <para>Retrieves the subclasses of the class using the specified
                   options.</para>
             </summary>
             <param name='options'>The specified additional options for retrieving subclasses of the class.</param>
             <returns>
             <para>A collection of the <see cref='T:System.Management.ManagementObject'/> objects
                representing the subclasses of the WMI class, according to the specified
                options.</para>
             </returns>
             <example>
                <code lang='C#'>EnumerationOptions opt = new EnumerationOptions();
            
             //Causes return of deep subclasses as opposed to only immediate ones.
             opt.enumerateDeep = true;
            
             ManagementObjectCollection c = (new
                   ManagementClass("Win32_Share")).GetSubclasses(opt);
                </code>
                <code lang='VB'>Dim opt As New EnumerationOptions()
            
             'Causes return of deep subclasses as opposed to only immediate ones.
             opt.enumerateDeep = true
            
             Dim cls As New ManagementClass("Win32_Share")
             Dim c As ManagementObjectCollection
            
             c = cls.GetSubClasses(opt)
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetSubclasses(System.Management.ManagementOperationObserver)">
            <summary>
               <para>Returns the collection of all classes derived from this class, asynchronously.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
        </member>
        <member name="M:System.Management.ManagementClass.GetSubclasses(System.Management.ManagementOperationObserver,System.Management.EnumerationOptions)">
            <summary>
               <para>Retrieves all classes derived from this class, asynchronously, using the specified
                  options.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
            <param name='options'>The specified additional options to use in the derived class retrieval.</param>
        </member>
        <member name="M:System.Management.ManagementClass.Derive(System.String)">
             <summary>
                <para>Derives a new class from this class.</para>
             </summary>
             <param name='newClassName'>The name of the new class to be derived.</param>
             <returns>
             <para>A new <see cref='T:System.Management.ManagementClass'/>
             that represents a new WMI class derived from the original class.</para>
             </returns>
             <remarks>
                <para>Note that the newly returned class has not been committed
                   until the <see cref='M:System.Management.ManagementObject.Put'/> method is explicitly called.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementClass existingClass = new ManagementClass("CIM_Service");
                ManagementClass newClass = existingClass.Derive("My_Service");
                newClass.Put(); //to commit the new class to the WMI repository.
                </code>
                <code lang='VB'>Dim existingClass As New ManagementClass("CIM_Service")
             Dim newClass As ManagementClass
            
             newClass = existingClass.Derive("My_Service")
             newClass.Put()  'to commit the new class to the WMI repository.
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.CreateInstance">
             <summary>
                <para>Creates a new instance of the WMI class.</para>
             </summary>
             <returns>
             <para>A <see cref='T:System.Management.ManagementObject'/> that represents a new
                instance of the WMI class.</para>
             </returns>
             <remarks>
                <para>Note that the new instance is not committed until the
                <see cref='M:System.Management.ManagementObject.Put'/> method is called. Before committing it, the key properties must
                   be specified.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementClass envClass = new ManagementClass("Win32_Environment");
                ManagementObject newInstance =
                   existingClass.CreateInstance("My_Service");
                newInstance["Name"] = "Cori";
                newInstance.Put(); //to commit the new instance.
                </code>
                <code lang='VB'>Dim envClass As New ManagementClass("Win32_Environment")
             Dim newInstance As ManagementObject
            
             newInstance = existingClass.CreateInstance("My_Service")
             newInstance("Name") = "Cori"
             newInstance.Put()  'to commit the new instance.
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.Clone">
            <summary>
               <para>Returns a copy of the object.</para>
            </summary>
            <returns>
               <para> The cloned
                  object.</para>
            </returns>
            <remarks>
               <para>Note that this does not create a copy of the
                  WMI class; only an additional representation is created.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelatedClasses">
             <overload>
                Retrieves classes related
                to the WMI class.
             </overload>
             <summary>
                <para> Retrieves classes related to the WMI class.</para>
             </summary>
             <returns>
             <para>A collection of the <see cref='T:System.Management.ManagementClass'/> or <see cref='T:System.Management.ManagementObject'/>
             objects that represents WMI classes or instances related to
             the WMI class.</para>
             </returns>
             <remarks>
                <para>The method queries the WMI schema for all
                   possible associations that the WMI class may have with other classes, or in rare
                   cases, to instances.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementClass c = new ManagementClass("Win32_LogicalDisk");
            
             foreach (ManagementClass r in c.GetRelatedClasses())
                 Console.WriteLine("Instances of {0} may have
                                    relationships to this class", r["__CLASS"]);
                </code>
                <code lang='VB'>Dim c As New ManagementClass("Win32_LogicalDisk")
             Dim r As ManagementClass
            
             For Each r In c.GetRelatedClasses()
                 Console.WriteLine("Instances of {0} may have relationships _
                                    to this class", r("__CLASS"))
             Next r
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.String)">
            <summary>
               <para> Retrieves classes related to the WMI class.</para>
            </summary>
            <param name='relatedClass'><para>The class from which resulting classes have to be derived.</para></param>
            <returns>
               A collection of classes related to
               this class.
            </returns>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.String,System.String,System.String,System.String,System.String,System.String,System.Management.EnumerationOptions)">
            <summary>
               <para> Retrieves classes related to the WMI class based on the specified
                  options.</para>
            </summary>
            <param name=' relatedClass'><para>The class from which resulting classes have to be derived.</para></param>
            <param name=' relationshipClass'> The relationship type which resulting classes must have with the source class.</param>
            <param name=' relationshipQualifier'>This qualifier must be present on the relationship.</param>
            <param name=' relatedQualifier'>This qualifier must be present on the resulting classes.</param>
            <param name=' relatedRole'>The resulting classes must have this role in the relationship.</param>
            <param name=' thisRole'>The source class must have this role in the relationship.</param>
            <param name=' options'>The options for retrieving the resulting classes.</param>
            <returns>
               <para>A collection of classes related to
                  this class.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.Management.ManagementOperationObserver)">
            <summary>
               <para> Retrieves classes
                  related to the WMI class, asynchronously.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.Management.ManagementOperationObserver,System.String)">
            <summary>
               <para> Retrieves classes related to the WMI class, asynchronously, given the related
                  class name.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
            <param name=' relatedClass'>The name of the related class.</param>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.String,System.String,System.String,System.Management.EnumerationOptions)">
            <summary>
               <para> Retrieves classes related to the
                  WMI class, asynchronously, using the specified options.</para>
            </summary>
            <param name='watcher'>Handler for progress and results of the asynchronous operation.</param>
            <param name=' relatedClass'><para>The class from which resulting classes have to be derived.</para></param>
            <param name=' relationshipClass'> The relationship type which resulting classes must have with the source class.</param>
            <param name=' relationshipQualifier'>This qualifier must be present on the relationship.</param>
            <param name=' relatedQualifier'>This qualifier must be present on the resulting classes.</param>
            <param name=' relatedRole'>The resulting classes must have this role in the relationship.</param>
            <param name=' thisRole'>The source class must have this role in the relationship.</param>
            <param name=' options'>The options for retrieving the resulting classes.</param>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelationshipClasses">
            <overload>
               Retrieves relationship
               classes that relate the class to others.
            </overload>
            <summary>
               <para>Retrieves relationship classes that relate the class to others.</para>
            </summary>
            <returns>
               <para>A collection of association classes
                  that relate the class to any other class.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.String)">
            <summary>
               <para>Retrieves relationship classes that relate the class to others, where the
                  endpoint class is the specified class.</para>
            </summary>
            <param name='relationshipClass'>The endpoint class for all relationship classes returned.</param>
            <returns>
               <para>A collection of association classes
                  that relate the class to the specified class.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.String,System.String,System.String,System.Management.EnumerationOptions)">
            <summary>
               <para> Retrieves relationship classes that relate this class to others, according to
                  specified options.</para>
            </summary>
            <param name='relationshipClass'><para> All resulting relationship classes must derive from this class.</para></param>
            <param name=' relationshipQualifier'>Resulting relationship classes must have this qualifier.</param>
            <param name=' thisRole'>The source class must have this role in the resulting relationship classes.</param>
            <param name=' options'>Specifies options for retrieving the results.</param>
            <returns>
               <para>A collection of association classes
                  that relate this class to others, according to the specified options.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.Management.ManagementOperationObserver)">
            <summary>
               <para>Retrieves relationship classes that relate the class to others,
                  asynchronously.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.Management.ManagementOperationObserver,System.String)">
            <summary>
               <para>Retrieves relationship classes that relate the class to the specified WMI class,
                  asynchronously.</para>
            </summary>
            <param name='watcher'>The object to handle the asynchronous operation's progress. </param>
            <param name=' relationshipClass'>The WMI class to which all returned relationships should point.</param>
        </member>
        <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.Management.EnumerationOptions)">
            <summary>
               <para>Retrieves relationship classes that relate the class according to the specified
                  options, asynchronously.</para>
            </summary>
            <param name='watcher'>The handler for progress and results of the asynchronous operation.</param>
            <param name='relationshipClass'><para>The class from which all resulting relationship classes must derive.</para></param>
            <param name=' relationshipQualifier'>The qualifier which the resulting relationship classes must have.</param>
            <param name=' thisRole'>The role which the source class must have in the resulting relationship classes.</param>
            <param name=' options'> The options for retrieving the results.</param>
        </member>
        <member name="M:System.Management.ManagementClass.GetStronglyTypedClassCode(System.Boolean,System.Boolean)">
             <overload>
                <para>Generates a strongly-typed class for a given WMI class.</para>
             </overload>
             <summary>
                <para>Generates a strongly-typed class for a given WMI class.</para>
             </summary>
             <param name='includeSystemClassInClassDef'><see langword='true'/> if the class for managing system properties must be included; otherwise, <see langword='false'/>.</param>
             <param name='systemPropertyClass'><see langword='true'/> if the generated class will manage system properties; otherwise, <see langword='false'/>.</param>
             <returns>
             <para>A <see cref='T:System.CodeDom.CodeTypeDeclaration'/> instance
                representing the declaration for the strongly-typed class.</para>
             </returns>
             <example>
                <code lang='C#'>using System;
             using System.Management;
             using System.CodeDom;
             using System.IO;
             using System.CodeDom.Compiler;
             using Microsoft.CSharp;
            
             void GenerateCSharpCode()
             {
                   string strFilePath = "C:\\temp\\LogicalDisk.cs";
                   CodeTypeDeclaration ClsDom;
            
                   ManagementClass cls1 = new ManagementClass(null,"Win32_LogicalDisk",null);
                   ClsDom = cls1.GetStronglyTypedClassCode(false,false);
            
                   ICodeGenerator cg = (new CSharpCodeProvider()).CreateGenerator ();
                   CodeNamespace cn = new CodeNamespace("TestNamespace");
            
                   // Add any imports to the code
                   cn.Imports.Add (new CodeNamespaceImport("System"));
                   cn.Imports.Add (new CodeNamespaceImport("System.ComponentModel"));
                   cn.Imports.Add (new CodeNamespaceImport("System.Management"));
                   cn.Imports.Add(new CodeNamespaceImport("System.Collections"));
            
                   // Add class to the namespace
                   cn.Types.Add (ClsDom);
            
                   //Now create the filestream (output file)
                   TextWriter tw = new StreamWriter(new
                   FileStream (strFilePath,FileMode.Create));
            
                   // And write it to the file
                   cg.GenerateCodeFromNamespace (cn, tw, new CodeGeneratorOptions());
            
                   tw.Close();
             }
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementClass.GetStronglyTypedClassCode(System.Management.CodeLanguage,System.String,System.String)">
             <summary>
                <para>Generates a strongly-typed class for a given WMI class. This function generates code for Visual Basic,
                   C#, or JScript, depending on the input parameters.</para>
             </summary>
             <param name='lang'>The language of the code to be generated.</param>
             <param name='filePath'>The path of the file where the code is to be written.</param>
             <param name='classNamespace'>The .NET namespace into which the class should be generated. If this is empty, the namespace will be generated from the WMI namespace.</param>
             <returns>
             <para><see langword='true'/>, if the method succeeded;
                otherwise, <see langword='false'/> .</para>
             </returns>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             ManagementClass cls = new ManagementClass(null,"Win32_LogicalDisk",null,"");
             cls.GetStronglyTypedClassCode(CodeLanguage.CSharp,"C:\temp\Logicaldisk.cs",String.Empty);
                </code>
             </example>
        </member>
        <member name="T:System.Management.ManagementDateTimeConverter">
             <summary>
                <para> Provides methods to convert DMTF datetime and time interval to CLR compliant
                <see cref='T:System.DateTime'/> and <see cref='T:System.TimeSpan'/> format and vice versa.
                </para>
             </summary>
             <example>
                <code lang='C#'>
             using System;
             using System.Management;
            
             // The sample below demonstrates the various conversions that can be done using ManagementDateTimeConverter class
             class Sample_ManagementDateTimeConverterClass
             {
                 public static int Main(string[] args)
                 {
                     string dmtfDate = "20020408141835.999999-420";
                     string dmtfTimeInterval = "00000010122532:123456:000";
            
                     // Converting DMTF datetime to System.DateTime
                     DateTime dt = ManagementDateTimeConverter.ToDateTime(dmtfDate);
            
                     // Converting System.DateTime to DMTF datetime
                     string dmtfDate = ManagementDateTimeConverter.ToDateTime(DateTime.Now);
            
                     // Converting DMTF timeinterval to System.TimeSpan
                     System.TimeSpan tsRet = ManagementDateTimeConverter. ToTimeSpan(dmtfTimeInterval);
            
                     //Converting System.TimeSpan to DMTF time interval format
                     System.TimeSpan ts = new System.TimeSpan(10,12,25,32,456);
                     string dmtfTimeInt  = ManagementDateTimeConverter.ToDmtfTimeInterval(ts);
            
                     return 0;
            
                 }
             }
                </code>
                <code lang='VB'>
             Imports System
             Imports System.Management
            
             'The sample below demonstrates the various conversions that can be done using ManagementDateTimeConverter class
             Class Sample_ManagementClass
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim dmtfDate As String = "20020408141835.999999-420"
                     Dim dmtfTimeInterval As String = "00000010122532:123456:000"
            
                     'Converting DMTF datetime and intervals to System.DateTime
                     Dim dt As DateTime = ManagementDateTimeConverter.ToDateTime(dmtfDate)
            
                     'Converting System.DateTime to DMTF datetime
                     dmtfDate = ManagementDateTimeConverter.ToDateTime(DateTime.Now)
            
                     ' Converting DMTF timeinterval to System.TimeSpan
                     Dim tsRet As System.TimeSpan = ManagementDateTimeConverter.ToTimeSpan(dmtfTimeInterval)
            
                     'Converting System.TimeSpan to DMTF time interval format
                     Dim ts As System.TimeSpan = New System.TimeSpan(10, 12, 25, 32, 456)
                     String dmtfTimeInt = ManagementDateTimeConverter.ToDmtfTimeInterval(ts)
            
                     Return 0
                 End Function
             End Class
            
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementDateTimeConverter.ToDateTime(System.String)">
            <summary>
            <para>Converts a given DMTF datetime to <see cref='T:System.DateTime'/> object. The returned DateTime will be in the
                       current TimeZone of the system.</para>
            </summary>
            <param name='dmtfDate'>A string representing the datetime in DMTF format.</param>
            <returns>
            <para>A <see cref='T:System.DateTime'/> object that represents the given DMTF datetime.</para>
            </returns>
            <remarks>
                       <para> Date and time in WMI is represented in DMTF datetime format. This format is explained in WMI SDK documentation.
                           DMTF datetime string has an UTC offset which this datetime string represents.
                            During conversion to <see cref='T:System.DateTime'/>, UTC offset is used to convert the date to the
                           current timezone. According to DMTF format a particular field can be represented by the character
                           '*'. This will be converted to the MinValue of this field that can be represented in <see cref='T:System.DateTime'/>.
                       </para>
            </remarks>
            <example>
               <code lang='C#'>
            // Convert a DMTF datetime to System.DateTime
            DateTime date = ManagementDateTimeConverter.ToDateTime("20020408141835.999999-420");
               </code>
               <code lang='VB'>
            ' Convert a DMTF datetime to System.DateTime
            Dim date as DateTime = ManagementDateTimeConverter.ToDateTime("20020408141835.999999-420")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementDateTimeConverter.ToDmtfDateTime(System.DateTime)">
             <summary>
             <para>Converts a given <see cref='T:System.DateTime'/> object to DMTF format.</para>
            
             </summary>
             <param name='date'>A <see cref='T:System.DateTime'/> object representing the datetime to be converted to DMTF datetime.</param>
             <returns>
             <para>A string that represents the DMTF datetime for the given DateTime object.</para>
             </returns>
             <remarks>
                        <para> Date and time in WMI is represented in DMTF datetime format. This format is explained in WMI SDK documentation.
                            The DMTF datetime string represented will be with respect to the UTC offset of the
                            current timezone. The lowest precision in DMTF is microseconds and
                            in <see cref='T:System.DateTime'/> is Ticks , which is equivalent to 100 of nanoseconds.
                             During conversion these Ticks are converted to microseconds and rounded
                             off to the nearest microsecond.
                        </para>
             </remarks>
             <example>
                <code lang='C#'>
             // Convert the current time in System.DateTime to DMTF format
             string dmtfDateTime = ManagementDateTimeConverter.ToDmtfDateTime(DateTime.Now);
                </code>
                <code lang='VB'>
             ' Convert the current time in System.DateTime to DMTF format
             Dim dmtfDateTime as String = ManagementDateTimeConverter.ToDmtfDateTime(DateTime.Now)
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementDateTimeConverter.ToTimeSpan(System.String)">
            <summary>
            <para>Converts a given DMTF time interval to <see cref='T:System.TimeSpan'/> object.</para>
            </summary>
            <param name='dmtfTimespan'>A string represesentation of the DMTF time interval.</param>
            <returns>
            <para>A <see cref='T:System.TimeSpan'/> object that represents the given DMTF time interval.</para>
            </returns>
            <remarks>
                       <para> Time interval in WMI is represented in DMTF format. This format is explained in WMI SDK documentation.
                               If the DMTF time interval value is more than that of
                               <see cref='F:System.TimeSpan.MaxValue'/> then <see cref='T:System.ArgumentOutOfRangeException'/> is thrown.
                       </para>
            </remarks>
            <example>
               <code lang='C#'>
            // Convert a DMTF time interval to System.TimeSpan
            TimeSpan dmtfTimeInterval = ManagementDateTimeConverter.ToTimeSpan("00000010122532:123456:000");
               </code>
               <code lang='VB'>
            ' Convert a DMTF time interval to System.TimeSpan
            Dim ts as TimeSpan = ManagementDateTimeConverter.ToTimeSpan("00000010122532:123456:000")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementDateTimeConverter.ToDmtfTimeInterval(System.TimeSpan)">
            <summary>
            <para>Converts a given <see cref='T:System.TimeSpan'/> object to DMTF time interval.</para>
            </summary>
            <param name='timespan'> A <see cref='T:System.TimeSpan'/> object representing the datetime to be converted to DMTF time interval.
            </param>
            <returns>
            <para>A string that represents the DMTF time interval for the given TimeSpan object.</para>
            </returns>
            <remarks>
                       <para> Time interval in WMI is represented in DMTF datetime format. This format
                           is explained in WMI SDK documentation. The lowest precision in
                           DMTF is microseconds and in <see cref='T:System.TimeSpan'/> is Ticks , which is equivalent
                           to 100 of nanoseconds.During conversion these Ticks are converted to
                           microseconds and rounded off to the nearest microsecond.
                       </para>
            </remarks>
            <example>
               <code lang='C#'>
            // Construct a Timespan object and convert it to DMTF format
            System.TimeSpan ts = new System.TimeSpan(10,12,25,32,456);
            String dmtfTimeInterval = ManagementDateTimeConverter.ToDmtfTimeInterval(ts);
               </code>
               <code lang='VB'>
            // Construct a Timespan object and convert it to DMTF format
            Dim ts as System.TimeSpan = new System.TimeSpan(10,12,25,32,456)
            Dim dmtfTimeInterval as String = ManagementDateTimeConverter.ToDmtfTimeInterval(ts)
               </code>
            </example>
        </member>
        <member name="T:System.Management.ManagementEventArgs">
            <summary>
               <para>Represents the virtual base class to hold event data for WMI events.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementEventArgs.#ctor(System.Object)">
            <summary>
            Constructor. This is not callable directly by applications.
            </summary>
            <param name="context">The operation context which is echoed back
            from the operation which trigerred the event.</param>
        </member>
        <member name="P:System.Management.ManagementEventArgs.Context">
            <summary>
               <para> Gets the operation context echoed back
                  from the operation that triggered the event.</para>
            </summary>
            <value>
               A WMI context object containing
               context information provided by the operation that triggered the event.
            </value>
        </member>
        <member name="T:System.Management.ObjectReadyEventArgs">
            <summary>
            <para>Holds event data for the <see cref='E:System.Management.ManagementOperationObserver.ObjectReady'/> event.</para>
            </summary>
        </member>
        <member name="M:System.Management.ObjectReadyEventArgs.#ctor(System.Object,System.Management.ManagementBaseObject)">
            <summary>
            Constructor.
            </summary>
            <param name="context">The operation context which is echoed back
            from the operation which triggered the event.</param>
            <param name="wmiObject">The newly arrived WmiObject.</param>
        </member>
        <member name="P:System.Management.ObjectReadyEventArgs.NewObject">
            <summary>
               <para> Gets the newly-returned object.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementBaseObject'/> representing the
               newly-returned object.</para>
            </value>
        </member>
        <member name="T:System.Management.CompletedEventArgs">
            <summary>
            <para> Holds event data for the <see cref='E:System.Management.ManagementOperationObserver.Completed'/> event.</para>
            </summary>
        </member>
        <member name="M:System.Management.CompletedEventArgs.#ctor(System.Object,System.Int32,System.Management.ManagementBaseObject)">
            <summary>
            Constructor.
            </summary>
            <param name="context">The operation context which is echoed back
            from the operation which trigerred the event.</param>
            <param name="status">The completion status of the operation.</param>
            <param name="wmiStatusObject">Additional status information
            encapsulated within a WmiObject. This may be null.</param>
        </member>
        <member name="P:System.Management.CompletedEventArgs.StatusObject">
            <summary>
               <para>Gets or sets additional status information
                  within a WMI object. This may be null.</para>
            </summary>
            <value>
            <para><see langword='null '/> if an error did not occur. Otherwise, may be non-null if the provider
               supports extended error information.</para>
            </value>
        </member>
        <member name="P:System.Management.CompletedEventArgs.Status">
            <summary>
               <para>Gets the completion status of the operation.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementStatus'/> value
               indicating the return code of the operation.</para>
            </value>
        </member>
        <member name="T:System.Management.ObjectPutEventArgs">
            <summary>
            <para>Holds event data for the <see cref='E:System.Management.ManagementOperationObserver.ObjectPut'/> event.</para>
            </summary>
        </member>
        <member name="M:System.Management.ObjectPutEventArgs.#ctor(System.Object,System.Management.ManagementPath)">
            <summary>
            Constructor
            </summary>
            <param name="context">The operation context which is echoed back
            from the operation which trigerred the event.</param>
            <param name="path">The WmiPath representing the identity of the
            object that has been put.</param>
        </member>
        <member name="P:System.Management.ObjectPutEventArgs.Path">
            <summary>
               <para> Gets the identity of the
                  object that has been put.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementPath'/> containing the path of the object that has
               been put.</para>
            </value>
        </member>
        <member name="T:System.Management.ProgressEventArgs">
            <summary>
            <para>Holds event data for the <see cref='E:System.Management.ManagementOperationObserver.Progress'/> event.</para>
            </summary>
        </member>
        <member name="M:System.Management.ProgressEventArgs.#ctor(System.Object,System.Int32,System.Int32,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="context">The operation context which is echoed back
            from the operation which trigerred the event.</param>
            <param name="upperBound">A quantity representing the total
            amount of work required to be done by the operation.</param>
            <param name="current">A quantity representing the current
            amount of work required to be done by the operation. This is
            always less than or equal to upperBound.</param>
            <param name="message">Optional additional information regarding
            operation progress.</param>
        </member>
        <member name="P:System.Management.ProgressEventArgs.UpperBound">
            <summary>
               <para> Gets the total
                  amount of work required to be done by the operation.</para>
            </summary>
            <value>
               An integer representing the total
               amount of work for the operation.
            </value>
        </member>
        <member name="P:System.Management.ProgressEventArgs.Current">
            <summary>
               <para> Gets the current amount of work
                  done by the operation. This is always less than or equal to <see cref='P:System.Management.ProgressEventArgs.UpperBound'/>.</para>
            </summary>
            <value>
               <para>An integer representing the current amount of work
                  already completed by the operation.</para>
            </value>
        </member>
        <member name="P:System.Management.ProgressEventArgs.Message">
            <summary>
               <para>Gets or sets optional additional information regarding the operation's progress.</para>
            </summary>
            <value>
               A string containing additional
               information regarding the operation's progress.
            </value>
        </member>
        <member name="T:System.Management.EventArrivedEventArgs">
            <summary>
            <para>Holds event data for the <see cref='E:System.Management.ManagementEventWatcher.EventArrived'/> event.</para>
            </summary>
        </member>
        <member name="P:System.Management.EventArrivedEventArgs.NewEvent">
            <summary>
               <para> Gets the WMI event that was delivered.</para>
            </summary>
            <value>
               The object representing the WMI event.
            </value>
        </member>
        <member name="T:System.Management.StoppedEventArgs">
            <summary>
            <para>Holds event data for the <see cref='E:System.Management.ManagementEventWatcher.Stopped'/> event.</para>
            </summary>
        </member>
        <member name="P:System.Management.StoppedEventArgs.Status">
            <summary>
               <para> Gets the completion status of the operation.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementStatus'/> value representing the status of the
               operation.</para>
            </value>
        </member>
        <member name="T:System.Management.EventArrivedEventHandler">
            <summary>
            <para>Represents the method that will handle the <see cref='E:System.Management.ManagementEventWatcher.EventArrived'/> event.</para>
            </summary>
        </member>
        <member name="T:System.Management.StoppedEventHandler">
            <summary>
            <para>Represents the method that will handle the <see cref='E:System.Management.ManagementEventWatcher.Stopped'/> event.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementEventWatcher">
             <summary>
                <para> Subscribes to temporary event notifications
                   based on a specified event query.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to subscribe to an event using the ManagementEventWatcher object.
             class Sample_ManagementEventWatcher
             {
                 public static int Main(string[] args) {
            
                     //For the example, we'll put a class into the repository, and watch
                     //for class deletion events when the class is deleted.
                     ManagementClass newClass = new ManagementClass();
                     newClass["__CLASS"] = "TestDeletionClass";
                     newClass.Put();
            
                     //Set up an event watcher and a handler for the event
                     ManagementEventWatcher watcher = new ManagementEventWatcher(
                         new WqlEventQuery("__ClassDeletionEvent"));
                     MyHandler handler = new MyHandler();
                     watcher.EventArrived += new EventArrivedEventHandler(handler.Arrived);
            
                     //Start watching for events
                     watcher.Start();
            
                     // For the purpose of this sample, we delete the class to trigger the event
                     // and wait for two seconds before terminating the consumer
                     newClass.Delete();
            
                     System.Threading.Thread.Sleep(2000);
            
                     //Stop watching
                     watcher.Stop();
            
                     return 0;
                 }
            
                 public class MyHandler {
                     public void Arrived(object sender, EventArrivedEventArgs e) {
                         Console.WriteLine("Class Deleted = " +
                            ((ManagementBaseObject)e.NewEvent["TargetClass"])["__CLASS"]);
                     }
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to subscribe an event using the ManagementEventWatcher object.
             Class Sample_ManagementEventWatcher
                 Public Shared Sub Main()
            
                     ' For the example, we'll put a class into the repository, and watch
                     ' for class deletion events when the class is deleted.
                     Dim newClass As New ManagementClass()
                     newClass("__CLASS") = "TestDeletionClass"
                     newClass.Put()
            
                     ' Set up an event watcher and a handler for the event
                     Dim watcher As _
                         New ManagementEventWatcher(New WqlEventQuery("__ClassDeletionEvent"))
                     Dim handler As New MyHandler()
                     AddHandler watcher.EventArrived, AddressOf handler.Arrived
            
                     ' Start watching for events
                     watcher.Start()
            
                     ' For the purpose of this sample, we delete the class to trigger the event
                     ' and wait for two seconds before terminating the consumer
                     newClass.Delete()
            
                     System.Threading.Thread.Sleep(2000)
            
                     ' Stop watching
                     watcher.Stop()
            
                 End Sub
            
                 Public Class MyHandler
                     Public Sub Arrived(sender As Object, e As EventArrivedEventArgs)
                         Console.WriteLine("Class Deleted = " &amp; _
                             CType(e.NewEvent("TargetClass"), ManagementBaseObject)("__CLASS"))
                     End Sub
                 End Class
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor">
            <overload>
               Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/> class.
            </overload>
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/> class. For further
               initialization, set the properties on the object. This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor(System.Management.EventQuery)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/> class when given a WMI event query.</para>
            </summary>
            <param name='query'>An <see cref='T:System.Management.EventQuery'/> object representing a WMI event query, which determines the events for which the watcher will listen.</param>
            <remarks>
               <para>The namespace in which the watcher will be listening for
                  events is the default namespace that is currently set.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor(System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/> class when given a WMI event query in the
               form of a string.</para>
            </summary>
            <param name='query'> A WMI event query, which defines the events for which the watcher will listen.</param>
            <remarks>
               <para>The namespace in which the watcher will be listening for
                  events is the default namespace that is currently set.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor(System.Management.ManagementScope,System.Management.EventQuery)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/>
            class that listens for events conforming to the given WMI event query.</para>
            </summary>
            <param name='scope'>A <see cref='T:System.Management.ManagementScope'/> object representing the scope (namespace) in which the watcher will listen for events.</param>
            <param name=' query'>An <see cref='T:System.Management.EventQuery'/> object representing a WMI event query, which determines the events for which the watcher will listen.</param>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor(System.String,System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/>
            class that listens for events conforming to the given WMI event query. For this
            variant, the query and the scope are specified as strings.</para>
            </summary>
            <param name='scope'> The management scope (namespace) in which the watcher will listen for events.</param>
            <param name=' query'> The query that defines the events for which the watcher will listen.</param>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor(System.String,System.String,System.Management.EventWatcherOptions)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/> class that listens for
               events conforming to the given WMI event query, according to the specified options. For
               this variant, the query and the scope are specified as strings. The options
               object can specify options such as a timeout and context information.</para>
            </summary>
            <param name='scope'>The management scope (namespace) in which the watcher will listen for events.</param>
            <param name=' query'>The query that defines the events for which the watcher will listen.</param>
            <param name='options'>An <see cref='T:System.Management.EventWatcherOptions'/> object representing additional options used to watch for events. </param>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.#ctor(System.Management.ManagementScope,System.Management.EventQuery,System.Management.EventWatcherOptions)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementEventWatcher'/> class
               that listens for events conforming to the given WMI event query, according to the specified
               options. For this variant, the query and the scope are specified objects. The
               options object can specify options such as timeout and context information.</para>
            </summary>
            <param name='scope'>A <see cref='T:System.Management.ManagementScope'/> object representing the scope (namespace) in which the watcher will listen for events.</param>
            <param name=' query'>An <see cref='T:System.Management.EventQuery'/> object representing a WMI event query, which determines the events for which the watcher will listen.</param>
            <param name='options'>An <see cref='T:System.Management.EventWatcherOptions'/> object representing additional options used to watch for events. </param>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.Finalize">
            <summary>
               <para>Ensures that outstanding calls are cleared. This is the destructor for the object.</para>
            </summary>
        </member>
        <member name="E:System.Management.ManagementEventWatcher.EventArrived">
            <summary>
               <para> Occurs when a new event arrives.</para>
            </summary>
        </member>
        <member name="E:System.Management.ManagementEventWatcher.Stopped">
            <summary>
               <para> Occurs when a subscription is canceled.</para>
            </summary>
        </member>
        <member name="P:System.Management.ManagementEventWatcher.Scope">
            <summary>
               <para>Gets or sets the scope in which to watch for events (namespace or scope).</para>
            </summary>
            <value>
               <para> The scope in which to watch for events (namespace or scope).</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementEventWatcher.Query">
            <summary>
               <para>Gets or sets the criteria to apply to events.</para>
            </summary>
            <value>
               <para> The criteria to apply to the events, which is equal to the event query.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementEventWatcher.Options">
            <summary>
               <para>Gets or sets the options used to watch for events.</para>
            </summary>
            <value>
               <para>The options used to watch for events.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.WaitForNextEvent">
            <summary>
               <para>Waits for the next event that matches the specified query to arrive, and
                  then returns it.</para>
            </summary>
            <returns>
            <para>A <see cref='T:System.Management.ManagementBaseObject'/> representing the
               newly arrived event.</para>
            </returns>
            <remarks>
               <para>If the event watcher object contains options with
                  a specified timeout, the API will wait for the next event only for the specified
                  amount of time; otherwise, the API will be blocked until the next event occurs.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.Start">
            <summary>
               <para>Subscribes to events with the given query and delivers
                  them, asynchronously, through the <see cref='E:System.Management.ManagementEventWatcher.EventArrived'/> event.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementEventWatcher.Stop">
            <summary>
               <para>Cancels the subscription whether it is synchronous or asynchronous.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementStatus">
            <summary>
               <para>Represents the enumeration of all WMI error codes that are currently defined.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NoError">
            <summary>
               The operation was successful.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.False">
            <summary>
               <para> This value is returned when no more objects
                  are available, the number of objects returned is less than the number requested,
                  or at the end of an enumeration. It is also returned when the method is called
                  with a value of 0 for the "uCount" parameter.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ResetToDefault">
            <summary>
               <para>An overridden property was deleted. This value is
                  returned to signal that the original, non-overridden value has been restored as a
                  result of the deletion.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.Different">
            <summary>
               <para> The compared items (such as objects and classes)
                  are not identical.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.Timedout">
            <summary>
               <para> A call timed out. This is not an
                  error condition; therefore, some results may have been returned.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NoMoreData">
            <summary>
               <para> No more data is available from the enumeration; the
                  user should terminate the enumeration. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.OperationCanceled">
            <summary>
               <para> The operation was
                  canceled.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.Pending">
            <summary>
               <para>A request is still in progress; however, the results are not
                  yet available.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.DuplicateObjects">
            <summary>
               <para> More than one copy of the same object was detected in
                  the result set of an enumeration. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.PartialResults">
            <summary>
               <para>The user did not receive all of the requested objects
                  because of inaccessible resources (other than security violations).</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.Failed">
            <summary>
               <para>The call failed.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NotFound">
            <summary>
               <para> The object could not be found. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.AccessDenied">
            <summary>
               The current user does not have permission to perform the
               action.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ProviderFailure">
            <summary>
               <para> The provider failed after
                  initialization. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.TypeMismatch">
            <summary>
               A type mismatch occurred.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.OutOfMemory">
            <summary>
               There was not enough memory for the operation.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidContext">
            <summary>
               <para>The context object is not valid.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidParameter">
            <summary>
               <para> One of the parameters to the call is not correct.
               </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NotAvailable">
            <summary>
               <para> The resource, typically a remote server, is not
                  currently available. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CriticalError">
            <summary>
               <para>An internal, critical, and unexpected error occurred.
                  Report this error to Microsoft Product Support Services.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidStream">
            <summary>
               <para>One or more network packets were corrupted during a remote session.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NotSupported">
            <summary>
               <para> The feature or operation is not supported. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidSuperclass">
            <summary>
               The specified base class is not valid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidNamespace">
            <summary>
               <para> The specified namespace could not be found. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidObject">
            <summary>
               The specified instance is not valid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidClass">
            <summary>
               The specified class is not valid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ProviderNotFound">
            <summary>
               A provider referenced in the schema does not have a
               corresponding registration.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidProviderRegistration">
            <summary>
               A provider referenced in the schema has an incorrect or
               incomplete registration.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ProviderLoadFailure">
            <summary>
               COM cannot locate a provider referenced in the schema.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InitializationFailure">
            <summary>
             A component, such as a provider, failed to initialize for internal reasons.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.TransportFailure">
            <summary>
               A networking error that prevents normal operation has
               occurred.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidOperation">
            <summary>
               <para> The requested operation is not valid. This error usually
                  applies to invalid attempts to delete classes or properties. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidQuery">
            <summary>
               The query was not syntactically valid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidQueryType">
            <summary>
               <para>The requested query language is not supported.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.AlreadyExists">
            <summary>
            <para>In a put operation, the <see langword='wbemChangeFlagCreateOnly'/>
            flag was specified, but the instance already exists.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.OverrideNotAllowed">
            <summary>
               <para>The add operation cannot be performed on the qualifier
                  because the owning object does not permit overrides.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.PropagatedQualifier">
            <summary>
               <para> The user attempted to delete a qualifier that was not
                  owned. The qualifier was inherited from a parent class. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.PropagatedProperty">
            <summary>
               <para> The user attempted to delete a property that was not
                  owned. The property was inherited from a parent class. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.Unexpected">
            <summary>
               The client made an unexpected and illegal sequence of
               calls.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.IllegalOperation">
            <summary>
               <para>The user requested an illegal operation, such as
                  spawning a class from an instance.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CannotBeKey">
            <summary>
               <para> There was an illegal attempt to specify a key qualifier
                  on a property that cannot be a key. The keys are specified in the class
                  definition for an object and cannot be altered on a per-instance basis.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.IncompleteClass">
            <summary>
               <para>The current object is not a valid class definition.
                  Either it is incomplete, or it has not been registered with WMI using
               <see cref='M:System.Management.ManagementObject.Put'/>().</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidSyntax">
            <summary>
               Reserved for future use.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NondecoratedObject">
            <summary>
               Reserved for future use.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ReadOnly">
            <summary>
               <para>The property that you are attempting to modify is read-only.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ProviderNotCapable">
            <summary>
               <para> The provider cannot perform the requested operation, such
                  as requesting a query that is too complex, retrieving an instance, creating or
                  updating a class, deleting a class, or enumerating a class. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ClassHasChildren">
            <summary>
               <para>An attempt was made to make a change that would
                  invalidate a derived class.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ClassHasInstances">
            <summary>
               <para> An attempt has been made to delete or modify a class that
                  has instances. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.QueryNotImplemented">
            <summary>
               Reserved for future use.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.IllegalNull">
            <summary>
               <para> A value of null was specified for a property that may
                  not be null, such as one that is marked by a <see langword='Key'/>, <see langword='Indexed'/>, or
               <see langword='Not_Null'/> qualifier.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidQualifierType">
            <summary>
               <para> The value provided for a qualifier was not a
                  legal qualifier type.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidPropertyType">
            <summary>
               The CIM type specified for a property is not valid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ValueOutOfRange">
            <summary>
               <para> The request was made with an out-of-range value, or is
                  incompatible with the type. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CannotBeSingleton">
            <summary>
               <para>An illegal attempt was made to make a class singleton,
                  such as when the class is derived from a non-singleton class.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidCimType">
            <summary>
               The CIM type specified is not valid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidMethod">
            <summary>
               The requested method is not available.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidMethodParameters">
            <summary>
               <para> The parameters provided for the method are not valid.
               </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.SystemProperty">
            <summary>
               There was an attempt to get qualifiers on a system
               property.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidProperty">
            <summary>
               The property type is not recognized.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CallCanceled">
            <summary>
               <para> An asynchronous process has been canceled internally or
                  by the user. Note that because of the timing and nature of the asynchronous
                  operation, the operation may not have been truly canceled. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ShuttingDown">
            <summary>
               <para>The user has requested an operation while WMI is in the
                  process of quitting.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.PropagatedMethod">
            <summary>
               <para> An attempt was made to reuse an existing method name from
                  a base class, and the signatures did not match. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UnsupportedParameter">
            <summary>
               <para> One or more parameter values, such as a query text, is
                  too complex or unsupported. WMI is requested to retry the operation
                  with simpler parameters. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MissingParameterID">
            <summary>
               A parameter was missing from the method call.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidParameterID">
            <summary>
               A method parameter has an invalid <see langword='ID'/> qualifier.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NonconsecutiveParameterIDs">
            <summary>
            <para> One or more of the method parameters have <see langword='ID'/>
            qualifiers that are out of sequence. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ParameterIDOnRetval">
            <summary>
            <para> The return value for a method has an <see langword='ID'/> qualifier.
            </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidObjectPath">
            <summary>
               The specified object path was invalid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.OutOfDiskSpace">
            <summary>
               <para> There is not enough free disk space to continue the
                  operation. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.BufferTooSmall">
            <summary>
               <para> The supplied buffer was too small to hold all the objects
                  in the enumerator or to read a string property. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UnsupportedPutExtension">
            <summary>
               The provider does not support the requested put
               operation.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UnknownObjectType">
            <summary>
               <para> An object with an incorrect type or version was
                  encountered during marshaling. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UnknownPacketType">
            <summary>
               <para> A packet with an incorrect type or version was
                  encountered during marshaling. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MarshalVersionMismatch">
            <summary>
               The packet has an unsupported version.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MarshalInvalidSignature">
            <summary>
               <para>The packet is corrupted.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidQualifier">
            <summary>
               An attempt has been made to mismatch qualifiers, such as
               putting [key] on an object instead of a property.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidDuplicateParameter">
            <summary>
               A duplicate parameter has been declared in a CIM method.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.TooMuchData">
            <summary>
               <para> Reserved for future use. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ServerTooBusy">
            <summary>
               <para>The delivery of an event has failed. The provider may
                  choose to re-raise the event.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidFlavor">
            <summary>
               The specified flavor was invalid.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CircularReference">
            <summary>
               <para> An attempt has been made to create a reference that is
                  circular (for example, deriving a class from itself). </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UnsupportedClassUpdate">
            <summary>
               The specified class is not supported.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CannotChangeKeyInheritance">
            <summary>
               <para> An attempt was made to change a key when instances or derived
                  classes are already using the key. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CannotChangeIndexInheritance">
            <summary>
               <para> An attempt was made to change an index when instances or derived
                  classes are already using the index. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.TooManyProperties">
            <summary>
               <para> An attempt was made to create more properties than the
                  current version of the class supports. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UpdateTypeMismatch">
            <summary>
               <para> A property was redefined with a conflicting type in a
                  derived class. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UpdateOverrideNotAllowed">
            <summary>
               <para> An attempt was made in a derived class to override a
                  non-overrideable qualifier. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UpdatePropagatedMethod">
            <summary>
               <para> A method was redeclared with a conflicting signature in a
                  derived class. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MethodNotImplemented">
            <summary>
               An attempt was made to execute a method not marked with
               [implemented] in any relevant class.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MethodDisabled">
            <summary>
               <para> An attempt was made to execute a method marked with
                  [disabled]. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.RefresherBusy">
            <summary>
               <para> The refresher is busy with another operation. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UnparsableQuery">
            <summary>
               <para> The filtering query is syntactically invalid. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.NotEventClass">
            <summary>
               The FROM clause of a filtering query references a class
               that is not an event class.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MissingGroupWithin">
            <summary>
               A GROUP BY clause was used without the corresponding
               GROUP WITHIN clause.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.MissingAggregationList">
            <summary>
               A GROUP BY clause was used. Aggregation on all properties
               is not supported.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.PropertyNotAnObject">
            <summary>
               <para> Dot notation was used on a property that is not an
                  embedded object. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.AggregatingByObject">
            <summary>
               A GROUP BY clause references a property that is an
               embedded object without using dot notation.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.UninterpretableProviderQuery">
            <summary>
               An event provider registration query
               (<see langword='__EventProviderRegistration'/>) did not specify the classes for which
               events were provided.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.BackupRestoreWinmgmtRunning">
            <summary>
               <para> An request was made to back up or restore the repository
                  while WinMgmt.exe was using it. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.QueueOverflow">
            <summary>
               <para> The asynchronous delivery queue overflowed from the
                  event consumer being too slow. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.PrivilegeNotHeld">
            <summary>
               The operation failed because the client did not have the
               necessary security privilege.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.InvalidOperator">
            <summary>
               <para>The operator is not valid for this property type.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.LocalCredentials">
            <summary>
               <para> The user specified a username, password, or authority on a
                  local connection. The user must use an empty user name and password and rely on
                  default security. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.CannotBeAbstract">
            <summary>
               <para> The class was made abstract when its base class is not
                  abstract. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.AmendedObject">
            <summary>
               <para> An amended object was used in a put operation without the
                  WBEM_FLAG_USE_AMENDED_QUALIFIERS flag being specified. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.ClientTooSlow">
            <summary>
               The client was not retrieving objects quickly enough from
               an enumeration.
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.RegistrationTooBroad">
            <summary>
               <para> The provider registration overlaps with the system event
                  domain. </para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementStatus.RegistrationTooPrecise">
            <summary>
               <para> A WITHIN clause was not used in this query. </para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementException">
             <summary>
                <para> Represents management exceptions.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to display error
             // information stored in a ManagementException object.
             class Sample_ManagementException
             {
                 public static int Main(string[] args)
                 {
                     try
                     {
                         ManagementObject disk =
                             new ManagementObject("Win32_LogicalDisk.DeviceID='BAD:'");
                         disk.Get(); // throws ManagementException
                         Console.WriteLine("This shouldn't be displayed.");
                     }
                     catch (ManagementException e)
                     {
                       Console.WriteLine("ErrorCode " + e.ErrorCode);
                       Console.WriteLine("Message " + e.Message);
                       Console.WriteLine("Source " + e.Source);
                       if (e.ErrorInformation) //extended error object
                           Console.WriteLine("Extended Description : " + e.ErrorInformation["Description"]);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to display error
             ' information stored in a ManagementException object.
             Class Sample_ManagementException
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Try
                         Dim disk As New ManagementObject("Win32_LogicalDisk.DeviceID='BAD:'")
                         disk.Get() ' throws ManagementException
                         Console.WriteLine("This shouldn't be displayed.")
                     Catch e As ManagementException
                         Console.WriteLine("ErrorCode " &amp; e.ErrorCode)
                         Console.WriteLine("Message " &amp; e.Message)
                         Console.WriteLine("Source " &amp; e.Source)
                         If e.ErrorInformation != Nothing Then 'extended error object
                             Console.WriteLine("Extended Description : " &amp; e.ErrorInformation("Description"))
                         End If
                     End Try
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementException'/> class that is serializable.</para>
            </summary>
            <param name='info'>The <see cref='T:System.Runtime.Serialization.SerializationInfo'/> to populate with data.</param>
            <param name='context'>The destination (see <see cref='T:System.Runtime.Serialization.StreamingContext'/> ) for this serialization.</param>
        </member>
        <member name="M:System.Management.ManagementException.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementException'/> class</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementException.#ctor(System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementException'/>
            class with a specified error message.</para>
            </summary>
            <param name='message'>The message that describes the error.</param>
        </member>
        <member name="M:System.Management.ManagementException.#ctor(System.String,System.Exception)">
            <summary>
            <para>Initializes a empty new instance of the <see cref='T:System.Management.ManagementException'/> class </para>
            </summary>
            <param name='message'>The message that describes the error.</param>
            <param name='innerException'>The exception that is the cause of the current exception. If the innerException
            parameter is not a null reference (Nothing in Visual Basic), the current exception is raised in a catch
            block that handles the inner exception.</param>
        </member>
        <member name="M:System.Management.ManagementException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            <para>Populates the <see cref='T:System.Runtime.Serialization.SerializationInfo'/> object with the data needed to
               serialize the <see cref='T:System.Management.ManagementException'/>
               object.</para>
            </summary>
            <param name='info'>The <see cref='T:System.Runtime.Serialization.SerializationInfo'/> to populate with data.</param>
            <param name='context'>The destination (see <see cref='T:System.Runtime.Serialization.StreamingContext'/> ) for this serialization.</param>
        </member>
        <member name="P:System.Management.ManagementException.ErrorInformation">
            <summary>
               <para>Gets the extended error object provided by WMI.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementBaseObject'/> representing the
               extended error object provided by WMI, if available; <see langword='null'/>
               otherwise.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementException.ErrorCode">
            <summary>
               <para>Gets the error code reported by WMI, which caused this exception.</para>
            </summary>
            <value>
               A <see cref='T:System.Management.ManagementStatus'/> value representing the error code returned by
               the WMI operation.
            </value>
        </member>
        <member name="T:System.Management.ManagementNamedValueCollection">
            <summary>
               <para> Represents a collection of named values
                  suitable for use as context information to WMI operations. The
                  names are case-insensitive.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.ManagementNamedValueCollection'/> class.
            </overload>
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementNamedValueCollection'/> class, which is empty. This is
               the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementNamedValueCollection'/> class that is serializable
               and uses the specified <see cref='T:System.Runtime.Serialization.SerializationInfo'/>
               and <see cref='T:System.Runtime.Serialization.StreamingContext'/>.</para>
            </summary>
            <param name='info'>The <see cref='T:System.Runtime.Serialization.SerializationInfo'/> to populate with data.</param>
            <param name='context'>The destination (see <see cref='T:System.Runtime.Serialization.StreamingContext'/> ) for this serialization.</param>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.GetContext">
            <summary>
               <para>Internal method to return an IWbemContext representation
               of the named value collection.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.Add(System.String,System.Object)">
            <summary>
               <para> Adds a single-named value to the collection.</para>
            </summary>
            <param name=' name'>The name of the new value.</param>
            <param name=' value'>The value to be associated with the name.</param>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.Remove(System.String)">
            <summary>
               <para> Removes a single-named value from the collection.
                  If the collection does not contain an element with the
                  specified name, the collection remains unchanged and no
                  exception is thrown.</para>
            </summary>
            <param name=' name'>The name of the value to be removed.</param>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.RemoveAll">
            <summary>
               <para>Removes all entries from the collection.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementNamedValueCollection.Clone">
            <summary>
               <para>Creates a clone of the collection. Individual values
                  are cloned. If a value does not support cloning, then a <see cref='T:System.NotSupportedException'/>
                  is thrown. </para>
            </summary>
            <returns>
               The new copy of the collection.
            </returns>
        </member>
        <member name="P:System.Management.ManagementNamedValueCollection.Item(System.String)">
            <summary>
               <para>Returns the value associated with the specified name from this collection.</para>
            </summary>
            <param name=' name'>The name of the value to be returned.</param>
            <value>
            <para>An <see cref='T:System.Object'/> containing the
               value of the specified item in this collection.</para>
            </value>
        </member>
        <member name="T:System.Management.IdentifierChangedEventHandler">
            <summary>
            Delegate definition for the IdentifierChanged event.
            This event is used to signal the ManagementObject that an identifying property
            has been changed. Identifying properties are the ones that identify the object,
            namely the scope, path and options.
            </summary>
        </member>
        <member name="T:System.Management.InternalObjectPutEventHandler">
            <summary>
            Delegate definition for InternalObjectPut event. This is used so that
            the WmiEventSink can signal to this object that the async Put call has
            completed.
            </summary>
        </member>
        <member name="T:System.Management.ManagementObject">
             <summary>
                <para> Represents a data management object.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates reading a property of a ManagementObject.
             class Sample_ManagementObject
             {
                 public static int Main(string[] args) {
                     ManagementObject disk = new ManagementObject(
                         "win32_logicaldisk.deviceid=\"c:\"");
                     disk.Get();
                     Console.WriteLine("Logical Disk Size = " + disk["Size"] + " bytes");
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates reading a property of a ManagementObject.
             Class Sample_ManagementObject
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim disk As New ManagementObject("win32_logicaldisk.deviceid=""c:""")
                     disk.Get()
                     Console.WriteLine(("Logical Disk Size = " &amp; disk("Size").ToString() _
                         &amp; " bytes"))
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor">
             <overload>
                Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class.
             </overload>
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class. This is the
                default constructor.</para>
             </summary>
             <example>
                <code lang='C#'>ManagementObject o = new ManagementObject();
            
             //Now set the path on this object to bind it to a 'real' manageable entity
             o.Path = new ManagementPath("Win32_LogicalDisk='c:'");
            
             //Now it can be used
             Console.WriteLine(o["FreeSpace"]);
                </code>
                <code lang='VB'>Dim o As New ManagementObject()
             Dim mp As New ManagementPath("Win32_LogicalDisk='c:'")
            
             'Now set the path on this object to bind it to a 'real' manageable entity
             o.Path = mp
            
             'Now it can be used
             Console.WriteLine(o("FreeSpace"))
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.Management.ManagementPath)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class for the specified WMI
               object path. The path is provided as a <see cref='T:System.Management.ManagementPath'/>.</para>
            </summary>
            <param name='path'>A <see cref='T:System.Management.ManagementPath'/> that contains a path to a WMI object.</param>
            <example>
               <code lang='C#'>ManagementPath p = new ManagementPath("Win32_Service.Name='Alerter'");
            ManagementObject o = new ManagementObject(p);
               </code>
               <code lang='VB'>Dim p As New ManagementPath("Win32_Service.Name=""Alerter""")
            Dim o As New ManagementObject(p)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.String)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class for the specified WMI object path. The path
                is provided as a string.</para>
             </summary>
             <param name='path'>A WMI path.</param>
             <remarks>
                <para>If the specified path is a relative path only (a server
                   or namespace is not specified), the default path is the local machine, and the
                   default namespace is the <see cref='P:System.Management.ManagementPath.DefaultPath'/>
                   path (by default, root\cimv2). If the user specifies a
                   full path, the default settings are overridden.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementObject o = new ManagementObject("Win32_Service.Name='Alerter'");
            
             //or with a full path :
            
             ManagementObject o = new ManagementObject("\\\\MyServer\\root\\MyApp:MyClass.Key='abc'");
                </code>
                <code lang='VB'>Dim o As New ManagementObject("Win32_Service.Name=""Alerter""")
            
             //or with a full path :
            
             Dim o As New ManagementObject("\\\\MyServer\\root\\MyApp:MyClass.Key=""abc""");
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.Management.ManagementPath,System.Management.ObjectGetOptions)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class bound to the specified
                WMI path, including the specified additional options.</para>
             </summary>
             <param name='path'>A <see cref='T:System.Management.ManagementPath'/> containing the WMI path.</param>
             <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> containing additional options for binding to the WMI object. This parameter could be null if default options are to be used.</param>
             <example>
                <code lang='C#'>ManagementPath p = new ManagementPath("Win32_ComputerSystem.Name='MyMachine'");
            
             //Set options for no context info, but requests amended qualifiers
             //to be contained in the object
             ObjectGetOptions opt = new ObjectGetOptions(null, true);
            
             ManagementObject o = new ManagementObject(p, opt);
            
             Console.WriteLine(o.GetQualifierValue("Description"));
                </code>
                <code lang='VB'>Dim p As New ManagementPath("Win32_ComputerSystem.Name=""MyMachine""")
            
             'Set options for no context info, but requests amended qualifiers
             'to be contained in the object
             Dim opt As New ObjectGetOptions(null, true)
            
             Dim o As New ManagementObject(p, opt)
            
             Console.WriteLine(o.GetQualifierValue("Description"));
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.String,System.Management.ObjectGetOptions)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class bound to the specified WMI path, including the
                specified additional options. In this variant, the path can be specified as a
                string.</para>
             </summary>
             <param name='path'>The WMI path to the object.</param>
             <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> representing options to get the specified WMI object.</param>
             <example>
                <code lang='C#'>//Set options for no context info,
             //but requests amended qualifiers to be contained in the object
             ObjectGetOptions opt = new ObjectGetOptions(null, true);
            
             ManagementObject o = new ManagementObject("Win32_ComputerSystem.Name='MyMachine'", opt);
            
             Console.WriteLine(o.GetQualifierValue("Description"));
                </code>
                <code lang='VB'>'Set options for no context info,
             'but requests amended qualifiers to be contained in the object
             Dim opt As New ObjectGetOptions(null, true)
            
             Dim o As New ManagementObject("Win32_ComputerSystem.Name=""MyMachine""", opt);
            
             Console.WriteLine(o.GetQualifierValue("Description"))
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.Management.ManagementScope,System.Management.ManagementPath,System.Management.ObjectGetOptions)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/>
            class bound to the specified WMI path that includes the specified options.</para>
            </summary>
            <param name='scope'>A <see cref='T:System.Management.ManagementScope'/> representing the scope in which the WMI object resides. In this version, scopes can only be WMI namespaces.</param>
            <param name=' path'>A <see cref='T:System.Management.ManagementPath'/> representing the WMI path to the manageable object.</param>
            <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> specifying additional options for getting the object.</param>
            <remarks>
               <para> Because WMI paths can be relative or full, a conflict between the scope and the path
                  specified may arise. However, if a scope is specified and
                  a relative WMI path is specified, then there is no conflict. The
                  following are some possible conflicts: </para>
               <para> If a scope is not specified and a relative WMI
                  path is specified, then the scope will default to the local machine's <see cref='P:System.Management.ManagementPath.DefaultPath'/>. </para>
               <para> If a scope is not specified and a full WMI path is
                  specified, then the scope will be inferred from the scope portion of the full
                  path. For example, the full WMI path: <c>\\MyMachine\root\MyNamespace:MyClass.Name='abc'</c> will
               represent the WMI object 'MyClass.Name='abc'" in the scope
               '\\MyMachine\root\MyNamespace'. </para>
            If a scope is specified and a full WMI path is specified, then the scope
            will override the scope portion of the full path. For example, if the following
            scope was specified: \\MyMachine\root\MyScope, and the following full path was
            specified: \\MyMachine\root\MyNamespace:MyClass.Name='abc', then look for the
            following <c>object:
            \\MyMachine\root\MyScope:MyClass.Name=
            'abc'</c>
            (the scope part of the full path is ignored).
            </remarks>
            <example>
               <code lang='C#'>ManagementScope s = new ManagementScope("\\\\MyMachine\\root\\cimv2");
            ManagementPath p = new ManagementPath("Win32_LogicalDisk.Name='c:'");
            ManagementObject o = new ManagementObject(s,p);
               </code>
               <code lang='VB'>Dim s As New ManagementScope("\\MyMachine\root\cimv2");
            Dim p As New ManagementPath("Win32_LogicalDisk.Name=""c:""");
            Dim o As New ManagementObject(s,p);
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.String,System.String,System.Management.ObjectGetOptions)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class
               bound to the specified WMI path, and includes the specified options. The scope and
               the path are specified as strings.</para>
            </summary>
            <param name='scopeString'>The scope for the WMI object.</param>
            <param name=' pathString'>The WMI object path.</param>
            <param name=' options'>An <see cref='T:System.Management.ObjectGetOptions'/> representing additional options for getting the WMI object.</param>
            <remarks>
               <para>See the equivalent overload for details.</para>
            </remarks>
            <example>
               <code lang='C#'>GetObjectOptions opt = new GetObjectOptions(null, true);
            ManagementObject o = new ManagementObject("root\\MyNamespace", "MyClass.Name='abc'", opt);
               </code>
               <code lang='VB'>Dim opt As New GetObjectOptions(null, true)
            Dim o As New ManagementObject("root\MyNamespace", "MyClass.Name=""abc""", opt);
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObject'/> class that is serializable.</para>
            </summary>
            <param name='info'>The <see cref='T:System.Runtime.Serialization.SerializationInfo'/> to populate with data.</param>
            <param name='context'>The destination (see <see cref='T:System.Runtime.Serialization.StreamingContext'/> ) for this serialization.</param>
        </member>
        <member name="P:System.Management.ManagementObject.Scope">
             <summary>
                <para> Gets or sets the scope in which this object resides.</para>
             </summary>
             <value>
             <para> A <see cref='T:System.Management.ManagementScope'/>.</para>
             </value>
             <remarks>
                <para>
                   Changing
                   this property after the management object has been bound to a WMI object in
                   a particular namespace results in releasing the original WMI object. This causes the management object to
                   be rebound to the new object specified by the new path properties and scope
                   values. </para>
                <para>The rebinding is performed in a "lazy" manner, that is, only when a requested
                   value requires the management object to be bound to the WMI object. Changes can
                   be made to more than just this property before attempting to rebind (for example, modifying the scope
                   and path properties simultaneously).</para>
             </remarks>
             <example>
                <code lang='C#'>//Create the object with the default namespace (root\cimv2)
             ManagementObject o = new ManagementObject();
            
             //Change the scope (=namespace) of this object to the one specified.
             o.Scope = new ManagementScope("root\\MyAppNamespace");
                </code>
                <code lang='VB'>'Create the object with the default namespace (root\cimv2)
             Dim o As New ManagementObject()
            
             'Change the scope (=namespace) of this object to the one specified.
             o.Scope = New ManagementScope("root\MyAppNamespace")
                </code>
             </example>
        </member>
        <member name="P:System.Management.ManagementObject.Path">
             <summary>
                <para> Gets or sets the object's WMI path.</para>
             </summary>
             <value>
             <para>A <see cref='T:System.Management.ManagementPath'/> representing the object's path.</para>
             </value>
             <remarks>
                <para>
                   Changing the property after the management
                   object has been bound to a WMI object in a particular namespace results in releasing
                   the original WMI object. This causes the management object to be rebound to
                   the new object specified by the new path properties and scope values.</para>
                <para>The rebinding is performed in a "lazy" manner, that is, only when a requested
                   value requires the management object to be bound to the WMI object. Changes can
                   be made to more than just the property before attempting to rebind (for example,
                   modifying the scope and path properties simultaneously).</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementObject o = new ManagementObject();
            
             //Specify the WMI path to which this object should be bound to
             o.Path = new ManagementPath("MyClass.Name='MyName'");
                </code>
                <code lang='VB'>Dim o As New ManagementObject()
            
             'Specify the WMI path to which this object should be bound to
             o.Path = New ManagementPath("MyClass.Name=""MyName""");
                </code>
             </example>
        </member>
        <member name="P:System.Management.ManagementObject.Options">
             <summary>
                <para>
                   Gets or
                   sets additional information to use when retrieving the object.</para>
             </summary>
             <value>
             <para>An <see cref='T:System.Management.ObjectGetOptions'/> to use when retrieving the object.</para>
             </value>
             <remarks>
                <para> When the property is
                   changed after the management object has been bound to a WMI object, the management object
                   is disconnected from the original WMI object and later rebound using the new
                   options.</para>
             </remarks>
             <example>
                <code lang='C#'>//Contains default options
             ManagementObject o = new ManagementObject("MyClass.Name='abc'");
            
             //Replace default options, in this case requesting retrieval of
             //amended qualifiers along with the WMI object.
             o.Options = new ObjectGetOptions(null, true);
                </code>
                <code lang='VB'>'Contains default options
             Dim o As New ManagementObject("MyClass.Name=""abc""")
            
             'Replace default options, in this case requesting retrieval of
             'amended qualifiers along with the WMI object.
             o.Options = New ObjectGetOptions(null, true)
                </code>
             </example>
        </member>
        <member name="P:System.Management.ManagementObject.ClassPath">
             <summary>
                <para>Gets or sets the path to the object's class.</para>
             </summary>
             <value>
             <para>A <see cref='T:System.Management.ManagementPath'/> representing the path to the object's
                class.</para>
             </value>
             <remarks>
                <para>This property is read-only.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementObject o = new ManagementObject("MyClass.Name='abc'");
            
             //Get the class definition for the object above.
             ManagementClass c = new ManagementClass(o.ClassPath);
                </code>
                <code lang='VB'>Dim o As New ManagementObject("MyClass.Name=""abc""")
            
             'Get the class definition for the object above.
             Dim c As New ManagementClass(o.ClassPath);
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.Get">
             <overload>
                Binds to the management object.
             </overload>
             <summary>
                <para> Binds to the management object.</para>
             </summary>
             <remarks>
                <para> The method is implicitly
                   invoked at the first attempt to get or set information to the WMI object. It
                   can also be explicitly invoked at the user's discretion, to better control the
                   timing and manner of retrieval.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementObject o = new ManagementObject("MyClass.Name='abc'");
             string s = o["SomeProperty"]; //this causes an implicit Get().
            
             //or :
            
             ManagementObject o= new ManagementObject("MyClass.Name= 'abc'");
             o.Get(); //explicitly
             //Now it's faster because the object has already been retrieved.
             string s = o["SomeProperty"];
                </code>
                <code lang='VB'>Dim o As New ManagementObject("MyClass.Name=""abc""")
             string s = o("SomeProperty") 'this causes an implicit Get().
            
             'or :
            
             Dim o As New ManagementObject("MyClass.Name= ""abc""")
             o.Get()  'explicitly
             'Now it's faster because the object has already been retrieved.
             string s = o("SomeProperty");
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.Get(System.Management.ManagementOperationObserver)">
             <summary>
                <para> Binds to the management object asynchronously.</para>
             </summary>
             <param name='watcher'>The object to receive the results of the operation as events.</param>
             <remarks>
                <para>The method will issue the request to get the object
                   and then will immediately return. The results of the operation will then be
                   delivered through events being fired on the watcher object provided.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementObject o = new ManagementObject("MyClass.Name='abc'");
            
             //Set up handlers for asynchronous get
             MyHandler h = new MyHandler();
             ManagementOperationObserver ob = new ManagementOperationObserver();
             ob.Completed += new CompletedEventHandler(h.Done);
            
             //Get the object asynchronously
             o.Get(ob);
            
             //Wait until operation is completed
             while (!h.Completed)
                 System.Threading.Thread.Sleep (1000);
            
             //Here we can use the object
             Console.WriteLine(o["SomeProperty"]);
            
             public class MyHandler
             {
                 private bool completed = false;
            
                 public void Done(object sender, CompletedEventArgs e) {
                     Console.WriteLine("async Get completed !");
                     completed = true;
                 }
            
                 public bool Completed {
                     get {
                         return completed;
                     }
                 }
             }
                </code>
                <code lang='VB'>Dim o As New ManagementObject("MyClass.Name=""abc""")
            
             'Set up handlers for asynchronous get
             Dim h As New MyHandler()
             Dim ob As New ManagementOperationObserver()
             ob.Completed += New CompletedEventHandler(h.Done)
            
             'Get the object asynchronously
             o.Get(ob)
            
             'Wait until operation is completed
             While Not h.Completed
                 System.Threading.Thread.Sleep(1000)
             End While
            
             'Here we can use the object
             Console.WriteLine(o("SomeProperty"))
            
             Public Class MyHandler
                 Private _completed As Boolean = false;
            
                 Public Sub Done(sender As Object, e As EventArrivedEventArgs)
                     Console.WriteLine("async Get completed !")
                     _completed = True
                 End Sub
            
                 Public ReadOnly Property Completed() As Boolean
                    Get
                        Return _completed
                    End Get
                 End Property
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelated">
            <overload>
               <para>Gets a collection of objects related to the object (associators).</para>
            </overload>
            <summary>
               <para>Gets a collection of objects related to the object (associators).</para>
            </summary>
            <returns>
            <para>A <see cref='T:System.Management.ManagementObjectCollection'/> containing the
               related objects.</para>
            </returns>
            <remarks>
               <para> The operation is equivalent to an ASSOCIATORS OF query where ResultClass = relatedClass.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementObject o = new ManagementObject("Win32_Service='Alerter'");
            foreach (ManagementBaseObject b in o.GetRelated())
                Console.WriteLine("Object related to Alerter service : ", b.Path);
               </code>
               <code lang='VB'>Dim o As New ManagementObject("Win32_Service=""Alerter""")
            Dim b As ManagementBaseObject
            For Each b In o.GetRelated()
                Console.WriteLine("Object related to Alerter service : ", b.Path)
            Next b
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelated(System.String)">
            <summary>
               <para>Gets a collection of objects related to the object (associators).</para>
            </summary>
            <param name='relatedClass'>A class of related objects. </param>
            <returns>
               A <see cref='T:System.Management.ManagementObjectCollection'/> containing the related objects.
            </returns>
            <example>
               <code lang='C#'>ManagementObject o = new ManagementObject("Win32_Service='Alerter'");
            foreach (ManagementBaseObject b in o.GetRelated("Win32_Service")
                Console.WriteLine("Service related to the Alerter service {0} is {1}", b["Name"], b["State"]);
               </code>
               <code lang='VB'>Dim o As New ManagementObject("Win32_Service=""Alerter""");
            Dim b As ManagementBaseObject
            For Each b in o.GetRelated("Win32_Service")
                Console.WriteLine("Service related to the Alerter service {0} is {1}", b("Name"), b("State"))
            Next b
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelated(System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
            <summary>
               <para>Gets a collection of objects related to the object (associators).</para>
            </summary>
            <param name='relatedClass'>The class of the related objects. </param>
            <param name='relationshipClass'>The relationship class of interest. </param>
            <param name='relationshipQualifier'>The qualifier required to be present on the relationship class. </param>
            <param name='relatedQualifier'>The qualifier required to be present on the related class. </param>
            <param name='relatedRole'>The role that the related class is playing in the relationship. </param>
            <param name='thisRole'>The role that this class is playing in the relationship. </param>
            <param name='classDefinitionsOnly'>When this method returns, it contains only class definitions for the instances that match the query. </param>
            <param name='options'>Extended options for how to execute the query. </param>
            <returns>
               A <see cref='T:System.Management.ManagementObjectCollection'/> containing the related objects.
            </returns>
            <remarks>
               <para>This operation is equivalent to an ASSOCIATORS OF query where ResultClass = &lt;relatedClass&gt;.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelated(System.Management.ManagementOperationObserver)">
            <summary>
               <para> Gets a collection of objects
                  related to the object (associators) asynchronously. This call returns immediately, and a
                  delegate is called when the results are available.</para>
            </summary>
            <param name='watcher'>The object to use to return results. </param>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelated(System.Management.ManagementOperationObserver,System.String)">
            <summary>
               <para>Gets a collection of objects related to the object (associators).</para>
            </summary>
            <param name='watcher'>The object to use to return results. </param>
            <param name='relatedClass'>The class of related objects. </param>
            <remarks>
               <para>This operation is equivalent to an ASSOCIATORS OF query where ResultClass = &lt;relatedClass&gt;.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelated(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
            <summary>
               <para>Gets a collection of objects related to the object (associators).</para>
            </summary>
            <param name='watcher'>The object to use to return results. </param>
            <param name='relatedClass'>The class of the related objects. </param>
            <param name='relationshipClass'>The relationship class of interest. </param>
            <param name='relationshipQualifier'>The qualifier required to be present on the relationship class. </param>
            <param name='relatedQualifier'>The qualifier required to be present on the related class. </param>
            <param name='relatedRole'>The role that the related class is playing in the relationship. </param>
            <param name='thisRole'>The role that this class is playing in the relationship. </param>
            <param name='classDefinitionsOnly'>Return only class definitions for the instances that match the query. </param>
            <param name='options'>Extended options for how to execute the query.</param>
            <remarks>
               <para>This operation is equivalent to an ASSOCIATORS OF query where ResultClass = &lt;relatedClass&gt;.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelationships">
            <overload>
               Gets a collection of associations to the object.
            </overload>
            <summary>
               <para>Gets a collection of associations to the object.</para>
            </summary>
            <returns>
            <para>A <see cref='T:System.Management.ManagementObjectCollection'/> containing the association objects.</para>
            </returns>
            <remarks>
               <para> The operation is equivalent to a REFERENCES OF query.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelationships(System.String)">
            <summary>
               <para>Gets a collection of associations to the object.</para>
            </summary>
            <param name='relationshipClass'>The associations to include. </param>
            <returns>
               A <see cref='T:System.Management.ManagementObjectCollection'/> containing the association objects.
            </returns>
            <remarks>
               <para>This operation is equivalent to a REFERENCES OF query where the AssocClass = &lt;relationshipClass&gt;.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelationships(System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
            <summary>
               <para>Gets a collection of associations to the object.</para>
            </summary>
            <param name='relationshipClass'>The type of relationship of interest. </param>
            <param name='relationshipQualifier'>The qualifier to be present on the relationship. </param>
            <param name='thisRole'>The role of this object in the relationship. </param>
            <param name='classDefinitionsOnly'>When this method returns, it contains only the class definitions for the result set. </param>
            <param name='options'>The extended options for the query execution. </param>
            <returns>
               A <see cref='T:System.Management.ManagementObjectCollection'/> containing the association objects.
            </returns>
            <remarks>
               <para>This operation is equivalent to a REFERENCES OF query with possibly all the extensions.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelationships(System.Management.ManagementOperationObserver)">
            <summary>
               <para>Gets a collection of associations to the object.</para>
            </summary>
            <param name='watcher'>The object to use to return results. </param>
            <remarks>
            This operation is equivalent to a REFERENCES OF query
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelationships(System.Management.ManagementOperationObserver,System.String)">
            <summary>
               <para>Gets a collection of associations to the object.</para>
            </summary>
            <param name='watcher'>The object to use to return results. </param>
            <param name='relationshipClass'>The associations to include. </param>
            <remarks>
               <para>This operation is equivalent to a REFERENCES OF query where the AssocClass = &lt;relationshipClass&gt;.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetRelationships(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
            <summary>
               <para>Gets a collection of associations to the object.</para>
            </summary>
            <param name='watcher'>The object to use to return results. </param>
            <param name='relationshipClass'>The type of relationship of interest. </param>
            <param name='relationshipQualifier'>The qualifier to be present on the relationship. </param>
            <param name='thisRole'>The role of this object in the relationship. </param>
            <param name='classDefinitionsOnly'>When this method returns, it contains only the class definitions for the result set. </param>
            <param name='options'>The extended options for the query execution. </param>
            <remarks>
               <para>This operation is equivalent to a REFERENCES OF query with possibly all the extensions.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.Put">
            <overload>
               Commits the changes to the object.
            </overload>
            <summary>
               <para>Commits the changes to the object.</para>
            </summary>
            <returns>
            <para>A <see cref='T:System.Management.ManagementPath'/> containing the path to the committed
               object.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.Put(System.Management.PutOptions)">
            <summary>
               <para>Commits the changes to the object.</para>
            </summary>
            <param name='options'>The options for how to commit the changes. </param>
            <returns>
               A <see cref='T:System.Management.ManagementPath'/> containing the path to the committed object.
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.Put(System.Management.ManagementOperationObserver)">
            <summary>
               <para>Commits the changes to the object, asynchronously.</para>
            </summary>
            <param name='watcher'>A <see cref='T:System.Management.ManagementOperationObserver'/> used to handle the progress and results of the asynchronous operation.</param>
        </member>
        <member name="M:System.Management.ManagementObject.Put(System.Management.ManagementOperationObserver,System.Management.PutOptions)">
            <summary>
               <para>Commits the changes to the object asynchronously and
                  using the specified options.</para>
            </summary>
            <param name='watcher'>A <see cref='T:System.Management.ManagementOperationObserver'/> used to handle the progress and results of the asynchronous operation.</param>
            <param name=' options'>A <see cref='T:System.Management.PutOptions'/> used to specify additional options for the commit operation.</param>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementPath)">
            <overload>
               Copies the object to a different location.
            </overload>
            <summary>
               <para>Copies the object to a different location.</para>
            </summary>
            <param name='path'>The <see cref='T:System.Management.ManagementPath'/> to which the object should be copied. </param>
            <returns>
               <para>The new path of the copied object.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.String)">
            <summary>
               <para>Copies the object to a different location.</para>
            </summary>
            <param name='path'>The path to which the object should be copied. </param>
            <returns>
               The new path of the copied object.
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.String,System.Management.PutOptions)">
            <summary>
               <para>Copies the object to a different location.</para>
            </summary>
            <param name='path'>The path to which the object should be copied.</param>
            <param name='options'>The options for how the object should be put.</param>
            <returns>
               The new path of the copied object.
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementPath,System.Management.PutOptions)">
            <summary>
               <para>Copies the object to a different location.</para>
            </summary>
            <param name='path'>The <see cref='T:System.Management.ManagementPath'/> to which the object should be copied.</param>
            <param name='options'>The options for how the object should be put.</param>
            <returns>
               The new path of the copied object.
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.Management.ManagementPath)">
            <summary>
               <para>Copies the object to a different location, asynchronously.</para>
            </summary>
            <param name='watcher'>The object that will receive the results of the operation.</param>
            <param name='path'>A <see cref='T:System.Management.ManagementPath'/> specifying the path to which the object should be copied.</param>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.String)">
            <summary>
               <para>Copies the object to a different location, asynchronously.</para>
            </summary>
            <param name='watcher'>The object that will receive the results of the operation.</param>
            <param name='path'> The path to which the object should be copied.</param>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.String,System.Management.PutOptions)">
            <summary>
               <para>Copies the object to a different location, asynchronously.</para>
            </summary>
            <param name='watcher'>The object that will receive the results of the operation.</param>
            <param name='path'>The path to which the object should be copied.</param>
            <param name='options'>The options for how the object should be put.</param>
        </member>
        <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.Management.ManagementPath,System.Management.PutOptions)">
            <summary>
               <para>Copies the object to a different location, asynchronously.</para>
            </summary>
            <param name='watcher'>The object that will receive the results of the operation.</param>
            <param name='path'>The path to which the object should be copied.</param>
            <param name='options'>The options for how the object should be put.</param>
        </member>
        <member name="M:System.Management.ManagementObject.Delete">
            <overload>
               Deletes the object.
            </overload>
            <summary>
               <para>Deletes the object.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementObject.Delete(System.Management.DeleteOptions)">
            <summary>
               <para>Deletes the object.</para>
            </summary>
            <param name='options'>The options for how to delete the object. </param>
        </member>
        <member name="M:System.Management.ManagementObject.Delete(System.Management.ManagementOperationObserver)">
            <summary>
               <para>Deletes the object.</para>
            </summary>
            <param name='watcher'>The object that will receive the results of the operation.</param>
        </member>
        <member name="M:System.Management.ManagementObject.Delete(System.Management.ManagementOperationObserver,System.Management.DeleteOptions)">
            <summary>
               <para>Deletes the object.</para>
            </summary>
            <param name='watcher'>The object that will receive the results of the operation.</param>
            <param name='options'>The options for how to delete the object.</param>
        </member>
        <member name="M:System.Management.ManagementObject.InvokeMethod(System.String,System.Object[])">
             <overload>
                <para>Invokes a method on the object.</para>
             </overload>
             <summary>
                <para>
                   Invokes a method on the object.</para>
             </summary>
             <param name='methodName'>The name of the method to execute. </param>
             <param name='args'>An array containing parameter values. </param>
             <returns>
                <para>The value returned by the method.</para>
             </returns>
             <remarks>
                <para>If the method is static, the execution
                   should still succeed.</para>
             </remarks>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates invoking a WMI method using an array of arguments.
             public class InvokeMethod
             {
                 public static void Main()
                 {
            
                     //Get the object on which the method will be invoked
                     ManagementClass processClass = new ManagementClass("Win32_Process");
            
                     //Create an array containing all arguments for the method
                     object[] methodArgs = {"notepad.exe", null, null, 0};
            
                     //Execute the method
                     object result = processClass.InvokeMethod ("Create", methodArgs);
            
                     //Display results
                     Console.WriteLine ("Creation of process returned: " + result);
                     Console.WriteLine ("Process id: " + methodArgs[3]);
                 }
            
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates invoking a WMI method using an array of arguments.
             Class InvokeMethod
                 Public Overloads Shared Function Main(ByVal args() As String) As Integer
            
                     ' Get the object on which the method will be invoked
                     Dim processClass As New ManagementClass("Win32_Process")
            
                     ' Create an array containing all arguments for the method
                     Dim methodArgs() As Object = {"notepad.exe", Nothing, Nothing, 0}
            
                     ' Execute the method
                     Dim result As Object = processClass.InvokeMethod("Create", methodArgs)
            
                     'Display results
                     Console.WriteLine("Creation of process returned: {0}", result)
                     Console.WriteLine("Process id: {0}", methodArgs(3))
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.InvokeMethod(System.Management.ManagementOperationObserver,System.String,System.Object[])">
            <summary>
               <para>Invokes a method on the object, asynchronously.</para>
            </summary>
            <param name='watcher'>The object to receive the results of the operation.</param>
            <param name='methodName'>The name of the method to execute. </param>
            <param name='args'>An array containing parameter values. </param>
            <remarks>
               <para>If the method is static, the execution
                  should still succeed.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.InvokeMethod(System.String,System.Management.ManagementBaseObject,System.Management.InvokeMethodOptions)">
             <summary>
                <para>Invokes a method on the WMI object. The input and output
                   parameters are represented as <see cref='T:System.Management.ManagementBaseObject'/>
                   objects.</para>
             </summary>
             <param name='methodName'>The name of the method to execute.</param>
             <param name=' inParameters'>A <see cref='T:System.Management.ManagementBaseObject'/> holding the input parameters to the method.</param>
             <param name=' options'>An <see cref='T:System.Management.InvokeMethodOptions'/> containing additional options for the execution of the method.</param>
             <returns>
             <para>A <see cref='T:System.Management.ManagementBaseObject'/> containing the
                output parameters and return value of the executed method.</para>
             </returns>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates invoking a WMI method using parameter objects
             public class InvokeMethod
             {
                 public static void Main()
                 {
            
                     //Get the object on which the method will be invoked
                     ManagementClass processClass = new ManagementClass("Win32_Process");
            
                     //Get an input parameters object for this method
                     ManagementBaseObject inParams = processClass.GetMethodParameters("Create");
            
                     //Fill in input parameter values
                     inParams["CommandLine"] = "calc.exe";
            
                     //Execute the method
                     ManagementBaseObject outParams = processClass.InvokeMethod ("Create", inParams, null);
            
                     //Display results
                     //Note: The return code of the method is provided in the "returnValue" property of the outParams object
                     Console.WriteLine("Creation of calculator process returned: " + outParams["returnValue"]);
                     Console.WriteLine("Process ID: " + outParams["processId"]);
                }
             }
                </code>
                <code lang='VB'>
             Imports System
             Imports System.Management
            
             ' This sample demonstrates invoking a WMI method using parameter objects
             Class InvokeMethod
                 Public Overloads Shared Function Main(ByVal args() As String) As Integer
            
                     ' Get the object on which the method will be invoked
                     Dim processClass As New ManagementClass("Win32_Process")
            
                      ' Get an input parameters object for this method
                     Dim inParams As ManagementBaseObject = processClass.GetMethodParameters("Create")
            
                     ' Fill in input parameter values
                     inParams("CommandLine") = "calc.exe"
            
                     ' Execute the method
                     Dim outParams As ManagementBaseObject = processClass.InvokeMethod("Create", inParams, Nothing)
            
                     ' Display results
                     ' Note: The return code of the method is provided in the "returnValue" property of the outParams object
                     Console.WriteLine("Creation of calculator process returned: {0}", outParams("returnValue"))
                     Console.WriteLine("Process ID: {0}", outParams("processId"))
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObject.InvokeMethod(System.Management.ManagementOperationObserver,System.String,System.Management.ManagementBaseObject,System.Management.InvokeMethodOptions)">
            <summary>
               <para>Invokes a method on the object, asynchronously.</para>
            </summary>
            <param name='watcher'>A <see cref='T:System.Management.ManagementOperationObserver'/> used to handle the asynchronous execution's progress and results.</param>
            <param name=' methodName'>The name of the method to be executed.</param>
            <param name=' inParameters'><para>A <see cref='T:System.Management.ManagementBaseObject'/> containing the input parameters for the method.</para></param>
            <param name=' options'>An <see cref='T:System.Management.InvokeMethodOptions'/> containing additional options used to execute the method.</param>
            <remarks>
               <para>The method invokes the specified method execution and then
                  returns. Progress and results are reported through events on the <see cref='T:System.Management.ManagementOperationObserver'/>.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.GetMethodParameters(System.String)">
            <summary>
            <para>Returns a <see cref='T:System.Management.ManagementBaseObject'/> representing the list of input parameters for a method.</para>
            </summary>
            <param name='methodName'>The name of the method. </param>
            <returns>
            <para>A <see cref='T:System.Management.ManagementBaseObject'/> containing the
               input parameters to the method.</para>
            </returns>
            <remarks>
               <para> Gets the object containing the input parameters to a
                  method, and then fills in the values and passes the object to the <see cref='M:System.Management.ManagementObject.InvokeMethod(System.String,System.Management.ManagementBaseObject,System.Management.InvokeMethodOptions)'/> call.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementObject.Clone">
            <summary>
               <para>Creates a copy of the object.</para>
            </summary>
            <returns>
               <para>The copied object.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementObject.ToString">
            <summary>
               <para>Returns the full path of the object. This is an override of the
                  default object implementation.</para>
            </summary>
            <returns>
               <para> The full path of
                  the object.</para>
            </returns>
        </member>
        <member name="T:System.Management.ManagementObjectCollection">
             <summary>
                <para> Represents different collections of management objects
                   retrieved through WMI. The objects in this collection are of <see cref='T:System.Management.ManagementBaseObject'/>-derived types, including <see cref='T:System.Management.ManagementObject'/> and <see cref='T:System.Management.ManagementClass'/>
                   .</para>
                <para> The collection can be the result of a WMI
                   query executed through a <see cref='T:System.Management.ManagementObjectSearcher'/> object, or an enumeration of
                   management objects of a specified type retrieved through a <see cref='T:System.Management.ManagementClass'/> representing that type.
                   In addition, this can be a collection of management objects related in a specified
                   way to a specific management object - in this case the collection would
                   be retrieved through a method such as <see cref='M:System.Management.ManagementObject.GetRelated'/>.</para>
             <para>The collection can be walked using the <see cref='T:System.Management.ManagementObjectCollection.ManagementObjectEnumerator'/> and objects in it can be inspected or
                manipulated for various management tasks.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to enumerate instances of a ManagementClass object.
             class Sample_ManagementObjectCollection
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("Win32_LogicalDisk");
                     ManagementObjectCollection disks = diskClass.GetInstances();
                     foreach (ManagementObject disk in disks) {
                         Console.WriteLine("Disk = " + disk["deviceid"]);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to enumerate instances of a ManagementClass object.
             Class Sample_ManagementObjectCollection
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim diskClass As New ManagementClass("Win32_LogicalDisk")
                     Dim disks As ManagementObjectCollection = diskClass.GetInstances()
                     Dim disk As ManagementObject
                     For Each disk In disks
                         Console.WriteLine("Disk = " &amp; disk("deviceid").ToString())
                     Next disk
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.Finalize">
            <summary>
            <para>Disposes of resources the object is holding. This is the destructor for the object.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.Dispose">
            <summary>
            Releases resources associated with this object. After this
            method has been called, an attempt to use this object will
            result in an ObjectDisposedException being thrown.
            </summary>
        </member>
        <member name="P:System.Management.ManagementObjectCollection.Count">
            <summary>
               <para>Represents the number of objects in the collection.</para>
            </summary>
            <value>
               <para>The number of objects in the collection.</para>
            </value>
            <remarks>
               <para>This property is very expensive - it requires that
               all members of the collection be enumerated.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ManagementObjectCollection.IsSynchronized">
            <summary>
               <para>Represents whether the object is synchronized.</para>
            </summary>
            <value>
            <para><see langword='true'/>, if the object is synchronized;
               otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementObjectCollection.SyncRoot">
            <summary>
               <para>Represents the object to be used for synchronization.</para>
            </summary>
            <value>
               <para> The object to be used for synchronization.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.CopyTo(System.Array,System.Int32)">
            <overload>
               Copies the collection to an array.
            </overload>
            <summary>
               <para> Copies the collection to an array.</para>
            </summary>
            <param name='array'>An array to copy to. </param>
            <param name='index'>The index to start from. </param>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.CopyTo(System.Management.ManagementBaseObject[],System.Int32)">
            <summary>
            <para>Copies the items in the collection to a <see cref='T:System.Management.ManagementBaseObject'/>
            array.</para>
            </summary>
            <param name='objectCollection'>The target array.</param>
            <param name=' index'>The index to start from.</param>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.GetEnumerator">
            <summary>
               <para>Returns the enumerator for the collection. If the collection was retrieved from an operation that
            specified the EnumerationOptions.Rewindable = false only one iteration through this enumerator is allowed.
            Note that this applies to using the Count property of the collection as well since an iteration over the collection
            is required. Due to this, code using the Count property should never specify EnumerationOptions.Rewindable = false.
            </para>
            </summary>
            <returns>
               An <see cref='T:System.Collections.IEnumerator'/>that can be used to iterate through the
               collection.
            </returns>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.System#Collections#IEnumerable#GetEnumerator">
            <internalonly/>
            <summary>
               <para>Returns an enumerator that can iterate through a collection.</para>
            </summary>
            <returns>
               An <see cref='T:System.Collections.IEnumerator'/> that can be used to iterate
               through the collection.
            </returns>
        </member>
        <member name="T:System.Management.ManagementObjectCollection.ManagementObjectEnumerator">
             <summary>
                <para>Represents the enumerator on the collection.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to enumerate all logical disks
             // using the ManagementObjectEnumerator object.
             class Sample_ManagementObjectEnumerator
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("Win32_LogicalDisk");
                     ManagementObjectCollection disks = diskClass.GetInstances();
                     ManagementObjectCollection.ManagementObjectEnumerator disksEnumerator =
                         disks.GetEnumerator();
                     while(disksEnumerator.MoveNext()) {
                         ManagementObject disk = (ManagementObject)disksEnumerator.Current;
                        Console.WriteLine("Disk found: " + disk["deviceid"]);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
                   Imports System.Management
                   ' This sample demonstrates how to enumerate all logical disks
                   ' using ManagementObjectEnumerator object.
                   Class Sample_ManagementObjectEnumerator
                   Overloads Public Shared Function Main(args() As String) As Integer
                   Dim diskClass As New ManagementClass("Win32_LogicalDisk")
                   Dim disks As ManagementObjectCollection = diskClass.GetInstances()
                   Dim disksEnumerator As _
                   ManagementObjectCollection.ManagementObjectEnumerator = _
                   disks.GetEnumerator()
                   While disksEnumerator.MoveNext()
                   Dim disk As ManagementObject = _
                   CType(disksEnumerator.Current, ManagementObject)
                   Console.WriteLine("Disk found: " &amp; disk("deviceid"))
                   End While
                   Return 0
                   End Function
                   End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Finalize">
            <summary>
            <para>Disposes of resources the object is holding. This is the destructor for the object.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Dispose">
            <summary>
            Releases resources associated with this object. After this
            method has been called, an attempt to use this object will
            result in an ObjectDisposedException being thrown.
            </summary>
        </member>
        <member name="P:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Current">
            <summary>
            <para>Gets the current <see cref='T:System.Management.ManagementBaseObject'/> that this enumerator points
               to.</para>
            </summary>
            <value>
               <para>The current object in the enumeration.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.System#Collections#IEnumerator#Current">
            <internalonly/>
            <summary>
               <para>Returns the current object in the enumeration.</para>
            </summary>
            <value>
               <para>The current object in the enumeration.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.MoveNext">
            <summary>
               Indicates whether the enumerator has moved to
               the next object in the enumeration.
            </summary>
            <returns>
            <para><see langword='true'/>, if the enumerator was
               successfully advanced to the next element; <see langword='false'/> if the enumerator has
               passed the end of the collection.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Reset">
            <summary>
               <para>Resets the enumerator to the beginning of the collection.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementObjectSearcher">
             <summary>
                <para> Retrieves a collection of management objects based
                   on a specified query.</para>
                <para>This class is one of the more commonly used entry points to retrieving
                   management information. For example, it can be used to enumerate all disk
                   drives, network adapters, processes and many more management objects on a
                   system, or to query for all network connections that are up, services that are
                   paused etc. </para>
                <para>When instantiated, an instance of this class takes as input a WMI
                   query represented in an <see cref='T:System.Management.ObjectQuery'/> or it's derivatives, and optionally a <see cref='T:System.Management.ManagementScope'/> representing the WMI namespace
                   to execute the query in. It can also take additional advanced
                   options in an <see cref='T:System.Management.EnumerationOptions'/> object. When the Get() method on this object
                   is invoked, the ManagementObjectSearcher executes the given query in the
                   specified scope and returns a collection of management objects that match the
                   query in a <see cref='T:System.Management.ManagementObjectCollection'/>.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates perform a query using
             // ManagementObjectSearcher object.
             class Sample_ManagementObjectSearcher
             {
                 public static int Main(string[] args) {
                     ManagementObjectSearcher searcher = new
                         ManagementObjectSearcher("select * from win32_share");
                     foreach (ManagementObject share in searcher.Get()) {
                         Console.WriteLine("Share = " + share["Name"]);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates perform a query using
             ' ManagementObjectSearcher object.
             Class Sample_ManagementObjectSearcher
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_Share")
                     Dim share As ManagementObject
                     For Each share In searcher.Get()
                         Console.WriteLine("Share = " &amp; share("Name").ToString())
                     Next share
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor">
            <overload>
               Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class.
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class. After some properties on
               this object are set, the object can be used to invoke a query for management information. This is the default
               constructor.</para>
            </summary>
            <example>
               <code lang='C#'>ManagementObjectSearcher s = new ManagementObjectSearcher();
               </code>
               <code lang='VB'>Dim s As New ManagementObjectSearcher()
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class used
               to invoke the specified query for management information.</para>
            </summary>
            <param name='queryString'>The WMI query to be invoked by the object.</param>
            <example>
               <code lang='C#'>ManagementObjectSearcher s =
                new ManagementObjectSearcher("SELECT * FROM Win32_Service");
               </code>
               <code lang='VB'>Dim s As New ManagementObjectSearcher("SELECT * FROM Win32_Service")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.Management.ObjectQuery)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class used to invoke the
               specified query for management information.</para>
            </summary>
            <param name='query'>An <see cref='T:System.Management.ObjectQuery'/> representing the query to be invoked by the searcher.</param>
            <example>
               <code lang='C#'>SelectQuery q = new SelectQuery("Win32_Service", "State='Running'");
            ManagementObjectSearcher s = new ManagementObjectSearcher(q);
               </code>
               <code lang='VB'>Dim q As New SelectQuery("Win32_Service", "State=""Running""")
            Dim s As New ManagementObjectSearcher(q)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.String,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class used to invoke the
               specified query in the specified scope.</para>
            </summary>
            <param name='scope'>The scope in which to query.</param>
            <param name=' queryString'>The query to be invoked.</param>
            <remarks>
            <para>If no scope is specified, the default scope (<see cref='P:System.Management.ManagementPath.DefaultPath'/>) is used.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementObjectSearcher s = new ManagementObjectSearcher(
                                           "root\\MyApp",
                                           "SELECT * FROM MyClass WHERE MyProp=5");
               </code>
               <code lang='VB'>Dim s As New ManagementObjectSearcher( _
                                           "root\MyApp", _
                                           "SELECT * FROM MyClass WHERE MyProp=5")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.Management.ManagementScope,System.Management.ObjectQuery)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class used to invoke the
               specified query in the specified scope.</para>
            </summary>
            <param name='scope'>A <see cref='T:System.Management.ManagementScope'/> representing the scope in which to invoke the query.</param>
            <param name=' query'>An <see cref='T:System.Management.ObjectQuery'/> representing the query to be invoked.</param>
            <remarks>
            <para>If no scope is specified, the default scope (<see cref='P:System.Management.ManagementPath.DefaultPath'/>) is
               used.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementScope myScope = new ManagementScope("root\\MyApp");
            SelectQuery q = new SelectQuery("Win32_Environment", "User=&lt;system&gt;");
            ManagementObjectSearcher s = new ManagementObjectSearcher(myScope,q);
               </code>
               <code lang='VB'>Dim myScope As New ManagementScope("root\MyApp")
            Dim q As New SelectQuery("Win32_Environment", "User=&lt;system&gt;")
            Dim s As New ManagementObjectSearcher(myScope,q)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.String,System.String,System.Management.EnumerationOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class used to invoke the specified
               query, in the specified scope, and with the specified options.</para>
            </summary>
            <param name='scope'>The scope in which the query should be invoked.</param>
            <param name=' queryString'>The query to be invoked.</param>
            <param name=' options'>An <see cref='T:System.Management.EnumerationOptions'/> specifying additional options for the query.</param>
            <example>
               <code lang='C#'>ManagementObjectSearcher s = new ManagementObjectSearcher(
                "root\\MyApp",
                "SELECT * FROM MyClass",
                new EnumerationOptions(null, InfiniteTimeout, 1, true, false, true);
               </code>
               <code lang='VB'>Dim s As New ManagementObjectSearcher( _
                "root\MyApp", _
                "SELECT * FROM MyClass", _
                New EnumerationOptions(Null, InfiniteTimeout, 1, True, False, True)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.Management.ManagementScope,System.Management.ObjectQuery,System.Management.EnumerationOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementObjectSearcher'/> class to be
               used to invoke the specified query in the specified scope, with the specified
               options.</para>
            </summary>
            <param name='scope'>A <see cref='T:System.Management.ManagementScope'/> specifying the scope of the query</param>
            <param name=' query'>An <see cref='T:System.Management.ObjectQuery'/> specifying the query to be invoked</param>
            <param name=' options'>An <see cref='T:System.Management.EnumerationOptions'/> specifying additional options to be used for the query.</param>
            <example>
               <code lang='C#'>ManagementScope scope = new ManagementScope("root\\MyApp");
            SelectQuery q = new SelectQuery("SELECT * FROM MyClass");
            EnumerationOptions o = new EnumerationOptions(null, InfiniteTimeout, 1, true, false, true);
            ManagementObjectSearcher s = new ManagementObjectSearcher(scope, q, o);
               </code>
               <code lang='VB'>Dim scope As New ManagementScope("root\MyApp")
            Dim q As New SelectQuery("SELECT * FROM MyClass")
            Dim o As New EnumerationOptions(Null, InfiniteTimeout, 1, True, False, True)
            Dim s As New ManagementObjectSearcher(scope, q, o)
               </code>
            </example>
        </member>
        <member name="P:System.Management.ManagementObjectSearcher.Scope">
            <summary>
               <para>Gets or sets the scope in which to look for objects (the scope represents a WMI namespace).</para>
            </summary>
            <value>
               <para> The scope (namespace) in which to look for objects.</para>
            </value>
            <remarks>
               <para>When the value of this property is changed,
                  the <see cref='T:System.Management.ManagementObjectSearcher'/>
                  is re-bound to the new scope.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementObjectSearcher s = new ManagementObjectSearcher();
            s.Scope = new ManagementScope("root\\MyApp");
               </code>
               <code lang='VB'>Dim s As New ManagementObjectSearcher()
            Dim ms As New ManagementScope ("root\MyApp")
            s.Scope = ms
               </code>
            </example>
        </member>
        <member name="P:System.Management.ManagementObjectSearcher.Query">
            <summary>
               <para> Gets or sets the query to be invoked in the
                  searcher (that is, the criteria to be applied to the search for management objects).</para>
            </summary>
            <value>
               <para> The criteria to apply to the query.</para>
            </value>
            <remarks>
            <para>When the value of this property is changed, the <see cref='T:System.Management.ManagementObjectSearcher'/>
            is reset to use the new query.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ManagementObjectSearcher.Options">
            <summary>
               <para>Gets or sets the options for how to search for objects.</para>
            </summary>
            <value>
               <para>The options for how to search for objects.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.Get">
            <overload>
               Invokes the specified WMI query and returns the resulting collection.
            </overload>
            <summary>
               <para>Invokes the specified WMI query and returns the
                  resulting collection.</para>
            </summary>
            <returns>
            <para>A <see cref='T:System.Management.ManagementObjectCollection'/> containing the objects that match the
               specified query.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementObjectSearcher.Get(System.Management.ManagementOperationObserver)">
            <summary>
               <para>Invokes the WMI query, asynchronously, and binds to a watcher to deliver the results.</para>
            </summary>
            <param name='watcher'>The watcher that raises events triggered by the operation. </param>
        </member>
        <member name="T:System.Management.ObjectReadyEventHandler">
            <summary>
            <para>Represents the method that will handle the <see cref='E:System.Management.ManagementOperationObserver.ObjectReady'/> event.</para>
            </summary>
        </member>
        <member name="T:System.Management.CompletedEventHandler">
            <summary>
            <para>Represents the method that will handle the <see cref='E:System.Management.ManagementOperationObserver.Completed'/> event.</para>
            </summary>
        </member>
        <member name="T:System.Management.ProgressEventHandler">
            <summary>
            <para>Represents the method that will handle the <see cref='E:System.Management.ManagementOperationObserver.Progress'/> event.</para>
            </summary>
        </member>
        <member name="T:System.Management.ObjectPutEventHandler">
            <summary>
            <para>Represents the method that will handle the <see cref='E:System.Management.ManagementOperationObserver.ObjectPut'/> event.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementOperationObserver">
             <summary>
                <para>Used to manage asynchronous operations and handle management information and events received asynchronously.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to read a ManagementObject asychronously
             // using the ManagementOperationObserver object.
            
             class Sample_ManagementOperationObserver {
                 public static int Main(string[] args) {
            
                     //Set up a handler for the asynchronous callback
                     ManagementOperationObserver observer = new ManagementOperationObserver();
                     MyHandler completionHandler = new MyHandler();
                     observer.Completed += new CompletedEventHandler(completionHandler.Done);
            
                     //Invoke the asynchronous read of the object
                     ManagementObject disk = new ManagementObject("Win32_logicaldisk='C:'");
                     disk.Get(observer);
            
                     //For the purpose of this sample, we keep the main
                     // thread alive until the asynchronous operation is completed.
            
                     while (!completionHandler.IsComplete) {
                         System.Threading.Thread.Sleep(500);
                     }
            
                     Console.WriteLine("Size= " + disk["Size"] + " bytes.");
            
                     return 0;
                 }
            
                 public class MyHandler
                 {
                     private bool isComplete = false;
            
                     public void Done(object sender, CompletedEventArgs e) {
                         isComplete = true;
                     }
            
                     public bool IsComplete {
                         get {
                             return isComplete;
                         }
                     }
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to read a ManagementObject asychronously
             ' using the ManagementOperationObserver object.
            
             Class Sample_ManagementOperationObserver
                 Overloads Public Shared Function Main(args() As String) As Integer
            
                     'Set up a handler for the asynchronous callback
                     Dim observer As New ManagementOperationObserver()
                     Dim completionHandler As New MyHandler()
                     AddHandler observer.Completed, AddressOf completionHandler.Done
            
                     ' Invoke the object read asynchronously
                     Dim disk As New ManagementObject("Win32_logicaldisk='C:'")
                     disk.Get(observer)
            
                     ' For the purpose of this sample, we keep the main
                     ' thread alive until the asynchronous operation is finished.
                     While Not completionHandler.IsComplete Then
                         System.Threading.Thread.Sleep(500)
                     End While
            
                     Console.WriteLine("Size = " + disk("Size").ToString() &amp; " bytes")
            
                     Return 0
                 End Function
            
                 Public Class MyHandler
                     Private _isComplete As Boolean = False
            
                     Public Sub Done(sender As Object, e As CompletedEventArgs)
                         _isComplete = True
                     End Sub 'Done
            
                     Public ReadOnly Property IsComplete() As Boolean
                         Get
                             Return _isComplete
                         End Get
                     End Property
                 End Class
             End Class
                </code>
             </example>
        </member>
        <member name="E:System.Management.ManagementOperationObserver.ObjectReady">
            <summary>
               <para> Occurs when a new object is available.</para>
            </summary>
        </member>
        <member name="E:System.Management.ManagementOperationObserver.Completed">
            <summary>
               <para> Occurs when an operation has completed.</para>
            </summary>
        </member>
        <member name="E:System.Management.ManagementOperationObserver.Progress">
            <summary>
               <para> Occurs to indicate the progress of an ongoing operation.</para>
            </summary>
        </member>
        <member name="E:System.Management.ManagementOperationObserver.ObjectPut">
            <summary>
               <para> Occurs when an object has been successfully committed.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementOperationObserver.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementOperationObserver'/> class. This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementOperationObserver.Cancel">
            <summary>
               <para> Cancels all outstanding operations.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementOperationObserver.FireObjectReady(System.Management.ObjectReadyEventArgs)">
            <summary>
            Fires the ObjectReady event to whomsoever is listening
            </summary>
            <param name="args"> </param>
        </member>
        <member name="T:System.Management.WmiDelegateInvoker">
            <summary>
            This class handles the posting of events to delegates. For each event
            it queues a set of requests (one per target delegate) to the thread pool
            to handle the event. It ensures that no single delegate can throw
            an exception that prevents the event from reaching any other delegates.
            It also ensures that the sender does not signal the processing of the
            WMI event as "done" until all target delegates have signalled that they are
            done.
            </summary>
        </member>
        <member name="M:System.Management.WmiDelegateInvoker.FireEventToDelegates(System.MulticastDelegate,System.Management.ManagementEventArgs)">
            <summary>
            Custom handler for firing a WMI event to a list of delegates. We use
            the process thread pool to handle the firing.
            </summary>
            <param name="md">The MulticastDelegate representing the collection
            of targets for the event</param>
            <param name="args">The accompanying event arguments</param>
        </member>
        <member name="T:System.Management.AuthenticationLevel">
            <summary>
               <para>Describes the authentication level to be used to connect to WMI. This is used for the COM connection to WMI.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.Default">
            <summary>
               <para>The default COM authentication level. WMI uses the default Windows Authentication setting.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.None">
            <summary>
               <para> No COM authentication.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.Connect">
            <summary>
               <para> Connect-level COM authentication.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.Call">
            <summary>
               <para> Call-level COM authentication.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.Packet">
            <summary>
               <para> Packet-level COM authentication.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.PacketIntegrity">
            <summary>
               <para>Packet Integrity-level COM authentication.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.PacketPrivacy">
            <summary>
               <para>Packet Privacy-level COM authentication.</para>
            </summary>
        </member>
        <member name="F:System.Management.AuthenticationLevel.Unchanged">
            <summary>
               <para>The default COM authentication level. WMI uses the default Windows Authentication setting.</para>
            </summary>
        </member>
        <member name="T:System.Management.ImpersonationLevel">
            <summary>
               <para>Describes the impersonation level to be used to connect to WMI.</para>
            </summary>
        </member>
        <member name="F:System.Management.ImpersonationLevel.Default">
            <summary>
               <para>Default impersonation.</para>
            </summary>
        </member>
        <member name="F:System.Management.ImpersonationLevel.Anonymous">
            <summary>
               <para> Anonymous COM impersonation level that hides the
                  identity of the caller. Calls to WMI may fail
                  with this impersonation level.</para>
            </summary>
        </member>
        <member name="F:System.Management.ImpersonationLevel.Identify">
            <summary>
               <para> Identify-level COM impersonation level that allows objects
                  to query the credentials of the caller. Calls to
                  WMI may fail with this impersonation level.</para>
            </summary>
        </member>
        <member name="F:System.Management.ImpersonationLevel.Impersonate">
            <summary>
               <para> Impersonate-level COM impersonation level that allows
                  objects to use the credentials of the caller. This is the recommended impersonation level for WMI calls.</para>
            </summary>
        </member>
        <member name="F:System.Management.ImpersonationLevel.Delegate">
            <summary>
               <para> Delegate-level COM impersonation level that allows objects
                  to permit other objects to use the credentials of the caller. This
                  level, which will work with WMI calls but may constitute an unnecessary
                  security risk, is supported only under Windows 2000.</para>
            </summary>
        </member>
        <member name="T:System.Management.PutType">
            <summary>
               <para>Describes the possible effects of saving an object to WMI when
                  using <see cref='M:System.Management.ManagementObject.Put'/>.</para>
            </summary>
        </member>
        <member name="F:System.Management.PutType.None">
            <summary>
               <para> Invalid Type </para>
            </summary>
        </member>
        <member name="F:System.Management.PutType.UpdateOnly">
            <summary>
               <para> Updates an existing object
                  only; does not create a new object.</para>
            </summary>
        </member>
        <member name="F:System.Management.PutType.CreateOnly">
            <summary>
               <para> Creates an object only;
                  does not update an existing object.</para>
            </summary>
        </member>
        <member name="F:System.Management.PutType.UpdateOrCreate">
            <summary>
               <para> Saves the object, whether
                  updating an existing object or creating a new object.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementOptions">
            <summary>
               <para>
                  Provides an abstract base class for all Options objects.</para>
               <para>Options objects are used to customize different management operations. </para>
               <para>Use one of the Options classes derived from this class, as
                  indicated by the signature of the operation being performed.</para>
            </summary>
        </member>
        <member name="F:System.Management.ManagementOptions.InfiniteTimeout">
            <summary>
               <para> Specifies an infinite timeout.</para>
            </summary>
        </member>
        <member name="P:System.Management.ManagementOptions.Context">
            <summary>
               <para> Gets or sets a WMI context object. This is a
                  name-value pairs list to be passed through to a WMI provider that supports
                  context information for customized operation.</para>
            </summary>
            <value>
               <para>A name-value pairs list to be passed through to a WMI provider that
                  supports context information for customized operation.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementOptions.Timeout">
            <summary>
               <para>Gets or sets the timeout to apply to the operation.
                  Note that for operations that return collections, this timeout applies to the
                  enumeration through the resulting collection, not the operation itself
                  (the <see cref='P:System.Management.EnumerationOptions.ReturnImmediately'/>
                  property is used for the latter).</para>
               This property is used to indicate that the operation should be performed semisynchronously.
            </summary>
            <value>
            <para>The default value for this property is <see cref='F:System.Management.ManagementOptions.InfiniteTimeout'/>
            , which means the operation will block.
            The value specified must be positive.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.EnumerationOptions">
             <summary>
                <para>Provides a base class for query and enumeration-related options
                   objects.</para>
                <para>Use this class to customize enumeration of management
                   objects, traverse management object relationships, or query for
                   management objects.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to enumerate all top-level WMI classes
             // and subclasses in root/cimv2 namespace.
             class Sample_EnumerationOptions
             {
                 public static int Main(string[] args) {
                     ManagementClass newClass = new ManagementClass();
                     EnumerationOptions options = new EnumerationOptions();
                     options.EnumerateDeep = false;
                     foreach (ManagementObject o in newClass.GetSubclasses(options)) {
                         Console.WriteLine(o["__Class"]);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to enumerate all top-level WMI classes
             ' and subclasses in root/cimv2 namespace.
             Class Sample_EnumerationOptions
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim newClass As New ManagementClass()
                     Dim options As New EnumerationOptions()
                     options.EnumerateDeep = False
                     Dim o As ManagementObject
                     For Each o In newClass.GetSubclasses(options)
                         Console.WriteLine(o("__Class"))
                     Next o
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.EnumerationOptions.ReturnImmediately">
            <summary>
               <para>Gets or sets a value indicating whether the invoked operation should be
                  performed in a synchronous or semisynchronous fashion. If this property is set
                  to <see langword='true'/>, the enumeration is invoked and the call returns immediately. The actual
                  retrieval of the results will occur when the resulting collection is walked.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the invoked operation should
               be performed in a synchronous or semisynchronous fashion; otherwise,
            <see langword='false'/>. The default value is <see langword='true'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.EnumerationOptions.BlockSize">
            <summary>
               <para> Gets or sets the block size
                  for block operations. When enumerating through a collection, WMI will return results in
                  groups of the specified size.</para>
            </summary>
            <value>
               <para>The default value is 1.</para>
            </value>
        </member>
        <member name="P:System.Management.EnumerationOptions.Rewindable">
            <summary>
               <para>Gets or sets a value indicating whether the collection is assumed to be
                  rewindable. If <see langword='true'/>, the objects in the
                  collection will be kept available for multiple enumerations. If
               <see langword='false'/>, the collection
                  can only be enumerated one time.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the collection is assumed to
               be rewindable; otherwise, <see langword='false'/>. The default value is
            <see langword='true'/>.</para>
            </value>
            <remarks>
               <para>A rewindable collection is more costly in memory
                  consumption as all the objects need to be kept available at the same time.
                  In a collection defined as non-rewindable, the objects are discarded after being returned
                  in the enumeration.</para>
            </remarks>
        </member>
        <member name="P:System.Management.EnumerationOptions.UseAmendedQualifiers">
            <summary>
               <para> Gets or sets a value indicating whether the objects returned from
                  WMI should contain amended information. Typically, amended information is localizable
                  information attached to the WMI object, such as object and property
                  descriptions.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the objects returned from WMI
               should contain amended information; otherwise, <see langword='false'/>. The
               default value is <see langword='false'/>.</para>
            </value>
            <remarks>
               <para>If descriptions and other amended information are not of
                  interest, setting this property to <see langword='false'/>
                  is more
                  efficient.</para>
            </remarks>
        </member>
        <member name="P:System.Management.EnumerationOptions.EnsureLocatable">
            <summary>
               <para>Gets or sets a value indicating whether to the objects returned should have
                  locatable information in them. This ensures that the system properties, such as
               <see langword='__PATH'/>, <see langword='__RELPATH'/>, and
               <see langword='__SERVER'/>, are non-NULL. This flag can only be used in queries,
                  and is ignored in enumerations.</para>
            </summary>
            <value>
            <para><see langword='true'/> if WMI
               should ensure all returned objects have valid paths; otherwise,
            <see langword='false'/>. The default value is <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.EnumerationOptions.PrototypeOnly">
            <summary>
               <para>Gets or sets a value indicating whether the query should return a
                  prototype of the result set instead of the actual results. This flag is used for
                  prototyping.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the
               query should return a prototype of the result set instead of the actual results;
               otherwise, <see langword='false'/>. The default value is
            <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.EnumerationOptions.DirectRead">
            <summary>
               <para> Gets or sets a value indicating whether direct access to the WMI provider is requested for the specified class,
                  without any regard to its base class or derived classes.</para>
            </summary>
            <value>
            <para><see langword='true'/> if only
               objects of the specified class should be received, without regard to derivation
               or inheritance; otherwise, <see langword='false'/>. The default value is
            <see langword='false'/>. </para>
            </value>
        </member>
        <member name="P:System.Management.EnumerationOptions.EnumerateDeep">
            <summary>
               <para> Gets or sets a value indicating whether recursive enumeration is requested
                  into all classes derived from the specified base class. If
               <see langword='false'/>, only immediate derived
                  class members are returned.</para>
            </summary>
            <value>
            <para><see langword='true'/> if recursive enumeration is requested
               into all classes derived from the specified base class; otherwise,
            <see langword='false'/>. The default value is <see langword='false'/>.</para>
            </value>
        </member>
        <member name="M:System.Management.EnumerationOptions.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.EnumerationOptions'/> class.
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.EnumerationOptions'/>
            class with default values (see the individual property descriptions
            for what the default values are). This is the default constructor. </para>
            </summary>
        </member>
        <member name="M:System.Management.EnumerationOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Int32,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.EnumerationOptions'/> class to be used for queries or enumerations,
               allowing the user to specify values for the different options.</para>
            </summary>
            <param name='context'>The options context object containing provider-specific information that can be passed through to the provider.</param>
            <param name=' timeout'>The timeout value for enumerating through the results.</param>
            <param name=' blockSize'>The number of items to retrieve at one time from WMI.</param>
            <param name=' rewindable'><see langword='true'/> to specify whether the result set is rewindable (=allows multiple traversal or one-time); otherwise, <see langword='false'/>.</param>
            <param name=' returnImmediatley'><see langword='true'/> to specify whether the operation should return immediately (semi-sync) or block until all results are available; otherwise, <see langword='false'/> .</param>
            <param name=' useAmendedQualifiers'><see langword='true'/> to specify whether the returned objects should contain amended (locale-aware) qualifiers; otherwise, <see langword='false'/> .</param>
            <param name=' ensureLocatable'><see langword='true'/> to specify to WMI that it should ensure all returned objects have valid paths; otherwise, <see langword='false'/> .</param>
            <param name=' prototypeOnly'><see langword='true'/> to return a prototype of the result set instead of the actual results; otherwise, <see langword='false'/> .</param>
            <param name=' directRead'><see langword='true'/> to retrieve objects of only the specified class only or from derived classes as well; otherwise, <see langword='false'/> .</param>
            <param name=' enumerateDeep'><see langword='true'/> to specify recursive enumeration in subclasses; otherwise, <see langword='false'/> .</param>
        </member>
        <member name="M:System.Management.EnumerationOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.EventWatcherOptions">
             <summary>
                <para> Specifies options for management event watching.</para>
                <para>Use this class to customize subscriptions for watching management events. </para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to listen to an event using ManagementEventWatcher object.
             class Sample_EventWatcherOptions
             {
                 public static int Main(string[] args) {
                     ManagementClass newClass = new ManagementClass();
                     newClass["__CLASS"] = "TestDeletionClass";
                     newClass.Put();
            
                     EventWatcherOptions options = new EventWatcherOptions();
                     ManagementEventWatcher watcher = new ManagementEventWatcher(null,
                                                                                 new WqlEventQuery("__classdeletionevent"),
                                                                                 options);
                     MyHandler handler = new MyHandler();
                     watcher.EventArrived += new EventArrivedEventHandler(handler.Arrived);
                     watcher.Start();
            
                     // Delete class to trigger event
                     newClass.Delete();
            
                     //For the purpose of this example, we will wait
                     // two seconds before main thread terminates.
                     System.Threading.Thread.Sleep(2000);
            
                     watcher.Stop();
            
                     return 0;
                 }
            
                 public class MyHandler
                 {
                    public void Arrived(object sender, EventArrivedEventArgs e) {
                        Console.WriteLine("Class Deleted= " +
                            ((ManagementBaseObject)e.NewEvent["TargetClass"])["__CLASS"]);
                    }
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to listen to an event using the ManagementEventWatcher object.
             Class Sample_EventWatcherOptions
                 Public Shared Sub Main()
                     Dim newClass As New ManagementClass()
                     newClass("__CLASS") = "TestDeletionClass"
                     newClass.Put()
            
                     Dim options As _
                         New EventWatcherOptions()
                     Dim watcher As New ManagementEventWatcher( _
                         Nothing, _
                         New WqlEventQuery("__classdeletionevent"), _
                         options)
                     Dim handler As New MyHandler()
                     AddHandler watcher.EventArrived, AddressOf handler.Arrived
                     watcher.Start()
            
                     ' Delete class to trigger event
                     newClass.Delete()
            
                     ' For the purpose of this example, we will wait
                     ' two seconds before main thread terminates.
                     System.Threading.Thread.Sleep(2000)
                     watcher.Stop()
                 End Sub
            
                 Public Class MyHandler
                     Public Sub Arrived(sender As Object, e As EventArrivedEventArgs)
                         Console.WriteLine("Class Deleted = " &amp; _
                             CType(e.NewEvent("TargetClass"), ManagementBaseObject)("__CLASS"))
                     End Sub
                 End Class
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.EventWatcherOptions.BlockSize">
            <summary>
               <para>Gets or sets the block size for block operations. When waiting for events, this
                  value specifies how many events to wait for before returning.</para>
            </summary>
            <value>
               <para>The default value is 1.</para>
            </value>
        </member>
        <member name="M:System.Management.EventWatcherOptions.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.EventWatcherOptions'/> class. </para>
            </overload>
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.EventWatcherOptions'/> class for event watching, using default values.
               This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.EventWatcherOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Int32)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.EventWatcherOptions'/> class with the given
               values.</para>
            </summary>
            <param name='context'>The options context object containing provider-specific information to be passed through to the provider. </param>
            <param name=' timeout'>The timeout to wait for the next events.</param>
            <param name=' blockSize'>The number of events to wait for in each block.</param>
        </member>
        <member name="M:System.Management.EventWatcherOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               The cloned object.
            </returns>
        </member>
        <member name="T:System.Management.ObjectGetOptions">
             <summary>
                <para> Specifies options for getting a management object.</para>
                Use this class to customize retrieval of a management object.
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to set a timeout value and list
             // all amended qualifiers in a ManagementClass object.
             class Sample_ObjectGetOptions
             {
                 public static int Main(string[] args) {
                     // Request amended qualifiers
                     ObjectGetOptions options =
                         new ObjectGetOptions(null, new TimeSpan(0,0,0,5), true);
                     ManagementClass diskClass =
                         new ManagementClass("root/cimv2", "Win32_Process", options);
                     foreach (QualifierData qualifier in diskClass.Qualifiers) {
                         Console.WriteLine(qualifier.Name + ":" + qualifier.Value);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to set a timeout value and list
             ' all amended qualifiers in a ManagementClass object.
             Class Sample_ObjectGetOptions
                 Overloads Public Shared Function Main(args() As String) As Integer
                     ' Request amended qualifiers
                     Dim options As _
                         New ObjectGetOptions(Nothing, New TimeSpan(0, 0, 0, 5), True)
                     Dim diskClass As New ManagementClass( _
                         "root/cimv2", _
                         "Win32_Process", _
                         options)
                     Dim qualifier As QualifierData
                     For Each qualifier In diskClass.Qualifiers
                         Console.WriteLine(qualifier.Name &amp; ":" &amp; qualifier.Value)
                     Next qualifier
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.ObjectGetOptions.UseAmendedQualifiers">
            <summary>
               <para> Gets or sets a value indicating whether the objects returned from WMI should
                  contain amended information. Typically, amended information is localizable information
                  attached to the WMI object, such as object and property descriptions.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the objects returned from WMI
               should contain amended information; otherwise, <see langword='false'/>. The
               default value is <see langword='false'/>.</para>
            </value>
        </member>
        <member name="M:System.Management.ObjectGetOptions.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.ObjectGetOptions'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ObjectGetOptions'/> class for getting a WMI object, using
               default values. This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ObjectGetOptions.#ctor(System.Management.ManagementNamedValueCollection)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ObjectGetOptions'/> class for getting a WMI object, using the
               specified provider-specific context.</para>
            </summary>
            <param name='context'>A provider-specific, named-value pairs context object to be passed through to the provider.</param>
        </member>
        <member name="M:System.Management.ObjectGetOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Boolean)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ObjectGetOptions'/> class for getting a WMI object,
               using the given options values.</para>
            </summary>
            <param name='context'>A provider-specific, named-value pairs context object to be passed through to the provider.</param>
            <param name=' timeout'>The length of time to let the operation perform before it times out. The default is <see cref='F:System.Management.ManagementOptions.InfiniteTimeout'/> .</param>
            <param name=' useAmendedQualifiers'><see langword='true'/> if the returned objects should contain amended (locale-aware) qualifiers; otherwise, <see langword='false'/>. </param>
        </member>
        <member name="M:System.Management.ObjectGetOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.PutOptions">
             <summary>
                <para> Specifies options for committing management
                   object changes.</para>
                <para>Use this class to customize how values are saved to a management object.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to specify a PutOptions using
             // PutOptions object when saving a ManagementClass object to
             // the WMI respository.
             class Sample_PutOptions
             {
                 public static int Main(string[] args) {
                     ManagementClass newClass = new ManagementClass("root/default",
                                                                    String.Empty,
                                                                    null);
                     newClass["__Class"] = "class999xc";
            
                     PutOptions options = new PutOptions();
                     options.Type = PutType.UpdateOnly;
            
                     try
                     {
                         newClass.Put(options); //will fail if the class doesn't already exist
                     }
                     catch (ManagementException e)
                     {
                         Console.WriteLine("Couldn't update class: " + e.ErrorCode);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to specify a PutOptions using
             ' PutOptions object when saving a ManagementClass object to
             ' WMI respository.
             Class Sample_PutOptions
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim newClass As New ManagementClass( _
                        "root/default", _
                        String.Empty, _
                        Nothing)
                     newClass("__Class") = "class999xc"
            
                     Dim options As New PutOptions()
                     options.Type = PutType.UpdateOnly 'will fail if the class doesn't already exist
            
                     Try
                         newClass.Put(options)
                     Catch e As ManagementException
                         Console.WriteLine("Couldn't update class: " &amp; e.ErrorCode)
                     End Try
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.PutOptions.UseAmendedQualifiers">
            <summary>
               <para> Gets or sets a value indicating whether the objects returned from WMI should
                  contain amended information. Typically, amended information is localizable information
                  attached to the WMI object, such as object and property descriptions.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the objects returned from WMI
               should contain amended information; otherwise, <see langword='false'/>. The
               default value is <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.PutOptions.Type">
            <summary>
               <para>Gets or sets the type of commit to be performed for the object.</para>
            </summary>
            <value>
            <para>The default value is <see cref='F:System.Management.PutType.UpdateOrCreate'/>.</para>
            </value>
        </member>
        <member name="M:System.Management.PutOptions.#ctor">
            <overload>
            <para> Initializes a new instance of the <see cref='T:System.Management.PutOptions'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.PutOptions'/> class for put operations, using default values.
               This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.PutOptions.#ctor(System.Management.ManagementNamedValueCollection)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.PutOptions'/> class for committing a WMI object, using the
               specified provider-specific context.</para>
            </summary>
            <param name='context'>A provider-specific, named-value pairs context object to be passed through to the provider.</param>
        </member>
        <member name="M:System.Management.PutOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Boolean,System.Management.PutType)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.PutOptions'/> class for committing a WMI object, using
               the specified option values.</para>
            </summary>
            <param name='context'>A provider-specific, named-value pairs object to be passed through to the provider. </param>
            <param name=' timeout'>The length of time to let the operation perform before it times out. The default is <see cref='F:System.Management.ManagementOptions.InfiniteTimeout'/> .</param>
            <param name=' useAmendedQualifiers'><see langword='true'/> if the returned objects should contain amended (locale-aware) qualifiers; otherwise, <see langword='false'/>. </param>
            <param name=' putType'> The type of commit to be performed (update or create).</param>
        </member>
        <member name="M:System.Management.PutOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.DeleteOptions">
             <summary>
                <para> Specifies options for deleting a management
                   object.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to specify a timeout value
             // when deleting a ManagementClass object.
             class Sample_DeleteOptions
             {
                 public static int Main(string[] args) {
                     ManagementClass newClass = new ManagementClass();
                     newClass["__CLASS"] = "ClassToDelete";
                     newClass.Put();
            
                     // Set deletion options: delete operation timeout value
                     DeleteOptions opt = new DeleteOptions(null, new TimeSpan(0,0,0,5));
            
                     ManagementClass dummyClassToDelete =
                         new ManagementClass("ClassToDelete");
                     dummyClassToDelete.Delete(opt);
            
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to specify a timeout value
             ' when deleting a ManagementClass object.
             Class Sample_DeleteOptions
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim newClass As New ManagementClass()
                     newClass("__CLASS") = "ClassToDelete"
                     newClass.Put()
            
                     ' Set deletion options: delete operation timeout value
                     Dim opt As New DeleteOptions(Nothing, New TimeSpan(0, 0, 0, 5))
            
                     Dim dummyClassToDelete As New ManagementClass("ClassToDelete")
                     dummyClassToDelete.Delete(opt)
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.DeleteOptions.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.DeleteOptions'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.DeleteOptions'/> class for the delete operation, using default values.
               This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.DeleteOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.DeleteOptions'/> class for a delete operation, using
               the specified values.</para>
            </summary>
            <param name='context'>A provider-specific, named-value pairs object to be passed through to the provider. </param>
            <param name='timeout'>The length of time to let the operation perform before it times out. The default value is <see cref='F:System.Management.ManagementOptions.InfiniteTimeout'/> . Setting this parameter will invoke the operation semisynchronously.</param>
        </member>
        <member name="M:System.Management.DeleteOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>A cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.InvokeMethodOptions">
             <summary>
                <para> Specifies options for invoking a management method.</para>
                <para>Use this class to customize the execution of a method on a management
                   object.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to stop a system service.
             class Sample_InvokeMethodOptions
             {
                 public static int Main(string[] args) {
                     ManagementObject service =
                         new ManagementObject("win32_service=\"winmgmt\"");
                     InvokeMethodOptions options = new InvokeMethodOptions();
                     options.Timeout = new TimeSpan(0,0,0,5);
            
                     ManagementBaseObject outParams = service.InvokeMethod("StopService", null, options);
            
                     Console.WriteLine("Return Status = " + outParams["ReturnValue"]);
            
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to stop a system service.
             Class Sample_InvokeMethodOptions
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim service As New ManagementObject("win32_service=""winmgmt""")
                     Dim options As New InvokeMethodOptions()
                     options.Timeout = New TimeSpan(0, 0, 0, 5)
            
                     Dim outParams As ManagementBaseObject = service.InvokeMethod( _
                         "StopService", _
                         Nothing, _
                         options)
            
                     Console.WriteLine("Return Status = " &amp; _
                         outParams("ReturnValue").ToString())
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.InvokeMethodOptions.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.InvokeMethodOptions'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.InvokeMethodOptions'/> class for the <see cref='M:System.Management.ManagementObject.InvokeMethod(System.String,System.Management.ManagementBaseObject,System.Management.InvokeMethodOptions)'/> operation, using default values.
               This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.InvokeMethodOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.InvokeMethodOptions'/> class for an invoke operation using
               the specified values.</para>
            </summary>
            <param name=' context'>A provider-specific, named-value pairs object to be passed through to the provider. </param>
            <param name='timeout'>The length of time to let the operation perform before it times out. The default value is <see cref='F:System.Management.ManagementOptions.InfiniteTimeout'/> . Setting this parameter will invoke the operation semisynchronously.</param>
        </member>
        <member name="M:System.Management.InvokeMethodOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.ConnectionOptions">
             <summary>
                <para> Specifies all settings required to make a WMI connection.</para>
                <para>Use this class to customize a connection to WMI made via a
                   ManagementScope object.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example demonstrates how to connect to remote machine
             // using supplied credentials.
             class Sample_ConnectionOptions
             {
                 public static int Main(string[] args) {
                     ConnectionOptions options = new ConnectionOptions();
                     options.Username = "domain\\username";
                     options.Password = "password";
                     ManagementScope scope = new ManagementScope(
                         "\\\\servername\\root\\cimv2",
                         options);
                     try {
                         scope.Connect();
                         ManagementObject disk = new ManagementObject(
                             scope,
                             new ManagementPath("Win32_logicaldisk='c:'"),
                             null);
                         disk.Get();
                     }
                     catch (Exception e) {
                         Console.WriteLine("Failed to connect: " + e.Message);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example demonstrates how to connect to remote machine
             ' using supplied credentials.
             Class Sample_ConnectionOptions
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim options As New ConnectionOptions()
                     options.Username = "domain\username"
                     options.Password = "password"
                     Dim scope As New ManagementScope("\\servername\root\cimv2", options)
                     Try
                         scope.Connect()
                         Dim disk As New ManagementObject(scope, _
                             New ManagementPath("Win32_logicaldisk='c:'"), Nothing)
                         disk.Get()
                     Catch e As UnauthorizedAccessException
                         Console.WriteLine(("Failed to connect: " + e.Message))
                     End Try
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.ConnectionOptions.Locale">
            <summary>
               <para>Gets or sets the locale to be used for the connection operation.</para>
            </summary>
            <value>
               <para>The default value is DEFAULTLOCALE.</para>
            </value>
        </member>
        <member name="P:System.Management.ConnectionOptions.Username">
            <summary>
               <para>Gets or sets the user name to be used for the connection operation.</para>
            </summary>
            <value>
               <para>Null if the connection will use the currently logged-on user; otherwise, a string representing the user name. The default value is null.</para>
            </value>
            <remarks>
               <para>If the user name is from a domain other than the current
                  domain, the string may contain the domain name and user name, separated by a backslash:</para>
               <c>
                  <para>string username = "EnterDomainHere\\EnterUsernameHere";</para>
               </c>
            </remarks>
        </member>
        <member name="P:System.Management.ConnectionOptions.Password">
            <summary>
               <para>Sets the password for the specified user. The value can be set, but not retrieved.</para>
            </summary>
            <value>
               <para> The default value is null. If the user name is also
                  null, the credentials used will be those of the currently logged-on user.</para>
            </value>
            <remarks>
               <para> A blank string ("") specifies a valid
                  zero-length password.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ConnectionOptions.SecurePassword">
            <summary>
               <para>Sets the secure password for the specified user. The value can be set, but not retrieved.</para>
            </summary>
            <value>
               <para> The default value is null. If the user name is also
                  null, the credentials used will be those of the currently logged-on user.</para>
            </value>
            <remarks>
               <para> A blank securestring ("") specifies a valid
                  zero-length password.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ConnectionOptions.Authority">
            <summary>
               <para>Gets or sets the authority to be used to authenticate the specified user.</para>
            </summary>
            <value>
               <para>If not null, this property can contain the name of the
                  Windows NT/Windows 2000 domain in which to obtain the user to
                  authenticate.</para>
            </value>
            <remarks>
               <para>
                  The property must be passed
                  as follows: If it begins with the string "Kerberos:", Kerberos
                  authentication will be used and this property should contain a Kerberos principal name. For
                  example, Kerberos:&lt;principal name&gt;.</para>
               <para>If the property value begins with the string "NTLMDOMAIN:", NTLM
                  authentication will be used and the property should contain a NTLM domain name.
                  For example, NTLMDOMAIN:&lt;domain name&gt;. </para>
               <para>If the property is null, NTLM authentication will be used and the NTLM domain
                  of the current user will be used.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ConnectionOptions.Impersonation">
            <summary>
               <para>Gets or sets the COM impersonation level to be used for operations in this connection.</para>
            </summary>
            <value>
               <para>The COM impersonation level to be used for operations in
                  this connection. The default value is <see cref='F:System.Management.ImpersonationLevel.Impersonate' qualify='true'/>, which indicates that the WMI provider can
                  impersonate the client when performing the requested operations in this connection.</para>
            </value>
            <remarks>
            <para>The <see cref='F:System.Management.ImpersonationLevel.Impersonate' qualify='true'/> setting is advantageous when the provider is
               a trusted application or service. It eliminates the need for the provider to
               perform client identity and access checks for the requested operations. However,
               note that if for some reason the provider cannot be trusted, allowing it to
               impersonate the client may constitute a security threat. In such cases, it is
               recommended that this property be set by the client to a lower value, such as
            <see cref='F:System.Management.ImpersonationLevel.Identify' qualify='true'/>. Note that this may cause failure of the
               provider to perform the requested operations, for lack of sufficient permissions
               or inability to perform access checks.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ConnectionOptions.Authentication">
            <summary>
               <para>Gets or sets the COM authentication level to be used for operations in this connection.</para>
            </summary>
            <value>
               <para>The COM authentication level to be used for operations
                  in this connection. The default value is <see cref='F:System.Management.AuthenticationLevel.Unchanged' qualify='true'/>, which indicates that the
                  client will use the authentication level requested by the server, according to
                  the standard DCOM negotiation process.</para>
            </value>
            <remarks>
               <para>On Windows 2000 and below, the WMI service will request
                  Connect level authentication, while on Windows XP and higher it will request
                  Packet level authentication. If the client requires a specific authentication
                  setting, this property can be used to control the authentication level on this
                  particular connection. For example, the property can be set to <see cref='F:System.Management.AuthenticationLevel.PacketPrivacy' qualify='true'/>
                  if the
                  client requires all communication to be encrypted.</para>
            </remarks>
        </member>
        <member name="P:System.Management.ConnectionOptions.EnablePrivileges">
            <summary>
               <para>Gets or sets a value indicating whether user privileges need to be enabled for
                  the connection operation. This property should only be used when the operation
                  performed requires a certain user privilege to be enabled
                  (for example, a machine reboot).</para>
            </summary>
            <value>
            <para><see langword='true'/> if user privileges need to be
               enabled for the connection operation; otherwise, <see langword='false'/>. The
               default value is <see langword='false'/>.</para>
            </value>
        </member>
        <member name="M:System.Management.ConnectionOptions.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.ConnectionOptions'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ConnectionOptions'/> class for the connection operation, using default values. This is the
               default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ConnectionOptions.#ctor(System.String,System.String,System.String,System.String,System.Management.ImpersonationLevel,System.Management.AuthenticationLevel,System.Boolean,System.Management.ManagementNamedValueCollection,System.TimeSpan)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ConnectionOptions'/> class to be used for a WMI
               connection, using the specified values.</para>
            </summary>
            <param name='locale'>The locale to be used for the connection.</param>
            <param name=' username'>The user name to be used for the connection. If null, the credentials of the currently logged-on user are used.</param>
            <param name=' password'>The password for the given user name. If the user name is also null, the credentials used will be those of the currently logged-on user.</param>
            <param name=' authority'><para>The authority to be used to authenticate the specified user.</para></param>
            <param name=' impersonation'>The COM impersonation level to be used for the connection.</param>
            <param name=' authentication'>The COM authentication level to be used for the connection.</param>
            <param name=' enablePrivileges'><see langword='true'/>to enable special user privileges; otherwise, <see langword='false'/> . This parameter should only be used when performing an operation that requires special Windows NT user privileges.</param>
            <param name=' context'>A provider-specific, named value pairs object to be passed through to the provider.</param>
            <param name=' timeout'>Reserved for future use.</param>
        </member>
        <member name="M:System.Management.ConnectionOptions.#ctor(System.String,System.String,System.Security.SecureString,System.String,System.Management.ImpersonationLevel,System.Management.AuthenticationLevel,System.Boolean,System.Management.ManagementNamedValueCollection,System.TimeSpan)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ConnectionOptions'/> class to be used for a WMI
               connection, using the specified values.</para>
            </summary>
            <param name='locale'>The locale to be used for the connection.</param>
            <param name='username'>The user name to be used for the connection. If null, the credentials of the currently logged-on user are used.</param>
            <param name='password'>The secure password for the given user name. If the user name is also null, the credentials used will be those of the currently logged-on user.</param>
            <param name='authority'><para>The authority to be used to authenticate the specified user.</para></param>
            <param name='impersonation'>The COM impersonation level to be used for the connection.</param>
            <param name='authentication'>The COM authentication level to be used for the connection.</param>
            <param name='enablePrivileges'><see langword='true'/>to enable special user privileges; otherwise, <see langword='false'/> . This parameter should only be used when performing an operation that requires special Windows NT user privileges.</param>
            <param name='context'>A provider-specific, named value pairs object to be passed through to the provider.</param>
            <param name='timeout'>Reserved for future use.</param>
        </member>
        <member name="M:System.Management.ConnectionOptions.Clone">
            <summary>
               <para> Returns a copy of the object.</para>
            </summary>
            <returns>
               <para>The cloned object.</para>
            </returns>
        </member>
        <member name="T:System.Management.ManagementPath">
             <summary>
                <para>Provides a wrapper for parsing and building paths to WMI objects.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample displays all properties in a ManagementPath object.
            
             class Sample_ManagementPath
             {
                 public static int Main(string[] args) {
                     ManagementPath path = new ManagementPath( "\\\\MyServer\\MyNamespace:Win32_logicaldisk='c:'");
            
                     // Results of full path parsing
                     Console.WriteLine("Path: " + path.Path);
                     Console.WriteLine("RelativePath: " + path.RelativePath);
                     Console.WriteLine("Server: " + path.Server);
                     Console.WriteLine("NamespacePath: " + path.NamespacePath);
                     Console.WriteLine("ClassName: " + path.ClassName);
                     Console.WriteLine("IsClass: " + path.IsClass);
                     Console.WriteLine("IsInstance: " + path.IsInstance);
                     Console.WriteLine("IsSingleton: " + path.IsSingleton);
            
                     // Change a portion of the full path
                     path.Server = "AnotherServer";
                     Console.WriteLine("New Path: " + path.Path);
                     return 0;
                }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             'This sample displays all properties in a ManagementPath object.
             Class Sample_ManagementPath Overloads
                 Public Shared Function Main(args() As String) As Integer
                     Dim path As _ New
                     ManagementPath("\\MyServer\MyNamespace:Win32_LogicalDisk='c:'")
            
                     ' Results of full path parsing
                     Console.WriteLine("Path: " &amp; path.Path)
                     Console.WriteLine("RelativePath: " &amp; path.RelativePath)
                     Console.WriteLine("Server: " &amp; path.Server)
                     Console.WriteLine("NamespacePath: " &amp; path.NamespacePath)
                     Console.WriteLine("ClassName: " &amp; path.ClassName)
                     Console.WriteLine("IsClass: " &amp; path.IsClass)
                     Console.WriteLine("IsInstance: " &amp; path.IsInstance)
                     Console.WriteLine("IsSingleton: " &amp; path.IsSingleton)
            
                     ' Change a portion of the full path
                     path.Server= "AnotherServer"
                     Console.WriteLine("New Path: " &amp; path.Path)
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ManagementPath.GetManagementPath(System.Management.IWbemClassObjectFreeThreaded)">
            <summary>
            Internal static "factory" method for making a new ManagementPath
            from the system property of a WMI object
            </summary>
            <param name="wbemObject">The WMI object whose __PATH property will
            be used to supply the returned object</param>
        </member>
        <member name="M:System.Management.ManagementPath.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.ManagementPath'/> class.
            </overload>
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ManagementPath'/> class that is empty. This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementPath.#ctor(System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementPath'/> class for the given path.</para>
            </summary>
            <param name='path'> The object path. </param>
        </member>
        <member name="M:System.Management.ManagementPath.ToString">
            <summary>
               <para>Returns the full object path as the string representation.</para>
            </summary>
            <returns>
               A string containing the full object
               path represented by this object. This value is equivalent to the value of the
            <see cref='P:System.Management.ManagementPath.Path'/> property.
            </returns>
        </member>
        <member name="M:System.Management.ManagementPath.Clone">
            <summary>
            <para>Returns a copy of the <see cref='T:System.Management.ManagementPath'/>.</para>
            </summary>
            <returns>
               The cloned object.
            </returns>
        </member>
        <member name="M:System.Management.ManagementPath.System#ICloneable#Clone">
            <summary>
            Standard Clone returns a copy of this ManagementPath as a generic "Object" type
            </summary>
            <returns>
               The cloned object.
            </returns>
        </member>
        <member name="P:System.Management.ManagementPath.DefaultPath">
            <summary>
               <para>Gets or sets the default scope path used when no scope is specified.
                  The default scope is /-/ \\.\root\cimv2, and can be changed by setting this property.</para>
            </summary>
            <value>
               <para>By default the scope value is /-/ \\.\root\cimv2, or a different scope path if
                  the default was changed.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementPath.SetAsClass">
            <summary>
               <para> Sets the path as a new class path. This means that the path must have
                  a class name but not key values.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementPath.SetAsSingleton">
            <summary>
               <para> Sets the path as a new singleton object path. This means that it is a path to an instance but
                  there are no key values.</para>
            </summary>
        </member>
        <member name="P:System.Management.ManagementPath.Path">
            <summary>
               <para> Gets or sets the string representation of the full path in the object.</para>
            </summary>
            <value>
               <para>A string containing the full path
                  represented in this object.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.RelativePath">
            <summary>
               <para> Gets or sets the relative path: class name and keys only.</para>
            </summary>
            <value>
               A string containing the relative
               path (not including the server and namespace portions) represented in this
               object.
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.Server">
            <summary>
               <para>Gets or sets the server part of the path.</para>
            </summary>
            <value>
               A string containing the server name
               from the path represented in this object.
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.NamespacePath">
            <summary>
               <para>Gets or sets the namespace part of the path. Note that this does not include
                  the server name, which can be retrieved separately.</para>
            </summary>
            <value>
               A string containing the namespace
               portion of the path represented in this object.
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.ClassName">
            <summary>
               Gets or sets the class portion of the path.
            </summary>
            <value>
               A string containing the name of the
               class.
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.IsClass">
            <summary>
               <para>Gets or sets a value indicating whether this is a class path.</para>
            </summary>
            <value>
            <para><see langword='true'/> if this is a class path; otherwise,
            <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.IsInstance">
            <summary>
               <para>Gets or sets a value indicating whether this is an instance path.</para>
            </summary>
            <value>
            <para><see langword='true'/> if this is an instance path; otherwise,
            <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementPath.IsSingleton">
            <summary>
               <para>Gets or sets a value indicating whether this is a singleton instance path.</para>
            </summary>
            <value>
            <para><see langword='true'/> if this is a singleton instance path; otherwise,
            <see langword='false'/>.</para>
            </value>
        </member>
        <member name="T:System.Management.ManagementPathConverter">
            <summary>
            Converts a String to a ManagementPath
            </summary>
        </member>
        <member name="M:System.Management.ManagementPathConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Determines if this converter can convert an object in the given source type to the native type of the converter.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='sourceType'>A Type that represents the type you wish to convert from.</param>
            <returns>
               <para>true if this converter can perform the conversion; otherwise, false.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementPathConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Gets a value indicating whether this converter can convert an object to the given destination type using the context.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='destinationType'>A Type that represents the type you wish to convert to.</param>
            <returns>
               <para>true if this converter can perform the conversion; otherwise, false.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementPathConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
                 Converts the given object to another type.  The most common types to convert
                 are to and from a string object.  The default implementation will make a call
                 to ToString on the object if the object is valid and if the destination
                 type is string.  If this cannot convert to the destination type, this will
                 throw a NotSupportedException.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='culture'>A CultureInfo object. If a null reference (Nothing in Visual Basic) is passed, the current culture is assumed.</param>
            <param name='value'>The Object to convert.</param>
            <param name='destinationType'>The Type to convert the value parameter to.</param>
            <returns>An Object that represents the converted value.</returns>
        </member>
        <member name="T:System.Management.ManagementQuery">
            <summary>
               <para> Provides an abstract base class for all management query objects.</para>
            </summary>
            <remarks>
               <para> This class is abstract; only
                  derivatives of it are actually used in the API.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementQuery.ParseQuery(System.String)">
            <summary>
             Parses the query string and sets the property values accordingly.
            </summary>
            <param name="query">The query string to be parsed.</param>
        </member>
        <member name="P:System.Management.ManagementQuery.QueryString">
            <summary>
               <para>Gets or sets the query in text format.</para>
            </summary>
            <value>
               <para> If the query object is
                  constructed with no parameters, the property is null until specifically set. If the
                  object was constructed with a specified query, the property returns the specified
                  query string.</para>
            </value>
        </member>
        <member name="P:System.Management.ManagementQuery.QueryLanguage">
            <summary>
               <para> Gets or sets the query language used in the query
                  string, defining the format of the query string.</para>
            </summary>
            <value>
               <para>Can be set to any supported query
                  language. "WQL" is the only value supported intrinsically by WMI.</para>
            </value>
        </member>
        <member name="M:System.Management.ManagementQuery.Clone">
            <summary>
               <para>Returns a copy of the object.</para>
            </summary>
            <returns>
               The cloned object.
            </returns>
        </member>
        <member name="T:System.Management.ObjectQuery">
             <summary>
                <para> Represents a management query that returns instances or classes.</para>
             </summary>
             <remarks>
                <para>This class or its derivatives are used to specify a
                   query in the <see cref='T:System.Management.ManagementObjectSearcher'/>. Use
                   a more specific query class whenever possible.</para>
             </remarks>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates creating a query.
            
             class Sample_ObjectQuery
             {
                 public static int Main(string[] args)
                 {
                     ObjectQuery objectQuery = new ObjectQuery("select * from Win32_Share");
                     ManagementObjectSearcher searcher =
                         new ManagementObjectSearcher(objectQuery);
                     foreach (ManagementObject share in searcher.Get())
                     {
                         Console.WriteLine("Share = " + share["Name"]);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates creating a query.
            
             Class Sample_ObjectQuery
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim objectQuery As New ObjectQuery("select * from Win32_Share")
                     Dim searcher As New ManagementObjectSearcher(objectQuery)
                     Dim share As ManagementObject
                     For Each share In searcher.Get()
                         Console.WriteLine("Share = " &amp; share("Name"))
                     Next share
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.ObjectQuery.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.ObjectQuery'/>
            class.</para>
            </overload>
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ObjectQuery'/>
            class with no initialized values. This
            is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ObjectQuery.#ctor(System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ObjectQuery'/>
            class
            for a specific query string.</para>
            </summary>
            <param name='query'>The string representation of the query.</param>
        </member>
        <member name="M:System.Management.ObjectQuery.#ctor(System.String,System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.ObjectQuery'/>
            class for a specific
            query string and language.</para>
            </summary>
            <param name='language'>The query language in which this query is specified.</param>
            <param name=' query'>The string representation of the query.</param>
        </member>
        <member name="M:System.Management.ObjectQuery.Clone">
            <summary>
               <para>Returns a copy of the object.</para>
            </summary>
            <returns>
               The cloned object.
            </returns>
        </member>
        <member name="T:System.Management.EventQuery">
             <summary>
                <para> Represents a WMI event query.</para>
             </summary>
             <remarks>
                <para> Objects of this class or its derivatives are used in
                <see cref='T:System.Management.ManagementEventWatcher'/> to subscribe to
                   WMI events. Use more specific derivatives of this class whenever possible.</para>
             </remarks>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to subscribe to an event
             // using the EventQuery object.
            
             class Sample_EventQuery
             {
                 public static int Main(string[] args)
                 {
                     //For this example, we make sure we have an arbitrary class on root\default
                     ManagementClass newClass = new ManagementClass(
                         "root\\default",
                         String.Empty,
                         null);
                     newClass["__Class"] = "TestWql";
                     newClass.Put();
            
                     //Create a query object for watching for class deletion events
                     EventQuery eventQuery = new EventQuery("select * from __classdeletionevent");
            
                     //Initialize an event watcher object with this query
                     ManagementEventWatcher watcher = new ManagementEventWatcher(
                         new ManagementScope("root/default"),
                         eventQuery);
            
                     //Set up a handler for incoming events
                     MyHandler handler = new MyHandler();
                     watcher.EventArrived += new EventArrivedEventHandler(handler.Arrived);
            
                     //Start watching for events
                     watcher.Start();
            
                     //For this example, we delete the class to trigger an event
                     newClass.Delete();
            
                     //Nothing better to do - we loop to wait for an event to arrive.
                     while (!handler.IsArrived) {
                          System.Threading.Thread.Sleep(1000);
                     }
            
                     //In this example we only want to wait for one event, so we can stop watching
                     watcher.Stop();
            
                     //Get some values from the event.
                     //Note: this can also be done in the event handler.
                     ManagementBaseObject eventArg =
                         (ManagementBaseObject)(handler.ReturnedArgs.NewEvent["TargetClass"]);
                     Console.WriteLine("Class Deleted = " + eventArg["__CLASS"]);
            
                     return 0;
                 }
            
                 public class MyHandler
                 {
                     private bool isArrived = false;
                     private EventArrivedEventArgs args;
            
                     //Handles the event when it arrives
                     public void Arrived(object sender, EventArrivedEventArgs e) {
                         args = e;
                         isArrived = true;
                     }
            
                     //Public property to get at the event information stored in the handler
                     public EventArrivedEventArgs ReturnedArgs {
                         get {
                             return args;
                         }
                     }
            
                     //Used to determine whether the event has arrived or not.
                     public bool IsArrived {
                         get {
                             return isArrived;
                         }
                     }
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to subscribe an event
             ' using the EventQuery object.
            
             Class Sample_EventQuery
                 Public Shared Sub Main()
            
                     'For this example, we make sure we have an arbitrary class on root\default
                     Dim newClass As New ManagementClass( _
                         "root\default", _
                         String.Empty, Nothing)
                         newClass("__Class") = "TestWql"
                         newClass.Put()
            
                     'Create a query object for watching for class deletion events
                     Dim eventQuery As New EventQuery("select * from __classdeletionevent")
            
                     'Initialize an event watcher object with this query
                     Dim watcher As New ManagementEventWatcher( _
                         New ManagementScope("root/default"), _
                         eventQuery)
            
                     'Set up a handler for incoming events
                     Dim handler As New MyHandler()
                     AddHandler watcher.EventArrived, AddressOf handler.Arrived
            
                     'Start watching for events
                     watcher.Start()
            
                     'For this example, we delete the class to trigger an event
                     newClass.Delete()
            
                     'Nothing better to do - we loop to wait for an event to arrive.
                     While Not handler.IsArrived
                         Console.Write("0")
                         System.Threading.Thread.Sleep(1000)
                     End While
            
                     'In this example we only want to wait for one event, so we can stop watching
                     watcher.Stop()
            
                     'Get some values from the event
                     'Note: this can also be done in the event handler.
                     Dim eventArg As ManagementBaseObject = CType( _
                         handler.ReturnedArgs.NewEvent("TargetClass"), _
                         ManagementBaseObject)
                     Console.WriteLine(("Class Deleted = " + eventArg("__CLASS")))
            
                 End Sub
            
                 Public Class MyHandler
                     Private _isArrived As Boolean = False
                     Private args As EventArrivedEventArgs
            
                     'Handles the event when it arrives
                     Public Sub Arrived(sender As Object, e As EventArrivedEventArgs)
                         args = e
                         _isArrived = True
                     End Sub
            
                     'Public property to get at the event information stored in the handler
                     Public ReadOnly Property ReturnedArgs() As EventArrivedEventArgs
                         Get
                             Return args
                         End Get
                     End Property
            
                     'Used to determine whether the event has arrived or not.
                     Public ReadOnly Property IsArrived() As Boolean
                         Get
                             Return _isArrived
                         End Get
                     End Property
                 End Class
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.EventQuery.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.EventQuery'/>
            class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.EventQuery'/>
            class. This is the
            default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.EventQuery.#ctor(System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.EventQuery'/>
            class for the specified query.</para>
            </summary>
            <param name='query'>A textual representation of the event query.</param>
        </member>
        <member name="M:System.Management.EventQuery.#ctor(System.String,System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.EventQuery'/>
            class for the specified
            language and query.</para>
            </summary>
            <param name='language'>The language in which the query string is specified. </param>
            <param name=' query'>The string representation of the query.</param>
        </member>
        <member name="M:System.Management.EventQuery.Clone">
            <summary>
               <para>Returns a copy of the object.</para>
            </summary>
            <returns>
               The cloned object.
            </returns>
        </member>
        <member name="T:System.Management.WqlObjectQuery">
             <summary>
                <para> Represents a WMI data query in WQL format.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to use a WqlObjectQuery class to
             // perform an object query.
            
             class Sample_WqlObjectQuery
             {
                 public static int Main(string[] args) {
                     WqlObjectQuery objectQuery = new WqlObjectQuery("select * from Win32_Share");
                     ManagementObjectSearcher searcher =
                         new ManagementObjectSearcher(objectQuery);
            
                     foreach (ManagementObject share in searcher.Get()) {
                         Console.WriteLine("Share = " + share["Name"]);
                     }
            
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrate how to use a WqlObjectQuery class to
             ' perform an object query.
            
             Class Sample_WqlObjectQuery
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim objectQuery As New WqlObjectQuery("select * from Win32_Share")
                     Dim searcher As New ManagementObjectSearcher(objectQuery)
            
                     Dim share As ManagementObject
                     For Each share In searcher.Get()
                         Console.WriteLine("Share = " &amp; share("Name"))
                     Next share
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.WqlObjectQuery.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.WqlObjectQuery'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.WqlObjectQuery'/> class. This is the
               default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.WqlObjectQuery.#ctor(System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlObjectQuery'/> class initialized to the
               specified query.</para>
            </summary>
            <param name='query'><para> The representation of the data query.</para></param>
        </member>
        <member name="P:System.Management.WqlObjectQuery.QueryLanguage">
            <summary>
               <para>Gets or sets the language of the query.</para>
            </summary>
            <value>
               <para> The value of this
                  property is always "WQL".</para>
            </value>
        </member>
        <member name="M:System.Management.WqlObjectQuery.Clone">
            <summary>
               <para>Creates a copy of the object.</para>
            </summary>
            <returns>
               The copied object.
            </returns>
        </member>
        <member name="T:System.Management.SelectQuery">
             <summary>
                <para> Represents a WQL SELECT data query.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to perform a WQL select query.
            
             class Sample_SelectQuery
             {
                 public static int Main(string[] args) {
                     SelectQuery selectQuery = new SelectQuery("win32_logicaldisk");
                     ManagementObjectSearcher searcher =
                         new ManagementObjectSearcher(selectQuery);
            
                     foreach (ManagementObject disk in searcher.Get()) {
                         Console.WriteLine(disk.ToString());
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to perform a WQL select query.
            
             Class Sample_SelectQuery
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim selectQuery As New SelectQuery("win32_logicaldisk")
                     Dim searcher As New ManagementObjectSearcher(selectQuery)
            
                     Dim disk As ManagementObject
                     For Each disk In  searcher.Get()
                         Console.WriteLine(disk.ToString())
                     Next disk
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.SelectQuery.#ctor">
            <overload>
            <para>Initializes a new instance of the <see cref='T:System.Management.SelectQuery'/>
            class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.SelectQuery'/>
            class. This is the
            default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.SelectQuery.#ctor(System.String)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.SelectQuery'/> class for the specified
                query or the specified class name.</para>
             </summary>
             <param name='queryOrClassName'>The entire query or the class name to use in the query. The parser in this class attempts to parse the string as a valid WQL SELECT query. If the parser is unsuccessful, it assumes the string is a class name.</param>
             <example>
                <code lang='C#'>SelectQuery s = new SelectQuery("SELECT * FROM Win32_Service WHERE State='Stopped');
            
             or
            
             //This is equivalent to "SELECT * FROM Win32_Service"
             SelectQuery s = new SelectQuery("Win32_Service");
                </code>
                <code lang='VB'>Dim s As New SelectQuery("SELECT * FROM Win32_Service WHERE State='Stopped')
            
             or
            
             //This is equivalent to "SELECT * FROM Win32_Service"
             Dim s As New SelectQuery("Win32_Service")
                </code>
             </example>
        </member>
        <member name="M:System.Management.SelectQuery.#ctor(System.String,System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.SelectQuery'/>
            class with the specified
            class name and condition.</para>
            </summary>
            <param name='className'>The name of the class to select in the query.</param>
            <param name=' condition'>The condition to be applied in the query.</param>
            <example>
               <code lang='C#'>SelectQuery s = new SelectQuery("Win32_Process", "HandleID=1234");
               </code>
               <code lang='VB'>Dim s As New SelectQuery("Win32_Process", "HandleID=1234")
               </code>
            </example>
        </member>
        <member name="M:System.Management.SelectQuery.#ctor(System.String,System.String,System.String[])">
             <summary>
             <para> Initializes a new instance of the <see cref='T:System.Management.SelectQuery'/>
             class with the specified
             class name and condition, selecting only the specified properties.</para>
             </summary>
             <param name='className'>The name of the class from which to select.</param>
             <param name='condition'>The condition to be applied to instances of the selected class.</param>
             <param name='selectedProperties'>An array of property names to be returned in the query results.</param>
             <example>
                <code lang='C#'>String[] properties = {"VariableName", "VariableValue"};
            
             SelectQuery s = new SelectQuery("Win32_Environment",
                                             "User='&lt;system&gt;'",
                                             properties);
                </code>
                <code lang='VB'>Dim properties As String[] = {"VariableName", "VariableValue"}
            
             Dim s As New SelectQuery("Win32_Environment", _
                                      "User=""&lt;system&gt;""", _
                                      properties)
                </code>
             </example>
        </member>
        <member name="M:System.Management.SelectQuery.#ctor(System.Boolean,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.SelectQuery'/>
            class for a schema query, optionally specifying a condition. For schema queries,
            only the <paramref name="condition"/> parameter is valid.</para>
            </summary>
            <param name='isSchemaQuery'><see langword='true'/>to indicate that this is a schema query; otherwise, <see langword='false'/>. A <see langword='false'/> value is invalid in this constructor.</param>
            <param name='condition'>The condition to be applied to form the result set of classes.</param>
            <example>
               <code lang='C#'>SelectQuery s = new SelectQuery(true, "__CLASS = 'Win32_Service'");
               </code>
               <code lang='VB'>Dim s As New SelectQuery(true, "__CLASS = ""Win32_Service""")
               </code>
            </example>
        </member>
        <member name="P:System.Management.SelectQuery.QueryString">
            <summary>
            <para>Gets or sets the query in the <see cref='T:System.Management.SelectQuery'/>, in string form.</para>
            </summary>
            <value>
               <para>A string representing the query.</para>
            </value>
            <remarks>
               <para> Setting this
                  property value overrides any previous value stored in the object. In addition, setting this
                  property causes the other members of the object to be updated when the string
                  is reparsed.</para>
            </remarks>
            <example>
               <code lang='C#'>SelectQuery s = new SelectQuery();
            s.QueryString = "SELECT * FROM Win32_LogicalDisk";
               </code>
               <code lang='VB'>Dim s As New SelectQuery()
            s.QueryString = "SELECT * FROM Win32_LogicalDisk"
               </code>
            </example>
        </member>
        <member name="P:System.Management.SelectQuery.IsSchemaQuery">
            <summary>
               <para>Gets or sets a value indicating whether this query is a schema query or an instances query.</para>
            </summary>
            <value>
            <para><see langword='true'/> if this query
               should be evaluated over the schema; <see langword='false'/> if the query should
               be evaluated over instances.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new query type.</para>
            </remarks>
        </member>
        <member name="P:System.Management.SelectQuery.ClassName">
             <summary>
                <para>Gets or sets the class name to be selected from in the query.</para>
             </summary>
             <value>
                <para>A string representing the name of the
                   class.</para>
             </value>
             <remarks>
                <para> Setting this property value
                   overrides any previous value stored in the object. The query string is
                   rebuilt to reflect the new class name.</para>
             </remarks>
             <example>
                <code lang='C#'>SelectQuery s = new SelectQuery("SELECT * FROM Win32_LogicalDisk");
             Console.WriteLine(s.QueryString); //output is : SELECT * FROM Win32_LogicalDisk
            
             s.ClassName = "Win32_Process";
             Console.WriteLine(s.QueryString); //output is : SELECT * FROM Win32_Process
                </code>
                <code lang='VB'>Dim s As New SelectQuery("SELECT * FROM Win32_LogicalDisk")
             Console.WriteLine(s.QueryString)  'output is : SELECT * FROM Win32_LogicalDisk
            
             s.ClassName = "Win32_Process"
             Console.WriteLine(s.QueryString)  'output is : SELECT * FROM Win32_Process
                </code>
             </example>
        </member>
        <member name="P:System.Management.SelectQuery.Condition">
            <summary>
               <para>Gets or sets the condition to be applied in the SELECT
                  query.</para>
            </summary>
            <value>
               A string containing the condition to
               be applied in the SELECT query.
            </value>
            <remarks>
               <para> Setting this property value overrides any previous value
                  stored in the object. The query string is rebuilt to reflect the new
                  condition.</para>
            </remarks>
        </member>
        <member name="P:System.Management.SelectQuery.SelectedProperties">
            <summary>
               <para> Gets or sets an array of property names to be
                  selected in the query.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Collections.Specialized.StringCollection'/> containing the names of the
               properties to be selected in the query.</para>
            </value>
            <remarks>
               <para> Setting this property value overrides any previous value stored
                  in the object. The query string is rebuilt to reflect the new
                  properties.</para>
            </remarks>
        </member>
        <member name="M:System.Management.SelectQuery.BuildQuery">
            <summary>
             Builds the query string according to the current property values.
            </summary>
        </member>
        <member name="M:System.Management.SelectQuery.ParseQuery(System.String)">
            <summary>
             Parses the query string and sets the property values accordingly.
            </summary>
            <param name="query">The query string to be parsed.</param>
        </member>
        <member name="M:System.Management.SelectQuery.Clone">
            <summary>
               <para> Creates a copy of the object.</para>
            </summary>
            <returns>
               The copied object.
            </returns>
        </member>
        <member name="T:System.Management.RelatedObjectQuery">
             <summary>
                <para> Represents a WQL ASSOCIATORS OF data query.
                   It can be used for both instances and schema queries.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to query all instances associated
             // with Win32_LogicalDisk='C:'.
            
             class Sample_RelatedObjectQuery
             {
                 public static int Main(string[] args) {
            
                     //This query requests all objects related to the 'C:' drive.
                     RelatedObjectQuery relatedQuery =
                         new RelatedObjectQuery("win32_logicaldisk='c:'");
                     ManagementObjectSearcher searcher =
                         new ManagementObjectSearcher(relatedQuery);
            
                     foreach (ManagementObject relatedObject in searcher.Get()) {
                         Console.WriteLine(relatedObject.ToString());
                     }
            
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to query all instances associated
             ' with Win32_LogicalDisk='C:'.
            
             Class Sample_RelatedObjectQuery
                 Overloads Public Shared Function Main(args() As String) As Integer
            
                     'This query requests all objects related to the 'C:' drive.
                     Dim relatedQuery As New RelatedObjectQuery("win32_logicaldisk='c:'")
                     Dim searcher As New ManagementObjectSearcher(relatedQuery)
            
                     Dim relatedObject As ManagementObject
                     For Each relatedObject In  searcher.Get()
                         Console.WriteLine(relatedObject.ToString())
                     Next relatedObject
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.RelatedObjectQuery'/> class.
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelatedObjectQuery'/> class. This is the
               default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.#ctor(System.String)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.RelatedObjectQuery'/>class. If the specified string can be successfully parsed as
                a WQL query, it is considered to be the query string; otherwise, it is assumed to be the path of the source
                object for the query. In this case, the query is assumed to be an instance query. </para>
             </summary>
             <param name='queryOrSourceObject'>The query string or the path of the source object.</param>
             <example>
                <code lang='C#'>//This query retrieves all objects related to the 'mymachine' computer system
             //It specifies the full query string in the constructor
             RelatedObjectQuery q =
                 new RelatedObjectQuery("associators of {Win32_ComputerSystem.Name='mymachine'}");
            
             //or
            
             //This query retrieves all objects related to the 'Alerter' service
             //It specifies only the object of interest in the constructor
             RelatedObjectQuery q =
                 new RelatedObjectQuery("Win32_Service.Name='Alerter'");
                </code>
                <code lang='VB'>'This query retrieves all objects related to the 'mymachine' computer system
             'It specifies the full query string in the constructor
             Dim q As New RelatedObjectQuery("associators of {Win32_ComputerSystem.Name='mymachine'}")
            
             'or
            
             'This query retrieves all objects related to the 'Alerter' service
             'It specifies only the object of interest in the constructor
             Dim q As New RelatedObjectQuery("Win32_Service.Name='Alerter'")
                </code>
             </example>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.#ctor(System.String,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelatedObjectQuery'/> class for the given source object and related class.
               The query is assumed to be an instance query (as opposed to a schema query).</para>
            </summary>
            <param name='sourceObject'>The path of the source object for this query.</param>
            <param name='relatedClass'>The related objects class.</param>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelatedObjectQuery'/> class for the given set of parameters.
               The query is assumed to be an instance query (as opposed to a schema query).</para>
            </summary>
            <param name='sourceObject'>The path of the source object.</param>
            <param name='relatedClass'>The related objects required class.</param>
            <param name='relationshipClass'>The relationship type.</param>
            <param name='relatedQualifier'>The qualifier required to be present on the related objects.</param>
            <param name='relationshipQualifier'>The qualifier required to be present on the relationships.</param>
            <param name='relatedRole'>The role that the related objects are required to play in the relationship.</param>
            <param name='thisRole'>The role that the source object is required to play in the relationship.</param>
            <param name='classDefinitionsOnly'><see langword='true'/>to return only the class definitions of the related objects; otherwise, <see langword='false'/> .</param>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.#ctor(System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelatedObjectQuery'/> class for a schema query using the given set
               of parameters. This constructor is used for schema queries only: the first
               parameter must be set to <see langword='true'/>
               .</para>
            </summary>
            <param name='isSchemaQuery'><see langword='true'/>to indicate that this is a schema query; otherwise, <see langword='false'/> .</param>
            <param name='sourceObject'>The path of the source class.</param>
            <param name='relatedClass'>The related objects' required base class.</param>
            <param name='relationshipClass'>The relationship type.</param>
            <param name='relatedQualifier'>The qualifier required to be present on the related objects.</param>
            <param name='relationshipQualifier'>The qualifier required to be present on the relationships.</param>
            <param name='relatedRole'>The role that the related objects are required to play in the relationship.</param>
            <param name='thisRole'>The role that the source class is required to play in the relationship.</param>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.IsSchemaQuery">
            <summary>
               <para>Gets or sets a value indicating whether this is a schema query or an instance query.</para>
            </summary>
            <value>
            <see langword='true'/> if this query
               should be evaluated over the schema; <see langword='false'/> if the query should
               be evaluated over instances.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new query type.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.SourceObject">
            <summary>
               <para> Gets or sets the source object to be used for the query. For instance
                  queries, this is typically an instance path. For schema queries, this is typically a class name.</para>
            </summary>
            <value>
               A string representing the path of the
               object to be used for the query.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new source object.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.RelatedClass">
            <summary>
               <para>Gets or sets the class of the endpoint objects.</para>
            </summary>
            <value>
               <para>A string containing the related class
                  name.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new related class.</para>
            </remarks>
            <example>
               <para>To find all the Win32 services available on a computer, this property is set
                  to "Win32_Service" : </para>
               <code lang='C#'>RelatedObjectQuery q = new RelatedObjectQuery("Win32_ComputerSystem='MySystem'");
            q.RelatedClass = "Win32_Service";
               </code>
               <code lang='VB'>Dim q As New RelatedObjectQuery("Win32_ComputerSystem=""MySystem""")
            q.RelatedClass = "Win32_Service"
               </code>
            </example>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.RelationshipClass">
            <summary>
               <para>Gets or sets the type of relationship (association).</para>
            </summary>
            <value>
               <para>A string containing the relationship
                  class name.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new relationship class.</para>
            </remarks>
            <example>
               <para>For example, for finding all the Win32 services dependent on
                  a service, this property should be set to the "Win32_DependentService" association class: </para>
               <code lang='C#'>RelatedObjectQuery q = new RelatedObjectQuery("Win32_Service='TCP/IP'");
            q.RelationshipClass = "Win32_DependentService";
               </code>
               <code lang='VB'>Dim q As New RelatedObjectQuery("Win32_Service=""TCP/IP""")
            q.RelationshipClass = "Win32_DependentService"
               </code>
            </example>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.RelatedQualifier">
            <summary>
               <para>Gets or sets a qualifier required to be defined on the related objects.</para>
            </summary>
            <value>
               A string containing the name of the
               qualifier required on the related objects.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new qualifier.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.RelationshipQualifier">
            <summary>
               <para>Gets or sets a qualifier required to be defined on the relationship objects.</para>
            </summary>
            <value>
               <para>A string containing the name of the qualifier required
                  on the relationship objects.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new qualifier.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.RelatedRole">
            <summary>
               <para>Gets or sets the role that the related objects returned should be playing in the relationship.</para>
            </summary>
            <value>
               <para>A string containing the role of the
                  related objects.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new role.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.ThisRole">
            <summary>
               <para>Gets or sets the role that the source object should be playing in the relationship.</para>
            </summary>
            <value>
               <para>A string containing the role of this object.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new role.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelatedObjectQuery.ClassDefinitionsOnly">
            <summary>
               <para>Gets or sets a value indicating that for all instances that adhere to the query, only their class definitions be returned.
                  This parameter is only valid for instance queries.</para>
            </summary>
            <value>
            <see langword='true'/> if the query
               requests only class definitions of the result set; otherwise,
            <see langword='false'/>.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new flag.</para>
            </remarks>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.BuildQuery">
            <summary>
             Builds the query string according to the current property values.
            </summary>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.ParseQuery(System.String)">
            <summary>
             Parses the query string and sets the property values accordingly.
            </summary>
            <param name="query">The query string to be parsed.</param>
        </member>
        <member name="M:System.Management.RelatedObjectQuery.Clone">
            <summary>
               <para>Creates a copy of the object.</para>
            </summary>
            <returns>
               The copied object.
            </returns>
        </member>
        <member name="T:System.Management.RelationshipQuery">
             <summary>
                <para> Represents a WQL REFERENCES OF data query.</para>
             </summary>
             <example>
                <para>The following example searches for all objects related to the
                   'C:' drive object:</para>
                <code lang='C#'>using System;
             using System.Management;
            
             class Sample_RelationshipQuery
             {
                 public static int Main(string[] args) {
                     RelationshipQuery query =
                         new RelationshipQuery("references of {Win32_LogicalDisk.DeviceID='C:'}");
                     ManagementObjectSearcher searcher =
                         new ManagementObjectSearcher(query);
            
                     foreach (ManagementObject assoc in searcher.Get()) {
                         Console.WriteLine("Association class = " + assoc["__CLASS"]);
                     }
            
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             Class Sample_RelatedObjectQuery
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim query As New RelationshipQuery("references of {Win32_LogicalDisk.DeviceID='C:'}")
                     Dim searcher As New ManagementObjectSearcher(query)
                     Dim assoc As ManagementObject
            
                     For Each assoc In searcher.Get()
                         Console.WriteLine("Association class = " &amp; assoc("__CLASS"))
                     Next assoc
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.RelationshipQuery.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.RelationshipQuery'/> class.
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelationshipQuery'/> class. This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.RelationshipQuery.#ctor(System.String)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.RelationshipQuery'/>class. If the specified string can be successfully parsed as
                a WQL query, it is considered to be the query string; otherwise, it is assumed to be the path of the source
                object for the query. In this case, the query is assumed to be an instances query. </para>
             </summary>
             <param name='queryOrSourceObject'>The query string or the class name for this query.</param>
             <example>
                <para>This example shows the two different ways to use this constructor:</para>
                <code lang='C#'>//Full query string is specified to the constructor
             RelationshipQuery q = new RelationshipQuery("references of {Win32_ComputerSystem.Name='mymachine'}");
            
             //Only the object of interest is specified to the constructor
             RelationshipQuery q = new RelationshipQuery("Win32_Service.Name='Alerter'");
                </code>
                <code lang='VB'>'Full query string is specified to the constructor
             Dim q As New RelationshipQuery("references of {Win32_ComputerSystem.Name='mymachine'}")
            
             'Only the object of interest is specified to the constructor
             Dim q As New RelationshipQuery("Win32_Service.Name='Alerter'")
                </code>
             </example>
        </member>
        <member name="M:System.Management.RelationshipQuery.#ctor(System.String,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelationshipQuery'/> class for the given source object and relationship class.
               The query is assumed to be an instance query (as opposed to a schema query).</para>
            </summary>
            <param name='sourceObject'> The path of the source object for this query.</param>
            <param name='relationshipClass'> The type of relationship for which to query.</param>
        </member>
        <member name="M:System.Management.RelationshipQuery.#ctor(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelationshipQuery'/> class for the given set of parameters.
               The query is assumed to be an instance query (as opposed to a schema query).</para>
            </summary>
            <param name='sourceObject'> The path of the source object for this query.</param>
            <param name='relationshipClass'> The type of relationship for which to query.</param>
            <param name='relationshipQualifier'> A qualifier required to be present on the relationship object.</param>
            <param name='thisRole'> The role that the source object is required to play in the relationship.</param>
            <param name='classDefinitionsOnly'>When this method returns, it contains a boolean that indicates that only class definitions for the resulting objects are returned.</param>
        </member>
        <member name="M:System.Management.RelationshipQuery.#ctor(System.Boolean,System.String,System.String,System.String,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.RelationshipQuery'/> class for a schema query using the given set
               of parameters. This constructor is used for schema queries only, so the first
               parameter must be <see langword='true'/>
               .</para>
            </summary>
            <param name='isSchemaQuery'><see langword='true'/>to indicate that this is a schema query; otherwise, <see langword='false'/> .</param>
            <param name='sourceObject'> The path of the source class for this query.</param>
            <param name='relationshipClass'> The type of relationship for which to query.</param>
            <param name='relationshipQualifier'> A qualifier required to be present on the relationship class.</param>
            <param name='thisRole'> The role that the source class is required to play in the relationship.</param>
        </member>
        <member name="P:System.Management.RelationshipQuery.IsSchemaQuery">
            <summary>
               <para>Gets or sets a value indicating whether this query is a schema query or an instance query.</para>
            </summary>
            <value>
            <see langword='true'/> if this query
               should be evaluated over the schema; <see langword='false'/> if the query should
               be evaluated over instances.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new query type.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelationshipQuery.SourceObject">
            <summary>
               <para>Gets or sets the source object for this query.</para>
            </summary>
            <value>
               A string representing the path of
               the object to be used for the query.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new source object.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelationshipQuery.RelationshipClass">
            <summary>
               <para>Gets or sets the class of the relationship objects wanted in the query.</para>
            </summary>
            <value>
               A string containing the relationship
               class name.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new class.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelationshipQuery.RelationshipQualifier">
            <summary>
               <para>Gets or sets a qualifier required on the relationship objects.</para>
            </summary>
            <value>
               A string containing the name of the
               qualifier required on the relationship objects.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new qualifier.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelationshipQuery.ThisRole">
            <summary>
               <para>Gets or sets the role of the source object in the relationship.</para>
            </summary>
            <value>
               A string containing the role of this
               object.
            </value>
            <remarks>
               <para>Setting this property value overrides any
                  previous value stored in the object. The query string is
                  rebuilt to reflect the new role.</para>
            </remarks>
        </member>
        <member name="P:System.Management.RelationshipQuery.ClassDefinitionsOnly">
            <summary>
               <para>Gets or sets a value indicating that only the class definitions of the relevant relationship objects be returned.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the query requests only class definitions of the
               result set; otherwise, <see langword='false'/>.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any previous
                  value stored in the object. As a side-effect, the query string is
                  rebuilt to reflect the new flag.</para>
            </remarks>
        </member>
        <member name="M:System.Management.RelationshipQuery.BuildQuery">
            <summary>
             Builds the query string according to the current property values.
            </summary>
        </member>
        <member name="M:System.Management.RelationshipQuery.ParseQuery(System.String)">
            <summary>
             Parses the query string and sets the property values accordingly.
            </summary>
            <param name="query">The query string to be parsed.</param>
        </member>
        <member name="M:System.Management.RelationshipQuery.Clone">
            <summary>
               <para>Creates a copy of the object.</para>
            </summary>
            <returns>
               The copied object.
            </returns>
        </member>
        <member name="T:System.Management.WqlEventQuery">
             <summary>
                <para> Represents a WMI event query in WQL format.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to subscribe to an event
             // using a WQL event query.
            
             class Sample_EventQuery
             {
                 public static int Main(string[] args)
                 {
                     //For this example, we make sure we have an arbitrary class on root\default
                     ManagementClass newClass = new ManagementClass(
                         "root\\default",
                         String.Empty,
                         null);
                     newClass["__Class"] = "TestWql";
                     newClass.Put();
            
                     //Create a query object for watching for class deletion events
                     WqlEventQuery eventQuery = new WqlEventQuery("select * from __classdeletionevent");
            
                     //Initialize an event watcher object with this query
                     ManagementEventWatcher watcher = new ManagementEventWatcher(
                         new ManagementScope("root/default"),
                         eventQuery);
            
                     //Set up a handler for incoming events
                     MyHandler handler = new MyHandler();
                     watcher.EventArrived += new EventArrivedEventHandler(handler.Arrived);
            
                     //Start watching for events
                     watcher.Start();
            
                     //For this example, we delete the class to trigger an event
                     newClass.Delete();
            
                     //Nothing better to do - we loop to wait for an event to arrive.
                     while (!handler.IsArrived) {
                          System.Threading.Thread.Sleep(1000);
                     }
            
                     //In this example we only want to wait for one event, so we can stop watching
                     watcher.Stop();
            
                     return 0;
                 }
            
                 public class MyHandler
                 {
                     private bool isArrived = false;
            
                     //Handles the event when it arrives
                     public void Arrived(object sender, EventArrivedEventArgs e) {
                         ManagementBaseObject eventArg = (ManagementBaseObject)(e.NewEvent["TargetClass"]);
                         Console.WriteLine("Class Deleted = " + eventArg["__CLASS"]);
                         isArrived = true;
                     }
            
                      //Used to determine whether the event has arrived or not.
                     public bool IsArrived {
                         get {
                             return isArrived;
                         }
                     }
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to subscribe an event
             ' using a WQL event query.
            
             Class Sample_EventQuery
                 Public Shared Sub Main()
            
                     'For this example, we make sure we have an arbitrary class on root\default
                     Dim newClass As New ManagementClass( _
                         "root\default", _
                         String.Empty, Nothing)
                         newClass("__Class") = "TestWql"
                         newClass.Put()
            
                     'Create a query object for watching for class deletion events
                     Dim eventQuery As New WqlEventQuery("select * from __classdeletionevent")
            
                     'Initialize an event watcher object with this query
                     Dim watcher As New ManagementEventWatcher( _
                         New ManagementScope("root/default"), _
                         eventQuery)
            
                     'Set up a handler for incoming events
                     Dim handler As New MyHandler()
                     AddHandler watcher.EventArrived, AddressOf handler.Arrived
            
                     'Start watching for events
                     watcher.Start()
            
                     'For this example, we delete the class to trigger an event
                     newClass.Delete()
            
                     'Nothing better to do - we loop to wait for an event to arrive.
                     While Not handler.IsArrived
                         Console.Write("0")
                         System.Threading.Thread.Sleep(1000)
                     End While
            
                     'In this example we only want to wait for one event, so we can stop watching
                     watcher.Stop()
            
                 End Sub
            
                 Public Class MyHandler
                     Private _isArrived As Boolean = False
            
                     'Handles the event when it arrives
                     Public Sub Arrived(sender As Object, e As EventArrivedEventArgs)
                         Dim eventArg As ManagementBaseObject = CType( _
                             e.NewEvent("TargetClass"), _
                             ManagementBaseObject)
                         Console.WriteLine(("Class Deleted = " + eventArg("__CLASS")))
                         _isArrived = True
                     End Sub
            
                     'Used to determine whether the event has arrived or not.
                     Public ReadOnly Property IsArrived() As Boolean
                         Get
                             Return _isArrived
                         End Get
                     End Property
                 End Class
             End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor">
            <overload>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/> class.</para>
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class. This is the default
            constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String)">
             <summary>
             <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
             class based on the given
             query string or event class name.</para>
             </summary>
             <param name='queryOrEventClassName'>The string representing either the entire event query or the name of the event class to query. The object will try to parse the string as a valid event query. If unsuccessful, the parser will assume that the parameter represents an event class name.</param>
             <example>
                <para>The two options below are equivalent :</para>
                <code lang='C#'>//Full query string specified to the constructor
             WqlEventQuery q = new WqlEventQuery("SELECT * FROM MyEvent");
            
             //Only relevant event class name specified to the constructor
             WqlEventQuery q = new WqlEventQuery("MyEvent"); //results in the same query as above.
                </code>
                <code lang='VB'>'Full query string specified to the constructor
             Dim q As New WqlEventQuery("SELECT * FROM MyEvent")
            
             'Only relevant event class name specified to the constructor
             Dim q As New WqlEventQuery("MyEvent") 'results in the same query as above
                </code>
             </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class for the
            specified event class name, with the specified condition.</para>
            </summary>
            <param name='eventClassName'>The name of the event class to query.</param>
            <param name=' condition'>The condition to apply to events of the specified class.</param>
            <example>
               <para>This example shows how to create an event query that contains a condition in
                  addition to the event class :</para>
               <code lang='C#'>//Requests all "MyEvent" events where the event's properties
            //match the specified condition
            WqlEventQuery q = new WqlEventQuery("MyEvent", "FirstProp &lt; 20 and SecondProp = 'red'");
               </code>
               <code lang='VB'>'Requests all "MyEvent" events where the event's properties
            'match the specified condition
            Dim q As New WqlEventQuery("MyEvent", "FirstProp &lt; 20 and SecondProp = 'red'")
               </code>
            </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.TimeSpan)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class for the specified
            event class, with the specified latency time.</para>
            </summary>
            <param name='eventClassName'>The name of the event class to query.</param>
            <param name=' withinInterval'>A timespan value specifying the latency acceptable for receiving this event. This value is used in cases where there is no explicit event provider for the query requested, and WMI is required to poll for the condition. This interval is the maximum amount of time that can pass before notification of an event must be delivered. </param>
            <example>
               <para>This example shows creating an event query that contains
                  a
                  time interval.</para>
               <code lang='C#'>//Requests all instance creation events, with a specified latency of
            //10 seconds. The query created is "SELECT * FROM __InstanceCreationEvent WITHIN 10"
            WqlEventQuery q = new WqlEventQuery("__InstanceCreationEvent",
                                                new TimeSpan(0,0,10));
               </code>
               <code lang='VB'>'Requests all instance creation events, with a specified latency of
            '10 seconds. The query created is "SELECT * FROM __InstanceCreationEvent WITHIN 10"
            Dim t As New TimeSpan(0,0,10)
            Dim q As New WqlEventQuery("__InstanceCreationEvent", t)
               </code>
            </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.TimeSpan,System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class with the specified
            event class name, polling interval, and condition.</para>
            </summary>
            <param name='eventClassName'>The name of the event class to query. </param>
            <param name=' withinInterval'>A timespan value specifying the latency acceptable for receiving this event. This value is used in cases where there is no explicit event provider for the query requested and WMI is required to poll for the condition. This interval is the maximum amount of time that can pass before notification of an event must be delivered. </param>
            <param name=' condition'>The condition to apply to events of the specified class.</param>
            <example>
               <para> This example creates the event query: "SELECT * FROM
               <see langword='__InstanceCreationEvent '/>WITHIN 10 WHERE
               <see langword='TargetInstance'/> ISA <see langword='Win32_Service'/>", which means
                  "send notification of the creation of <see langword='Win32_Service '/>
                  instances,
                  with a 10-second polling interval."</para>
               <code lang='C#'>//Requests notification of the creation of Win32_Service instances with a 10 second
            //allowed latency.
            WqlEventQuery q = new WqlEventQuery("__InstanceCreationEvent",
                                                new TimeSpan(0,0,10),
                                                "TargetInstance isa 'Win32_Service'");
               </code>
               <code lang='VB'>'Requests notification of the creation of Win32_Service instances with a 10 second
            'allowed latency.
            Dim t As New TimeSpan(0,0,10)
            Dim q As New WqlEventQuery("__InstanceCreationEvent", _
                                       t, _
                                       "TargetInstance isa ""Win32_Service""")
               </code>
            </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.String,System.TimeSpan)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class with the specified
            event class name, condition, and grouping interval.</para>
            </summary>
            <param name='eventClassName'>The name of the event class to query. </param>
            <param name='condition'>The condition to apply to events of the specified class.</param>
            <param name=' groupWithinInterval'>The specified interval at which WMI sends one aggregate event, rather than many events.</param>
            <example>
               <para>This example creates the event query: "SELECT * FROM
               <see langword='FrequentEvent'/> WHERE <see langword='InterestingProperty'/>= 5
                  GROUP WITHIN 10", which means "send notification of events of type
               <see langword='FrequentEvent'/>, in which the
               <see langword='InterestingProperty'/> is equal to 5, but send an aggregate event in
                  a
                  10-second interval."</para>
               <code lang='C#'>//Sends an aggregate of the requested events every 10 seconds
            WqlEventQuery q = new WqlEventQuery("FrequentEvent",
                                                "InterestingProperty = 5",
                                                new TimeSpan(0,0,10));
               </code>
               <code lang='VB'>'Sends an aggregate of the requested events every 10 seconds
            Dim t As New TimeSpan(0,0,10)
            Dim q As New WqlEventQuery("FrequentEvent", _
                                       "InterestingProperty = 5", _
                                       t)
               </code>
            </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.String,System.TimeSpan,System.String[])">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class with the specified event class
            name, condition, grouping interval, and grouping properties.</para>
            </summary>
            <param name='eventClassName'>The name of the event class to query. </param>
            <param name='condition'>The condition to apply to events of the specified class.</param>
            <param name=' groupWithinInterval'>The specified interval at which WMI sends one aggregate event, rather than many events. </param>
            <param name=' groupByPropertyList'>The properties in the event class by which the events should be grouped.</param>
            <example>
               <para>This example creates the event query: "SELECT * FROM
               <see langword='EmailEvent'/> WHERE <see langword='Sender'/> = 'MyBoss' GROUP
                  WITHIN 300 BY <see langword='Importance'/>", which means "send notification when
                  new email from a particular sender has arrived within the last 10 minutes,
                  combined with other events that have the same value in the
               <see langword='Importance'/>
               property."</para>
            <code lang='C#'>//Requests "EmailEvent" events where the Sender property is "MyBoss", and
            //groups them based on importance
            String[] props = {"Importance"};
            WqlEventQuery q = new WqlEventQuery("EmailEvent",
                                                "Sender = 'MyBoss'",
                                                new TimeSpan(0,10,0),
                                                props);
            </code>
            <code lang='VB'>'Requests "EmailEvent" events where the Sender property is "MyBoss", and
            'groups them based on importance
            Dim props() As String = {"Importance"}
            Dim t As New TimeSpan(0,10,0)
            Dim q As New WqlEventQuery("EmailEvent", _
                                       "Sender = ""MyBoss""", _
                                       t, _
                                       props)
            </code>
            </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.TimeSpan,System.String,System.TimeSpan,System.String[],System.String)">
            <summary>
            <para> Initializes a new instance of the <see cref='T:System.Management.WqlEventQuery'/>
            class with the specified event class
            name, condition, grouping interval, grouping properties, and specified number of events.</para>
            </summary>
            <param name='eventClassName'>The name of the event class on which to be queried.</param>
            <param name='withinInterval'>A timespan value specifying the latency acceptable for receiving this event. This value is used in cases where there is no explicit event provider for the query requested, and WMI is required to poll for the condition. This interval is the maximum amount of time that can pass before notification of an event must be delivered.</param>
            <param name=' condition'>The condition to apply to events of the specified class.</param>
            <param name=' groupWithinInterval'>The specified interval at which WMI sends one aggregate event, rather than many events. </param>
            <param name=' groupByPropertyList'>The properties in the event class by which the events should be grouped.</param>
            <param name=' havingCondition'>The condition to apply to the number of events.</param>
            <example>
               <para>This example creates the event query: "SELECT * FROM
               <see langword='__InstanceCreationEvent '/>WHERE <see langword='TargetInstance'/>
               ISA <see langword='Win32_NTLogEvent '/>GROUP WITHIN 300 BY
            <see langword='TargetInstance.SourceName'/> HAVING
            <see langword='NumberOfEvents'/> &gt; 15" which means "deliver aggregate events
               only if the number of <see langword='Win32_NTLogEvent '/>events received from the
               same source exceeds 15."</para>
            <code lang='C#'>//Requests sending aggregated events if the number of events exceeds 15.
            String[] props = {"TargetInstance.SourceName"};
            WqlEventQuery q = new WqlEventQuery("__InstanceCreationEvent",
                                                "TargetInstance isa 'Win32_NTLogEvent'",
                                                new TimeSpan(0,10,0),
                                                props,
                                                "NumberOfEvents &gt;15");
            </code>
            <code lang='VB'>'Requests sending aggregated events if the number of events exceeds 15.
            Dim props() As String = {"TargetInstance.SourceName"};
            Dim t As New TimeSpan(0,10,0)
            Dim q As WqlEventQuery("__InstanceCreationEvent", _
                                   "TargetInstance isa ""Win32_NTLogEvent""", _
                                   t, _
                                   props, _
                                   "NumberOfEvents &gt;15")
            </code>
            </example>
        </member>
        <member name="P:System.Management.WqlEventQuery.QueryLanguage">
            <summary>
               <para>Gets or sets the language of the query.</para>
            </summary>
            <value>
               <para>The value of this property in this
                  object is always "WQL".</para>
            </value>
        </member>
        <member name="P:System.Management.WqlEventQuery.QueryString">
            <summary>
               <para>Gets or sets the string representing the query.</para>
            </summary>
            <value>
               A string representing the query.
            </value>
        </member>
        <member name="P:System.Management.WqlEventQuery.EventClassName">
            <summary>
               <para> Gets or sets the event class to query.</para>
            </summary>
            <value>
               A string containing the name of the
               event class to query.
            </value>
            <remarks>
               <para> Setting this property value overrides any previous value
                  stored
                  in the object. The query string is rebuilt to
                  reflect the new class name.</para>
            </remarks>
            <example>
            <para>This example creates a new <see cref='T:System.Management.WqlEventQuery'/>
            that represents the query: "SELECT * FROM <see langword='MyEvent'/> ".</para>
            <code lang='C#'>WqlEventQuery q = new WqlEventQuery();
            q.EventClassName = "MyEvent";
            </code>
            <code lang='VB'>Dim q As New WqlEventQuery()
            q.EventClassName = "MyEvent"
            </code>
            </example>
        </member>
        <member name="P:System.Management.WqlEventQuery.Condition">
            <summary>
               <para>Gets or sets the condition to be applied to events of the
                  specified class.</para>
            </summary>
            <value>
               <para>The condition is represented as a
                  string, containing one or more clauses of the form: &lt;propName&gt;
                  &lt;operator&gt; &lt;value&gt; combined with and/or operators. &lt;propName&gt;
                  must represent a property defined on the event class specified in this query.</para>
            </value>
            <remarks>
               <para>Setting this property value overrides any previous value
                  stored in the object. The query string is rebuilt to
                  reflect the new condition.</para>
            </remarks>
            <example>
            <para>This example creates a new <see cref='T:System.Management.WqlEventQuery'/>
            that represents the query: "SELECT * FROM <see langword='MyEvent'/> WHERE
            <see langword='PropVal'/> &gt; 8".</para>
            <code lang='C#'>WqlEventQuery q = new WqlEventQuery();
            q.EventClassName = "MyEvent";
            q.Condition = "PropVal &gt; 8";
            </code>
            <code lang='VB'>Dim q As New WqlEventQuery()
            q.EventClassName = "MyEvent"
            q.Condition = "PropVal &gt; 8"
            </code>
            </example>
        </member>
        <member name="P:System.Management.WqlEventQuery.WithinInterval">
            <summary>
               <para>Gets or sets the polling interval to be used in this query.</para>
            </summary>
            <value>
               <para>Null, if there is no polling involved; otherwise, a
                  valid <see cref='T:System.TimeSpan'/>
                  value if polling is required.</para>
            </value>
            <remarks>
               <para>This property should only be set in cases
                  where there is no event provider for the event requested, and WMI is required to
                  poll for the requested condition.</para>
               <para>Setting this property value overrides any previous value
                  stored in
                  the object. The query string is rebuilt to reflect the new interval.</para>
            </remarks>
            <example>
            <para>This example creates a new <see cref='T:System.Management.WqlEventQuery'/>
            that represents the query: "SELECT * FROM <see langword='__InstanceModificationEvent '/>WITHIN 10 WHERE <see langword='PropVal'/> &gt; 8".</para>
            <code lang='C#'>WqlEventQuery q = new WqlEventQuery();
            q.EventClassName = "__InstanceModificationEvent";
            q.Condition = "PropVal &gt; 8";
            q.WithinInterval = new TimeSpan(0,0,10);
            </code>
            <code lang='VB'>Dim q As New WqlEventQuery()
            q.EventClassName = "__InstanceModificationEvent"
            q.Condition = "PropVal &gt; 8"
            q.WithinInterval = New TimeSpan(0,0,10)
            </code>
            </example>
        </member>
        <member name="P:System.Management.WqlEventQuery.GroupWithinInterval">
            <summary>
               <para>Gets or sets the interval to be used for grouping events of
                  the same type.</para>
            </summary>
            <value>
               <para> Null, if there is no
                  grouping involved; otherwise, the interval in which WMI should group events of
                  the same type.</para>
            </value>
            <remarks>
               <para> Setting this property value overrides any previous value stored in
                  the object. The query string is rebuilt to reflect the new interval.</para>
            </remarks>
            <example>
            <para>This example creates a new <see cref='T:System.Management.WqlEventQuery'/>
            that represents the query: "SELECT * FROM <see langword='MyEvent'/> WHERE
            <see langword='PropVal'/> &gt; 8 GROUP WITHIN 10", which means "send notification
            of all <see langword='MyEvent'/> events where the <see langword='PropVal'/>
            property is greater than 8, and aggregate these events within 10-second intervals."</para>
            <code lang='C#'>WqlEventQuery q = new WqlEventQuery();
            q.EventClassName = "MyEvent";
            q.Condition = "PropVal &gt; 8";
            q.GroupWithinInterval = new TimeSpan(0,0,10);
            </code>
            <code lang='VB'>Dim q As New WqlEventQuery()
            q.EventClassName = "MyEvent"
            q.Condition = "PropVal &gt; 8"
            q.GroupWithinInterval = New TimeSpan(0,0,10)
            </code>
            </example>
        </member>
        <member name="P:System.Management.WqlEventQuery.GroupByPropertyList">
            <summary>
               <para>Gets or sets properties in the event to be used for
                  grouping events of the same type.</para>
            </summary>
            <value>
               <para>
                  Null, if no grouping is required; otherwise, a collection of event
                  property names.</para>
            </value>
            <remarks>
               <para> Setting this property value overrides any previous value stored in
                  the object. The query string is rebuilt to reflect the new grouping.</para>
            </remarks>
            <example>
            <para>This example creates a new <see cref='T:System.Management.WqlEventQuery'/>
            that represents the query: "SELECT * FROM <see langword='EmailEvent'/> GROUP
            WITHIN 300 BY <see langword='Sender'/>", which means "send notification of all
            <see langword='EmailEvent'/> events, aggregated by the <see langword='Sender'/>property, within 10-minute intervals."</para>
            <code lang='C#'>WqlEventQuery q = new WqlEventQuery();
            q.EventClassName = "EmailEvent";
            q.GroupWithinInterval = new TimeSpan(0,10,0);
            q.GroupByPropertyList = new StringCollection();
            q.GroupByPropertyList.Add("Sender");
            </code>
            <code lang='VB'>Dim q As New WqlEventQuery()
            q.EventClassName = "EmailEvent"
            q.GroupWithinInterval = New TimeSpan(0,10,0)
            q.GroupByPropertyList = New StringCollection()
            q.GroupByPropertyList.Add("Sender")
            </code>
            </example>
        </member>
        <member name="P:System.Management.WqlEventQuery.HavingCondition">
            <summary>
               <para>Gets or sets the condition to be applied to the aggregation of
                  events, based on the number of events received.</para>
            </summary>
            <value>
               <para>
                  Null, if no aggregation or no condition should be applied;
                  otherwise, a condition of the form "NumberOfEvents &lt;operator&gt;
                  &lt;value&gt;".</para>
            </value>
            <remarks>
               <para> Setting this property value overrides any previous value stored in
                  the object. The query string is rebuilt to reflect the new grouping condition.</para>
            </remarks>
            <example>
            <para>This example creates a new <see cref='T:System.Management.WqlEventQuery'/>
            that represents the query: "SELECT * FROM <see langword='EmailEvent'/> GROUP
            WITHIN 300 HAVING <see langword='NumberOfEvents'/> &gt; 5", which means "send
            notification of all <see langword='EmailEvent'/> events, aggregated within
            10-minute intervals, if there are more than 5 occurrences."</para>
            <code lang='C#'>WqlEventQuery q = new WqlEventQuery();
            q.EventClassName = "EmailEvent";
            q.GroupWithinInterval = new TimeSpan(0,10,0);
            q.HavingCondition = "NumberOfEvents &gt; 5";
            </code>
            <code lang='VB'>Dim q As New WqlEventQuery()
            q.EventClassName = "EmailEvent"
            q.GroupWithinInterval = new TimeSpan(0,10,0)
            q.HavingCondition = "NumberOfEvents &gt; 5"
            </code>
            </example>
        </member>
        <member name="M:System.Management.WqlEventQuery.BuildQuery">
            <summary>
             Builds the query string according to the current property values.
            </summary>
        </member>
        <member name="M:System.Management.WqlEventQuery.ParseQuery(System.String)">
            <summary>
             Parses the query string and sets the property values accordingly.
            </summary>
            <param name="query">The query string to be parsed.</param>
        </member>
        <member name="M:System.Management.WqlEventQuery.Clone">
            <summary>
               <para>Creates a copy of the object.</para>
            </summary>
            <returns>
               The copied object.
            </returns>
        </member>
        <member name="T:System.Management.ManagementQueryConverter">
            <summary>
            Converts a String to a ManagementQuery
            </summary>
        </member>
        <member name="M:System.Management.ManagementQueryConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Determines if this converter can convert an object in the given source type to the native type of the converter.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='sourceType'>A Type that represents the type you wish to convert from.</param>
            <returns>
               <para>true if this converter can perform the conversion; otherwise, false.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementQueryConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Gets a value indicating whether this converter can convert an object to the given destination type using the context.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='destinationType'>A Type that represents the type you wish to convert to.</param>
            <returns>
               <para>true if this converter can perform the conversion; otherwise, false.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementQueryConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
                 Converts the given object to another type.  The most common types to convert
                 are to and from a string object.  The default implementation will make a call
                 to ToString on the object if the object is valid and if the destination
                 type is string.  If this cannot convert to the destination type, this will
                 throw a NotSupportedException.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='culture'>A CultureInfo object. If a null reference (Nothing in Visual Basic) is passed, the current culture is assumed.</param>
            <param name='value'>The Object to convert.</param>
            <param name='destinationType'>The Type to convert the value parameter to.</param>
            <returns>An Object that represents the converted value.</returns>
        </member>
        <member name="T:System.Management.ManagementScope">
             <summary>
                <para>Represents a scope for management operations. In v1.0 the scope defines the WMI namespace in which management operations are performed.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to connect to root/default namespace
             // using ManagmentScope object.
             class Sample_ManagementScope
             {
                 public static int Main(string[] args)
                 {
                     ManagementScope scope = new ManagementScope("root\\default");
                     scope.Connect();
                     ManagementClass newClass = new ManagementClass(
                         scope,
                         new ManagementPath(),
                         null);
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to connect to root/default namespace
             ' using ManagmentScope object.
             Class Sample_ManagementScope
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim scope As New ManagementScope("root\default")
                     scope.Connect()
                     Dim newClass As New ManagementClass(scope, _
                         New ManagementPath(), _
                         Nothing)
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.ManagementScope.IsConnected">
            <summary>
            <para> Gets or sets a value indicating whether the <see cref='T:System.Management.ManagementScope'/> is currently bound to a
               WMI server and namespace.</para>
            </summary>
            <value>
            <para><see langword='true'/> if a connection is alive (bound
               to a server and namespace); otherwise, <see langword='false'/>.</para>
            </value>
            <remarks>
               <para> A scope is disconnected after creation until someone
                  explicitly calls <see cref='M:System.Management.ManagementScope.Connect'/>(), or uses the scope for any
                  operation that requires a live connection. Also, the scope is
                  disconnected from the previous connection whenever the identifying properties of the scope are
                  changed.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementScope.#ctor">
            <overload>
               Initializes a new instance
               of the <see cref='T:System.Management.ManagementScope'/> class.
            </overload>
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementScope'/> class, with default values. This is the
               default constructor.</para>
            </summary>
            <remarks>
               <para> If the object doesn't have any
                  properties set before connection, it will be initialized with default values
                  (for example, the local machine and the root\cimv2 namespace).</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementScope s = new ManagementScope();
               </code>
               <code lang='VB'>Dim s As New ManagementScope()
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementScope.#ctor(System.Management.ManagementPath)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementScope'/> class representing
               the specified scope path.</para>
            </summary>
            <param name='path'>A <see cref='T:System.Management.ManagementPath'/> containing the path to a server and namespace for the <see cref='T:System.Management.ManagementScope'/>.</param>
            <example>
               <code lang='C#'>ManagementScope s = new ManagementScope(new ManagementPath("\\\\MyServer\\root\\default"));
               </code>
               <code lang='VB'>Dim p As New ManagementPath("\\MyServer\root\default")
            Dim s As New ManagementScope(p)
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementScope.#ctor(System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementScope'/> class representing the specified scope
               path.</para>
            </summary>
            <param name='path'>The server and namespace path for the <see cref='T:System.Management.ManagementScope'/>.</param>
            <example>
               <code lang='C#'>ManagementScope s = new ManagementScope("\\\\MyServer\\root\\default");
               </code>
               <code lang='VB'>Dim s As New ManagementScope("\\MyServer\root\default")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementScope.#ctor(System.String,System.Management.ConnectionOptions)">
            <summary>
            <para>Initializes a new instance of the <see cref='T:System.Management.ManagementScope'/> class representing the specified scope path,
               with the specified options.</para>
            </summary>
            <param name='path'>The server and namespace for the <see cref='T:System.Management.ManagementScope'/>.</param>
            <param name=' options'>A <see cref='T:System.Management.ConnectionOptions'/> containing options for the connection.</param>
            <example>
               <code lang='C#'>ConnectionOptions opt = new ConnectionOptions();
            opt.Username = "Me";
            opt.Password = "MyPassword";
            ManagementScope s = new ManagementScope("\\\\MyServer\\root\\default", opt);
               </code>
               <code lang='VB'>Dim opt As New ConnectionOptions()
            opt.Username = "Me"
            opt.Password = "MyPassword"
            Dim s As New ManagementScope("\\MyServer\root\default", opt);
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementScope.#ctor(System.Management.ManagementPath,System.Management.ConnectionOptions)">
             <summary>
             <para>Initializes a new instance of the <see cref='T:System.Management.ManagementScope'/> class representing the specified scope path,
                with the specified options.</para>
             </summary>
             <param name='path'>A <see cref='T:System.Management.ManagementPath'/> containing the path to the server and namespace for the <see cref='T:System.Management.ManagementScope'/>.</param>
             <param name=' options'>The <see cref='T:System.Management.ConnectionOptions'/> containing options for the connection.</param>
             <example>
                <code lang='C#'>ConnectionOptions opt = new ConnectionOptions();
             opt.Username = "Me";
             opt.Password = "MyPassword";
            
             ManagementPath p = new ManagementPath("\\\\MyServer\\root\\default");
             ManagementScope = new ManagementScope(p, opt);
                </code>
                <code lang='VB'>Dim opt As New ConnectionOptions()
             opt.UserName = "Me"
             opt.Password = "MyPassword"
            
             Dim p As New ManagementPath("\\MyServer\root\default")
             Dim s As New ManagementScope(p, opt)
                </code>
             </example>
        </member>
        <member name="P:System.Management.ManagementScope.Options">
             <summary>
                <para> Gets or sets options for making the WMI connection.</para>
             </summary>
             <value>
             <para>The valid <see cref='T:System.Management.ConnectionOptions'/>
             containing options for the WMI connection.</para>
             </value>
             <example>
                <code lang='C#'>//This constructor creates a scope object with default options
             ManagementScope s = new ManagementScope("root\\MyApp");
            
             //Change default connection options -
             //In this example, set the system privileges to enabled for operations that require system privileges.
             s.Options.EnablePrivileges = true;
                </code>
                <code lang='VB'>'This constructor creates a scope object with default options
             Dim s As New ManagementScope("root\\MyApp")
            
             'Change default connection options -
             'In this example, set the system privileges to enabled for operations that require system privileges.
             s.Options.EnablePrivileges = True
                </code>
             </example>
        </member>
        <member name="P:System.Management.ManagementScope.Path">
            <summary>
            <para>Gets or sets the path for the <see cref='T:System.Management.ManagementScope'/>.</para>
            </summary>
            <value>
            <para> A <see cref='T:System.Management.ManagementPath'/> containing
               the path to a server and namespace.</para>
            </value>
            <example>
               <code lang='C#'>ManagementScope s = new ManagementScope();
            s.Path = new ManagementPath("root\\MyApp");
               </code>
               <code lang='VB'>Dim s As New ManagementScope()
            s.Path = New ManagementPath("root\MyApp")
               </code>
            </example>
        </member>
        <member name="M:System.Management.ManagementScope.Clone">
            <summary>
               <para>Returns a copy of the object.</para>
            </summary>
            <returns>
            <para>A new copy of the <see cref='T:System.Management.ManagementScope'/>.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementScope.System#ICloneable#Clone">
            <summary>
               <para>Clone a copy of this object.</para>
            </summary>
            <returns>
               A new copy of this object.
               object.
            </returns>
        </member>
        <member name="M:System.Management.ManagementScope.Connect">
             <summary>
             <para>Connects this <see cref='T:System.Management.ManagementScope'/> to the actual WMI
                scope.</para>
             </summary>
             <remarks>
                <para>This method is called implicitly when the
                   scope is used in an operation that requires it to be connected. Calling it
                   explicitly allows the user to control the time of connection.</para>
             </remarks>
             <example>
                <code lang='C#'>ManagementScope s = new ManagementScope("root\\MyApp");
            
             //Explicit call to connect the scope object to the WMI namespace
             s.Connect();
            
             //The following doesn't do any implicit scope connections because s is already connected.
             ManagementObject o = new ManagementObject(s, "Win32_LogicalDisk='C:'", null);
                </code>
                <code lang='VB'>Dim s As New ManagementScope("root\\MyApp")
            
             'Explicit call to connect the scope object to the WMI namespace
             s.Connect()
            
             'The following doesn't do any implicit scope connections because s is already connected.
             Dim o As New ManagementObject(s, "Win32_LogicalDisk=""C:""", null)
                </code>
             </example>
        </member>
        <member name="T:System.Management.ManagementScopeConverter">
            <summary>
            Converts a String to a ManagementScope
            </summary>
        </member>
        <member name="M:System.Management.ManagementScopeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Determines if this converter can convert an object in the given source type to the native type of the converter.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='sourceType'>A Type that represents the type you wish to convert from.</param>
            <returns>
               <para>true if this converter can perform the conversion; otherwise, false.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementScopeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Gets a value indicating whether this converter can convert an object to the given destination type using the context.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='destinationType'>A Type that represents the type you wish to convert to.</param>
            <returns>
               <para>true if this converter can perform the conversion; otherwise, false.</para>
            </returns>
        </member>
        <member name="M:System.Management.ManagementScopeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
                 Converts the given object to another type.  The most common types to convert
                 are to and from a string object.  The default implementation will make a call
                 to ToString on the object if the object is valid and if the destination
                 type is string.  If this cannot convert to the destination type, this will
                 throw a NotSupportedException.
            </summary>
            <param name='context'>An ITypeDescriptorContext that provides a format context.</param>
            <param name='culture'>A CultureInfo object. If a null reference (Nothing in Visual Basic) is passed, the current culture is assumed.</param>
            <param name='value'>The Object to convert.</param>
            <param name='destinationType'>The Type to convert the value parameter to.</param>
            <returns>An Object that represents the converted value.</returns>
        </member>
        <member name="T:System.Management.MethodData">
             <summary>
                <para> Contains information about a WMI method.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This example shows how to obtain meta data
             // about a WMI method with a given name in a given WMI class
            
             class Sample_MethodData
             {
                 public static int Main(string[] args) {
            
                     // Get the "SetPowerState" method in the Win32_LogicalDisk class
                     ManagementClass diskClass = new ManagementClass("win32_logicaldisk");
                     MethodData m = diskClass.Methods["SetPowerState"];
            
                     // Get method name (albeit we already know it)
                     Console.WriteLine("Name: " + m.Name);
            
                     // Get the name of the top-most class where this specific method was defined
                     Console.WriteLine("Origin: " + m.Origin);
            
                     // List names and types of input parameters
                     ManagementBaseObject inParams = m.InParameters;
                     foreach (PropertyData pdata in inParams.Properties) {
                         Console.WriteLine();
                         Console.WriteLine("InParam_Name: " + pdata.Name);
                         Console.WriteLine("InParam_Type: " + pdata.Type);
                     }
            
                     // List names and types of output parameters
                     ManagementBaseObject outParams = m.OutParameters;
                     foreach (PropertyData pdata in outParams.Properties) {
                         Console.WriteLine();
                         Console.WriteLine("OutParam_Name: " + pdata.Name);
                         Console.WriteLine("OutParam_Type: " + pdata.Type);
                     }
            
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This example shows how to obtain meta data
             ' about a WMI method with a given name in a given WMI class
            
             Class Sample_ManagementClass
                 Overloads Public Shared Function Main(args() As String) As Integer
            
                     ' Get the "SetPowerState" method in the Win32_LogicalDisk class
                     Dim diskClass As New ManagementClass("Win32_LogicalDisk")
                     Dim m As MethodData = diskClass.Methods("SetPowerState")
            
                     ' Get method name (albeit we already know it)
                     Console.WriteLine("Name: " &amp; m.Name)
            
                     ' Get the name of the top-most class where
                     ' this specific method was defined
                     Console.WriteLine("Origin: " &amp; m.Origin)
            
                     ' List names and types of input parameters
                     Dim inParams As ManagementBaseObject
                     inParams = m.InParameters
                     Dim pdata As PropertyData
                     For Each pdata In inParams.Properties
                         Console.WriteLine()
                         Console.WriteLine("InParam_Name: " &amp; pdata.Name)
                         Console.WriteLine("InParam_Type: " &amp; pdata.Type)
                     Next pdata
            
                     ' List names and types of output parameters
                     Dim outParams As ManagementBaseObject
                     outParams = m.OutParameters
                     For Each pdata in outParams.Properties
                         Console.WriteLine()
                         Console.WriteLine("OutParam_Name: " &amp; pdata.Name)
                         Console.WriteLine("OutParam_Type: " &amp; pdata.Type)
                     Next pdata
            
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.MethodData.Name">
            <summary>
               <para>Gets or sets the name of the method.</para>
            </summary>
            <value>
               <para>The name of the method.</para>
            </value>
        </member>
        <member name="P:System.Management.MethodData.InParameters">
            <summary>
               <para> Gets or sets the input parameters to the method. Each
                  parameter is described as a property in the object. If a parameter is both in
                  and out, it appears in both the <see cref='P:System.Management.MethodData.InParameters'/> and <see cref='P:System.Management.MethodData.OutParameters'/>
                  properties.</para>
            </summary>
            <value>
               <para>
                  A <see cref='T:System.Management.ManagementBaseObject'/>
                  containing all the input parameters to the
                  method.</para>
            </value>
            <remarks>
               <para>Each parameter in the object should have an
               <see langword='ID'/>
               qualifier, identifying the order of the parameters in the method call.</para>
            </remarks>
        </member>
        <member name="P:System.Management.MethodData.OutParameters">
            <summary>
               <para> Gets or sets the output parameters to the method. Each
                  parameter is described as a property in the object. If a parameter is both in
                  and out, it will appear in both the <see cref='P:System.Management.MethodData.InParameters'/> and <see cref='P:System.Management.MethodData.OutParameters'/>
                  properties.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.ManagementBaseObject'/> containing all the output parameters to the method. </para>
            </value>
            <remarks>
               <para>Each parameter in this object should have an
               <see langword='ID'/> qualifier to identify the
                  order of the parameters in the method call.</para>
               <para>The ReturnValue property is a special property of
                  the <see cref='P:System.Management.MethodData.OutParameters'/>
                  object and
                  holds the return value of the method.</para>
            </remarks>
        </member>
        <member name="P:System.Management.MethodData.Origin">
            <summary>
               <para>Gets the name of the management class in which the method was first
                  introduced in the class inheritance hierarchy.</para>
            </summary>
            <value>
               A string representing the originating
               management class name.
            </value>
        </member>
        <member name="P:System.Management.MethodData.Qualifiers">
            <summary>
               <para>Gets a collection of qualifiers defined in the
                  method. Each element is of type <see cref='T:System.Management.QualifierData'/>
                  and contains information such as the qualifier name, value, and
                  flavor.</para>
            </summary>
            <value>
               A <see cref='T:System.Management.QualifierDataCollection'/> containing the
               qualifiers for this method.
            </value>
            <seealso cref='T:System.Management.QualifierData'/>
        </member>
        <member name="T:System.Management.MethodDataCollection">
             <summary>
                <para> Represents the set of methods available in the collection.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates enumerate all methods in a ManagementClass object.
             class Sample_MethodDataCollection
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("win32_logicaldisk");
                     MethodDataCollection diskMethods = diskClass.Methods;
                     foreach (MethodData method in diskMethods) {
                         Console.WriteLine("Method = " + method.Name);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates enumerate all methods in a ManagementClass object.
             Class Sample_MethodDataCollection
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim diskClass As New ManagementClass("win32_logicaldisk")
                     Dim diskMethods As MethodDataCollection = diskClass.Methods
                     Dim method As MethodData
                     For Each method In diskMethods
                         Console.WriteLine("Method = " &amp; method.Name)
                     Next method
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.MethodDataCollection.Count">
            <summary>
            <para>Represents the number of objects in the <see cref='T:System.Management.MethodDataCollection'/>.</para>
            </summary>
            <value>
            <para> The number of objects in the <see cref='T:System.Management.MethodDataCollection'/>. </para>
            </value>
        </member>
        <member name="P:System.Management.MethodDataCollection.IsSynchronized">
            <summary>
               <para>Indicates whether the object is synchronized.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the object is synchronized;
               otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.MethodDataCollection.SyncRoot">
            <summary>
               <para>Represents the object to be used for synchronization.</para>
            </summary>
            <value>
               <para>The object to be used for synchronization.</para>
            </value>
        </member>
        <member name="M:System.Management.MethodDataCollection.CopyTo(System.Array,System.Int32)">
            <overload>
            <para>Copies the <see cref='T:System.Management.MethodDataCollection'/> into an array.</para>
            </overload>
            <summary>
            <para> Copies the <see cref='T:System.Management.MethodDataCollection'/> into an array.</para>
            </summary>
            <param name='array'>The array to which to copy the collection. </param>
            <param name='index'>The index from which to start. </param>
        </member>
        <member name="M:System.Management.MethodDataCollection.CopyTo(System.Management.MethodData[],System.Int32)">
            <summary>
            <para>Copies the <see cref='T:System.Management.MethodDataCollection'/> to a specialized <see cref='T:System.Management.MethodData'/>
            array.</para>
            </summary>
            <param name='methodArray'>The destination array to which to copy the <see cref='T:System.Management.MethodData'/> objects.</param>
            <param name=' index'>The index in the destination array from which to start the copy.</param>
        </member>
        <member name="M:System.Management.MethodDataCollection.GetEnumerator">
            <summary>
            <para>Returns an enumerator for the <see cref='T:System.Management.MethodDataCollection'/>.</para>
            </summary>
            <remarks>
               <para> Each call to this method
                  returns a new enumerator on the collection. Multiple enumerators can be obtained
                  for the same method collection. However, each enumerator takes a snapshot
                  of the collection, so changes made to the collection after the enumerator was
                  obtained are not reflected.</para>
            </remarks>
            <returns>An <see cref="T:System.Collections.IEnumerator"/> to enumerate through the collection.</returns>
        </member>
        <member name="T:System.Management.MethodDataCollection.MethodDataEnumerator">
             <summary>
             <para>Represents the enumerator for <see cref='T:System.Management.MethodData'/>
             objects in the <see cref='T:System.Management.MethodDataCollection'/>.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to enumerate all methods in
             // Win32_LogicalDisk class using MethodDataEnumerator object.
            
             class Sample_MethodDataEnumerator
             {
              public static int Main(string[] args)
              {
               ManagementClass diskClass = new ManagementClass("win32_logicaldisk");
               MethodDataCollection.MethodDataEnumerator diskEnumerator =
                diskClass.Methods.GetEnumerator();
               while(diskEnumerator.MoveNext())
               {
                MethodData method = diskEnumerator.Current;
                Console.WriteLine("Method = " + method.Name);
               }
               return 0;
              }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to enumerate all methods in
             ' Win32_LogicalDisk class using MethodDataEnumerator object.
            
             Class Sample_MethodDataEnumerator
              Overloads Public Shared Function Main(args() As String) As Integer
               Dim diskClass As New ManagementClass("win32_logicaldisk")
               Dim diskEnumerator As _
                    MethodDataCollection.MethodDataEnumerator = _
                   diskClass.Methods.GetEnumerator()
               While diskEnumerator.MoveNext()
                Dim method As MethodData = diskEnumerator.Current
                Console.WriteLine("Method = " &amp; method.Name)
               End While
               Return 0
              End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.MethodDataCollection.MethodDataEnumerator.System#Collections#IEnumerator#Current">
            <internalonly/>
        </member>
        <member name="P:System.Management.MethodDataCollection.MethodDataEnumerator.Current">
            <summary>
            <para>Returns the current <see cref='T:System.Management.MethodData'/> in the <see cref='T:System.Management.MethodDataCollection'/>
            enumeration.</para>
            </summary>
            <value>The current <see cref='T:System.Management.MethodData'/> item in the collection.</value>
        </member>
        <member name="M:System.Management.MethodDataCollection.MethodDataEnumerator.MoveNext">
            <summary>
            <para>Moves to the next element in the <see cref='T:System.Management.MethodDataCollection'/> enumeration.</para>
            </summary>
            <returns><see langword='true'/> if the enumerator was successfully advanced to the next method; <see langword='false'/> if the enumerator has passed the end of the collection.</returns>
        </member>
        <member name="M:System.Management.MethodDataCollection.MethodDataEnumerator.Reset">
            <summary>
            <para>Resets the enumerator to the beginning of the <see cref='T:System.Management.MethodDataCollection'/> enumeration.</para>
            </summary>
        </member>
        <member name="P:System.Management.MethodDataCollection.Item(System.String)">
            <summary>
            <para>Returns the specified <see cref='T:System.Management.MethodData'/> from the <see cref='T:System.Management.MethodDataCollection'/>.</para>
            </summary>
            <param name='methodName'>The name of the method requested.</param>
            <value>A <see cref='T:System.Management.MethodData'/> instance containing all information about the specified method.</value>
        </member>
        <member name="M:System.Management.MethodDataCollection.Remove(System.String)">
            <summary>
            <para>Removes a <see cref='T:System.Management.MethodData'/> from the <see cref='T:System.Management.MethodDataCollection'/>.</para>
            </summary>
            <param name='methodName'>The name of the method to remove from the collection.</param>
            <remarks>
               <para>
                  Removing <see cref='T:System.Management.MethodData'/> objects from the <see cref='T:System.Management.MethodDataCollection'/>
                  can only be done when the class has no
                  instances. Any other case will result in an exception.</para>
            </remarks>
        </member>
        <member name="M:System.Management.MethodDataCollection.Add(System.String)">
            <overload>
            <para>Adds a <see cref='T:System.Management.MethodData'/> to the <see cref='T:System.Management.MethodDataCollection'/>.</para>
            </overload>
            <summary>
            <para>Adds a <see cref='T:System.Management.MethodData'/> to the <see cref='T:System.Management.MethodDataCollection'/>. This overload will
               add a new method with no parameters to the collection.</para>
            </summary>
            <param name='methodName'>The name of the method to add.</param>
            <remarks>
            <para> Adding <see cref='T:System.Management.MethodData'/> objects to the <see cref='T:System.Management.MethodDataCollection'/> can only
               be done when the class has no instances. Any other case will result in an
               exception.</para>
            </remarks>
        </member>
        <member name="M:System.Management.MethodDataCollection.Add(System.String,System.Management.ManagementBaseObject,System.Management.ManagementBaseObject)">
            <summary>
            <para>Adds a <see cref='T:System.Management.MethodData'/> to the <see cref='T:System.Management.MethodDataCollection'/>. This overload will add a new method with the
               specified parameter objects to the collection.</para>
            </summary>
            <param name='methodName'>The name of the method to add.</param>
            <param name=' inParameters'>The <see cref='T:System.Management.ManagementBaseObject'/> holding the input parameters to the method.</param>
            <param name=' outParameters'>The <see cref='T:System.Management.ManagementBaseObject'/> holding the output parameters to the method.</param>
            <remarks>
            <para> Adding <see cref='T:System.Management.MethodData'/> objects to the <see cref='T:System.Management.MethodDataCollection'/> can only be
               done when the class has no instances. Any other case will result in an
               exception.</para>
            </remarks>
        </member>
        <member name="T:System.Management.PropertyData">
             <summary>
                <para> Represents information about a WMI property.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample displays all properties that qualifies the "DeviceID" property
             // in Win32_LogicalDisk.DeviceID='C' instance.
             class Sample_PropertyData
             {
                 public static int Main(string[] args) {
                     ManagementObject disk =
                         new ManagementObject("Win32_LogicalDisk.DeviceID=\"C:\"");
                     PropertyData diskProperty = disk.Properties["DeviceID"];
                     Console.WriteLine("Name: " + diskProperty.Name);
                     Console.WriteLine("Type: " + diskProperty.Type);
                     Console.WriteLine("Value: " + diskProperty.Value);
                     Console.WriteLine("IsArray: " + diskProperty.IsArray);
                     Console.WriteLine("IsLocal: " + diskProperty.IsLocal);
                     Console.WriteLine("Origin: " + diskProperty.Origin);
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample displays all properties that qualifies the "DeviceID" property
             ' in Win32_LogicalDisk.DeviceID='C' instance.
             Class Sample_PropertyData
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim disk As New ManagementObject("Win32_LogicalDisk.DeviceID=""C:""")
                     Dim diskProperty As PropertyData = disk.Properties("DeviceID")
                     Console.WriteLine("Name: " &amp; diskProperty.Name)
                     Console.WriteLine("Type: " &amp; diskProperty.Type)
                     Console.WriteLine("Value: " &amp; diskProperty.Value)
                     Console.WriteLine("IsArray: " &amp; diskProperty.IsArray)
                     Console.WriteLine("IsLocal: " &amp; diskProperty.IsLocal)
                     Console.WriteLine("Origin: " &amp; diskProperty.Origin)
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.PropertyData.Name">
            <summary>
               <para>Gets or sets the name of the property.</para>
            </summary>
            <value>
               A string containing the name of the
               property.
            </value>
        </member>
        <member name="P:System.Management.PropertyData.Value">
            <summary>
               <para>Gets or sets the current value of the property.</para>
            </summary>
            <value>
               An object containing the value of the
               property.
            </value>
        </member>
        <member name="P:System.Management.PropertyData.Type">
            <summary>
               <para>Gets or sets the CIM type of the property.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.CimType'/> value
               representing the CIM type of the property.</para>
            </value>
        </member>
        <member name="P:System.Management.PropertyData.IsLocal">
            <summary>
               <para>Gets or sets a value indicating whether the property has been defined in the current WMI class.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the property has been defined
               in the current WMI class; otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.PropertyData.IsArray">
            <summary>
               <para>Gets or sets a value indicating whether the property is an array.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the property is an array; otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.PropertyData.Origin">
            <summary>
               <para>Gets or sets the name of the WMI class in the hierarchy in which the property was introduced.</para>
            </summary>
            <value>
               A string containing the name of the
               originating WMI class.
            </value>
        </member>
        <member name="P:System.Management.PropertyData.Qualifiers">
            <summary>
               <para>Gets or sets the set of qualifiers defined on the property.</para>
            </summary>
            <value>
            <para>A <see cref='T:System.Management.QualifierDataCollection'/> that represents
               the set of qualifiers defined on the property.</para>
            </value>
        </member>
        <member name="M:System.Management.PropertyData.MapWmiValueToValue(System.Object,System.Management.CimType,System.Boolean)">
            <summary>
            Takes a property value returned from WMI and maps it to an
            appropriate managed code representation.
            </summary>
            <param name="wmiValue"> </param>
            <param name="type"> </param>
            <param name="isArray"> </param>
        </member>
        <member name="M:System.Management.PropertyData.MapValueToWmiValue(System.Object,System.Management.CimType,System.Boolean)">
            <summary>
            Takes a managed code value, together with a desired property
            </summary>
            <param name="val"> </param>
            <param name="type"> </param>
            <param name="isArray"> </param>
        </member>
        <member name="T:System.Management.PropertyDataCollection">
             <summary>
                <para> Represents the set of properties of a WMI object.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to enumerate properties
             // in a ManagementObject object.
             class Sample_PropertyDataCollection
             {
                 public static int Main(string[] args) {
                     ManagementObject disk = new ManagementObject("win32_logicaldisk.deviceid = \"c:\"");
                     PropertyDataCollection diskProperties = disk.Properties;
                     foreach (PropertyData diskProperty in diskProperties) {
                         Console.WriteLine("Property = " + diskProperty.Name);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to enumerate properties
             ' in a ManagementObject object.
             Class Sample_PropertyDataCollection
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim disk As New ManagementObject("win32_logicaldisk.deviceid=""c:""")
                     Dim diskProperties As PropertyDataCollection = disk.Properties
                     Dim diskProperty As PropertyData
                     For Each diskProperty In diskProperties
                         Console.WriteLine("Property = " &amp; diskProperty.Name)
                     Next diskProperty
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.PropertyDataCollection.Count">
            <summary>
            <para>Gets or sets the number of objects in the <see cref='T:System.Management.PropertyDataCollection'/>.</para>
            </summary>
            <value>
               <para>The number of objects in the collection.</para>
            </value>
        </member>
        <member name="P:System.Management.PropertyDataCollection.IsSynchronized">
            <summary>
               <para>Gets or sets a value indicating whether the object is synchronized.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the object is synchronized;
               otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.PropertyDataCollection.SyncRoot">
            <summary>
               <para>Gets or sets the object to be used for synchronization.</para>
            </summary>
            <value>
               <para>The object to be used for synchronization.</para>
            </value>
        </member>
        <member name="M:System.Management.PropertyDataCollection.CopyTo(System.Array,System.Int32)">
            <overload>
            <para>Copies the <see cref='T:System.Management.PropertyDataCollection'/> into an array.</para>
            </overload>
            <summary>
            <para>Copies the <see cref='T:System.Management.PropertyDataCollection'/> into an array.</para>
            </summary>
            <param name='array'>The array to which to copy the <see cref='T:System.Management.PropertyDataCollection'/>. </param>
            <param name='index'>The index from which to start copying. </param>
        </member>
        <member name="M:System.Management.PropertyDataCollection.CopyTo(System.Management.PropertyData[],System.Int32)">
            <summary>
            <para>Copies the <see cref='T:System.Management.PropertyDataCollection'/> to a specialized <see cref='T:System.Management.PropertyData'/> object
               array.</para>
            </summary>
            <param name='propertyArray'>The destination array to contain the copied <see cref='T:System.Management.PropertyDataCollection'/>.</param>
            <param name=' index'>The index in the destination array from which to start copying.</param>
        </member>
        <member name="M:System.Management.PropertyDataCollection.GetEnumerator">
            <summary>
            <para>Returns the enumerator for this <see cref='T:System.Management.PropertyDataCollection'/>.</para>
            </summary>
            <returns>
            <para>An <see cref='T:System.Collections.IEnumerator'/>
            that can be used to iterate through the collection.</para>
            </returns>
        </member>
        <member name="T:System.Management.PropertyDataCollection.PropertyDataEnumerator">
             <summary>
             <para>Represents the enumerator for <see cref='T:System.Management.PropertyData'/>
             objects in the <see cref='T:System.Management.PropertyDataCollection'/>.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to enumerate all properties in a
             // ManagementObject using the PropertyDataEnumerator object.
             class Sample_PropertyDataEnumerator
             {
                 public static int Main(string[] args) {
                     ManagementObject disk = new ManagementObject("Win32_LogicalDisk.DeviceID='C:'");
                     PropertyDataCollection.PropertyDataEnumerator propertyEnumerator = disk.Properties.GetEnumerator();
                     while(propertyEnumerator.MoveNext()) {
                         PropertyData p = (PropertyData)propertyEnumerator.Current;
                         Console.WriteLine("Property found: " + p.Name);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to enumerate all properties in a
             ' ManagementObject using PropertyDataEnumerator object.
             Class Sample_PropertyDataEnumerator
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim disk As New ManagementObject("Win32_LogicalDisk.DeviceID='C:'")
                     Dim propertyEnumerator As _
                       PropertyDataCollection.PropertyDataEnumerator = disk.Properties.GetEnumerator()
                     While propertyEnumerator.MoveNext()
                         Dim p As PropertyData = _
                             CType(propertyEnumerator.Current, PropertyData)
                         Console.WriteLine("Property found: " &amp; p.Name)
                      End While
                      Return 0
                  End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.PropertyDataCollection.PropertyDataEnumerator.System#Collections#IEnumerator#Current">
            <internalonly/>
        </member>
        <member name="P:System.Management.PropertyDataCollection.PropertyDataEnumerator.Current">
            <summary>
            <para>Gets the current <see cref='T:System.Management.PropertyData'/> in the <see cref='T:System.Management.PropertyDataCollection'/> enumeration.</para>
            </summary>
            <value>
               The current <see cref='T:System.Management.PropertyData'/>
               element in the collection.
            </value>
        </member>
        <member name="M:System.Management.PropertyDataCollection.PropertyDataEnumerator.MoveNext">
            <summary>
            <para> Moves to the next element in the <see cref='T:System.Management.PropertyDataCollection'/>
            enumeration.</para>
            </summary>
            <returns>
            <para><see langword='true'/> if the enumerator was successfully advanced to the next element;
            <see langword='false'/> if the enumerator has passed the end of the collection.</para>
            </returns>
        </member>
        <member name="M:System.Management.PropertyDataCollection.PropertyDataEnumerator.Reset">
            <summary>
            <para>Resets the enumerator to the beginning of the <see cref='T:System.Management.PropertyDataCollection'/>
            enumeration.</para>
            </summary>
        </member>
        <member name="P:System.Management.PropertyDataCollection.Item(System.String)">
            <summary>
            <para> Returns the specified property from the <see cref='T:System.Management.PropertyDataCollection'/>, using [] syntax.</para>
            </summary>
            <param name='propertyName'>The name of the property to retrieve.</param>
            <value>
            <para> A <see cref='T:System.Management.PropertyData'/>, based on
               the name specified.</para>
            </value>
            <example>
               <code lang='C#'>ManagementObject o = new ManagementObject("Win32_LogicalDisk.Name = 'C:'");
            Console.WriteLine("Free space on C: drive is: ", c.Properties["FreeSpace"].Value);
               </code>
               <code lang='VB'>Dim o As New ManagementObject("Win32_LogicalDisk.Name=""C:""")
            Console.WriteLine("Free space on C: drive is: " &amp; c.Properties("FreeSpace").Value)
               </code>
            </example>
        </member>
        <member name="M:System.Management.PropertyDataCollection.Remove(System.String)">
            <summary>
            <para>Removes a <see cref='T:System.Management.PropertyData'/> from the <see cref='T:System.Management.PropertyDataCollection'/>.</para>
            </summary>
            <param name='propertyName'>The name of the property to be removed.</param>
            <remarks>
               <para> Properties can only be removed from class definitions,
                  not from instances. This method is only valid when invoked on a property
                  collection in a <see cref='T:System.Management.ManagementClass'/>.</para>
            </remarks>
            <example>
               <code lang='C#'>ManagementClass c = new ManagementClass("MyClass");
            c.Properties.Remove("PropThatIDontWantOnThisClass");
               </code>
               <code lang='VB'>Dim c As New ManagementClass("MyClass")
            c.Properties.Remove("PropThatIDontWantOnThisClass")
               </code>
            </example>
        </member>
        <member name="M:System.Management.PropertyDataCollection.Add(System.String,System.Object)">
            <overload>
            <para>Adds a new <see cref='T:System.Management.PropertyData'/> with the specified value.</para>
            </overload>
            <summary>
            <para>Adds a new <see cref='T:System.Management.PropertyData'/> with the specified value. The value cannot
               be null and must be convertible to a CIM type.</para>
            </summary>
            <param name='propertyName'>The name of the new property.</param>
            <param name='propertyValue'>The value of the property (cannot be null).</param>
            <remarks>
               <para> Properties can only be added to class definitions, not
                  to instances. This method is only valid when invoked on a <see cref='T:System.Management.PropertyDataCollection'/>
                  in
                  a <see cref='T:System.Management.ManagementClass'/>.</para>
            </remarks>
        </member>
        <member name="M:System.Management.PropertyDataCollection.Add(System.String,System.Object,System.Management.CimType)">
            <summary>
            <para>Adds a new <see cref='T:System.Management.PropertyData'/> with the specified value and CIM type.</para>
            </summary>
            <param name='propertyName'>The name of the property.</param>
            <param name='propertyValue'>The value of the property (which can be null).</param>
            <param name='propertyType'>The CIM type of the property.</param>
            <remarks>
               <para> Properties can only be added to class definitions, not
                  to instances. This method is only valid when invoked on a <see cref='T:System.Management.PropertyDataCollection'/>
                  in
                  a <see cref='T:System.Management.ManagementClass'/>.</para>
            </remarks>
        </member>
        <member name="M:System.Management.PropertyDataCollection.Add(System.String,System.Management.CimType,System.Boolean)">
            <summary>
            <para>Adds a new <see cref='T:System.Management.PropertyData'/> with no assigned value.</para>
            </summary>
            <param name='propertyName'>The name of the property.</param>
            <param name='propertyType'>The CIM type of the property.</param>
            <param name='isArray'><see langword='true'/> to specify that the property is an array type; otherwise, <see langword='false'/>.</param>
            <remarks>
               <para> Properties can only be added to class definitions, not
                  to instances. This method is only valid when invoked on a <see cref='T:System.Management.PropertyDataCollection'/>
                  in
                  a <see cref='T:System.Management.ManagementClass'/>.</para>
            </remarks>
        </member>
        <member name="T:System.Management.QualifierData">
             <summary>
                <para> Contains information about a WMI qualifier.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to enumerate qualifiers
             // of a ManagementClass object.
             class Sample_QualifierData
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("Win32_LogicalDisk");
                     diskClass.Options.UseAmendedQualifiers = true;
                     QualifierData diskQualifier = diskClass.Qualifiers["Description"];
                     Console.WriteLine(diskQualifier.Name + " = " + diskQualifier.Value);
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to enumerate qualifiers
             ' of a ManagementClass object.
             Class Sample_QualifierData
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim diskClass As New ManagementClass("win32_logicaldisk")
                     diskClass.Options.UseAmendedQualifiers = True
                     Dim diskQualifier As QualifierData = diskClass.Qualifiers("Description")
                     Console.WriteLine(diskQualifier.Name + " = " + diskQualifier.Value)
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.QualifierData.Name">
            <summary>
               <para>Represents the name of the qualifier.</para>
            </summary>
            <value>
               <para>The name of the qualifier.</para>
            </value>
        </member>
        <member name="P:System.Management.QualifierData.Value">
            <summary>
               <para>Gets or sets the value of the qualifier.</para>
            </summary>
            <value>
               <para>The value of the qualifier.</para>
            </value>
            <remarks>
               <para> Qualifiers can only be of the following subset of CIM
                  types: <see langword='string'/>, <see langword='uint16'/>,
               <see langword='uint32'/>, <see langword='sint32'/>, <see langword='uint64'/>,
               <see langword='sint64'/>, <see langword='real32'/>, <see langword='real64'/>,
               <see langword='bool'/>.
                  </para>
            </remarks>
        </member>
        <member name="P:System.Management.QualifierData.IsAmended">
            <summary>
               <para> Gets or sets a value indicating whether the qualifier is amended.</para>
            </summary>
            <value>
            <para><see langword='true'/> if this qualifier is amended;
               otherwise, <see langword='false'/>.</para>
            </value>
            <remarks>
               <para> Amended qualifiers are
                  qualifiers whose value can be localized through WMI. Localized qualifiers
                  reside in separate namespaces in WMI and can be merged into the basic class
                  definition when retrieved.</para>
            </remarks>
        </member>
        <member name="P:System.Management.QualifierData.IsLocal">
            <summary>
               <para>Gets or sets a value indicating whether the qualifier has been defined locally on
                  this class or has been propagated from a base class.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the qualifier has been defined
               locally on this class; otherwise, <see langword='false'/>. </para>
            </value>
        </member>
        <member name="P:System.Management.QualifierData.PropagatesToInstance">
            <summary>
               <para>Gets or sets a value indicating whether the qualifier should be propagated to instances of the
                  class.</para>
            </summary>
            <value>
            <para><see langword='true'/> if this qualifier should be
               propagated to instances of the class; otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.QualifierData.PropagatesToSubclass">
            <summary>
               <para>Gets or sets a value indicating whether the qualifier should be propagated to
                  subclasses of the class.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the qualifier should be
               propagated to subclasses of this class; otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.QualifierData.IsOverridable">
            <summary>
               <para>Gets or sets a value indicating whether the value of the qualifier can be
                  overridden when propagated.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the value of the qualifier
               can be overridden when propagated; otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="T:System.Management.QualifierDataCollection">
             <summary>
             <para> Represents a collection of <see cref='T:System.Management.QualifierData'/> objects.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to list all qualifiers including amended
             // qualifiers of a ManagementClass object.
             class Sample_QualifierDataCollection
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("Win32_LogicalDisk");
                     diskClass.Options.UseAmendedQualifiers = true;
                     QualifierDataCollection qualifierCollection = diskClass.Qualifiers;
                     foreach (QualifierData q in qualifierCollection) {
                         Console.WriteLine(q.Name + " = " + q.Value);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
                   Imports System.Management
                   ' This sample demonstrates how to list all qualifiers including amended
                   ' qualifiers of a ManagementClass object.
                   Class Sample_QualifierDataCollection
                   Overloads Public Shared Function Main(args() As String) As Integer
                   Dim diskClass As New ManagementClass("Win32_LogicalDisk")
                   diskClass.Options.UseAmendedQualifiers = true
                   Dim qualifierCollection As QualifierDataCollection = diskClass.Qualifiers
                   Dim q As QualifierData
                   For Each q In qualifierCollection
                   Console.WriteLine(q.Name &amp; " = " &amp; q.Value)
                   Next q
                   Return 0
                   End Function
                   End Class
                </code>
             </example>
        </member>
        <member name="M:System.Management.QualifierDataCollection.GetTypeQualifierSet">
            <summary>
            Return the qualifier set associated with its type
            Overload with use of private data member, qualifierType
            </summary>
        </member>
        <member name="M:System.Management.QualifierDataCollection.GetTypeQualifierSet(System.Management.QualifierType)">
            <summary>
            Return the qualifier set associated with its type
            </summary>
        </member>
        <member name="P:System.Management.QualifierDataCollection.Count">
            <summary>
            <para>Gets or sets the number of <see cref='T:System.Management.QualifierData'/> objects in the <see cref='T:System.Management.QualifierDataCollection'/>.</para>
            </summary>
            <value>
               <para>The number of objects in the collection.</para>
            </value>
        </member>
        <member name="P:System.Management.QualifierDataCollection.IsSynchronized">
            <summary>
               <para>Gets or sets a value indicating whether the object is synchronized.</para>
            </summary>
            <value>
            <para><see langword='true'/> if the object is synchronized;
               otherwise, <see langword='false'/>.</para>
            </value>
        </member>
        <member name="P:System.Management.QualifierDataCollection.SyncRoot">
            <summary>
               <para>Gets or sets the object to be used for synchronization.</para>
            </summary>
            <value>
               <para>The object to be used for synchronization.</para>
            </value>
        </member>
        <member name="M:System.Management.QualifierDataCollection.CopyTo(System.Array,System.Int32)">
            <overload>
            <para>Copies the <see cref='T:System.Management.QualifierDataCollection'/> into an array.</para>
            </overload>
            <summary>
            <para> Copies the <see cref='T:System.Management.QualifierDataCollection'/> into an array.</para>
            </summary>
            <param name='array'>The array to which to copy the <see cref='T:System.Management.QualifierDataCollection'/>. </param>
            <param name='index'>The index from which to start copying. </param>
        </member>
        <member name="M:System.Management.QualifierDataCollection.CopyTo(System.Management.QualifierData[],System.Int32)">
            <summary>
            <para>Copies the <see cref='T:System.Management.QualifierDataCollection'/> into a specialized
            <see cref='T:System.Management.QualifierData'/>
            array.</para>
            </summary>
            <param name='qualifierArray'><para>The specialized array of <see cref='T:System.Management.QualifierData'/> objects
            to which to copy the <see cref='T:System.Management.QualifierDataCollection'/>.</para></param>
            <param name=' index'>The index from which to start copying.</param>
        </member>
        <member name="M:System.Management.QualifierDataCollection.GetEnumerator">
            <summary>
            <para>Returns an enumerator for the <see cref='T:System.Management.QualifierDataCollection'/>. This method is strongly typed.</para>
            </summary>
            <returns>
            <para>An <see cref='T:System.Collections.IEnumerator'/> that can be used to iterate through the
               collection.</para>
            </returns>
        </member>
        <member name="T:System.Management.QualifierDataCollection.QualifierDataEnumerator">
             <summary>
             <para>Represents the enumerator for <see cref='T:System.Management.QualifierData'/>
             objects in the <see cref='T:System.Management.QualifierDataCollection'/>.</para>
             </summary>
             <example>
                <code lang='C#'>using System;
             using System.Management;
            
             // This sample demonstrates how to enumerate qualifiers of a ManagementClass
             // using QualifierDataEnumerator object.
             class Sample_QualifierDataEnumerator
             {
                 public static int Main(string[] args) {
                     ManagementClass diskClass = new ManagementClass("Win32_LogicalDisk");
                     diskClass.Options.UseAmendedQualifiers = true;
                     QualifierDataCollection diskQualifier = diskClass.Qualifiers;
                     QualifierDataCollection.QualifierDataEnumerator
                         qualifierEnumerator = diskQualifier.GetEnumerator();
                     while(qualifierEnumerator.MoveNext()) {
                         Console.WriteLine(qualifierEnumerator.Current.Name + " = " +
                             qualifierEnumerator.Current.Value);
                     }
                     return 0;
                 }
             }
                </code>
                <code lang='VB'>Imports System
             Imports System.Management
            
             ' This sample demonstrates how to enumerate qualifiers of a ManagementClass
             ' using QualifierDataEnumerator object.
             Class Sample_QualifierDataEnumerator
                 Overloads Public Shared Function Main(args() As String) As Integer
                     Dim diskClass As New ManagementClass("win32_logicaldisk")
                     diskClass.Options.UseAmendedQualifiers = True
                     Dim diskQualifier As QualifierDataCollection = diskClass.Qualifiers
                     Dim qualifierEnumerator As _
                         QualifierDataCollection.QualifierDataEnumerator = _
                             diskQualifier.GetEnumerator()
                     While qualifierEnumerator.MoveNext()
                         Console.WriteLine(qualifierEnumerator.Current.Name &amp; _
                             " = " &amp; qualifierEnumerator.Current.Value)
                     End While
                     Return 0
                 End Function
             End Class
                </code>
             </example>
        </member>
        <member name="P:System.Management.QualifierDataCollection.QualifierDataEnumerator.System#Collections#IEnumerator#Current">
            <internalonly/>
        </member>
        <member name="P:System.Management.QualifierDataCollection.QualifierDataEnumerator.Current">
            <summary>
            <para>Gets or sets the current <see cref='T:System.Management.QualifierData'/> in the <see cref='T:System.Management.QualifierDataCollection'/> enumeration.</para>
            </summary>
            <value>
            <para>The current <see cref='T:System.Management.QualifierData'/> element in the collection.</para>
            </value>
        </member>
        <member name="M:System.Management.QualifierDataCollection.QualifierDataEnumerator.MoveNext">
            <summary>
            <para> Moves to the next element in the <see cref='T:System.Management.QualifierDataCollection'/> enumeration.</para>
            </summary>
            <returns>
            <para><see langword='true'/> if the enumerator was successfully advanced to the next
               element; <see langword='false'/> if the enumerator has passed the end of the
               collection.</para>
            </returns>
        </member>
        <member name="M:System.Management.QualifierDataCollection.QualifierDataEnumerator.Reset">
            <summary>
            <para>Resets the enumerator to the beginning of the <see cref='T:System.Management.QualifierDataCollection'/> enumeration.</para>
            </summary>
        </member>
        <member name="P:System.Management.QualifierDataCollection.Item(System.String)">
            <summary>
            <para> Gets the specified <see cref='T:System.Management.QualifierData'/> from the <see cref='T:System.Management.QualifierDataCollection'/>.</para>
            </summary>
            <param name='qualifierName'>The name of the <see cref='T:System.Management.QualifierData'/> to access in the <see cref='T:System.Management.QualifierDataCollection'/>. </param>
            <value>
            <para>A <see cref='T:System.Management.QualifierData'/>, based on the name specified.</para>
            </value>
        </member>
        <member name="M:System.Management.QualifierDataCollection.Remove(System.String)">
            <summary>
            <para>Removes a <see cref='T:System.Management.QualifierData'/> from the <see cref='T:System.Management.QualifierDataCollection'/> by name.</para>
            </summary>
            <param name='qualifierName'>The name of the <see cref='T:System.Management.QualifierData'/> to remove. </param>
        </member>
        <member name="M:System.Management.QualifierDataCollection.Add(System.String,System.Object)">
            <overload>
            <para>Adds a <see cref='T:System.Management.QualifierData'/> to the <see cref='T:System.Management.QualifierDataCollection'/>.</para>
            </overload>
            <summary>
            <para>Adds a <see cref='T:System.Management.QualifierData'/> to the <see cref='T:System.Management.QualifierDataCollection'/>. This overload specifies the qualifier name and value.</para>
            </summary>
            <param name='qualifierName'>The name of the <see cref='T:System.Management.QualifierData'/> to be added to the <see cref='T:System.Management.QualifierDataCollection'/>. </param>
            <param name='qualifierValue'>The value for the new qualifier. </param>
        </member>
        <member name="M:System.Management.QualifierDataCollection.Add(System.String,System.Object,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            <para>Adds a <see cref='T:System.Management.QualifierData'/> to the <see cref='T:System.Management.QualifierDataCollection'/>. This overload
               specifies all property values for a <see cref='T:System.Management.QualifierData'/> object.</para>
            </summary>
            <param name='qualifierName'>The qualifier name. </param>
            <param name='qualifierValue'>The qualifier value. </param>
            <param name='isAmended'><see langword='true'/> to specify that this qualifier is amended (flavor); otherwise, <see langword='false'/>. </param>
            <param name='propagatesToInstance'><see langword='true'/> to propagate this qualifier to instances; otherwise, <see langword='false'/>. </param>
            <param name='propagatesToSubclass'><see langword='true'/> to propagate this qualifier to subclasses; otherwise, <see langword='false'/>. </param>
            <param name='isOverridable'><see langword='true'/> to specify that this qualifier's value is overridable in instances of subclasses; otherwise, <see langword='false'/>. </param>
        </member>
        <member name="T:System.Management.CodeLanguage">
            <summary>
                <para>Defines the languages supported by the code generator.</para>
            </summary>
        </member>
        <member name="F:System.Management.CodeLanguage.CSharp">
            <summary>
               A value for generating C# code.
            </summary>
        </member>
        <member name="F:System.Management.CodeLanguage.JScript">
            <summary>
               <para>A value for generating JScript code.</para>
            </summary>
        </member>
        <member name="F:System.Management.CodeLanguage.VB">
            <summary>
               <para>A value for generating Visual Basic code.</para>
            </summary>
        </member>
        <member name="F:System.Management.CodeLanguage.VJSharp">
            <summary>
               <para>A value for generating Visual J# code.</para>
            </summary>
        </member>
        <member name="F:System.Management.CodeLanguage.Mcpp">
            <summary>
               <para>A value for generating Managed C++ code.</para>
            </summary>
        </member>
        <member name="T:System.Management.ManagementClassGenerator">
            <summary>
               Used to generate a strongly-typed code class for a given WMI class.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.#ctor">
            <summary>
               <para>Creates an empty generator object. This is the default constructor.</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.#ctor(System.Management.ManagementClass)">
            <summary>
               <para>Creates a generator object and initializes it
                with the specified <see cref="T:System.Management.ManagementClass"/>.</para>
            </summary>
            <param name='cls'><see cref="T:System.Management.ManagementClass"/> object for which the code is to be generated.</param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateCode(System.Boolean,System.Boolean)">
            <summary>
               <para>
                  Returns a <see cref="T:System.CodeDom.CodeTypeDeclaration"/> for
                  this class.</para>
            </summary>
            <param name='includeSystemProperties'>Indicates if a class for handling system properties should be included.</param>
            <param name='systemPropertyClass'>Indicates if the generated code is for a class that handles system properties.</param>
            <returns>
               <para>Returns the <see cref="T:System.CodeDom.CodeTypeDeclaration"/> for the WMI class.</para>
            </returns>
            <remarks>
               <para>If includeSystemProperties is <see langword="true"/>,
                  the ManagementSystemProperties class is included in the generated class definition.
                  This parameter is ignored if systemPropertyClass is <see langword="true"/>.</para>
            </remarks>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateCode(System.Management.CodeLanguage,System.String,System.String)">
            <summary>
            Generates a strongly-typed code class for the specified language provider (C#, Visual Basic or JScript)
            and writes it to the specified file.
            </summary>
            <param name="lang">The language to generate in.</param>
            <param name="filePath">The path to the file where the generated code should be stored.</param>
            <param name="netNamespace">The .NET namespace into which the class is generated.</param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.CheckIfClassIsProperlyInitialized">
            <summary>
            Checks if mandatory properties are properly initialized.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GetCodeTypeDeclarationForClass(System.Boolean)">
            <summary>
            This function will generate the code. This is the function which
            should be called for generating the code.
            </summary>
            <param name="bIncludeSystemClassinClassDef">
            Flag to indicate if system properties are to be included or not
            </param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.InitializeClassObject">
            <summary>
            Function for initializing the class object that will be used to get all the
            method and properties of the WMI Class for generating the code.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.InitilializePublicPrivateMembers">
            <summary>
            This functrion initializes the public attributes and private variables
            list that will be used in the generated code.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ProcessNamingCollisions">
            <summary>
            This function will solve the naming collisions that might occur
            due to the collision between the local objects of the generated
            class and the properties/methods of the original WMI Class.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ResolveCollision(System.String,System.Boolean)">
            <summary>
            This function is used to resolve (actually generate a new name) collision
            between the generated class properties/variables with WMI methods/properties.
            This function safely assumes that there will be atleast one string left
            in the series prop0, prop1 ...prop(maxInt) . Otherwise this function will
            enter an infinite loop. May be we can avoid this through something, which
            i will think about it later
            </summary>
            <param name="inString"> </param>
            <param name="bCheckthisFirst"></param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ProcessNamespaceAndClassName">
            <summary>
            This function processes the WMI namespace and WMI classname and converts them to
            the namespace used to generate the class and the classname.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GeneratePublicReadOnlyProperty(System.String,System.String,System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            This function generates the code for the read only property.
            The generated code will be of the form
                   public &lt;propType&gt; &lt;propName&gt;{
                       get {
                               return (&lt;propValue&gt;);
                           }
                   }
            </summary>
            <param name="propName"> </param>
            <param name="propType"> </param>
            <param name="propValue"> </param>
            <param name="isLiteral"></param>
            <param name="isBrowsable"></param>
            <param name="Comment"></param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateSystemPropertiesClass">
            <summary>
            Function for generating the helper class "ManagementSystemProperties" which is
            used for separating the system properties from the other properties. This is used
            just to make the drop down list in the editor to look good.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateProperties">
            <summary>
            This function will enumerate all the properties (except systemproperties)
            of the WMI class and will generate them as properties of the managed code
            wrapper class.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ProcessPropertyQualifiers(System.Management.PropertyData,System.Boolean@,System.Boolean@,System.Boolean@,System.Boolean,System.Boolean@)">
            <summary>
            This function will process the qualifiers for a given WMI property and set the
            attributes of the generated property accordingly.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GeneratePropertyHelperEnums(System.Management.PropertyData,System.String,System.Boolean)">
            <summary>
            This function will generate enums corresponding to the Values/Valuemap pair
            and for the BitValues/Bitmap pair.
            </summary>
            <returns>
            returns if the property is an enum. This is checked by if enum is added or not
            </returns>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructPath">
             <summary>
             This function generated the static function which s used to construct the path
                 private static String ConstructPath(String keyName)
                    {
                        //FOR NON SINGLETON CLASSES
                        String strPath;
                        strPath = ((("\\&lt;defNamespace&gt;:&lt;defClassName&gt;";
                        strPath = ((_strPath) + (((".Key1=") + (key_Key1))));
                        strPath = ((_strPath) + (((",Key2=") + ((("\"") + (((key_Key2) + ("\""))))))));
                        return strPath;
            
                        //FOR SINGLETON CLASS
                        return "\\&lt;defNameSpace&gt;:&lt;defClassName&gt;=@";
                    }
             </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateDefaultConstructor">
            <summary>
            This function generates the default constructor.
            public Cons() {
                   _privObject = new ManagementObject();
                _privSystemProps = new ManagementSystemProperties(_privObject);
            }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithKeys">
             <summary>
            This function create the constructor which accepts the key values.
            public cons(UInt32 key_Key1, String key_Key2) :this(null,&lt;ClassName&gt;.ConstructPath(&lt;key1,key2&gt;),null) {
             }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithScopeKeys">
             <summary>
            This function create the constructor which accepts a scope and key values.
            public cons(ManagementScope scope,UInt32 key_Key1, String key_Key2) :this(scope,&lt;ClassName&gt;.ConstructPath(&lt;key1,key2&gt;),null) {
             }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithPath">
            <summary>
            This function generates code for the constructor which accepts ManagementPath as the parameter.
            The generated code will look something like this
                   public Cons(ManagementPath path) : this (null, path,null){
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithPathOptions">
            <summary>
            This function generates code for the constructor which accepts ManagementPath and GetOptions
            as parameters.
            The generated code will look something like this
                   public Cons(ManagementPath path, ObjectGetOptions options) : this (null, path,options){
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithScopePath">
            <summary>
            This function generates code for the constructor which accepts Scope as a string, path as a
            string and GetOptions().
            The generated code will look something like this
                   public Cons(String scope, String path, ObjectGetOptions options) :
                                       this (new ManagementScope(scope), new ManagementPath(path),options){
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithScope">
            <summary>
            This function generates code for the constructor which accepts ManagementScope as parameters.
            The generated code will look something like this
                   public Cons(ManagementScope scope, ObjectGetOptions options) : this (scope, &lt;ClassName&gt;.ConstructPath(),null){
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithOptions">
            <summary>
            This function generates code for the constructor which accepts GetOptions
            as parameters.
            The generated code will look something like this
                   public Cons(ObjectGetOptions options) : this (null, &lt;ClassName&gt;.ConstructPath(),options){
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithScopeOptions">
            <summary>
            This function generates code for the constructor which accepts ManagementScope and GetOptions
            as parameters.
            The generated code will look something like this
                   public Cons(ManagementScope scope, ObjectGetOptions options) : this (scope, &lt;ClassName&gt;.ConstructPath(),options){
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateConstructorWithScopePathOptions">
            <summary>
            This function generated the constructor like
                   public cons(ManagementScope scope, ManagamentPath path,ObjectGetOptions getOptions)
                   {
                       PrivateObject = new ManagementObject(scope,path,getOptions);
                       PrivateSystemProperties = new ManagementSystemProperties(PrivateObject);
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenarateConstructorWithLateBound">
            <summary>
            This function generates code for the constructor which accepts ManagementObject as the parameter.
            The generated code will look something like this
                   public Cons(ManagementObject theObject) {
                   if (CheckIfProperClass(theObject.Scope, theObject.Path, theObject.Options) = true) {
                           privObject = theObject;
                           privSystemProps = new WmiSystemProps(privObject);
                           curObj = privObject;
                       }
                       else {
                           throw new ArgumentException("Class name doesn't match");
                       }
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenarateConstructorWithLateBoundForEmbedded">
             <summary>
             This function generates code for the constructor which accepts ManagementObject as the parameter.
             The generated code will look something like this
                    public Cons(ManagementBaseObject theObject) {
                    if (CheckIfProperClass(theObject) = true)
                    {
                        embeddedObj = theObject
                        PrivateSystemProperties = New ManagementSystemProperties(theObject)
                        curObj = embeddedObj
                        isEmbedded = true
                    }
                    else
                    {
                        throw new ArgumentException("Class name doesn't match");
                    }
                }
            
             </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateInitializeObject">
            <summary>
            This function generated the constructor like
                   public cons(ManagementScope scope, ManagamentPath path,ObjectGetOptions getOptions)
                   {
                       PrivateObject = new ManagementObject(scope,path,getOptions);
                       PrivateSystemProperties = new ManagementSystemProperties(PrivateObject);
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateMethods">
             <summary>
             This function generates the WMI methods as the methods in the generated class.
             The generated code will look something like this
                    public &lt;retType&gt; Method1(&lt;type&gt; param1, &lt;type&gt; param2,...) {
                        ManagementBaseObject inParams = null;
                        inParams = _privObject.GetMethodParameters("ChangeStartMode");
                        inParams["&lt;inparam1&gt;"] = &lt;Value&gt;;
                        inParams["&lt;inoutparam2&gt;"] = &lt;Value&gt;;
                        ................................
                        ManagementBaseObject outParams = _privObject.InvokeMethod("ChangeStartMode", inParams, null);
                        inoutParam3 = (&lt;type&gt;)(outParams.Properties["&lt;inoutParam3&gt;"]);
                        outParam4 = (String)(outParams.Properties["&lt;outParam4&gt;"]);
                        ................................
                        return (&lt;retType&gt;)(outParams.Properties["ReturnValue"].Value);
                 }
            
                 The code generated changes if the method is static function
                    public &lt;retType&gt; Method1(&lt;type&gt; param1, &lt;type&gt; param2,...) {
                        ManagementBaseObject inParams = null;
                        ManagementObject classObj = new ManagementObject(null, "WIN32_SHARE", null); // the classname
                        inParams = classObj.GetMethodParameters("Create");
                        inParams["&lt;inparam1&gt;"] = &lt;Value&gt;;
                        inParams["&lt;inoutparam2&gt;"] = &lt;Value&gt;;
                        ................................
                        ManagementBaseObject outParams = classObj.InvokeMethod("ChangeStartMode", inParams, null);
                        inoutParam3 = (&lt;type&gt;)(outParams.Properties["&lt;inoutParam3&gt;"]);
                        outParam4 = (String)(outParams.Properties["&lt;outParam4&gt;"]);
                        ................................
                        return (&lt;retType&gt;)(outParams.Properties["ReturnValue"].Value);
                 }
            
             </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithNoParameters">
            <summary>
            This function returns a Collectionclass for the query
                   "Select * from &lt;ClassName&gt;"
               This is a static method. The output is like this
                   public static ServiceCollection All()
                   {
                       return GetInstances((System.Management.ManagementScope)null,(System.Management.EnumerateionOptions)null);
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithCondition">
            <summary>
            This function will accept the condition and will return collection for the query
                   "select * from &lt;ClassName&gt; where &lt;condition&gt;"
               The generated code will be like
                   public static ServiceCollection GetInstances(String Condition) {
                       return GetInstances(null,Condition,null);
                }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithProperties">
            <summary>
            This function returns the collection for the query
                   "select &lt;parameterList&gt; from &lt;ClassName&gt;"
               The generated output is like
                   public static ServiceCollection GetInstances(String []selectedProperties) {
                       return GetInstances(null,null,selectedProperties);
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithWhereProperties">
            <summary>
            This function returns the collection for the query
                   "select &lt;parameterList> from &lt;ClassName&gt; where &lt;WhereClause&gt;"
               The generated output is like
                   public static ServiceCollection GetInstances(String condition, String []selectedProperties) {
                       return GetInstances(null,condition,selectedProperties);
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithScope">
            <summary>
            This function returns a Collectionclass for the query
                   "Select * from &lt;ClassName&gt;"
               This is a static method. The output is like this
               public static (ObjectCollection)GetInstances(System.Management.ManagementScope mgmtScope, System.Management.EnumerationOptions enumOptions)
               {
                   if ((mgmtScope == null))
                   {
                       mgmtScope = new System.Management.ManagementScope();
                       mgmtScope.Path.NamespacePath = "root\\CimV2";
                   }
                   System.Management.ManagementPath pathObj = new System.Management.ManagementPath();
                   pathObj.ClassName = "CIM_LogicalDisk";
                   pathObj.NamespacePath = "root\\CimV2";
                   System.Management.ManagementClass clsObject = new System.Management.ManagementClass(mgmtScope, pathObj, null);
                   if ((enumOptions == null))
                   {
                       enumOptions = new System.Management.EnumerationOptions();
                       enumOptions.EnsureLocatable = true;
                   }
                   return new ObjectCollection(clsObject.GetInstances(enumOptions));
               }
               This method takes the scope which is useful for connection to remote machine
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithScopeCondition">
            <summary>
            This function will accept the condition and will return collection for the query
                   "select * from &lt;ClassName&gt; where &lt;condition&gt;"
               The generated code will be like
                   public static ServiceCollection GetInstances(String Condition) {
                       return GetInstances(scope,Condition,null);
                }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithScopeProperties">
            <summary>
            This function returns the collection for the query
                   "select &lt;parameterList&gt; from &lt;ClassName&gt;"
               The generated output is like
                   public static ServiceCollection GetInstances(String []selectedProperties) {
                       return GetInstances(scope,null,selectedProperties);
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateGetInstancesWithScopeWhereProperties">
            <summary>
            This function generates the code like
                public static ServiceCollection GetInstances(ManagementScope scope,String Condition, String[] selectedProperties)    {
                       if (scope == null)
                       {
                           scope = new ManagementScope();
                           scope.Path.NamespacePath = WMINamespace;
                       }
                    ManagementObjectSearcher ObjectSearcher = new ManagementObjectSearcher(scope,new SelectQuery("Win32_Service",Condition,selectedProperties));
                       QueryOptions query = new QueryOptions();
                       query.EnsureLocatable = true;
                       ObjectSearcher.Options = query;
                       return new ServiceCollection(ObjectSearcher.Get());
                   }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GeneratePrivateMember(System.String,System.String,System.String)">
            <summary>
            This function will add the variable as a private member to the class.
            The generated code will look like this
                    private &lt;MemberType&gt; &lt;MemberName&gt;;
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GeneratePrivateMember(System.String,System.String,System.CodeDom.CodeExpression,System.Boolean,System.String)">
            <summary>
            This function will add the variable as a private member to the class.
            The generated code will look like this
                    private &lt;MemberType&gt; &lt;MemberName&gt; = &lt;initValue&gt;;
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.IsContainedIn(System.String,System.Collections.SortedList@)">
            <summary>
            This function will find a given string in the passed
            in a case insensitive manner and will return true if the string is found.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ConvertCIMType(System.Management.CimType,System.Boolean)">
            <summary>
            This function will convert the given CIMTYPE to an acceptable .NET type.
            Since CLS doesn't support lotz of the basic types, we are using .NET helper
            classes here. We safely assume that there won't be any problem using them
            since .NET has to be there for the System.Management.Dll to work.
            </summary>
            <param name="cType"> </param>
            <param name="isArray"> </param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.isTypeInt(System.Management.CimType)">
            <summary>
            This function is used to determine whether the given CIMTYPE can be represented as an integer.
            This helper function is mainly used to determine whether this type will be support by enums.
            </summary>
            <param name="cType"> </param>
        </member>
        <member name="P:System.Management.ManagementClassGenerator.GeneratedFileName">
            <summary>
               <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="P:System.Management.ManagementClassGenerator.GeneratedTypeName">
            <summary>
               <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ConvertValuesToName(System.String)">
            <summary>
            Function to convert a given ValueMap or BitMap name to property enum name
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ResolveEnumNameValues(System.Collections.ArrayList,System.Collections.ArrayList@)">
            <summary>
            This function goes thru the names in array list and resolves any duplicates
            if any so that these names can be added as values of enum
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.IsContainedInArray(System.String,System.Collections.ArrayList)">
            <summary>
            This function will find a given string in the passed
            array list.
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.InitializeCodeGenerator(System.Management.CodeLanguage)">
            <summary>
            Function to create a appropriate generator
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GetUnsignedSupport(System.Management.CodeLanguage)">
            <summary>
            Function which checks if the language supports Unsigned numbers
            </summary>
            <param name="Language">Language</param>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateCommitMethod">
            <summary>
            Function which adds commit function to commit all the changes
            to the object to WMI
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ConvertBitMapValueToInt32(System.String)">
            <summary>
            Function to convert a value in format "0x..." to a integer
            to the object to WMI
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GetConversionFunction(System.Management.CimType)">
            <summary>
            Function to get the Converstion function to be used for Numeric datatypes
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.IsDesignerSerializationVisibilityToBeSet(System.String)">
            <summary>
            Checks if a given property is to be visible for Designer seriliazation
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.IsPropertyValueType(System.Management.CimType)">
            <summary>
            Checks if the given property type is represented as ValueType
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.IsDynamicClass">
            <summary>
            Gets the dynamic qualifier on the class to find if the
            class is a dynamic class
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ConvertToNumericValueAndAddToArray(System.Management.CimType,System.String,System.Collections.ArrayList,System.String@)">
            <summary>
            Converts a numeric value to appropriate type and adds it to array
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.AddClassComments(System.CodeDom.CodeTypeDeclaration)">
            <summary>
            Adds comments at the beginning of the class defination
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateClassNameProperty">
            <summary>
            Generates code for ManagementClassName Property
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateIfClassvalidFuncWithAllParams">
            <summary>
            Generates the functions CheckIfProperClass() which checks if the given path
            can be represented with the generated code
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateIfClassvalidFunction">
            <summary>
            Generates the functions CheckIfProperClass() which checks if the given path
            can be represented with the generated code
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateCodeForRefAndDateTimeTypes(System.CodeDom.CodeIndexerExpression,System.Boolean,System.CodeDom.CodeStatementCollection,System.String,System.CodeDom.CodeVariableReferenceExpression,System.Boolean)">
            <summary>
            Generates code for Property Get for Cimtype.Reference and CimType.DateTime type property
            Also generated code to initialize a variable after converting a property to DateTime and ManagementPathProperty
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.AddPropertySet(System.CodeDom.CodeIndexerExpression,System.Boolean,System.CodeDom.CodeStatementCollection,System.String,System.CodeDom.CodeVariableReferenceExpression)">
            <summary>
            Generates code for Property Set for Cimtype.DateTime and CimType.Reference type property
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.CreateObjectForProperty(System.String,System.CodeDom.CodeExpression)">
            <summary>
            Internal function used to create object. Used in adding code for Property Get for DateTime and Reference properties
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.ConvertPropertyToString(System.String,System.CodeDom.CodeExpression)">
            <summary>
            Internal function used to create code to convert DateTime or ManagementPath to String
            convert a expression. Used in adding code for Property Set for DateTime and Reference properties
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateCreateInstance">
            <summary>
            This function generates static CreateInstance to create an WMI instance.
            public static GenClass CreateInstance() {
                   return new GenClass(new ManagementClass(new System.Management.ManagementClass(CreatedWmiNamespace, CreatedClassName, null).CreateInstance()));
            }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateDeleteInstance">
            <summary>
            This function generates static CreateInstance to create an WMI instance.
            public static GenClass CreateInstance() {
                   PrivateLateBoundObject.Delete();
            }
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateDateTimeConversionFunction">
            <summary>
            Function to genreate helper function for DMTF to DateTime and DateTime to DMTF
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.GenerateTimeSpanConversionFunction">
            <summary>
            Function to genreate helper function for DMTF Time interval to TimeSpan and vice versa
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.AddToDateTimeFunction">
            <summary>
            Generated code for function to do conversion of date from DMTF format to DateTime format
            </summary>
        </member>
        <member name="M:System.Management.ManagementClassGenerator.DateTimeConversionFunctionHelper(System.CodeDom.CodeStatementCollection,System.String,System.String,System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Generates some common code used in conversion function for DateTime
            </summary>
        </member>
        <member name="T:System.Management.ThreadDispatch">
             <summary>
             The ThreadDispatch class allows clients to very easily spawn a thread, specify a worker delegate to be called from the
             spawned thread and wait until thread finishes work. This is important in cases where a new thread needs to be spawned but
             the main thread has to wait until the spawned thread finishes. As an example of this, in WMI we spawn MTA threads and create
             various objects in these threads to make sure we avoid marshaling cost.
             If the worker thread returns a value it is stored in the ThreadDispatch object and accessible to clients via the Result property.
             Also, any exception thrown is propagated from worker thread to main thread (by rethrowing orinal exception):
            
                 ThreadDispatch disp = new ThreadDispatch ( new ThreadDispatch.ThreadWorkerMethod  ( Class1.Func ) ) ;
                 disp.Start ( ) ;
            
             Four different delegate types are supported:
            
                 1. Delegate with no parameter and no return value.
                 2. Delegate with no parameter and return value.
                 3. Delegate with parameter and no return value.
                 4. Delegate with parameter and return value.
            
             </summary>
        </member>
        <member name="P:System.Management.ThreadDispatch.Exception">
            <summary>
            [Get] Gets the exception associated with the operation performed by thread.
            This can be null if no exception has been thrown.
            </summary>
        </member>
        <member name="P:System.Management.ThreadDispatch.Parameter">
            <summary>
            [Get/Set] The parameter to be used by worker thread. This will typically be a 'this' reference for access to instance members.
            </summary>
        </member>
        <member name="P:System.Management.ThreadDispatch.IsBackgroundThread">
            <summary>
            [Get/Set] The background property of a thread. Defaults to false.
            </summary>
        </member>
        <member name="P:System.Management.ThreadDispatch.Result">
            <summary>
            [Get] The result of the worker method called.
            </summary>
        </member>
        <member name="P:System.Management.ThreadDispatch.ApartmentType">
            <summary>
            [Get/Set] The thread apartment type
            </summary>
        </member>
        <member name="T:System.Management.ThreadDispatch.ThreadWorkerMethodWithReturn">
            <summary>
            Delegate declaration representing signature of worker method with return value.
            </summary>
        </member>
        <member name="T:System.Management.ThreadDispatch.ThreadWorkerMethodWithReturnAndParam">
            <summary>
            Delegate declaration representing signature of worker method with return value and parameter.
            </summary>
        </member>
        <member name="T:System.Management.ThreadDispatch.ThreadWorkerMethod">
            <summary>
            Delegate declaration representing signature of worker method with no return value and no parameter
            </summary>
        </member>
        <member name="T:System.Management.ThreadDispatch.ThreadWorkerMethodWithParam">
            <summary>
            Delegate declaration representing signature of worker method with parameter.
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.#ctor(System.Management.ThreadDispatch.ThreadWorkerMethodWithReturn)">
            <summary>
            Constructs a ThreadDispatch object with the thread worker method.
            Default settings on the ThreadDispatch object are:
                1. Parameter is null
                2. Thread ApartmentState will be MTA
                3. Background thread status will be false
            </summary>
            <param name="workerMethod">Delegate to be called to perform the work</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.#ctor(System.Management.ThreadDispatch.ThreadWorkerMethodWithReturnAndParam)">
            <summary>
            Constructs a ThreadDispatch object with the thread worker method.
            Default settings on the ThreadDispatch object are:
                1. Parameter is null
                2. Thread ApartmentState will be MTA
                3. Background thread status will be false
            </summary>
            <param name="workerMethod">Delegate to be called to perform the work</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.#ctor(System.Management.ThreadDispatch.ThreadWorkerMethodWithParam)">
            <summary>
            Constructs a ThreadDispatch object with the thread worker method.
            Default settings on the ThreadDispatch object are:
                1. Parameter is null
                2. Thread ApartmentState will be MTA
                3. Background thread status will be false
            </summary>
            <param name="workerMethod">Delegate to be called to perform the work</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.#ctor(System.Management.ThreadDispatch.ThreadWorkerMethod)">
            <summary>
            Constructs a ThreadDispatch object with the thread worker method.
            Default settings on the ThreadDispatch object are:
                1. Parameter is null
                2. Thread ApartmentState will be MTA
                3. Background thread status will be false
            </summary>
            <param name="workerMethod">Delegate to be called to perform the work</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.Start">
            <summary>
            Starts the thread execution and waits for thread to finish. If an exception occurs in the worker method
            this method rethrows the exception.
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.#ctor">
            <summary>
            Initializes the ThreadDispatch instance with initial values. Note that this constructor is private to avoid
            declaring instances without specifying at least a worker delegate instance.
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.InitializeThreadState(System.Object,System.Management.ThreadDispatch.ThreadWorkerMethodWithReturn,System.Threading.ApartmentState,System.Boolean)">
            <summary>
            Initializes the thread state members.
            </summary>
            <param name="threadParams">Parameters to be passed to thread</param>
            <param name="workerMethod">The delegate to be called from thread</param>
            <param name="aptState">The apartment of the thread created</param>
            <param name="background">Thread is created as a background or not</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.InitializeThreadState(System.Object,System.Management.ThreadDispatch.ThreadWorkerMethodWithReturnAndParam,System.Threading.ApartmentState,System.Boolean)">
            <summary>
            Initializes the thread state members.
            </summary>
            <param name="threadParams">Parameters to be passed to thread</param>
            <param name="workerMethod">The delegate to be called from thread</param>
            <param name="aptState">The apartment of the thread created</param>
            <param name="background">Thread is created as a background or not</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.InitializeThreadState(System.Object,System.Management.ThreadDispatch.ThreadWorkerMethod,System.Threading.ApartmentState,System.Boolean)">
            <summary>
            Initializes the thread state members.
            </summary>
            <param name="threadParams">Parameters to be passed to thread</param>
            <param name="workerMethod">The delegate to be called from thread</param>
            <param name="aptState">The apartment of the thread created</param>
            <param name="background">Thread is created as a background or not</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.InitializeThreadState(System.Object,System.Management.ThreadDispatch.ThreadWorkerMethodWithParam,System.Threading.ApartmentState,System.Boolean)">
            <summary>
            Initializes the thread state members.
            </summary>
            <param name="threadParams">Parameters to be passed to thread</param>
            <param name="workerMethod">The delegate to be called from thread</param>
            <param name="aptState">The apartment of the thread created</param>
            <param name="background">Thread is created as a background or not</param>
        </member>
        <member name="M:System.Management.ThreadDispatch.DispatchThread">
            <summary>
            Starts the execution of the thread and waits until the threadFinished event is signaled before continuing
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.ThreadEntryPoint">
            <summary>
            Entry point for the newly created thread. This method is wrapped in a try/catch block and captures any
            exceptions thrown from the worker method and re-throws the exception.
            The worker method for this thread entry point has no parameter and no return value.
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.ThreadEntryPointMethodWithParam">
            <summary>
            Entry point for the newly created thread. This method is wrapped in a try/catch block and captures any
            exceptions thrown from the worker method and re-throws the exception.
            The worker method for this thread entry point takes a parameter and no return value.
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.ThreadEntryPointMethodWithReturn">
            <summary>
            Entry point for the newly created thread. This method is wrapped in a try/catch block and captures any
            exceptions thrown from the worker method and re-throws the exception.
            The worker method for this thread entry point has no parameter but does return a value.
            </summary>
        </member>
        <member name="M:System.Management.ThreadDispatch.ThreadEntryPointMethodWithReturnAndParam">
            <summary>
            Entry point for the newly created thread. This method is wrapped in a try/catch block and captures any
            exceptions thrown from the worker method and re-throws the exception.
            The worker method for this thread entry point has a parameter and return value.
            </summary>
        </member>
        <member name="P:System.SR.InvalidQuery">
            <summary>The Query string supplied was invalid or improperly formed</summary>
        </member>
        <member name="P:System.SR.InvalidQueryDuplicatedToken">
            <summary>The Query string supplied was invalid because it contains a duplicate token</summary>
        </member>
        <member name="P:System.SR.InvalidQueryNullToken">
            <summary>The Query string supplied was invalid because a supplied token was null</summary>
        </member>
        <member name="P:System.SR.WorkerThreadWakeupFailed">
            <summary>Unable to wakeup the worker thread to create an object in MTA</summary>
        </member>
        <member name="P:System.SR.ClassNameNotInitializedException">
            <summary>ClassName not initialized.</summary>
        </member>
        <member name="P:System.SR.ClassNameNotFoundException">
            <summary>Class name does not match.</summary>
        </member>
        <member name="P:System.SR.CommentAttributeProperty">
            <summary>Every property added to the class for WMI property has attributes set to define its behavior in Visual Studio designer and also to define a TypeConverter to be used.</summary>
        </member>
        <member name="P:System.SR.CommentAutoCommitProperty">
            <summary>Property to show the commit behavior for the WMI object. If true, WMI object will be automatically saved after each property modification.(ie. Put() is called after modification of a property).</summary>
        </member>
        <member name="P:System.SR.CommentClassBegin">
            <summary>An Early Bound class generated for the WMI class.</summary>
        </member>
        <member name="P:System.SR.CommentConstructors">
            <summary>Below are different overloads of constructors to initialize an instance of the class with a WMI object.</summary>
        </member>
        <member name="P:System.SR.CommentCreatedClass">
            <summary>Private property to hold the name of WMI class which created this class.</summary>
        </member>
        <member name="P:System.SR.CommentCreatedWmiNamespace">
            <summary>Private property to hold the WMI namespace in which the class resides.</summary>
        </member>
        <member name="P:System.SR.CommentCurrentObject">
            <summary>The current WMI object used</summary>
        </member>
        <member name="P:System.SR.CommentDateConversionFunction">
            <summary>Datetime conversion functions ToDateTime and ToDmtfDateTime are added to the class to convert DMTF datetime to System.DateTime and vice-versa.</summary>
        </member>
        <member name="P:System.SR.CommentEmbeddedObject">
            <summary>Private variable to hold the embedded property representing the instance.</summary>
        </member>
        <member name="P:System.SR.CommentEnumeratorImplementation">
            <summary>Enumerator implementation for enumerating instances of the class.</summary>
        </member>
        <member name="P:System.SR.CommentFlagForEmbedded">
            <summary>Flag to indicate if the instance is an embedded object.</summary>
        </member>
        <member name="P:System.SR.CommentGetInstances">
            <summary>Different overloads of GetInstances() help in enumerating instances of the WMI class.</summary>
        </member>
        <member name="P:System.SR.CommentIsPropNull">
            <summary>Functions Is&lt;PropertyName&gt;Null() are used to check if a property is NULL.</summary>
        </member>
        <member name="P:System.SR.CommentLateBoundObject">
            <summary>Underlying lateBound WMI object.</summary>
        </member>
        <member name="P:System.SR.CommentLateBoundProperty">
            <summary>Property returning the underlying lateBound object.</summary>
        </member>
        <member name="P:System.SR.CommentManagementPath">
            <summary>The ManagementPath of the underlying WMI object.</summary>
        </member>
        <member name="P:System.SR.CommentManagementScope">
            <summary>ManagementScope of the object.</summary>
        </member>
        <member name="P:System.SR.CommentOriginNamespace">
            <summary>Property returns the namespace of the WMI class.</summary>
        </member>
        <member name="P:System.SR.CommentPrivateAutoCommit">
            <summary>Member variable to store the 'automatic commit' behavior for the class.</summary>
        </member>
        <member name="P:System.SR.CommentPrototypeConverter">
            <summary>TypeConverter to handle null values for ValueType properties</summary>
        </member>
        <member name="P:System.SR.CommentResetProperty">
            <summary>Functions Reset&lt;PropertyName&gt; are added for Nullable Read/Write properties. These functions are used by VS designer in property browser to set a property to NULL.</summary>
        </member>
        <member name="P:System.SR.CommentShouldSerialize">
            <summary>Functions ShouldSerialize&lt;PropertyName&gt; are functions used by VS property browser to check if a particular property has to be serialized. These functions are added for all ValueType properties ( properties of type Int32, BOOL etc.. which cannot be set to n ...</summary>
        </member>
        <member name="P:System.SR.CommentStaticManagementScope">
            <summary>Private member variable to hold the ManagementScope which is used by the various methods.</summary>
        </member>
        <member name="P:System.SR.CommentStaticScopeProperty">
            <summary>Public static scope property which is used by the various methods.</summary>
        </member>
        <member name="P:System.SR.CommentSystemObject">
            <summary>Property pointing to an embedded object to get System properties of the WMI object.</summary>
        </member>
        <member name="P:System.SR.CommentSystemPropertiesClass">
            <summary>Embedded class to represent WMI system Properties.</summary>
        </member>
        <member name="P:System.SR.CommentTimeSpanConversionFunction">
            <summary>Time interval functions  ToTimeSpan and ToDmtfTimeInterval are added to the class to convert DMTF Time Interval to  System.TimeSpan and vice-versa.</summary>
        </member>
        <member name="P:System.SR.CommentToDateTime">
            <summary>Converts a given datetime in DMTF format to System.DateTime object.</summary>
        </member>
        <member name="P:System.SR.CommentToDmtfDateTime">
            <summary>Converts a given System.DateTime object to DMTF datetime format.</summary>
        </member>
        <member name="P:System.SR.CommentToDmtfTimeInterval">
            <summary>Converts a given System.TimeSpan object to DMTF Time interval format.</summary>
        </member>
        <member name="P:System.SR.CommentToTimeSpan">
            <summary>Converts a given time interval in DMTF format to System.TimeSpan object.</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment">
            <summary>If the embedded property is strongly typed then, to strongly type the property to the type of</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment2">
            <summary>the embedded object, you have to do the following things.</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment3">
            <summary>\t1. Generate Managed class for the WMI class of the embedded property. This can be done with MgmtClassGen.exe tool or from Server Explorer.</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment4">
            <summary>\t2. Include the namespace of the generated class.</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment5">
            <summary>\t3. Change the property get/set functions so as return the instance of the Managed class.</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment6">
            <summary>Below is a sample code.</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment7">
            <summary>VB Code</summary>
        </member>
        <member name="P:System.SR.EmbeddedComment8">
            <summary>C# Code</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment1">
            <summary>public &lt;ManagedClassName of Embedded property&gt; &lt;PropertyName&gt;</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment10">
            <summary>\t\tIf (AutoCommitProp == true &amp;&amp; isEmbedded == false)</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment11">
            <summary>\t\t{</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment12">
            <summary>\t\t\tPrivateLateBoundObject.Put();</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment13">
            <summary>\t\t}</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment14">
            <summary>\t}</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment15">
            <summary>}</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment2">
            <summary>{</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment3">
            <summary>\tget</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment4">
            <summary>\t{</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment5">
            <summary>\t\treturn new &lt;ManagedClassName of Embedded property&gt;((System.Management.ManagementBaseObject)(curObj["&lt;PropertyName&gt;"]));</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment6">
            <summary>\t}</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment7">
            <summary>\tset</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment8">
            <summary>\t{</summary>
        </member>
        <member name="P:System.SR.EmbeddedCSharpComment9">
            <summary>\t\tcurObj["&lt;PropertyName&gt;"] = value.LateBoundObject;</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment1">
            <summary>Public Property &lt;PropertyName&gt;() As &lt;ManagedClassName of Embedded property&gt;</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment10">
            <summary>\tEnd Set</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment2">
            <summary>\tGet</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment3">
            <summary>\t\tReturn New &lt;ManagedClassName of Embedded Property&gt;(CType(curObj("&lt;PropertyName&gt;"),System.Management.ManagementBaseObject))</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment4">
            <summary>\tEnd Get</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment5">
            <summary>\tSet(ByVal Value As &lt;ManagedClassName of Embedded property&gt;)</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment6">
            <summary>\t\tcurObj("EObject")=Value.LateBoundObject</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment7">
            <summary>\t\tIf (AutoCommitProp = True And isEmbedded = False) Then</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment8">
            <summary>\t\t\tPrivateLateBoundObject.Put()</summary>
        </member>
        <member name="P:System.SR.EmbeddedVisualBasicComment9">
            <summary>\t\tEnd If</summary>
        </member>
        <member name="P:System.SR.EmptyFilePathException">
            <summary>FilePath cannot be empty.</summary>
        </member>
        <member name="P:System.SR.NamespaceNotInitializedException">
            <summary>Namespace not initialized.</summary>
        </member>
        <member name="P:System.SR.NullFilePathException">
            <summary>FilePath or code generator object is null.</summary>
        </member>
        <member name="P:System.SR.UnableToCreateCodeGeneratorException">
            <summary>Unable to create code generator for '{0}'</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_SystemManagement">
            <summary>System.Management currently is only supported for Windows desktop applications.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_FullFrameworkRequired">
            <summary>Could not find an installation of .NET Framework v4.0.30319. System.Management requires native modules from the .NET Framework to operate.</summary>
        </member>
        <member name="P:System.SR.LoadLibraryFailed">
            <summary>Failed to load required native library '{0}'.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_FrameworkUpdatedRequired">
            <summary>The native library '{0}' does not have all required functions. Please, update the .NET Framework.</summary>
        </member>
        <member name="P:System.SR.InvalidQueryTokenExpected">
            <summary>The Query string supplied was invalid or improperly formed. Token `{0}` is expected</summary>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
    </members>
</doc>
