<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.NameResolution</name>
  </assembly>
  <members>
    <member name="T:System.Net.Dns">
      <summary>Provides simple domain name resolution functionality.</summary>
    </member>
    <member name="M:System.Net.Dns.GetHostAddressesAsync(System.String)">
      <summary>Returns the Internet Protocol (IP) addresses for the specified host as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation. The <see cref="P:System.Threading.Tasks.Task`1.Result" /> property on the task object returns an array of type <see cref="T:System.Net.IPAddress" /> that holds the IP addresses for the host that is specified by the <paramref name="hostNameOrAddress" /> parameter.</returns>
      <param name="hostNameOrAddress">The host name or IP address to resolve.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hostNameOrAddress" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The length of <paramref name="hostNameOrAddress" /> is greater than 255 characters. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error is encountered when resolving <paramref name="hostNameOrAddress" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hostNameOrAddress" /> is an invalid IP address.</exception>
    </member>
    <member name="M:System.Net.Dns.GetHostEntryAsync(System.Net.IPAddress)">
      <summary>Resolves an IP address to an <see cref="T:System.Net.IPHostEntry" /> instance as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation. The <see cref="P:System.Threading.Tasks.Task`1.Result" /> property on the task object returns an <see cref="T:System.Net.IPHostEntry" /> instance that contains address information about the host specified in <paramref name="address" />.</returns>
      <param name="address">An IP address.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error is encountered when resolving <paramref name="address" />. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="address" /> is an invalid IP address.</exception>
    </member>
    <member name="M:System.Net.Dns.GetHostEntryAsync(System.String)">
      <summary>Resolves a host name or IP address to an <see cref="T:System.Net.IPHostEntry" /> instance as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation. The <see cref="P:System.Threading.Tasks.Task`1.Result" /> property on the task object returns an <see cref="T:System.Net.IPHostEntry" /> instance that contains address information about the host specified in <paramref name="hostNameOrAddress" />.</returns>
      <param name="hostNameOrAddress">The host name or IP address to resolve.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hostNameOrAddress" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The length of <paramref name="hostNameOrAddress" /> parameter is greater than 255 characters. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">An error was encountered when resolving the <paramref name="hostNameOrAddress" /> parameter. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="hostNameOrAddress" /> parameter is an invalid IP address. </exception>
    </member>
    <member name="M:System.Net.Dns.GetHostName">
      <summary>Gets the host name of the local computer.</summary>
      <returns>A string that contains the DNS host name of the local computer.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">An error is encountered when resolving the local host name. </exception>
      <PermissionSet>
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.IPHostEntry">
      <summary>Provides a container class for Internet host address information.</summary>
    </member>
    <member name="M:System.Net.IPHostEntry.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.IPHostEntry" /> class.</summary>
    </member>
    <member name="P:System.Net.IPHostEntry.AddressList">
      <summary>Gets or sets a list of IP addresses that are associated with a host.</summary>
      <returns>An array of type <see cref="T:System.Net.IPAddress" /> that contains IP addresses that resolve to the host names that are contained in the <see cref="P:System.Net.IPHostEntry.Aliases" /> property.</returns>
    </member>
    <member name="P:System.Net.IPHostEntry.Aliases">
      <summary>Gets or sets a list of aliases that are associated with a host.</summary>
      <returns>An array of strings that contain DNS names that resolve to the IP addresses in the <see cref="P:System.Net.IPHostEntry.AddressList" /> property.</returns>
    </member>
    <member name="P:System.Net.IPHostEntry.HostName">
      <summary>Gets or sets the DNS name of the host.</summary>
      <returns>A string that contains the primary host name for the server.</returns>
    </member>
  </members>
</doc>