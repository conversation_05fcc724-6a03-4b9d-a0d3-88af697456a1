<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XmlSerializer</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Serialization.XmlAnyAttributeAttribute">
      <summary>指定成员（返回 <see cref="T:System.Xml.XmlAttribute" /> 对象的数组的字段）可以包含任何 XML 特性。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyAttributeAttribute.#ctor">
      <summary>构造 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttribute">
      <summary>指定成员（返回 <see cref="T:System.Xml.XmlElement" /> 或 <see cref="T:System.Xml.XmlNode" /> 对象的数组的字段）可以包含对象，该对象表示在序列化或反序列化的对象中没有相应成员的所有 XML 元素。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 类的新实例并指定在 XML 文档中生成的 XML 元素名称。</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 元素的名称。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 类的新实例并指定在 XML 文档中生成的 XML 元素名称及其 XML 命名空间。</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 元素的名称。</param>
      <param name="ns">XML 元素的 XML 命名空间。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Name">
      <summary>获取或设置 XML 元素名。</summary>
      <returns>XML 元素的名称。</returns>
      <exception cref="T:System.InvalidOperationException">数组成员的元素名称与 <see cref="P:System.Xml.Serialization.XmlAnyElementAttribute.Name" /> 属性指定的元素名称不匹配。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Namespace">
      <summary>获取或设置在 XML 文档中生成的 XML 命名空间。</summary>
      <returns>一个 XML 命名空间。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Order">
      <summary>获取或设置序列化或反序列化元素的显式顺序。</summary>
      <returns>代码生成的顺序。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttributes">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 对象的集合。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Add(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>将 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 添加到集合中。</summary>
      <returns>新添加的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</returns>
      <param name="attribute">要相加的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Clear">
      <summary>从 <see cref="T:System.Collections.CollectionBaseinstance" /> 中移除所有对象。不能重写此方法。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Contains(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>获取一个值，该值指示集合中是否存在指定的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</summary>
      <returns>如果集合中存在该 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />，则为 true；否则为 false。</returns>
      <param name="attribute">您关注的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.CopyTo(System.Xml.Serialization.XmlAnyElementAttribute[],System.Int32)">
      <summary>将整个集合复制到 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 对象的一个兼容一维数组，从目标数组的指定索引处开始。</summary>
      <param name="array">
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 对象的一维数组，它是从集合复制来的元素的目标。该数组的索引必须从零开始。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Count">
      <summary>获取包含在 <see cref="T:System.Collections.CollectionBase" /> 实例中的元素数。</summary>
      <returns>包含在 <see cref="T:System.Collections.CollectionBase" /> 实例中的元素数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.GetEnumerator">
      <summary>返回循环访问 <see cref="T:System.Collections.CollectionBaseinstance" /> 的枚举数。</summary>
      <returns>一个循环访问 <see cref="T:System.Collections.CollectionBaseinstance" /> 的枚举器。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.IndexOf(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>获取指定的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</summary>
      <returns>指定 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</returns>
      <param name="attribute">您需要其索引的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>在集合中的指定索引处插入 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</summary>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的插入位置的索引。</param>
      <param name="attribute">要插入的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Item(System.Int32)">
      <summary>获取或设置指定索引处的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</summary>
      <returns>指定索引处的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</returns>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Remove(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>从集合中移除指定的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</summary>
      <param name="attribute">要移除的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.RemoveAt(System.Int32)">
      <summary>移除 <see cref="T:System.Collections.CollectionBaseinstance" /> 的指定索引处的元素。不能重写此方法。</summary>
      <param name="index">要被移除的元素的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>将整个集合复制到 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 对象的一个兼容一维数组，从目标数组的指定索引处开始。</summary>
      <param name="array">一维数组。</param>
      <param name="index">指定的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.CollectionBase" /> 的访问（线程安全）。</summary>
      <returns>如果同步对 <see cref="T:System.Collections.CollectionBase" /> 的访问，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.CollectionBase" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.CollectionBase" /> 的访问的对象。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>将对象添加到 <see cref="T:System.Collections.CollectionBase" /> 的结尾处。</summary>
      <returns>要添加到集合中的对象。</returns>
      <param name="value">作为要添加的元素的值的对象。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>确定 <see cref="T:System.Collections.CollectionBase" /> 是否包含特定元素。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBase" /> 包含特定元素，则为 True；否则为 false。</returns>
      <param name="value">元素的值。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>搜索指定的“对象”，并返回整个 <see cref="T:System.Collections.CollectionBase" /> 中第一个匹配项的从零开始的索引。</summary>
      <returns>对象的从零开始的索引。</returns>
      <param name="value">对象的值。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>将元素插入 <see cref="T:System.Collections.CollectionBase" /> 的指定索引处。</summary>
      <param name="index">索引，在此处插入元素。</param>
      <param name="value">元素的值。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.CollectionBasehas" /> 是否固定大小。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBasehas" /> 固定大小，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.CollectionBase" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBase" /> 为只读，则为 True；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>获取或设置位于指定索引处的元素。</summary>
      <returns>位于指定索引处的元素。</returns>
      <param name="index">元素的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>从 <see cref="T:System.Collections.CollectionBase" /> 中移除特定对象的第一个匹配项。</summary>
      <param name="value">移除的对象的值。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayAttribute">
      <summary>指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 必须将特定的类成员序列化为 XML 元素的数组。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 类的新实例，并指定在 XML 文档实例中生成的 XML 元素名称。</summary>
      <param name="elementName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 元素的名称。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.ElementName">
      <summary>获取或设置提供给序列化数组的 XML 元素名称。</summary>
      <returns>序列化数组的 XML 元素名称。默认值为向其分配 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 的成员的名称。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Form">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 元素名称是限定的还是非限定的。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值之一。默认值为 XmlSchemaForm.None。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.IsNullable">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必须将成员序列化为 xsi:nil 属性设置为 true 的 XML 空标记。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成 xsi:nil 属性，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Namespace">
      <summary>获取或设置 XML 元素的命名空间。</summary>
      <returns>XML 元素的命名空间。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Order">
      <summary>获取或设置序列化或反序列化元素的显式顺序。</summary>
      <returns>代码生成的顺序。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttribute">
      <summary>表示指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可以放置在序列化数组中的派生类型的特性。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 类的新实例，并指定在 XML 文档中生成的 XML 元素的名称。</summary>
      <param name="elementName">XML 元素的名称。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 类的新实例，并指定在 XML 文档中生成的 XML 元素的名称，以及可插入到所生成的 XML 文档中的 <see cref="T:System.Type" />。</summary>
      <param name="elementName">XML 元素的名称。</param>
      <param name="type">要序列化的对象的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 类的新实例，并指定可插入到序列化数组中的 <see cref="T:System.Type" />。</summary>
      <param name="type">要序列化的对象的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.DataType">
      <summary>获取或设置生成的 XML 元素的 XML 数据类型。</summary>
      <returns>“XML 架构定义”(XSD) 数据类型，定义见名为“XML 架构第 2 部分：数据类型”的“万维网联合会”(www.w3.org) 文档。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.ElementName">
      <summary>获取或设置生成的 XML 元素的名称。</summary>
      <returns>生成的 XML 元素的名称。默认值为成员标识符。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Form">
      <summary>获取或设置一个值，该值指示生成的 XML 元素的名称是否是限定的。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值之一。默认值为 XmlSchemaForm.None。</returns>
      <exception cref="T:System.Exception">
        <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Form" /> 属性设置为 XmlSchemaForm.Unqualified，并且指定 <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace" /> 值。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.IsNullable">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必须将成员序列化为 xsi:nil 属性设置为 true 的 XML 空标记。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成 xsi:nil 特性，则为 true；否则为 false，且不生成实例。默认值为 true。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace">
      <summary>获取或设置生成的 XML 元素的命名空间。</summary>
      <returns>生成的 XML 元素的命名空间。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.NestingLevel">
      <summary>获取或设置受 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 影响的 XML 元素的层次结构中的级别。</summary>
      <returns>数组的数组中的索引集从零开始的索引。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Type">
      <summary>获取或设置数组中允许的类型。</summary>
      <returns>数组中允许的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttributes">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 对象的集合。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Add(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>将 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 添加到集合中。</summary>
      <returns>所添加的项的索引。</returns>
      <param name="attribute">要添加到集合中的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Clear">
      <summary>从 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中移除所有元素。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Contains(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>确定集合是否包含指定的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</summary>
      <returns>如果该集合包含指定的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />，则为 true；否则为 false。</returns>
      <param name="attribute">要检查的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.CopyTo(System.Xml.Serialization.XmlArrayItemAttribute[],System.Int32)">
      <summary>从指定的目标索引开始，将 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 数组复制到集合。</summary>
      <param name="array">要复制到集合中的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 对象的数组。</param>
      <param name="index">从该处开始特性复制的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Count">
      <summary>获取 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.GetEnumerator">
      <summary>返回整个 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的一个枚举器。</summary>
      <returns>用于整个 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.IndexOf(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>返回所指定 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 在集合中首个匹配项的从零开始的索引；如果在集合中找不到该特性，则为 -1。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 在集合中的首个索引；如果在集合中找不到该特性，则为 -1。</returns>
      <param name="attribute">要在集合中定位的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Insert(System.Int32,System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>在集合中的指定索引处插入 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</summary>
      <param name="index">在该处插入特性的索引。</param>
      <param name="attribute">要插入的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Item(System.Int32)">
      <summary>获取或设置指定索引处的项。</summary>
      <returns>位于指定索引处的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</returns>
      <param name="index">要获取或设置的从零开始的集合成员的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Remove(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>如果存在，则从集合中移除 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</summary>
      <param name="attribute">要移除的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.RemoveAt(System.Int32)">
      <summary>移除指定索引处的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 项。</summary>
      <param name="index">要移除的项的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从目标数组的指定索引处开始将整个 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 复制到兼容的一维 <see cref="T:System.Array" />。</summary>
      <param name="array">作为从 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 复制的元素的目标的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的访问是同步的（线程安全），则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Add(System.Object)">
      <summary>将对象添加到 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的结尾处。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 索引，已在此处添加了 <paramref name="value" />。</returns>
      <param name="value">要添加到 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 末尾的 <see cref="T:System.Object" />。该值可以为 null。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>确定集合是否包含指定的 <see cref="T:System.Object" />。</summary>
      <returns>如果该集合包含指定的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>返回所指定 <see cref="T:System.Object" /> 在集合中首个匹配项的从零开始的索引；如果在集合中找不到该特性，则为 -1。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 在集合中的首个索引；如果在集合中找不到该特性，则为 -1。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>将元素插入 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的指定索引处。</summary>
      <param name="index">从零开始的索引，应在该位置插入 <paramref name="value" />。</param>
      <param name="value">要插入的 <see cref="T:System.Object" />。该值可以为 null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="index" /> 大于 <see cref="P:System.Xml.Serialization.XmlArrayItemAttributes.Count" />。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是否具有固定大小。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定大小，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>获取或设置指定索引处的项。</summary>
      <returns>位于指定索引处的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</returns>
      <param name="index">要获取或设置的从零开始的集合成员的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>从 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中移除特定对象的第一个匹配项。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeAttribute">
      <summary>指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 必须将类成员序列化为 XML 属性。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 类的新实例，并指定生成的 XML 属性的名称。</summary>
      <param name="attributeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 特性的名称。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 类的新实例。</summary>
      <param name="attributeName">生成的 XML 特性的名称。</param>
      <param name="type">用来存储特性的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 类的新实例。</summary>
      <param name="type">用来存储特性的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.AttributeName">
      <summary>获取或设置 XML 属性的名称。</summary>
      <returns>XML 属性的名称。默认值为成员名称。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.DataType">
      <summary>获取或设置 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 属性的 XSD 数据类型。</summary>
      <returns>一种 XSD（XML 架构文档）数据类型，由名为“XML Schema: DataTypes”（XML 架构：数据类型）的万维网联合会 (www.w3.org) 文档定义。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Form">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XML 属性名称是否是限定的。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值之一。默认值为 XmlForm.None。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Namespace">
      <summary>获取或设置 XML 属性的 XML 命名空间。</summary>
      <returns>XML 属性的 XML 命名空间。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Type">
      <summary>获取或设置 XML 属性的复杂类型。</summary>
      <returns>XML 属性的类型。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeOverrides">
      <summary>允许您在使用 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化或反序列化对象时重写属性、字段和类特性。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.XmlAttributes)">
      <summary>将 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象添加到 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象的集合中。<paramref name="type" /> 参数指定一个要重写的对象。<paramref name="member" /> 参数指定被重写的成员的名称。</summary>
      <param name="type">要重写的对象的 <see cref="T:System.Type" />。</param>
      <param name="member">要重写的成员的名称。</param>
      <param name="attributes">表示重写特性的 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.Xml.Serialization.XmlAttributes)">
      <summary>将 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象添加到 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象的集合中。<paramref name="type" /> 参数指定由 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象重写的对象。</summary>
      <param name="type">被重写的对象的 <see cref="T:System.Type" />。</param>
      <param name="attributes">表示重写特性的 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 对象。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type)">
      <summary>获取与指定的基类类型关联的对象。</summary>
      <returns>表示重写属性集合的 <see cref="T:System.Xml.Serialization.XmlAttributes" />。</returns>
      <param name="type">与要检索的特性的集合关联的基类 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type,System.String)">
      <summary>获取与指定（基类）类型关联的对象。成员参数指定被重写的基类成员。</summary>
      <returns>表示重写属性集合的 <see cref="T:System.Xml.Serialization.XmlAttributes" />。</returns>
      <param name="type">与所需特性的集合关联的基类 <see cref="T:System.Type" />。</param>
      <param name="member">指定返回的 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 的重写成员的名称。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributes">
      <summary>表示一个属性对象的集合，这些对象控制 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化和反序列化对象。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyAttribute">
      <summary>获取或设置要重写的 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />。</summary>
      <returns>要重写的 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyElements">
      <summary>获取要重写的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 对象集合。</summary>
      <returns>表示 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 对象集合的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> 对象。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArray">
      <summary>获取或设置一个对象，该对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化返回数组的公共字段或读/写属性。</summary>
      <returns>一个 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化如何返回数组的公共字段或读/写属性。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArrayItems">
      <summary>获取或设置一个对象集合，这些对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化插入数组（由公共字段或读/写属性返回）的项。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 对象，它包含 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 对象的集合。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAttribute">
      <summary>获取或设置一个对象，该对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何将公共字段或公共读/写属性序列化为 XML 特性。</summary>
      <returns>控制将公共字段或读/写属性序列化为 XML 特性的 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlChoiceIdentifier">
      <summary>获取或设置一个对象，该对象使您可以区别一组选项。</summary>
      <returns>可应用到被序列化为 xsi:choice 元素的类成员的 <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlDefaultValue">
      <summary>获取或设置 XML 元素或属性的默认值。</summary>
      <returns>表示 XML 元素或属性的默认值的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlElements">
      <summary>获取一个对象集合，这些对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何将公共字段或读/写属性序列化为 XML 元素。</summary>
      <returns>包含一个 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 对象集合的 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlEnum">
      <summary>获取或设置一个对象，该对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化枚举成员。</summary>
      <returns>指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化枚举成员的 <see cref="T:System.Xml.Serialization.XmlEnumAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlIgnore">
      <summary>获取或设置一个值，该值指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否序列化公共字段或公共读/写属性。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 不得序列化字段或属性，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.Xmlns">
      <summary>获取或设置一个值，该值指定当重写包含返回 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 对象的成员的对象时，是否保留所有的命名空间声明。</summary>
      <returns>如果应保留命名空间声明，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlRoot">
      <summary>获取或设置一个对象，该对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何将类序列化为 XML 根元素。</summary>
      <returns>重写特性化为 XML 根元素的类的 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlText">
      <summary>获取或设置一个对象，该对象指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 将公共字段或公共读/写属性序列化为 XML 文本。</summary>
      <returns>重写公共属性或字段的默认序列化的 <see cref="T:System.Xml.Serialization.XmlTextAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlType">
      <summary>获取或设置一个对象，该对象指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化一个已对其应用 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 的类。</summary>
      <returns>重写应用于类声明的 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 的 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute">
      <summary>指定可以通过使用枚举来进一步检测成员。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> 类的新实例。</summary>
      <param name="name">返回用于检测选项的枚举的成员名称。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlChoiceIdentifierAttribute.MemberName">
      <summary>获取或设置字段的名称，该字段返回在检测类型时使用的枚举。</summary>
      <returns>返回枚举的字段的名称。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttribute">
      <summary>指示公共字段或属性在 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化或反序列化包含它们的对象时表示 XML 元素。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 类的新实例，并指定 XML 元素的名称。</summary>
      <param name="elementName">序列化成员的 XML 元素名。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的新实例，并指定 XML 元素的名称和 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 应用到的成员的派生类型。此成员类型在 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化包含它的对象时使用。</summary>
      <param name="elementName">序列化成员的 XML 元素名。</param>
      <param name="type">从该成员的类型派生的对象的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 类的新实例，并指定 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 所应用到的成员的类型。此类型在序列化或反序列化包含它的对象时由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 使用。</summary>
      <param name="type">从该成员的类型派生的对象的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.DataType">
      <summary>获取或设置由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的 XMl 元素的 XML 架构定义 (XSD) 数据类型。</summary>
      <returns>“XML 架构”数据类型，如名为“XML 架构第 2 部分：数据类型”的“万维网联合会”(www.w3.org) 文档中所定义。</returns>
      <exception cref="T:System.Exception">已指定的 XML 架构数据类型无法映射到 .NET 数据类型。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.ElementName">
      <summary>获取或设置生成的 XML 元素的名称。</summary>
      <returns>生成的 XML 元素的名称。默认值为成员标识符。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Form">
      <summary>获取或设置一个值，该值指示元素是否是限定的。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值之一。默认值为 <see cref="F:System.Xml.Schema.XmlSchemaForm.None" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.IsNullable">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必须将设置为 null 的成员序列化为 xsi:nil 属性设置为 true 的空标记。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成 xsi:nil 属性，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Namespace">
      <summary>获取或设置分配给 XML 元素的命名空间，这些 XML 元素是在序列化类时得到的。</summary>
      <returns>XML 元素的命名空间。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Order">
      <summary>获取或设置序列化或反序列化元素的显式顺序。</summary>
      <returns>代码生成的顺序。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Type">
      <summary>获取或设置用于表示 XML 元素的对象类型。</summary>
      <returns>成员的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttributes">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 对象的集合，该对象由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 用来重写序列化类的默认方式。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Add(System.Xml.Serialization.XmlElementAttribute)">
      <summary>将 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 添加到集合中。</summary>
      <returns>新添加项的从零开始的索引。</returns>
      <param name="attribute">要相加的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Clear">
      <summary>从 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中移除所有元素。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Contains(System.Xml.Serialization.XmlElementAttribute)">
      <summary>确定集合是否包含指定对象。</summary>
      <returns>如果该集合中存在对象，则为 true；否则为 false。</returns>
      <param name="attribute">要查找的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.CopyTo(System.Xml.Serialization.XmlElementAttribute[],System.Int32)">
      <summary>将 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 或它的一部分复制到一维数组中。</summary>
      <param name="array">保留所复制的元素的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 数组。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Count">
      <summary>获取 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中包含的元素数。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中包含的元素个数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.GetEnumerator">
      <summary>返回整个 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的一个枚举器。</summary>
      <returns>用于整个 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.IndexOf(System.Xml.Serialization.XmlElementAttribute)">
      <summary>获取指定的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的索引。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的从零开始的索引。</returns>
      <param name="attribute">要检索其索引的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlElementAttribute)">
      <summary>向集合中插入 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</summary>
      <param name="index">从零开始的索引，在此处插入了成员。</param>
      <param name="attribute">要插入的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Item(System.Int32)">
      <summary>获取或设置位于指定索引处的元素。</summary>
      <returns>位于指定索引处的元素。</returns>
      <param name="index">要获得或设置的元素从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">设置该属性，而且 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Remove(System.Xml.Serialization.XmlElementAttribute)">
      <summary>从集合中移除指定的对象。</summary>
      <param name="attribute">要从该集合中移除的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.RemoveAt(System.Int32)">
      <summary>移除指定索引处的 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 项。</summary>
      <param name="index">要移除的项的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从目标数组的指定索引处开始将整个 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 复制到兼容的一维 <see cref="T:System.Array" />。</summary>
      <param name="array">作为从 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 复制的元素的目标的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的访问是同步的（线程安全），则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的访问的对象。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>将对象添加到 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的结尾处。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 索引，已在此处添加了 <paramref name="value" />。</returns>
      <param name="value">要添加到 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 末尾的 <see cref="T:System.Object" />。该值可以为 null。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>确定 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是否包含特定值。</summary>
      <returns>如果在 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中找到 <see cref="T:System.Object" />，则为 true；否则为 false。</returns>
      <param name="value">要在 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中定位的对象。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>确定 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中特定项的索引。</summary>
      <returns>如果在列表中找到，则为 <paramref name="value" /> 的索引；否则为 -1。</returns>
      <param name="value">要在 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中定位的对象。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>将元素插入 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的指定索引处。</summary>
      <param name="index">从零开始的索引，应在该位置插入 <paramref name="value" />。</param>
      <param name="value">要插入的 <see cref="T:System.Object" />。该值可以为 null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。- 或 -<paramref name="index" /> 大于 <see cref="P:System.Xml.Serialization.XmlElementAttributes.Count" />。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是否具有固定大小。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定大小，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>获取或设置位于指定索引处的元素。</summary>
      <returns>位于指定索引处的元素。</returns>
      <param name="index">要获得或设置的元素从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">设置该属性，而且 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>从 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中移除特定对象的第一个匹配项。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 为只读。- 或 -<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定大小。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlEnumAttribute">
      <summary>控制 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化枚举成员。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> 类的新实例，并指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成或识别的（当该序列化程序分别序列化或反序列化枚举时）XML 值。</summary>
      <param name="name">该枚举成员的重写名。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlEnumAttribute.Name">
      <summary>获取或设置当 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化枚举时在 XML 文档实例中生成的值，或当它反序列化该枚举成员时识别的值。</summary>
      <returns>当 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化枚举时在 XML 文档实例中生成的值，或当它反序列化该枚举成员时识别的值。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlIgnoreAttribute">
      <summary>指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 的 <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> 方法不序列化公共字段或公共读/写属性值。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIgnoreAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlIgnoreAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlIncludeAttribute">
      <summary>允许 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 在它序列化或反序列化对象时识别类型。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIncludeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" /> 类的新实例。</summary>
      <param name="type">要包含的对象的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlIncludeAttribute.Type">
      <summary>获取或设置要包含的对象的类型。</summary>
      <returns>要包含的对象的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute">
      <summary>指定目标属性、参数、返回值或类成员包含与 XML 文档中所用命名空间关联的前缀。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlRootAttribute">
      <summary>控制视为 XML 根元素的属性目标的 XML 序列化。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> 类的新实例，并指定 XML 根元素的名称。</summary>
      <param name="elementName">XML 根元素的名称。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.DataType">
      <summary>获取或设置 XML 根元素的 XSD 数据类型。</summary>
      <returns>一种 XSD（XML 架构文档）数据类型，由名为“XML Schema: DataTypes”（XML 架构：数据类型）的万维网联合会 (www.w3.org) 文档定义。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.ElementName">
      <summary>获取或设置由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的 <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> 和 <see cref="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)" /> 方法分别生成和识别的 XML 元素的名称。</summary>
      <returns>在 XML 文档实例中生成和识别的 XML 根元素的名称。默认值为序列化类的名称。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.IsNullable">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必须将设置为 null 的成员序列化为设置为 true 的 xsi:nil 属性。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成 xsi:nil 属性，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.Namespace">
      <summary>获取或设置 XML 根元素的命名空间。</summary>
      <returns>XML 元素的命名空间。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializer">
      <summary>将对象序列化到 XML 文档中和从 XML 文档中反序列化对象。<see cref="T:System.Xml.Serialization.XmlSerializer" /> 使您得以控制如何将对象编码到 XML 中。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例，该类可以将指定类型的对象序列化为 XML 文档，也可以将 XML 文档反序列化为指定类型的对象。</summary>
      <param name="type">此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的对象的类型。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例，该类可以将指定类型的对象序列化为 XML 文档，也可以将 XML 文档反序列化为指定类型的对象。指定所有 XML 元素的默认命名空间。</summary>
      <param name="type">此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的对象的类型。</param>
      <param name="defaultNamespace">用于所有 XML 元素的默认命名空间。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Type[])">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例，该类可以将指定类型的对象序列化为 XML 文档，也可以将 XML 文档反序列化为指定类型的对象。如果属性或字段返回一个数组，则 <paramref name="extraTypes" /> 参数指定可插入到该数组的对象。</summary>
      <param name="type">此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的对象的类型。</param>
      <param name="extraTypes">要序列化的其他对象类型的 <see cref="T:System.Type" /> 数组。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例，该类可以将指定类型的对象序列化为 XML 文档，也可以将 XML 文档反序列化为指定类型的对象。要序列化的每个对象本身可包含类的实例，此重载可使用其他类重写这些实例。</summary>
      <param name="type">要序列化的对象的类型。</param>
      <param name="overrides">一个 <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例，该类可将 <see cref="T:System.Object" /> 类型的对象序列化为 XML 文档实例，并可将 XML 文档实例反序列化为 <see cref="T:System.Object" /> 类型的对象。要序列化的每个对象本身可包含类的实例，此重载可使用其他类重写这些实例。此重载还指定所有 XML 元素的默认命名空间和用作 XML 根元素的类。</summary>
      <param name="type">此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的对象的类型。</param>
      <param name="overrides">一个 <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />，它扩展或重写 <paramref name="type" /> 参数中指定类的行为。</param>
      <param name="extraTypes">要序列化的其他对象类型的 <see cref="T:System.Type" /> 数组。</param>
      <param name="root">定义 XML 根元素属性的 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</param>
      <param name="defaultNamespace">XML 文档中所有 XML 元素的默认命名空间。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 类的新实例，该类可以将指定类型的对象序列化为 XML 文档，也可以将 XML 文档反序列化为指定类型的对象。还可以指定作为 XML 根元素使用的类。</summary>
      <param name="type">此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的对象的类型。</param>
      <param name="root">表示 XML 根元素的 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CanDeserialize(System.Xml.XmlReader)">
      <summary>获取一个值，该值指示此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否可以反序列化指定的 XML 文档。</summary>
      <returns>如果此 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可以反序列化 <see cref="T:System.Xml.XmlReader" /> 指向的对象，则为 true，否则为 false。</returns>
      <param name="xmlReader">指向要反序列化的文档的 <see cref="T:System.Xml.XmlReader" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)">
      <summary>反序列化指定 <see cref="T:System.IO.Stream" /> 包含的 XML 文档。</summary>
      <returns>正被反序列化的 <see cref="T:System.Object" />。</returns>
      <param name="stream">包含要反序列化的 XML 文档的 <see cref="T:System.IO.Stream" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.TextReader)">
      <summary>反序列化指定 <see cref="T:System.IO.TextReader" /> 包含的 XML 文档。</summary>
      <returns>正被反序列化的 <see cref="T:System.Object" />。</returns>
      <param name="textReader">
        <see cref="T:System.IO.TextReader" /> 包含要反序列化的 XML 文档。</param>
      <exception cref="T:System.InvalidOperationException">反序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader)">
      <summary>反序列化指定 <see cref="T:System.xml.XmlReader" /> 包含的 XML 文档。</summary>
      <returns>正被反序列化的 <see cref="T:System.Object" />。</returns>
      <param name="xmlReader">包含要反序列化的 XML 文档的 <see cref="T:System.xml.XmlReader" />。</param>
      <exception cref="T:System.InvalidOperationException">反序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromTypes(System.Type[])">
      <summary>返回从类型数组创建的 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 对象的数组。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 对象的数组。</returns>
      <param name="types">
        <see cref="T:System.Type" /> 对象的数组。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>使用指定的 <see cref="T:System.IO.Stream" /> 序列化指定的 <see cref="T:System.Object" /> 并将 XML 文档写入文件。</summary>
      <param name="stream">用于编写 XML 文档的 <see cref="T:System.IO.Stream" />。</param>
      <param name="o">将要序列化的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.InvalidOperationException">序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>使用引用指定命名空间的指定 <see cref="T:System.IO.Stream" /> 序列化指定的 <see cref="T:System.Object" /> 并将 XML 文档写入文件。</summary>
      <param name="stream">用于编写 XML 文档的 <see cref="T:System.IO.Stream" />。</param>
      <param name="o">将要序列化的 <see cref="T:System.Object" />。</param>
      <param name="namespaces">该对象所引用的 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)">
      <summary>使用指定的 <see cref="T:System.IO.TextWriter" /> 序列化指定的 <see cref="T:System.Object" /> 并将 XML 文档写入文件。</summary>
      <param name="textWriter">用于编写 XML 文档的 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="o">将要序列化的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>使用指定的 <see cref="T:System.IO.TextWriter" /> 和指定命名空间序列化指定的 <see cref="T:System.Object" /> 并将 XML 文档写入文件。</summary>
      <param name="textWriter">用于编写 XML 文档的 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="o">将要序列化的 <see cref="T:System.Object" />。</param>
      <param name="namespaces">包含生成的 XML 文档的命名空间的 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlWriter" /> 序列化指定的 <see cref="T:System.Object" /> 并将 XML 文档写入文件。</summary>
      <param name="xmlWriter">用于编写 XML 文档的 <see cref="T:System.xml.XmlWriter" />。</param>
      <param name="o">将要序列化的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.InvalidOperationException">序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>使用指定的 <see cref="T:System.Xml.XmlWriter" /> 和指定命名空间序列化指定的 <see cref="T:System.Object" /> 并将 XML 文档写入文件。</summary>
      <param name="xmlWriter">用于编写 XML 文档的 <see cref="T:System.xml.XmlWriter" />。</param>
      <param name="o">将要序列化的 <see cref="T:System.Object" />。</param>
      <param name="namespaces">该对象所引用的 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">序列化期间发生错误。使用 <see cref="P:System.Exception.InnerException" /> 属性时可使用原始异常。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerNamespaces">
      <summary>包含 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 用于在 XML 文档实例中生成限定名的 XML 命名空间和前缀。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>使用包含前缀和命名空间对集合的 XmlSerializerNamespaces 的指定实例，初始化 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 类的新实例。</summary>
      <param name="namespaces">包含命名空间和前缀对的 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 的实例。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.XmlQualifiedName[])">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 类的新实例。</summary>
      <param name="namespaces">
        <see cref="T:System.Xml.XmlQualifiedName" /> 对象的数组。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.Add(System.String,System.String)">
      <summary>将前缀和命名空间对添加到 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 对象。</summary>
      <param name="prefix">与 XML 命名空间关联的前缀。</param>
      <param name="ns">一个 XML 命名空间。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerNamespaces.Count">
      <summary>获取集合中前缀和命名空间对的数目。</summary>
      <returns>集合中前缀和命名空间对的数目。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.ToArray">
      <summary>获取 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 对象中前缀和命名空间对的数组。</summary>
      <returns>在 XML 文档中用作限定名的 <see cref="T:System.Xml.XmlQualifiedName" /> 对象的数组。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTextAttribute">
      <summary>当序列化或反序列化包含该成员的类时，向 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 指示应将该成员作为 XML 文本处理。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> 类的新实例。</summary>
      <param name="type">要进行序列化的成员的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.DataType">
      <summary>获取或设置由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 生成的文本的“XML 架构”定义语言 (XSD) 数据类型</summary>
      <returns>XML 架构数据类型，如“万维网联合会”(www.w3.org) 文档“XML 架构第 2 部分：数据类型”所定义。</returns>
      <exception cref="T:System.Exception">已指定的 XML 架构数据类型无法映射到 .NET 数据类型。</exception>
      <exception cref="T:System.InvalidOperationException">已指定的 XML 架构数据类型对该属性无效，且无法转换为成员类型。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.Type">
      <summary>获取或设置成员的类型。</summary>
      <returns>成员的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeAttribute">
      <summary>控制当属性目标由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化时生成的 XML 架构。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 类的新实例，并指定 XML 类型的名称。</summary>
      <param name="typeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化类实例时生成（和在反序列化类实例时识别）的 XML 类型的名称。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.AnonymousType">
      <summary>获取或设置一个值，该值确定生成的构架类型是否为 XSD 匿名类型。</summary>
      <returns>如果结果架构类型为 XSD 匿名类型，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.IncludeInSchema">
      <summary>获取或设置一个值，该值指示是否要在 XML 架构文档中包含该类型。</summary>
      <returns>若要将此类型包括到 XML 架构文档中，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.Namespace">
      <summary>获取或设置 XML 类型的命名空间。</summary>
      <returns>XML 类型的命名空间。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.TypeName">
      <summary>获取或设置 XML 类型的名称。</summary>
      <returns>XML 类型的名称。</returns>
    </member>
  </members>
</doc>