<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XmlSerializer</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Serialization.XmlAnyAttributeAttribute">
      <summary>メンバー (<see cref="T:System.Xml.XmlAttribute" /> オブジェクトの配列を返すフィールド) に任意の XML 属性を含めることができるように指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyAttributeAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> クラスの新しいインスタンスを生成します。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttribute">
      <summary>メンバー (<see cref="T:System.Xml.XmlElement" /> オブジェクトまたは <see cref="T:System.Xml.XmlNode" /> オブジェクトの配列を返すフィールド) に、シリアル化または逆シリアル化対象のオブジェクト内に対応するメンバーがない任意の XML 要素を表すオブジェクトを含めるように指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> クラスの新しいインスタンスを初期化し、XML ドキュメントに生成される XML 要素名を指定します。</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が生成する XML 要素の名前。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> クラスの新しいインスタンスを初期化し、XML ドキュメントに生成される XML 要素名とその XML 名前空間を指定します。</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が生成する XML 要素の名前。</param>
      <param name="ns">XML 要素の XML 名前空間。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Name">
      <summary>XML 要素名を取得または設定します。</summary>
      <returns>XML 要素の名前。</returns>
      <exception cref="T:System.InvalidOperationException">配列メンバーの要素名が、<see cref="P:System.Xml.Serialization.XmlAnyElementAttribute.Name" /> プロパティに指定されている要素名と一致しません。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Namespace">
      <summary>XML ドキュメントに生成される XML 名前空間を取得または設定します。</summary>
      <returns>XML 名前空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Order">
      <summary>要素のシリアル化または逆シリアル化を行う明示的な順序を取得または設定します。</summary>
      <returns>コード生成の順序。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> オブジェクトのコレクションを表します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Add(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> をコレクションに追加します。</summary>
      <returns>新しく追加された <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> のインデックス。</returns>
      <param name="attribute">追加する <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Clear">
      <summary>
        <see cref="T:System.Collections.CollectionBaseinstance" /> からすべてのオブジェクトを削除します。このメソッドはオーバーライドできません。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Contains(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>指定した <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> がコレクション内に存在するかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> がコレクション内に存在する場合は true。それ以外の場合は false。</returns>
      <param name="attribute">コレクション内に存在するかどうかを確認する対象の <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.CopyTo(System.Xml.Serialization.XmlAnyElementAttribute[],System.Int32)">
      <summary>コピー先配列の指定されたインデックスを開始位置として、コレクション全体を、<see cref="T:System.Xml.Serialization.XmlElementAttribute" /> オブジェクトの互換性がある 1 次元配列にコピーします。</summary>
      <param name="array">コレクションからコピーされる要素のコピー先である <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> オブジェクトの 1 次元配列。配列では 0 から始まるインデックスを使用する必要があります。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Count">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> インスタンスに格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" /> インスタンスに格納されている要素の数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.CollectionBaseinstance" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBaseinstance" /> を反復処理する列挙子。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.IndexOf(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>指定した <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> のインデックスを取得します。</summary>
      <returns>指定した <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> のインデックス。</returns>
      <param name="attribute">インデックスを取得する対象の <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> をコレクション内の指定のインデックス位置に挿入します。</summary>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> の挿入位置を示すインデックス。</param>
      <param name="attribute">挿入する <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Item(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> を取得または設定します。</summary>
      <returns>指定したインデックス位置にある <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</returns>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> のインデックス。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Remove(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>指定した <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> をコレクションから削除します。</summary>
      <param name="attribute">削除する <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.RemoveAt(System.Int32)">
      <summary>
        <see cref="T:System.Collections.CollectionBaseinstance" /> の指定したインデックスにある要素を削除します。このメソッドはオーバーライドできません。</summary>
      <param name="index">削除される要素のインデックス。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>コレクション全体を <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> オブジェクトの互換性がある 1 次元配列にコピーします。コピー操作は、コピー先の配列の指定したインデックスから始まります。</summary>
      <param name="array">1 次元配列。</param>
      <param name="index">指定したインデックス。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" /> へのアクセスが同期されている場合は True。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" /> へのアクセスを同期するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> の末尾にオブジェクトを追加します。</summary>
      <returns>コレクションに追加されたオブジェクト。</returns>
      <param name="value">コレクションに追加されるオブジェクトの値。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> に特定の要素が格納されているかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" /> に特定の要素が含まれている場合は True。それ以外の場合は false。</returns>
      <param name="value">要素の値。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>指定したオブジェクトを検索し、<see cref="T:System.Collections.CollectionBase" /> 全体内で最初に見つかった位置の 0 から始まるインデックスを返します。</summary>
      <returns>オブジェクトの 0 から始まるインデックス。</returns>
      <param name="value">オブジェクトの値。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> 内の指定したインデックスの位置に要素を挿入します。</summary>
      <param name="index">要素が挿入されるインデックス。</param>
      <param name="value">要素の値。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.CollectionBasehas" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBasehas" /> が固定サイズの場合は True。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" /> が読み取り専用である場合は True。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックスにある要素。</returns>
      <param name="index">要素のインデックス。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> 内で最初に見つかった特定のオブジェクトを削除します。</summary>
      <param name="value">削除されるオブジェクトの値。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が特定のクラス メンバーを XML 要素の配列としてシリアル化する必要があることを指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> クラスの新しいインスタンスを初期化し、XML ドキュメント インスタンスに生成される XML 要素名を指定します。</summary>
      <param name="elementName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が生成する XML 要素の名前。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.ElementName">
      <summary>シリアル化された配列に与えられた、XML 要素の名前を取得または設定します。</summary>
      <returns>シリアル化された配列の XML 要素名。既定値は、<see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> が割り当てられたメンバーの名前です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Form">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> によって生成された XML 要素名が修飾されているかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 値のいずれか。既定値は、XmlSchemaForm.None です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> で、xsi:nil 属性が true に設定された空の XML タグとしてメンバーをシリアル化する必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が xsi:nil 属性を生成する場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Namespace">
      <summary>XML 要素の名前空間を取得または設定します。</summary>
      <returns>XML 要素の名前空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Order">
      <summary>要素のシリアル化または逆シリアル化を行う明示的な順序を取得または設定します。</summary>
      <returns>コード生成の順序。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化された配列で配置できる派生型を指定する属性を表します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> クラスの新しいインスタンスを初期化し、XML ドキュメントで生成される XML 要素の名前を指定します。</summary>
      <param name="elementName">XML 要素の名前。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String,System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> クラスの新しいインスタンスを初期化し、XML ドキュメントで生成される XML 要素の名前、および生成される XML ドキュメントに挿入できる <see cref="T:System.Type" /> を指定します。</summary>
      <param name="elementName">XML 要素の名前。</param>
      <param name="type">シリアル化するオブジェクトの <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> クラスの新しいインスタンスを初期化し、シリアル化される配列に挿入できる <see cref="T:System.Type" /> を指定します。</summary>
      <param name="type">シリアル化するオブジェクトの <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.DataType">
      <summary>生成された XML 要素の XML データ型を取得または設定します。</summary>
      <returns>World Wide Web Consortium (www.w3.org) のドキュメント『XML Schema Part 2: DataTypes』で定義されている XML スキーマ定義 (XSD) データ型。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.ElementName">
      <summary>生成された XML 要素の名前を取得または設定します。</summary>
      <returns>生成された XML 要素の名前。既定値はメンバー識別子です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Form">
      <summary>生成された XML 要素名が修飾されているかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 値のいずれか。既定値は、XmlSchemaForm.None です。</returns>
      <exception cref="T:System.Exception">
        <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Form" /> プロパティが XmlSchemaForm.Unqualified に設定され、<see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace" /> 値が指定されています。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> で、xsi:nil 属性が true に設定された空の XML タグとしてメンバーをシリアル化する必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が xsi:nil 属性を生成する場合は true。それ以外の場合は false で、インスタンスは作成されません。既定値は、true です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace">
      <summary>生成された XML 要素の名前空間を取得または設定します。</summary>
      <returns>生成された XML 要素の名前空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.NestingLevel">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> が影響を与える XML 要素の階層構造のレベルを取得または設定します。</summary>
      <returns>複数の配列内の 1 つの配列のインデックスのセットの 0 から始まるインデックス番号。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Type">
      <summary>配列内で使用できる型を取得または設定します。</summary>
      <returns>配列内で使用できる <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> オブジェクトのコレクションを表します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Add(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> をコレクションに追加します。</summary>
      <returns>追加された項目のインデックス。</returns>
      <param name="attribute">コレクションに追加する <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Clear">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> からすべての要素を削除します。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Contains(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>指定した <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> がコレクションに含まれているかどうかを判断します。</summary>
      <returns>指定した <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> がコレクションに含まれている場合は true。それ以外の場合は false。</returns>
      <param name="attribute">確認する対象の <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.CopyTo(System.Xml.Serialization.XmlArrayItemAttribute[],System.Int32)">
      <summary>コピー先の指定したインデックスを開始位置として、<see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 配列をコレクションにコピーします。</summary>
      <param name="array">コレクションにコピーする <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> オブジェクトの配列。</param>
      <param name="index">コピーされた属性の開始位置のインデックス。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Count">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.GetEnumerator">
      <summary>この <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> の列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 全体の <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.IndexOf(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>コレクション内で指定した <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> が最初に見つかった位置の 0 から始まるインデックスを返します。属性がコレクション内で見つからなかった場合は -1 を返します。</summary>
      <returns>コレクション内の <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> の最初のインデックス。コレクション内に属性が存在しない場合は -1。</returns>
      <param name="attribute">コレクション内で検索する <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Insert(System.Int32,System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> をコレクション内の指定のインデックス位置に挿入します。</summary>
      <param name="index">属性が挿入される位置のインデックス。</param>
      <param name="attribute">挿入する <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Item(System.Int32)">
      <summary>指定したインデックス位置にある項目を取得または設定します。</summary>
      <returns>指定したインデックス位置にある <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</returns>
      <param name="index">取得または設定するコレクション メンバーの 0 から始まるインデックス。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Remove(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>コレクションに <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> が存在する場合は削除します。</summary>
      <param name="attribute">削除する <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.RemoveAt(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 項目を削除します。</summary>
      <param name="index">削除する項目の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> の有効なインデックスではありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>すべての <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> を互換性のある 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、コピー先の配列の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> から要素をコピーする、1 次元の <see cref="T:System.Array" /> です。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Add(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> の末尾にオブジェクトを追加します。</summary>
      <returns>
        <paramref name="value" /> が追加された位置の <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> インデックス。</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> の末尾に追加する <see cref="T:System.Object" />。値は null に設定できます。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> がコレクションに含まれているかどうかを判断します。</summary>
      <returns>指定した <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> がコレクションに含まれている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>コレクション内で指定した <see cref="T:System.Object" /> が最初に見つかった位置の 0 から始まるインデックスを返します。属性がコレクション内で見つからなかった場合は -1 を返します。</summary>
      <returns>コレクション内の <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> の最初のインデックス。コレクション内に属性が存在しない場合は -1。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 内の指定したインデックスの位置に要素を挿入します。</summary>
      <param name="index">
        <paramref name="value" /> を挿入する位置の、0 から始まるインデックス番号。</param>
      <param name="value">挿入する <see cref="T:System.Object" />。値は null に設定できます。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Xml.Serialization.XmlArrayItemAttributes.Count" /> より大きくなっています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズの場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が読み取り専用である場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>指定したインデックス位置にある項目を取得または設定します。</summary>
      <returns>指定したインデックス位置にある <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</returns>
      <param name="index">取得または設定するコレクション メンバーの 0 から始まるインデックス。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 内で最初に見つかった特定のオブジェクトを削除します。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がクラス メンバーを XML 属性としてシリアル化する必要があることを指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> クラスの新しいインスタンスを初期化し、生成される XML 属性の名前を指定します。</summary>
      <param name="attributeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が生成する XML 属性の名前。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String,System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="attributeName">生成される XML 属性の名前。</param>
      <param name="type">属性を取得するために使用する <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="type">属性を取得するために使用する <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.AttributeName">
      <summary>XML 属性の名前を取得または設定します。</summary>
      <returns>XML 属性の名前。既定値はメンバー名です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.DataType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> によって生成された XML 属性の XSD データ型を取得または設定します。</summary>
      <returns>W3C (World Wide Web Consortium) (www.w3.org ) のドキュメント『XML Schema: DataTypes』で定義されている XSD (XML スキーマ ドキュメント) データ型。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Form">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> によって生成された XML 属性名が修飾されているかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 値のいずれか。既定値は、XmlForm.None です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Namespace">
      <summary>XML 属性の XML 名前空間を取得または設定します。</summary>
      <returns>XML 属性の XML 名前空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Type">
      <summary>XML 属性の複合型を取得または設定します。</summary>
      <returns>XML 属性の型。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeOverrides">
      <summary>オブジェクトをシリアル化または逆シリアル化するために <see cref="T:System.Xml.Serialization.XmlSerializer" /> を使用するときに、プロパティ、フィールド、クラスの各属性をユーザーがオーバーライドできるようにします。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.XmlAttributes)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクトを <see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクトのコレクションに追加します。<paramref name="type" /> パラメーターは、オーバーライドされるオブジェクトを指定します。<paramref name="member" /> パラメーターは、オーバーライドされるメンバーの名前を指定します。</summary>
      <param name="type">オーバーライドするオブジェクトの <see cref="T:System.Type" />。</param>
      <param name="member">オーバーライドするメンバーの名前。</param>
      <param name="attributes">オーバーライドする側の属性を表す <see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクト。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.Xml.Serialization.XmlAttributes)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクトを <see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクトのコレクションに追加します。<paramref name="type" /> パラメーターは、<see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクトによってオーバーライドされるオブジェクトを指定します。</summary>
      <param name="type">オーバーライドされるオブジェクトの <see cref="T:System.Type" />。</param>
      <param name="attributes">オーバーライドする側の属性を表す <see cref="T:System.Xml.Serialization.XmlAttributes" /> オブジェクト。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type)">
      <summary>指定された (基本クラス) 型に関連付けられたオブジェクトを取得します。</summary>
      <returns>オーバーライドする側の属性のコレクションを表す <see cref="T:System.Xml.Serialization.XmlAttributes" />。</returns>
      <param name="type">取得する属性のコレクションに関連付けられている基本クラスの <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type,System.String)">
      <summary>指定された (基本クラス) 型に関連付けられたオブジェクトを取得します。メンバー パラメーターは、オーバーライドされた基本クラス メンバーを指定します。</summary>
      <returns>オーバーライドする側の属性のコレクションを表す <see cref="T:System.Xml.Serialization.XmlAttributes" />。</returns>
      <param name="type">使用する属性のコレクションに関連付けられている基本クラスの <see cref="T:System.Type" />。</param>
      <param name="member">返す <see cref="T:System.Xml.Serialization.XmlAttributes" /> を指定する、オーバーライドされたメンバーの名前。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がオブジェクトをシリアル化および逆シリアル化する方法を制御する属性オブジェクトのコレクションを表します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributes" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyAttribute">
      <summary>オーバーライドする <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> を取得または設定します。</summary>
      <returns>オーバーライドする <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyElements">
      <summary>オーバーライドする <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> オブジェクトのコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> オブジェクトのコレクションを表す <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArray">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が、配列を返すパブリック フィールドまたは読み取り/書き込みプロパティをシリアル化する方法を指定するオブジェクトを取得または設定します。</summary>
      <returns>配列を返すパブリック フィールドまたは読み取り/書き込みプロパティを <see cref="T:System.Xml.Serialization.XmlSerializer" /> でシリアル化する方法を指定する <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArrayItems">
      <summary>パブリック フィールドまたは読み取り/書き込みプロパティによって返された配列に挿入されている項目を <see cref="T:System.Xml.Serialization.XmlSerializer" /> によってシリアル化する方法を指定するオブジェクトのコレクションを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> オブジェクトのコレクションを格納している <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が、パブリック フィールドまたはパブリックな読み取り/書き込みプロパティを XML 属性としてシリアル化する方法を指定するオブジェクトを取得または設定します。</summary>
      <returns>パブリック フィールドまたは読み取り/書き込みプロパティを XML 属性としてシリアル化する方法を制御する <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlChoiceIdentifier">
      <summary>複数の選択肢を区別できるようにするオブジェクトを取得または設定します。</summary>
      <returns>xsi:choice 要素としてシリアル化されているクラス メンバーに適用できる <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlDefaultValue">
      <summary>XML 要素または XML 属性の既定値を取得または設定します。</summary>
      <returns>XML 要素または XML 属性の既定値を表す <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlElements">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がパブリック フィールドまたは読み取り/書き込みプロパティを XML 要素としてシリアル化する方法を指定する、オブジェクトのコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> オブジェクトのコレクションを格納している <see cref="T:System.Xml.Serialization.XmlElementAttributes" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlEnum">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が列挙体メンバーをシリアル化する方法を指定するオブジェクトを取得または指定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が列挙体メンバーをシリアル化する方法を指定する <see cref="T:System.Xml.Serialization.XmlEnumAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlIgnore">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がパブリック フィールドまたは読み書き可能なパブリック プロパティをシリアル化するかどうかを指定する値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がそのフィールドまたはプロパティをシリアル化しない場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.Xmlns">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> オブジェクトを返すメンバーを格納するオブジェクトがオーバーライドされたときに、すべての名前空間宣言を保持するかどうかを示す値を取得または設定します。</summary>
      <returns>名前空間宣言を保持する場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlRoot">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がクラスを XML ルート要素としてシリアル化する方法を指定するオブジェクトを取得または指定します。</summary>
      <returns>XML ルート要素として属性が設定されているクラスをオーバーライドする <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlText">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> に対してパブリック フィールドまたはパブリックな読み取り/書き込みプロパティを XML テキストとしてシリアル化するよう指示するオブジェクトを取得または設定します。</summary>
      <returns>パブリック プロパティまたはフィールドの既定のシリアル化をオーバーライドする <see cref="T:System.Xml.Serialization.XmlTextAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> が適用されているクラスを <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化する方法を指定するオブジェクトを取得または指定します。</summary>
      <returns>クラス宣言に適用された <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> をオーバーライドする <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute">
      <summary>列挙体を使用して、メンバーを明確に検出できるようにすることを指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">メンバーを検出するために使用される列挙体を返すメンバー名。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlChoiceIdentifierAttribute.MemberName">
      <summary>型を検出するときに使用される列挙体を返すフィールドの名前を取得または設定します。</summary>
      <returns>列挙体を返すフィールドの名前。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttribute">
      <summary>パブリック フィールドまたはパブリック プロパティを持つオブジェクトを <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化または逆シリアル化するときに、それらのフィールドまたはプロパティが XML 要素を表すかどうかを示します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> クラスの新しいインスタンスを初期化し、XML 要素の名前を指定します。</summary>
      <param name="elementName">シリアル化されたメンバーの XML 要素名。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String,System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> の新しいインスタンスを初期化し、<see cref="T:System.Xml.Serialization.XmlElementAttribute" /> の適用先であるメンバーの XML 要素の名前と派生型を指定します。このメンバー型が使用されるのは、その型を含むオブジェクトを <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化する場合です。</summary>
      <param name="elementName">シリアル化されたメンバーの XML 要素名。</param>
      <param name="type">メンバーの型から派生したオブジェクトの <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> クラスの新しいインスタンスを初期化し、<see cref="T:System.Xml.Serialization.XmlElementAttribute" /> の適用先のメンバーの型を指定します。この型が使用されるのは、その型を含むオブジェクトを <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化または逆シリアル化する場合です。</summary>
      <param name="type">メンバーの型から派生したオブジェクトの <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.DataType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> によって生成された XML 要素の XML スキーマ定義 (XSD: XML Schema Definition) データ型を取得または設定します。</summary>
      <returns>W3C (World Wide Web Consortium) (www.w3.org ) のドキュメント『XML Schema Part 2: Datatypes』で定義されている XML スキーマ データ型。</returns>
      <exception cref="T:System.Exception">指定した XML スキーマ データ型を .NET データ型に割り当てることはできません。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.ElementName">
      <summary>生成された XML 要素の名前を取得または設定します。</summary>
      <returns>生成された XML 要素の名前。既定値はメンバー識別子です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Form">
      <summary>要素が修飾されているかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 値のいずれか。既定値は、<see cref="F:System.Xml.Schema.XmlSchemaForm.None" /> です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が、null に設定されているメンバーを、xsi:nil 属性が true に設定されている空タグとしてシリアル化する必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が xsi:nil 属性を生成する場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Namespace">
      <summary>クラスがシリアル化されたときに、結果として XML 要素に割り当てられた名前空間を取得または設定します。</summary>
      <returns>XML 要素の名前空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Order">
      <summary>要素のシリアル化または逆シリアル化を行う明示的な順序を取得または設定します。</summary>
      <returns>コード生成の順序。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Type">
      <summary>XML 要素を表すために使用されるオブジェクト型を取得または設定します。</summary>
      <returns>メンバーの <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がクラスをシリアル化する既定の方法をオーバーライドするために使用する、<see cref="T:System.Xml.Serialization.XmlElementAttribute" /> オブジェクトのコレクションを表します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Add(System.Xml.Serialization.XmlElementAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> をコレクションに追加します。</summary>
      <returns>新しく追加された項目の 0 から始まるインデックス。</returns>
      <param name="attribute">追加する <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Clear">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> からすべての要素を削除します。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Contains(System.Xml.Serialization.XmlElementAttribute)">
      <summary>指定したオブジェクトがコレクションに格納されているかどうかを確認します。</summary>
      <returns>オブジェクトがコレクション内に存在する場合は true。それ以外の場合は false。</returns>
      <param name="attribute">検索対象の <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.CopyTo(System.Xml.Serialization.XmlElementAttribute[],System.Int32)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> またはその一部を 1 次元配列にコピーします。</summary>
      <param name="array">コピーされた要素を保つための <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> アレー。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Count">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> に格納されている要素の数を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> に格納されている要素の数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.GetEnumerator">
      <summary>この <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> の列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 全体の <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.IndexOf(System.Xml.Serialization.XmlElementAttribute)">
      <summary>指定した <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> のインデックスを取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> の 0 から始まるインデックス番号。</returns>
      <param name="attribute">インデックスを取得する <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlElementAttribute)">
      <summary>コレクションに <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> を挿入します。</summary>
      <param name="index">メンバーが挿入される 0 から始まるインデックス。</param>
      <param name="attribute">挿入する <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックスにある要素。</returns>
      <param name="index">取得または設定する要素の、0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> の有効なインデックスではありません。</exception>
      <exception cref="T:System.NotSupportedException">このプロパティが設定されていますが、<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が読み取り専用です。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Remove(System.Xml.Serialization.XmlElementAttribute)">
      <summary>指定されたオブジェクトをコレクションから削除します。</summary>
      <param name="attribute">コレクションから削除する <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.RemoveAt(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 項目を削除します。</summary>
      <param name="index">削除する項目の 0 から始まるインデックス。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> の有効なインデックスではありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>すべての <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> を互換性のある 1 次元の <see cref="T:System.Array" /> にコピーします。コピー操作は、コピー先の配列の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> から要素をコピーする、1 次元の <see cref="T:System.Array" /> です。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> へのアクセスを同期するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> の末尾にオブジェクトを追加します。</summary>
      <returns>
        <paramref name="value" /> が追加された位置の <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> インデックス。</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> の末尾に追加する <see cref="T:System.Object" />。値は null に設定できます。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> に特定の値が格納されているかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Object" /> が <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> に存在する場合は true。それ以外の場合は false。</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 内で検索するオブジェクト。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 内での指定した項目のインデックスを調べます。</summary>
      <returns>リストに存在する場合は <paramref name="value" /> のインデックス。それ以外の場合は -1。</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 内で検索するオブジェクト。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 内の指定したインデックスの位置に要素を挿入します。</summary>
      <param name="index">
        <paramref name="value" /> を挿入する位置の、0 から始まるインデックス番号。</param>
      <param name="value">挿入する <see cref="T:System.Object" />。値は null に設定できます。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。または<paramref name="index" /> が <see cref="P:System.Xml.Serialization.XmlElementAttributes.Count" /> より大きくなっています。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズの場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が読み取り専用である場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックスにある要素。</returns>
      <param name="index">取得または設定する要素の、0 から始まるインデックス番号。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> の有効なインデックスではありません。</exception>
      <exception cref="T:System.NotSupportedException">このプロパティが設定されていますが、<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が読み取り専用です。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 内で最初に見つかった特定のオブジェクトを削除します。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> は読み取り専用です。または<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> が固定サイズです。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlEnumAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が列挙体メンバーをシリアル化する方法を制御します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> クラスの新しいインスタンスを初期化し、<see cref="T:System.Xml.Serialization.XmlSerializer" /> が生成する (列挙体をシリアル化する場合) または認識する (列挙体を逆シリアル化する場合) XML 値を指定します。</summary>
      <param name="name">オーバーライドする側の列挙体メンバーの名前。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlEnumAttribute.Name">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が列挙体をシリアル化する場合は XML ドキュメント インスタンスに生成された値を、列挙体メンバーを逆シリアル化する場合は認識した値を、取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が列挙体をシリアル化する場合は XML ドキュメント インスタンスに生成された値、列挙体メンバーを逆シリアル化する場合は認識した値。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlIgnoreAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> の <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> メソッドに対して、パブリック フィールドまたはパブリックな読み書き可能プロパティの値をシリアル化しないように指示します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIgnoreAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlIgnoreAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlIncludeAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がオブジェクトをシリアル化または逆シリアル化するときに、型を認識できるようにします。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIncludeAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="type">含めるオブジェクトの <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlIncludeAttribute.Type">
      <summary>含めるオブジェクトの型を取得または設定します。</summary>
      <returns>含めるオブジェクトの <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute">
      <summary>対象となるプロパティ、パラメーター、戻り値、またはクラス メンバーに、XML ドキュメント内で使用する、名前空間に関連付けられたプレフィックスを含めることを指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlRootAttribute">
      <summary>属性ターゲットを XML ルート要素として XML にシリアル化する方法を制御します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> クラスの新しいインスタンスを初期化し、XML ルート要素の名前を指定します。</summary>
      <param name="elementName">XML ルート要素の名前。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.DataType">
      <summary>XML ルート要素の XSD データ型を取得または設定します。</summary>
      <returns>W3C (World Wide Web Consortium) (www.w3.org ) のドキュメント『XML Schema: DataTypes』で定義されている XSD (XML スキーマ ドキュメント) データ型。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.ElementName">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> メソッドおよび <see cref="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)" /> メソッドによって生成および認識される XML 要素名を取得または設定します。</summary>
      <returns>XML ドキュメント インスタンスで生成および認識された XML ルート要素名。既定値は、シリアル化されたクラスの名前です。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> で、null に設定されているメンバーを、true に設定されている xsi:nil 属性にシリアル化するかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が xsi:nil 属性を生成する場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.Namespace">
      <summary>XML ルート要素の名前空間を取得または設定します。</summary>
      <returns>XML 要素の名前空間。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializer">
      <summary>オブジェクトから XML ドキュメントへのシリアル化および XML ドキュメントからオブジェクトへの逆シリアル化を行います。<see cref="T:System.Xml.Serialization.XmlSerializer" /> により、オブジェクトを XML にエンコードする方法を制御できます。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type)">
      <summary>指定した型のオブジェクトを XML ドキュメントにシリアル化したり、XML ドキュメントを指定した型のオブジェクトに逆シリアル化したりできる、<see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="type">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化できるオブジェクトの型。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.String)">
      <summary>指定した型のオブジェクトを XML ドキュメントにシリアル化したり、XML ドキュメントを指定した型のオブジェクトに逆シリアル化したりできる、<see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。すべての XML 要素の既定の名前空間を指定します。</summary>
      <param name="type">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化できるオブジェクトの型。</param>
      <param name="defaultNamespace">すべての XML 要素で使用する既定の名前空間。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Type[])">
      <summary>指定した型のオブジェクトを XML ドキュメントにシリアル化したり、XML ドキュメントを指定した型のオブジェクトに逆シリアル化したりできる、<see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。プロパティまたはフィールドが配列を返す場合、<paramref name="extraTypes" /> パラメーターには、その配列に挿入できるオブジェクトを指定します。</summary>
      <param name="type">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化できるオブジェクトの型。</param>
      <param name="extraTypes">シリアル化する追加のオブジェクト型の <see cref="T:System.Type" /> 配列。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>指定した型のオブジェクトを XML ドキュメントにシリアル化したり、XML ドキュメントを指定した型のオブジェクトに逆シリアル化したりできる、<see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。シリアル化される各オブジェクトはそれ自体がクラスのインスタンスを含むことができ、それをこのオーバーロードによって他のクラスでオーバーライドします。</summary>
      <param name="type">シリアル化するオブジェクトの型。</param>
      <param name="overrides">
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>
        <see cref="T:System.Object" /> 型のオブジェクトを XML ドキュメント インスタンスにシリアル化したり、XML ドキュメント インスタンスを <see cref="T:System.Object" /> 型のオブジェクトに逆シリアル化したりできる、<see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。シリアル化される各オブジェクトはそれ自体がクラスのインスタンスを含むことができ、それをこのオーバーロードによって他のクラスでオーバーライドします。このオーバーロードでは、すべての XML 要素の既定の名前空間、および XML ルート要素として使用するクラスも指定します。</summary>
      <param name="type">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化できるオブジェクトの型。</param>
      <param name="overrides">
        <paramref name="type" /> パラメーターで指定されたクラスの動作を拡張またはオーバーライドする <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />。</param>
      <param name="extraTypes">シリアル化する追加のオブジェクト型の <see cref="T:System.Type" /> 配列。</param>
      <param name="root">XML ルート要素プロパティを定義する <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</param>
      <param name="defaultNamespace">XML ドキュメント内のすべての XML 要素の既定の名前空間。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>指定した型のオブジェクトを XML ドキュメントにシリアル化したり、XML ドキュメントを指定した型のオブジェクトに逆シリアル化したりできる、<see cref="T:System.Xml.Serialization.XmlSerializer" /> クラスの新しいインスタンスを初期化します。また、XML ルート要素として使用するクラスを指定します。</summary>
      <param name="type">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がシリアル化できるオブジェクトの型。</param>
      <param name="root">XML ルート要素を表す <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CanDeserialize(System.Xml.XmlReader)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が、指定された XML ドキュメントを逆シリアル化できるかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Xml.XmlReader" /> が指すオブジェクトを <see cref="T:System.Xml.Serialization.XmlSerializer" /> が逆シリアル化できる場合は true。それ以外の場合は false。</returns>
      <param name="xmlReader">逆シリアル化するドキュメントを指す <see cref="T:System.Xml.XmlReader" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)">
      <summary>指定した <see cref="T:System.IO.Stream" /> に格納されている XML ドキュメントを逆シリアル化します。</summary>
      <returns>逆シリアル化される <see cref="T:System.Object" />。</returns>
      <param name="stream">逆シリアル化する XML ドキュメントを格納している <see cref="T:System.IO.Stream" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.TextReader)">
      <summary>指定した <see cref="T:System.IO.TextReader" /> に格納されている XML ドキュメントを逆シリアル化します。</summary>
      <returns>逆シリアル化される <see cref="T:System.Object" />。</returns>
      <param name="textReader">逆シリアル化する XML ドキュメントを格納している <see cref="T:System.IO.TextReader" />。</param>
      <exception cref="T:System.InvalidOperationException">逆シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader)">
      <summary>指定した <see cref="T:System.xml.XmlReader" /> に格納されている XML ドキュメントを逆シリアル化します。</summary>
      <returns>逆シリアル化される <see cref="T:System.Object" />。</returns>
      <param name="xmlReader">逆シリアル化する XML ドキュメントを格納している <see cref="T:System.xml.XmlReader" />。</param>
      <exception cref="T:System.InvalidOperationException">逆シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromTypes(System.Type[])">
      <summary>型の配列から作成された、<see cref="T:System.Xml.Serialization.XmlSerializer" /> オブジェクトの配列を返します。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> オブジェクトの配列。</returns>
      <param name="types">
        <see cref="T:System.Type" /> オブジェクトの配列。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>指定した <see cref="T:System.Object" /> をシリアル化し、生成された XML ドキュメントを、指定した <see cref="T:System.IO.Stream" /> を使用してファイルに書き込みます。</summary>
      <param name="stream">XML ドキュメントを書き込むために使用する <see cref="T:System.IO.Stream" />。</param>
      <param name="o">シリアル化する <see cref="T:System.Object" />。</param>
      <exception cref="T:System.InvalidOperationException">シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>指定した <see cref="T:System.Object" /> をシリアル化し、指定した <see cref="T:System.IO.Stream" /> を使用して、指定した名前空間を参照し、生成された XML ドキュメントをファイルに書き込みます。</summary>
      <param name="stream">XML ドキュメントを書き込むために使用する <see cref="T:System.IO.Stream" />。</param>
      <param name="o">シリアル化する <see cref="T:System.Object" />。</param>
      <param name="namespaces">オブジェクトが参照する <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)">
      <summary>指定した <see cref="T:System.Object" /> をシリアル化し、生成された XML ドキュメントを、指定した <see cref="T:System.IO.TextWriter" /> を使用してファイルに書き込みます。</summary>
      <param name="textWriter">XML ドキュメントを書き込むために使用する <see cref="T:System.IO.TextWriter" />。</param>
      <param name="o">シリアル化する <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>指定した <see cref="T:System.Object" /> をシリアル化し、指定した <see cref="T:System.IO.TextWriter" /> を使用して XML ドキュメントをファイルに書き込み、指定した名前空間を参照します。</summary>
      <param name="textWriter">XML ドキュメントを書き込むために使用する <see cref="T:System.IO.TextWriter" />。</param>
      <param name="o">シリアル化する <see cref="T:System.Object" />。</param>
      <param name="namespaces">生成された XML ドキュメントで使用する名前空間を格納している <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object)">
      <summary>指定した <see cref="T:System.Object" /> をシリアル化し、生成された XML ドキュメントを、指定した <see cref="T:System.Xml.XmlWriter" /> を使用してファイルに書き込みます。</summary>
      <param name="xmlWriter">XML ドキュメントを書き込むために使用する <see cref="T:System.xml.XmlWriter" />。</param>
      <param name="o">シリアル化する <see cref="T:System.Object" />。</param>
      <exception cref="T:System.InvalidOperationException">シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>指定した <see cref="T:System.Object" /> をシリアル化し、指定した <see cref="T:System.Xml.XmlWriter" /> を使用して XML ドキュメントをファイルに書き込み、指定した名前空間を参照します。</summary>
      <param name="xmlWriter">XML ドキュメントを書き込むために使用する <see cref="T:System.xml.XmlWriter" />。</param>
      <param name="o">シリアル化する <see cref="T:System.Object" />。</param>
      <param name="namespaces">オブジェクトが参照する <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">シリアル化中にエラーが発生しました。元の例外には、<see cref="P:System.Exception.InnerException" /> プロパティを使用してアクセスできます。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerNamespaces">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が XML ドキュメント インスタンスで修飾名を生成するために使用する XML 名前空間とプレフィックスが格納されています。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>プレフィックスと名前空間のペアのコレクションを保持する XmlSerializerNamespaces のインスタンスを指定して、<see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="namespaces">名前空間とプレフィックスのペアを保持する <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> のインスタンス。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.XmlQualifiedName[])">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="namespaces">
        <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトの配列。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.Add(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> オブジェクトにプレフィックスと名前空間のペアを追加します。</summary>
      <param name="prefix">XML 名前空間に関連付けられているプリフィックス。</param>
      <param name="ns">XML 名前空間。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerNamespaces.Count">
      <summary>コレクション内のプレフィックスと名前空間のペアの数を取得します。</summary>
      <returns>コレクション内のプレフィックスと名前空間のペアの数。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.ToArray">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> オブジェクト内のプレフィックスと名前空間のペアの配列を取得します。</summary>
      <returns>XML ドキュメントで修飾名として使用される <see cref="T:System.Xml.XmlQualifiedName" /> オブジェクトの配列。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTextAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> が、クラスをシリアル化または逆シリアル化するときに、そのクラスに含まれる特定のメンバーを XML テキストとして処理する必要があることを指定します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="type">シリアル化するメンバーの <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.DataType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> によって生成されたテキストの XML スキーマ定義言語 (XSD: XML Schema Definition Language) データ型を取得または設定します。</summary>
      <returns>W3C (World Wide Web Consortium) (www.w3.org ) のドキュメント『XML Schema Part 2: Datatypes』で定義されている XML スキーマ (XSD) データ型。</returns>
      <exception cref="T:System.Exception">指定した XML スキーマ データ型を .NET データ型に割り当てることはできません。</exception>
      <exception cref="T:System.InvalidOperationException">指定した XML スキーマ データ型はプロパティとしては無効なので、そのメンバー型に変換できません。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.Type">
      <summary>メンバーの型を取得または設定します。</summary>
      <returns>メンバーの <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeAttribute">
      <summary>この属性が適用された対象が <see cref="T:System.Xml.Serialization.XmlSerializer" /> によってシリアル化されるときに生成される XML スキーマを制御します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> クラスの新しいインスタンスを初期化し、XML 型の名前を指定します。</summary>
      <param name="typeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> がクラス インスタンスをシリアル化する場合に生成する (およびクラス インスタンスを逆シリアル化する場合に認識する) XML 型の名前。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.AnonymousType">
      <summary>結果のスキーマ型が XSD 匿名型であるかどうかを判断する値を取得または設定します。</summary>
      <returns>結果のスキーマ型が XSD 匿名型である場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.IncludeInSchema">
      <summary>XML スキーマ ドキュメントに型を含めるかどうかを示す値を取得または設定します。</summary>
      <returns>XML スキーマ ドキュメントに型を含める場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.Namespace">
      <summary>XML 型の名前空間を取得または設定します。</summary>
      <returns>XML 型の名前空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.TypeName">
      <summary>XML 型の名前を取得または設定します。</summary>
      <returns>XML 型の名前。</returns>
    </member>
  </members>
</doc>