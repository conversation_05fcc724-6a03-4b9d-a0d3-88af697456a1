<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XmlSerializer</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Serialization.XmlAnyAttributeAttribute">
      <summary>Указывает, что член (поле, возвращающее массив объектов <see cref="T:System.Xml.XmlAttribute" />) может содержать любые атрибуты XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyAttributeAttribute.#ctor">
      <summary>Конструирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttribute">
      <summary>Указывает, что член (поле, возвращающее массив объектов <see cref="T:System.Xml.XmlElement" /> или <see cref="T:System.Xml.XmlNode" />) содержит объекты, представляющие любые элементы XML, не имеющие соответствующего члена в сериализуемом или десериализуемом объекте.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> и указывает имя элемента XML, сгенерированного в документе XML.</summary>
      <param name="name">Имя XML-элемента, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String,System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> и указывает имя элемента XML, сгенерированного в документе XML, и его пространство имен XML.</summary>
      <param name="name">Имя XML-элемента, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
      <param name="ns">Пространство имен XML элемента XML. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Name">
      <summary>Возвращает или задает имя элемента XML.</summary>
      <returns>Имя элемента XML.</returns>
      <exception cref="T:System.InvalidOperationException">Имя элемента члена массива не соответствует имени элемента, указанному свойством <see cref="P:System.Xml.Serialization.XmlAnyElementAttribute.Name" />. </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Namespace">
      <summary>Возвращает или задает пространство имен XML, сгенерированное в документе XML.</summary>
      <returns>Пространство имен XML.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Order">
      <summary>Получает или задает порядок сериализации или десериализации элементов.</summary>
      <returns>Порядок генерирования кода.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttributes">
      <summary>Представляет коллекцию объектов <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" />. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Add(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Добавляет в коллекцию объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</summary>
      <returns>Индекс только что добавленного объекта <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</returns>
      <param name="attribute">Добавляемый объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Clear">
      <summary>Удаляет все объекты из <see cref="T:System.Collections.CollectionBaseinstance" />.Этот метод не может быть переопределен.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Contains(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Получает значение, которое указывает, содержится ли заданный объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> в коллекции.</summary>
      <returns>Значение true, если объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> содержится в коллекции; в противном случае — значение false.</returns>
      <param name="attribute">Нужный объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.CopyTo(System.Xml.Serialization.XmlAnyElementAttribute[],System.Int32)">
      <summary>Копирует коллекцию целиком в совместимый одномерный массив объектов <see cref="T:System.Xml.Serialization.XmlElementAttribute" />, начиная с заданного индекса целевого массива. </summary>
      <param name="array">Одномерный массив объектов <see cref="T:System.Xml.Serialization.XmlElementAttribute" />, который является конечным объектом копирования элементов коллекции.Индексация в массиве должна вестись с нуля.</param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Count">
      <summary>Получает число элементов, содержащихся в экземпляре класса <see cref="T:System.Collections.CollectionBase" />.</summary>
      <returns>Число элементов, содержащихся в экземпляре класса <see cref="T:System.Collections.CollectionBase" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Collections.CollectionBaseinstance" />.</summary>
      <returns>Перечислитель, выполняющий итерацию в наборе <see cref="T:System.Collections.CollectionBaseinstance" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.IndexOf(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Получает индекс заданного ограничения <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</summary>
      <returns>Индекс указанного объекта <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</returns>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> с нужным индексом. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Вставляет объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> в коллекцию по заданному индексу.</summary>
      <param name="index">Индекс вставки элемента <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />. </param>
      <param name="attribute">Вставляемый объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Item(System.Int32)">
      <summary>Получает или задает объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> с указанным индексом.</summary>
      <returns>Объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> по указанному индексу.</returns>
      <param name="index">Индекс объекта <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Remove(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Удаляет указанную панель объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> из коллекции.</summary>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> для удаления. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.RemoveAt(System.Int32)">
      <summary>Удаляет элемент списка <see cref="T:System.Collections.CollectionBaseinstance" /> с указанным индексом.Этот метод не может быть переопределен.</summary>
      <param name="index">Индекс элемента, который должен быть удален.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует коллекцию целиком в совместимый одномерный массив объектов <see cref="T:System.Xml.Serialization.XmlElementAttribute" />, начиная с заданного индекса целевого массива.</summary>
      <param name="array">Одномерный массив.</param>
      <param name="index">Заданный индекс.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Collections.CollectionBase" /> синхронизированным (потокобезопасным).</summary>
      <returns>True, если доступ к коллекции <see cref="T:System.Collections.CollectionBase" /> является синхронизированным; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Collections.CollectionBase" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к коллекции <see cref="T:System.Collections.CollectionBase" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>Добавляет объект в конец коллекции <see cref="T:System.Collections.CollectionBase" />.</summary>
      <returns>Добавленный объект для коллекции.</returns>
      <param name="value">Значение объекта для добавления в коллекцию.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>Определяет, содержит ли интерфейс <see cref="T:System.Collections.CollectionBase" /> определенный элемент.</summary>
      <returns>True, если <see cref="T:System.Collections.CollectionBase" /> содержит определенный элемент; в противном случае — значение false.</returns>
      <param name="value">Значение элемента.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>Осуществляет поиск указанного объекта и возвращает индекс (с нуля) первого вхождения, найденного в пределах всего <see cref="T:System.Collections.CollectionBase" />.</summary>
      <returns>Отсчитываемый от нуля индекс объекта.</returns>
      <param name="value">Значение объекта.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Добавляет элемент в список <see cref="T:System.Collections.CollectionBase" /> в позиции с указанным индексом.</summary>
      <param name="index">Индекс, указывающий, куда вставить элемент.</param>
      <param name="value">Значение элемента.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>Получает значение, показывающее, имеет ли <see cref="T:System.Collections.CollectionBasehas" /> фиксированный размер.</summary>
      <returns>True, если коллекция <see cref="T:System.Collections.CollectionBasehas" /> имеет фиксированный размер; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>Получает значение, указывающее, доступна ли <see cref="T:System.Collections.CollectionBase" /> только для чтения.</summary>
      <returns>Значение True, если <see cref="T:System.Collections.CollectionBase" /> доступна только для чтения; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>Получает или задает элемент с указанным индексом.</summary>
      <returns>Элемент с заданным индексом.</returns>
      <param name="index">Индекс элемента.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Collections.CollectionBase" />.</summary>
      <param name="value">Значение удаленного объекта.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayAttribute">
      <summary>Указывает, что <see cref="T:System.Xml.Serialization.XmlSerializer" /> необходимо выполнить сериализацию конкретного члена класса в качестве массива XML-элементов.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> и указывает имя XML-элемента, созданного в экземпляре XML-документа.</summary>
      <param name="elementName">Имя XML-элемента, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.ElementName">
      <summary>Получает или задает имя XML-элемента, присвоенное сериализованному массиву.</summary>
      <returns>Имя XML-элемента сериализованного массива.По умолчанию используется имя члена, которому назначается <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Form">
      <summary>Получает или задает значение, которое показывает, является ли имя XML-элемента, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />, квалифицированным или неквалифицированным.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.Schema.XmlSchemaForm" />.Значение по умолчанию — XmlSchemaForm.None.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.IsNullable">
      <summary>Получает или задает значение, которое показывает, должен ли <see cref="T:System.Xml.Serialization.XmlSerializer" /> выполнить сериализацию члена как пустого тега XML с атрибутом xsi:nil, для которого установлено значение true.</summary>
      <returns>true, если <see cref="T:System.Xml.Serialization.XmlSerializer" /> создает атрибут xsi:nil; в противном случае, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Namespace">
      <summary>Получает или задает пространство имен XML-элемента.</summary>
      <returns>Пространство имен XML-элемента.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Order">
      <summary>Получает или задает порядок сериализации или десериализации элементов.</summary>
      <returns>Порядок генерирования кода.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttribute">
      <summary>Представляет атрибут, который определяет производные типы, которые могут быть размещены <see cref="T:System.Xml.Serialization.XmlSerializer" /> в сериализованном массиве.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> и указывает имя элемента XML, сгенерированного в документе XML.</summary>
      <param name="elementName">Имя элемента XML. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String,System.Type)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> и определяет имя элемента XML, сгенерированного в документе XML, и <see cref="T:System.Type" />, который может быть вставлен в сгенерированный документ XML.</summary>
      <param name="elementName">Имя элемента XML. </param>
      <param name="type">Тип <see cref="T:System.Type" /> сериализуемого объекта. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.Type)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> и определяет <see cref="T:System.Type" />, который может быть вставлен в сериализованный массив.</summary>
      <param name="type">Тип <see cref="T:System.Type" /> сериализуемого объекта. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.DataType">
      <summary>Возвращает или задает тип данных XML сгенерированного элемента XML.</summary>
      <returns>Тип данных определения схемы XML (XSD) согласно документу "Схема XML, часть 2: типы данных" консорциума World Wide Web (www.w3.org).</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.ElementName">
      <summary>Получает или задает имя созданного XML-элемента</summary>
      <returns>Имя созданного XML-элемента.По умолчанию используется идентификатор члена</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Form">
      <summary>Возвращает или задает значение, которое указывает, является ли имя сгенерированного элемента XML полным.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.Schema.XmlSchemaForm" />.Значение по умолчанию — XmlSchemaForm.None.</returns>
      <exception cref="T:System.Exception">Свойство <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Form" /> имеет значение XmlSchemaForm.Unqualified, а свойство <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace" /> задано. </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.IsNullable">
      <summary>Получает или задает значение, которое показывает, должен ли <see cref="T:System.Xml.Serialization.XmlSerializer" /> выполнить сериализацию члена как пустого тега XML с атрибутом xsi:nil, для которого установлено значение true.</summary>
      <returns>true, если <see cref="T:System.Xml.Serialization.XmlSerializer" /> генерирует атрибут xsi:nil, в противном случае false, а экземпляр не генерируется.Значение по умолчанию — true.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace">
      <summary>Возвращает или задает пространство имен сгенерированного элемента XML.</summary>
      <returns>Пространство имен сгенерированного элемента XML.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.NestingLevel">
      <summary>Возвращает или задает уровень в иерархии элементов XML, на который воздействует <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.</summary>
      <returns>Индекс, начинающийся с нуля, набора индексов в массиве массивов.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Type">
      <summary>Возвращает или задает тип, допустимый в массиве.</summary>
      <returns>
        <see cref="T:System.Type" />, допустимый в массиве.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttributes">
      <summary>Представляет коллекцию объектов <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Add(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Добавляет в коллекцию объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.</summary>
      <returns>Индекс добавляемого элемента.</returns>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />, добавляемый в коллекцию. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Clear">
      <summary>Удаляет все элементы из коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</summary>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Contains(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Определяет, содержит ли коллекция указанный <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />. </summary>
      <returns>Значение true, если коллекция содержит заданный объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />; в противном случае — значение false.</returns>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> для проверки.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.CopyTo(System.Xml.Serialization.XmlArrayItemAttribute[],System.Int32)">
      <summary>Копирует в коллекцию массив <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />, начиная с заданного индекса целевого объекта. </summary>
      <param name="array">Массив объектов <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> для копирования в коллекцию.</param>
      <param name="index">Индекс, по которому будут расположены скопированные атрибуты.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.GetEnumerator">
      <summary>Возвращает перечислитель для класса <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.IEnumerator" /> для массива <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.IndexOf(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Возвращает отсчитываемый от нуля индекс первого вхождения заданного <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> в коллекции либо значение -1, если атрибут не обнаружен в коллекции. </summary>
      <returns>Первый индекс объекта <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> в коллекции или значение -1, если атрибут не обнаружен в коллекции.</returns>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> для поиска в коллекции.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Insert(System.Int32,System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Вставляет элемент <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> в коллекцию по заданному индексу. </summary>
      <param name="index">Индекс, по которому вставлен атрибут.</param>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> для вставки.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Item(System.Int32)">
      <summary>Возвращает или задает элемент с указанным индексом.</summary>
      <returns>Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> с указанным индексом.</returns>
      <param name="index">Начинающийся с нуля индекс полученного или заданного члена коллекции. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Remove(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Удаляет объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> из коллекции, если он содержится в ней. </summary>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> для удаления.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.RemoveAt(System.Int32)">
      <summary>Удаляет элемент <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> по указанному индексу.</summary>
      <param name="index">Отсчитываемый от нуля индекс удаляемого элемента. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> не является допустимым индексом в списке <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />. </exception>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует целый массив <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> в совместимый одномерный массив <see cref="T:System.Array" />, начиная с заданного индекса целевого массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> синхронизированным (потокобезопасным).</summary>
      <returns>Значение true, если доступ к коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> является синхронизированным (потокобезопасным); в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Add(System.Object)">
      <summary>Добавляет объект в конец коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</summary>
      <returns>Индекс коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />, по которому был добавлен параметр <paramref name="value" />.</returns>
      <param name="value">Объект <see cref="T:System.Object" />, добавляемый в конец коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.Допускается значение null.</param>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>Определяет, содержит ли коллекция указанный объект <see cref="T:System.Object" />. </summary>
      <returns>Значение true, если коллекция содержит заданный объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>Возвращает отсчитываемый от нуля индекс первого вхождения заданного <see cref="T:System.Object" /> в коллекции либо значение -1, если атрибут не обнаружен в коллекции. </summary>
      <returns>Первый индекс объекта <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> в коллекции или значение -1, если атрибут не обнаружен в коллекции.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Добавляет элемент в список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> в позиции с указанным индексом.</summary>
      <param name="index">Отсчитываемый от нуля индекс, по которому следует вставить параметр <paramref name="value" />. </param>
      <param name="value">Вставляемый объект <see cref="T:System.Object" />.Допускается значение null.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.– или – Значение <paramref name="index" /> больше значения <see cref="P:System.Xml.Serialization.XmlArrayItemAttributes.Count" />. </exception>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsFixedSize">
      <summary>Получает значение, показывающее, имеет ли список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> фиксированный размер.</summary>
      <returns>Значение true, если список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> имеет фиксированный размер, в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsReadOnly">
      <summary>Получает значение, указывающее, доступна ли <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> доступна только для чтения; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>Возвращает или задает элемент с указанным индексом.</summary>
      <returns>Объект <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> с указанным индексом.</returns>
      <param name="index">Начинающийся с нуля индекс полученного или заданного члена коллекции. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />.</summary>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeAttribute">
      <summary>Указывает, что <see cref="T:System.Xml.Serialization.XmlSerializer" /> необходимо выполнить сериализацию члена класса в качестве XML-атрибута.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />, а также указывает имя созданного XML-атрибута.</summary>
      <param name="attributeName">Имя XML-атрибута, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String,System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />.</summary>
      <param name="attributeName">Имя созданного XML-атрибута. </param>
      <param name="type">
        <see cref="T:System.Type" />, используемый для хранения атрибута. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />.</summary>
      <param name="type">
        <see cref="T:System.Type" />, используемый для хранения атрибута. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.AttributeName">
      <summary>Возвращает или задает имя XML-атрибута.</summary>
      <returns>Имя XML-атрибута.По умолчанию это имя члена.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.DataType">
      <summary>Возвращает или задает тип данных XSD XML-атрибута, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <returns>Тип данных XSD (документ схемы XML), как определено документом консорциума W3C (www.w3.org) "XML-схема: Типы данных".</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Form">
      <summary>Возвращает или задает значение, которое показывает, является ли имя XML-атрибута, созданного при помощи <see cref="T:System.Xml.Serialization.XmlSerializer" />, квалифицированным.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.Schema.XmlSchemaForm" />.Значение по умолчанию — XmlForm.None.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Namespace">
      <summary>Возвращает или задает пространство имен XML для XML-атрибута.</summary>
      <returns>Пространство имен XML для XML-атрибута.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Type">
      <summary>Возвращает или задает сложный тип XML-атрибута.</summary>
      <returns>Тип XML-атрибута.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeOverrides">
      <summary>Позволяет переопределять атрибуты свойства, поля и класса при использовании <see cref="T:System.Xml.Serialization.XmlSerializer" /> для сериализации или десериализации объекта.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.XmlAttributes)">
      <summary>Добавляет объект <see cref="T:System.Xml.Serialization.XmlAttributes" /> к коллекции объектов <see cref="T:System.Xml.Serialization.XmlAttributes" />.Параметр <paramref name="type" /> указывает объект для переопределения.Параметр <paramref name="member" /> указывает имя переопределяемого члена.</summary>
      <param name="type">
        <see cref="T:System.Type" /> объекта для переопределения. </param>
      <param name="member">Имя члена для переопределения. </param>
      <param name="attributes">Объект <see cref="T:System.Xml.Serialization.XmlAttributes" />, представляющий атрибуты переопределения. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.Xml.Serialization.XmlAttributes)">
      <summary>Добавляет объект <see cref="T:System.Xml.Serialization.XmlAttributes" /> к коллекции объектов <see cref="T:System.Xml.Serialization.XmlAttributes" />.Параметр <paramref name="type" /> указывает объект для переопределения объектом <see cref="T:System.Xml.Serialization.XmlAttributes" />.</summary>
      <param name="type">
        <see cref="T:System.Type" /> переопределяемого объекта. </param>
      <param name="attributes">Объект <see cref="T:System.Xml.Serialization.XmlAttributes" />, представляющий атрибуты переопределения. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type)">
      <summary>Получает объект, ассоциированный с указанным типом базового класса.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAttributes" />, представляющий коллекцию атрибутов переопределения.</returns>
      <param name="type">Базовый класс <see cref="T:System.Type" />, ассоциированный с коллекцией атрибутов для извлечения. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type,System.String)">
      <summary>Получает объект, ассоциированный с указанным типом (базового класса).Параметр члена указывает имя переопределяемого члена базового класса.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAttributes" />, представляющий коллекцию атрибутов переопределения.</returns>
      <param name="type">Базовый класс <see cref="T:System.Type" />, ассоциированный с требуемой коллекцией атрибутов. </param>
      <param name="member">Имя переопределенного члена, указывающего <see cref="T:System.Xml.Serialization.XmlAttributes" /> для возврата. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributes">
      <summary>Представление коллекции объектов атрибутов, управляющих сериализацией и десериализацией объекта с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlAttributes" />.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyAttribute">
      <summary>Получает или задает <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> для переопределения.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> для переопределения.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyElements">
      <summary>Получение коллекции объектов <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> для переопределения.</summary>
      <returns>Объект <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" />, представляющий коллекцию объектов <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArray">
      <summary>Получает или задает объект, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для открытого поля или свойства чтения/записи, которое возвращает массив.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для открытого поля или свойства чтения/записи, которое возвращает массив.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArrayItems">
      <summary>Получает или задает коллекцию объектов, определяющих сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для элементов, которые вставлены в массив, возвращенный открытым полем или свойством чтения/записи.</summary>
      <returns>Список <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />, в котором содержится коллекция объектов <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAttribute">
      <summary>Получает или задает объект, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для открытого поля или свойства чтения/записи как атрибута XML.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />, управляющий сериализацией открытого поля или свойства чтения/записи как атрибута XML.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlChoiceIdentifier">
      <summary>Получает или задает объект, позволяющий определиться с выбором.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />, который можно применить к члену класса, который сериализуется как элемент xsi:choice.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlDefaultValue">
      <summary>Получает или задает значение по умолчанию XML-элемента или атрибута.</summary>
      <returns>Объект <see cref="T:System.Object" />, представляющей значение по умолчанию элемента XML или атрибута.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlElements">
      <summary>Получение коллекции объектов, задающих сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для открытого поля или свойства чтения/записи как элемента XML.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />, содержащий коллекцию объектов <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlEnum">
      <summary>Получает или задает объект, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для члена перечисления.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlEnumAttribute" />, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для члена перечисления.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlIgnore">
      <summary>Получает или задает значение, задающее то, будет ли выполнена сериализация с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для открытого поля или открытого свойства чтения/записи.</summary>
      <returns>true, если <see cref="T:System.Xml.Serialization.XmlSerializer" /> не должен сериализовать поле или свойство; в противном случае false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.Xmlns">
      <summary>Возвращает и задает значение, определяющее, стоит ли сохранить все объявления пространств имен, если объект с членом, возвращающим объект <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />, переопределен.</summary>
      <returns>true, если объявления пространств имен следует сохранить, иначе false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlRoot">
      <summary>Получает или задает объект, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для класса как корневого элемента XML.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" />, переопределяющий класс с атрибутами корневого элемента XML.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlText">
      <summary>Получает или задает объект, указывающий <see cref="T:System.Xml.Serialization.XmlSerializer" /> сериализовать открытое поле или свойство чтения/записи как текст XML.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlTextAttribute" />, переопределяющий сериализацию по умолчанию для открытого свойства или поля.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlType">
      <summary>Получает или задает объект, задающий сериализацию с помощью <see cref="T:System.Xml.Serialization.XmlSerializer" /> для класса, к которому был применен <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />, который переопределяет <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />, примененный к объявлению класса.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute">
      <summary>Указывает, что член может быть обнаружен посредством перечисления.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />.</summary>
      <param name="name">Имя члена, возвращающего перечисление, используемое для определения выбора. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlChoiceIdentifierAttribute.MemberName">
      <summary>Получает или задает имя поля, возвращающего перечисление для использования при определении типов.</summary>
      <returns>Имя поля, возвращающего перечисление.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttribute">
      <summary>Указывает, что открытое поле или свойство представляет XML-элемент, когда <see cref="T:System.Xml.Serialization.XmlSerializer" /> сериализует или десериализует объект, содержащий его.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> и указывает имя элемента XML.</summary>
      <param name="elementName">Имя XML-элемента сериализованного члена. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String,System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> и указывает имя XML-элемента и производного типа для члена, к которому применяется <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.Данный тип члена используйте при сериализации <see cref="T:System.Xml.Serialization.XmlSerializer" /> содержащего его объекта.</summary>
      <param name="elementName">Имя XML-элемента сериализованного члена. </param>
      <param name="type">
        <see cref="T:System.Type" /> объекта, являющегося производным от типа члена. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> и указывает тип для члена, к которому применяется <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.Данный тип используется при сериализации или десериализации <see cref="T:System.Xml.Serialization.XmlSerializer" /> содержащего его объекта.</summary>
      <param name="type">
        <see cref="T:System.Type" /> объекта, являющегося производным от типа члена. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.DataType">
      <summary>Получает или задает тип данных определения схемы XML (XSD), сгенерированного <see cref="T:System.Xml.Serialization.XmlSerializer" /> элемента XML.</summary>
      <returns>Тип данных XML-схемы в соответствии с документом консорциума W3C (www.w3.org) "XML Schema Part 2: Datatypes".</returns>
      <exception cref="T:System.Exception">Указанный тип данных XML-схемы не может иметь соответствия в типах данных .NET. </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.ElementName">
      <summary>Получает или задает имя созданного XML-элемента</summary>
      <returns>Имя созданного XML-элемента.По умолчанию используется идентификатор члена</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Form">
      <summary>Получает или задает значение, указывающее квалифицирован ли элемент.</summary>
      <returns>Одно из значений <see cref="T:System.Xml.Schema.XmlSchemaForm" />.Значение по умолчанию — <see cref="F:System.Xml.Schema.XmlSchemaForm.None" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.IsNullable">
      <summary>Получает или задает значение, которое указывает, должен ли <see cref="T:System.Xml.Serialization.XmlSerializer" /> сериализовать члена, имеющего значение null, в качестве пустого тега с атрибутом xsi:nil со значением true.</summary>
      <returns>true, если <see cref="T:System.Xml.Serialization.XmlSerializer" /> создает атрибут xsi:nil; в противном случае, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Namespace">
      <summary>Получает или задает пространство имен, присвоенное элементу XML, получаемому при сериализации класса.</summary>
      <returns>Пространство имен XML-элемента.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Order">
      <summary>Получает или задает порядок сериализации или десериализации элементов.</summary>
      <returns>Порядок генерирования кода.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Type">
      <summary>Получает или задает тип объекта, используемый для представления элемента XML.</summary>
      <returns>
        <see cref="T:System.Type" /> члена.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttributes">
      <summary>Представляет коллекцию объектов <see cref="T:System.Xml.Serialization.XmlElementAttribute" />, используемую <see cref="T:System.Xml.Serialization.XmlSerializer" /> для переопределения способа сериализации класса, используемого по умолчанию.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlElementAttributes" />. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Add(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Добавляет <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> в коллекцию.</summary>
      <returns>Отсчитываемый от нуля индекс вновь добавленного элемента.</returns>
      <param name="attribute">Добавляемый объект <see cref="T:System.Xml.Serialization.XmlElementAttribute" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Clear">
      <summary>Удаляет все элементы из коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Contains(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Определяет, содержит ли коллекция указанный объект.</summary>
      <returns>true, если объект существует в коллекции; в противном случае — значение false.</returns>
      <param name="attribute">Объект <see cref="T:System.Xml.Serialization.XmlElementAttribute" />, который нужно найти. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.CopyTo(System.Xml.Serialization.XmlElementAttribute[],System.Int32)">
      <summary>Полностью или частично копирует <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> в одномерный массив.</summary>
      <param name="array">Массив <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> для хранения скопированных элементов. </param>
      <param name="index">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Count">
      <summary>Получает число элементов, содержащихся в интерфейсе <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <returns>Число элементов, содержащихся в интерфейсе <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.GetEnumerator">
      <summary>Возвращает перечислитель для класса <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <returns>Интерфейс <see cref="T:System.Collections.IEnumerator" /> для массива <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.IndexOf(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Получает индекс заданного ограничения <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.</summary>
      <returns>Начинающийся с нуля индекс <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.</returns>
      <param name="attribute">
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" />, индекс которого требуется извлечь.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlElementAttribute)">
      <summary>Вставляет <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> в коллекцию.</summary>
      <param name="index">Отсчитываемый от нуля индекс для вставки члена. </param>
      <param name="attribute">Вставляемый объект <see cref="T:System.Xml.Serialization.XmlElementAttribute" />. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Item(System.Int32)">
      <summary>Получает или задает элемент с указанным индексом.</summary>
      <returns>Элемент с заданным индексом.</returns>
      <param name="index">Отсчитываемый с нуля индекс получаемого или задаваемого элемента. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> не является допустимым индексом в списке <see cref="T:System.Xml.Serialization.XmlElementAttributes" />. </exception>
      <exception cref="T:System.NotSupportedException">Свойство задано, и объект <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Remove(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Удаляет указанный объект из коллекции.</summary>
      <param name="attribute">
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> для удаления из коллекции. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.RemoveAt(System.Int32)">
      <summary>Удаляет элемент <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> по указанному индексу.</summary>
      <param name="index">Отсчитываемый от нуля индекс удаляемого элемента. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> не является допустимым индексом в списке <see cref="T:System.Xml.Serialization.XmlElementAttributes" />. </exception>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Копирует целый массив <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> в совместимый одномерный массив <see cref="T:System.Array" />, начиная с заданного индекса целевого массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.Индексация в массиве <see cref="T:System.Array" /> должна начинаться с нуля.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>Получает значение, показывающее, является ли доступ к коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> синхронизированным (потокобезопасным).</summary>
      <returns>Значение true, если доступ к коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> является синхронизированным (потокобезопасным); в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>Получает объект, с помощью которого можно синхронизировать доступ к коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <returns>Объект, который может использоваться для синхронизации доступа к коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>Добавляет объект в конец коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <returns>Индекс коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />, по которому был добавлен параметр <paramref name="value" />.</returns>
      <param name="value">Объект <see cref="T:System.Object" />, добавляемый в конец коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.Допускается значение null.</param>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> указанное значение.</summary>
      <returns>Значение true, если объект <see cref="T:System.Object" /> найден в списке <see cref="T:System.Xml.Serialization.XmlElementAttributes" />; в противном случае — значение false.</returns>
      <param name="value">Объект, который требуется найти в <see cref="T:System.Xml.Serialization.XmlElementAttributes" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>Определяет индекс заданного элемента коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <returns>Индекс <paramref name="value" />, если он найден в списке; в противном случае -1.</returns>
      <param name="value">Объект, который требуется найти в <see cref="T:System.Xml.Serialization.XmlElementAttributes" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Добавляет элемент в список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> в позиции с указанным индексом.</summary>
      <param name="index">Отсчитываемый от нуля индекс, по которому следует вставить параметр <paramref name="value" />. </param>
      <param name="value">Вставляемый объект <see cref="T:System.Object" />.Допускается значение null.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.– или – Значение <paramref name="index" /> больше значения <see cref="P:System.Xml.Serialization.XmlElementAttributes.Count" />. </exception>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>Получает значение, показывающее, имеет ли список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> фиксированный размер.</summary>
      <returns>Значение true, если список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> имеет фиксированный размер, в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>Получает значение, указывающее, доступна ли <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступна только для чтения; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>Получает или задает элемент с указанным индексом.</summary>
      <returns>Элемент с заданным индексом.</returns>
      <param name="index">Отсчитываемый с нуля индекс получаемого или задаваемого элемента. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="index" /> не является допустимым индексом в списке <see cref="T:System.Xml.Serialization.XmlElementAttributes" />. </exception>
      <exception cref="T:System.NotSupportedException">Свойство задано, и объект <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения. </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>Удаляет первый экземпляр указанного объекта из коллекции <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</summary>
      <exception cref="T:System.NotSupportedException">Список <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> доступен только для чтения.– или – Коллекция <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> имеет фиксированный размер. </exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlEnumAttribute">
      <summary>Управляет тем, как <see cref="T:System.Xml.Serialization.XmlSerializer" /> сериализует член перечисления.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlEnumAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> и определяет XML-значение, которое создает или распознает <see cref="T:System.Xml.Serialization.XmlSerializer" /> (при сериализации или десериализации перечисления, соответственно).</summary>
      <param name="name">Переопределяющее имя члена перечисления. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlEnumAttribute.Name">
      <summary>Получает или задает значение, создаваемое в экземпляре XML-документа, когда <see cref="T:System.Xml.Serialization.XmlSerializer" /> сериализует перечисление, или значение, распознаваемое при десериализации члена перечисления.</summary>
      <returns>Значение, создаваемое в экземпляре XML-документа, когда <see cref="T:System.Xml.Serialization.XmlSerializer" /> сериализует перечисление, или значение, распознаваемое при десериализации члена перечисления.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlIgnoreAttribute">
      <summary>Инструктирует метод <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" />, принадлежащий <see cref="T:System.Xml.Serialization.XmlSerializer" />, не сериализовывать значение открытого поля или открытого свойства чтения/записи.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIgnoreAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlIgnoreAttribute" />.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlIncludeAttribute">
      <summary>Позволяет <see cref="T:System.Xml.Serialization.XmlSerializer" /> распознавать тип в процессе сериализации или десериализации объекта.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIncludeAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" />.</summary>
      <param name="type">
        <see cref="T:System.Type" /> объекта, который необходимо включить. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlIncludeAttribute.Type">
      <summary>Получает или задает тип объекта, который необходимо включить.</summary>
      <returns>
        <see cref="T:System.Type" /> объекта, который необходимо включить.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute">
      <summary>Указывает, что целевое свойство, параметр, возвращаемое значение или член класса содержит префиксы, связанные с пространствами имен, используемыми в документе XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute" />.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlRootAttribute">
      <summary>Управляет XML-сериализацией конечного объекта атрибута как корневого XML-элемента.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlRootAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor(System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> и указывает имя корневого XML-элемента.</summary>
      <param name="elementName">Имя корневого XML-элемента. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.DataType">
      <summary>Возвращает или задает тип данных XSD корневого XML-элемента.</summary>
      <returns>Тип данных XSD (документ схемы XML), как определено документом консорциума W3C (www.w3.org) "XML-схема: Типы данных".</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.ElementName">
      <summary>Возвращает или задает имя XML-элемента, создаваемого и опознаваемого методами <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> и <see cref="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)" /> класса <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <returns>Имя корневого XML-элемента, который создается и распознается в экземпляре XML-документа.По умолчанию — это имя сериализуемого класса.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.IsNullable">
      <summary>Возвращает или задает значение, которое указывает, должен ли <see cref="T:System.Xml.Serialization.XmlSerializer" /> выполнять сериализацию члена со значением null в атрибут xsi:nil со значением true.</summary>
      <returns>true, если <see cref="T:System.Xml.Serialization.XmlSerializer" /> создает атрибут xsi:nil; в противном случае, false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.Namespace">
      <summary>Возвращает или задает пространство имен для корневого XML-элемента.</summary>
      <returns>Пространство имен для XML-элемента.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializer">
      <summary>Сериализует и десериализует объекты в документы XML и из них.<see cref="T:System.Xml.Serialization.XmlSerializer" /> позволяет контролировать способ кодирования объектов в XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializer" />, который может сериализовать объекты заданного типа в документы XML, а также десериализовать документы XML в объекты заданного типа.</summary>
      <param name="type">Тип объекта, который может быть сериализован <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializer" />, который может сериализовать объекты заданного типа в документы XML, а также десериализовать документы XML в объекты заданного типа.Указывает пространство имен по умолчанию для всех элементов XML.</summary>
      <param name="type">Тип объекта, который может быть сериализован <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
      <param name="defaultNamespace">Пространство имен по умолчанию для всех элементов XML. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Type[])">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializer" />, который может сериализовать объекты заданного типа в документы XML, и может десериализовать документы XML в объект заданного типа.Если свойство или поле возвращает массив, параметр <paramref name="extraTypes" /> определяет объекты, которые могут быть вставлены в массив.</summary>
      <param name="type">Тип объекта, который может быть сериализован <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
      <param name="extraTypes">Массив <see cref="T:System.Type" /> дополнительных типов объектов для сериализации. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializer" />, который может сериализовать объекты заданного типа в документы XML, а также десериализовать документы XML в объекты заданного типа.Каждый сериализуемый объект может сам содержать экземпляры классов, которые данная перегрузка позволяет переопределить с другими классами.</summary>
      <param name="type">Тип сериализуемого объекта. </param>
      <param name="overrides">Объект <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializer" />, который может сериализовать объекты типа <see cref="T:System.Object" /> в экземпляры документа XML, а также десериализовать экземпляры документа XML в объекты типа <see cref="T:System.Object" />.Каждый сериализуемый объект может сам содержать экземпляры классов, которые данная перегрузка переопределяет с другими классами.Данная перегрузка также указывает пространство имен по умолчанию для всех элементов XML и класс для использования в качестве корневого элемента XML.</summary>
      <param name="type">Тип объекта, который может быть сериализован <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
      <param name="overrides">
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />, расширяющий или переопределяющий поведение класса, задается в параметре <paramref name="type" />. </param>
      <param name="extraTypes">Массив <see cref="T:System.Type" /> дополнительных типов объектов для сериализации. </param>
      <param name="root">
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" />, указывающий свойство корневого элемента XML. </param>
      <param name="defaultNamespace">Пространство имен по умолчанию всех элементов XML в документе XML. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializer" />, который может сериализовать объекты заданного типа в документы XML, а также десериализовать документ XML в объект заданного типа.Также указывает класс для использования в качестве корневого элемента XML.</summary>
      <param name="type">Тип объекта, который может быть сериализован <see cref="T:System.Xml.Serialization.XmlSerializer" />. </param>
      <param name="root">
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" />, представляющий свойство корневого элемента XML. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CanDeserialize(System.Xml.XmlReader)">
      <summary>Получает значение, указывающее возможность выполнения данным <see cref="T:System.Xml.Serialization.XmlSerializer" /> десериализации документа XML.</summary>
      <returns>true, если <see cref="T:System.Xml.Serialization.XmlSerializer" /> может выполнить десериализацию объекта, на который указывает <see cref="T:System.Xml.XmlReader" />, в противном случае false.</returns>
      <param name="xmlReader">
        <see cref="T:System.Xml.XmlReader" />, указывающий на документ для десериализации. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)">
      <summary>Десериализует документ XML, содержащийся указанным <see cref="T:System.IO.Stream" />.</summary>
      <returns>
        <see cref="T:System.Object" /> десериализуется.</returns>
      <param name="stream">
        <see cref="T:System.IO.Stream" />, содержащий документ XML для десериализации. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.TextReader)">
      <summary>Десериализует документ XML, содержащийся указанным <see cref="T:System.IO.TextReader" />.</summary>
      <returns>
        <see cref="T:System.Object" /> десериализуется.</returns>
      <param name="textReader">
        <see cref="T:System.IO.TextReader" />, содержащий документ XML для десериализации. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при десериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader)">
      <summary>Десериализует документ XML, содержащийся указанным <see cref="T:System.xml.XmlReader" />.</summary>
      <returns>
        <see cref="T:System.Object" /> десериализуется.</returns>
      <param name="xmlReader">
        <see cref="T:System.xml.XmlReader" />, содержащий документ XML для десериализации. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при десериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromTypes(System.Type[])">
      <summary>Возвращает массив объектов <see cref="T:System.Xml.Serialization.XmlSerializer" />, созданный из массива типов.</summary>
      <returns>Массив объектов <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
      <param name="types">Массив объектов <see cref="T:System.Type" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>Сериализует указанный <see cref="T:System.Object" /> и записывает документ XML в файл с помощью заданного <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> используется для записи документа XML. </param>
      <param name="o">
        <see cref="T:System.Object" /> для сериализации. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при сериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Сериализует указанный <see cref="T:System.Object" /> и записывает документ XML в файл с помощью заданного <see cref="T:System.IO.Stream" />, ссылающегося на заданные пространства имен.</summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> используется для записи документа XML. </param>
      <param name="o">
        <see cref="T:System.Object" /> для сериализации. </param>
      <param name="namespaces">
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> со ссылкой объекта. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при сериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)">
      <summary>Сериализует указанный <see cref="T:System.Object" /> и записывает документ XML в файл с помощью заданного <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="textWriter">
        <see cref="T:System.IO.TextWriter" /> используется для записи документа XML. </param>
      <param name="o">
        <see cref="T:System.Object" /> для сериализации. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Сериализует указанный объект <see cref="T:System.Object" /> и записывает документ XML в файл с помощью заданного <see cref="T:System.IO.TextWriter" /> и ссылается на заданные пространства имен.</summary>
      <param name="textWriter">
        <see cref="T:System.IO.TextWriter" /> используется для записи документа XML. </param>
      <param name="o">
        <see cref="T:System.Object" /> для сериализации. </param>
      <param name="namespaces">
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />, содержащий пространства имен для сгенерированного документа XML. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при сериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object)">
      <summary>Сериализует указанный <see cref="T:System.Object" /> и записывает документ XML в файл с помощью заданного <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="xmlWriter">
        <see cref="T:System.xml.XmlWriter" /> используется для записи документа XML. </param>
      <param name="o">
        <see cref="T:System.Object" /> для сериализации. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при сериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Сериализует указанный <see cref="T:System.Object" /> и записывает документ XML в файл с помощью заданного <see cref="T:System.Xml.XmlWriter" />, ссылающегося на заданные пространства имен.</summary>
      <param name="xmlWriter">
        <see cref="T:System.xml.XmlWriter" /> используется для записи документа XML. </param>
      <param name="o">
        <see cref="T:System.Object" /> для сериализации. </param>
      <param name="namespaces">
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> со ссылкой объекта. </param>
      <exception cref="T:System.InvalidOperationException">Возникла ошибка при сериализации.Исходное исключение доступно с помощью свойства <see cref="P:System.Exception.InnerException" />.</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerNamespaces">
      <summary>Содержит пространства имен XML и префиксы, используемые <see cref="T:System.Xml.Serialization.XmlSerializer" /> для генерирования полных имен в экземпляре документа XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Инициализация нового экземпляра  класса <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> с помощью определенного экземпляра XmlSerializerNamespaces, содержащего коллекцию пар префикса и пространства имен.</summary>
      <param name="namespaces">Экземпляр <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />, содержащий пары пространства имен и префикса. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.XmlQualifiedName[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />.</summary>
      <param name="namespaces">Массив объектов <see cref="T:System.Xml.XmlQualifiedName" />. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.Add(System.String,System.String)">
      <summary>Добавляет пару префикса и пространства имен объекту <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />.</summary>
      <param name="prefix">Префикс ассоциирован с пространством имен XML. </param>
      <param name="ns">Пространство имен XML. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerNamespaces.Count">
      <summary>Получает число пар префикса и пространства имен в коллекции.</summary>
      <returns>Число пар префикса и пространства имен в коллекции.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.ToArray">
      <summary>Получает массив пар префикса и пространства имен в объекте <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />.</summary>
      <returns>Массив объектов <see cref="T:System.Xml.XmlQualifiedName" />, используемых в качестве квалифицированных имен в документе XML.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTextAttribute">
      <summary>Указывает на <see cref="T:System.Xml.Serialization.XmlSerializer" />, что член должен обрабатываться как текст XML, когда содержащий его класс сериализуется или десериализуется.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlTextAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor(System.Type)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlTextAttribute" />.</summary>
      <param name="type">
        <see cref="T:System.Type" /> сериализуемого члена. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.DataType">
      <summary>Получает или задает тип данных языка определения схем XML (XSD) текста, сгенерированного <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <returns>Тип данных схемы XML (XSD) согласно документу "Схема XML, часть 2: типы данных" консорциума World Wide Web (www.w3.org).</returns>
      <exception cref="T:System.Exception">Указанный тип данных XML-схемы не может иметь соответствия в типах данных .NET. </exception>
      <exception cref="T:System.InvalidOperationException">Указанный тип данных схемы XML неверен для свойства и не может быть преобразован в тип члена. </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.Type">
      <summary>Получает или задает тип члена.</summary>
      <returns>
        <see cref="T:System.Type" /> члена.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeAttribute">
      <summary>Управляет схемой XML, сгенерированной при сериализации <see cref="T:System.Xml.Serialization.XmlSerializer" /> цели атрибута.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> и задает имя типа XML.</summary>
      <param name="typeName">Имя типа XML, генерируемое <see cref="T:System.Xml.Serialization.XmlSerializer" /> при сериализации экземпляра класса (и определении при десериализации экземпляра класса). </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.AnonymousType">
      <summary>Получает или задает значение, определяющее, является ли результирующий тип схемы анонимным типом XSD.</summary>
      <returns>true, если результирующий тип схемы является анонимным типом XSD, в противном случае false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.IncludeInSchema">
      <summary>Получает или задает значение, указывающее, включается ли тип в документы схемы XML.</summary>
      <returns>true для включения типа в документ схемы XML, в противном случае false.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.Namespace">
      <summary>Получает или задает пространство имен типа XML.</summary>
      <returns>Пространство имен типа XML.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.TypeName">
      <summary>Получает или задает имя типа XML.</summary>
      <returns>Имя типа XML.</returns>
    </member>
  </members>
</doc>