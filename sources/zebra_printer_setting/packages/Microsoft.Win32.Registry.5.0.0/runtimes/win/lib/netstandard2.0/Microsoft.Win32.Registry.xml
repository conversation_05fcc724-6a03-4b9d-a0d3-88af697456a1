<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Win32.Registry</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.Registry">
      <summary>Provides <see cref="T:Microsoft.Win32.RegistryKey" /> objects that represent the root keys in the Windows registry, and <see langword="static" /> methods to access key/value pairs.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.ClassesRoot">
      <summary>Defines the types (or classes) of documents and the properties associated with those types. This field reads the Windows registry base key HKEY_CLASSES_ROOT.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.CurrentConfig">
      <summary>Contains configuration information pertaining to the hardware that is not specific to the user. This field reads the Windows registry base key HKEY_CURRENT_CONFIG.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.CurrentUser">
      <summary>Contains information about the current user preferences. This field reads the Windows registry base key HKEY_CURRENT_USER.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.LocalMachine">
      <summary>Contains the configuration data for the local machine. This field reads the Windows registry base key HKEY_LOCAL_MACHINE.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.PerformanceData">
      <summary>Contains performance information for software components. This field reads the Windows registry base key HKEY_PERFORMANCE_DATA.</summary>
    </member>
    <member name="F:Microsoft.Win32.Registry.Users">
      <summary>Contains information about the default user configuration. This field reads the Windows registry base key HKEY_USERS.</summary>
    </member>
    <member name="M:Microsoft.Win32.Registry.GetValue(System.String,System.String,System.Object)">
      <summary>Retrieves the value associated with the specified name, in the specified registry key. If the name is not found in the specified key, returns a default value that you provide, or <see langword="null" /> if the specified key does not exist.</summary>
      <param name="keyName">The full registry path of the key, beginning with a valid registry root, such as "HKEY_CURRENT_USER".</param>
      <param name="valueName">The name of the name/value pair.</param>
      <param name="defaultValue">The value to return if <paramref name="valueName" /> does not exist.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.</exception>
      <returns>
        <see langword="null" /> if the subkey specified by <paramref name="keyName" /> does not exist; otherwise, the value associated with <paramref name="valueName" />, or <paramref name="defaultValue" /> if <paramref name="valueName" /> is not found.</returns>
    </member>
    <member name="M:Microsoft.Win32.Registry.SetValue(System.String,System.String,System.Object)">
      <summary>Sets the specified name/value pair on the specified registry key. If the specified key does not exist, it is created.</summary>
      <param name="keyName">The full registry path of the key, beginning with a valid registry root, such as "HKEY_CURRENT_USER".</param>
      <param name="valueName">The name of the name/value pair.</param>
      <param name="value">The value to be stored.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.  
  
 -or-  
  
 <paramref name="keyName" /> is longer than the maximum length allowed (255 characters).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and thus cannot be written to; for example, it is a root-level node.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys.</exception>
    </member>
    <member name="M:Microsoft.Win32.Registry.SetValue(System.String,System.String,System.Object,Microsoft.Win32.RegistryValueKind)">
      <summary>Sets the name/value pair on the specified registry key, using the specified registry data type. If the specified key does not exist, it is created.</summary>
      <param name="keyName">The full registry path of the key, beginning with a valid registry root, such as "HKEY_CURRENT_USER".</param>
      <param name="valueName">The name of the name/value pair.</param>
      <param name="value">The value to be stored.</param>
      <param name="valueKind">The registry data type to use when storing the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.  
  
 -or-  
  
 <paramref name="keyName" /> is longer than the maximum length allowed (255 characters).  
  
 -or-  
  
 The type of <paramref name="value" /> did not match the registry data type specified by <paramref name="valueKind" />, therefore the data could not be converted properly.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and thus cannot be written to; for example, it is a root-level node, or the key has not been opened with write access.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys.</exception>
    </member>
    <member name="T:Microsoft.Win32.RegistryHive">
      <summary>Represents the possible values for a top-level node on a foreign machine.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.ClassesRoot">
      <summary>Represents the HKEY_CLASSES_ROOT base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.CurrentConfig">
      <summary>Represents the HKEY_CURRENT_CONFIG base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.CurrentUser">
      <summary>Represents the HKEY_CURRENT_USER base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.LocalMachine">
      <summary>Represents the HKEY_LOCAL_MACHINE base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.PerformanceData">
      <summary>Represents the HKEY_PERFORMANCE_DATA base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryHive.Users">
      <summary>Represents the HKEY_USERS base key on another computer. This value can be passed to the <see cref="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)" /> method, to open this node remotely.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryKey">
      <summary>Represents a key-level node in the Windows registry. This class is a registry encapsulation.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.Close">
      <summary>Closes the key and flushes it to disk if its contents have been modified.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String)">
      <summary>Creates a new subkey or opens an existing subkey for write access.</summary>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> on which this method is being invoked is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key , or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,Microsoft.Win32.RegistryKeyPermissionCheck)">
      <summary>Creates a new subkey or opens an existing subkey for write access, using the specified permission check option.</summary>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <param name="permissionCheck">One of the enumeration values that specifies whether the key is opened for read or read/write access.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="permissionCheck" /> contains an invalid value.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> on which this method is being invoked is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key, or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,Microsoft.Win32.RegistryKeyPermissionCheck,Microsoft.Win32.RegistryOptions)">
      <summary>Creates a subkey or opens a subkey for write access, using the specified permission check and registry options.</summary>
      <param name="subkey">The name or path of the subkey to create or open.</param>
      <param name="permissionCheck">One of the enumeration values that specifies whether the key is opened for read or read/write access.</param>
      <param name="registryOptions">The registry option to use; for example, that creates a volatile key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> object is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> object cannot be written to; for example, it was not opened as a writable key, or the user does not have the required access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,Microsoft.Win32.RegistryKeyPermissionCheck,Microsoft.Win32.RegistryOptions,System.Security.AccessControl.RegistrySecurity)">
      <summary>Creates a subkey or opens a subkey for write access, using the specified permission check option, registry option, and registry security.</summary>
      <param name="subkey">The name or path of the subkey to create or open.</param>
      <param name="permissionCheck">One of the enumeration values that specifies whether the key is opened for read or read/write access.</param>
      <param name="registryOptions">The registry option to use.</param>
      <param name="registrySecurity">The access control security for the new subkey.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> object is closed. Closed keys cannot be accessed.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> object cannot be written to; for example, it was not opened as a writable key, or the user does not have the required access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,Microsoft.Win32.RegistryKeyPermissionCheck,System.Security.AccessControl.RegistrySecurity)">
      <summary>Creates a new subkey or opens an existing subkey for write access, using the specified permission check option and registry security.</summary>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <param name="permissionCheck">One of the enumeration values that specifies whether the key is opened for read or read/write access.</param>
      <param name="registrySecurity">The access control security for the new key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="permissionCheck" /> contains an invalid value.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> on which this method is being invoked is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key, or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,System.Boolean)">
      <summary>Creates a new subkey or opens an existing subkey with the specified access. Available starting with .NET Framework 4.6.</summary>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <param name="writable">
        <see langword="true" /> to indicate the new subkey is writable; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key, or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.CreateSubKey(System.String,System.Boolean,Microsoft.Win32.RegistryOptions)">
      <summary>Creates a new subkey or opens an existing subkey with the specified access. Available starting with .NET Framework 4.6.</summary>
      <param name="subkey">The name or path of the subkey to create or open. This string is not case-sensitive.</param>
      <param name="writable">
        <see langword="true" /> to indicate the new subkey is writable; otherwise, <see langword="false" />.</param>
      <param name="options">The registry option to use.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> does not specify a valid Option</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or open the registry key.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> cannot be written to; for example, it was not opened as a writable key, or the user does not have the necessary access rights.</exception>
      <exception cref="T:System.IO.IOException">The nesting level exceeds 510.  
  
 -or-  
  
 A system error occurred, such as deletion of the key, or an attempt to create a key in the <see cref="F:Microsoft.Win32.Registry.LocalMachine" /> root.</exception>
      <returns>The newly created subkey, or <see langword="null" /> if the operation failed. If a zero-length string is specified for <paramref name="subkey" />, the current <see cref="T:Microsoft.Win32.RegistryKey" /> object is returned.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKey(System.String)">
      <summary>Deletes the specified subkey.</summary>
      <param name="subkey">The name of the subkey to delete. This string is not case-sensitive.</param>
      <exception cref="T:System.InvalidOperationException">The <paramref name="subkey" /> has child subkeys</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="subkey" /> parameter does not specify a valid registry key</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" /></exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKey(System.String,System.Boolean)">
      <summary>Deletes the specified subkey, and specifies whether an exception is raised if the subkey is not found.</summary>
      <param name="subkey">The name of the subkey to delete. This string is not case-sensitive.</param>
      <param name="throwOnMissingSubKey">Indicates whether an exception should be raised if the specified subkey cannot be found. If this argument is <see langword="true" /> and the specified subkey does not exist, an exception is raised. If this argument is <see langword="false" /> and the specified subkey does not exist, no action is taken.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="subkey" /> has child subkeys.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="subkey" /> does not specify a valid registry key, and <paramref name="throwOnMissingSubKey" /> is <see langword="true" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKeyTree(System.String)">
      <summary>Deletes a subkey and any child subkeys recursively.</summary>
      <param name="subkey">The subkey to delete. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">Deletion of a root hive is attempted.  
  
 -or-  
  
 <paramref name="subkey" /> does not specify a valid registry subkey.</exception>
      <exception cref="T:System.IO.IOException">An I/O error has occurred.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteSubKeyTree(System.String,System.Boolean)">
      <summary>Deletes the specified subkey and any child subkeys recursively, and specifies whether an exception is raised if the subkey is not found.</summary>
      <param name="subkey">The name of the subkey to delete. This string is not case-sensitive.</param>
      <param name="throwOnMissingSubKey">Indicates whether an exception should be raised if the specified subkey cannot be found. If this argument is <see langword="true" /> and the specified subkey does not exist, an exception is raised. If this argument is <see langword="false" /> and the specified subkey does not exist, no action is taken.</param>
      <exception cref="T:System.ArgumentException">An attempt was made to delete the root hive of the tree.  
  
 -or-  
  
 <paramref name="subkey" /> does not specify a valid registry subkey, and <paramref name="throwOnMissingSubKey" /> is <see langword="true" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subkey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the key.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteValue(System.String)">
      <summary>Deletes the specified value from this key.</summary>
      <param name="name">The name of the value to delete.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid reference to a value.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the value.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is read-only.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.DeleteValue(System.String,System.Boolean)">
      <summary>Deletes the specified value from this key, and specifies whether an exception is raised if the value is not found.</summary>
      <param name="name">The name of the value to delete.</param>
      <param name="throwOnMissingValue">Indicates whether an exception should be raised if the specified value cannot be found. If this argument is <see langword="true" /> and the specified value does not exist, an exception is raised. If this argument is <see langword="false" /> and the specified value does not exist, no action is taken.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is not a valid reference to a value and <paramref name="throwOnMissingValue" /> is <see langword="true" />.  
  
 -or-  
  
 <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to delete the value.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is read-only.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.Win32.RegistryKey" /> class.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.Flush">
      <summary>Writes all the attributes of the specified open registry key into the registry.</summary>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.FromHandle(Microsoft.Win32.SafeHandles.SafeRegistryHandle)">
      <summary>Creates a registry key from a specified handle.</summary>
      <param name="handle">The handle to the registry key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="handle" /> is <see langword="null" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to perform this action.</exception>
      <returns>A registry key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.FromHandle(Microsoft.Win32.SafeHandles.SafeRegistryHandle,Microsoft.Win32.RegistryView)">
      <summary>Creates a registry key from a specified handle and registry view setting.</summary>
      <param name="handle">The handle to the registry key.</param>
      <param name="view">The registry view to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="view" /> is invalid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="handle" /> is <see langword="null" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to perform this action.</exception>
      <returns>A registry key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetAccessControl">
      <summary>Returns the access control security for the current registry key.</summary>
      <exception cref="T:System.Security.SecurityException">The user does not have the necessary permissions.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.InvalidOperationException">The current key has been deleted.</exception>
      <returns>An object that describes the access control permissions on the registry key represented by the current <see cref="T:Microsoft.Win32.RegistryKey" />.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetAccessControl(System.Security.AccessControl.AccessControlSections)">
      <summary>Returns the specified sections of the access control security for the current registry key.</summary>
      <param name="includeSections">A bitwise combination of enumeration values that specifies the type of security information to get.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the necessary permissions.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.InvalidOperationException">The current key has been deleted.</exception>
      <returns>An object that describes the access control permissions on the registry key represented by the current <see cref="T:Microsoft.Win32.RegistryKey" />.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetSubKeyNames">
      <summary>Retrieves an array of strings that contains all the subkey names.</summary>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, for example the current key has been deleted.</exception>
      <returns>An array of strings that contains the names of the subkeys for the current key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValue(System.String)">
      <summary>Retrieves the value associated with the specified name. Returns <see langword="null" /> if the name/value pair does not exist in the registry.</summary>
      <param name="name">The name of the value to retrieve. This string is not case-sensitive.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <returns>The value associated with <paramref name="name" />, or <see langword="null" /> if <paramref name="name" /> is not found.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValue(System.String,System.Object)">
      <summary>Retrieves the value associated with the specified name. If the name is not found, returns the default value that you provide.</summary>
      <param name="name">The name of the value to retrieve. This string is not case-sensitive.</param>
      <param name="defaultValue">The value to return if <paramref name="name" /> does not exist.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <returns>The value associated with <paramref name="name" />, with any embedded environment variables left unexpanded, or <paramref name="defaultValue" /> if <paramref name="name" /> is not found.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValue(System.String,System.Object,Microsoft.Win32.RegistryValueOptions)">
      <summary>Retrieves the value associated with the specified name and retrieval options. If the name is not found, returns the default value that you provide.</summary>
      <param name="name">The name of the value to retrieve. This string is not case-sensitive.</param>
      <param name="defaultValue">The value to return if <paramref name="name" /> does not exist.</param>
      <param name="options">One of the enumeration values that specifies optional processing of the retrieved value.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="options" /> is not a valid <see cref="T:Microsoft.Win32.RegistryValueOptions" /> value; for example, an invalid value is cast to <see cref="T:Microsoft.Win32.RegistryValueOptions" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <returns>The value associated with <paramref name="name" />, processed according to the specified <paramref name="options" />, or <paramref name="defaultValue" /> if <paramref name="name" /> is not found.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValueKind(System.String)">
      <summary>Retrieves the registry data type of the value associated with the specified name.</summary>
      <param name="name">The name of the value whose registry data type is to be retrieved. This string is not case-sensitive.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.IO.IOException">The subkey that contains the specified value does not exist.  
  
 -or-  
  
 The name/value pair specified by <paramref name="name" /> does not exist.  
  
 This exception is not thrown on Windows 95, Windows 98, or Windows Millennium Edition.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <returns>The registry data type of the value associated with <paramref name="name" />.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.GetValueNames">
      <summary>Retrieves an array of strings that contains all the value names associated with this key.</summary>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred; for example, the current key has been deleted.</exception>
      <returns>An array of strings that contains the value names for the current key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenBaseKey(Microsoft.Win32.RegistryHive,Microsoft.Win32.RegistryView)">
      <summary>Opens a new <see cref="T:Microsoft.Win32.RegistryKey" /> that represents the requested key on the local machine with the specified view.</summary>
      <param name="hKey">The HKEY to open.</param>
      <param name="view">The registry view to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hKey" /> or <paramref name="view" /> is invalid.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to perform this action.</exception>
      <returns>The requested registry key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String)">
      <summary>Opens a new <see cref="T:Microsoft.Win32.RegistryKey" /> that represents the requested key on a remote machine.</summary>
      <param name="hKey">The HKEY to open, from the <see cref="T:Microsoft.Win32.RegistryHive" /> enumeration.</param>
      <param name="machineName">The remote machine.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hKey" /> is invalid.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="machineName" /> is not found.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="machineName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the proper permissions to perform this operation.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <returns>The requested registry key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenRemoteBaseKey(Microsoft.Win32.RegistryHive,System.String,Microsoft.Win32.RegistryView)">
      <summary>Opens a new registry key that represents the requested key on a remote machine with the specified view.</summary>
      <param name="hKey">The HKEY to open from the <see cref="T:Microsoft.Win32.RegistryHive" /> enumeration.</param>
      <param name="machineName">The remote machine.</param>
      <param name="view">The registry view to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hKey" /> or <paramref name="view" /> is invalid.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="machineName" /> is not found.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="machineName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the required permissions to perform this operation.</exception>
      <returns>The requested registry key.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String)">
      <summary>Retrieves a subkey as read-only.</summary>
      <param name="name">The name or path of the subkey to open as read-only.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" /></exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read the registry key.</exception>
      <returns>The subkey requested, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String,Microsoft.Win32.RegistryKeyPermissionCheck)">
      <summary>Retrieves the specified subkey for read or read/write access.</summary>
      <param name="name">The name or path of the subkey to create or open.</param>
      <param name="permissionCheck">One of the enumeration values that specifies whether the key is opened for read or read/write access.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" /></exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="permissionCheck" /> contains an invalid value.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read the registry key.</exception>
      <returns>The subkey requested, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String,Microsoft.Win32.RegistryKeyPermissionCheck,System.Security.AccessControl.RegistryRights)">
      <summary>Retrieves the specified subkey for read or read/write access, requesting the specified access rights.</summary>
      <param name="name">The name or path of the subkey to create or open.</param>
      <param name="permissionCheck">One of the enumeration values that specifies whether the key is opened for read or read/write access.</param>
      <param name="rights">A bitwise combination of enumeration values that specifies the desired security access.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" /></exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="permissionCheck" /> contains an invalid value.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.Security.SecurityException">
        <paramref name="rights" /> includes invalid registry rights values.  
  
 -or-  
  
 The user does not have the requested permissions.</exception>
      <returns>The subkey requested, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String,System.Boolean)">
      <summary>Retrieves a specified subkey, and specifies whether write access is to be applied to the key.</summary>
      <param name="name">Name or path of the subkey to open.</param>
      <param name="writable">Set to <see langword="true" /> if you need write access to the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to access the registry key in the specified mode.</exception>
      <returns>The subkey requested, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.OpenSubKey(System.String,System.Security.AccessControl.RegistryRights)">
      <summary>Retrieves a subkey with the specified name and access rights. Available starting with .NET Framework 4.6.</summary>
      <param name="name">The name or path of the subkey to create or open.</param>
      <param name="rights">The rights for the registry key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to access the registry key in the specified mode.</exception>
      <returns>The subkey requested, or <see langword="null" /> if the operation failed.</returns>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.SetAccessControl(System.Security.AccessControl.RegistrySecurity)">
      <summary>Applies Windows access control security to an existing registry key.</summary>
      <param name="registrySecurity">The access control security to apply to the current subkey.</param>
      <exception cref="T:System.UnauthorizedAccessException">The current <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a key with access control security, and the caller does not have <see cref="F:System.Security.AccessControl.RegistryRights.ChangePermissions" /> rights.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="registrySecurity" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.SetValue(System.String,System.Object)">
      <summary>Sets the specified name/value pair.</summary>
      <param name="name">The name of the value to store.</param>
      <param name="value">The data to be stored.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is an unsupported data type.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and cannot be written to; for example, the key has not been opened with write access.  
  
 -or-  
  
 The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows Millennium Edition or Windows 98.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys.</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows 2000, Windows XP, or Windows Server 2003.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.SetValue(System.String,System.Object,Microsoft.Win32.RegistryValueKind)">
      <summary>Sets the value of a name/value pair in the registry key, using the specified registry data type.</summary>
      <param name="name">The name of the value to be stored.</param>
      <param name="value">The data to be stored.</param>
      <param name="valueKind">The registry data type to use when storing the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The type of <paramref name="value" /> did not match the registry data type specified by <paramref name="valueKind" />, therefore the data could not be converted properly.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and cannot be written to; for example, the key has not been opened with write access.  
  
 -or-  
  
 The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows Millennium Edition or Windows 98.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys.</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> object represents a root-level node, and the operating system is Windows 2000, Windows XP, or Windows Server 2003.</exception>
    </member>
    <member name="M:Microsoft.Win32.RegistryKey.ToString">
      <summary>Retrieves a string representation of this key.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being accessed is closed (closed keys cannot be accessed).</exception>
      <returns>A string representing the key. If the specified key is invalid (cannot be found) then <see langword="null" /> is returned.</returns>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.Handle">
      <summary>Gets a <see cref="T:Microsoft.Win32.SafeHandles.SafeRegistryHandle" /> object that represents the registry key that the current <see cref="T:Microsoft.Win32.RegistryKey" /> object encapsulates.</summary>
      <exception cref="T:System.ObjectDisposedException">The registry key is closed. Closed keys cannot be accessed.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, such as deletion of the current key.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read the key.</exception>
      <returns>The handle to the registry key.</returns>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.Name">
      <summary>Retrieves the name of the key.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is closed (closed keys cannot be accessed).</exception>
      <returns>The absolute (qualified) name of the key.</returns>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.SubKeyCount">
      <summary>Retrieves the count of subkeys of the current key.</summary>
      <exception cref="T:System.Security.SecurityException">The user does not have read permission for the key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, for example the current key has been deleted.</exception>
      <returns>The number of subkeys of the current key.</returns>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.ValueCount">
      <summary>Retrieves the count of values in the key.</summary>
      <exception cref="T:System.Security.SecurityException">The user does not have read permission for the key.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:Microsoft.Win32.RegistryKey" /> being manipulated is closed (closed keys cannot be accessed).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have the necessary registry rights.</exception>
      <exception cref="T:System.IO.IOException">A system error occurred, for example the current key has been deleted.</exception>
      <returns>The number of name/value pairs in the key.</returns>
    </member>
    <member name="P:Microsoft.Win32.RegistryKey.View">
      <summary>Gets the view that was used to create the registry key.</summary>
      <returns>The view that was used to create the registry key.  
  
 -or-  
  
 <see cref="F:Microsoft.Win32.RegistryView.Default" />, if no view was used.</returns>
    </member>
    <member name="T:Microsoft.Win32.RegistryKeyPermissionCheck">
      <summary>Specifies whether security checks are performed when opening registry keys and accessing their name/value pairs.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryKeyPermissionCheck.Default">
      <summary>The registry key inherits the mode of its parent. Security checks are performed when trying to access subkeys or values, unless the parent was opened with <see cref="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadSubTree" /> or <see cref="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadWriteSubTree" /> mode.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadSubTree">
      <summary>Security checks are not performed when accessing subkeys or values. A security check is performed when trying to open the current key, unless the parent was opened with <see cref="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadSubTree" /> or <see cref="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadWriteSubTree" />.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadWriteSubTree">
      <summary>Security checks are not performed when accessing subkeys or values. A security check is performed when trying to open the current key, unless the parent was opened with <see cref="F:Microsoft.Win32.RegistryKeyPermissionCheck.ReadWriteSubTree" />.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryOptions">
      <summary>Specifies options to use when creating a registry key.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryOptions.None">
      <summary>A non-volatile key. This is the default.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryOptions.Volatile">
      <summary>A volatile key. The information is stored in memory and is not preserved when the corresponding registry hive is unloaded.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryValueKind">
      <summary>Specifies the data types to use when storing values in the registry, or identifies the data type of a value in the registry.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.Binary">
      <summary>Binary data in any form. This value is equivalent to the Windows API registry data type REG_BINARY.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.DWord">
      <summary>A 32-bit binary number. This value is equivalent to the Windows API registry data type REG_DWORD.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.ExpandString">
      <summary>A null-terminated string that contains unexpanded references to environment variables, such as %PATH%, that are expanded when the value is retrieved. This value is equivalent to the Windows API registry data type REG_EXPAND_SZ.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.MultiString">
      <summary>An array of null-terminated strings, terminated by two null characters. This value is equivalent to the Windows API registry data type REG_MULTI_SZ.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.None">
      <summary>No data type.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.QWord">
      <summary>A 64-bit binary number. This value is equivalent to the Windows API registry data type REG_QWORD.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.String">
      <summary>A null-terminated string. This value is equivalent to the Windows API registry data type REG_SZ.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueKind.Unknown">
      <summary>An unsupported registry data type. For example, the Microsoft Windows API registry data type REG_RESOURCE_LIST is unsupported. Use this value to specify that the <see cref="M:Microsoft.Win32.RegistryKey.SetValue(System.String,System.Object)" /> method should determine the appropriate registry data type when storing a name/value pair.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryValueOptions">
      <summary>Specifies optional behavior when retrieving name/value pairs from a registry key.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueOptions.DoNotExpandEnvironmentNames">
      <summary>A value of type <see cref="F:Microsoft.Win32.RegistryValueKind.ExpandString" /> is retrieved without expanding its embedded environment variables.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryValueOptions.None">
      <summary>No optional behavior is specified.</summary>
    </member>
    <member name="T:Microsoft.Win32.RegistryView">
      <summary>Specifies which registry view to target on a 64-bit operating system.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryView.Default">
      <summary>The default view.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryView.Registry32">
      <summary>The 32-bit view.</summary>
    </member>
    <member name="F:Microsoft.Win32.RegistryView.Registry64">
      <summary>The 64-bit view.</summary>
    </member>
    <member name="T:Microsoft.Win32.SafeHandles.SafeRegistryHandle">
      <summary>Represents a safe handle to the Windows registry.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeRegistryHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Win32.SafeHandles.SafeRegistryHandle" /> class.</summary>
      <param name="preexistingHandle">An object that represents the pre-existing handle to use.</param>
      <param name="ownsHandle">
        <see langword="true" /> to reliably release the handle during the finalization phase; <see langword="false" /> to prevent reliable release.</param>
    </member>
    <member name="T:System.Security.AccessControl.RegistryAccessRule">
      <summary>Represents a set of access rights allowed or denied for a user or group. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RegistryAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.RegistryRights,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> class, specifying the user or group the rule applies to, the access rights, and whether the specified access rights are allowed or denied.</summary>
      <param name="identity">The user or group the rule applies to. Must be of type <see cref="T:System.Security.Principal.SecurityIdentifier" /> or a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="registryRights">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values indicating the rights allowed or denied.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values indicating whether the rights are allowed or denied.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="registryRights" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="type" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identity" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="eventRights" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" /> nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistryAccessRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.RegistryRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> class, specifying the user or group the rule applies to, the access rights, the inheritance flags, the propagation flags, and whether the specified access rights are allowed or denied.</summary>
      <param name="identity">The user or group the rule applies to. Must be of type <see cref="T:System.Security.Principal.SecurityIdentifier" /> or a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="registryRights">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values specifying the rights allowed or denied.</param>
      <param name="inheritanceFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.InheritanceFlags" /> flags specifying how access rights are inherited from other objects.</param>
      <param name="propagationFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.PropagationFlags" /> flags specifying how access rights are propagated to other objects.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values specifying whether the rights are allowed or denied.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="registryRights" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="type" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="inheritanceFlags" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="propagationFlags" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identity" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="registryRights" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" />, nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistryAccessRule.#ctor(System.String,System.Security.AccessControl.RegistryRights,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> class, specifying the name of the user or group the rule applies to, the access rights, and whether the specified access rights are allowed or denied.</summary>
      <param name="identity">The name of the user or group the rule applies to.</param>
      <param name="registryRights">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values indicating the rights allowed or denied.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values indicating whether the rights are allowed or denied.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="registryRights" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="type" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="registryRights" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="identity" /> is a zero-length string.  
  
 -or-  
  
 <paramref name="identity" /> is longer than 512 characters.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistryAccessRule.#ctor(System.String,System.Security.AccessControl.RegistryRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> class, specifying the name of the user or group the rule applies to, the access rights, the inheritance flags, the propagation flags, and whether the specified access rights are allowed or denied.</summary>
      <param name="identity">The name of the user or group the rule applies to.</param>
      <param name="registryRights">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values indicating the rights allowed or denied.</param>
      <param name="inheritanceFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.InheritanceFlags" /> flags specifying how access rights are inherited from other objects.</param>
      <param name="propagationFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.PropagationFlags" /> flags specifying how access rights are propagated to other objects.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values specifying whether the rights are allowed or denied.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="registryRights" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="type" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="inheritanceFlags" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="propagationFlags" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventRights" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="identity" /> is a zero-length string.  
  
 -or-  
  
 <paramref name="identity" /> is longer than 512 characters.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RegistryAccessRule.RegistryRights">
      <summary>Gets the rights allowed or denied by the access rule.</summary>
      <returns>A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values indicating the rights allowed or denied by the access rule.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RegistryAuditRule">
      <summary>Represents a set of access rights to be audited for a user or group. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RegistryAuditRule.#ctor(System.Security.Principal.IdentityReference,System.Security.AccessControl.RegistryRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> class, specifying the user or group to audit, the rights to audit, whether to take inheritance into account, and whether to audit success, failure, or both.</summary>
      <param name="identity">The user or group the rule applies to. Must be of type <see cref="T:System.Security.Principal.SecurityIdentifier" /> or a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
      <param name="registryRights">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values specifying the kinds of access to audit.</param>
      <param name="inheritanceFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values specifying whether the audit rule applies to subkeys of the current key.</param>
      <param name="propagationFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that affect the way an inherited audit rule is propagated to subkeys of the current key.</param>
      <param name="flags">A bitwise combination of <see cref="T:System.Security.AccessControl.AuditFlags" /> values specifying whether to audit success, failure, or both.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="eventRights" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="flags" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="inheritanceFlags" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="propagationFlags" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identity" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="registryRights" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" /> nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistryAuditRule.#ctor(System.String,System.Security.AccessControl.RegistryRights,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> class, specifying the name of the user or group to audit, the rights to audit, whether to take inheritance into account, and whether to audit success, failure, or both.</summary>
      <param name="identity">The name of the user or group the rule applies to.</param>
      <param name="registryRights">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values specifying the kinds of access to audit.</param>
      <param name="inheritanceFlags">A combination of <see cref="T:System.Security.AccessControl.InheritanceFlags" /> flags that specifies whether the audit rule applies to subkeys of the current key.</param>
      <param name="propagationFlags">A combination of <see cref="T:System.Security.AccessControl.PropagationFlags" /> flags that affect the way an inherited audit rule is propagated to subkeys of the current key.</param>
      <param name="flags">A bitwise combination of <see cref="T:System.Security.AccessControl.AuditFlags" /> values specifying whether to audit success, failure, or both.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="eventRights" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="flags" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="inheritanceFlags" /> specifies an invalid value.  
  
 -or-  
  
 <paramref name="propagationFlags" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="registryRights" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identity" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="identity" /> is a zero-length string.  
  
 -or-  
  
 <paramref name="identity" /> is longer than 512 characters.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RegistryAuditRule.RegistryRights">
      <summary>Gets the access rights affected by the audit rule.</summary>
      <returns>A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values that indicates the rights affected by the audit rule.</returns>
    </member>
    <member name="T:System.Security.AccessControl.RegistryRights">
      <summary>Specifies the access control rights that can be applied to registry objects.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ChangePermissions">
      <summary>The right to change the access rules and audit rules associated with a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.CreateLink">
      <summary>Reserved for system use.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.CreateSubKey">
      <summary>The right to create subkeys of a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.Delete">
      <summary>The right to delete a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.EnumerateSubKeys">
      <summary>The right to list the subkeys of a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ExecuteKey">
      <summary>Same as <see cref="F:System.Security.AccessControl.RegistryRights.ReadKey" />.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.FullControl">
      <summary>The right to exert full control over a registry key, and to modify its access rules and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.Notify">
      <summary>The right to request notification of changes on a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.QueryValues">
      <summary>The right to query the name/value pairs in a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ReadKey">
      <summary>The right to query the name/value pairs in a registry key, to request notification of changes, to enumerate its subkeys, and to read its access rules and audit rules.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.ReadPermissions">
      <summary>The right to open and copy the access rules and audit rules for a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.SetValue">
      <summary>The right to create, delete, or set name/value pairs in a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.TakeOwnership">
      <summary>The right to change the owner of a registry key.</summary>
    </member>
    <member name="F:System.Security.AccessControl.RegistryRights.WriteKey">
      <summary>The right to create, delete, and set the name/value pairs in a registry key, to create or delete subkeys, to request notification of changes, to enumerate its subkeys, and to read its access rules and audit rules.</summary>
    </member>
    <member name="T:System.Security.AccessControl.RegistrySecurity">
      <summary>Represents the Windows access control security for a registry key. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.RegistrySecurity" /> class with default values.</summary>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Creates a new access control rule for the specified user, with the specified access rights, access control, and flags.</summary>
      <param name="identityReference">An <see cref="T:System.Security.Principal.IdentityReference" /> that identifies the user or group the rule applies to.</param>
      <param name="accessMask">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values specifying the access rights to allow or deny, cast to an integer.</param>
      <param name="isInherited">A Boolean value specifying whether the rule is inherited.</param>
      <param name="inheritanceFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values specifying how the rule is inherited by subkeys.</param>
      <param name="propagationFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that modify the way the rule is inherited by subkeys. Meaningless if the value of <paramref name="inheritanceFlags" /> is <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType" /> values specifying whether the rights are allowed or denied.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" />, <paramref name="inheritanceFlags" />, <paramref name="propagationFlags" />, or <paramref name="type" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identityReference" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="accessMask" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identityReference" /> is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" />, nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <returns>A <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> object representing the specified rights for the specified user.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.AddAccessRule(System.Security.AccessControl.RegistryAccessRule)">
      <summary>Searches for a matching access control with which the new rule can be merged. If none are found, adds the new rule.</summary>
      <param name="rule">The access control rule to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.AddAuditRule(System.Security.AccessControl.RegistryAuditRule)">
      <summary>Searches for an audit rule with which the new rule can be merged. If none are found, adds the new rule.</summary>
      <param name="rule">The audit rule to add. The user specified by this rule determines the search.</param>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Creates a new audit rule, specifying the user the rule applies to, the access rights to audit, the inheritance and propagation of the rule, and the outcome that triggers the rule.</summary>
      <param name="identityReference">An <see cref="T:System.Security.Principal.IdentityReference" /> that identifies the user or group the rule applies to.</param>
      <param name="accessMask">A bitwise combination of <see cref="T:System.Security.AccessControl.RegistryRights" /> values specifying the access rights to audit, cast to an integer.</param>
      <param name="isInherited">A Boolean value specifying whether the rule is inherited.</param>
      <param name="inheritanceFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.InheritanceFlags" /> values specifying how the rule is inherited by subkeys.</param>
      <param name="propagationFlags">A bitwise combination of <see cref="T:System.Security.AccessControl.PropagationFlags" /> values that modify the way the rule is inherited by subkeys. Meaningless if the value of <paramref name="inheritanceFlags" /> is <see cref="F:System.Security.AccessControl.InheritanceFlags.None" />.</param>
      <param name="flags">A bitwise combination of <see cref="T:System.Security.AccessControl.AuditFlags" /> values specifying whether to audit successful access, failed access, or both.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="accessMask" />, <paramref name="inheritanceFlags" />, <paramref name="propagationFlags" />, or <paramref name="flags" /> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identityReference" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="accessMask" /> is zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="identityReference" /> is neither of type <see cref="T:System.Security.Principal.SecurityIdentifier" />, nor of a type such as <see cref="T:System.Security.Principal.NTAccount" /> that can be converted to type <see cref="T:System.Security.Principal.SecurityIdentifier" />.</exception>
      <returns>A <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> object representing the specified audit rule for the specified user, with the specified flags. The return type of the method is the base class, <see cref="T:System.Security.AccessControl.AuditRule" />, but the return value can be cast safely to the derived class.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.RemoveAccessRule(System.Security.AccessControl.RegistryAccessRule)">
      <summary>Searches for an access control rule with the same user and <see cref="T:System.Security.AccessControl.AccessControlType" /> (allow or deny) as the specified access rule, and with compatible inheritance and propagation flags; if such a rule is found, the rights contained in the specified access rule are removed from it.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> that specifies the user and <see cref="T:System.Security.AccessControl.AccessControlType" /> to search for, and a set of inheritance and propagation flags that a matching rule, if found, must be compatible with. Specifies the rights to remove from the compatible rule, if found.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if a compatible rule is found; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.RemoveAccessRuleAll(System.Security.AccessControl.RegistryAccessRule)">
      <summary>Searches for all access control rules with the same user and <see cref="T:System.Security.AccessControl.AccessControlType" /> (allow or deny) as the specified rule and, if found, removes them.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> that specifies the user and <see cref="T:System.Security.AccessControl.AccessControlType" /> to search for. Any rights, inheritance flags, or propagation flags specified by this rule are ignored.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.RemoveAccessRuleSpecific(System.Security.AccessControl.RegistryAccessRule)">
      <summary>Searches for an access control rule that exactly matches the specified rule and, if found, removes it.</summary>
      <param name="rule">The <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.RemoveAuditRule(System.Security.AccessControl.RegistryAuditRule)">
      <summary>Searches for an audit control rule with the same user as the specified rule, and with compatible inheritance and propagation flags; if a compatible rule is found, the rights contained in the specified rule are removed from it.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> that specifies the user to search for, and a set of inheritance and propagation flags that a matching rule, if found, must be compatible with. Specifies the rights to remove from the compatible rule, if found.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if a compatible rule is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.RemoveAuditRuleAll(System.Security.AccessControl.RegistryAuditRule)">
      <summary>Searches for all audit rules with the same user as the specified rule and, if found, removes them.</summary>
      <param name="rule">A <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> that specifies the user to search for. Any rights, inheritance flags, or propagation flags specified by this rule are ignored.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.RemoveAuditRuleSpecific(System.Security.AccessControl.RegistryAuditRule)">
      <summary>Searches for an audit rule that exactly matches the specified rule and, if found, removes it.</summary>
      <param name="rule">The <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> to be removed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.ResetAccessRule(System.Security.AccessControl.RegistryAccessRule)">
      <summary>Removes all access control rules with the same user as the specified rule, regardless of <see cref="T:System.Security.AccessControl.AccessControlType" />, and then adds the specified rule.</summary>
      <param name="rule">The <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> to add. The user specified by this rule determines the rules to remove before this rule is added.</param>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.SetAccessRule(System.Security.AccessControl.RegistryAccessRule)">
      <summary>Removes all access control rules with the same user and <see cref="T:System.Security.AccessControl.AccessControlType" /> (allow or deny) as the specified rule, and then adds the specified rule.</summary>
      <param name="rule">The <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> to add. The user and <see cref="T:System.Security.AccessControl.AccessControlType" /> of this rule determine the rules to remove before this rule is added.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.AccessControl.RegistrySecurity.SetAuditRule(System.Security.AccessControl.RegistryAuditRule)">
      <summary>Removes all audit rules with the same user as the specified rule, regardless of the <see cref="T:System.Security.AccessControl.AuditFlags" /> value, and then adds the specified rule.</summary>
      <param name="rule">The <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> to add. The user specified by this rule determines the rules to remove before this rule is added.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rule" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.AccessControl.RegistrySecurity.AccessRightType">
      <summary>Gets the enumeration type that the <see cref="T:System.Security.AccessControl.RegistrySecurity" /> class uses to represent access rights.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the <see cref="T:System.Security.AccessControl.RegistryRights" /> enumeration.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RegistrySecurity.AccessRuleType">
      <summary>Gets the type that the <see cref="T:System.Security.AccessControl.RegistrySecurity" /> class uses to represent access rules.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the <see cref="T:System.Security.AccessControl.RegistryAccessRule" /> class.</returns>
    </member>
    <member name="P:System.Security.AccessControl.RegistrySecurity.AuditRuleType">
      <summary>Gets the type that the <see cref="T:System.Security.AccessControl.RegistrySecurity" /> class uses to represent audit rules.</summary>
      <returns>A <see cref="T:System.Type" /> object representing the <see cref="T:System.Security.AccessControl.RegistryAuditRule" /> class.</returns>
    </member>
  </members>
</doc>