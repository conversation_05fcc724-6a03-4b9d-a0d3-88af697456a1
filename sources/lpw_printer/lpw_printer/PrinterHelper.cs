using System.Drawing.Printing;
using System.Drawing;
using System;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Diagnostics;
using static lpw_printer.LabelSizeHelper;
using System.Globalization;

namespace lpw_printer
{
    internal class PrinterHelper
    {
        /// <summary>
        /// Retrieves the DPI (Dots Per Inch) for a specified printer.
        /// </summary>
        /// <param name="printerName">Name of the printer.</param>
        /// <returns>Average DPI if found, otherwise -1.</returns>
        public static int GetDPI(string printerName)
        {
            PrinterSettings printerSettings = new PrinterSettings { PrinterName = printerName };

            foreach (PrinterResolution resolution in printerSettings.PrinterResolutions)
            {
                if (resolution.Kind == PrinterResolutionKind.Custom && resolution.X > 0 && resolution.Y > 0)
                {
                    return (resolution.X + resolution.Y) / 2;
                }
            }

            // Fallback DPI by model name (basic)
            if (printerName.ToLower().Contains("zt411")) return 300;
            if (printerName.ToLower().Contains("zd620")) return 300;
            if (printerName.ToLower().Contains("420")) return 203;

            return 203; // default to 203 dpi
        }

        /// <summary>
        /// Prints an image to the specified printer, applying paper size and orientation settings.
        /// </summary>
        /// <param name="printerName">Name of the printer.</param>
        /// <param name="imagePath">Path to the image file.</param>
        /// <param name="size">Paper size (e.g., "2x3inch", "29x60mm").</param>
        /// <param name="orientation">Image orientation, either "portrait" or "landscape".</param>
        /// <param name="paperTray">The tray number or index to use.</param>
        /// <param name="rollType">Type of roll being used (optional).</param>
        /// <param name="showPrintedPage">Save the printed page as an image.</param>
        public static void Print(string printerName, string imagePath, string size, string orientation, string paperTray, string rollType, bool showPrintedPage)
        {
            try
            {
                var (width, height, sizeUnit) = ParseDimensions(size);
                float labelWidthInInches = ConvertToInches(width, sizeUnit);
                float labelHeightInInches = ConvertToInches(height, sizeUnit);

                // Get printer DPI
                int printerDpi = GetDPI(printerName);

                Image originalImage = Image.FromFile(imagePath);
                Bitmap dpiAdjustedImage = ResizeImageForDpi(originalImage, labelWidthInInches, labelHeightInInches, printerDpi);

                // Optional: convert to 1-bit monochrome for better clarity on label printers
                dpiAdjustedImage = ConvertToMonochrome(dpiAdjustedImage);

                // Adjust image orientation if needed
                RotateImageIfNecessary(dpiAdjustedImage, ref width, ref height, orientation);

                PrintDocument printDocument = new PrintDocument
                {
                    PrinterSettings = { PrinterName = printerName }
                };

                // Set paper size and page settings
                PaperSize printPaperSize = GetORCreatePaperSize(ref width, ref height, sizeUnit, printDocument.PrinterSettings.PaperSizes, rollType);
                PageSettings pageSettings = new PageSettings
                {
                    Landscape = false,
                    PaperSize = printPaperSize
                };

                // Set paper source if specified
                int paperSourceCount = printDocument.PrinterSettings.PaperSources.Count;
                Console.WriteLine($"No of available paper sources: {paperSourceCount}");
                if (!string.IsNullOrWhiteSpace(paperTray) &&
                    int.TryParse(paperTray, NumberStyles.Integer, CultureInfo.InvariantCulture, out int paperSourceIndex)
                    && paperSourceCount > 1 && paperSourceIndex < paperSourceCount)
                {
                    Console.WriteLine($"Going to set paper source {paperSourceIndex} from available number of {paperSourceCount} sources.");
                    pageSettings.PaperSource = printDocument.PrinterSettings.PaperSources[paperSourceIndex];
                }

                printDocument.DefaultPageSettings = pageSettings;

                printDocument.PrintPage += (sender, e) =>
                {
                    // Get the margins and printable area from the printer settings
                    var margins = new Margins(5, 5, 5, 5);
                    var paperSize = e.PageSettings.PaperSize;

                    float availablePrintableWidth = paperSize.Width - margins.Left - margins.Right;
                    float availablePrintableHeight = paperSize.Height - margins.Top - margins.Bottom;

                    // Resize image to fit available printable area while maintaining aspect ratio
                    float labelPrintableWidth = Math.Min(availablePrintableWidth, width);
                    float labelPrintableHeight = Math.Min(availablePrintableHeight, height);

                    float aspectRatio = dpiAdjustedImage.Width / (float)dpiAdjustedImage.Height;
                    if (labelPrintableWidth / aspectRatio <= labelPrintableHeight)
                    {
                        height = labelPrintableWidth / aspectRatio;
                        width = labelPrintableWidth;
                    }
                    else
                    {
                        width = labelPrintableHeight * aspectRatio;
                        height = labelPrintableHeight;
                    }

                    // Center the image in the printable area
                    float x = margins.Left + (availablePrintableWidth - width) / 2;
                    float y = margins.Top + (labelPrintableHeight - height) / 2;

                    // Draw the image
                    e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
                    e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                    e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

                    e.Graphics.DrawImage(dpiAdjustedImage, x, y, width, height);

                    // Save the printed page as an image if required
                    if (showPrintedPage)
                    {
                        SaveImageToDesktopAndOpen(dpiAdjustedImage, availablePrintableWidth, availablePrintableHeight, x, y, width, height);
                    }
                };

                // Start the printing process
                printDocument.PrintController = new StandardPrintController();
                printDocument.Print();
            }
            catch (Exception e)
            {
                Console.WriteLine($"PrintPage Error: {e.Message}");
            }
        }

        /// <summary>
        /// Converts a value to inches based on the provided unit.
        /// </summary>
        /// <param name="value">The value to convert.</param>
        /// <param name="unit">The unit of the value ("inch" or "mm").</param>
        /// <returns>Value in inches.</returns>
        public static float ConvertToInches(float value, string unit)
        {
            if (unit == "inch")
            {
                return value;
            }
            else if (unit == "mm")
            {
                return value / 25.4f;
            }
            else
            {
                return value;
            }
        }


        /// <summary>
        /// Resizes the image based on the printer's DPI and the label's dimensions.
        /// </summary>
        /// <param name="original">The original image to resize.</param>
        /// <param name="labelWidthInInches">Label width in inches.</param>
        /// <param name="labelHeightInInches">Label height in inches.</param>
        /// <param name="targetDpi">The target DPI of the printer.</param>
        /// <returns>Resized image.</returns>
        public static Bitmap ResizeImageForDpi(Image original, float labelWidthInInches, float labelHeightInInches, int targetDpi)
        {
            int targetWidthPx = (int)(labelWidthInInches * targetDpi);
            int targetHeightPx = (int)(labelHeightInInches * targetDpi);

            Bitmap resized = new Bitmap(targetWidthPx, targetHeightPx, PixelFormat.Format24bppRgb);
            using (Graphics g = Graphics.FromImage(resized))
            {
                g.Clear(Color.White);
                g.SmoothingMode = SmoothingMode.HighQuality;
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                g.DrawImage(original, 0, 0, targetWidthPx, targetHeightPx);
            }

            resized.SetResolution(targetDpi, targetDpi);
            return resized;
        }

        /// <summary>
        /// Converts an image to monochrome (1-bit) format for better clarity on label printers.
        /// </summary>
        /// <param name="source">The image to convert.</param>
        /// <returns>Converted monochrome image.</returns>
        public static Bitmap ConvertToMonochrome(Bitmap source)
        {
            // Create a new Bitmap with a supported pixel format (32bppArgb)
            Bitmap mono = new Bitmap(source.Width, source.Height, PixelFormat.Format32bppArgb);

            // Loop through the image pixels and convert to monochrome
            for (int x = 0; x < source.Width; x++)
            {
                for (int y = 0; y < source.Height; y++)
                {
                    Color pixelColor = source.GetPixel(x, y);

                    // Use a simple luminance formula to decide if the pixel is black or white
                    // A typical formula might use: 0.299*R + 0.587*G + 0.114*B
                    int brightness = (int)(0.299 * pixelColor.R + 0.587 * pixelColor.G + 0.114 * pixelColor.B);
                    Color monochromeColor = brightness < 128 ? Color.Black : Color.White;

                    mono.SetPixel(x, y, monochromeColor);
                }
            }

            return mono;
        }


        /// <summary>
        /// Saves the printed page as an image to the desktop and opens it in the default viewer.
        /// </summary>
        /// <param name="image">Image object to save.</param>
        /// <param name="availablePrintableWidth">Width of the printable area.</param>
        /// <param name="availablePrintableHeight">Height of the printable area.</param>
        /// <param name="x">X-coordinate for the image's top-left corner.</param>
        /// <param name="y">Y-coordinate for the image's top-left corner.</param>
        /// <param name="width">Width of the image.</param>
        /// <param name="height">Height of the image.</param>
        /// <param name="fileName">File name for the saved image, default is "PrintedPage.png".</param>
        public static void SaveImageToDesktopAndOpen(Image image, float availablePrintableWidth, float availablePrintableHeight, float x, float y, float width, float height, string fileName = "PrintedPage.png")
        {
            string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            string saveAsImagePath = Path.Combine(desktopPath, fileName);

            using (Bitmap bitmap = new Bitmap((int)availablePrintableWidth, (int)availablePrintableHeight))
            {
                using (Graphics graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.White);
                    graphics.SmoothingMode = SmoothingMode.HighQuality;
                    graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                    graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
                    graphics.DrawImage(image, x, y, width, height);
                }

                bitmap.Save(saveAsImagePath, ImageFormat.Png);
                Process.Start(new ProcessStartInfo(saveAsImagePath) { UseShellExecute = true });
            }
        }

        /// <summary>
        /// Determines the orientation (Portrait or Landscape) based on width and height.
        /// </summary>
        /// <param name="width">Width of the paper.</param>
        /// <param name="height">Height of the paper.</param>
        /// <returns>Orientation enum indicating Portrait or Landscape.</returns>
        private static Orientation GetOrientation(float width, float height)
        {
            return width < height ? Orientation.PORTRAIT : Orientation.LANDSCAPE;
        }

        /// <summary>
        /// Rotates the image if necessary to match the desired print orientation.
        /// </summary>
        /// <param name="image">The image to rotate.</param>
        /// <param name="width">Width of the label.</param>
        /// <param name="height">Height of the label.</param>
        /// <param name="orientation">The desired print orientation ("portrait" or "landscape").</param>
        private static void RotateImageIfNecessary(Image image, ref float width, ref float height, string orientation)
        {
            Orientation paperRollOrientation = GetOrientation(width, height);
            Orientation imageOrientation = GetOrientation(image.Width, image.Height);
            Orientation printOrientation = paperRollOrientation;

            if (!string.IsNullOrWhiteSpace(orientation))
            {
                printOrientation = orientation.ToLower() == "landscape" ? Orientation.LANDSCAPE : Orientation.PORTRAIT;
            }

            if ((paperRollOrientation == imageOrientation && imageOrientation != printOrientation) ||
                (paperRollOrientation == printOrientation && imageOrientation != printOrientation))
            {
                image.RotateFlip(RotateFlipType.Rotate90FlipNone);
                (width, height) = (height, width);
            }
        }

        /// <summary>
        /// Specifies the possible orientations for the label.
        /// </summary>
        public enum Orientation
        {
            PORTRAIT,
            LANDSCAPE
        }
    }
}
