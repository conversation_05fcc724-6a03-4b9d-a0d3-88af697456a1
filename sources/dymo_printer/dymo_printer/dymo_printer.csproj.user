<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartArguments>-print "DYMO LabelWriter 450 Turbo" "E:\Projects\Desktop3\res\tools\windows\print\dymo\config\2x4-portrait.label" "C:\Users\<USER>\Desktop\UMAR72x4_label-F2LTX77GHX9G-1709016991294.png" 1</StartArguments>
  </PropertyGroup>
  <PropertyGroup>
    <PublishUrlHistory>C:\Users\<USER>\Desktop\</PublishUrlHistory>
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup>
    <EnableSecurityDebugging>false</EnableSecurityDebugging>
  </PropertyGroup>
</Project>