<doc>
    <assembly>
        <name>SkiaSharp.Views.Desktop</name>
    </assembly>
    <members>
        <member name="T:SkiaSharp.Views.Desktop.Extensions">
            <summary>Various extension methods to convert between SkiaSharp types and System.Drawing types.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToBitmap(SkiaSharp.SKBitmap)">
            <param name="skiaBitmap">The SkiaSharp bitmap.</param>
            <summary>Converts a SkiaSharp bitmap into a System.Drawing bitmap.</summary>
            <returns>Returns a copy of the bitmap data as a System.Drawing bitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToBitmap(SkiaSharp.SKImage)">
            <param name="skiaImage">The SkiaSharp image.</param>
            <summary>Converts a SkiaSharp image into a System.Drawing bitmap.</summary>
            <returns>Returns a copy of the image data as a System.Drawing bitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToBitmap(SkiaSharp.SKPixmap)">
            <param name="pixmap">The SkiaSharp pixmap.</param>
            <summary>Converts a SkiaSharp pixmap into a System.Drawing bitmap.</summary>
            <returns>Returns a copy of the pixel data as a System.Drawing bitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToBitmap(SkiaSharp.SKPicture,SkiaSharp.SKSizeI)">
            <param name="picture">The SkiaSharp picture.</param>
            <param name="dimensions">The dimensions of the picture.</param>
            <summary>Converts a SkiaSharp picture into a System.Drawing bitmap.</summary>
            <returns>Returns a copy of the picture as a System.Drawing bitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingColor(SkiaSharp.SKColor)">
            <param name="color">The SkiaSharp color.</param>
            <summary>Converts a SkiaSharp color into a System.Drawing color.</summary>
            <returns>Returns a System.Drawing color.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingPoint(SkiaSharp.SKPoint)">
            <param name="point">The SkiaSharp point.</param>
            <summary>Converts a SkiaSharp point into a System.Drawing point.</summary>
            <returns>Returns a System.Drawing point.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingPoint(SkiaSharp.SKPointI)">
            <param name="point">The SkiaSharp point.</param>
            <summary>Converts a SkiaSharp point into a System.Drawing point.</summary>
            <returns>Returns a System.Drawing point.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingRect(SkiaSharp.SKRect)">
            <param name="rect">The SkiaSharp rectangle.</param>
            <summary>Converts a SkiaSharp rectangle into a System.Drawing rectangle.</summary>
            <returns>Returns a System.Drawing rectangle.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingRect(SkiaSharp.SKRectI)">
            <param name="rect">The SkiaSharp rectangle.</param>
            <summary>Converts a SkiaSharp rectangle into a System.Drawing rectangle.</summary>
            <returns>Returns a System.Drawing rectangle.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingSize(SkiaSharp.SKSize)">
            <param name="size">The SkiaSharp size.</param>
            <summary>Converts a SkiaSharp size into a System.Drawing size.</summary>
            <returns>Returns a System.Drawing size.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToDrawingSize(SkiaSharp.SKSizeI)">
            <param name="size">The SkiaSharp size.</param>
            <summary>Converts a SkiaSharp size into a System.Drawing size.</summary>
            <returns>Returns a System.Drawing size.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKBitmap(System.Drawing.Bitmap)">
            <param name="bitmap">The System.Drawing bitmap.</param>
            <summary>Converts a System.Drawing bitmap into a SkiaSharp bitmap.</summary>
            <returns>Returns a copy of the bitmap data as a SkiaSharp bitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKColor(System.Drawing.Color)">
            <param name="color">The System.Drawing color.</param>
            <summary>Converts a System.Drawing color into a SkiaSharp color.</summary>
            <returns>Returns a SkiaSharp color.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKImage(System.Drawing.Bitmap)">
            <param name="bitmap">The System.Drawing bitmap.</param>
            <summary>Converts a System.Drawing bitmap into a SkiaSharp image.</summary>
            <returns>Returns a copy of the bitmap data as a SkiaSharp image.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKPixmap(System.Drawing.Bitmap,SkiaSharp.SKPixmap)">
            <param name="bitmap">The System.Drawing bitmap.</param>
            <param name="pixmap">The SkiaSharp pixmap to hold the copy of the bitmap data.</param>
            <summary>Converts a System.Drawing bitmap into a SkiaSharp pixmap.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKPoint(System.Drawing.Point)">
            <param name="point">The System.Drawing point.</param>
            <summary>Converts a System.Drawing point into a SkiaSharp point.</summary>
            <returns>Returns a SkiaSharp point.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKPoint(System.Drawing.PointF)">
            <param name="point">The System.Drawing point.</param>
            <summary>Converts a System.Drawing point into a SkiaSharp point.</summary>
            <returns>Returns a SkiaSharp point.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKRect(System.Drawing.Rectangle)">
            <param name="rect">The System.Drawing rectangle.</param>
            <summary>Converts a System.Drawing rectangle into a SkiaSharp rectangle.</summary>
            <returns>Returns a SkiaSharp rectangle.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKRect(System.Drawing.RectangleF)">
            <param name="rect">The System.Drawing rectangle.</param>
            <summary>Converts a System.Drawing rectangle into a SkiaSharp rectangle.</summary>
            <returns>Returns a SkiaSharp rectangle.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKSize(System.Drawing.Size)">
            <param name="size">The System.Drawing size.</param>
            <summary>Converts a System.Drawing size into a SkiaSharp size.</summary>
            <returns>Returns a SkiaSharp size.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.Extensions.ToSKSize(System.Drawing.SizeF)">
            <param name="size">The System.Drawing size.</param>
            <summary>Converts a System.Drawing size into a SkiaSharp size.</summary>
            <returns>Returns a SkiaSharp size.</returns>
            <remarks />
        </member>
        <member name="T:SkiaSharp.Views.Desktop.SKControl">
            <summary>A control that can be drawn on using SkiaSharp drawing commands.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKControl">
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKControl" /> view.</summary>
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKControl.CanvasSize">
            <summary>Gets the current canvas size.</summary>
            <value />
            <remarks>The canvas size may be different to the view size as a result of the current device's pixel density.</remarks>
        </member>
        <member name="M:SkiaSharp.Views.Desktop.SKControl.Dispose(System.Boolean)">
            <param name="disposing">
                <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
            <summary>Releases the unmanaged resources used by the <see cref="T:SkiaSharp.Views.Desktop.SKControl" /> and optionally releases the managed resources.</summary>
            <remarks>Always dispose the object before you release your last reference to the <see cref="T:SkiaSharp.Views.Desktop.SKControl" />. Otherwise, the resources it is using will not be freed until the garbage collector calls the finalizer.</remarks>
        </member>
        <member name="M:SkiaSharp.Views.Desktop.SKControl.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <param name="e">A PaintEventArgs that contains the event data.</param>
            <summary>Raises the Paint event.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.SKControl.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs)">
            <param name="e">The event arguments that contain the drawing surface and information.</param>
            <summary>Implement this to draw on the canvas.</summary>
            <remarks>
                <format type="text/markdown"><![CDATA[
## Remarks

There are two ways to draw on this surface: by overriding the
<xref:SkiaSharp.Views.Desktop.SKControl.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs)>
method, or by attaching a handler to the
<xref:SkiaSharp.Views.Desktop.SKControl.PaintSurface>
event.

> [!IMPORTANT]
> If this method is overridden, then the base must be called, otherwise the
> event will not be fired.

## Examples

```csharp
protected override void OnPaintSurface (SKPaintSurfaceEventArgs e)
{
    // call the base method
    base.OnPaintSurface (e);

    var surface = e.Surface;
    var surfaceWidth = e.Info.Width;
    var surfaceHeight = e.Info.Height;

    var canvas = surface.Canvas;

    // draw on the canvas

    canvas.Flush ();
}
```
]]></format>
            </remarks>
        </member>
        <member name="E:SkiaSharp.Views.Desktop.SKControl.PaintSurface">
            <summary>Occurs when the the canvas needs to be redrawn.</summary>
            <remarks>
                <format type="text/markdown"><![CDATA[
## Remarks

There are two ways to draw on this surface: by overriding the
<xref:SkiaSharp.Views.Desktop.SKControl.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs)>
method, or by attaching a handler to the
<xref:SkiaSharp.Views.Desktop.SKControl.PaintSurface>
event.

## Examples

```csharp
myView.PaintSurface += (sender, e) => {
    var surface = e.Surface;
    var surfaceWidth = e.Info.Width;
    var surfaceHeight = e.Info.Height;

    var canvas = surface.Canvas;

    // draw on the canvas

    canvas.Flush ();
};
```
]]></format>
            </remarks>
        </member>
        <member name="T:SkiaSharp.Views.Desktop.SKGLControl">
            <summary>A hardware-accelerated control that can be drawn on using SkiaSharp drawing commands.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKGLControl">
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKGLControl" /> view.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKGLControl(OpenTK.Graphics.GraphicsMode)">
            <param name="mode">The OpenTK.Graphics.GraphicsMode of the control.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKGLControl" /> view.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKGLControl(OpenTK.Graphics.GraphicsMode,System.Int32,System.Int32,OpenTK.Graphics.GraphicsContextFlags)">
            <param name="mode">The OpenTK.Graphics.GraphicsMode of the control.</param>
            <param name="major">The major version for the OpenGL GraphicsContext.</param>
            <param name="minor">The minor version for the OpenGL GraphicsContext.</param>
            <param name="flags">The GraphicsContextFlags for the OpenGL GraphicsContext.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKGLControl" /> view.</summary>
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKGLControl.CanvasSize">
            <summary>Gets the current canvas size.</summary>
            <value />
            <remarks>The canvas size may be different to the view size as a result of the current device's pixel density.</remarks>
        </member>
        <member name="M:SkiaSharp.Views.Desktop.SKGLControl.Dispose(System.Boolean)">
            <param name="disposing">
                <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
            <summary>Releases the unmanaged resources used by the <see cref="T:SkiaSharp.Views.Desktop.SKGLControl" /> and optionally releases the managed resources.</summary>
            <remarks>Always dispose the object before you release your last reference to the <see cref="T:SkiaSharp.Views.Desktop.SKGLControl" />. Otherwise, the resources it is using will not be freed until the garbage collector calls the finalizer.</remarks>
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKGLControl.GRContext">
            <summary>Gets the current GPU context.</summary>
            <value />
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.SKGLControl.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <param name="e">A PaintEventArgs that contains the event data.</param>
            <summary>Raises the Paint event.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.Desktop.SKGLControl.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs)">
            <param name="e">The event arguments that contain the drawing surface and information.</param>
            <summary>Implement this to draw on the canvas.</summary>
            <remarks>
                <format type="text/markdown"><![CDATA[
## Remarks

There are two ways to draw on this surface: by overriding the
<xref:SkiaSharp.Views.Desktop.SKGLControl.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs)>
method, or by attaching a handler to the
<xref:SkiaSharp.Views.Desktop.SKGLControl.PaintSurface>
event. If the method is overridden, then the base must be called.

> [!IMPORTANT]
> If this method is overridden, then the base must be called, otherwise the
> event will not be fired.

## Examples

```csharp
protected override void OnPaintSurface (SKPaintGLSurfaceEventArgs e)
{
    // call the base method
    base.OnPaintSurface (e);

    var surface = e.Surface;
    var surfaceWidth = e.BackendRenderTarget.Width;
    var surfaceHeight = e.BackendRenderTarget.Height;

    var canvas = surface.Canvas;

    // draw on the canvas

    canvas.Flush ();
}
```
]]></format>
            </remarks>
        </member>
        <member name="E:SkiaSharp.Views.Desktop.SKGLControl.PaintSurface">
            <summary>Occurs when the surface needs to be redrawn.</summary>
            <remarks>
                <format type="text/markdown"><![CDATA[
## Remarks

There are two ways to draw on this surface: by overriding the
<xref:SkiaSharp.Views.Desktop.SKGLControl.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs)>
method, or by attaching a handler to the
<xref:SkiaSharp.Views.Desktop.SKGLControl.PaintSurface>
event.

## Examples

```csharp
myView.PaintSurface += (sender, e) => {
    var surface = e.Surface;
    var surfaceWidth = e.BackendRenderTarget.Width;
    var surfaceHeight = e.BackendRenderTarget.Height;

    var canvas = surface.Canvas;

    // draw on the canvas

    canvas.Flush ();
};
```
]]></format>
            </remarks>
        </member>
        <member name="T:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs">
            <summary>Provides data for the <see cref="E:SkiaSharp.Views.Desktop.SKGLControl.PaintSurface" /> event.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs(SkiaSharp.SKSurface,SkiaSharp.GRBackendRenderTarget)">
            <param name="surface">The surface that is being drawn on.</param>
            <param name="renderTarget">The render target that is currently being drawn.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs" /> event arguments.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs(SkiaSharp.SKSurface,SkiaSharp.GRBackendRenderTargetDesc)">
            <param name="surface">The surface that is being drawn on.</param>
            <param name="renderTarget">The render target that is currently being drawn.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs" /> event arguments.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs(SkiaSharp.SKSurface,SkiaSharp.GRBackendRenderTarget,SkiaSharp.GRSurfaceOrigin,SkiaSharp.SKColorType)">
            <param name="surface">The surface that is being drawn on.</param>
            <param name="renderTarget">The render target that is currently being drawn.</param>
            <param name="origin">The surface origin of the render target.</param>
            <param name="colorType">The color type of the render target.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs" /> event arguments.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs(SkiaSharp.SKSurface,SkiaSharp.GRBackendRenderTarget,SkiaSharp.GRSurfaceOrigin,SkiaSharp.SKColorType,SkiaSharp.GRGlFramebufferInfo)">
            <param name="surface">The surface that is being drawn on.</param>
            <param name="renderTarget">The render target that is currently being drawn.</param>
            <param name="origin">The surface origin of the render target.</param>
            <param name="colorType">The color type of the render target.</param>
            <param name="glInfo">The framebuffer info of the render target.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Mac.SKPaintGLSurfaceEventArgs" /> event arguments.</summary>
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs.BackendRenderTarget">
            <summary>Gets the render target that is currently being drawn.</summary>
            <value />
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs.ColorType">
            <summary>Gets the color type of the render target.</summary>
            <value />
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs.Origin">
            <summary>Gets the surface origin of the render target.</summary>
            <value />
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs.RenderTarget">
            <summary>Gets the render target that is currently being drawn.</summary>
            <value />
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintGLSurfaceEventArgs.Surface">
            <summary>Gets the surface that is currently being drawn on.</summary>
            <value />
            <remarks />
        </member>
        <member name="T:SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs">
            <summary>Provides data for the <see cref="E:SkiaSharp.Views.Desktop.SKControl.PaintSurface" /> event.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs(SkiaSharp.SKSurface,SkiaSharp.SKImageInfo)">
            <param name="surface">The surface that is being drawn on.</param>
            <param name="info">The information about the surface.</param>
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs" /> event arguments.</summary>
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs.Info">
            <summary>Gets the information about the surface that is currently being drawn.</summary>
            <value />
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs.Surface">
            <summary>Gets the surface that is currently being drawn on.</summary>
            <value />
            <remarks />
        </member>
    </members>
</doc>
