MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L Qڶ�        � " 0  $         �B       `    @                       �          `�                           �B  O    `  �                   �     �A  8                                                             H           .text   �"       $                    `.rsrc   �   `      &              @  @.reloc      �      *              @  B                �B      H     �'                                                        0 t      (  

o  
o  
s  
"   @"    "   @"    s  

	s  
(  
&	o  
 "    "    o  
 	o  
&	o  
 (   
&*0 M     s  
(!  
}  }  }  }  }  }  |   (  +|  (#  
*"($  
 *  0 �      �i�, r  p(%  
 8�   �o&  


	r� p('  
-r� p('  
-r� p('  
-0+Q(   +V�i��, r� p(%  
 +9(   +0�i�, rm p(%  
 +(   +
rN p(%  
 + *   0 A       ((  
o)  

+o*  
t,   (%  
  o+  
-��u"  ,o,  
 �*      
 !.     0 <      �
� (   r� p(%  
  � r� po-  
(.  
(%  
  � *    	     0 -     s  
(/  
}  }  }  |   (  +*"($  
 *"($  
 *No1  
{  o2  
*"($  
 *0	 �    {  
,+ .++t8�  s	  }  {  {  }   (3  
 (4  
}	  {	  o5  
o6  
(7  
-C%
}  }  
|  (  + �  {  |  �  %
}  (9  
}  {  }
  }  {
  {  �
  s:  
(  +}  {  �9.   �,  %r< p�%{  o<  
�%r| p�%{  o1  
�%r� p{  o=  
�4  (>  
�(?  
(%  
 (@  
}
  {
  {  oA  
 {
  r� p{  oB  
&{	  {
  {  o1  
{  oC  
oD  
(E  
-E%
}  }  
|  (  + ݷ   {  |  �  %
}  (F  
}  {  , �K �F r� p(%  
 �6�}  }  }	  }
  }  |  (G  
 �1�}  }  }	  }
  }  |  (H  
 *  A         /  6  6     *"($  
 * 0 �    {  
,++2 {  �}  {  �}  {  �}  }   ,++y {  �i�, {  �(I  
}   {  {  {  {  (  oD  

(E  
-D%
}  	}  |  (  + �   {  
|  �  %
}  (F  
}  {  , r� p{  rL p(K  
(%  
  + rj p{  r� p(K  
(%  
   �4}   r� p{  r p{  o-  
(L  
(%  
  � �/�}  }  }  }  |  (M  
 �)�}  }  }  }  |  (N  
 *  A4      A   �   3  4            b  i  /     *  BSJB         v4.0.30319     l     #~  x  �  #Strings      $  #US (     #GUID   8  �  #Blob         W
	
   �3      6                  N                             z      � \ �� ;   �� �� �� C� � (� J� �� �� �� e� ��
 �d
 #d s�  -� \ _ � �� C |  N r � c  �� S� ~� �� �� Q
 d v_ �_
 �� z_
 d �� 
� �� J�� �   � 6W �} � � �E �� T} %�    N         uiA      �iA       A  	  ;   A    �   A     � h   � t 4 % � ) � - � 5 - +9 G= G @ � L � hS ZX "  �  �   = >\ G LP     � �` �     � 8f )!    ��  4!    � �r �!    � H
 T"    � �r �"    � �r	 �"    �� 
 �"    �� 
 �"    �  x
 #    ��  #    �[  �%    �V   �%    ��  �%    �[  �'    �V      �   �         �   t   Z   Z   Z   D   f   f e  e 	 �  �  �
 ) � 1 � 9 � A � I � Q � Y � a � i � q � y � � � � � � � � [ � V  � � �/ !@5 !�5 !�9 � �? )�J 9�R � � � �9 � �] � � Q>d  �v  >  J� � � Y9� a�� a}� i�� q�� 	,� 	[� � � �� a�� �� > � � av� ��
��� � �+ ��  �? Y$ �g��m� *� � &� a��a������ * � �� ^�, �+4 �� 4 Y � �����?a��a��� .  ~.  �.  �. # �. + �. 3 �. ; �. C �. K �. S �. [ �. c �. k . s @ { m@ � �� � �� � �� � �� { �� � ��� � � �& k � � � � � �  %   '   %    ' p  4^���               �             �i             :              W               ��               �               �x          E � a � q Jw �q �� �   <>c__DisplayClass1_0 <Print>b__0 <printerName>5__1 <>8__1 <Print>d__1 <>u__1 IEnumerable`1 Task`1 AsyncTaskMethodBuilder`1 TaskAwaiter`1 <labelFilePath>5__2 <dymoSDKPrinter>5__2 <>u__2 Func`2 <imagePath>5__3 <printers>5__3 <HandlePrintOperation>d__3 <targetPrinter>5__4 <rollTray>5__4 <>s__5 Int16 <dymoSDKLabel>5__6 <ex>5__6 <>s__7 <Module> DymoSDK System.IO mscorlib System.Collections.Generic Add AwaitUnsafeOnCompleted get_IsCompleted get_Instance GetInstance FileMode Image get_Message Enumerable IDisposable Rectangle Console get_Name printerName get_DriverName WriteLine IAsyncStateMachine SetStateMachine stateMachine Type System.Core Close Dispose Parse Create <>1__state CompilerGeneratedAttribute GuidAttribute DebuggableAttribute ComVisibleAttribute AssemblyTitleAttribute AsyncStateMachineAttribute DebuggerStepThroughAttribute AssemblyTrademarkAttribute TargetFrameworkAttribute DebuggerHiddenAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute AssemblyCopyrightAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute DymoPrinter.exe PrintToPdf iTextSharp.text.pdf System.Runtime.Versioning String System.Drawing.Printing System.Drawing imagePath pdfFilePath labelFilePath SetImageFromFilePath LoadLabelFromFilePath get_Width get_Task IDymoLabel PrintLabel printer_util FileStream Program System Boolean Open Main HandlePrintToPdfOperation HandlePrintOperation System.Reflection StringCollection SetAbsolutePosition SetException StringComparison get_IsTwinTurbo App itextsharp System.Linq AsyncVoidMethodBuilder <>t__builder PrinterHelper GetAwaiter PdfWriter IPrinter IDymoPrinter ToLower IEnumerator GetEnumerator .ctor System.Diagnostics DymoSDK.Interfaces System.Runtime.InteropServices System.Runtime.CompilerServices DebuggingModes PrinterSettings args System.Threading.Tasks Equals DymoSDK.Implementations System.Collections get_InstalledPrinters GetPrinters Process Concat Format Object get_Height Init FirstOrDefault GetResult SetResult IElement Document get_Current Print Start prt HandlePrintersList MoveNext iTextSharp.text rollTray op_Equality     ��U s a g e : 
           F o r   p r i n t e r s   l i s t :                     - p t r l s t 
           F o r   p r i n t i n g   i m a g e   t o   P D F :     - p d f   [ P d f F i l e P a t h ]   [ I m a g e P a t h ] 
           F o r   p r i n t   i m a g e   o n   l a b e l :       - p r i n t   [ P r i n t e r N a m e ]   [ . L a b e l F i l e P a t h ]   [ I m a g e P a t h ]   [ r o l l T r a y ] - p t r l s t 	- p d f 
- p r i n t ��I n v a l i d   a r g u m e n t s .   U s a g e   f o r   p r i n t i n g   i m a g e   t o   P D F :   - p d f   [ P d f F i l e P a t h ]   [ I m a g e P a t h ] ��I n v a l i d   a r g u m e n t s .   U s a g e   f o r   p r i n t   i m a g e   o n   l a b e l :   - p r i n t   [ P r i n t e r N a m e ]   [ . L a b e l F i l e P a t h ]   [ I m a g e P a t h ]   [ r o l l T r a y ] [I n v a l i d   c o m m a n d .   U s e   - p t r l s t ,   - p d f   o r   - p r i n t . II m a g e   p r i n t e d   i n t o   P D F   s u c c e s s f u l l y .  GE r r o r   w h i l e   p r i n t i n g   i m a g e   t o   p d f :    ?F o u n d   P r i n t e r :   
   D r i v e r :                !
   N a m e :                    '
   I s T w i n T u r b o :     { 0 }  G R A P H I C  %P r i n t e r   n o t   f o u n d .  OI m a g e   p r i n t e d   o n   l a b e l   t h r o u g h   p r i n t e r      s u c c e s s f u l l y .  US D K   f a i l e d   t o   p r i n t   i m a g e   t h r o u g h   p r i n t e r    .  WE r r o r   w h i l e   p r i n t i n g   i m a g e   t h r o u g h   p r i n t e r (  :      �����"K�w�C̅        M eEI E   
 �� ��
 ��I�� �� ��u  u 0 
  Y     	����  ��  ��    q   ��
 ��imaiq    y  Yma
Yma  i 
ima
0 
ima   ��a  m �� 
a    }  Y}Yi	
i q  iq 	
i  �z\V4���T�m!t���?_�
:uymaa}imai��q  Y  a        TWrapNonExceptionThrows      pc_usb_info      ! Copyright © PhoneCheck 2023  ) $8096c939-dd84-4209-a7be-571b787d4126   1.0.0.0  M .NETFramework,Version=v4.7.2 TFrameworkDisplayName.NET Framework 4.7.2+ &printer_util.PrinterHelper+<Print>d__1     4 /printer_util.Program+<HandlePrintOperation>d__3       К�       i   4B  4$                             RSDS�A9"�D����̈́�   E:\Projects\Desktop3\sources\dymo_printer\dymo_printer\obj\Debug\DymoPrinter.pdb �B          �B                          �B            _CorExeMain mscoree.dll        �%  @                                                                                                                                                                                                                                                                                             �                  0  �                   H   X`  @          @4   V S _ V E R S I O N _ I N F O     ���                 ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   |   0 0 0 0 0 4 b 0      C o m m e n t s       "   C o m p a n y N a m e         @   F i l e D e s c r i p t i o n     p c _ u s b _ i n f o   0   F i l e V e r s i o n     1 . 0 . 0 . 0   @   I n t e r n a l N a m e   D y m o P r i n t e r . e x e   \   L e g a l C o p y r i g h t   C o p y r i g h t   �   P h o n e C h e c k   2 0 2 3   *   L e g a l T r a d e m a r k s         H   O r i g i n a l F i l e n a m e   D y m o P r i n t e r . e x e   8   P r o d u c t N a m e     p c _ u s b _ i n f o   4   P r o d u c t V e r s i o n   1 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   1 . 0 . 0 . 0                                                                                                            @     �2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      