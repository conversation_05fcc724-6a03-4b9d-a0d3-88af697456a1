<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- if ShouldIncludeNativeSkiaSharp == False then don't include the native libSkiaSharp -->
    <PropertyGroup>
        <ShouldIncludeNativeSkiaSharp Condition=" '$(ShouldIncludeNativeSkiaSharp)' == '' ">True</ShouldIncludeNativeSkiaSharp>
    </PropertyGroup>

    <PropertyGroup>
        <_SkiaSharpNativeLibraryCurrPath>$([System.IO.Path]::GetDirectoryName('$(MSBuildThisFileDirectory)'))</_SkiaSharpNativeLibraryCurrPath>
        <_SkiaSharpNativeLibraryDirName>$([System.IO.Path]::GetFileName('$(_SkiaSharpNativeLibraryCurrPath)'))</_SkiaSharpNativeLibraryDirName>
        <_SkiaSharpNativeLibraryRootDir>$(MSBuildThisFileDirectory)..\..\build\$(_SkiaSharpNativeLibraryDirName)\</_SkiaSharpNativeLibraryRootDir>
    </PropertyGroup>

    <ItemGroup Condition=" '$(ShouldIncludeNativeSkiaSharp)' != 'False' ">
        <NativeReference Include="$(_SkiaSharpNativeLibraryRootDir)libSkiaSharp.framework" Kind="Framework" />
    </ItemGroup>

</Project>