package com.phonecheck.info.android;

import com.phonecheck.command.device.android.action.AndroidStayAwakeCommand;
import com.phonecheck.command.device.android.imei.AndroidDisableLockCommand;
import com.phonecheck.command.device.android.imei.AndroidLockSwipeCommand;
import com.phonecheck.command.device.android.imei.AndroidLockSwipeUpCommand;
import com.phonecheck.command.device.android.info.*;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.battery.BatterySource;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.util.MacSupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.parser.device.android.info.AndroidBatteryMessageParser;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AndroidBatteryInfoServiceTest {
    @Mock
    private CommandExecutor executor;
    @InjectMocks
    private AndroidBatteryInfoService batteryInfoService;

    @Mock
    private MacSupportFilePathsStrategy macSupportFilePathsStrategy;

    @Mock
    private SupportFilePath supportFilePath;

    @Mock
    private AndroidBatteryMessageParser androidBatteryMessageParser;

    @Test
    public void getGoogleBatteryInfoTest() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");

        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(getBatteryCapacityStats());
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class))).
                thenReturn(getEstimatedBatteryCapacity());
        when(executor.execute(any(AndroidGetBatteryCycleCountCommand.class))).thenReturn(null);

        BatteryInfo batteryInfo = batteryInfoService.getGoogleBatteryInfo(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(91, batteryInfo.getHealthPercentage());
        assertEquals(0, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4577, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    public void getGoogleBatteryInfoTest1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");

        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(getBatteryCapacityStats());
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class))).
                thenReturn(getEstimatedBatteryCapacityForGoogle());
        when(executor.execute(any(AndroidGetBatteryCycleCountCommand.class))).thenReturn(null);

        BatteryInfo batteryInfo = batteryInfoService.getGoogleBatteryInfo(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(82, batteryInfo.getHealthPercentage());
        assertEquals(0, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4119, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    public void getBatteryHealthFromAsocTest() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(getBatteryCapacityStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromAsoc(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(96, batteryInfo.getHealthPercentage());
        assertEquals(160, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4800, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BATTERY_ASOC, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    public void getBatteryHealthFromUsageTest() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryUsageCommand.class))).thenReturn(getBatteryUsage());
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(getBatteryCapacityStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromUsage(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(94, batteryInfo.getHealthPercentage());
        assertEquals(403, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4700, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BATTERY_USAGE, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    public void getBatteryHealthFromCycleCountTest() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");

        when(executor.execute(any(AndroidGetBatteryCycleCountCommand.class))).thenReturn("100");

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromCycleCount(device);

        assertEquals(100, batteryInfo.getCycle());
        assertEquals(99, batteryInfo.getHealthPercentage());
    }

    @Test
    @DisplayName("Get Battery Health From Last Learned & Battery capacity Stats Test")
    public void getBatteryHealthFromBatteryStatsTest() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(getBatteryCapacityStats());
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class))).
                thenReturn(getLastLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(90, batteryInfo.getHealthPercentage());
        assertEquals(400, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4500, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Last Learned & Estimated Battery Stats Test")
    public void getBatteryHealthFromBatteryStatsTest1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class)))
                .thenReturn(getEstimatedBatteryCapacity());
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getLastLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(98, batteryInfo.getHealthPercentage());
        assertEquals(80, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4485, batteryInfo.getCurrentCapacity());
        assertEquals(4577, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Max Learned & Battery capacity Stats Test")
    public void getBatteryHealthFromBatteryStatsTest2() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(getBatteryCapacityStats());
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getMaxLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(88, batteryInfo.getHealthPercentage());
        assertEquals(480, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4400, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Max Learned & Estimated Battery Stats Test")
    public void getBatteryHealthFromBatteryStatsTest3() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class)))
                .thenReturn(getEstimatedBatteryCapacity());
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getMaxLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(96, batteryInfo.getHealthPercentage());
        assertEquals(160, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4393, batteryInfo.getCurrentCapacity());
        assertEquals(4577, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Min Learned & Battery capacity Stats Test")
    public void getBatteryHealthFromBatteryStatsTest4() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class)))
                .thenReturn(getBatteryCapacityStats());
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getMinLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(84, batteryInfo.getHealthPercentage());
        assertEquals(640, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4200, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Min Learned & Estimated Battery Stats Test")
    public void getBatteryHealthFromBatteryStatsTest5() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class)))
                .thenReturn(getEstimatedBatteryCapacity());
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getMinLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(91, batteryInfo.getHealthPercentage());
        assertEquals(360, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4165, batteryInfo.getCurrentCapacity());
        assertEquals(4577, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    public void test() throws IOException {
        when(supportFilePath.getPaths()).thenReturn(macSupportFilePathsStrategy);
        when(macSupportFilePathsStrategy.getToolsRootFolderPath()).thenReturn("root");
        String output = "エラー 電源をオンにして画面のロックを解除できませんでした - デバイスの接続を確認してください」;";
        when(executor.execute(any(AndroidGetBatteryMessageCommand.class)))
                .thenReturn(output);
        Pair<Boolean, Boolean> isBatteryDegraded = batteryInfoService.getBatteryMessageWithOCR("34869", "Japanese");
        Assertions.assertFalse(isBatteryDegraded.getLeft());
        verify(supportFilePath).getPaths();
        verify(macSupportFilePathsStrategy).getToolsRootFolderPath();
        verify(executor, Mockito.times(3)).execute(any(AndroidGetBatteryMessageCommand.class));
    }

    @Test
    public void getBatteryMessageWithOCRTest() throws IOException {
        when(supportFilePath.getPaths()).thenReturn(macSupportFilePathsStrategy);
        when(macSupportFilePathsStrategy.getToolsRootFolderPath()).thenReturn("root");
        String output = "battery_life_message: 50";
        when(executor.execute(any(AndroidGetBatteryMessageCommand.class))).thenReturn(output);
        when(executor.execute(any(AndroidStayAwakeCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidDisableLockCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidLockSwipeUpCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidLockSwipeCommand.class))).thenReturn(null);
        Pair<Boolean, Boolean> isBatteryDegraded = batteryInfoService.getBatteryMessageWithOCR("34869", "Japanese");
        assertTrue(isBatteryDegraded.getLeft());
        verify(supportFilePath).getPaths();
        verify(macSupportFilePathsStrategy).getToolsRootFolderPath();
        verify(executor, Mockito.times(1)).execute(any(AndroidGetBatteryMessageCommand.class));
        verify(executor, Mockito.times(1)).execute(any(AndroidStayAwakeCommand.class));
        verify(executor, Mockito.times(1)).execute(any(AndroidDisableLockCommand.class));
        verify(executor, Mockito.times(1)).execute(any(AndroidLockSwipeUpCommand.class));
        verify(executor, Mockito.times(2)).execute(any(AndroidLockSwipeCommand.class));
    }

    @Test
    @DisplayName("Get Battery Health From Last Learned & Estimated Battery Stats Test For Sony")
    public void getBatteryHealthFromBatteryStatsTest6() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        device.setMake("Sony");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class)))
                .thenReturn(getEstimatedBatteryCapacity());
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getMinLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(91, batteryInfo.getHealthPercentage());
        assertEquals(360, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4165, batteryInfo.getCurrentCapacity());
        assertEquals(4577, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Min Learned & Battery capacity Stats Test For Sony")
    public void getBatteryHealthFromBatteryStatsTest7() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        device.setMake("Sony");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class)))
                .thenReturn(getBatteryCapacityStats());
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getMinLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(100, batteryInfo.getBatteryPercentage());
        assertEquals(84, batteryInfo.getHealthPercentage());
        assertEquals(640, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(4200, batteryInfo.getCurrentCapacity());
        assertEquals(5000, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    @Test
    @DisplayName("Get Battery Health From Last Learned & Estimated Battery Stats Test")
    public void getBatteryHealthFromBatteryStatsTest8() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("12345");
        device.setModelNo("A1234");
        when(executor.execute(any(AndroidGetBatteryInfoCommand.class))).thenReturn(getBatteryStatsBatteryInfo());
        when(executor.execute(any(AndroidGetBatteryCapacityStatsCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetEstimatedBatteryCapacityCommand.class)))
                .thenReturn(getEstimatedBatteryCapacity());
        when(executor.execute(any(AndroidGetLastLearnedBatteryStatsCommand.class)))
                .thenReturn(getLastLearnedBatteryStats());

        BatteryInfo batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
        assertEquals(96, batteryInfo.getBatteryPercentage());
        assertEquals(93, batteryInfo.getHealthPercentage());
        assertEquals(280, batteryInfo.getCycle());
        assertEquals(0, batteryInfo.getOemHealthPercentage());
        assertEquals(0, batteryInfo.getCocoHealthPercentage());
        assertEquals(5631, batteryInfo.getCurrentCapacity());
        assertEquals(6440, batteryInfo.getDesignedCapacity());
        assertEquals(0, batteryInfo.getCocoCurrentCapacity());
        assertEquals(0, batteryInfo.getCocoDesignedCapacity());
        assertTrue(batteryInfo.getIsCharging());
        assertEquals(BatterySource.BS01, batteryInfo.getSource());
        assertEquals(0.0, batteryInfo.getBatteryResistance());
    }

    private String getBatteryInfo() {
        return """
                Current Battery Service state:
                  AC powered: false
                  USB powered: true
                  Wireless powered: false
                  Max charging current: 0
                  Max charging voltage: 0
                  Charge counter: 4577000
                  status: 2
                  health: 2
                  present: true
                  level: 100
                  scale: 100
                  voltage: 4256
                  temperature: 297
                  technology: Li-ion
                  batteryMiscEvent: 65536
                  batteryCurrentEvent: 0
                  mSecPlugTypeSummary: 2
                  LED Charging: true
                  LED Low Battery: true
                  current now: 3
                  charge counter: 4577000
                  Adaptive Fast Charging Settings: true
                  Super Fast Charging Settings: false
                FEATURE_WIRELESS_FAST_CHARGER_CONTROL: true
                  mWasUsedWirelessFastChargerPreviously: false
                  mWirelessFastChargingSettingsEnable: true
                LLB CAL: 20220323
                LLB MAN:\s
                LLB CURRENT: YEAR2023M9D29
                LLB DIFF: 78
                  mSavedBatteryBeginningDate: 0
                SEC_FEATURE_BATTERY_FULL_CAPACITY: true
                  mFullCapacityEnable: false
                FEATURE_HICCUP_CONTROL: false
                FEATURE_SUPPORTED_DAILY_BOARD: false
                SEC_FEATURE_BATTERY_LIFE_EXTENDER: false
                SEC_FEATURE_USE_WIRELESS_POWER_SHARING: false
                BatteryInfoBackUp
                  mSavedBatteryAsoc: 96
                  mSavedBatteryMaxTemp: 492
                  mSavedBatteryUsage: 40335
                  FEATURE_SAVE_BATTERY_CYCLE: true
                  SEC_FEATURE_PREVENT_SWELLING: false""";
    }

    private String getBatteryStatsBatteryInfo() {
        return """
                Current Battery Service state:
                  AC powered: true
                  USB powered: false
                  Wireless powered: false
                  Max charging current: 1475000
                  Max charging voltage: 5000000
                  Charge counter: 663119
                  status: 2
                  health: 2
                  present: true
                  level: 96
                  scale: 100
                  voltage: 3765
                  temperature: 270
                  technology: Li-ion
                  low temp shutdown level: -200
                  high temp shutdown level: 580
                  low battery level: 18
                  critical level: 10
                  shutdown level: 4
                  adjust shutdown level: 100
                  battery Type: 201
                  battery part number: BT-000393-00 R.C
                  battery serial number: T5191
                  battery Manufacture Date: 2021-05-12
                  rate capacity in mAh: 6440
                  decommission status of the battery: 0
                  base cumulative charge: 2620704
                  battery error status: 0
                  battery charge cycle: 407
                  battery total cumulative charge in mAh: 2624804
                  battery seconds since first use in secs: 106262948
                  battery present capacity: 5631
                  battery health percentage: 93
                  battery time to empty in secs: 65535
                  battery time to full in secs: 2289
                  battery present charge in mAh: 309
                  battery persent decommission threshold: 80
                  SOC from external: 6
                  SOC from internal: 10
                  Temperature from external: 27
                  Temperature from internal: 260
                  bk battery voltage: 0
                  bk battery temperature: 0
                  bk battery capacity: 0
                  bk low temp shutdown level: 255
                  bk high temp shutdown level: 255
                  pk battery status: 1
                  pk battery present: false
                  pk battery voltage: 2""";
    }

    private String getBatteryCapacityStats() {
        return "Capacity: 5000, Rated: 4900, Typical: 5000, Computed drain: 0, actual drain: 0";
    }

    private String getBatteryUsage() {
        return "mSavedBatteryUsage: 40335";
    }

    private String getEstimatedBatteryCapacity() {
        return "Estimated battery capacity: 4577 mAh";
    }

    private String getLastLearnedBatteryStats() {
        return "Last learned battery capacity: 4500 mAh";
    }

    private String getMaxLearnedBatteryStats() {
        return "Max learned battery capacity: 4400 mAh";
    }

    private String getMinLearnedBatteryStats() {
        return "Min learned battery capacity: 4200 mAh";
    }

    private String getEstimatedBatteryCapacityForGoogle() {
        return "+20m34s102ms (2) 093 +tmpwhitelist=1000:\"63b2f2e com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "         +21m28s867ms (2) 093 +job=u0a156:\"com.google.android.apps.turbo/com.google" +
                ".android.libraries.smartbattery.appusage.library.InferAppBucketsJob\"\n" +
                "         +21m28s975ms (2) 093 -job=u0a156:\"com.google.android.apps.turbo/com.google" +
                ".android.libraries.smartbattery.appusage.library.InferAppBucketsJob\"\n" +
                "         +21m36s891ms (2) 093 -tmpwhitelist=1000:\"63b2f2e com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +1h20m34s788ms (2) 093 +tmpwhitelist=1000:\"2ca806c com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +1h46m45s698ms (2) 092 -tmpwhitelist=1000:\"2ca806c com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +1h52m29s838ms (2) 092 +job=u0a156:\"com.google.android.apps.turbo/com.google.an" +
                "droid.libraries.smartbattery.appusage.library.InferAppBucketsJob\"\n" +
                "       +1h52m29s932ms (2) 092 -job=u0a156:\"com.google.android.apps.turbo/com.google" +
                ".android.libraries.smartbattery.appusage.library.InferAppBucketsJob\"\n" +
                "       +2h20m34s752ms (2) 092 +tmpwhitelist=1000:\"9a25979 com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +2h22m21s362ms (2) 092 -tmpwhitelist=1000:\"9a25979 com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +2h49m13s374ms (2) 091 +tmpwhitelist=1000:\"630c6fa com.android.settings.battery" +
                ".action.PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +3h29m23s797ms (2) 091 -cellular_high_tx_power +job=u0a156:\"com.google.android.apps." +
                "turbo/com.google.android.libraries.smartbattery.appusage.library.AppStandbyEnabledChangedJob\"\n" +
                "       +3h29m23s807ms (2) 091 +job=u0a156:\"com.google.android.apps.turbo/com.google.android" +
                ".libraries.smartbattery.appusage.library.InferAppBucketsJob\"\n" +
                "       +3h29m23s938ms (2) 091 -job=u0a156:\"com.google.android.apps.turbo/com.google.android" +
                ".libraries.smartbattery.appusage.library.AppStandbyEnabledChangedJob\"\n" +
                "       +3h29m24s539ms (2) 091 -job=u0a156:\"com.google.android.apps.turbo/com.google.android" +
                ".libraries.smartbattery.appusage.library.InferAppBucketsJob\"\n" +
                "       +3h29m27s917ms (2) 091 -tmpwhitelist=1000:\"630c6fa com.android.settings.battery.action" +
                ".PERIODIC_JOB_UPDATE/u0\"\n" +
                "       +3h34m23s396ms (2) 091 -cellular_high_tx_power stats=0:\"battery-state\"\n" +
                "  System starts: 0, currently on battery: false\n" +
                "  Estimated battery capacity: 4119 mAh\n" +
                "  Last learned battery capacity: 4130 mAh\n" +
                "  Min learned battery capacity: 4130 mAh\n" +
                "  Max learned battery capacity: 4132 mAh\n" +
                "  Time on battery: 3h 34m 23s 290ms (99.9%) realtime, 14m 35s 870ms (6.8%) uptime\n" +
                "  Time on battery screen off: 3h 28m 26s 106ms (97.2%) realtime, 8m 38s 687ms (4.0%) uptime\n" +
                "  Time on battery screen doze: 0ms (0.0%)\n" +
                "  Device battery use since last full charge\n" +
                "  Estimated power use (mAh):\n" +
                "  Kernel Wake lock google-battery: 764ms (114 times) realtime\n" +
                "  Kernel Wake lock battery: 13ms (2 times) realtime\n" +
                "  Wake lock u0a156 *job*/com.google.android.apps.turbo/com.google.android.libraries.smartbattery" +
                ".appusage.library.InferAppBucketsJob: 214ms (3 times) max=741 actual=969 realtime\n" +
                "  Wake lock u0a156 *job*/com.google.android.apps.turbo/com.google.android.libraries.smartbattery" +
                ".appusage.library.AppStandbyEnabledChangedJob: 18ms (1 times) max=155 actual=155 realtime\n" +
                "    Proc vendor.google.google_battery-service:\n" +
                "      Wakeup alarm *walarm*:com.android.settings.battery.action.PERIODIC_JOB_UPDATE: 4 times\n" +
                "    Wake lock *job*/com.google.android.apps.turbo/com.google.android.libraries.smartbattery" +
                ".appusage.library.InferAppBucketsJob: 214ms partial (3 times) max=741 actual=969 realtime\n" +
                "    Wake lock *job*/com.google.android.apps.turbo/com.google.android.libraries.smartbattery" +
                ".appusage.library.AppStandbyEnabledChangedJob: 18ms partial (1 times) max=155 actual=155 realtime\n" +
                "    Job com.google.android.apps.turbo/com.google.android.libraries.smartbattery.appusage" +
                ".library.InferAppBucketsJob: 934ms realtime (3 times)\n" +
                "    Job com.google.android.apps.turbo/com.google.android.libraries.smartbattery.appusage" +
                ".library.AppStandbyEnabledChangedJob: 141ms realtime (1 times)\n" +
                "    Job Completions com.google.android.apps.turbo/com.google.android.libraries.smartbattery" +
                ".appusage.library.InferAppBucketsJob: successful_finish(3x)\n" +
                "    Job Completions com.google.android.apps.turbo/com.google.android.libraries.smartbattery" +
                ".appusage.library.AppStandbyEnabledChangedJob: canceled(1x)\n" +
                "      Service com.google.android.libraries.smartbattery.appusage.library" +
                ".AppStandbyEnabledChangedJob:\n" +
                "      Service com.google.android.libraries.smartbattery.appusage.library" +
                ".InferAppBucketsJob:\n" +
                "    battery_level_collection_delay_ms=300000\n" +
                "    battery_charged_delay_ms=900000\n" +
                "    battery_charging_enforce_level=90\n" +
                "On-battery energy consumer stats (microcoulombs) \n";
    }
}
