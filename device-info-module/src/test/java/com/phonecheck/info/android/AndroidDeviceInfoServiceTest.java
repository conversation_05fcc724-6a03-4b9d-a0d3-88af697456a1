package com.phonecheck.info.android;

import com.phonecheck.command.device.android.info.*;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.android.AndroidProperty;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DiskSize;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.MemoryUnit;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.android.info.AndroidGetDiskSizeParser;
import com.phonecheck.parser.device.android.info.AndroidGetPropertiesParser;
import com.phonecheck.parser.device.android.info.AndroidRAMSizeParser;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidDeviceInfoServiceTest {
    private final AndroidDevice device = new AndroidDevice();

    private final String outputString = "sample output";
    @Mock
    private CommandExecutor executor;
    @Mock
    private AndroidGetDiskSizeParser androidGetDiskSizeParser;
    @Mock
    private AndroidRAMSizeParser androidRAMSizeParser;
    @Mock
    private AndroidGetPropertiesParser androidGetPropertiesParser;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private InMemoryStore inMemoryStore;
    @InjectMocks
    private AndroidDeviceInfoService androidDeviceInfoService;

    @Test
    public void getPropertiesTest() throws IOException {

        AndroidProperty[] androidProperties = {AndroidProperty.FIRMWARE, AndroidProperty.OS_VERSION};
        Map<AndroidProperty, String> outputMap = new HashMap<>();
        when(executor.execute(any(AndroidGetPropertiesCommand.class))).thenReturn(outputString);
        when(androidGetPropertiesParser.parse(outputString, androidProperties)).thenReturn(outputMap);

        Map<AndroidProperty, String> propertiesMap = androidDeviceInfoService.getProperties(device, androidProperties);

        verify(executor).execute(any(AndroidGetPropertiesCommand.class));
        verify(androidGetPropertiesParser).parse(outputString, androidProperties);
        assertEquals(outputMap, propertiesMap);
    }

    @Test
    public void getDiskSizeByStorageSizeCommandTest() throws IOException {
        when(executor.execute(any(AndroidGetStorageSizeCommand.class))).thenReturn(outputString);
        when(androidGetDiskSizeParser.parseStorageSizeOutput(outputString))
                .thenReturn(new DiskSize(128L, MemoryUnit.GIGABYTES));

        DiskSize result = androidDeviceInfoService.getDiskSize(device);

        verify(executor).execute(any(AndroidGetStorageSizeCommand.class));
        verify(androidGetDiskSizeParser).parseStorageSizeOutput(outputString);
        assertEquals(result, new DiskSize(128L, MemoryUnit.GIGABYTES));
    }

    @Test
    public void getDiskSizeByDiskStatsCommandTest() throws IOException {
        when(executor.execute(any(AndroidGetStorageSizeCommand.class))).thenReturn(null);
        when(executor.execute(any(AndroidGetDiskStatsCommand.class))).thenReturn(outputString);
        when(androidGetDiskSizeParser.parseDiskStatsOutput(outputString))
                .thenReturn(new DiskSize(128L, MemoryUnit.GIGABYTES));

        DiskSize result = androidDeviceInfoService.getDiskSize(device);

        verify(executor).execute(any(AndroidGetDiskStatsCommand.class));
        verify(androidGetDiskSizeParser).parseDiskStatsOutput(outputString);
        assertEquals(result, new DiskSize(128L, MemoryUnit.GIGABYTES));
    }

    @Test
    public void getDeviceRAMTest() throws IOException {

        when(executor.execute(any(AndroidRAMSizeCommand.class))).thenReturn(outputString);
        when(androidRAMSizeParser.parse(outputString)).thenReturn("8 GB RAM");

        String result = androidDeviceInfoService.getDeviceRAM(device);

        verify(executor).execute(any(AndroidRAMSizeCommand.class));
        verify(androidRAMSizeParser).parse(outputString);
        assertEquals("8 GB RAM", result);
    }

    @Test
    public void testGetRootedStatusWhenDeviceIsRooted() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil).
                printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidRootedStatusCommand.class)))
                .thenReturn("Sample output");

        RootedStatus output = androidDeviceInfoService.getRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidRootedStatusCommand.class));
    }


    @Test
    public void testGetRootedStatusWhenDeviceIsNotRooted() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil)
                .printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidRootedStatusCommand.class))).thenReturn("");

        RootedStatus output = androidDeviceInfoService.getRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.NOT_ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidRootedStatusCommand.class));
    }

    @Test
    public void testIsDeviceNetworkLocked() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("locked");
        boolean isLocked = androidDeviceInfoService.isDeviceNetworkLocked(device);
        verify(executor).execute(any(AndroidGetPropertyByKeyCommand.class));
        Assertions.assertTrue(isLocked);
    }


    @Test
    public void testIsDeviceESimSupported() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidESimStatusFromEuiccCommand.class))).thenReturn("");
        when(executor.execute(any(AndroidESimStatusFromSettingsCommand.class))).thenReturn("");
        when(executor.execute(any(AndroidESimStatusFromSystemPropCommand.class))).thenReturn("");
        when(executor.execute(any(AndroidESimStatusFromEuiccPackageCommand.class)))
                .thenReturn("package:com.samsung.euicc");
        boolean isESimSupported = androidDeviceInfoService.isESimSupported(device);
        verify(executor).execute(any(AndroidESimStatusFromEuiccCommand.class));
        verify(executor).execute(any(AndroidESimStatusFromSettingsCommand.class));
        verify(executor).execute(any(AndroidESimStatusFromSystemPropCommand.class));
        verify(executor).execute(any(AndroidESimStatusFromEuiccPackageCommand.class));
        Assertions.assertTrue(isESimSupported);
    }

    @Test
    public void testIsDeviceNetworkUnlocked() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("Absent");
        boolean isLocked = androidDeviceInfoService.isDeviceNetworkLocked(device);
        verify(executor).execute(any(AndroidGetPropertyByKeyCommand.class));
        Assertions.assertFalse(isLocked);
    }

    @Test
    public void testIsSimCardPresent() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("LOADED");
        boolean isPresent = androidDeviceInfoService.isSimCardPresent(device);
        Assertions.assertTrue(isPresent);
    }

    @Test
    public void testIsSimCardAbsentActivatedOrUnactivated() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("ABSENT");
        boolean isPresent = androidDeviceInfoService.isSimCardPresent(device);
        Assertions.assertFalse(isPresent);
    }

    @Test
    public void testGetSimSlotsCountEmpty() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("");
        int simSlotsCount = androidDeviceInfoService.getSimSlotsCount(device);
        Assertions.assertEquals(simSlotsCount, 0);
    }

    @Test
    public void testGetSimSlotsCount1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("ABSENT");
        int simSlotsCount = androidDeviceInfoService.getSimSlotsCount(device);
        Assertions.assertEquals(simSlotsCount, 1);
    }

    @Test
    public void testGetSimSlotsCount2() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("ABSENT,LOADED");
        int simSlotsCount = androidDeviceInfoService.getSimSlotsCount(device);
        Assertions.assertEquals(simSlotsCount, 2);
    }

    @Test
    public void testGetPresentSimSlotsCount() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("ABSENT,LOADED");
        int simSlotsCount = androidDeviceInfoService.getPresentSimsCount(device);
        Assertions.assertEquals(simSlotsCount, 1);
    }

    @Test
    public void testGetPresentSimSlotsCount1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("ABSENT,ABSENT");
        int simSlotsCount = androidDeviceInfoService.getPresentSimsCount(device);
        Assertions.assertEquals(simSlotsCount, 0);
    }

    @Test
    public void testGetPresentSimSlotsCount3() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class))).thenReturn("");
        int simSlotsCount = androidDeviceInfoService.getPresentSimsCount(device);
        Assertions.assertEquals(simSlotsCount, 0);
    }

    @Test
    public void testisSimCardCheckEnabledAndSimDetected1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);

        CloudCustomizationResponse customizationResponse = CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings
                        .builder()
                        .warningMessagesEnabled(true)
                        .warningMessages(CloudCustomizationResponse.WarningMessage
                                .builder()
                                .isSimCardCheckEnabled(true)
                                .build())
                        .build()).build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(customizationResponse);
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class)))
                .thenReturn("ABSENT,LOADED");

        boolean isSimCardCheckEnabledAndSimDetected
                = androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device);
        Assertions.assertTrue(isSimCardCheckEnabledAndSimDetected);
    }

    @Test
    public void testisSimCardCheckEnabledAndSimDetected2() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);

        CloudCustomizationResponse customizationResponse = CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings
                        .builder()
                        .warningMessagesEnabled(true)
                        .warningMessages(CloudCustomizationResponse.WarningMessage
                                .builder()
                                .isSimCardCheckEnabled(true)
                                .build())
                        .build()).build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(customizationResponse);
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class)))
                .thenReturn("ABSENT,NOT_READY");

        boolean isSimCardCheckEnabledAndSimDetected
                = androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device);
        Assertions.assertFalse(isSimCardCheckEnabledAndSimDetected);
    }

    @Test
    public void testisSimCardCheckEnabledAndSimDetected3() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);

        CloudCustomizationResponse customizationResponse = CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings
                        .builder()
                        .warningMessagesEnabled(true)
                        .warningMessages(CloudCustomizationResponse.WarningMessage
                                .builder()
                                .isSimCardCheckEnabled(true)
                                .build())
                        .build()).build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(customizationResponse);
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class)))
                .thenReturn("LOADED");

        boolean isSimCardCheckEnabledAndSimDetected
                = androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device);
        Assertions.assertTrue(isSimCardCheckEnabledAndSimDetected);
    }

    @Test
    public void testisSimCardCheckEnabledAndSimDetected4() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);

        CloudCustomizationResponse customizationResponse = CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings
                        .builder()
                        .warningMessagesEnabled(true)
                        .warningMessages(CloudCustomizationResponse.WarningMessage
                                .builder()
                                .isSimCardCheckEnabled(true)
                                .build())
                        .build()).build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(customizationResponse);
        when(executor.execute(any(AndroidGetPropertyByKeyCommand.class)))
                .thenReturn("ABSENT, ABSENT");

        boolean isSimCardCheckEnabledAndSimDetected
                = androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device);
        Assertions.assertFalse(isSimCardCheckEnabledAndSimDetected);
    }

    @Test
    public void testGetSuRootedStatusWhenDeviceIsRooted() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil).
                printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidGetSuRootedStatusCommand.class), Mockito.anyLong()))
                .thenReturn("");

        RootedStatus output = androidDeviceInfoService.getSuRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidGetSuRootedStatusCommand.class),
                Mockito.anyLong());
    }

    @Test
    public void testGetSuRootedStatusForOtherThanNonRootedPath() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil).
                printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidGetSuRootedStatusCommand.class), Mockito.anyLong()))
                .thenReturn("bin/path/");

        RootedStatus output = androidDeviceInfoService.getSuRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidGetSuRootedStatusCommand.class),
                Mockito.anyLong());
    }

    @Test
    public void testGetSuRootedStatusWhenDeviceIsNotRooted() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil).
                printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidGetSuRootedStatusCommand.class), Mockito.anyLong()))
                .thenReturn("inaccessible");

        RootedStatus output = androidDeviceInfoService.getSuRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.NOT_ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidGetSuRootedStatusCommand.class),
                Mockito.anyLong());
    }

    @Test
    public void testIsDeviceStereoSupported1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidCheckStereoSupportCommand.class)))
                .thenReturn("SEC_AUDIO_SUPPORT_DUAL_SPEAKER=true");
        boolean isDeviceStereoSupported = androidDeviceInfoService.isDeviceStereoSupported(device);
        Assertions.assertTrue(isDeviceStereoSupported);
    }

    @Test
    public void testIsDeviceStereoSupported2() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidCheckStereoSupportCommand.class)))
                .thenReturn("SEC_AUDIO_SUPPORT_DUAL_SPEAKER=false");
        boolean isDeviceStereoSupported = androidDeviceInfoService.isDeviceStereoSupported(device);
        Assertions.assertFalse(isDeviceStereoSupported);
    }

    @Test
    public void testIsDeviceStereoSupported3() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        when(executor.execute(any(AndroidCheckStereoSupportCommand.class)))
                .thenReturn(StringUtils.EMPTY);
        boolean isDeviceStereoSupported = androidDeviceInfoService.isDeviceStereoSupported(device);
        Assertions.assertFalse(isDeviceStereoSupported);
    }

    @Test
    public void testGetMagiskRootedStatusWhenDeviceIsRooted() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil).
                printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidGetMagiskRootStatusCommand.class)))
                .thenReturn("package:com.topjohnwu.magisk");

        RootedStatus output = androidDeviceInfoService.getMagiskRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidGetMagiskRootStatusCommand.class));
    }

    @Test
    public void testGetMagiskRootedStatusWhenDeviceIsNotRooted() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("test device");
        Mockito.doNothing().when(timerLoggerUtil).
                printTimerLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDateTime.class));
        Mockito.when(executor.execute(Mockito.any(AndroidGetMagiskRootStatusCommand.class)))
                .thenReturn("");

        RootedStatus output = androidDeviceInfoService.getMagiskRootedStatus(device);

        Assertions.assertNotNull(output);
        Assertions.assertEquals(RootedStatus.NOT_ROOTED, output);
        verify(executor, times(1)).execute(Mockito.any(AndroidGetMagiskRootStatusCommand.class));
    }
}
