package com.phonecheck.info.android;

import com.phonecheck.command.device.android.info.AndroidFrpStatusCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.parser.device.android.info.AndroidFrpStatusViaAdbParser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AndroidDeviceLockInfoServiceTest {
    @Mock
    private CommandExecutor executor;

    @Mock
    private AndroidFrpStatusViaAdbParser androidFrpStatusViaAdbParser;

    @InjectMocks
    private AndroidDeviceLockInfoService frpInfoService;


    @Test
    public void testGetFrpStatus() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("123");
        String output = "Something";
        DeviceLock expectedDeviceLock = DeviceLock.OFF;
        when(executor.execute(any(AndroidFrpStatusCommand.class))).thenReturn(output);
        when(androidFrpStatusViaAdbParser.parse(output)).thenReturn(expectedDeviceLock);

        DeviceLock deviceLock = frpInfoService.getFrpStatusViaAdb(device);

        assertNotNull(deviceLock);
        assertEquals(expectedDeviceLock, deviceLock);
    }
}
