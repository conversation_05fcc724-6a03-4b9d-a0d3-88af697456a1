package com.phonecheck.info.android;

import com.phonecheck.model.android.AndroidProperty;
import com.phonecheck.model.device.AndroidDevice;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidSerialInfoServiceTest {

    @Mock
    private AndroidDeviceInfoService deviceInfoService;

    private AndroidSerialInfoService serialInfoService;
    private AndroidDevice testDevice;

    @BeforeEach
    void setUp() {
        serialInfoService = new AndroidSerialInfoService(deviceInfoService);
        testDevice = new AndroidDevice();
        testDevice.setId("test-device-123");
    }

    @Test
    void getDeviceSerialReturnsRilSerialNumberWhenValid() throws IOException {
        // Arrange
        String expectedSerial = "ABC123";
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.RIL_SERIAL_NO)))
                .thenReturn(expectedSerial);

        // Act
        String result = serialInfoService.getDeviceSerial(testDevice);

        // Assert
        assertEquals(expectedSerial, result);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.RIL_SERIAL_NO);
        verify(deviceInfoService, never()).getPropertyByKey(testDevice, AndroidProperty.BOOT_SERIAL_NO);
    }

    @Test
    void getDeviceSerialReturnsBootSerialNumberWhenRilSerialIsBlank() throws IOException {
        // Arrange
        String expectedSerial = "XYZ789";
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.RIL_SERIAL_NO)))
                .thenReturn("");
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.BOOT_SERIAL_NO)))
                .thenReturn(expectedSerial);

        // Act
        String result = serialInfoService.getDeviceSerial(testDevice);

        // Assert
        assertEquals(expectedSerial, result);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.RIL_SERIAL_NO);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.BOOT_SERIAL_NO);
    }

    @Test
    void getDeviceSerialReturnsBootSerialNumberWhenRilSerialContainsZeros() throws IOException {
        // Arrange
        String expectedSerial = "XYZ789";
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.RIL_SERIAL_NO)))
                .thenReturn("0000123");
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.BOOT_SERIAL_NO)))
                .thenReturn(expectedSerial);

        // Act
        String result = serialInfoService.getDeviceSerial(testDevice);

        // Assert
        assertEquals(expectedSerial, result);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.RIL_SERIAL_NO);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.BOOT_SERIAL_NO);
    }

    @Test
    void getDeviceSerialReturnsEmptyStringWhenIOExceptionOccurs() throws IOException {
        // Arrange
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.RIL_SERIAL_NO)))
                .thenThrow(new IOException("Test exception"));

        // Act
        String result = serialInfoService.getDeviceSerial(testDevice);

        // Assert
        assertEquals(StringUtils.EMPTY, result);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.RIL_SERIAL_NO);
        verify(deviceInfoService, never()).getPropertyByKey(testDevice, AndroidProperty.BOOT_SERIAL_NO);
    }

    @Test
    void getDeviceSerialReturnsEmptyStringWhenBothSerialPropertiesAreNull() throws IOException {
        // Arrange
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.RIL_SERIAL_NO)))
                .thenReturn(null);
        when(deviceInfoService.getPropertyByKey(any(), eq(AndroidProperty.BOOT_SERIAL_NO)))
                .thenReturn(null);

        // Act
        String result = serialInfoService.getDeviceSerial(testDevice);

        // Assert
        assertNull(null, result);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.RIL_SERIAL_NO);
        verify(deviceInfoService).getPropertyByKey(testDevice, AndroidProperty.BOOT_SERIAL_NO);
    }
}