package com.phonecheck.info.android;

import com.phonecheck.command.device.android.info.AndroidGetPropertyByKeyCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.status.KnoxStatus;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@AllArgsConstructor
public class AndroidKnoxInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidKnoxInfoService.class);

    private final CommandExecutor executor;
    private static final String BOOT_WARRANTY_BIT_ARGUMENT = "ro.boot.warranty_bit";
    private static final String WARRANTY_BIT_CMD_ARGUMENT = "ro.warranty_bit";

    /**
     * Retrieves Samsung device's knox status
     *
     * @param device
     * @return KnoxStatus
     */
    public KnoxStatus getKnoxStatus(final AndroidDevice device) {
        try {
            int knoxVoid = getKnoxVoidStatus(device, BOOT_WARRANTY_BIT_ARGUMENT);
            if (knoxVoid == -1) {
                knoxVoid = getKnoxVoidStatus(device, WARRANTY_BIT_CMD_ARGUMENT);
            }
            return (knoxVoid == 1) ? KnoxStatus.KNOX_ON : KnoxStatus.KNOX_OFF;
        } catch (IOException e) {
            LOGGER.error("Exception occurred while fetching knox info", e);
            return KnoxStatus.KNOX_OFF;
        }
    }

    /**
     * fetches knox void status for the device
     *
     * @param device
     * @param key
     * @return -1 for KNOX_NA , 0 for KNOX_OFF, 1 for KNOX_ON
     * @throws IOException
     */
    private int getKnoxVoidStatus(final AndroidDevice device, final String key) throws IOException {
        final String output = executor.execute(new AndroidGetPropertyByKeyCommand(device.getId(),
                key));
        return StringUtils.isBlank(output) ? -1 : Integer.parseInt(output.trim());
    }
}
