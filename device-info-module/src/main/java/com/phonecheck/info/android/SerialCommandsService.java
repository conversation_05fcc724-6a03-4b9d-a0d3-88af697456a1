package com.phonecheck.info.android;

import com.phonecheck.command.device.android.info.AndroidSimSerialWithSlotCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.parser.device.android.info.SerialSubInfoParser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@AllArgsConstructor
public class SerialCommandsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SerialCommandsService.class);
    private static final String DATA_NOT_FOUND = "Parceldatanotfull";
    private final CommandExecutor executor;
    private final SerialSubInfoParser serialSubInfoParser;

    // ==================================================================
    //                       Sub info commands
    // ==================================================================

    /**
     * Executes command on an Android device to retrieve Sim serial information using transaction code and slot number
     *
     * @param deviceId        The unique identifier of the Android device.
     * @param transactionCode The command argument to execute.
     * @param slotNumber      slot number
     * @return The Serial value if successfully retrieved, or null if an error occurs.
     * @throws IOException If an I/O error occurs during command execution.
     */
    public String getSerialWithITelephony(final String deviceId, final String transactionCode, final String slotNumber)
            throws IOException {
        final String output =
                executor.execute(new AndroidSimSerialWithSlotCommand(deviceId, transactionCode, slotNumber));
        LOGGER.info("Serial via ITelephony command output: {}", output);
        if (StringUtils.isNotBlank(output)) {
            String parsedSerial = serialSubInfoParser.parse(output);
            if (StringUtils.isNotBlank(parsedSerial) &&
                    !StringUtils.contains(parsedSerial, DATA_NOT_FOUND)) {
                return parsedSerial.replaceAll("\\D", "");
            }
        }
        return null;
    }
}