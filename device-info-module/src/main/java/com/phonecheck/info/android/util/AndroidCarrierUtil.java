package com.phonecheck.info.android.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class AndroidCarrierUtil {
    private static final String US_CELLULAR_MARKER = "uscellular";
    private static final String AIO_MARKER = "aio";
    private static final String T_MOBILE_MARKER = "tmrevc";
    private static final String T_MOBILE_CARRIER_PATTERN = "([t,T]*)+([-,\\s]*)+[M,m]+[o,O]+[b,B]+[i,I]+[l,L]+[e,E]";
    private static final String ATANDT_CARRIER_PATTERN =
            "([a,A][t,T][&,\\s]*[t,T])|([a,A][t,T]\\s?[a,A][n,N][d,D]\\s?[t,T])";
    private static final String SPRINT_CARRIER_PATTERN = "[s,S]+[p,P]+[r,R]+[i,I]+[n,N]+[t,T]";
    private static final String VERIZON_CARRIER_PATTERN = "[V,v]+[e,E]+[r,R]+[i,I]+[z,Z]+[o,O]+[n,N]";
    private static final String UNLOCK_CARRIER_PATTERN = "[u,U]+[n,N]+[l,L]+[o,O]+[c,C]+[k,K]+[e,E]+[d,D]";

    /**
     * Resolve raw carrier string
     *
     * @param propertyValue carrier property
     * @return raw carrier
     */
    public String resolveRawCarrier(final String propertyValue) {
        if (StringUtils.isNotBlank(propertyValue)) {
            if (propertyValue.contains(US_CELLULAR_MARKER)) {
                return "US Cellular";
            } else if (propertyValue.contains(AIO_MARKER)) {
                return "Cricket";
            } else {
                return propertyValue.replace("android", "").replace("-", "").
                        replace("us", "").replace("att", "at&t");
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * Get user readable carrier name from rawCarrier string
     *
     * @param rawCarrier raw carrier
     * @return carrier
     */
    public String getFormattedCarrier(final String rawCarrier) {
        if (StringUtils.containsIgnoreCase(rawCarrier, T_MOBILE_MARKER)) {
            return "T-Mobile";
        }

        Pattern carrierPattern = Pattern.compile(T_MOBILE_CARRIER_PATTERN);
        Matcher matcher = carrierPattern.matcher(rawCarrier);
        if (matcher.find()) {
            return "T-Mobile";
        }

        carrierPattern = Pattern.compile(ATANDT_CARRIER_PATTERN);
        matcher = carrierPattern.matcher(rawCarrier);
        if (matcher.find()) {
            return "AT&T";
        }

        carrierPattern = Pattern.compile(SPRINT_CARRIER_PATTERN);
        matcher = carrierPattern.matcher(rawCarrier);
        if (matcher.find()) {
            return "Sprint";
        }

        carrierPattern = Pattern.compile(VERIZON_CARRIER_PATTERN);
        matcher = carrierPattern.matcher(rawCarrier);
        if (matcher.find()) {
            return "Verizon";
        }

        carrierPattern = Pattern.compile(UNLOCK_CARRIER_PATTERN);
        matcher = carrierPattern.matcher(rawCarrier);
        if (matcher.find()) {
            return "Unlocked";
        }
        return rawCarrier;
    }
}