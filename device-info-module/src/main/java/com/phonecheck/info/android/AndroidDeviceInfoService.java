package com.phonecheck.info.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.command.CommandOutput;
import com.phonecheck.command.device.android.erase.AndroidSdCardDetectCommand;
import com.phonecheck.command.device.android.info.*;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.android.*;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.constants.android.AndroidAppPackageConstants;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DiskSize;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.android.erase.AndroidSdCardDetectParser;
import com.phonecheck.parser.device.android.info.*;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.phonecheck.model.constants.android.AndroidPathConstants.PATH_FOR_DEVICE_INFO_FILE;

@Service
@AllArgsConstructor
public class AndroidDeviceInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDeviceInfoService.class);

    public static final String AT_UN_KEY = "UN";
    public static final String SD_CARD_PRESENT_MARKER = "op3+DN1mazUsYQK3L9CKLUucNTDg2Bvc3KrR4NlN";
    public static final String SIM_CARD_PRESENT_MARKER = "U4TqffGE42ZINj/sDi+6+hBw43L1xvah4Y9Gof0aXv7klWeQ4qztv11SWQ==";
    private static final String TELEPHONY_FEATURE = "feature:android.hardware.telephony";
    private static final String ACTIVATION_OUTPUT_FOR_PINLOCK = "NOT_ALLOWED_SLO";
    private static final List<String> SU_NOT_ROOTED_STATUS = Arrays.asList("denied", "permission denied",
            "inaccessible", "not found");
    private static final String MAGISK_KEY = "magisk";
    private static final String MAGISK_NOT_RECOGNIZED_KEY = "not recognized";

    private static final String INITIALIZE_SWAD_OUTPUT = "CHANGE TO DDEXE";

    private static final String ACTIVATE_1_OUTPUT = "+ACTIVATE:0,OK";

    private static final String ENABLE_SWAD_OUTPUT = "CHANGE TO ATD";


    private final ObjectMapper mapper;
    private final CommandExecutor executor;
    private final CommandOutput commandOutput;
    private final AndroidGetDiskSizeParser androidGetDiskSizeParser;
    private final AndroidRAMSizeParser androidRAMSizeParser;
    private final AndroidGetPropertiesParser androidGetPropertiesParser;
    private final AndroidAtGetPropertiesParser androidAtGetPropertiesParser;
    private final AndroidSdCardDetectParser androidSdCardDetectParser;
    private final AndroidFileSystemService androidFileSystemService;
    private final TimerLoggerUtil timerLoggerUtil;
    private final InMemoryStore inMemoryStore;

    /**
     * Retrieves device property by key from the android device
     *
     * @param device target android device
     * @param key    property to retrieve
     * @return string
     * @throws IOException when the output couldn't be read
     */
    public String getPropertyByKey(final AndroidDevice device,
                                   final AndroidProperty key) throws IOException {
        final String output = executor.execute(new AndroidGetPropertyByKeyCommand(device.getId(), key.getName()));

        if (StringUtils.isBlank(output)) {
            return null;
        } else if (AndroidAdbCommandErrorMsg.getErrorMsgList().stream().anyMatch(output::contains)) {
            LOGGER.warn("Android device property ({}) retrieval failed with error: {}", key, output);
            return null;
        }
        return output.trim().replaceAll("(\\s)*,(\\s)*$", "");
    }

    /**
     * Retrieves device properties from the android device
     *
     * @param device     target android device
     * @param properties properties to retrieve
     * @return specified properties and their values as a map
     * @throws IOException when the output couldn't be read
     */
    public Map<AndroidProperty, String> getProperties(final AndroidDevice device,
                                                      final AndroidProperty... properties) throws IOException {
        LOGGER.info("Getting properties for android device");
        final String output = executor.execute(new AndroidGetPropertiesCommand(device.getId()));
        if (output != null) {
            return androidGetPropertiesParser.parse(output, properties);
        } else {
            return null;
        }
    }

    /**
     * Format raw device firmware properly
     *
     * @param device      target device
     * @param rawFirmware raw firmware string
     * @return formatted firmware
     */
    public String formatRawFirmware(final AndroidDevice device,
                                    final String rawFirmware) {
        String firmware = "";
        if (StringUtils.isNotBlank(rawFirmware)) {
            firmware = rawFirmware.replace("*", "");
            if (StringUtils.containsIgnoreCase(device.getMake(), "samsung")) {
                if (firmware.contains("PPR1.180610.011.")) {
                    firmware = firmware.replace("PPR1.180610.011.", "");
                }
            }
        }

        return firmware;
    }

    /**
     * Get Disk size of the android device
     *
     * @param device target device
     * @return disk Size object
     */
    public DiskSize getDiskSize(final AndroidDevice device) {
        try {
            LOGGER.info("Getting disk size for android device from storage size command");
            final String storageSizeOutput = executor.execute(new AndroidGetStorageSizeCommand(device.getId()));
            if (StringUtils.isNotBlank(storageSizeOutput)) {
                DiskSize diskSize = androidGetDiskSizeParser.parseStorageSizeOutput(storageSizeOutput);
                if (diskSize != null) {
                    return diskSize;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error: fetching android disk size from storage size command has failed", e);
        }
        try {
            LOGGER.info("Getting disk size for android device from disk stats command");
            final String diskStatsOutput = executor.execute(new AndroidGetDiskStatsCommand(device.getId()));
            if (StringUtils.isNotBlank(diskStatsOutput)) {
                return androidGetDiskSizeParser.parseDiskStatsOutput(diskStatsOutput);
            }
        } catch (IOException e) {
            LOGGER.error("Error: fetching android disk size from disk stats command has failed", e);
        }
        LOGGER.info("Returning null from disk stats command.");
        return null;
    }

    /**
     * Get device RAM
     *
     * @param device target device
     * @return RAM string
     * @throws IOException in case of failure
     */
    public String getDeviceRAM(final AndroidDevice device) throws IOException {
        LOGGER.info("Getting RAM size for android device");
        final String output = executor.execute(new AndroidRAMSizeCommand(device.getId()));
        if (output != null) {
            return androidRAMSizeParser.parse(output);
        } else {
            return null;
        }
    }

    /**
     * This method is used to check if device support sim card or wifi only
     *
     * @param device target device
     * @return TRUE if device support sim card FALSE otherwise
     */
    public boolean isDeviceSimCardSupported(final AndroidDevice device) {
        try {
            LOGGER.info("Checking sim card support for android device");
            final String output = executor.execute(new AndroidGetTelephonyFeaturesCommand(device.getId()));
            return StringUtils.isNotBlank(output) && StringUtils.containsIgnoreCase(output, TELEPHONY_FEATURE);
        } catch (IOException e) {
            LOGGER.error("Error while checking sim card support", e);
        }
        return false;
    }

    /**
     * Get AT properties for the android device
     *
     * @param portName target device port name
     * @return map of props
     * @throws IOException in case of failure
     */
    public Map<String, String> getAtDeviceInfo(final String portName) throws IOException {
        String output = executor.execute(new AndroidAtDeviceInfoCommand(portName));
        if (StringUtils.isNotBlank(output)) {
            int start = output.indexOf(":") + 1;
            int end = output.lastIndexOf("\"");
            if (start > 0 && end > start) {
                output = output.substring(start, end).replace("\"", "").trim();
            }

            return androidAtGetPropertiesParser.parse(output);
        } else {
            return null;
        }
    }

    /**
     * Get installed packages on the device
     *
     * @param deviceId device id
     * @return list string of packages
     * @throws IOException in case of failure
     */
    public String getThirdPartyInstalledPackages(final String deviceId) throws IOException {
        return executor.execute(new AndroidGetThirdPartyPackagesCommand(deviceId));
    }

    /**
     * Get device abi list
     *
     * @param deviceId device id
     * @return list
     * @throws IOException in case of failure
     */
    public String getAbiList(final String deviceId) throws IOException {
        return executor.execute(new AndroidGetAbiListCommand(deviceId));
    }

    /**
     * Get device rooted status by executing command
     *
     * @param device target device
     * @return RootedStatus
     */
    public RootedStatus getRootedStatus(final AndroidDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get Rooted Status adb command execution start", exeStartTime);

        final String output = executor.execute(new AndroidRootedStatusCommand(device.getId()));

        timerLoggerUtil.printTimerLog(device.getId(),
                "Get Rooted Status adb command execution end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        LOGGER.info("Response of getRootedStatus: {}", output);

        return StringUtils.isNotBlank(output) ? RootedStatus.ROOTED : RootedStatus.NOT_ROOTED;
    }

    /**
     * Get device rooted status by executing su command
     *
     * @param device target device
     * @return RootedStatus
     */
    public RootedStatus getSuRootedStatus(final AndroidDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get Su Rooted Status adb command execution start", exeStartTime);
        final String output = executor.execute(
                new AndroidGetSuRootedStatusCommand(device.getId(), false), 5);
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get Su Rooted Status adb command execution end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        LOGGER.info("Response of getSuRootedStatus: {}", output);
        if (StringUtils.isBlank(output)) {
            return RootedStatus.ROOTED;
        }
        final String trimmedOutput = output.trim();
        if (SU_NOT_ROOTED_STATUS.stream().anyMatch(status -> StringUtils.containsIgnoreCase(trimmedOutput, status))) {
            return RootedStatus.NOT_ROOTED;
        }
        return RootedStatus.ROOTED;
    }

    /**
     * Get device rooted status by executing Magisk command
     *
     * @param device target device
     * @return RootedStatus
     */
    public RootedStatus getMagiskRootedStatus(final AndroidDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get Magisk Rooted Status adb command execution start", exeStartTime);
        final String output = executor.execute(
                new AndroidGetMagiskRootStatusCommand(device.getId()));
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get Magisk Rooted Status adb command execution end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        LOGGER.info("Response of getMagiskRootedStatus: {}", output);
        if (StringUtils.isNotBlank(output)) {
            return StringUtils.containsIgnoreCase(output, MAGISK_KEY) &&
                    !StringUtils.containsIgnoreCase(output, MAGISK_NOT_RECOGNIZED_KEY) ?
                    RootedStatus.ROOTED : RootedStatus.NOT_ROOTED;
        } else {
            return RootedStatus.NOT_ROOTED;
        }
    }

    /**
     * Method to load device info retrieved by PC utility
     *
     * @param device target device
     * @return PcUtilityDeviceInfo
     * @throws IOException error occurred while loading device info
     */
    public PcUtilityDeviceInfo getPcUtilityDeviceInfo(final AndroidDevice device) throws IOException {
        final String deviceInfoContent =
                androidFileSystemService.readFileContentFromAndroidDevice(device, PATH_FOR_DEVICE_INFO_FILE);

        LOGGER.info("PC Utility DeviceInfo.json Content: {}", deviceInfoContent);
        return mapper.readValue(deviceInfoContent, PcUtilityDeviceInfo.class);
    }

    /**
     * Retrieves the SD card storage path if it is present
     *
     * @param device target device
     * @return sd card present status
     */
    public boolean isSdCardCheckEnabledAndSdCardDetected(final AndroidDevice device) {
        CloudCustomizationResponse.WorkflowSettings workflow = inMemoryStore.getAssignedCloudCustomization()
                .getWorkflow();
        if (workflow != null && workflow.isWarningMessagesEnabled()) {
            if (workflow.getWarningMessages() != null
                    && workflow.getWarningMessages().isSdCardCheckEnabled()) {
                try {
                    if (AndroidConnectionMode.ADB.equals(device.getAndroidConnectionMode())) {
                        return StringUtils.isNotBlank(getAdbSdCardStoragePathIfPresent(device));
                    } else {
                        return checkAtSdCardStoragePathIfPresent(device);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error while fetching SD card path for the device.", e);
                }
            }
        }

        return false;
    }

    /**
     * Retrieves the SD card storage path if it is present
     *
     * @param device target device
     * @return path of the SD card storage
     * @throws IOException in case of failure
     */
    public String getAdbSdCardStoragePathIfPresent(final AndroidDevice device) throws IOException {
        final String output = executor.execute(new AndroidSdCardDetectCommand(device.getId()));
        if (StringUtils.isNotBlank(output)) {
            LOGGER.info("Output of android ADB SD card path cmd: {}", output);
            return androidSdCardDetectParser.parse(output);
        }
        return null;
    }

    /**
     * Retrieves the SD card storage path if it is present
     *
     * @param device target device
     * @return path of the SD card storage
     * @throws IOException in case of failure
     */
    public boolean checkAtSdCardStoragePathIfPresent(final AndroidDevice device) throws IOException {
        executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.DISABLE_SWADT.getCmd())));
        executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.ACTIVATION.getCmd())));
        executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.ENABLE_SWADT.getCmd())));
        String output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.SD_CARD_DETECTION.getCmd())));
        if (StringUtils.isNotBlank(output)) {
            executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                    commandOutput.dt(AtCommand.DISABLE_SWADT.getCmd())));
            LOGGER.info("Output of android AT SD card path cmd: {}", output);
            return output.contains(commandOutput.dt(SD_CARD_PRESENT_MARKER));
        }
        return false;
    }

    /**
     * Method to check if sim card is inserted in the device
     *
     * @param device target device
     * @return boolean true if sim-card found otherwise false
     */
    public boolean isSimCardCheckEnabledAndSimDetected(final AndroidDevice device) {
        CloudCustomizationResponse.WorkflowSettings workflow = inMemoryStore.getAssignedCloudCustomization()
                .getWorkflow();
        if (workflow != null && workflow.isWarningMessagesEnabled()) {
            if (workflow.getWarningMessages() != null
                    && workflow.getWarningMessages().isSimCardCheckEnabled()) {
                try {
                    if (AndroidConnectionMode.ADB.equals(device.getAndroidConnectionMode())) {
                        String simState = getPropertyByKey(device, AndroidProperty.SIM_STATE);
                        LOGGER.info("Android Sim state is: {}", simState);

                        boolean absent = Arrays.stream(simState.split(","))
                                .map(String::trim)
                                .allMatch(state -> StringUtils.equalsIgnoreCase(state, "ABSENT")
                                        || StringUtils.equalsIgnoreCase(state, "NOT_READY"));
                        return !absent;
                    } else {
                        return checkAtSimCardPresent(device);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error while fetching Sim status for the device.", e);
                }
            }
        }
        return false;
    }

    /**
     * Retrieves the Sim card present status
     *
     * @param device target device
     * @return path of the SD card storage
     * @throws IOException in case of failure
     */
    public boolean checkAtSimCardPresent(final AndroidDevice device) throws IOException {
        executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.DISABLE_SWADT.getCmd())));
        executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.ACTIVATION.getCmd())));
        executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.ENABLE_SWADT.getCmd())));
        String output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                commandOutput.dt(AtCommand.SIM_CARD_DETECTION.getCmd())));
        if (StringUtils.isNotBlank(output)) {
            executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                    commandOutput.dt(AtCommand.DISABLE_SWADT.getCmd())));
            LOGGER.info("Output of android AT Sim card path cmd: {}", output);
            return output.contains(commandOutput.dt(SIM_CARD_PRESENT_MARKER));
        }
        return false;
    }

    /**
     * Method to check if device is network locked via getprop
     *
     * @param device target device
     * @return boolean true if sim-card found otherwise false
     */
    public boolean isDeviceNetworkLocked(final AndroidDevice device) {
        try {
            String simState = getPropertyByKey(device, AndroidProperty.SIM_STATE);
            LOGGER.info("Android Sim state for device network lock detection: {}", simState);
            return StringUtils.containsIgnoreCase(simState, "locked") ||
                    StringUtils.containsIgnoreCase(simState, "restrict");
        } catch (Exception e) {
            LOGGER.error("Error while fetching device network lock status for the device.", e);
        }
        return false;
    }

    /**
     * Method to check if sim card is present in device activated or unactivated
     *
     * @param device target device
     * @return boolean if sim card is present or not
     */
    public boolean isSimCardPresent(final AndroidDevice device) {
        try {
            String simState = getPropertyByKey(device, AndroidProperty.SIM_STATE);
            LOGGER.info("Android Sim state for sim card present detection: {}", simState);
            if (StringUtils.isNotBlank(simState)) {
                String[] simStates = simState.split(",");
                for (String state : simStates) {
                    if (!StringUtils.containsIgnoreCase(state.trim(), "ABSENT")) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while fetching sim present status for the device.", e);
        }
        return false;
    }

    /**
     * Method to check if device is eSim supported or not
     *
     * @param device target device
     * @return boolean
     */
    public boolean isESimSupported(final AndroidDevice device) {
        boolean isESimSupported;

        try {
            String output = executor.execute(new AndroidESimStatusFromEuiccCommand(device.getId()));
            output = StringUtils.trim(output);
            LOGGER.info("Output of android eSimSupport cmd from euicc: {}", output);
            if (StringUtils.containsIgnoreCase(output, "true")
                    || StringUtils.containsIgnoreCase(output, ": enabled")) {
                isESimSupported = true;
            } else if (StringUtils.containsIgnoreCase(output, "false")
                    || StringUtils.containsIgnoreCase(output, ": not enabled")) {
                isESimSupported = false;
            } else {
                output = executor.execute(new AndroidESimStatusFromSettingsCommand(device.getId()));
                output = StringUtils.trim(output);
                LOGGER.info("Output of android eSimSupport cmd from global settings: {}", output);
                if (StringUtils.isNotBlank(output)
                        && !StringUtils.equals(output, "null")
                        && !StringUtils.contains(output, "error")) {
                    isESimSupported = true;
                } else {
                    output = executor.execute(new AndroidESimStatusFromSystemPropCommand(device.getId()));
                    output = StringUtils.trim(output);
                    LOGGER.info("Output of android eSimSupport cmd from system props: {}", output);
                    if (StringUtils.isNotBlank(output)
                            && !StringUtils.equals(output, "null")
                            && !StringUtils.contains(output, "error")) {
                        isESimSupported = true;
                    } else {
                        output = executor.execute(new AndroidESimStatusFromEuiccPackageCommand(device.getId()));
                        output = StringUtils.trim(output);
                        LOGGER.info("Output of android eSimSupport cmd from euicc packages list: {}", output);
                        isESimSupported = StringUtils.isNotBlank(output)
                                && !StringUtils.equals(output, "null")
                                && !StringUtils.contains(output, "error");
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while fetching eSim support status for the device.", e);

            isESimSupported = false;
        }

        return isESimSupported;
    }

    /**
     * Retrieves the number of SIM slots from an {@link AndroidDevice} by querying its SIM state property.
     *
     * @param device target device
     * @return Integer value of sim count
     */
    public int getSimSlotsCount(final AndroidDevice device) {
        try {
            String simState = getPropertyByKey(device, AndroidProperty.SIM_STATE);
            LOGGER.info("Output of android sim-count cmd: {}", simState);
            if (StringUtils.isNotBlank(simState)) {
                return simState.split(",").length;
            } else {
                return 0;
            }
        } catch (Exception e) {
            LOGGER.error("Error while fetching sim count for the device.", e);
        }
        return 0;
    }

    /**
     * Checks if the provided IMEI is valid.
     *
     * @param imei The IMEI to validate.
     * @return true if the IMEI is valid, otherwise false.
     */
    public boolean isValidIMEI(final String imei) {
        return StringUtils.isNotEmpty(imei) &&
                !imei.contains("00000000") &&
                imei.length() >= 14 &&
                imei.matches("\\d+");
    }

    /**
     * Method to get count for present sims in device activated or unactivated
     *
     * @param device target device
     * @return int Present sims count
     */
    public int getPresentSimsCount(final AndroidDevice device) {
        int simCount = 0;
        try {
            String simState = getPropertyByKey(device, AndroidProperty.SIM_STATE);
            LOGGER.info("Output of android present-sim-status cmd: {}", simState);
            if (StringUtils.isNotBlank(simState)) {
                String[] simStates = simState.split(",");
                for (String state : simStates) {
                    if (!StringUtils.containsIgnoreCase(state.trim(), "ABSENT")) {
                        simCount++;
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while fetching present sims count for the device.", e);
        }
        return simCount;
    }


    /**
     * Method to check if android device is pin locked or no
     *
     * @param device target device
     * @return boolean representing the result
     */
    public boolean isDevicePinLocked(final AndroidDevice device) {
        try {
            executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                    commandOutput.dt(AtCommand.DISABLE_SWADT.getCmd())));
            String output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                    commandOutput.dt(AtCommand.ACTIVATION.getCmd())));

            if (StringUtils.isNotBlank(output) && StringUtils.containsIgnoreCase(output,
                    ACTIVATION_OUTPUT_FOR_PINLOCK)) {
                LOGGER.info("Found pin-lock to be true post activation command");

                return true;
            } else {
                output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                        commandOutput.dt(AtCommand.INITIALIZE_SWADT.getCmd())));

                if (StringUtils.isNotBlank(output) && StringUtils.containsIgnoreCase(output,
                        INITIALIZE_SWAD_OUTPUT)) {

                    LOGGER.info("Found the search text in the output of INITIALIZE SWADT command");

                    output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                            commandOutput.dt(AtCommand.ACTIVATION_1.getCmd())));


                    if (StringUtils.isNotBlank(output) && StringUtils.containsIgnoreCase(output,
                            ACTIVATE_1_OUTPUT)) {

                        LOGGER.info("Found the search text in the output of ACTIVATION 1 command");

                        output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                                commandOutput.dt(AtCommand.ENABLE_SWADT.getCmd())));

                        if (StringUtils.isNotBlank(output) && StringUtils.containsIgnoreCase(output,
                                ENABLE_SWAD_OUTPUT)) {
                            LOGGER.info("Found the search text in the output of ENABLE SWAD command");

                            output = executor.execute(new AndroidAtExecuteCommand(device.getPortName(),
                                    commandOutput.dt(AtCommand.PROD_CODE.getCmd())));

                            return StringUtils.isBlank(output) || StringUtils
                                    .containsIgnoreCase(output, "ERROR");
                        }
                    }
                }
            }
        } catch (Exception ee) {
            LOGGER.error("Error while checking pin lock status for the device", ee);
        }
        return false;
    }

    /**
     * Checks if PC utility has admin rights
     *
     * @param device target device
     * @return true if PC Utility has admin permissions
     */
    public boolean isAppAdminForXiaomi(final AndroidDevice device) {
        try {
            final String output = executor.execute(new AndroidGetXiaomiAppAdminCommand(device.getId()));
            LOGGER.info("Output of app admin command: {}", output);
            return StringUtils.containsIgnoreCase(output, AndroidAppPackageConstants.PC_UTILITY_PACKAGE_LITE);
        } catch (Exception e) {
            LOGGER.error("Error while checking PC Utility app is admin for the Xiaomi device to erase", e);
        }
        return false;
    }

    /**
     * Checks if device has stereo speakers
     *
     * @param device target device
     * @return true if device has stereo speakers
     */
    public boolean isDeviceStereoSupported(final AndroidDevice device) {
        try {
            final String output = executor.execute(new AndroidCheckStereoSupportCommand(device.getId()));
            LOGGER.info("Output of device stereo check command: {}", output);
            return StringUtils.containsIgnoreCase(output, "true");
        } catch (Exception e) {
            LOGGER.error("Error while checking if device supports stereo speakers.", e);
        }
        return false;
    }
}
