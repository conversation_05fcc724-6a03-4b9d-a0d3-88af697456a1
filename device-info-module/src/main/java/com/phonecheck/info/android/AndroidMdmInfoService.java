package com.phonecheck.info.android;

import com.phonecheck.command.device.android.info.*;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.parser.device.android.info.AndroidGetMdmPackageParser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@AllArgsConstructor
public class AndroidMdmInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidMdmInfoService.class);
    private static final String TEST_DPC_PACKAGE_NAME = "com.afwsamples.testdpc";
    private static final String MDM_DEVICE_POLICY_RECEIVER = "MDMDeviceAdminReciever";
    private static final String NOTIFICATION_ZERO_TOUCH = "NOTIFICATION_ZERO_TOUCH";
    private static final String ZERO_TOUCH_NOTIFICATIONS = "Zero-touch notifications";
    private static final String ZERO_TOUCH = "ZeroTouch";
    private static final String ZERO_TOUCH_FACTORY_ACTIVITY = ".zerotouch.FactoryResetActivity";

    private final CommandExecutor executor;
    private final AndroidGetMdmPackageParser getMdmPackageParser;

    /**
     * Retrieves the MDM status by executing ADB commands on the specified Android device.
     *
     * @param deviceId The Android device id for which to retrieve the MDM status.
     * @return The MDM status of the device.
     */
    public MdmStatus getMdmStatus(final String deviceId) {
        try {
            // Get mdm package from device policy command, prop, notification and intent
            // return OFF if ON not found from any step
            if (MdmStatus.ON.equals(getMdmPackageFromDevicePolicy(deviceId)) ||
                    MdmStatus.ON.equals(getMdmPackageFromSettings(deviceId)) ||
                    // TODO: Below condition returns true/false/blank which is not a good option to detect
                    //  MDM we can remove it after testing getMdmPackageFromSettings with client
//                    MdmStatus.ON.equals(getMdmPackageFromProp(deviceId)) ||
                    MdmStatus.ON.equals(getMdmPackageFromNotification(deviceId)) ||
                    MdmStatus.ON.equals(getMdmPackageFromIntent(deviceId))) {
                return MdmStatus.ON;
            }
        } catch (IOException e) {
            LOGGER.error("Error while trying to fetch Mdm for android device", e);
        }

        return MdmStatus.OFF;
    }

    /**
     * Retrieves the MDM package from the device policy on the specified Android device.
     *
     * @param deviceId The Android device id from which to retrieve the device policy.
     * @return The MDM package extracted from the device policy.
     * @throws IOException If an I/O error occurs while executing commands on the device.
     */
    private MdmStatus getMdmPackageFromDevicePolicy(final String deviceId) throws IOException {
        MdmStatus mdmStatus = MdmStatus.OFF;
        final String output = executor.execute(new AndroidGetDevicePolicyCommand(deviceId), 20);
        LOGGER.info("Android Device Policy : {}", output);
        if (StringUtils.isNotBlank(output)) {
            if (output.contains(MDM_DEVICE_POLICY_RECEIVER)) {
                LOGGER.info("MDM status found ON from device policy through MDM_DEVICE_POLICY_RECEIVER");
                return MdmStatus.ON;
            }
            // Parse mdm package from device policy
            String mdmPackage = getMdmPackageParser.parse(output);
            if (StringUtils.isNotBlank(mdmPackage)) {
                // Get installed app packages from connected device
                String packageList = getDeviceInstalledPackages(deviceId);
                if (StringUtils.isNotBlank(packageList)) {
                    // Verify MDM package is not test DPC package, and return ON/OFF
                    mdmStatus = verifyMdmPackageNotTestDpc(mdmPackage, packageList);
                }
            }
        }
        return mdmStatus;
    }

    /**
     * Retrieves the list of installed packages on the specified Android device.
     *
     * @param deviceId The Android device for which to retrieve the installed packages.
     * @return A string containing the list of installed packages.
     * @throws IOException If an I/O error occurs while executing commands on the device.
     */
    private String getDeviceInstalledPackages(final String deviceId) throws IOException {
        String output = executor.execute(new AndroidGetInstalledPackagesCommand(deviceId, 1), 20);
        LOGGER.info("Android device package list : {}", output);
        if (StringUtils.isBlank(output) || StringUtils.containsIgnoreCase("not found", output)) {
            output = executor.execute(new AndroidGetInstalledPackagesCommand(deviceId, 2));
            LOGGER.info("Android device package list from second command : {}", output);
            return output;
        }
        return output;
    }

    /**
     * Verifies that the specified MDM package is not a test DPC
     *
     * @param foundMdmPackage          The MDM package to verify.
     * @param deviceInstalledPackageList The list of installed packages on the device.
     * @return The status of the MDM package (ON if found and not a test DPC, OFF otherwise).
     */
    private MdmStatus verifyMdmPackageNotTestDpc(final String foundMdmPackage,
                                                 final String deviceInstalledPackageList) {
        String[] lines = deviceInstalledPackageList.split(System.lineSeparator());
        for (String installedPackageName : lines) {
            if (StringUtils.contains(installedPackageName, foundMdmPackage) &&
                    !StringUtils.contains(installedPackageName, TEST_DPC_PACKAGE_NAME)) {
                LOGGER.info("Found MDM package not test dpc package, status ON");
                return MdmStatus.ON;
            }
        }
        return MdmStatus.OFF;
    }

    /**
     * Retrieves the MdmStatus from the settings of an Android device.
     *
     * @param deviceId The AndroidDevice id
     * @return The MdmStatus
     * @throws IOException exception
     */
    private MdmStatus getMdmPackageFromSettings(final String deviceId) throws IOException {
        final String output = executor.execute(new AndroidGetMdmFromSettingsCommand(deviceId), 20);
        LOGGER.info("Android get Mdm from settings output: {}", output);
        if (StringUtils.isBlank(output) ||
                output.replaceAll("[\r\n]", StringUtils.EMPTY).contains("null") ||
                output.contains(TEST_DPC_PACKAGE_NAME)) {
            LOGGER.info("Found android MDM status OFF from Settings command");
            return MdmStatus.OFF;
        } else {
            LOGGER.info("Found android MDM status ON from Settings command");
            return MdmStatus.ON;
        }
    }

    /**
     * Retrieves the MdmStatus from the properties of an Android device.
     *
     * @param deviceId The AndroidDevice id
     * @return The MdmStatus
     * @throws IOException exception
     */
    private MdmStatus getMdmPackageFromProp(final String deviceId) throws IOException {
        final String output = executor.execute(new AndroidGetMdmFromPropCommand(deviceId), 20);
        LOGGER.info("Android get Mdm from prop output: {}", output);
        if (StringUtils.isNotBlank(output) &&
                output.replaceAll("[\r\n]", StringUtils.EMPTY).contains("true")) {
            LOGGER.info("Found android MDM status ON from prop command");
            return MdmStatus.ON;
        } else {
            LOGGER.info("Found android MDM status OFF from prop command");
            return MdmStatus.OFF;
        }
    }

    /**
     * Retrieves the MdmStatus from notifications on an Android device.
     *
     * @param deviceId android device id
     * @return status
     * @throws IOException exception
     */
    private MdmStatus getMdmPackageFromNotification(final String deviceId) throws IOException {
        final String output = executor.execute(new AndroidGetMdmFromNotificationCommand(deviceId),
                20);
        LOGGER.info("Android get Mdm from notification output : {}", output);
        if (StringUtils.isNotBlank(output) && (output.contains(NOTIFICATION_ZERO_TOUCH) ||
                output.contains(ZERO_TOUCH_NOTIFICATIONS) || output.contains(ZERO_TOUCH))) {
            LOGGER.info("Found android MDM status ON from notification command");
            return MdmStatus.ON;
        }
        LOGGER.info("Found android MDM status OFF from notification command");
        return MdmStatus.OFF;
    }

    /**
     * Retrieves the MdmStatus from intent on an Android device.
     *
     * @param deviceId android device id
     * @return status
     * @throws IOException exception
     */
    private MdmStatus getMdmPackageFromIntent(final String deviceId) throws IOException {
        final String output = executor.execute(new AndroidGetMdmFromIntentCommand(deviceId),
                20);
        LOGGER.info("Android get Mdm from intent output : {}", output);
        if (StringUtils.isNotBlank(output) && output.contains(ZERO_TOUCH_FACTORY_ACTIVITY)) {
            LOGGER.info("Found android MDM status ON from activity intent command");
            return MdmStatus.ON;
        }
        LOGGER.info("Found android MDM status OFF from activity intent command");
        return MdmStatus.OFF;
    }

}
