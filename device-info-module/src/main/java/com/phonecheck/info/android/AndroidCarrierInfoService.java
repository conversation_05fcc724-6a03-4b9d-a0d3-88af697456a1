package com.phonecheck.info.android;

import com.phonecheck.dao.model.TblCarrierFilter;
import com.phonecheck.dao.service.lookup.CarrierFilterDBLookupService;
import com.phonecheck.info.android.util.AndroidCarrierUtil;
import com.phonecheck.model.android.AndroidProperty;
import com.phonecheck.model.device.AndroidDevice;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
@AllArgsConstructor
public class AndroidCarrierInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidCarrierInfoService.class);
    private static final String PACKAGE_VERIZON = "com.vzw.hss.myverizon";
    private static final String PACKAGE_ATT = "com.att.myWireless";
    private static final String PACKAGE_T_MOBILE = "com.tmobile.pr.mytmobile";
    private static final String NOT_FOUND_MARKER = "not found";

    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final CarrierFilterDBLookupService carrierFilterDBLookupService;
    private final AndroidCarrierUtil androidCarrierUtil;


    /**
     * Populate Carrier info in the device
     *
     * @param device target device
     * @return carrier
     */
    public String getDeviceCarrierInfo(final AndroidDevice device) {
        // Attempt info retrieval from ADB
        String carrier = getCarrierFromAdb(device);

        // As a fallback, check the packages installed on the device for carrier specific apps
        if (StringUtils.isBlank(carrier)) {
            try {
                String packageListStr = androidDeviceInfoService.getThirdPartyInstalledPackages(device.getId());
                if (packageListStr.contains(PACKAGE_ATT)) {
                    carrier = "AT&T";
                } else if (packageListStr.contains(PACKAGE_VERIZON)) {
                    carrier = "Verizon";
                } else if (packageListStr.contains(PACKAGE_T_MOBILE)) {
                    carrier = "T-Mobile";
                }
            } catch (IOException e) {
                LOGGER.error("Error while getting packages installed on device", e);
            }
        }

        // Get filtered carrier from Database
        if (StringUtils.isNotBlank(carrier)) {
            String filterCarrier = getFilterCarrierFromDb(carrier);
            if (StringUtils.isNotBlank(filterCarrier)) {
                carrier = filterCarrier;
            }
        }

        LOGGER.info("Carrier found: {}", carrier);
        return carrier;
    }

    /**
     * Get carrier info from ADB
     *
     * @param device target device
     * @return carrier from adb
     */
    public String getCarrierFromAdb(final AndroidDevice device) {
        if (!androidDeviceInfoService.isDeviceSimCardSupported(device)) {
            // Just for future logs in case to verify device carrier
            String deviceCarrier = getCarrierFromProperties(device, List.of(AndroidProperty.CARRIER_RO));
            LOGGER.info("Carrier from device is: {}", deviceCarrier);
            return "Wifi Only";
        }

        return getCarrierFromProperties(device, List.of(
                AndroidProperty.CARRIER_MS, AndroidProperty.CARRIER_ID, AndroidProperty.CARRIER_AM,
                AndroidProperty.CARRIER_ALPHA, AndroidProperty.CARRIER_RO
        ));
    }

    /**
     * Get carrier from command with property list
     *
     * @param device           the target device
     * @param listOfProperties that used to fetch carrier with adb command
     * @return formatted carrier or empty string if no command works
     */
    private String getCarrierFromProperties(final AndroidDevice device, final List<AndroidProperty> listOfProperties) {
        for (AndroidProperty androidProperty : listOfProperties) {
            try {
                String propertyValue = androidDeviceInfoService.getPropertyByKey(device, androidProperty);
                if (StringUtils.isNotBlank(propertyValue) && !StringUtils.contains(propertyValue, NOT_FOUND_MARKER)) {
                    String rawCarrier = androidCarrierUtil.resolveRawCarrier(propertyValue);
                    if (StringUtils.isNotBlank(rawCarrier)) {
                        if (StringUtils.isNotBlank(device.getMake()) &&
                                StringUtils.containsIgnoreCase(rawCarrier, device.getMake())) {
                            return androidCarrierUtil.getFormattedCarrier("");
                        } else {
                            return androidCarrierUtil.getFormattedCarrier(rawCarrier);
                        }
                    }
                }

            } catch (final IOException e) {
                LOGGER.error("Error getting property value of key : {}", androidProperty.getName(), e);
            }
        }

        return StringUtils.EMPTY;
    }

    /**
     * Filters the carrier using a database lookup
     *
     * @param carrier The carrier to be filtered
     * @return The resolved carrier from the database
     */
    public String getFilterCarrierFromDb(final String carrier) {
        TblCarrierFilter tblCarrierFilter = carrierFilterDBLookupService.getCarrierFilter(carrier);
        if (tblCarrierFilter != null && StringUtils.isNotEmpty(tblCarrierFilter.getResolvedCarrier())) {
            return tblCarrierFilter.getResolvedCarrier();
        }
        return StringUtils.EMPTY;
    }

}
