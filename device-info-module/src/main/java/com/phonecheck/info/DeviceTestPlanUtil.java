package com.phonecheck.info;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

import static com.phonecheck.model.constants.FileConstants.*;

@Component
@AllArgsConstructor
public class DeviceTestPlanUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceTestPlanUtil.class);
    private static final String FACE_ID_KEY = "Face ID";
    private static final String CHARGING_KEY = "Wired Charging";
    private static final String SIM_READER1_KEY = "Sim Reader 1";
    private static final String SIM_READER2_KEY = "Sim Reader 2";
    private static final String SIM_REMOVE_KEY = "Sim Remove Key";
    private static final String SIM_READER_KEY = "Sim Reader Key";
    private static final String SIM_READER_TEST_KEY = "Sim Reader Test";
    private static final String NETWORK_CONNECTIVITY_KEY = "Network Connectivity";
    private static final String NETWORK_CONNECTIVITY1_KEY = "Network Connectivity 1";
    private static final String NETWORK_CONNECTIVITY2_KEY = "Network Connectivity 2";

    private FileUtil fileUtil;
    private SupportFilePath supportFilePath;

    /**
     * Checks if the "Face ID" key exists in the test plan file.
     *
     * @param device device
     * @return status
     */
    public boolean isFaceIdKeyExistInTestPlan(final Device device) {
        try {
            final File deviceTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + IOS_FILES_FOLDER + File.separator +
                    device.getSerial() + File.separator + TEST_PLAN_FILE_NAME);

            String testPlanContent = readDeviceTestPlanFromFile(device, deviceTestPlanFile.getPath());
            return StringUtils.isNotBlank(testPlanContent) && testPlanContent.contains(FACE_ID_KEY);
        } catch (IOException e) {
            LOGGER.error("Error while checking FaceId key exists in Device-Test-Plan file", e);
        }
        return false;
    }

    /**
     * Removes the "Face ID" key from the test plan file content if it exists.
     * Handles the removal of any associated commas to maintain proper formatting.
     *
     * @param device device
     */
    public void removeFaceIdKeyFromDeviceTestPlan(final Device device) {
        try {
            final File deviceTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + IOS_FILES_FOLDER + File.separator +
                    device.getSerial() + File.separator + TEST_PLAN_FILE_NAME);

            String deviceTestPlanContent = readDeviceTestPlanFromFile(device, deviceTestPlanFile.getPath());
            if (StringUtils.isNotBlank(deviceTestPlanContent)) {
                deviceTestPlanContent = deviceTestPlanContent
                        .replace("," + FACE_ID_KEY, StringUtils.EMPTY)
                        .replace(FACE_ID_KEY + ",", StringUtils.EMPTY)
                        .replace(FACE_ID_KEY, StringUtils.EMPTY)
                        .replaceAll("^,+|,+$", "");

                fileUtil.writeStringToFile(deviceTestPlanFile, deviceTestPlanContent);
            }
        } catch (IOException e) {
            LOGGER.error("Error while removing face id key from device test plan file", e);
        }
    }

    /**
     * Reads the device test plan from the specified file path. If the file does not exist,
     * it creates the file and copies the device test plan content into it.
     *
     * @param device                 device
     * @param deviceTestPlanFilePath The path to the device test plan file.
     * @return The content of the device test plan file as a String.
     * @throws IOException If an I/O error occurs while reading or creating the file.
     */
    private String readDeviceTestPlanFromFile(final Device device, final String deviceTestPlanFilePath)
            throws IOException {
        LOGGER.info("Getting device test plan for the device");

        createAndCopyDeviceTestPlanFile(device);

        // Read and return content from file
        return fileUtil.readFile(new File(deviceTestPlanFilePath));
    }

    /**
     * Creates a device-specific test plan file and copies the content from the main test plan file.
     * If the file does not exist, it will be created, and the content of the main test plan will
     * be written into the newly created file.
     *
     * @param device                 target device
     */
    public void createAndCopyDeviceTestPlanFile(final Device device) {
        try {
            LOGGER.info("Creating TestList.json for device.");
            final File deviceTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + IOS_FILES_FOLDER + File.separator +
                    device.getSerial() + File.separator + TEST_PLAN_FILE_NAME);


            final String deviceTypeFolder = DeviceType.ANDROID.equals(device.getDeviceType()) ?
                    ANDROID_FILES_FOLDER : IOS_FILES_FOLDER;

            final File mainTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + deviceTypeFolder + File.separator + TEST_PLAN_FILE_NAME);

            String mainTestPlanFileContent = fileUtil.readFile(fileUtil.getFileFromPath(mainTestPlanFile.getPath()));
            fileUtil.writeStringToFile(deviceTestPlanFile, mainTestPlanFileContent);

        } catch (IOException e) {
            LOGGER.error("Failed to create device TestList.json", e);
        }
    }

    /**
     * Sets face id in test plan for IOS devices
     *
     * @param device IOS device for which to update the test plan
     */
    public void updateFaceIdKeyInTestPlan(final IosDevice device) {
        boolean isFaceIdKeyExist = isFaceIdKeyExistInTestPlan(device);
        if (!isFaceIdKeyExist) {
            LOGGER.info("Face id key does not exist in test plan, skipping FaceId detection " +
                    "from mobile device and performed from Desktop");
            return;
        }

        if (device.getOsMajorVersion() >= 15) {
            device.setFaceIdSupported(Boolean.FALSE);
            LOGGER.info("Device OS Version >= 15, Face ID detection will be performed on mobile device");
        } else {
            LOGGER.info("Device OS Version < 15, Face id detection will be performed on Desktop");
            // Remove FaceId key from test plan as FaceId is going to perform from Desktop
            removeFaceIdKeyFromDeviceTestPlan(device);
        }
    }

    /**
     * Removes unused call test keys related to SIM readers and network connectivity
     * from the device's test plan file.
     *
     * @param device device.
     */
    public void removeCallTestKeyFromDeviceTestPlan(final Device device) {
        try {
            final File deviceTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + IOS_FILES_FOLDER + File.separator +
                    device.getSerial() + File.separator + TEST_PLAN_FILE_NAME);

            String deviceTestPlanContent = readDeviceTestPlanFromFile(device, deviceTestPlanFile.getPath());
            if (StringUtils.isNotBlank(deviceTestPlanContent)) {
                deviceTestPlanContent = deviceTestPlanContent
                        .replace("," + SIM_READER1_KEY, StringUtils.EMPTY)
                        .replace(SIM_READER2_KEY + ",", StringUtils.EMPTY)
                        .replace(SIM_REMOVE_KEY, StringUtils.EMPTY)
                        .replace(SIM_READER_KEY, StringUtils.EMPTY)
                        .replace(SIM_READER_TEST_KEY, StringUtils.EMPTY)
                        .replace(NETWORK_CONNECTIVITY_KEY, StringUtils.EMPTY)
                        .replace(NETWORK_CONNECTIVITY1_KEY, StringUtils.EMPTY)
                        .replace(NETWORK_CONNECTIVITY2_KEY, StringUtils.EMPTY)
                        .replaceAll("^,+|,+$", "");

                fileUtil.writeStringToFile(deviceTestPlanFile, deviceTestPlanContent);
            }
        } catch (IOException e) {
            LOGGER.error("Error while removing face id key from device test plan file", e);
        }
    }

    /**
     * Checks if the "Battery Charging" key exists in the test plan file.
     *
     * @param device device
     * @return status
     */
    public boolean isChargingKeyExistInTestPlan(final Device device) {
        try {
            File deviceTestPlanFile = null;
            if (device instanceof IosDevice) {
                deviceTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                        File.separator + IOS_FILES_FOLDER + File.separator +
                        device.getSerial() + File.separator + TEST_PLAN_FILE_NAME);
            } else if (device instanceof AndroidDevice) {
                deviceTestPlanFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                        File.separator + ANDROID_FILES_FOLDER + File.separator + TEST_PLAN_FILE_NAME);
            }
            if (deviceTestPlanFile != null && deviceTestPlanFile.exists()) {
                String testPlanContent = readDeviceTestPlanFromFile(device, deviceTestPlanFile.getPath());
                return !StringUtils.isNotBlank(testPlanContent) || !testPlanContent.contains(CHARGING_KEY);
            }
        } catch (IOException e) {
            LOGGER.error("Error while checking battery charging key exists in Device-Test-Plan file", e);
        }
        return true;
    }

}
