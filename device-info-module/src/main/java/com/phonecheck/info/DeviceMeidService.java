package com.phonecheck.info;

import com.phonecheck.model.meid.MeidInputType;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.regex.Pattern;

@Service
@NoArgsConstructor
public class DeviceMeidService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceMeidService.class);
    private static final String MEID_DEC_PATTERN = "\\d{18}";
    private static final String MEID_HEX_PATTERN = "[a-fA-F\\d]{14}";
    private static final String ESN_DEC_PATTERN = "\\d{11}";
    private static final String ESN_HEX_PATTERN = "[a-fA-F\\d]{9}";
    private static final String NO_VALUE = " -- ";

    /**
     * Method to check for device input type by checking its pattern
     *
     * @param inputString imei input
     * @return returns MeidInputType
     */
    private MeidInputType detectInputType(final String inputString) {
        if (Pattern.matches(MEID_DEC_PATTERN, inputString)) {
            return MeidInputType.MEID_DEC;
        } else if (Pattern.matches(MEID_HEX_PATTERN, inputString)) {
            return MeidInputType.MEID_HEX;
        } else if (Pattern.matches(ESN_DEC_PATTERN, inputString)) {
            return MeidInputType.ESN_DEC;
        } else if (Pattern.matches(ESN_HEX_PATTERN, inputString)) {
            return MeidInputType.ESN_HEX;
        } else {
            return MeidInputType.NONE;
        }
    }

    /**
     * Returns MEID in decimal format.
     *
     * @param inputString the provided input value.
     * @return the MEID in decimal format.
     */
    public String getMeidDec(final String inputString) {
        MeidInputType detectedType = detectInputType(inputString);
        return switch (detectedType) {
            case MEID_DEC -> inputString;
            case MEID_HEX -> convertBase(inputString, 16, 10, 8, 10, 8);
            default -> NO_VALUE;
        };
    }

    /**
     * Returns MEID in hexadecimal format.
     *
     * @param inputString the provided input value.
     * @return the MEID in hexadecimal format.
     */
    public String getMeidHex(final String inputString) {
        MeidInputType detectedType = detectInputType(inputString);
        return switch (detectedType) {
            case MEID_HEX -> inputString;
            case MEID_DEC -> convertBase(inputString, 10, 16, 10, 8, 6);
            default -> NO_VALUE;
        };
    }

    /**
     * Returns ESN in hexadecimal format.
     *
     * @param inputString the provided input value.
     * @return the ESN in hexadecimal format.
     */
    public String getEsnHex(final String inputString) {
        MeidInputType detectedType = detectInputType(inputString);
        return switch (detectedType) {
            case ESN_HEX -> inputString;
            case ESN_DEC -> convertBase(inputString, 10, 16, 3, 2, 6);
            case MEID_DEC, MEID_HEX -> calculatePESN(inputString, detectedType.getFormat());
            default -> NO_VALUE;
        };
    }

    /**
     * Returns ESN in decimal format.
     *
     * @param inputString the provided input value.
     * @return the ESN in decimal format.
     */
    public String getEsnDec(final String inputString) {
        MeidInputType detectedType = detectInputType(inputString);
        return switch (detectedType) {
            case ESN_DEC -> inputString;
            case ESN_HEX -> convertBase(inputString, 16, 10, 2, 3, 8);
            case MEID_DEC, MEID_HEX ->
                    convertBase(calculatePESN(inputString, detectedType.getFormat()), 16, 10, 2, 3, 8);
            default -> NO_VALUE;
        };
    }

    /**
     * Calculates Pseudo ESN (pESN) using SHA-1 hashing.
     *
     * @param inputString the input string.
     * @param format      the format of the input (HEX/DEC).
     * @return the pESN value.
     */
    private String calculatePESN(final String inputString, final MeidInputType.Format format) {
        String input = (format == MeidInputType.Format.DEC) ? getMeidHex(inputString) : inputString;
        int[] p = new int[7];
        for (int i = 0; i < 7; i++) {
            p[i] = Integer.parseInt(input.substring(i * 2, i * 2 + 2), 16);
        }
        String calc = new String(p, 0, 7);
        try {
            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            sha1.update(calc.getBytes(StandardCharsets.ISO_8859_1));
            byte[] sha1hash = sha1.digest();
            String hash = convertBytesToHex(sha1hash);
            return ("80" + hash.substring(hash.length() - 6)).toUpperCase();
        } catch (Exception e) {
            LOGGER.error("Exception occurred while calculating Pseudo ESN value.");

            return "ERROR";
        }
    }

    /**
     * Converts a number from one base to another with padding options.
     *
     * @param inputString The input number to convert.
     * @param srcBase     The source base of the input number.
     * @param dstBase     The destination base for conversion.
     * @param p1Width     The width of the first part of the transformed string.
     * @param p1Padding   The padding for the first part.
     * @param p2Padding   The padding for the second part.
     * @return The converted number in uppercase.
     */
    private String convertBase(final String inputString, final int srcBase,
                               final int dstBase, final int p1Width,
                               final int p1Padding, final int p2Padding) {
        // Extract and convert the first part.
        String p1 = convertAndPad(inputString.substring(0, p1Width), srcBase, dstBase, p1Padding);

        // Extract and convert the second part.
        String p2 = convertAndPad(inputString.substring(p1Width), srcBase, dstBase, p2Padding);

        // Combine the two parts and convert to uppercase.
        return (p1 + p2).toUpperCase();
    }

    /**
     * Converts a number from one base to another and pads it to the specified length.
     *
     * @param number     The number to convert.
     * @param srcBase    The source base of the number.
     * @param dstBase    The destination base for conversion.
     * @param paddingLen The desired length of the padded result.
     * @return The converted and padded number.
     */
    private String convertAndPad(final String number, final int srcBase,
                                 final int dstBase, final int paddingLen) {
        String converted = Long.toString(Long.parseLong(number, srcBase), dstBase);
        return leftPad(converted, paddingLen, "0");
    }

    /**
     * Left-pads a string with a given character to the specified length.
     *
     * @param input       The input string.
     * @param len         The desired length of the result.
     * @param paddingChar The character used for padding.
     * @return The left-padded string.
     */
    private String leftPad(final String input, final int len, final String paddingChar) {
        StringBuilder padded = new StringBuilder(input);
        while (padded.length() < len) {
            padded.insert(0, paddingChar);
        }
        return padded.toString();
    }

    /**
     * Converts bytes to a hexadecimal string.
     *
     * @param data the byte array.
     * @return the hexadecimal string.
     */
    private String convertBytesToHex(final byte[] data) {
        StringBuilder builder = new StringBuilder();
        for (byte dataByte : data) {
            builder.append(String.format("%02x", dataByte));
        }
        return builder.toString();
    }
}