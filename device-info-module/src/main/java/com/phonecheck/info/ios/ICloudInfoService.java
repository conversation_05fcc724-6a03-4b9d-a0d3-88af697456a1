package com.phonecheck.info.ios;

import com.phonecheck.command.device.ios.info.IosGetActivationInfoCommand;
import com.phonecheck.command.device.ios.info.IosGetAppleIdCommand;
import com.phonecheck.command.device.ios.info.IosGetFindMyIphoneStatusCommand;
import com.phonecheck.command.device.ios.info.IosGetIcloudStatusCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.ios.ProcessName;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.ios.info.IosActivationInfoParser;
import com.phonecheck.parser.device.ios.info.IosAppleIdInfoParser;
import com.phonecheck.parser.device.ios.info.IosFindMyIphoneStatusParser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;

@Service
@AllArgsConstructor
public class ICloudInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ICloudInfoService.class);
    private static final int RETRIES_TO_GET_ICLOUD = 5;

    private final CommandExecutor executor;
    private final IosActivationInfoParser iosActivationInfoParser;
    private final IosFindMyIphoneStatusParser iosFindMyIphoneStatusParser;
    private final IosAppleIdInfoParser iosAppleIdInfoParser;
    private final IosDeviceInfoService iosDeviceInfoService;

    private final TimerLoggerUtil timerLoggerUtil;

    /**
     * Retrieves iCloud status (on/off) for the target device
     *
     * @param device        target device
     * @return iCloud status
     * @throws IOException in case of failure
     */
    public DeviceLock getIcloudStatus(final IosDevice device) throws IOException {

        // try to get icloud status by activation info
        if (getIcloudStatusByActivation(device)) {
            LOGGER.info("Got Icloud status ON by activation");
            return DeviceLock.ON;
        }

        // try to get icloud status by checking find my iphone status
        if (getFindMyIphoneStatus(device)) {
            LOGGER.info("Got Icloud status ON by Find my iphone status");
            return DeviceLock.ON;
        }

        // try to get icloud status by getting icloud appleId
        if (StringUtils.isNotBlank(getIcloudAppleId(device))) {
            LOGGER.info("Got Icloud status ON by apple ID");
            return DeviceLock.ON;
        }

        // try to get icloud status by killing SharingD
        if (SetupDoneStatus.DONE.equals(device.getSetupDoneStatus())) {
            if (getIcloudStatusByKillingSharingD(device)) {
                LOGGER.info("Got Icloud status ON by from syslog after killing sharingd");
                return DeviceLock.ON;
            }
        }

        // Try to get iCloud from idevice-info command
        if (getIcloudFromInfoCommand(device)) {
            LOGGER.info("Got Icloud status ON from ideviceinfo");
            return DeviceLock.ON;
        }

        LOGGER.info("Icloud status determined OFF");
        return DeviceLock.OFF;
    }


    /**
     * Retrieves iCloud status by calling activation command for target device
     *
     * @param device target device
     * @return icloud status
     * @throws IOException in case of command failure
     */
    private Boolean getIcloudStatusByActivation(final IosDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get ICloud by activation exe (ideviceactivation) start", exeStartTime);
        String output = executor.execute(new IosGetActivationInfoCommand(device.getId()), 30);
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get ICloud by activation exe (ideviceactivation) end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        return iosActivationInfoParser.parse(output);
    }


    /**
     * Retrieves find my iphone status for the target device
     *
     * @param device target device
     * @return find my iphone status
     * @throws IOException in case of command failure
     */
    private Boolean getFindMyIphoneStatus(final IosDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get find my phone exe (ideviceinfo) start", exeStartTime);
        String output = executor.execute(new IosGetFindMyIphoneStatusCommand(device.getId()));
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get find my phone exe (ideviceinfo) end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        return iosFindMyIphoneStatusParser.parse(output);

    }

    /**
     * Retrieves appleId for iCloud from the target device
     *
     * @param device target device
     * @return appleId
     * @throws IOException in case of command failure
     */
    private String getIcloudAppleId(final IosDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get ICloud apple id exe (ideviceAppleid) start", exeStartTime);
        String output = executor.execute(new IosGetAppleIdCommand(device.getId()));
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get ICloud apple id exe (ideviceAppleid) end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        return iosAppleIdInfoParser.parse(output);
    }

    /**
     * Kills shareD process
     *
     * @param device target device
     * @return icloud status
     */
    private boolean getIcloudStatusByKillingSharingD(final IosDevice device) {
        int killShareDProcessCounter = 0;
        boolean icloudStatus = false;
        do {
            try {
                // Get the process id for sharingD
                Map<ProcessName, String> processNameIdMap =
                        iosDeviceInfoService.getDeviceProcessIds(device, ProcessName.SHARINGD);
                // Kill the processes
                if (processNameIdMap != null && processNameIdMap.containsKey(ProcessName.SHARINGD)) {
                    iosDeviceInfoService.killProcess(device, processNameIdMap.get(ProcessName.SHARINGD));
                    LOGGER.info("Killed sharingD process from the device.");
                }
            } catch (IOException e) {
                LOGGER.error("Exception occurred while killing sharingD process from the device", e);
            }
            // Wait for 4 seconds after killing sharingD process to get icloud status from syslog
            try {
                Thread.sleep(4000);
            } catch (InterruptedException e) {
                // do nothing
            }
            if (DeviceLock.ON.equals(device.getDeviceLock())) {
                icloudStatus = true;
                break;
            } else if (DeviceLock.OFF.equals(device.getDeviceLock())) {
                break;
            }

            killShareDProcessCounter++;
        } while (killShareDProcessCounter < RETRIES_TO_GET_ICLOUD);

        return icloudStatus;
    }

    /**
     * Retrieves appleId for iCloud from the target device using idevice-info
     *
     * @param device target device
     * @return icloud status
     * @throws IOException in case of command failure
     */
    private boolean getIcloudFromInfoCommand(final IosDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get ICloud idevice-info exe (ideviceAppleid) start", exeStartTime);
        String output = executor.execute(new IosGetIcloudStatusCommand(device.getId()));
        timerLoggerUtil.printTimerLog(device.getId(),
                "Get ICloud idevice-info exe (ideviceAppleid) end", exeStartTime);
        timerLoggerUtil.printTimerLog(device.getId(), output, exeStartTime);
        return StringUtils.containsIgnoreCase(output, "iCloud");
    }
}
