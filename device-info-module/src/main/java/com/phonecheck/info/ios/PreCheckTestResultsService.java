package com.phonecheck.info.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.command.device.ios.precheck.PreCheckDeviceInfoCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.PreCheckInfo;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.TestResults;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.phonecheck.model.precheck.PreCheckTestConstants.*;

@Service
@AllArgsConstructor
public class PreCheckTestResultsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreCheckTestResultsService.class);
    private final CommandExecutor executor;
    private final ObjectMapper objectMapper;

    /**
     * Retrieves a list of pending tests from the given semi-auto test results.
     *
     * @param semiResults the semi-auto test results
     * @return a list of test names with a status of "Pending"
     */
    public List<String> getSemiPendingTests(final PreCheckInfo.SemiAutoResults semiResults) {
        Map<String, String> resultMap = objectMapper.convertValue(semiResults, Map.class);
        // Filter pending tests
        return resultMap.entrySet().stream()
                .filter(entry -> "Pending".equalsIgnoreCase(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * Starts the PreCheck info test process for the specified device with the given tag.
     * This method executes a {@link PreCheckDeviceInfoCommand} to initiate the PreCheck
     * info test process using the provided device ID and tag.
     *
     * @param deviceId the ID of the device on which the PreCheck info test should be started
     * @param tag      the parameter used to fetch the PreCheck info test
     */
    public void startInfoTestProcess(final String deviceId, final String tag) {
        try {
            final String output =
                    executor.execute(new PreCheckDeviceInfoCommand(deviceId, tag, "1"), 10);
            LOGGER.info("PreCheck deviceInfo test command output: {}", output);
        } catch (Exception e) {
            LOGGER.error("Error executing PreCheckDeviceInfoCommand", e);
        }
    }

    /**
     * Stops the running PreCheck info test process for the specified device.
     * This method executes a {@link PreCheckDeviceInfoCommand} to stop the running PreCheck
     * info test process using the device ID.
     *
     * @param deviceId target device
     * @param tag tag need to be stopped
     */
    public void stopInfoTestProcess(final String deviceId, final String tag) {
        try {
            LOGGER.info("Trying to stop PreCheck info test process with parameter: {}", tag);
            final String output = executor.execute(
                    new PreCheckDeviceInfoCommand(deviceId, tag, "0"), 10);
            LOGGER.info("PreCheck deviceInfo test stopping command output: {}", output);
        } catch (Exception e) {
            LOGGER.error("Error executing PreCheckDeviceInfoCommand", e);
        }
    }

    /**
     * Maps auto test results from the pre-check info to the device test results.
     *
     * @param deviceInTracker The iOS device whose test results need to be updated.
     */
    public void mapAutoResultsToDeviceTestResults(final IosDevice deviceInTracker) {
        final PreCheckInfo preCheckInfo = deviceInTracker.getPreCheckInfo();
        if (preCheckInfo == null) {
            LOGGER.info("PreCheck info is empty while mapping pre-check auto results");
            return;
        }

        PreCheckInfo.AutoResults autoResults = preCheckInfo.getAutoResults();
        final Map<String, String> resultMap = new LinkedHashMap<>();

        resultMap.put(P_BLUETOOTH, autoResults.getBluetooth());
        resultMap.put(P_WIFI, autoResults.getWifi());
        resultMap.put(P_WIRELESS_CHARGING, autoResults.getWirelessCharger());
        resultMap.put(P_NFC, autoResults.getNfc());
        resultMap.put(P_FINGERPRINT_SENSOR, autoResults.getFingerprint());
        resultMap.put(P_PANIC, autoResults.getPanic());
        resultMap.put(P_PROXIMITY_SENSOR, autoResults.getProximity());
        resultMap.put(P_MIC_RECORDING, autoResults.getMic());
        resultMap.put(P_GYROSCOPE, autoResults.getGyroSensor());
        resultMap.put(P_ACCELEROMETER, autoResults.getAccelerometer());
        resultMap.put(P_ALS_SENSOR, autoResults.getAlsSensor());
        resultMap.put(P_COMPASS, autoResults.getCompass());
        resultMap.put(P_FRONT_CAMERA, autoResults.getFrontCamera());
        resultMap.put(P_FRONT_IR_CAMERA, autoResults.getFrontIRCamera());
        resultMap.put(P_FRONT_DOT_PROJECTOR, autoResults.getFrontDotProjector());
        resultMap.put(P_BACK_CAMERA, autoResults.getBackCamera());
        resultMap.put(P_BACK_CAMERA_TELE, autoResults.getBackCameraTele());
        resultMap.put(P_BACK_CAMERA_WIDE, autoResults.getBackCameraWide());
        resultMap.put(P_BUTTON_SENSOR, autoResults.getButtonSensor());

        setupTestResultFromInfoMap(deviceInTracker, resultMap);
    }

    /**
     * Maps semi-auto test results from the pre-check info to the device test results.
     *
     * @param deviceInTracker The iOS device whose test results need to be updated.
     */
    public void mapSemiResultsToDeviceTestResults(final IosDevice deviceInTracker) {
        final PreCheckInfo preCheckInfo = deviceInTracker.getPreCheckInfo();
        if (preCheckInfo == null) {
            LOGGER.info("PreCheck info is empty while mapping pre-check semi results");
            return;
        }

        PreCheckInfo.SemiAutoResults semiResults = preCheckInfo.getSemiAutoResults();
        final Map<String, String> resultMap = new LinkedHashMap<>();

        putIfNotNull(resultMap, P_HOME_BUTTON, semiResults.getHomeButton());
        putIfNotNull(resultMap, P_POWER_BUTTON, semiResults.getPowerButton());
        putIfNotNull(resultMap, P_PROXIMITY_TEST, semiResults.getProximityTest());
        putIfNotNull(resultMap, P_RINGER_OFF, semiResults.getRingerOff());
        putIfNotNull(resultMap, P_RINGER_ON, semiResults.getRingerOn());
        putIfNotNull(resultMap, P_TOUCH_SCREEN, semiResults.getTouchScreen());
        putIfNotNull(resultMap, P_VOLUME_DOWN, semiResults.getVolumeDown());
        putIfNotNull(resultMap, P_VOLUME_UP, semiResults.getVolumeUp());

        setupTestResultFromInfoMap(deviceInTracker, resultMap);
    }

    /**
     * Maps device info test results from the pre-check info to the device test results.
     *
     * @param deviceInTracker The iOS device whose test results need to be updated.
     */
    public void mapInfoResultsToDeviceTestResults(final IosDevice deviceInTracker) {
        final PreCheckInfo preCheckInfo = deviceInTracker.getPreCheckInfo();
        if (preCheckInfo == null) {
            LOGGER.info("PreCheck info is empty while mapping pre-check info results");
            return;
        }

        PreCheckInfo.DeviceInfoResults infoResults = preCheckInfo.getDeviceInfoResults();
        final Map<String, String> resultMap = new LinkedHashMap<>();
        putIfNotNull(resultMap, P_LCD_COLOR, infoResults.getLcdColor());
        putIfNotNull(resultMap, P_DIGITIZER, infoResults.getDigitizerTest());
        putIfNotNull(resultMap, P_SPEAKER, infoResults.getSpeaker());

        setupTestResultFromInfoMap(deviceInTracker, resultMap);
    }

    /**
     * Updates the device's test result tracker with the provided test result map.
     *
     * @param deviceInTracker The iOS device whose test results need to be updated.
     * @param resultMap       A map containing test result keys and their corresponding statuses.
     */
    private void setupTestResultFromInfoMap(final IosDevice deviceInTracker, final Map<String, String> resultMap) {
        final List<String> passedTests = resultMap.entrySet().stream()
                .filter(entry -> "Pass".equalsIgnoreCase(entry.getValue()))
                .map(Map.Entry::getKey)
                .toList();

        final List<String> failedTests = resultMap.entrySet().stream()
                .filter(entry -> "Fail".equalsIgnoreCase(entry.getValue()))
                .map(Map.Entry::getKey)
                .toList();

        DeviceTestResult trackerDeviceTestResult = deviceInTracker.getDeviceTestResult() != null
                ? deviceInTracker.getDeviceTestResult()
                : new DeviceTestResult();

        TestResults trackerTestResults = trackerDeviceTestResult.getTestResults() != null
                ? trackerDeviceTestResult.getTestResults()
                : new TestResults();

        // Append passed test and remove duplication from tracker device
        List<String> trackerPassedList = trackerTestResults.getPassed();
        passedTests.stream()
                .filter(test -> !trackerPassedList.contains(test))
                .forEach(trackerPassedList::add);

        // Append failed test and remove duplication from tracker device
        List<String> trackerFailedList = trackerTestResults.getFailed();
        failedTests.stream()
                .filter(test -> !trackerFailedList.contains(test))
                .forEach(trackerFailedList::add);

        trackerDeviceTestResult.setTestResults(trackerTestResults);
        deviceInTracker.setDeviceTestResult(trackerDeviceTestResult);
        LOGGER.info("After PreCheck test result received, final device test results are: {}", trackerDeviceTestResult);
    }

    /**
     * Adds a key-value pair to the given map if the value is not null.
     *
     * @param map   The map to update.
     * @param key   The key to insert.
     * @param value The value to insert if not null.
     */
    public void putIfNotNull(final Map<String, String> map, final String key, final String value) {
        Optional.ofNullable(value).ifPresent(v -> map.put(key, v));
    }
}
