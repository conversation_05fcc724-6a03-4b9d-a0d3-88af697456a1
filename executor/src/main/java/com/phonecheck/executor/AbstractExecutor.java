package com.phonecheck.executor;

import com.phonecheck.command.AbstractAndroidCommand;
import com.phonecheck.command.AbstractIosCommand;
import com.phonecheck.command.ICommand;
import com.phonecheck.model.util.MacSupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Scanner;
import java.util.concurrent.*;
import java.util.regex.Pattern;

public abstract class AbstractExecutor {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractExecutor.class);

    private final SupportFilePath supportFilePath;

    public AbstractExecutor(final SupportFilePath supportFilePath) {
        this.supportFilePath = supportFilePath;
    }

    /**
     * Method to return the file object which points to the folder in which we want to
     * execute command
     *
     * @param command command to execute
     * @return folder
     */
    protected File getCommandExecutionFolder(final ICommand command) {
        File processDirectory;

        if (command instanceof AbstractIosCommand) {
            processDirectory = new File(supportFilePath.getPaths().getToolsRootFolderPath(), "ios");
        } else if (command instanceof AbstractAndroidCommand) {
            processDirectory = new File(supportFilePath.getPaths().getToolsRootFolderPath(), "android");
        } else {
            processDirectory = new File(supportFilePath.getPaths().getToolsRootFolderPath());
        }

        return processDirectory;
    }

    /**
     * Method to return formatted command based on operating system
     *
     * @param command target command
     * @return formatted command
     */
    public String[] getFormattedCommand(final ICommand command) {
        String[] cmd;

        if (supportFilePath.getPaths() instanceof MacSupportFilePathsStrategy) {
            cmd = command.getCmd();

            // Check if the command contains "|"
            if (Arrays.asList(cmd).contains("|")) {
                String[] newCmd = new String[3];
                newCmd[0] = "/bin/sh";
                newCmd[1] = "-c";
                newCmd[2] = String.join(" ", cmd);
                cmd = newCmd;
            }
        } else {
            // We need to remove any "./" from the start of first command element
            // and For windows, we need to replace "grep" with "findstr"
            cmd = command.getCmd();
            cmd = Arrays.stream(cmd)
                    .filter(e -> !e.equals("bash") && !e.equals("-E")) // Remove "bash" & "-E" as it mac specific
                    .map(e -> {
                        if (e.equals("grep")) {
                            return "findstr"; // Replace "grep" with "findstr"
                        } else if (e.endsWith(".sh")) {
                            return e.replace(".sh", ".bat"); // Replace ".sh" with ".bat"
                        } else if (e.contains("|") && e.split("\\|").length == 2) {
                            return e.replace("|", " ");
                        } else {
                            return e; // Keep the element as is
                        }
                    }).toArray(String[]::new);

            for (int i = 0; i < cmd.length; i++) {
                if (cmd[i].startsWith("./") || cmd[i].startsWith(".\\")) {
                    cmd[i] = cmd[i].substring(2);
                    String resourcesPath = getCommandExecutionFolder(command).getPath();
                    resourcesPath = resourcesPath.contains("Program Files") ?
                            resourcesPath.replace("Program Files", "progra~1") : resourcesPath;
                    cmd[i] = resourcesPath + "\\" + cmd[i];
                }
            }

            // Check if the command contains "|"
            if (Arrays.asList(cmd).contains("|")) {
                String[] newCmd = new String[cmd.length + 2];
                newCmd[0] = "cmd.exe";
                newCmd[1] = "/c";
                System.arraycopy(cmd, 0, newCmd, 2, cmd.length);
                cmd = newCmd;
            }
        }

        return cmd;
    }

    protected boolean isDeviceFound(final String output) {
        return !(output.contains("No device found with udid ")
                || output.trim().equals("error: closed")
                || Pattern.compile("adb: device " + ".*" + " not found").matcher(output.trim()).matches()
                || Pattern.compile("error: device " + "'.*'" + " not found").matcher(output.trim()).matches());
    }

    // ==================================================================
    //                      Methods to run Commands
    // ==================================================================

    /**
     * Executes a specified  command and waits for its completion.
     *
     * @param command command to execute.
     * @return The output of the executed command as a string.
     * @throws IOException If an I/O error occurs while executing the command.
     */
    protected String runCommand(final ICommand command) throws IOException {
        StringBuilder output = new StringBuilder();
        Process process = getProcess(command);
        try {
            InputStream inputStream = process.getInputStream();
            Scanner scanner = new Scanner(inputStream);
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine();
                output.append(line).append("\n");
            }
            scanner.close();
        } catch (Exception e) {
            LOGGER.error("Process for command {} failed with an exception.", command.getClass().getName(), e);
        } finally {
            killProcess(process);
        }

        return output.toString();
    }

    /**
     * Executes a system command with a specified timeout and returns the command's output as a string.
     *
     * @param command command to execute.
     * @param timeOut maximum time in seconds, to wait for the command to complete.
     * @return output of the command if it completes within the timeout,
     * null if the command is interrupted or encounters an error.
     * @throws IOException If an I/O error occurs while executing the command.
     */
    public String runCommandWithTimeOut(final ICommand command, final long timeOut) throws IOException {
        StringBuilder output = new StringBuilder();
        Process process = getProcess(command);
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Future<String> future = executor.submit(() -> {
            try {
                InputStream inputStream = process.getInputStream();
                Scanner scanner = new Scanner(inputStream);
                while (scanner.hasNextLine()) {
                    String line = scanner.nextLine();
                    output.append(line).append("\n");
                }
                scanner.close();
                return output.toString();
            } finally {
                killProcess(process);
            }
        });
        try {
            return future.get(timeOut, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            LOGGER.warn("Process for command {} terminated due to timeout.", command.getClass().getName());
            return null;
        } catch (InterruptedException | ExecutionException e) {
            LOGGER.error("Error executing the command {}.", command.getClass().getName(), e);
            return null;
        } finally {
            killProcess(process);
            executor.shutdown();
        }
    }

    /**
     * Executes a system command with a specified timeout and returns the command's output as a string.
     *
     * @param command command to execute.
     * @param timeOutInSeconds maximum time in seconds, to wait for the command to complete.
     * @return output of the command if it completes within the timeout or the
     * partial output read within the timeout
     * @throws IOException If an I/O error occurs while executing the command.
     */
    public String runCommandWithTimeOutAndReturnReadOutput(final ICommand command,
                                                           final long timeOutInSeconds)
            throws IOException {
        StringBuilder output = new StringBuilder();
        Process process = getProcess(command);
        ExecutorService executor = Executors.newSingleThreadExecutor();

        Future<Void> future = executor.submit(() -> {
            try (InputStream inputStream = process.getInputStream();
                 Scanner scanner = new Scanner(inputStream)) {
                while (scanner.hasNextLine()) {
                    output.append(scanner.nextLine()).append("\n");
                }
            }
            return null;
        });

        try {
            future.get(timeOutInSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            LOGGER.warn("Process for command {} timed out.", command);
        } catch (InterruptedException | ExecutionException e) {
            LOGGER.error("Error executing the command {}.", command, e);
        } finally {
            killProcess(process);
            executor.shutdown();
        }

        return output.toString();
    }

    /**
     * Executes a specified command in fire and forget manner.
     *
     * @param command command to execute.
     * @throws IOException If an I/O error occurs while executing the command.
     */
    protected void runCommandInBackground(final ICommand command) throws IOException {
        getProcess(command);
    }

    // ==================================================================
    //                   Methods related to Process
    // ==================================================================

    /**
     * Get process using process builder
     *
     * @param command command to execute.
     * @return process
     * @throws IOException exception
     */
    public Process getProcess(final ICommand command) throws IOException {
        ProcessBuilder processBuilder = new ProcessBuilder(getFormattedCommand(command));
        processBuilder.directory(getCommandExecutionFolder(command));
        processBuilder.redirectErrorStream(true);
        return processBuilder.start();
    }

    /**
     * Method to kill dangling process
     *
     * @param process dangling process
     */
    public void killProcess(final Process process) {
        new Thread(() -> {
            if (process.isAlive()) {
                process.destroy();
                if (process.isAlive()) {
                    process.destroyForcibly();
                }
            }
        }).start();
    }
}
