package com.phonecheck.executor;

import com.phonecheck.command.ICommand;
import com.phonecheck.model.util.SupportFilePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class CommandExecutor extends AbstractExecutor {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommandExecutor.class);
    private static final int COMMAND_RETRIES = 5;

    public CommandExecutor(final SupportFilePath supportFilePath) {
        super(supportFilePath);
    }

    /**
     * Executes a specified system command and retries {@link CommandExecutor#COMMAND_RETRIES}
     * times in case of failure.
     *
     * @param command command to execute.
     * @return exe response || null
     * @throws IOException if error occurs while executing the command.
     */
    public String execute(final ICommand command) throws IOException {
        try {
            for (int i = 0; i < COMMAND_RETRIES; i++) {
                String response = runCommand(command);
                if (response != null && isDeviceFound(response)) {
                    return response;
                }
                // wait 500 ms for the next retry
                Thread.sleep(500);
                LOGGER.warn("Command {} execution failed during attempt no: {}", command.getClass().getName(), i + 1);
            }
        } catch (final InterruptedException e) {
            LOGGER.error("Command thread interrupted.", e);
        }
        return null;
    }

    /**
     * Executes a specified system command only once without any retries.
     *
     * @param command command to execute.
     * @return exe response || null
     * @throws IOException if error occurs while executing the command.
     */
    public String executeOnce(final ICommand command) throws IOException {
        String response = runCommand(command);
        if (response != null && isDeviceFound(response)) {
            return response;
        }

        LOGGER.warn("Command {} execution failed", command.getClass().getName());

        return null;
    }

    /**
     * Executes a specified system command with a timeout and retries {@link CommandExecutor#COMMAND_RETRIES}
     * times in case of failure.
     *
     * @param command command to execute.
     * @param timeOut The maximum time, in seconds, to wait for each command execution attempt.
     * @return The response from the command if it succeeds and the expected device is found;
     * null if the command fails after the specified number of retries or is interrupted.
     * @throws IOException If an I/O error occurs while executing the command.
     */
    public String execute(final ICommand command, final long timeOut) throws IOException {
        try {
            for (int i = 0; i < COMMAND_RETRIES; i++) {
                String response = runCommandWithTimeOut(command, timeOut);
                if (response != null && isDeviceFound(response)) {
                    return response;
                }
                // wait 500 ms for the next retry
                Thread.sleep(500);
                LOGGER.warn("Command {} execution failed during attempt no: {}", command.getClass().getName(), i + 1);
            }
        } catch (final InterruptedException e) {
            LOGGER.error("Command thread interrupted.", e);
        }
        return null;
    }

    /**
     * Executes a specified system command with a timeout only once without any retries.
     *
     * @param command command to execute.
     * @param timeOut The maximum time, in seconds, to wait for each command execution attempt.
     * @return The response from the command if it succeeds and the expected device is found;
     * null if the command fails after the specified number of retries or is interrupted.
     * @throws IOException If an I/O error occurs while executing the command.
     */
    public String executeOnce(final ICommand command, final long timeOut) throws IOException {
        String response = runCommandWithTimeOut(command, timeOut);
        if (response != null && isDeviceFound(response)) {
            return response;
        }

        LOGGER.warn("Command {} execution failed", command.getClass().getName());

        return null;
    }

    /**
     * Executes a specified system command in background. It executes a command in fire and forget manner.
     *
     * @param command command to execute.
     * @throws IOException If an I/O error occurs while executing the command.
     */
    public void executeInBackground(final ICommand command) throws IOException {
        runCommandInBackground(command);
    }
}
