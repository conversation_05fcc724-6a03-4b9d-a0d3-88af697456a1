package com.phonecheck.station;

import com.phonecheck.command.system.mac.GetMacHardwareInfoCommand;
import com.phonecheck.command.system.windows.GetWindowsDriveSerialCommand;
import com.phonecheck.command.system.windows.GetWindowsHardwareIdCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.system.mac.GetMacHardwareIdParser;
import com.phonecheck.parser.system.windows.GetWindowsDriveSerialParser;
import com.phonecheck.parser.system.windows.GetWindowsHardwareIdParser;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Component
@AllArgsConstructor
public class HardwareInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(HardwareInfoService.class);
    private final CommandExecutor executor;
    private final GetMacHardwareIdParser macHardwareIdParser;
    private final GetWindowsHardwareIdParser windowsHardwareIdParser;
    private final GetWindowsDriveSerialParser windowsDriveSerialParser;
    private final OsChecker osChecker;

    /**
     * Get the underlying workstation hardware UUID
     *
     * @return hardware uuid / pc_id
     * @throws IOException in case of command failure
     */
    public String getPcId() throws IOException {
        String pcId;
        LOGGER.info("Getting hardware uuid / pc_id");
        if (osChecker.isMac()) {
            final String macCommandOutput = executor.execute(new GetMacHardwareInfoCommand());
            LOGGER.info("Mac hardware info command output : {}", macCommandOutput);
            pcId = macCommandOutput != null
                    ? macHardwareIdParser.parse(macCommandOutput) : null;
        } else {
            String windowsCommandOutput = executor.execute(new GetWindowsHardwareIdCommand());
            LOGGER.info("Windows hardware info command output : {}", windowsCommandOutput);
            pcId = windowsCommandOutput != null
                    ? windowsHardwareIdParser.parse(windowsCommandOutput) : null;

            if (pcId == null) {
                windowsCommandOutput = executor.execute(new GetWindowsDriveSerialCommand());
                LOGGER.info("Windows drive serial command output : {}", windowsCommandOutput);
                pcId = windowsCommandOutput != null
                        ? windowsDriveSerialParser.parse(windowsCommandOutput) : null;
            }
        }

        return pcId;
    }
}
