package com.phonecheck.device.connection.iwatch;

import com.phonecheck.command.device.iwatch.IWatchGetBasicInfoCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.util.DiskSizeUtil;
import com.phonecheck.model.device.IWatchInfo;
import com.phonecheck.parser.device.iwatch.IWatchBasicInfoParser;
import org.apache.commons.lang3.StringUtils;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.unit.DataSize;

import java.io.IOException;

@Service
@AllArgsConstructor
public class IWatchPairService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchPairService.class);

    private final CommandExecutor executor;
    private final IWatchBasicInfoParser iWatchBasicInfoParser;
    private final DiskSizeUtil diskSizeUtil;

    /**
     * Creates for each currently-connected UUID in Mux mode
     * @param hostId host id
     * @return status
     * @throws IOException exception
     */
    public IWatchInfo retrieveIWatchBasicInfo(final String hostId) throws IOException {
        final String output = executor.execute(new IWatchGetBasicInfoCommand(hostId), 30);
        LOGGER.info("IWatch get basic info command output : {}", output);
        if (StringUtils.isNotBlank(output)) {
            return iWatchBasicInfoParser.parseResponseToIWatch(output);
        }
        return null;
    }

    /**
     * Retrieves the disk size as a rounded value in gigabytes.
     *
     * @param totalSize the total disk size as a string representation of bytes
     * @return a string representing the rounded disk size in gigabytes
     */
    public String retrieveDiskSize(final String totalSize) {
        if (StringUtils.isNotBlank(totalSize)) {
            long diskSizeBytes = Long.parseLong(totalSize);
            DataSize dataSize = DataSize.ofBytes(diskSizeBytes);
            return diskSizeUtil.roundDeviceDiskSize(dataSize.toGigabytes()).toString();
        }
        return StringUtils.EMPTY;
    }
}