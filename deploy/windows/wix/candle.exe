MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L 'p�]        � 0  @          ]       `    @                       �     �=  @�                           �\  O    `  �                   �     �[                                                               H           .text   =       @                    `.rsrc   �   `      P              @  @.reloc      �      `              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �\      H     L0  #       hS  �  [  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�1  r�  po  
�1  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po   
o  
 (  +,r�  p	o!  
o  
 (  +,r+ po"  
o  
 (  +,rO po#  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  ($  
o%  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 o       (&  
s'  
}  s'  
}  s(  
}  }	  s'  
}
  s'  
}  }  r� pr� ps)  
}  }  }  *F(*  
s  o  *   0 �    (  {  o+  
,{  o,  

݀  {  -+(-  
&�#&{  (.  
o/  
{  o,  

�M  {  o0  
-	}  +&{  o0  
/{  ,(
  r� ps1  
z{  ,(2  
{  , (  (3  
(4  
{  o,  

��  {  o5  
+o6  
{  (7  
o/  
o8  
-��u  ,o9  
�}  s:  

{  %�/  
s;  
o<  
+o=  
{
  o>  
o?  
&�{
  o0  
2�{	  o@  
sA  
{  %�/  
s;  
oB  
{  oC  
{  oD  
{  oE  
{	  oF  
{  oG  
{  o5  
+o6  
(H  
oI  
oJ  
o8  
-��u  ,o9  
�(K  
sL  
{  o5  
8z  o6  
		
{  ,"	~  oM  
�i3�
�
(N  

(O  

-E{  ,
{  +S{
  ,{
  
r� p(P  
(Q  
+0
r� p(P  
+ (R  
-{
  ,{
  (Q  

(3  
{  ,
(S  
oT  
+{  ,{  sU  
oT  
{  oV  
�!oW  
,(S  
oW  
.oW  
oX  
�,Y{  -Q{  -IoY  
,	oZ  
(N  
o[  
-s\  
o]  

o^  
o8  
:z����u  ,o9  
�o_  
+A(`  
(a  
ob  
/)(a  
(c  
{  (d  
(e  
o/  
(f  
-���  o9  
��Z{  og  
o/  
�C{  oh  
oi  
oj  
ok  
(l  
o/  
uG  -	uH  ,�� {  o,  
*	* A�      -      5   #   &     �   )   �             �  +   
            �  @   8  !          7  �  �            �  N   /                 ?  ?     $          ?  V  C   %  0 +    
8  �9  om  
9  om  
3{  r p(n  
oo  
8�  -op  
./op  
@   oq  
r p(r  
,"{  r p(s  
o/  
}  8�  dop  
@�   om  
/=op  
3{  (t  
o/  
*oq  
r] p(u  
oM  

{  	�ov  
,8{  	�	�i.	�+~w  
{  	�ox  
t0  (y  
o/  
*	�i3{  	�~w  
oz  
8�  {  	�	�oz  
8�  ra p(r  
,}  8�  Iop  
3{
  oq  
o?  
&8�  rk p(r  
,8�%
({  
-{  rs p(|  
o/  
*{  �o?  
&8A  r} p(r  
,}  8(  r� p(r  
-
r� p(r  
,V{  �%
(}  
(~  
-8r� po  
-r� po  
,
}
  8�  }  8�  *r� p(r  
,}  8�  r� p(r  
-r� p(r  
9�   r� p(r  
,{  r� pr� p(�  
o/  
�%
({  
-{  ~w  
(�  
o/  
*�r� p(�  
-�r� p(�  
,}	  8	  �r� p(�  
,}	  8�  �r� p(�  
-�r� p(�  
,}	  8�  �r p(�  
,}	  8�  {  �(�  
o/  
8�  pop  
3&oq  
}  om  
�}  8Z  r p(r  
,"{  r p(s  
o/  
}  8+  r p(r  
,"{  r p(s  
o/  
}  8�  r# p(r  
,,{  r# pr/ p(�  
o/  
{  o�  
8�  r/ po�  
9�   oq  
om  
-{  o�  
+8(�  
o�  
(�  
2{  (�  
o/  
{  o�  
�U  &{  (�  
o/  
�<  &{  (�  
o/  
�#  r5 p(r  
,,{  r5 prA p(�  
o/  
{  o�  
8�  rA po�  
9�   oq  
om  
-{  o�  
+8(�  
o�  
(�  
		2{  (�  
o/  
{  	o�  
�|  &{  (�  
o/  
�c  &{  (�  
o/  
�J  rG p(r  
,'{  rG p(s  
o/  
{  o�  
8  rS p(r  
,{  o�  
8�   rW p(r  
-
r[ p(r  
,}  *{  o?  
&8�   @op  
3oq  
(�  
(  8�   
{  ,!~  oM  


�i3
�

�
r p(n  
{  ,P,L�i/ (�  
{  (�  
o/  
+2{  �~  ��O  (  
o?  
&+
{  oo  
�
�i?����* 4    tT�'    tT�(    MT�'    MT�(  F�O  %;��  *(&  
*�~  -re p�  ($  
o�  
s�  
�  ~  *~  *�  *V(
  r� p~  o�  
*V(
  rA p~  o�  
*   BSJB         v2.0.50727     l   �  #~  \	  @  #Strings    �  \  #US �     #GUID       #Blob         W�		   �3      O               �                                         �      ��
 9�
 ��
 7   6z �z 
} "�
 Mz  z �z �z jz ��
 
} �z
 ��
 �} M}
 ��  �� �& B	& q} C
& �	& A   #  
 f
�  �} �& �
 O&o z
   2   Y& \} z %} G}
 �{	 ��
 ��
 2	�
 �G
 \
 	 �} , } �} �} �& I	& N � �& & �& 5} ]} �& & g	& �	}    �[  �	[  �	[  �& &    �} ��
 �& �} �G 
} 	} �& �}    R       � v&=    �`=      �`=  	  aH ,
H iH �H JH �k �k �o �s Fk %k JH H �
w �w �w g w _w _w H X	z �
w �w1 P
~ �� ��P     � �� �!    � �� "    ��
�  T"    ��
  �"    � �� �"    � �� \(    � �& �/    ��
�  �/    ��
  �/    �.	� 0    ��� 0    ��� 0    ��	 30    ���	    �   �   �  W   *   *   *   w	 �
  �
  �

 ) �
 1 �
 9 �
 A �
 I �
 Q �
 Y �
 a �
 i �
 q �
 � �
 I�
 Q�
 Y�
 y�
  � :8 � �< � �8 �B � G
8 � �8 � �8 ��H � �
 � �	M � �	M ��Q Q �8 Y 9
8 1 f8 a  
8 ) n8 ��y � �� y �
 � �
 � �
 � �
 �\� �
� � �M �H� ��
� � �� � M ��
 �	� ��� ��	� � w
� � �
8 ��
� � �� � % � �
 ��
� C� /� t� � � �� �
 � C� `
 � F � 3 � �� �
 � � $� �*� �*�	0 �
A�Z
L	�T	xT	�Y	�Y	� _�3d� Mj�
 � �q� ;z � U�	}  k� �
  � � � � w
�$ �
�, a� M !�, ���r�$ �� !�	�)O8 )��y �8 )48 ����M ��� �&��,���#1��� ��7��>� �C�7H� tK��� � PI�V�� 7I ]�/_��g��o��	��Yw�� ��gi��i
�a* ��� 7�q �0
 �
7�[ �$ �t q-�!������a�
�a��) � �.  �.  �.  �. # �. + . 3 #. ; ). C /. K @. S U. [ �. c ). k �I � �� { �� � �� � �� s �' q � 
    2	�  �  �  �� 
         
    	 v 6����            ��             �v                �}               ��               �               �e           �
       e  X  ]  b  g  l  M      IEnumerable`1 IEqualityComparer`1 List`1 ToInt32 KeyValuePair`2 Dictionary`2 MD5 <Module> System.IO T suppressSchema mscorlib System.Collections.Generic IllegalSuppressWarningId Load Add TypeSpecificationForExtensionRequired IsPathRooted System.Collections.Specialized ValueListKind Replace set_SourceTrace get_StackTrace add_Message get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage AddRange IDisposable Hashtable Candle RuntimeTypeHandle GetTypeFromHandle candle CommandLineResponseFile preprocessFile get_CannotSpecifyMoreThanOneSourceFileForSingleTargetFile outputFile Compile Console wconsole get_Title GetFileName get_ProductName ParseCommandLine WriteLine Combine GetType CompilerCore get_Culture set_Culture resourceCulture get_InvariantCulture get_OrdinalIgnoreCase Close Dispose Parse set_SuppressValidate Create Intermediate EditorBrowsableState MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute get_Value TryGetValue value Save candle.exe ToString GetString Substring IsValidArg DeprecatedCommandLineSwitch GetFullPath get_Length EndsWith StartsWith original System.ComponentModel System.Xml Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item System set_CurrentPlatform platform resourceMan Main AddExtension ChangeExtension WixExtension get_FileVersion allowPerSourceOutputSpecification MultipleFilesMatchedWithOutputSpecification get_Location System.Globalization PrepareConsoleForLocalization System.Reflection StringCollection InvalidVariableDefinition DuplicateVariableDefinition SEHException UnexpectedException NullReferenceException TargetInvocationException FormatException ArgumentException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo showLogo showHelp Char get_LastErrorNumber DisplayToolHeader IFormatProvider get_ResourceManager ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler StringComparer InvalidPlatformParameter StreamWriter TextWriter DisplayToolFooter get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError Preprocessor sourceOutputSeparator StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.CandleStrings.resources DebuggingModes set_ShowPedanticMessages showPedanticMessages set_ShowVerboseMessages sourceFiles GetFiles GetCustomAttributes set_SuppressAllWarnings WixWarnings CandleStrings invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args get_IncludeSearchPaths includeSearchPaths Equals Microsoft.Tools.WindowsInstallerXml.Tools Contains System.Collections get_Chars ReplacePlaceholders parameters WixErrors Process get_Comments Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Split set_SuppressFilesVitalByDefault suppressFilesVitalByDefault set_FipsCompliant fipsCompliant XmlDocument UnsupportedCommandLineArgument UseFipsArgument get_Current get_Count Convert CreateValueList extensionList get_Out get_PreprocessOut set_PreprocessOut preprocessToStdout DuplicateSourcesForOutput MoveNext wix Display ToCharArray get_Key ContainsKey System.Security.Cryptography get_Assembly assembly get_Company GetFileOrDirectory outputDirectory op_Equality IsNullOrEmpty     [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	C N D L  c a n d l e . e x e  	- o u t . w i x o b j  
S o u r c e  Ca l l o w P e r S o u r c e O u t p u t S p e c i f i c a t i o n  =  	f i p s  e x t  	- e x t 
n o l o g o  o  o u t  \  /  p e d a n t i c  p l a t f o r m  	a r c h  i n t e l  x 8 6  x 6 4  i n t e l 6 4  	i a 6 4  a r m  s f d v i t a l  s s  s w a l l  s w  w x a l l  w x  t r a c e  v  ?  	h e l p  oM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . C a n d l e S t r i n g s  kC a n n o t S p e c i f y M o r e T h a n O n e S o u r c e F i l e F o r S i n g l e T a r g e t F i l e  H e l p M e s s a g e     6���� F��ޯZD3�         ��E)-1I   E     
)
-

1
  ���� ��  Ieimquy}����q��q��q����    ��  �� ��   u ��  ��  Q   Y } }  ��
mq
 �       �	 �	 ��U  �	 ����  q      �� ��q  �� ��q   ��    ��  ��  �� ��     ��      	 ] �) �� �)  ��  �- �5  �  A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QUY]���� A	A     �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Compiler  
 Compiler          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******   �  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP��6����    o   N  jC a n n o t S p e c i f y M o r e T h a n O n e S o u r c e F i l e F o r S i n g l e T a r g e t F i l e     H e l p M e s s a g e �   �Cannot specify more than one source file with single output file.  Either specify an output directory for the -out argument by ending the argument with a '\' or remove the -out argument to have the source files compiled to the current directory.�
 usage:  candle.exe [-?] [-nologo] [-out outputFile] sourceFile [sourceFile ...] [@responseFile]

   -arch      set architecture defaults for package, components, etc.
              values: x86, x64, or ia64 (default: x86)
   -d<name>[=<value>]  define a parameter for the preprocessor
   -ext <extension>  extension assembly or "class, assembly"
   -fips      enables FIPS compliant algorithms
   -I<dir>    add to include search path
   -nologo    skip printing candle logo information
   -o[ut]     specify output file (default: write to current directory)
   -p<file>   preprocess to a file (or stdout if no file supplied)
   -pedantic  show pedantic messages
   -platform  (deprecated alias for -arch)
   -sfdvital  suppress marking files as Vital by default (deprecated)
   -ss        suppress schema validation of documents (performance boost) (deprecated)
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1009 -sw1103)
   -swall     suppress all warnings (deprecated)
   -trace     show source trace for errors, warnings, and verbose messages (deprecated)
   -v         verbose output
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1009 -wx1103)
   -wxall     treat all warnings as errors (deprecated)
   -? | -help this help information      -^	<0)ؗ�4��z�m{��%XSZ�	Q�!]�O��������
��V���v)Nh�la���⤖�� ���W��EZ�-!��5�9�*W��=�������H�g#��Qc�QDAYh��w^V    'p�]         �[  �K  RSDSa����H��Pʦ��   C:\agent\_work\66\s\build\obj\ship\x86\candle\candle.pdb                                                                                                                                                                                                            �\          ]                          �\            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              �   P  �                  8  �               	  �                     h  �               	  �  �`  `          `4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   R   F i l e D e s c r i p t i o n     W i X   T o o l s e t   C o m p i l e r     8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   .   I n t e r n a l N a m e   c a n d l e     � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   >   O r i g i n a l F i l e n a m e   c a n d l e . e x e     \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	� d  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Candle" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Compiler</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       P     =                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      