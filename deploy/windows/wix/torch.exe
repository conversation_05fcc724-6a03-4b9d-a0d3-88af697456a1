MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L (p�]        � 0  P          �g       �    @                       �     �Q  @�                           |g  O    �  �                   �     Df                                                               H           .text   �G       P                    `.rsrc   �   �      `              @  @.reloc      �      p              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �g      H     `5  $#       �X  @
  �e  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�,  r�  po  
�,  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po  
o  
 (  +,r�  p	o   
o  
 (  +,r+ po!  
o  
 (  +,rO po"  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  (#  
o$  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 R       (%  
s&  
}  s&  
}	  s&  
}
  r� pr� ps'  
}  }  }  }  *F((  
s  o  *0 Y    

(  {  ,*{  ,"{  r� pr� p()  
o*  
}  �+  %r� p�%r	 p�%r p�%r) p��+  %r� p�%r	 p��+  %r	 p�%r p��+  %r) p�{	  o+  
@�   {	  o,  
	
+#�
	(-  

(.  
,
+X�i2�
:v  +=�	(-  
(.  
,{  	(-  
	(/  
o*  
+X�i2�:  {  	r3 p(0  
(1  
o*  
8�   {	  o+  
@�   {	  o2  
8�   o3  
{  ,:+'�(-  
(.  
,	+HX�i2�+8+'�(-  
(.  
,	+X�i2�-{  r3 p(0  
(1  
o*  
o4  
:G����u  ,o5  
�}  {  o6  
,{  o7  
ݻ  {  -}  {  ,(8  
{  ,!(  (9  
(:  
{  o7  
�v  {
  o2  
+o3  
{  (;  
o*  
o4  
-��u  ,o5  
�}
  s<  

s=  
s>  
{  o2  
+&o3  
(?  
o@  
oA  
oB  
o4  
-��u  ,o5  
�{  %�*  
sC  
oD  
{  %�*  
sC  
oE  
{  %�*  
sC  
oF  
r9 p(G  
oH  
r9 p(G  
oI  
r9 p(G  
sJ  

sK  
oL  
{
  oM  
{  oN  
oO  
{  oP  
{  -	oQ  
}  {	  o+  
3Q{	  o,  
(R  
oS  
;g  {  {	  o,  
(T  
o*  
{  o7  
ݜ  {  9�   {	  o,  
(-  
r p(.  
,{	  o,  
(U  
oV  
+{	  o,  
(R  
{	  o,  
(-  
r p(.  
,{	  o,  
(U  
oV  
+{	  o,  
(R  
}  +J{	  o,  
{  rK p(W  
oX  
{	  o,  
{  ri p(W  
oX  
{  oY  
oZ  
,oZ  
o[  
2o\  
(]  
s^  
z,.{  ,{  	oQ  
o_  
+{  o`  
&�!  uB  ,}  {  oa  
o*  
��   }  {  ob  
oc  
od  
oe  
(f  
o*  
uC  -	uD  ,�ݪ   ,2{  ,og  
-"(  oh  
(i  
+(
  oh  
(i  
,2{  ,oj  
-"(  oh  
(i  
+(  oh  
(i  
	,<{  ,$	oQ  
(k  
�&&�#&(  	oQ  
(i  
�(  	oQ  
(i  
�{  o7  
**   A�     �  �   `            �  )               Q  3   �                !  )  *            !  S  M               #     !           &             �  �  �       0 D	    
81	  �9#	  ol  
9	  -om  
./om  
@�  on  

r� p	(o  
,}  8�  r� p	(o  
,2	{  �%(p  
}  {  (q  
,*}  8�  r� p	(o  
,8�%(r  
-{  r� p(s  
o*  
*{  �ot  
&8Z  r� p	(o  
,}  8A  r� p	(o  
,}  8(  r� p	(o  
-
r� p	(o  
,)	{  �%(u  
}  {  (q  
9�  *r� p	(o  
,}
  8�  r� p	(o  
,}  8�  r� p	(o  
,,{  r� pr� p(v  
o*  
{  ow  
8z  	r� pox  
9�   	on  
ol  
-{  ow  
+8(y  
oz  
({  
2{  (|  
o*  
{  o}  
�  &{  (|  
o*  
��  &{  (|  
o*  
��  r� p	(o  
,,{  r� pr� p(v  
o*  
{  o~  
8�  	r� pox  
9�   	on  
ol  
-{  o~  
+8(y  
oz  
({  
2{  (  
o*  
{  o�  
�3  &{  (  
o*  
�  &{  (  
o*  
�  r p	(o  
9$  �%(r  
-{  	(�  
o*  
*,{  (�  
o*  
8�  �o�  
r� p(o  
-Hr
 p(o  
-Mr p(o  
-Rr p(o  
-Wr p(o  
-\r p(o  
-b+t{  `}  8E  {  `}  82  {  `}  8  {  `}  8  {  `}  8�  {   `}  8�  {  	(�  
o*  
8�  r! p	(o  
9�   �%(r  
-{  	(�  
o*  
*{  ,{  (�  
o*  
8  �o�  
r% p(o  
-r7 p(o  
-rI p(o  
-+' ?  }  +- ? "}  +   "	}  +{  	(�  
o*  
*
8  rU p	(o  
,{  o�  
8�  rY p	(o  
9�  �%(r  
-{  	(�  
o*  
*,{  (�  
o*  
8�  �o�  
(  		 6?�5O	 1�5	 &�;�   	 1�;�   8m  	 <�;�   	 �=�;�   	 6?�;�   8D  	 G�5#	 �@�;�   	 �E�.y	 G�.Z8  	 �N�;�   	 �P�;�   	 �S�;�   8�  ra p(o  
:�   8�  re p(o  
:�   8�  ri p(o  
:�   8�  rm p(o  
:�   8�  r! p(o  
:�   8�  rq p(o  
:�   8k  rU p(o  
:�   8U  ru p(o  
:�   8?  ry p(o  
:�   8)  r} p(o  
:�   8  r� p(o  
:�   8�   {      `}  8�  {      `}  8�  {      `}  8�  {      `}  8�  {      `}  8|  {       `}  8e  {     @ `}  8N  {     � `}  87  {      `}  8   {      `}  8	  {      `}  8�   {  	(�  
o*  
8�   ry p	(o  
,)	{  �%(p  
}  {  (q  
9�   *r� p	(o  
,}  8�   r� p	(o  
,	}  +vr� p	(o  
-
r� p	(o  
,}  *{
  	ot  
&+E@om  
3on  
(�  
(  +&{  (�  


(q  
,*{	  
ot  
&��i?����*4    �T*"    �TC#    �T"    �T#  (%  
*�~  -r� p�  (#  
o�  
s�  
�  ~  *~  *�  *V(	  r p~  o�  
*V(	  r p~  o�  
*V(	  rU p~  o�  
*V(	  r� p~  o�  
*V(	  r� p~  o�  
*   0 ,     ,' ŝ�
+om  
a � Z
Xol  
2�*BSJB         v2.0.50727     l   �  #~  `	  �  #Strings    <  �  #US 4     #GUID   D  �  #Blob         W�		   �3      J            	   �                                            p      �~
 =~
 �L
 �
   :$ �$ � &_
 Q$ $$ �$ �$ n$ ��
 �
� �$
 iL
 �� Q�
 Y�  !	e  �e �e �e
 FZ	 qe
  
�  \� ue e � �1  �� ��
 �Z	 �L
 �~
 ��
 ]�
 lO
 
O ��  � � h� >e �e  e �e 1  H� (	e �� �e �e �e F	e �
� �	e e = e 6e je 	e �e �� �_
 �1  �e �� M� �� �e    	       � .e=    �=      �=         =    � �
 � �
 � �   � U# `# �# 7	' � 
  �  �    �  �+ �  n Q�UQ�Q�eQ�; �/ 94P     � q9 �!    � �@ "    �E
�  T"    �?
  �"    � �J �"    � YP �*    � �V t4    �?
  |4    ��\ �4    �!� �4    �-b �4    �5i	 �4    �O
i	 �4    �1
i	 �4    �n
i	 5    ��	i	 (5    � �m	    F   �   �  [            e   /
	 ?
  ?
  ?

 ) ?
 1 ?
 9 ?
 A ?
 I ?
 Q ?
 Y ?
 a ?
 i ?
 q ?
 !?
 )?
 1?
 Q?
  � �8 � y< � �8 Y� B � �
8 � �8 � �8 YHH � ?
 � �	M � �	M Y�
Q Q �8 Y �
8 1 8 a �
8 ) �8 izy � k� y ?
 � ?
 � ?
 y� ��� � �� � CM � �� �H� Y� ��� Y� �#� � 1
� � 78 � x� � ^ ��	� � �M y�� ��� yr	� �� � ?
 � ?
 � ?
 � c � �� �?

�� � � � � �E� �� � � � ?
 �?
 ��	� 
 � �
 � � � �  � �8 � c !� )��/�c 6�c?��D� � J� zT� E_�CM � [e��k� ?
t� k{�� �� �	�� 8 �y 8 � 8 ����P� ��8 ���� P� )��YM YQ�Y�� Y��1��Y��1���l /� h �1������� Y�AI�A�
�A ��J /� �
 ��	/� �/��Y�	8 �- Qf
1�i�9?
9�& T � X � \  ` ) � �.  �.  �.  �. # �. + �. 3 �. ; �. C �. K 
. S . [ i. c �. k �I � �� s �� { �� � �� � �' q � �.    �r  Vx  9~  S
~  5
~  r
~  �	~ 	   
         
 	      
    v �            E�             3A                3�               <�               <�           �
       ;  X  ]  b  g  l  M      ToInt32 <Module> <PrivateImplementationDetails> System.IO T Pdb mscorlib IllegalSuppressWarningId Load Add TypeSpecificationForExtensionRequired System.Collections.Specialized Bind Unbind Replace get_StackTrace set_IsAdminImage adminImage add_Message get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage GetEnvironmentVariable IDisposable RuntimeTypeHandle GetTypeFromHandle CommandLineResponseFile GetFile outputFile Console wconsole get_Title get_ProductName ParseCommandLine WriteLine Combine get_Type GetType OutputType get_Culture set_Culture resourceCulture get_InvariantCulture Dispose Parse EditorBrowsableState Delete CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value Save torch.exe Diff ToString GetString Substring IsValidArg Torch torch DeprecatedCommandLineSwitch ComputeStringHash get_BasePath exportBasePath VerifyPath get_Length StartsWith Microsoft.Tools.WindowsInstallerXml.Msi original System.ComponentModel Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item System NoDifferencesInTransform InvalidWixTransform resourceMan Main Join wixPdbExtension AddExtension UnexpectedFileExtension msiExtension GetExtension wixMstExtension wixOutExtension WixExtension get_FileVersion get_Location get_TempFilesLocation set_TempFilesLocation IllegalCommandlineArgumentCombination System.Globalization PrepareConsoleForLocalization System.Reflection TableCollection TempFileCollection StringCollection SourceLineNumberCollection SEHException UnexpectedException DirectoryNotFoundException NullReferenceException FormatException WixInvalidIdtException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo showLogo showHelp get_LastErrorNumber DisplayToolHeader IFormatProvider WixBinder Unbinder Differ get_ResourceManager BinderFileManager ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler DisplayToolFooter set_WixVariableResolver ToLower get_WAR_FailedToDeleteTempDir get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.TorchStrings.resources DebuggingModes set_ShowPedanticMessages showPedanticMessages set_ShowVerboseMessages get_Tables DeleteTempFiles inputFiles GetCustomAttributes TransformFlags validationFlags set_SuppressAllWarnings WixWarnings TorchStrings invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args Equals Microsoft.Tools.WindowsInstallerXml.Tools Contains get_Chars get_SourceLineNumbers ReplacePlaceholders WixErrors set_SuppressExtractCabinets get_Comments IllegalValidationArguments WrongFileExtensionForNumberOfInputs xmlInputs set_PreserveUnchangedRows preserveUnchangedRows get_INF_TorchTempDirLocatedAt get_INF_BinderTempDirLocatedAt get_INF_UnbinderTempDirLocatedAt Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Environment ExpectedArgument UnsupportedCommandLineArgument get_Current get_Count Convert extensionList get_Output xmlOutput MoveNext wix Display tidy get_Assembly assembly get_Company GetDirectory op_Equality IsNullOrEmpty   [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  	T R C H  t o r c h . e x e  a  x i  . w i x m s t  . w i x o u t  . w i x p d b  	. m s i  ,    W I X _ T E M P  t a r g e t B i n a r i e s  u p d a t e d B i n a r i e s  a x  e x t  	- e x t 
n o l o g o  
n o t i d y  o  o u t  p  p e d a n t i c  s w a l l  s w  w x a l l  w x  	s e r r  b  c  d  e  f  t  l a n g u a g e  i n s t a n c e  p a t c h  v  v a l  g  l  r  s  u  w  x  y  z  x o  ?  	h e l p  mM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . T o r c h S t r i n g s  H e l p M e s s a g e  5I N F _ B i n d e r T e m p D i r L o c a t e d A t  3I N F _ T o r c h T e m p D i r L o c a t e d A t  9I N F _ U n b i n d e r T e m p D i r L o c a t e d A t  3W A R _ F a i l e d T o D e l e t e T e m p D i r   N_6�GB� ��r.�         ��E)-1I   E     
)
-

1
  ���� ��  2]aeimquymm}�� �� ��   ��   q    �� y y  �� �� m  �� �� ��  m 	 m��
 mmmY  ��  � ��� �� ��� m  ��  �� ��  
	  	 U    �� ��  ��  � �%  ��  U  A A ��	�z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����. w i x m s t . w i x p d b . w i x o u t . m s i QUY���� A	A      �� ��   	 �� ��         TWrapNonExceptionThrows      " WiX Toolset Transform Builder   Transform Builder          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US  @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******        6
  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADP̿n���Ė�����ƶ8�   T       �      �  H e l p M e s s a g e     4I N F _ B i n d e r T e m p D i r L o c a t e d A t �
  2I N F _ T o r c h T e m p D i r L o c a t e d A t �
  8I N F _ U n b i n d e r T e m p D i r L o c a t e d A t �
  2W A R _ F a i l e d T o D e l e t e T e m p D i r '  � usage: torch.exe [-?] [options] targetInput updatedInput -out outputFile [@responseFile]

   -a         admin image (generates source file information in the transform)
              (default with -ax)
   -ax <path> admin image with extraction of binaries
              (combination of -a and -x)
   -ext <extension>  extension assembly or "class, assembly"
   -nologo    skip printing logo information
   -notidy    do not delete temporary files (useful for debugging)
   -o[ut]     specify output file (default: write to current directory)
   -p         preserve unmodified content in the output
   -pedantic  show pedantic messages
   -serr <L>  suppress error when applying transform; see Error flags below
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1011 -sw1012)
   -swall     suppress all warnings (deprecated)
   -t <type>  use default validation flags for the transform type
              (see Transform types below)
   -v         verbose output
   -val <L>   validation flags for the transform; see Validation flags below
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1011 -wx1012)
   -wxall     treat all warnings as errors (deprecated)
   -x <path>  extract binaries to the specified path
   -xi        input WiX format instead of MSI format (.wixout or .wixpdb)
   -xo        output wixout instead of MST format
              (set by default if -xi is present)
   -? | -help this help information

Error flags:
   a          Ignore errors when adding an existing row
   b          Ignore errors when deleting a missing row
   c          Ignore errors when adding an existing table
   d          Ignore errors when deleting a missing table
   e          Ignore errors when modifying a missing row
   f          Ignore errors when changing the code page

Validation flags:
   g          UpgradeCode must match
   l          Language must match
   r          Product ID must match
   s          Check major version only
   t          Check major and minor versions
   u          Check major, minor, and upgrade versions
   v          Upgrade version < target version
   w          Upgrade version <= target version
   x          Upgrade version = target version
   y          Upgrade version > target version
   z          Upgrade version >= target version

Transform types:
   language   Default flags for a language transform
   instance   Default flags for an instance transform
   patch      Default flags for a patch transform

Environment variables:
   WIX_TEMP   overrides the temporary directory used for cab extraction, binary extraction, ...,Binder temporary directory located at '{0}'.+Torch temporary directory located at '{0}'..Unbinder temporary directory located at '{0}'.2Warning, failed to delete temporary directory: {0}      ���V��f�Bb��#r�tճ=�Wz ̠�M<ܡΝ��a1/���H���hV/��B�/�z�f�:2�'%�>I�d҆ζn�E>�H$��+�5xX,\�� �^M�����X�5r3br��x�g�^�    (p�]         `f  `V  RSDS�I�=�e�A�����S�O   C:\agent\_work\66\s\build\obj\ship\x86\torch\torch.pdb                                                                                                                                                                                                              �g          �g                          �g            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �   P  �                  8  �               	  �                     h  �               	  �  ��  h          h4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   d   F i l e D e s c r i p t i o n     W i X   T o o l s e t   T r a n s f o r m   B u i l d e r   8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   ,   I n t e r n a l N a m e   t o r c h   � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   < 
  O r i g i n a l F i l e n a m e   t o r c h . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Torch" version="*******" processorArchitecture="x86" type="win32"/>
 <description>WiX Toolset Transform Builder</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         `     �7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      