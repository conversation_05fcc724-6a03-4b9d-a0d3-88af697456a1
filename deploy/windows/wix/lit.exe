MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L (p�]        � 0  @          "V       `    @                       �     �   @�                           �U  O    `  �                   �     �T                                                               H           .text   (6       @                    `.rsrc   �   `      P              @  @.reloc      �      `              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                V      H     �.  <       �M  @  T  �                                   0 r    94  o  
(  

r  po  
o  
 r  po  
o  
 r?  po  
o  
 rc  po  
o  
 r  po  
,<o  
s  
r  po  
�0  r�  po  
�0  (  
o  
 (  +,r�  po  
o  
 (  +,r�  po   
o  
 (  +,r�  p	o!  
o  
 (  +,r+ po"  
o  
 (  +,rO po#  
o  
 ro p~  o  
 r� p~  o  
 r� p~  o  
 *  0 B     �  �  ($  
o%  

,�,�u  �  �  q  �  �*�r� p�  r� p�  r p�  r9 p�  r� p�  *  0 {       (&  
s'  
}  s'  
}	  s'  
}
  s'  
}  s'  
}  }
  r� pr� ps(  
}  s'  
}  s'  
}  s'  
}  *F()  
s  o  *   0 f    
s*  
(  s+  
{  o,  
+=o-  
(.  
o/  
{  o0  
/{  {  o1  
}  o2  
-��u  ,o3  
�{  (  {  o4  
,{  o5  
ݷ  {	  o0  
-	}  +B{  -:{	  o0  
/(6  
s7  
z{	  o8  
(9  
r� p(:  
}  {
  ,(;  
{  ,!(  (<  
(=  
{  o5  
�)  {
  o,  
+o-  
{  (>  
o?  
o2  
-��u  ,o3  
�}
  s@  

{  %�?  
sA  
oB  
{  oC  
{  ,R{  o,  
+#o-  
		=oD  
3{  	oE  
&o2  
-��u  ,o3  
�oF  

+v
(G  
oH  
oI  
,\{  ,G(J  
(
  �  %oI  
oK  
oL  
�%{  oK  
oL  
�(M  
r� psN  
zoI  
}  
(O  
-��
�  o3  
�{	  o,  
8�   o-  
(P  
(Q  

{  
oR  
-{  
oE  
& oS  
{  {  (T  
oU  
oV  
�+&� oS  
{  {  (W  
oX  
oV  
o2  
:j����u  ,o3  
�oY  

	9�  {  9�   {  -sZ  
}  {  9�   {  o,  
+Vo-  
=oD  
3{  o[  
oE  
&+,�A  %=�o\  
{  o]  
��o^  
o2  
-��u  ,o3  
�{  o,  
+o-  
{  o_  
oE  
&o2  
-��u  ,o3  
�}  {  o,  
+"o-  
oS  
{  (`  
	oa  
o2  
-��u  ,o3  
�sb  
{  %�?  
sA  
oc  
	{  {  od  
�Z{  oe  
o?  
�C{  of  
og  
oL  
oh  
(i  
o?  
uC  -	uD  ,�� {  o5  
**  A    "   J   l             G  )   p            �  0   �              �   �             �  *        %     �  �   S            �  c               1  )   Z            �  /   �                 �  �     #          �    C   $  0 �    
8�  �9�  oj  
9�  -ok  
./ok  
@�  ol  
r p(m  
,/{  �%
(n  

	(o  
,*{  	oE  
&8v  r p(m  
,}  8]  r� p(m  
,8�%
(p  
-{  r p(q  
o?  
*{  �oE  
&8  r p(m  
,1{  �%
(r  
(o  
,*{  oE  
&8�  r p(m  
,}
  8�  r+ p(m  
-
r/ p(m  
,){  �%
(r  
}  {  (o  
9  *r7 p(m  
,}  8e  rI p(m  
,}  8L  rO p(m  
,}  83  rU p(m  
,,{  rU pra p(s  
o?  
{  ot  
8�  ra pou  
9�   ol  
oj  
-{  ot  
+8(v  
ow  
(x  
2{  (y  
o?  
{  oz  
݌  &{  (y  
o?  
�s  &{  (y  
o?  
�Z  rg p(m  
,,{  rg prs p(s  
o?  
{  o{  
8!  rs pou  
9�   ol  
oj  
-{  o{  
+8(v  
ow  
(x  
2{  (|  
o?  
{  o}  
ݳ   &{  (|  
o?  
ݚ   &{  (|  
o?  
݁   ry p(m  
,{  o~  
+fr} p(m  
-
r� p(m  
,}  *{  oE  
&+5oj  
1@ok  
3ol  
(  
(  +
{  oE  
&�
�i?���*   4    Tc&    T|'    �T<&    �TU'  0 q     
+co8  
,Toj  
,Loj  
1--ok  
./ok  
3ol  
{
  oE  
&+{	  r� p(�  
o�  
�
o0  
2�*(&  
*�~  -r� p�  ($  
o�  
s�  
�  ~  *~  *�  *V(
  r p~  o�  
*V(
  rC p~  o�  
*BSJB         v2.0.50727     l   H  #~  �  T
  #Strings      \  #US d     #GUID   t  �  #Blob         W�		   �3      I            	   �                                               ��	 L�	 u	 
   I, �, � 5�	 `, 3, , �, }, ��	 0� 
,
 ou	 �� `�
 R�  @) t) �� �) c)  =  �) #
)
 I	�  4�k ]	   b) �) ) ) � �) �� �
 �� �u	 ��	 �	 c�
 o
  �� 
 � �� @� D) d) {) �) ) �  �� �
) 	) �
) �) �� �� u) ��
 >�  �� ��	 �) N� �� �� d)           � 4)=    q{=      �
{=  	  d C l  M U R l
 �
 �
 �
 � � � ?
 %  � �" � v
 o �& /+P     � �0 �!    � �7 "    �n	�  T"    �h	  �"    � �A �"    � _G p)    � �F �-    � �M -.    �h	 	 5.    �S	 a.    �T	 h.    �#Y	 p.    �.`
 �.    �`
    
   
   
  j   /   /   /   /   t	 h	  h	  h	
 ) h	 1 h	 9 h	 A h	 I h	 Q h	 Y h	 a h	 i h	 q h	 � h	 Ah	 Ih	 Qh	 qh	  � �8 � < � �8 y� B � ^8 � �8 � �8 y�H � h	 � �M � �M yQ Q 
8 Y P8 1 $8 a 78 ) �8 �Ry � �
� y h	 � h	 � h	 �� � h	  h	 � Z	� � �8 � q �  v � � �M � �� � �� � T �		� � �M ��� h	� � w� ��� ����� ��	��� �{� �� h	 �h	� � #� &
 y�*� v / Z	4 �D� �I� OaTy �Zy �8 y)`�h	  �� ��� ��� � �H � �jq p�|� +�� q �� �|� ��� h	 � 4�yu�� B�v  � _�q �� ��	h	 	� #� z���!� 8 !�Z!� 8 ���y�M y��y�� y8
�)+
�yD
�)���z )|����
 y�a?Ta%A +�X � �6	 �	��  �T
 I\3��
?� +F�
LYh	QY�X) � �.  t.  }.  �. # �. + �. 3 �. ; �. C �. K �. S . [ Q. c �. k tI � �� { �� � � � � s ' q � �9    d  Lj  2p  p 
         
    	 v � =�            rw             `4                `�               i�               i�           �	       #  X  ]  b  g  l  M    List`1 ToInt32 <Module> System.IO T suppressSchema mscorlib System.Collections.Generic IllegalSuppressWarningId Load Add TypeSpecificationForExtensionRequired System.Collections.Specialized Replace get_StackTrace add_Message get_Message ElevateWarningMessage SuppressWarningMessage get_HelpMessage AddRange IDisposable RuntimeTypeHandle GetTypeFromHandle CommandLineResponseFile GetFile outputFile Console wconsole get_Title GetFileName get_ProductName GetDirectoryName ParseCommandLine WriteLine Combine GetType get_CurrentUICulture get_Culture set_Culture resourceCulture get_InvariantCulture Dispose Parse Intermediate EditorBrowsableState MTAThreadAttribute CompilerGeneratedAttribute GeneratedCodeAttribute DebuggerNonUserCodeAttribute NeutralResourcesLanguageAttribute DebuggableAttribute EditorBrowsableAttribute ComVisibleAttribute AssemblyTitleAttribute AssemblyFileVersionAttribute AssemblyConfigurationAttribute AssemblyDescriptionAttribute CompilationRelaxationsAttribute AssemblyProductAttribute TryGetAttribute AssemblyCopyrightAttribute CLSCompliantAttribute AssemblyCompanyAttribute RuntimeCompatibilityAttribute attribute value Save lit.exe IndexOf ToString GetString Substring IsValidArg DeprecatedCommandLineSwitch GetFullPath get_Length StartsWith suppressVersionCheck original System.ComponentModel Microsoft.Tools.WindowsInstallerXml VSExtensionsLandingUrl NewsUrl SupportUrl get_Item System resourceMan Librarian Main AddExtension ChangeExtension WixExtension get_FileVersion get_Location System.Globalization AddLocalization PrepareConsoleForLocalization System.Reflection NameValueCollection StringCollection SectionCollection TableDefinitionCollection SEHException UnexpectedException NullReferenceException WixNotIntermediateException FormatException ArgumentException OverflowException WixException get_Description WixDistribution AppCommon StringComparison Run CultureInfo FileVersionInfo GetVersionInfo NumberFormatInfo showLogo ParseCommandLinePassTwo showHelp Char get_LastErrorNumber DisplayToolHeader IFormatProvider get_ResourceManager get_BinderFileManager get_EXP_CannotLoadBinderFileManager binderFileManager IMessageHandler ConsoleMessageHandler messageHandler MessageEventHandler System.CodeDom.Compiler DisplayToolFooter WixVariableResolver get_Major get_Minor get_Error get_EncounteredError IllegalWarningIdAsError set_WarningAsError StringEnumerator GetEnumerator .ctor .cctor System.Diagnostics System.Runtime.InteropServices System.Runtime.CompilerServices System.Resources Microsoft.Tools.WindowsInstallerXml.Tools.LitStrings.resources DebuggingModes set_ShowPedanticMessages showPedanticMessages set_ShowVerboseMessages bindFiles localizationFiles GetFiles inputFiles GetCustomAttributes set_SuppressAllWarnings WixWarnings LitStrings unparsedArgs invalidArgs MessageEventArgs WixWarningEventArgs WixErrorEventArgs args get_BindPaths get_NamedBindPaths bindPaths get_SourcePaths sourcePaths Microsoft.Tools.WindowsInstallerXml.Tools Contains get_Sections get_TableDefinitions get_Chars ReplacePlaceholders WixErrors get_Comments Concat TelemetryUrlFormat get_NumberFormat Object get_Product ShortProduct get_Copyright get_LegalCopyright Lit Split UnsupportedCommandLineArgument get_Current get_Count Convert extensionList MustSpecifyOutputWithMoreThanOneInput MoveNext wix Display get_Assembly assembly get_Company Library GetDirectory op_Equality IsNullOrEmpty    [ F i l e C o m m e n t s ]  [ F i l e C o p y r i g h t ]  #[ F i l e P r o d u c t N a m e ]  [ F i l e V e r s i o n ]  /[ F i l e V e r s i o n M a j o r M i n o r ]  .  #[ A s s e m b l y C o m p a n y ]  '[ A s s e m b l y C o p y r i g h t ]  +[ A s s e m b l y D e s c r i p t i o n ]  #[ A s s e m b l y P r o d u c t ]  [ A s s e m b l y T i t l e ]  [ N e w s U r l ]  [ S h o r t P r o d u c t ]  [ S u p p o r t U r l ]  7h t t p : / / w i x t o o l s e t . o r g / n e w s /  W i X   T o o l s e t  -h t t p : / / w i x t o o l s e t . o r g /  Wh t t p : / / w i x t o o l s e t . o r g / t e l e m e t r y / v { 0 } / ? r = { 1 }  ?h t t p : / / w i x t o o l s e t . o r g / r e l e a s e s /  L I T  l i t . e x e  . w i x l i b  e x t  b  b f  	- e x t l o c  
n o l o g o  o  o u t  p e d a n t i c  s s  s v  s w a l l  s w  w x a l l  w x  v  ?  	h e l p  
S o u r c e  iM i c r o s o f t . T o o l s . W i n d o w s I n s t a l l e r X m l . T o o l s . L i t S t r i n g s  ?E X P _ C a n n o t L o a d B i n d e r F i l e M a n a g e r  H e l p M e s s a g e   �g����B�Q�.]��J         ��E)-1I   E     
)
-

1
  ���� ��  6aeimqumy}mmq����������im  u m  	 QQ��    �� ��     �� ��  ��    } }m    m  U  ��  ��	 ��  � ���  e e
 q� qe  Q   �	
 ��� ��	 U��  �� ��	  
 Y   ��	 Y �� �  � ��     A A ���z\V4���5�oͨ+��� $  �  �      $  RSA1     A���|����Fx�{���=P�j�J"Q �vh���hMM8۬�����fj��i�`�|�4�٬0q)�`� *�����ث
?��_�����������ݿW�	�0h#��M�����QUY���� A	A    Q  �� ��   �� ��         TWrapNonExceptionThrows       WiX Toolset Library Tool   Library Tool          3.11.2.4516   .NET Foundation  I DCopyright (c) .NET Foundation and contributors. All rights reserved.  " Windows Installer XML Toolset  
 en-US     @ 3System.Resources.Tools.StronglyTypedResourceBuilder*******     5  ���   �   lSystem.Resources.ResourceReader, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089#System.Resources.RuntimeResourceSet          PADPADPˮ�����    C   "  >E X P _ C a n n o t L o a d B i n d e r F i l e M a n a g e r     H e l p M e s s a g e �   �cannot load binder file manager: {0}.  lit can only load one binder file manager and has already loaded binder file manager: {1}.�	 usage:  lit.exe [-?] [-nologo] [-out libraryFile] objectFile [objectFile ...] [@responseFile]

   -b <path>  binder path to locate all files (default: current directory)
              prefix the path with 'name=' where 'name' is the name of your
              named bindpath.
   -bf        bind files into the library file
   -ext <extension>  extension assembly or "class, assembly"
   -loc <loc.wxl>  bind localization strings from a wxl into the library file
   -nologo    skip printing lit logo information
   -o[ut]     specify output file (default: write to current directory)
   -pedantic  show pedantic messages
   -ss        suppress schema validation of documents (performance boost)
   -sv        suppress intermediate file version mismatch checking
   -sw[N]     suppress all warnings or a specific message ID
              (example: -sw1011 -sw1012)
   -swall     suppress all warnings (deprecated)
   -v         verbose output
   -wx[N]     treat all warnings or a specific message ID as an error
              (example: -wx1011 -wx1012)
   -wxall     treat all warnings as errors (deprecated)
   -? | -help this help information       wR(|]�|�d�Nx����5�K�ϊ�~x�$���AA���sM�3A� �g%-"�����Do\�%���b�s�zAM	��?S-3���v�K_��Xs��������9�=<dN|S0ht�TЯ2=wf    (p�]         �T  �D  RSDS��R�݃DK�C��ы\   C:\agent\_work\66\s\build\obj\ship\x86\lit\lit.pdb                                                                                                                                                                                                                  �U          V                          V            _CorExeMain mscoree.dll     �%  @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              �   P  �                  8  �               	  �                     h  �               	  �  �`  P          P4   V S _ V E R S I O N _ I N F O     ���       �    �?                         �   S t r i n g F i l e I n f o   �   0 4 0 9 0 4 E 4   @   C o m p a n y N a m e     . N E T   F o u n d a t i o n   R   F i l e D e s c r i p t i o n     W i X   T o o l s e t   C o m p i l e r     8   F i l e V e r s i o n     3 . 1 1 . 2 . 4 5 1 6   (   I n t e r n a l N a m e   l i t   � F  L e g a l C o p y r i g h t   C o p y r i g h t   ( c )   . N E T   F o u n d a t i o n   a n d   c o n t r i b u t o r s . �   A l l   r i g h t s   r e s e r v e d .   8   O r i g i n a l F i l e n a m e   l i t . e x e   \   P r o d u c t N a m e     W i n d o w s   I n s t a l l e r   X M L   T o o l s e t   <   P r o d u c t V e r s i o n   3 . 1 1 . 2 . 4 5 1 6   D    V a r F i l e I n f o     $    T r a n s l a t i o n     	��c  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Copyright (c) .NET Foundation and contributors. All rights reserved. Licensed under the Microsoft Reciprocal License. See LICENSE.TXT file in the project root for full license information. -->


<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"> 
 <assemblyIdentity name="Microsoft.Tools.WindowsInstallerXml.Tools.Lit" version="*******" processorArchitecture="x86" type="win32"/> 
 <description>WiX Toolset Library Tool</description> 
 <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      P     $6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      