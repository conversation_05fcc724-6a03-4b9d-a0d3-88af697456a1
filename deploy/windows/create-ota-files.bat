@echo off
SETLOCAL EnableDelayedExpansion

SET "APP_PROPERTIES=%~3\phonecheck-ui\src\main\resources"
SET "DEST=%~2"

:: Check if the destination zip file already exists and delete it
if exist "%DEST%\update.zip" del "%DEST%\update.zip"

:: Use robocopy instead of xcopy for faster copying
robocopy "%~1" "%DEST%\app" /E /NJH /NJS /NDL /NC /NS /MT:16

:: Get app version from application.properties using full path
for /f "tokens=2 delims== eol= " %%a in ('findstr /B "applicationVersion=" "!APP_PROPERTIES!\application.properties"') do set "appVersion=%%a"

:: Create update.json file
(
echo {"version":"!appVersion!","platform":"Windows","isStable":true}
) > "%DEST%\output.json"

:: Use 7-Zip for faster compression if available, otherwise fall back to PowerShell
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    7z a -tzip "%DEST%\update.zip" "%DEST%\*" -mx=1 -mm=Deflate -mmt=on
) else (
    powershell -Command "Compress-Archive -Path '%DEST%\*' -DestinationPath '%DEST%\update.zip' -Force -CompressionLevel Fastest"
)

:: Remove directories after zipped
rmdir /s /q "%DEST%\app"
del /q "%DEST%\output.json"

echo OTA files created successfully to %DEST%\update.zip.
ENDLOCAL