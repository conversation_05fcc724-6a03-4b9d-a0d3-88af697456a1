@echo off
SETLOCAL

:: Setting embedded java path variable to run daemon and backend jar
SET "SCRIPT_DIR=%~dp0"
SET "SCRIPT_DIR=%SCRIPT_DIR:~0,-1%"

:: Move up one directory from the script's directory
FOR /F "delims=" %%i IN ("%SCRIPT_DIR%\..") DO SET "ROOT=%%~fi"
SET EMBEDDED_JAVA_PATH=%ROOT%\runtime\bin\java.exe

:: Kill old processes if present
FOR /F "tokens=2 delims= " %%i IN ('tasklist /NH /FI "IMAGENAME eq java.exe"') DO (
    FOR /F "tokens=*" %%j IN ('jps -lv ^| findstr "pc-daemon.jar"') DO (
        IF "%%i"=="%%j" taskkill /F /PID %%i
    )
    FOR /F "tokens=*" %%j IN ('jps -lv ^| findstr "pc-backend.jar"') DO (
        IF "%%i"=="%%j" taskkill /F /PID %%i
    )
)

:: Starting the daemon and backend in background
START /B "" "%EMBEDDED_JAVA_PATH%" -jar %1\pc-daemon.jar
TIMEOUT /T 2 /NOBREAK >nul
START /B "" "%EMBEDDED_JAVA_PATH%" -jar %1\pc-backend.jar installer

echo %~dp0
ENDLOCAL
