package com.phonecheck.mqtt.subscriber;

import com.phonecheck.model.mqtt.messages.MqttTopicMessage;

/**
 * Contract for MQTT topic subscribers.
 * Implementors should override the hashCode() and equals() functions.
 */
public interface MqttTopicSubscriber {

    /**
     * @return this handler instance's unique ID
     */
    String getId();

    /**
     * @return topics to which this handler is subscribed
     */
    String[] getTopics();

    /**
     * Invoked when a message is received on a topic to which this handler subscribes
     *
     * @param msg topic message
     */
    void onMessage(MqttTopicMessage msg);
}
