package com.phonecheck.model.android;

import lombok.Getter;

@Getter
public enum AndroidAtStreamKey {
    DEVICE_INFO_KEY("\"DevConInfo\":"),
    EXPLOIT_SUPPORT_INFO_KEY("\"ADB_Exploit_Model_Supported\":"),
    EXPLOIT_CSC_SUPPORT_INFO_KEY("\"ADB_Exploit_CSC_Supported\""),
    FRP_INFO_KEY("\"FrpInfo\":"),
    ROOT_INFO_KEY("\"RootInfo\":"),
    NETWORK_LOCK_INFO_KEY("\"NetLockInfo\":"),
    OEM_INFO_KEY("\"OemInfo\":"),
    CONFIGURED_CARRIER_KEY("\"Configured_Carrier\":"),
    ORIGINAL_CARRIER_KEY("\"Original_Carrier\":"),
    CARRIER_LOCK_KEY("\"CarrierLock-AT\":"),
    PIN_LOCK_KEY("\"PIN_LOCK\":"),
    SIM_CARD_KEY("\"SimCard\":"),
    SIM_TRAY_KEY("\"SimTray\":"),
    KNOX_INFO_KEY("\"Knox\":"),
    SD_CARD_KEY("\"SDCard\":"),
    BATTERY_INFO_KEY("\"BatteryHealth\":"),
    PORT_UPDATED_KEY("Port has been updated to:"),
    USING_Q_PORT_KEY("Using Q-port:"),
    ADB_ACTIVATED_KEY("ADB Activated Successfully"),
    ALREADY_PREPARED_KEY("ALREADY ON HOME SCREEN , Still Want To Continue ?"),
    MANUALLY_OPEN_TEST_MENU_KEY("Cannot Open TestMenu Please Dial *#0*# Manually"),
    ERROR_KEY("ERROR:"),
    REBOOTING_FOR_EXPLOIT_KEY("Sending device to reboot"),
    REBOOTED_KEY("Device came back from reboot"),
    EXECUTION_COMPLETED_KEY("Command execution completed");

    private final String value;

    AndroidAtStreamKey(String value) {
        this.value = value;
    }
}
