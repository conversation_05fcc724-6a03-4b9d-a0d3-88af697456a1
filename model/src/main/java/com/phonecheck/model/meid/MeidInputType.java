package com.phonecheck.model.meid;

public enum MeidInputType {
    MEID_DEC(Type.MEID, Format.DEC),
    MEID_HEX(Type.MEID, Format.HEX),
    ESN_DEC(Type.ESN, Format.DEC),
    ESN_HEX(Type.ESN, Format.HEX),
    NONE(Type.NONE, Format.NONE);
    private final Type type;
    private final Format format;

    MeidInputType(Type type, Format format) {
        this.type = type;
        this.format = format;
    }

    public Type getType() {
        return type;
    }

    public Format getFormat() {
        return format;
    }

    public enum Type {
        MEID, ESN, NONE
    }

    public enum Format {
        HEX, DEC, NONE
    }
}