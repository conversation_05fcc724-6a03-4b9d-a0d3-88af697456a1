package com.phonecheck.model.syslog.android;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Model and a utility class to define all the syslog keys we currently have
 * for previous versions the default keys. And maintains a list of SysLogKeys
 * we can add more keys into that list in case we get new keys from cloud.
 */
@ToString
@Getter
@NoArgsConstructor
public final class AndroidSysLogKey {
    // Test results keys
    public static final AndroidSysLogKey START_READING_TEST_RESULTS = new AndroidSysLogKey("rhkg38yw4w-",
            AndroidSysLogKeyType.TEST_RESULTS,
            true);

    public static final AndroidSysLogKey END_READING_TEST_RESULTS = new AndroidSysLogKey("-4rhjg7x9gw",
            AndroidSysLogKeyType.TEST_RESULTS,
            true);

    // Erase device keys
    public static final AndroidSysLogKey START_READING_ERASE_STATUS = new AndroidSysLogKey("UGhvbmVDaGVja0VyYXNlclN0YXJ0",
            AndroidSysLogKeyType.ERASE,
            true);

    public static final AndroidSysLogKey END_READING_ERASE_STATUS = new AndroidSysLogKey("UGhvbmVDaGVja0VyYXNlckVuZA==",
            AndroidSysLogKeyType.ERASE,
            true);
    public static final AndroidSysLogKey BEGINNING_OF_CRASH = new AndroidSysLogKey("beginning of crash",
            AndroidSysLogKeyType.ERASE,
            true);
    public static final AndroidSysLogKey WIPE_DATA = new AndroidSysLogKey("wipe_data",
            AndroidSysLogKeyType.ERASE,
            true);

    // Fingerprint syslog keys
    public static final AndroidSysLogKey START_FINGER_PRINT_ADMIN = new AndroidSysLogKey("DeviceAdmin-",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey END_FINGER_PRINT_ADMIN = new AndroidSysLogKey("-DeviceAdmin",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_1 = new AndroidSysLogKey("vendor.samsung.hardware.biometrics.fingerprint@2.1-service: onAcquired",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_2 = new AndroidSysLogKey("FingerprintService: handleAcquired : acquiredInfo=",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_3 = new AndroidSysLogKey("vendor.samsung.hardware.biometrics.fingerprint@3.0-service: onAcquired",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_4 = new AndroidSysLogKey("FingerprintService: handleAcquired: acquiredInfo=6, vendor=20002",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_5 = new AndroidSysLogKey("FingerprintService: handleAcquired: acquiredInfo=6, vendor=20001",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_SCAN_EFFECT = new AndroidSysLogKey("FingerScanEffect-",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey START_FINGER_PRINT_PIN = new AndroidSysLogKey("FingerPrintPin-",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey END_FINGER_PRINT_PIN = new AndroidSysLogKey("-FingerPrintPin",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_6 = new AndroidSysLogKey("BSS_FingerprintEnrollActivity: onEnrollmentProgress : remaining = 94",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_7 = new AndroidSysLogKey("FingerprintManager: handleMessage = 101, 6, 10005",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_8 = new AndroidSysLogKey("BSS_FingerprintEnrollActivity: onEnrollmentHelp : helpMsgId = 10005 , helpString = null",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_9 = new AndroidSysLogKey("BSS_FingerprintEnrollActivity: onEnrollmentHelp : helpMsgId = 10006 , helpString = null",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_10 = new AndroidSysLogKey("BSS_FingerprintEnrollActivity: eventId[FINGERPRINT_ACQUIRED_CAPTURE_SUCCESS]",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_11 = new AndroidSysLogKey("FpstRegisterTouchFingerprint: onEnrollmentHelp : helpMsgId = 10002",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_12 = new AndroidSysLogKey("android.hardware.biometrics.fingerprint@2.1-service: onAcquired(0)",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_13 = new AndroidSysLogKey("fpc_hidl: onAcquired",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_14 = new AndroidSysLogKey("fpc_fingerprint_hal: do_enroll failed FPC_ERROR_CANCELLED",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_15 = new AndroidSysLogKey("fingerprintd: onAcquired(10002)",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);
    public static final AndroidSysLogKey FINGER_PRINT_16 = new AndroidSysLogKey("FpstRegisterTouchFingerprint: eventId[FINGERPRINT_ACQUIRED_CAPTURE_STARTED]",
            AndroidSysLogKeyType.FINGER_PRINT,
            true);

    // PC Utility deviceInfo.json keys
    public static final AndroidSysLogKey DEVICE_INFO_START_HASH = new AndroidSysLogKey("ZGV2aWNlaW5mb3N0YXJ0-",
            AndroidSysLogKeyType.DEVICE_INFO,
            true);
    public static final AndroidSysLogKey DEVICE_INFO_END_HASH = new AndroidSysLogKey("-ZGV2aWNlaW5mb2VuZA",
            AndroidSysLogKeyType.DEVICE_INFO,
            true);

    // OnePlus Alert Slider keys
    public static final AndroidSysLogKey START_READING_ALERT_SLIDER = new AndroidSysLogKey("ButtonsTest-",
            AndroidSysLogKeyType.ALERT_SLIDER,
            true);
    public static final AndroidSysLogKey END_READING_ALERT_SLIDER = new AndroidSysLogKey("-ButtonsTest",
            AndroidSysLogKeyType.ALERT_SLIDER,
            true);

    // By AlertSliderAudioPolicy : ASAP
    public static final AndroidSysLogKey ALERT_SLIDER_UP_BY_ASAP = new AndroidSysLogKey("AlertSliderAudioPolicy: setUp",
            AndroidSysLogKeyType.ALERT_SLIDER_UP,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_MID_BY_ASAP = new AndroidSysLogKey("AlertSliderAudioPolicy: setMiddle",
            AndroidSysLogKeyType.ALERT_SLIDER_MIDDLE,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_DOWN_BY_ASAP = new AndroidSysLogKey("AlertSliderAudioPolicy: setDown",
            AndroidSysLogKeyType.ALERT_SLIDER_DOWN,
            true);

    // By AlertSliderVibratorPolicy : ASVP
    public static final AndroidSysLogKey ALERT_SLIDER_UP_BY_ASVP = new AndroidSysLogKey("AlertSliderVibratorPolicy: setUp",
            AndroidSysLogKeyType.ALERT_SLIDER_UP,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_MID_BY_ASVP = new AndroidSysLogKey("AlertSliderVibratorPolicy: setMiddle",
            AndroidSysLogKeyType.ALERT_SLIDER_MIDDLE,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_DOWN_BY_ASVP = new AndroidSysLogKey("AlertSliderVibratorPolicy: setDown",
            AndroidSysLogKeyType.ALERT_SLIDER_DOWN,
            true);

    // By VolumeDialogImpl : VDI
    public static final AndroidSysLogKey ALERT_SLIDER_UP_BY_VDI = new AndroidSysLogKey("vol.VolumeDialogImpl:  isRingSilent:true isRingVibrate:false",
            AndroidSysLogKeyType.ALERT_SLIDER_UP,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_MID_BY_VDI = new AndroidSysLogKey("vol.VolumeDialogImpl:  isRingSilent:false isRingVibrate:true",
            AndroidSysLogKeyType.ALERT_SLIDER_MIDDLE,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_DOWN_BY_VDI = new AndroidSysLogKey("vol.VolumeDialogImpl:  isRingSilent:false isRingVibrate:false",
            AndroidSysLogKeyType.ALERT_SLIDER_DOWN,
            true);

    // By ThreeKeyAudioPolicy2 : TKAP
    public static final AndroidSysLogKey ALERT_SLIDER_UP_BY_TKAP = new AndroidSysLogKey("ThreeKeyAudioPolicy2: setUp",
            AndroidSysLogKeyType.ALERT_SLIDER_UP,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_MID_BY_TKAP = new AndroidSysLogKey("ThreeKeyAudioPolicy2: setMiddle",
            AndroidSysLogKeyType.ALERT_SLIDER_MIDDLE,
            true);
    public static final AndroidSysLogKey ALERT_SLIDER_DOWN_BY_TKAP = new AndroidSysLogKey("ThreeKeyAudioPolicy2: setDown",
            AndroidSysLogKeyType.ALERT_SLIDER_DOWN,
            true);
    public static final AndroidSysLogKey START_FACTORY_RESET = new AndroidSysLogKey("startFactoryReset",
            AndroidSysLogKeyType.FACTORY_RESET, true);

    // Samsung Flip LCD & Digitizer Test Keys
    public static final AndroidSysLogKey START_Z_FLIP_LCD_TEST = new AndroidSysLogKey(
            "OpenFlipLCDTest--OpenFlipLCDTest",
            AndroidSysLogKeyType.Z_FLIP_SCREEN_TEST,
            true);
    public static final AndroidSysLogKey START_Z_FLIP_DIGITIZER_TEST = new AndroidSysLogKey(
            "OpenFlipDigitizerTest--OpenFlipDigitizerTest",
            AndroidSysLogKeyType.Z_FLIP_SCREEN_TEST,
            true);
    public static final AndroidSysLogKey END_Z_FLIP_TEST = new AndroidSysLogKey(
            "CloseFlipTest--CloseFlipTest",
            AndroidSysLogKeyType.Z_FLIP_SCREEN_TEST,
            true);


    public static final List<AndroidSysLogKey> ANDROID_SYS_LOG_KEYS = new ArrayList<>(
            Arrays.asList(
                    START_READING_TEST_RESULTS,
                    END_READING_TEST_RESULTS,
                    START_READING_ERASE_STATUS,
                    END_READING_ERASE_STATUS,
                    WIPE_DATA,
                    START_FINGER_PRINT_ADMIN,
                    END_FINGER_PRINT_ADMIN,
                    FINGER_PRINT_1,
                    FINGER_PRINT_2,
                    FINGER_PRINT_3,
                    FINGER_PRINT_4,
                    FINGER_PRINT_5,
                    FINGER_PRINT_SCAN_EFFECT,
                    START_FINGER_PRINT_PIN,
                    END_FINGER_PRINT_PIN,
                    FINGER_PRINT_6,
                    FINGER_PRINT_7,
                    FINGER_PRINT_8,
                    FINGER_PRINT_9,
                    FINGER_PRINT_10,
                    DEVICE_INFO_START_HASH,
                    DEVICE_INFO_END_HASH,
                    START_READING_ALERT_SLIDER,
                    END_READING_ALERT_SLIDER,
                    ALERT_SLIDER_UP_BY_ASAP,
                    ALERT_SLIDER_MID_BY_ASAP,
                    ALERT_SLIDER_DOWN_BY_ASAP,
                    ALERT_SLIDER_UP_BY_ASVP,
                    ALERT_SLIDER_MID_BY_ASVP,
                    ALERT_SLIDER_DOWN_BY_ASVP,
                    ALERT_SLIDER_UP_BY_VDI,
                    ALERT_SLIDER_MID_BY_VDI,
                    ALERT_SLIDER_DOWN_BY_VDI,
                    ALERT_SLIDER_UP_BY_TKAP,
                    ALERT_SLIDER_MID_BY_TKAP,
                    ALERT_SLIDER_DOWN_BY_TKAP,
                    START_FACTORY_RESET,
                    START_Z_FLIP_LCD_TEST,
                    START_Z_FLIP_DIGITIZER_TEST,
                    END_Z_FLIP_TEST
            ));

    private String key;
    private AndroidSysLogKeyType type;
    private boolean working;

    public AndroidSysLogKey(final String key, final AndroidSysLogKeyType type, final boolean working) {
        this.key = key;
        this.type = type;
        this.working = working;
    }

    /**
     * Method to get the SysLogKey object from the string value
     *
     * @param key string value
     * @return SysLog Object
     */
    public static AndroidSysLogKey fromKey(String key) {
        for (AndroidSysLogKey status : ANDROID_SYS_LOG_KEYS) {
            if (status.key.equalsIgnoreCase(key)) {
                return status;
            }
        }
        return null;
    }

    /**
     * Method to add a single key to the SysLogKeys list
     *
     * @param key SysLog object
     */
    public static void addKey(AndroidSysLogKey key) {
        ANDROID_SYS_LOG_KEYS.add(key);
    }

    /**
     * Method to add a multiple keys to the SysLogKeys list
     *
     * @param keys SysLog objects list
     */
    public static void addKeys(List<AndroidSysLogKey> keys) {
        ANDROID_SYS_LOG_KEYS.addAll(keys);
    }

    /**
     * Method get list of syslog keys with specific type
     *
     * @param sysLogKeyType SysLog Key Type
     * @return list of SysLogKey
     */
    public static List<AndroidSysLogKey> getKeysByType(AndroidSysLogKeyType sysLogKeyType) {
        return ANDROID_SYS_LOG_KEYS.stream()
                .filter(sysLogKey -> sysLogKey.getType() == sysLogKeyType)
                .collect(Collectors.toList());
    }

}
