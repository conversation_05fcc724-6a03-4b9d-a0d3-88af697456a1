package com.phonecheck.model.ios;

import lombok.Getter;

public enum LicenseType {
    ERASE("Device Erase", "deviceEraseLicense"),
    APP_INSTALL("App Installation", "appInstallLicense"),
    ESN("Check ESN", "checkEsnLicense"),
    DEVICE("Device Connection", "deviceConnectionLicense"),
    ICLOUD("Check ICloud", "checkIcloudLicense"),
    OEM("Check OEM", "checkOemLicense"),
    IWATCH("Check IWatch", "iWatchLicense");

    final private String description;

    @Getter
    final private String localizedKey;

    LicenseType(String description, final String localizedKey) {
        this.description = description;
        this.localizedKey = localizedKey;
    }

    public String getDescription() {
        return this.description;
    }

    public static LicenseType licenseTypeFromString(final String type) {
        try {
            return LicenseType.valueOf(type);
        } catch (IllegalArgumentException exception) {
            return null;
        }
    }
}
