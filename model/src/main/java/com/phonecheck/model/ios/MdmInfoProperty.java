package com.phonecheck.model.ios;

public enum MdmInfoProperty {
    ALLOW_PAIRING("AllowPairing"),
    CLOUD_CONFIGURATION_UI_COMPLETE("CloudConfigurationUIComplete"),
    CONFIGURATION_WAS_APPLIED("ConfigurationWasApplied"),
    MDM_UNREMOVABLE("IsMDMUnremovable"),
    IS_MANDATORY("IsMandatory"),
    IS_SUPERVISED("IsSupervised"),
    ORGANIZATION_NAME("OrganizationName"),
    STATUS("Status"),
    CONFIGURATION_SOURCE("ConfigurationSource"),
    POST_SETUP_PROFILE_WAS_INSTALLED("PostSetupProfileWasInstalled"),
    MDM_STATUS("MDMStatus"),
    CONFIGURATION_URL("ConfigurationURL");
    private final String name;

    MdmInfoProperty(final String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static MdmInfoProperty getByName(final String name) {
        for (MdmInfoProperty e : MdmInfoProperty.values()) {
            if (e.name.equals(name)) {
                return e;
            }
        }
        return null;
    }
}

