package com.phonecheck.model.status;

import lombok.Getter;

/**
 * Possible "working" statuses for a device
 */
@Getter
public enum WorkingStatus {
    YES("Yes"),
    NO("No"),
    PENDING("Pending"),
    SKIPPED("Skipped");

    private final String key;

    WorkingStatus(String key) {
        this.key = key;
    }

    public static WorkingStatus fromKey(String key) {
        for (WorkingStatus status : values()) {
            if (status.key.equalsIgnoreCase(key)) {
                return status;
            }
        }
        return null;
    }
}
