package com.phonecheck.model.port;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class PortMapping {
    private List<Port> ports;

    @JsonAnySetter
    public void addPort(final String locationId, final int portNumber) {
        if (ports == null) {
            ports = new ArrayList<>();
        }

        Port port = new Port();
        port.setLocationId(locationId);
        port.setPortNumber(portNumber);

        this.ports.add(port);
    }
}
