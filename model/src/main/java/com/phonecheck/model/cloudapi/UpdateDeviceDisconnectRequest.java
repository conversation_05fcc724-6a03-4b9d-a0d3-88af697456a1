package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class UpdateDeviceDisconnectRequest {
    @JsonProperty("TransactionID")
    private int transactionId;
    @JsonProperty("LicenseID")
    private String licenseId;
    @JsonProperty("Serial")
    private String serial;
    @JsonProperty("deviceDisconnect")
    private String deviceDisconnect;
}
