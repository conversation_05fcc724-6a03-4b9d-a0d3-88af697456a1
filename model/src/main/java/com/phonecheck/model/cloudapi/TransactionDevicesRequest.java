package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionDevicesRequest {
    @JsonProperty("TransactionID")
    private Integer transactionId;
    @JsonProperty("LicenseID")
    private Integer licenseId;
    @JsonProperty("offset")
    private Integer offset;
    @JsonProperty("limit")
    private Integer limit;
}
