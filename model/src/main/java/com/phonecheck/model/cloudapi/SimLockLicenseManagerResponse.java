package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimLockLicenseManagerResponse {

    @JsonProperty("API")
    private String api;

    @JsonProperty("Remarks")
    private String remarks;

    @JsonProperty("FieldColor")
    private String fieldColor;

    @JsonProperty("deviceid")
    private String deviceId;

    @JsonProperty("Carrier")
    private String carrier;

    @JsonProperty("RawResponse")
    private String rawResponse;

    @JsonProperty("fallbackcall")
    private String fallbackCall;

    @JsonProperty("chargeStatus")
    private String chargeStatus;
    private String message;
    private Boolean isLicenseExpired;
}
