package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class VendorInvoiceInfoResponse {
    @JsonProperty("msg")
    private String msg;
    // object because you can get either a string or a ResponseData object
    @JsonProperty("response")
    private Object response;

    @Data
    public static class ResponseData {
        @JsonProperty("All Vendor Invoices")
        private List<VendorInvoiceInfoResponse.VendorInvoice> allVendorInvoices;
        @JsonProperty("Assigned Vendor Invoice To Station")
        private VendorInvoiceInfoResponse.VendorInvoice assignedVendorInvoice;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VendorInvoice {
        @JsonProperty("vendor")
        private String vendor;
        @JsonProperty("invoice")
        private String invoice;
    }
}
