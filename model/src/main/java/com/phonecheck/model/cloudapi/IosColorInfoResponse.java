package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class IosColorInfoResponse {
    private String version;
    @JsonProperty("ColorInfo")
    private List<IosColorInfo> iosColorInfos;

    @Data
    public static class IosColorInfo {
        @JsonProperty("color_id")
        private String colorId;
        @JsonProperty("device_title")
        private String deviceTitle;
        private String imei;
        @JsonProperty("serial_number")
        private String serialNumber;
        @JsonProperty("model_number")
        private String modelNumber;
        private String color;

    }
}
