package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Cloud3DbSyncResponse {
    @JsonProperty("success")
    private boolean success;

    @JsonProperty("msg")
    private String message;

    @JsonProperty("data")
    private ResponseData data;

    @JsonProperty("errors")
    private Object errors;

    @JsonProperty("error")
    private String error;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResponseData {
        @JsonProperty("transactionRecordId")
        private int transactionRecordId;
    }
}
