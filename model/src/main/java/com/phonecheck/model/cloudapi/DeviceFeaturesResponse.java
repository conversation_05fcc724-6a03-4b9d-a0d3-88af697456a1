package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceFeaturesResponse {
    @JsonProperty("ProductType")
    private String productType;
    @JsonProperty("ModelName")
    private String modelName;
    @JsonProperty("BDID")
    private String bdId;
    @JsonProperty("CPID")
    private String cpId;
    @JsonProperty("HeadSet")
    private boolean headSet;
    @JsonProperty("HeadsetLightPort")
    private boolean headsetLightPort;
    @JsonProperty("TelePhoto")
    private boolean telePhoto;
    @JsonProperty("UltraWide")
    private boolean ultraWide;
    @JsonProperty("DigitizerNotch")
    private boolean digitizerNotch;
    @JsonProperty("ManualDigitizerNotch")
    private boolean manualDigitizerNotch;
    @JsonProperty("FlipSwitch")
    private boolean flipSwitch;
    @JsonProperty("SkipHomeButton")
    private boolean skipHomeButton;
    @JsonProperty("WirelessCharging")
    private boolean wirelessCharging;
    @JsonProperty("EarPiece")
    private boolean earPiece;
    @JsonProperty("model_id")
    private String modelId;
    @JsonProperty("LiDAR")
    private boolean lidar;
}
