package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EsnResponse {
    private EsnApiResponse[] esnApiResults;
    private EsnLicensesExpiredResponse licensesExpiredResponse;
    private UsInsuranceBlackListInfo usInsuranceBlackListInfo;
    private TracFoneStraightTalkAPIResponse tracFoneStraightTalkAPIResponse;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EsnApiResponse {
        @JsonProperty("API")
        private String api;
        @JsonProperty("Remarks")
        private String remarks;
        @JsonProperty("FieldColor")
        private String fieldColor;
        @JsonProperty("deviceid")
        private String imei;
        @JsonProperty("Carrier")
        private String carrier;
        // rawResponse is sometimes a json object and sometimes a string
        @JsonProperty("RawResponse")
        private Object rawResponse;
        @JsonProperty("fallbackcall")
        private String fallbackCall;
        private String chargeStatus;

        @Data
        public static class RawResponse {
            @JsonProperty("refcode")
            private String refCode;
            @JsonProperty("devicetype")
            private String deviceType;
            @JsonProperty("marketingname")
            private String marketingName;
            @JsonProperty("brandname")
            private String brandName;
            @JsonProperty("manufacturer")
            private String manufacturer;
            @JsonProperty("responsestatus")
            private String responseStatus;
            @JsonProperty("blackliststatus")
            private String blacklistStatus;
            @JsonProperty("imeihistory")
            private ImeiHistory[] imeiHistory;
            @JsonProperty("greyliststatus")
            private String greyListStatus;
            @JsonProperty("operatingsys")
            private String operatingSys;
            @JsonProperty("modelname")
            private String modelName;
            @JsonProperty("deviceid")
            private String deviceId;
            @JsonProperty("result")
            private String result;
            @JsonProperty("IMEI1")
            private String imeiOne;
            @JsonProperty("certid")
            private String checkmendId;
        }

        @Data
        public static class RawResponseErrorCheckMend {
            @JsonProperty("errors")
            private Object errors;
            @JsonProperty("IMEI1")
            private String imei_1;
            @JsonProperty("IMEI2")
            private String imei_2;
            @JsonProperty("Serial")
            private String serial;
            @JsonProperty("Overall")
            private String Overall;
        }

        @Data
        public static class ImeiHistory {
            private String action;
            private String date;
            private String by;
            @JsonProperty("Country")
            private String country;
            @JsonProperty("reasoncode")
            private String reasonCode;
            @JsonProperty("reasoncodedesc")
            private String reasonCodeDesc;
        }

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EsnLicensesExpiredResponse {
        @JsonProperty("isLicenseExpired")
        private boolean isLicenseExpired;
        @JsonProperty("message")
        private String message;
        @JsonProperty("FieldColor")
        private String fieldColor;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UsInsuranceBlackListInfo {
        @JsonProperty("data")
        private BlackListInfo data;
        @JsonProperty("fromCache")
        private boolean fromCache;

        @Data
        public static class BlackListInfo {
            @JsonProperty("IMEI")
            private String IMEI;
            @JsonProperty("Requires Sim")
            private boolean requiresSim;
            @JsonProperty("Reuse Sim")
            private boolean reuseSim;
            @JsonProperty("Model")
            private String model;
            @JsonProperty("Manufacturer")
            private String manufacturer;
            @JsonProperty("Marketing Name")
            private String marketingName;
            @JsonProperty("Is GSM")
            private boolean isGSM;
            @JsonProperty("eSIM Compatible")
            private boolean eSIMCompatible;
            @JsonProperty("SIM Slots")
            private boolean simSlots;
            @JsonProperty("Bands")
            private String bands;
            @JsonProperty("VoLTE Compatible")
            private boolean voLTECompatible;
            @JsonProperty("Compatibility")
            private String compatibility;
            @JsonProperty("Device Type")
            private String deviceType;
            @JsonProperty("blacklistStatus")
            private String blacklistStatus;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TracFoneStraightTalkAPIResponse {
        @JsonProperty("data")
        private TracFoneApiResponse tracFoneApiResponse;
        @JsonProperty("serviceProviders")
        private ServiceProviders[] serviceProviders;
        @JsonProperty("fromCache")
        private boolean fromCache;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class TracFoneApiResponse {
            @JsonProperty("overall")
            private String overall;
            @JsonProperty("imei")
            private String imei;
            @JsonProperty("brand")
            private String brand;
            @JsonProperty("type")
            private String type;
            @JsonProperty("partNo")
            private String partNo;
            @JsonProperty("tracfoneStraightTalkEligibility")
            private String tracfoneStraightTalkEligibility;
            @JsonProperty("simRequired")
            private String simRequired;
            @JsonProperty("reservedLine")
            private String reservedLine;
            @JsonProperty("safeLinkPlan")
            private String safeLinkPlan;
            @JsonProperty("zipCode")
            private String zipCode;
            @JsonProperty("relatedAccount")
            private String relatedAccount;
            @JsonProperty("billingAccount")
            private String billingAccount;
            @JsonProperty("activationDate")
            private String activationDate;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ServiceProviders {
            @JsonProperty("serviceId")
            private String serviceId;
            @JsonProperty("provider")
            private String provider;
        }
    }
}
