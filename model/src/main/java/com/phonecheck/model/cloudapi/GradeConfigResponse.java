package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GradeConfigResponse {
    @JsonProperty("Additional")
    private List<String> additional;
    @JsonProperty("Grades")
    private List<String> grades;
}
