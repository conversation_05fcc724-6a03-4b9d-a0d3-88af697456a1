package com.phonecheck.model.mqtt.messages.iwatch;

import com.phonecheck.model.device.IWatchInfo;
import com.phonecheck.model.mqtt.messages.MqttConnectionMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MQTT message passed to the frontend when iWatch basic info successfully retrieved
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IWatchBasicInfoSuccessMessage extends MqttConnectionMessage {
    private IWatchInfo iWatchInfo;
    private boolean isFetchingBatteryHealth;
}
