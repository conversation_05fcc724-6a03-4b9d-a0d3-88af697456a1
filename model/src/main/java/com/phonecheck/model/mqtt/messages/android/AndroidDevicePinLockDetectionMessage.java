package com.phonecheck.model.mqtt.messages.android;

import com.phonecheck.model.mqtt.messages.GenericMqttRequestMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Raised when device pin lock is detected
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AndroidDevicePinLockDetectionMessage extends GenericMqttRequestMessage {
}
