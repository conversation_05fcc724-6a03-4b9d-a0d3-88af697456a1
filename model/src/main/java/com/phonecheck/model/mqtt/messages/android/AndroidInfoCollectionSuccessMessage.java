package com.phonecheck.model.mqtt.messages.android;

import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.DiskSize;
import com.phonecheck.model.mqtt.messages.MqttInfoCollectedMessage;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.status.SimStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Raised when device information is collected
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AndroidInfoCollectionSuccessMessage extends MqttInfoCollectedMessage {
    private String imei;
    private String imei2;
    private String serial;
    private String region;
    private String modelNo;
    private SimStatus simStatus;
    private String meid;
    private String productType;
    private String model;
    private DiskSize diskSize;
    private String notes;
    private String esn;
    private String ecid;
    private SetupDoneStatus setupDoneStatus;
    private RootedStatus rooted;
    private boolean sdCardPresent;
    private boolean isDeviceDualSimSupported;
    private String releaseType;
    private int batteryHealth;
    private int batteryPercentage;
    private String regulatoryModelNumber;
    private String simTechnology;
    private String simNetwork;
    private String color;
    private String productCode;
    private List<String> deviceColorList;
    private String carrier;
    private String make;
    private String ram;
    private float osMajorVersion;
    private Boolean simLock;
    private String lpn;
    private String custom1;
    private String firmware;
    private String countryOfOrigin;
    private String guid;
    private String infoId;
    private String skuCode;
    private String fccid;
    private Boolean isImeiValidate;
    private AndroidConnectionMode androidConnectionMode;
    private boolean gradePerformed;
    private String eid;
    private String grade;
}
