package com.phonecheck.model.mqtt.messages;

import com.phonecheck.model.device.DeviceLock;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Raised when device lock info is collected
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeviceLockStatusMessage extends GenericMqttRequestMessage {
    private DeviceLock deviceLock;
    private boolean isPinLockEnabled;
}
