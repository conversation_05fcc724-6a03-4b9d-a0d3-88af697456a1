package com.phonecheck.model.mqtt.messages.portmap;

import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.mqtt.messages.GenericMqttRequestMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceBoxesCreatedMessage extends GenericMqttRequestMessage {
    private DeviceConnectionMode deviceConnectionMode;
}
