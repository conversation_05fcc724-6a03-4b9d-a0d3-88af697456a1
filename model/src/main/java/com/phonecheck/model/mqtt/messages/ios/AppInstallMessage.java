package com.phonecheck.model.mqtt.messages.ios;

import com.phonecheck.model.mqtt.messages.GenericMqttRequestMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Raised when app install request is raised from UI
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AppInstallMessage extends GenericMqttRequestMessage {
    private String serial;
    private boolean isManualInstall;
}
