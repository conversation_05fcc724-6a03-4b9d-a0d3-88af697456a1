package com.phonecheck.model.mqtt.messages;

import com.phonecheck.model.battery.BatteryInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Mqtt Message to be sent from backend to UI when battery information is collected successfully
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeviceBatteryInfoSuccessMessage extends MqttBatteryInfoCollectedMessage {
    private BatteryInfo batteryInfo;
    private int batteryStateHealth;
    private int batteryPercentage;
    private Boolean batteryDegraded;
}
