package com.phonecheck.model.mqtt.messages.ios;

import com.phonecheck.model.mqtt.messages.MqttActivationSuccessMessage;
import com.phonecheck.model.simcarrier.SimCarrierBundleInfo;
import com.phonecheck.model.status.ActivationStatus;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.status.WorkingStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class IosActivationSuccessMessage extends MqttActivationSuccessMessage {
    private ActivationStatus activationStatus = ActivationStatus.ACTIVATED;
    private WorkingStatus touchIdStatus;
    private Boolean touchIdSupported;
    private WorkingStatus faceIdStatus;
    private Boolean faceIdSupported;
    private RootedStatus rooted;
    private SimCarrierBundleInfo simCarrierBundleInfo;
    private boolean isDeviceEsimOnly;
}
