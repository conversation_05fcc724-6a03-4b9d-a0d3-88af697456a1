package com.phonecheck.model.mqtt.messages;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.twowayapi.ApiCallType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * MQTT message passed to the backend to perform retries for bStar api
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class TwoWayApiRetriesRequestMessage extends GenericMqttRequestMessage {
    private String errorTitle;
    private ObjectNode requestObject;
    private ApiCallType apiCallType;
    private PrintOperation printOperation;
}