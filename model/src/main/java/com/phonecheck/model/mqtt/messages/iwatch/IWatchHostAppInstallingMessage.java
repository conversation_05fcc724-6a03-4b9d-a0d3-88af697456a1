package com.phonecheck.model.mqtt.messages.iwatch;

import com.phonecheck.model.mqtt.messages.MqttConnectionMessage;
import com.phonecheck.model.status.AppInstallStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MQTT message passed to the frontend when app installation process is starting
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IWatchHostAppInstallingMessage extends MqttConnectionMessage {
}
