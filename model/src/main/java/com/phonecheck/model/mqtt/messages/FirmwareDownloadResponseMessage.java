package com.phonecheck.model.mqtt.messages;

import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class FirmwareDownloadResponseMessage extends GenericMqttRequestMessage {
    // Progress: 0-99 (in progress), 100 (complete), -1 (failed)
    private String firmwareId;
    private FirmwareDownloadStatus firmwareDownloadStatus;
    private int progress;
}
