package com.phonecheck.model.phonecheckapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class LabelFxmlResponse {
    @JsonProperty("status")
    private String status;

    @JsonProperty("msg")
    private String message;

    @JsonProperty("data")
    private List<Data> dataList;

    @lombok.Data
    public static class Data {
        @JsonProperty("id")
        private int id;
        @JsonProperty("name")
        private String name;
        @JsonProperty("fxml")
        private String fxml;
        @JsonProperty("fxml_v2")
        private String fxmlV2;
        @JsonProperty("orientation")
        private String orientation;
        @JsonProperty("image")
        private String imageUrl;
        @JsonProperty("updated_at")
        private String updatedAt;
        @JsonProperty("canvas_size")
        private String canvasSize;
    }
}