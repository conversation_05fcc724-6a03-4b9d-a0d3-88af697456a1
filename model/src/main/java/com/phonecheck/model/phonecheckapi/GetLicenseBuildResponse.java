package com.phonecheck.model.phonecheckapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class GetLicenseBuildResponse {
    @JsonProperty("macOS")
    private LicenseBuildInfoForOs macOs;

    @JsonProperty("macOS_m1")
    private LicenseBuildInfoForOs macOsM1;

    @JsonProperty("Windows")
    private LicenseBuildInfoForOs windows;

    @JsonProperty("Linux")
    private LicenseBuildInfoForOs linux;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @ToString
    public static class LicenseBuildInfoForOs {
        private String version;
        @JsonProperty("is_stable")
        private Boolean isStable;
        private String changings;
        @JsonProperty("fileURL")
        private String fileUrl;
        private String message;
        @JsonProperty("is_assigned")
        private Boolean isAssigned;
        private Boolean isSilent;
        private Boolean isAppUpdate;
    }
}
