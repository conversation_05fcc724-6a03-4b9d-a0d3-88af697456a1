package com.phonecheck.model.constants;

import lombok.Getter;

@Getter
public enum EsnFieldColor {
    GREEN("Green"),
    RED("Red"),
    YELLOW("Yellow"),
    WHITE("White");

    private final String color;

    EsnFieldColor(final String color) {
        this.color = color;
    }

    public static EsnFieldColor fromColor(String color) {
        for (EsnFieldColor fieldColor : values()) {
            if (fieldColor.color.equalsIgnoreCase(color)) {
                return fieldColor;
            }
        }
        return null;
    }

}
