package com.phonecheck.model.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Provides support file paths for Mac OS
 */
public class MacSupportFilePathsStrategy implements ISupportFilePathsStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(MacSupportFilePathsStrategy.class);
    private final boolean appStartedFromInstaller;

    public MacSupportFilePathsStrategy(final boolean appStartedFromInstaller) {
        this.appStartedFromInstaller = appStartedFromInstaller;
    }

    @Override
    public String getRootFolderPath() {
        return System.getProperty("user.home") + "/Library/Application Support/PhoneCheck3";
    }

    @Override
    public String getToolsRootFolderPath() {
        return String.join(
                "/",
                getWorkingDirectoryPath(),
                appStartedFromInstaller ? ".res" : "res",
                "tools",
                "macOS"
        );
    }

    @Override
    public String getFilesRootFolderPath() {
        return String.join(
                "/",
                getWorkingDirectoryPath(),
                appStartedFromInstaller ? ".res" : "res",
                "files"
        );
    }

    @Override
    public String getWorkingDirectoryPath() {
        String path;

        if (appStartedFromInstaller) {
            // Sample classPath value
            // /Users/<USER>/.../PhoneCheck3.app/Contents/app/phonecheck-backend-0.0.1-SNAPSHOT.jar/
            String classPath = System.getProperty(CLASS_PATH);
            Path contentsPath = Paths.get(classPath).getParent().getParent();
            path = contentsPath.resolve("app").toString();
        } else {
            // user.dir property returns the current working directory, and it may differ depending on starting
            // point of our application. i.e. It's root dir of our project if we start our app through intellij's
            // run button and if we start it through gradle tasks it is root-dir/app-module-name/
            String workingDirectory = System.getProperty(USER_DIR);
            Path workingDirectoryPath = Paths.get(workingDirectory);

            path = workingDirectoryPath.endsWith(BACKEND_MODULE_NAME)
                    || workingDirectoryPath.endsWith(UI_MODULE_NAME)
                    ? workingDirectoryPath.getParent().toString()
                    : workingDirectoryPath.toString();
        }

        return path;
    }
}
