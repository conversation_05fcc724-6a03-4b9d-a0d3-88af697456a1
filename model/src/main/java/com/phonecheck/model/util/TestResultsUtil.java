package com.phonecheck.model.util;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.JapaneseConformityStatus;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.test.CosmeticsResults;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.test.TestResults;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Test results utility to prepend initial defects in test result fields
 * based on statuses.
 */
public class TestResultsUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(TestResultsUtil.class);

    /**
     * Updates test results of provided device, this method will always recreate the
     * failed and passed test results values based on initial defects statuses and the
     * provided failedResults and passedResults keys
     *
     * @param device             target device
     * @param failedResults      device failed test results
     * @param passedResults      device passed test results
     * @param cloudCustomization cloud customization
     */
    public static void updateTestResultsInDevice(final Device device,
                                                 final List<String> failedResults,       // only failed app results
                                                 final List<String> passedResults,       // only passed app results
                                                 final CloudCustomizationResponse cloudCustomization,
                                                 final LocalizationService localizationService) {

        if (device.getDeviceTestResult() == null) {
            device.setDeviceTestResult(new DeviceTestResult());
        }
        if (device.getDeviceTestResult().getTestResults() == null) {
            device.getDeviceTestResult().setTestResults(new TestResults());
        }

        LOGGER.info("Passed test results : {} ; Failed test results : {}", passedResults, failedResults);
        device.getDeviceTestResult().getTestResults().
                setPassed(passedResults);
        device.getDeviceTestResult().getTestResults().
                setPassedCount(String.valueOf(passedResults.size()));

        device.getDeviceTestResult().getTestResults().
                setFailed(failedResults);
        device.getDeviceTestResult().getTestResults().
                setFailedCount(String.valueOf(failedResults.size()));

        prependInitialDefectsToAppResults(device, cloudCustomization, localizationService);
        LOGGER.info("After updated initial defects: Passed test results : {} ; Failed test results : {}",
                device.getDeviceTestResult().getTestResults().getPassed(),
                device.getDeviceTestResult().getTestResults().getFailed());
    }

    /**
     * Updates cosmetic test results of provided device, this method will always recreate the
     * failed and passed cosmetic test results values based on the
     * provided failedCosmeticResults and passedCosmeticResults keys
     *
     * @param device
     * @param failedCosmeticResults
     * @param passedCosmeticResults
     */
    public static void updateCosmeticResultsInDevice(final Device device,
                                                     final List<String> failedCosmeticResults,
                                                     final List<String> passedCosmeticResults) {

        if (device.getDeviceTestResult().getCosmeticResults() == null) {
            device.getDeviceTestResult().setCosmeticResults(new CosmeticsResults());
        }

        LOGGER.info("Passed Cosmetic test results : {} ; Failed Cosmetic test results : {}",
                passedCosmeticResults, failedCosmeticResults);

        device.getDeviceTestResult().getCosmeticResults().
                setPassed(StringUtils.join(passedCosmeticResults, ","));
        device.getDeviceTestResult().getCosmeticResults().
                setPassedCount(String.valueOf(passedCosmeticResults.size()));

        device.getDeviceTestResult().getCosmeticResults().
                setFailed(StringUtils.join(failedCosmeticResults, ","));
        device.getDeviceTestResult().getCosmeticResults().
                setFailedCount(String.valueOf(failedCosmeticResults.size()));

    }

    /**
     * Checks if a specific key exists within the test plan for a given device type.
     *
     * @param key                The key to search for within the test plan.
     * @param device             The device type (Android or Apple) to determine which test plan to use.
     * @param cloudCustomization The cloud customization object containing the test plan data.
     * @return {@code true} if the key exists in the test plan for the specified device or in the shared test plan;
     * {@code false} otherwise.
     */
    public static boolean isKeyExistInTestPlan(
            final String key,
            final Device device,
            final CloudCustomizationResponse cloudCustomization) {

        // Retrieve the test plan from the cloud customization response
        CloudCustomizationResponse.TestPlan testPlan = cloudCustomization != null
                ? cloudCustomization.getTestPlan()
                : null;
        LOGGER.info("Finding key {} in test plan", key);

        // Return false immediately if the test plan is null
        if (testPlan == null) {
            return false;
        }

        // Get the test list for the specific device type; default to 'both' list if specific list is null
        List<CloudCustomizationResponse.DeviceTest> deviceTestsList =
                Optional.ofNullable(device instanceof AndroidDevice
                                ? testPlan.getAndroid()
                                : testPlan.getApple())
                        .orElse(testPlan.getBoth());

        // Return true if the key exists in the retrieved device test list
        return deviceTestsList != null && StringUtils.containsIgnoreCase(deviceTestsList.toString(), key);
    }

    /**
     * Prepends initial defects to the app test results
     *
     * @param device             target device
     * @param cloudCustomization cloud customization
     */
    public static void prependInitialDefectsToAppResults(final Device device,
                                                         final CloudCustomizationResponse cloudCustomization,
                                                         final LocalizationService localizationService) {
        List<String> failedDeviceResults =
                device.getDeviceTestResult().getTestResults().getFailed() != null ?
                        device.getDeviceTestResult().getTestResults().getFailed() : new ArrayList<>();
        List<String> passedDeviceResults =
                device.getDeviceTestResult().getTestResults().getPassed() != null ?
                        device.getDeviceTestResult().getTestResults().getPassed() : new ArrayList<>();
        List<String> failedInitialDefects = new ArrayList<>();
        List<String> passedInitialDefects = new ArrayList<>();

        // TODO: fix the no-imei defect logic
        // if (StringUtils.isBlank(device.getImei()) &&
        //        !failedDeviceResults.contains(InitialDefectKey.NO_IMEI.getKey())
        //        && DeviceType.IPHONE.equals(device.getDeviceType())) {
        //    failedInitialDefects.append(InitialDefectKey.NO_IMEI.getKey()).append(",");
        // }

        InitialDefectKey batteryHealthCriteria = FunctionalityStatusUtil.getBatteryHealthCriteria(device,
                cloudCustomization,
                false);

        if (batteryHealthCriteria != null && !failedDeviceResults.contains(batteryHealthCriteria.getKey())) {
            failedInitialDefects.add(batteryHealthCriteria.getKey());
        }

        boolean isDeviceLockEnabled =
                isKeyExistInTestPlan(InitialDefectKey.DEVICE_LOCK.getKey(), device, cloudCustomization);

        if (!failedDeviceResults.contains(InitialDefectKey.DEVICE_LOCK.getKey())
                && FunctionalityStatusUtil.isDeviceLockOn(device) && isDeviceLockEnabled) {
            failedInitialDefects.add(InitialDefectKey.DEVICE_LOCK.getKey());
        } else if (failedDeviceResults.contains(InitialDefectKey.DEVICE_LOCK.getKey())
                && (!FunctionalityStatusUtil.isDeviceLockOn(device) || !isDeviceLockEnabled)) {
            failedDeviceResults.remove(InitialDefectKey.DEVICE_LOCK.getKey());
        }

        if (!failedDeviceResults.contains(InitialDefectKey.KNOX.getKey())
                && FunctionalityStatusUtil.isKnoxOn(device)) {
            failedInitialDefects.add(InitialDefectKey.KNOX.getKey());
        } else if (failedDeviceResults.contains(InitialDefectKey.KNOX.getKey())
                && !FunctionalityStatusUtil.isKnoxOn(device)) {
            failedDeviceResults.remove(InitialDefectKey.KNOX.getKey());
        }

        boolean isMDMEnabled = isKeyExistInTestPlan(InitialDefectKey.MDM.getKey(), device, cloudCustomization);

        if (!failedDeviceResults.contains(InitialDefectKey.MDM.getKey())
                && FunctionalityStatusUtil.isMdmOn(device) && isMDMEnabled) {
            failedInitialDefects.add(InitialDefectKey.MDM.getKey());
        } else if (failedDeviceResults.contains(InitialDefectKey.MDM.getKey())
                && (!FunctionalityStatusUtil.isMdmOn(device) || !isMDMEnabled)) {
            failedDeviceResults.remove(InitialDefectKey.MDM.getKey());
        }
        if (!failedDeviceResults.contains(InitialDefectKey.BAD_ESN.getKey())
                && FunctionalityStatusUtil.isESNBad(device)) {
            failedInitialDefects.add(InitialDefectKey.BAD_ESN.getKey());
        } else if (failedDeviceResults.contains(InitialDefectKey.BAD_ESN.getKey())
                && StringUtils.isNotBlank(device.getEsnStatus()) && !FunctionalityStatusUtil.isESNBad(device)) {
            failedDeviceResults.remove(InitialDefectKey.BAD_ESN.getKey());
        }

        if (!failedDeviceResults.contains(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey()) &&
                Boolean.FALSE.equals(device.getIsImeiValidate())) {
            failedInitialDefects.add(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey());
        } else if (failedDeviceResults.contains(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey())
                && Boolean.TRUE.equals(device.getIsImeiValidate())) {
            failedDeviceResults.remove(InitialDefectKey.IMEI_SERIAL_MISMATCH.getKey());
        }

        if (JapaneseConformityStatus.NOT_CERTIFIED.equals(device.getJapaneseConformityStatus()) &&
                !device.getDeviceTestResult().getTestResults().getPassed().contains(
                        localizationService.getLanguageSpecificText(InitialDefectKey
                                .MANUAL_JAPANESE_CONFORMITY_MARK.getKey())
                )) {
            if (!failedDeviceResults.contains(localizationService.getLanguageSpecificText(InitialDefectKey
                    .JAPANESE_CONFORMITY_MARK.getKey()))) {
                failedInitialDefects.add(localizationService.getLanguageSpecificText(InitialDefectKey
                        .JAPANESE_CONFORMITY_MARK.getKey()));
            }
        } else if (JapaneseConformityStatus.CERTIFIED.equals(device.getJapaneseConformityStatus()) &&
                !device.getDeviceTestResult().getTestResults().getFailed().contains(
                        localizationService.getLanguageSpecificText(InitialDefectKey
                                .MANUAL_JAPANESE_CONFORMITY_MARK.getKey()))) {
            if (!passedDeviceResults.contains(localizationService.getLanguageSpecificText(InitialDefectKey
                    .JAPANESE_CONFORMITY_MARK.getKey()))) {
                passedInitialDefects.add(localizationService.getLanguageSpecificText(InitialDefectKey
                        .JAPANESE_CONFORMITY_MARK.getKey()));
            }
        }

        // Prepend IOS specific defects in failed / passed results
        if (device instanceof IosDevice iosDevice) {
            if (iosDevice.getTouchIdSensor() != null) {
                if (!passedDeviceResults.contains(InitialDefectKey.TOUCH_ID.getKey())
                        && iosDevice.getTouchIdSensor().equals(WorkingStatus.YES) &&
                        !failedDeviceResults.contains(InitialDefectKey.MANUAL_TOUCH_ID.getKey())) {
                    passedInitialDefects.add(InitialDefectKey.TOUCH_ID.getKey());
                }
                if (!failedDeviceResults.contains(InitialDefectKey.TOUCH_ID.getKey())
                        && iosDevice.getTouchIdSensor().equals(WorkingStatus.NO)) {
                    failedInitialDefects.add(InitialDefectKey.TOUCH_ID.getKey());
                }
            }

            if (!failedDeviceResults.contains(InitialDefectKey.BETA_OS.getKey())
                    && StringUtils.containsIgnoreCase(iosDevice.getReleaseType(), "beta")) {
                failedInitialDefects.add(InitialDefectKey.BETA_OS.getKey());
            }
        }

        if (!failedDeviceResults.contains(InitialDefectKey.BATTERY_DEGRADED.getKey())
                && FunctionalityStatusUtil.isBatteryDegraded(device, cloudCustomization)) {
            failedInitialDefects.add(InitialDefectKey.BATTERY_DEGRADED.getKey());
        }

        // Prepend defects in failed / passed results came from phonecheck mobile application
        failedDeviceResults.addAll(0, failedInitialDefects);
        passedDeviceResults.addAll(0, passedInitialDefects);

        Collections.sort(failedDeviceResults);
        Collections.sort(passedDeviceResults);
    }


    /**
     * Check Face ID key in Test Plan
     *
     * @param customization client's test plan
     * @return Boolean true if Test plan has Face ID Key, false otherwise
     */
    public static boolean hasFaceIDKeyInTestPlan(CloudCustomizationResponse customization) {

        if (customization == null || customization.getTestPlan() == null) {
            return Boolean.FALSE;
        }

        List<CloudCustomizationResponse.DeviceTest> testPlan = customization.getTestPlan().getApple() != null ?
                customization.getTestPlan().getApple() : customization.getTestPlan().getBoth();

        if (testPlan == null) {
            return Boolean.FALSE;
        }

        return testPlan.stream().anyMatch(plan -> InitialDefectKey.FACE_ID.getKey().equalsIgnoreCase(plan.getKey()));
    }

    /**
     * Method to verify if the test results have updated
     *
     * @param deviceTestResultFromFile   new test results
     * @param deviceTestResultFromDevice test results in the device object
     * @return true/false
     */
    public static boolean didTestResultsChange(final DeviceTestResult deviceTestResultFromFile,
                                               final DeviceTestResult deviceTestResultFromDevice) {
        // if there were no test results in the device, then we want to run automation
        if (deviceTestResultFromDevice == null) {
            return true;
        }

        boolean shouldCheckBatteryDrain = (deviceTestResultFromDevice.getBatteryResults() != null &&
                deviceTestResultFromDevice.getBatteryResults().getBatteryDrain() != null) ||
                (deviceTestResultFromFile.getBatteryResults() != null &&
                        deviceTestResultFromFile.getBatteryResults().getBatteryDrain() != null);
        LOGGER.info("Should check battery drain: {}", shouldCheckBatteryDrain);

        boolean isFailedListEqual = areListsEqual(deviceTestResultFromFile.getTestResults().getFailed(),
                deviceTestResultFromDevice.getTestResults().getFailed());
        LOGGER.info("Failed results equal: {}", isFailedListEqual);
        boolean isPendingListEqual = areListsEqual(deviceTestResultFromFile.getTestResults().getPending(),
                deviceTestResultFromDevice.getTestResults().getPending());
        LOGGER.info("Pending results equal: {}", isPendingListEqual);
        boolean isPassedListEqual = areListsEqual(deviceTestResultFromFile.getTestResults().getPassed(),
                deviceTestResultFromDevice.getTestResults().getPassed());
        LOGGER.info("Passed results equal: {}", isPassedListEqual);
        boolean isNotSupportedListEqual = areListsEqual(deviceTestResultFromFile.getTestResults().getNotSupported(),
                deviceTestResultFromDevice.getTestResults().getNotSupported());
        LOGGER.info("NotSupported results equal: {}", isNotSupportedListEqual);
        boolean isTotalListEqual = areListsEqual(deviceTestResultFromFile.getTestResults().getTotal(),
                deviceTestResultFromDevice.getTestResults().getTotal());
        LOGGER.info("Total results equal: {}", isTotalListEqual);

        LOGGER.info("Grade in test results from app is: {}", deviceTestResultFromFile.getGradeResults());
        LOGGER.info("Grade in test results on device is: {}", deviceTestResultFromDevice.getGradeResults());
        boolean isGradeEqual = StringUtils.isBlank(deviceTestResultFromFile.getGradeResults()) ||
                deviceTestResultFromDevice.getGradeResults().equals(deviceTestResultFromFile.getGradeResults());
        // if any of the lists above have changed, then we want to run automation
        return !isGradeEqual ||
                !(isFailedListEqual && isPendingListEqual &&
                        isPassedListEqual && isNotSupportedListEqual && isTotalListEqual) ||
                (shouldCheckBatteryDrain &&
                        !deviceTestResultFromFile.getBatteryResults()
                                .equals(deviceTestResultFromDevice.getBatteryResults()));
    }

    /**
     * Check if the test list strings are equal
     *
     * @param testListFromFile testlist from file
     * @param deviceTestList   testlist from device
     * @return true/false
     */
    private static boolean areListsEqual(final List<String> testListFromFile,
                                         final List<String> deviceTestList) {
        LOGGER.info("Lists to compare: {} || {}", testListFromFile, deviceTestList);


        Set<String> set1 = new HashSet<>(testListFromFile);
        Set<String> set2 = new HashSet<>(deviceTestList);

        boolean areSetsEqual = set1.equals(set2);
        LOGGER.info("Are Lists equal: {}", areSetsEqual);

        return areSetsEqual;
    }

    /**
     * Checks if all strings in a list are present in a comma-separated target string.
     *
     * @param listOfStrings the list of strings to check for presence
     * @param targetString  the target string in which to check for presence, as a comma-separated list
     * @return Boolean
     */
    public static boolean areAllStringsPresent(final List<String> listOfStrings, final String targetString) {
        LOGGER.info("List of strings {} present in target list", listOfStrings);
        List<String> targetList = Arrays.stream(targetString.split(","))
                .map(String::trim)
                .toList();
        return listOfStrings.stream()
                .allMatch(str -> targetList.stream()
                        .anyMatch(target -> StringUtils.equalsIgnoreCase(target, str)));
    }

    /**
     * Converts a comma-separated test results string into a List<String>.
     * Ensures an empty list is returned if the input is null or empty.
     * @param csv Comma separated values
     * @return  List
     */
    public static List<String> commaSeparatedStringToList(String csv) {
        return Optional.ofNullable(csv)
                .filter(s -> !s.isEmpty())
                .map(s -> new ArrayList<>(Arrays.asList(s.split(","))))
                .orElseGet(ArrayList::new);
    }

    /**
     * Converts a List<String> test results list (CSV) into a
     * comma-separated test results string.
     * Ensures an empty string is returned if the input is null or empty.
     * @param listOfStrings List of test results
     * @return  Comma separated test results string
     */
    public static String listToCommaSeparatedString(List<String> listOfStrings) {
        return Optional.ofNullable(listOfStrings)
                .filter(l -> !l.isEmpty())
                .map(l -> String.join(",", l))
                .orElse("");
    }
}
