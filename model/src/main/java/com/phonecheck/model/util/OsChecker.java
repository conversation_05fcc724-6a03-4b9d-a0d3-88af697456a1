package com.phonecheck.model.util;

import lombok.Getter;
import org.springframework.boot.info.OsInfo;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * Caches operating system information
 */
@Component
@Getter
public class OsChecker {
    private final OsInfo osInfo;
    private boolean isWindows;
    private boolean isMac;
    private boolean isLinux;
    private Os os;

    public OsChecker() {
        osInfo = new OsInfo();
        String platform = osInfo.getName().toLowerCase(Locale.ROOT);
        if (platform.contains("windows")) {
            isWindows = true;
            os = Os.WINDOWS;
        } else if (platform.contains("mac")) {
            isMac = true;
            os = Os.MAC;
        } else if (platform.contains("nix") || platform.contains("nux") || platform.contains("aix")) {
            isLinux = true;
            os = Os.LINUX;
        }
    }

    public String getName() {
        return osInfo.getName();
    }

    public String getVersion() {
        return osInfo.getVersion();
    }

    public String getArchitecture() {
        return osInfo.getArch();
    }
}
