package com.phonecheck.model.util;

import com.phonecheck.model.device.RunningMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

import static com.phonecheck.model.constants.FileConstants.RUNNING_MODE_FILE_NAME;

/**
 * Read and write Phonecheck 3 running mode in the file
 */
@Component
public class RunningModeUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(RunningModeUtil.class);
    private final FileUtil fileUtil;
    private final String runModeFilePath;

    public RunningModeUtil(final SupportFilePath supportFilePath, final FileUtil fileUtil) {
        this.fileUtil = fileUtil;
        this.runModeFilePath = String.join("/",
                supportFilePath.getPaths().getRootFolderPath(), RUNNING_MODE_FILE_NAME);
    }

    /**
     * Save Running mode in a local file
     * @param mode running mode
     */
    public void saveModeInFile(final RunningMode mode) {
        try {
            File file = fileUtil.isFileExists(runModeFilePath) ? fileUtil.getFileFromPath(runModeFilePath)
                    : fileUtil.createFile(runModeFilePath);
            fileUtil.writeStringToFile(file, mode.getKey());
        } catch (final IOException e) {
            LOGGER.error("Error saving running mode in the support path");
        }
    }

    /**
     * Get Running mode from Support Path
     *
     * @return run mode
     */
    public RunningMode getRunningModeFromFile() {
        try {
            File runModeFile = loadRunModeFile();
            String key = fileUtil.readFile(runModeFile);
            // Match the key to the corresponding RunningMode enum
            for (RunningMode mode : RunningMode.values()) {
                if (mode.getKey().equals(key)) {
                    return mode;
                }
            }
        } catch (Exception exception) {
            LOGGER.error("Error while reading running mode from SupportPath", exception);
        }
        return RunningMode.DEFAULT_MODE;
    }

    /**
     * Load the running mode file
     * @return file
     */
    private File loadRunModeFile() {
        File runModeFile = new File(runModeFilePath);
        if (!runModeFile.exists()) {
            LOGGER.info("No running mode set, creating file with default setting now");
            saveModeInFile(RunningMode.DEFAULT_MODE);
        }
        return runModeFile;
    }
}
