package com.phonecheck.model.util;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Provides support file paths for Windows
 */
public class WindowsSupportFilePathsStrategy implements ISupportFilePathsStrategy {
    private final boolean appStartedFromInstaller;

    public WindowsSupportFilePathsStrategy(final boolean appStartedFromInstaller) {
        this.appStartedFromInstaller = appStartedFromInstaller;
    }

    @Override
    public String getRootFolderPath() {
        return System.getenv("LOCALAPPDATA") + File.separator + "PhoneCheck3";
    }

    @Override
    public String getToolsRootFolderPath() {
        return String.join("/", getWorkingDirectoryPath(), "res", "tools", "windows");
    }

    @Override
    public String getFilesRootFolderPath() {
        return String.join("/", getWorkingDirectoryPath(), "res", "files");
    }

    @Override
    public String getWorkingDirectoryPath() {
        String path;

        if (appStartedFromInstaller) {
            // Sample classPath value
            // C:\Program Files\PhoneCheck\app\phonecheck-ui-0.0.1-SNAPSHOT.jar
            String classPath = System.getProperty(CLASS_PATH);
            Path appPath = Paths.get(classPath).getParent();
            path = appPath.toString();
        } else {
            // user.dir property returns the current working directory, and it may differ depending on starting
            // point of our application. i.e. It's root dir of our project if we start our app through intellij's
            // run button and if we start it through gradle tasks it is root-dir/app-module-name/
            String workingDirectory = System.getProperty(USER_DIR);
            Path workingDirectoryPath = Paths.get(workingDirectory);

            path = workingDirectoryPath.endsWith(BACKEND_MODULE_NAME)
                    || workingDirectoryPath.endsWith(UI_MODULE_NAME)
                    ? workingDirectoryPath.getParent().toString()
                    : workingDirectoryPath.toString();
        }

        return path;
    }
}
