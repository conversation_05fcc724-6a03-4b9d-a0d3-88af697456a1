package com.phonecheck.model.twowayapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Represents a request model for Label API.
 */
@Getter
@Setter
@ToString
public class LabelApiRequestModel {
    @JsonProperty("ApiKey")
    private String apiKey;
    @JsonProperty("SerialNumber")
    private String serialNumber;
    @JsonProperty("IMEI")
    private String imei;

    public LabelApiRequestModel(String apiKey, String serialNumber, String imei) {
        this.apiKey = apiKey;
        this.serialNumber = serialNumber;
        this.imei = imei;
    }
}