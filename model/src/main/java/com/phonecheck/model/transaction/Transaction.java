package com.phonecheck.model.transaction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Represents a transaction in the system.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Transaction {
    private int transactionId;
    private String vendorName;
    private String invoiceNo;
    private String boxNo;
    private String qty;
    private String transactionDate;
    private String stationId;
    private int licenseId;
    private boolean isCreatedByCloud;

    /**
     * Constructs a new transaction with the given values.
     *
     * @param vendorName      the vendor name
     * @param invoiceNo       the invoice number
     * @param boxNo           the box number
     * @param qty             the quantity
     * @param transactionDate the date of the transaction
     * @param stationId       the ID of the station where the transaction occurred
     * @param licenseId       the ID of the license used for the transaction
     */
    public Transaction(String vendorName, String invoiceNo, String boxNo, String qty,
                       String transactionDate, String stationId, int licenseId) {
        this.vendorName = vendorName;
        this.invoiceNo = invoiceNo;
        this.boxNo = boxNo;
        this.qty = qty;
        this.transactionDate = transactionDate;
        this.stationId = stationId;
        this.licenseId = licenseId;
    }

    /**
     * Creates a new instance of a transaction with default values, except for the given username and license ID.
     *
     * @param username  the username associated with the transaction
     * @param licenseId the ID of the license used for the transaction
     * @return a new transaction instance
     */
    public static Transaction newInstance(String username, int licenseId) {
        return new Transaction(" ", " ", " ", " ",
                new Date().toString(), username, licenseId);
    }

}