package com.phonecheck.model.event.device;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/** Event to be raised in response for device reprocess button clicked*/
@Getter
public class DeviceReprocessResponseEvent extends AbstractDeviceEvent {
    private final boolean fromReprint;

    public DeviceReprocessResponseEvent(final Object source,
                                        final Device device,
                                        final boolean fromReprint) {
        super(source, device);
        this.fromReprint = fromReprint;
    }
}