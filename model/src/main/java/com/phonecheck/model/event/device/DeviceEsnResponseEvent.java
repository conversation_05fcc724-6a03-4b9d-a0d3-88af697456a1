package com.phonecheck.model.event.device;

import com.phonecheck.model.constants.EsnFieldColor;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.status.EsnStatus;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when ESN has a response
 */
@Getter
@Setter
public class DeviceEsnResponseEvent extends AbstractDeviceEvent {
    private final EsnStatus esnStatus;
    private final EsnFieldColor esnFieldColor;
    private final String esnUIResponse;

    public DeviceEsnResponseEvent(final Object source, final Device device,
                                  final EsnStatus esnStatus,
                                  final EsnFieldColor esnFieldColor,
                                  final String esnUIResponse) {
        super(source, device);
        this.esnStatus = esnStatus;
        this.esnFieldColor = esnFieldColor;
        this.esnUIResponse = esnUIResponse;
    }
}
