package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when android device lock info is successfully retrieved
 */
@Getter
public class AndroidDeviceLockInfoSuccessEvent extends AbstractDeviceEvent {
    public AndroidDeviceLockInfoSuccessEvent(final Object source, final AndroidDevice device) {
        super(source, device);
    }
}

