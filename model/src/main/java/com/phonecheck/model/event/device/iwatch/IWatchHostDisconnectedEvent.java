package com.phonecheck.model.event.device.iwatch;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when iWatch host disconnected
 */
@Setter
@Getter
public class IWatchHostDisconnectedEvent extends AbstractDeviceEvent {
    private boolean isManualRelease;

    public IWatchHostDisconnectedEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
