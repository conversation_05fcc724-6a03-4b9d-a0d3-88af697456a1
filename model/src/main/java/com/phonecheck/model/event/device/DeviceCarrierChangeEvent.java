package com.phonecheck.model.event.device;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Event raised when device carrier is changed on UI
 */
@Getter
public class DeviceCarrierChangeEvent extends AbstractDeviceEvent {
    public DeviceCarrierChangeEvent(final Object source, final Device device) {
        super(source, device);
    }
}
