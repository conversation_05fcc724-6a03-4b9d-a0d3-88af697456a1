package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised for the DONE action from UI. This event will be listened by AndroidSDCardWarningListener
 * which has logic for verifying SD card presence again.
 */
@Getter
@Setter
public class AndroidDeviceSdCardWarningEvent extends AbstractDeviceEvent {
    private boolean showSdCardDefect;
    private boolean eraseRequired;

    public AndroidDeviceSdCardWarningEvent(final Object source,
                                           final Device device,
                                           final boolean showSdCardDefect, final boolean eraseRequired
    ) {
        super(source, device);
        this.showSdCardDefect = showSdCardDefect;
        this.eraseRequired = eraseRequired;
    }
}
