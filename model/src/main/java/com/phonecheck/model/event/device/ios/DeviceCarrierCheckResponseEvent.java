package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.cloudapi.CarrierSimLockStatusResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Event passed to UI with carrier response
 */
@Getter
@Setter
public class DeviceCarrierCheckResponseEvent extends AbstractDeviceEvent {
    private CarrierSimLockStatusResponse.RawResponse carrierResponse;
    private String carrier;
    private boolean simLocked;
    private boolean errorServerResponse;

    public DeviceCarrierCheckResponseEvent(final Object source,
                                           final Device device,
                                           final CarrierSimLockStatusResponse.RawResponse carrierResponse,
                                           final String carrier,
                                           final boolean simLocked,
                                           final boolean errorServerResponse) {
        super(source, device);
        this.carrierResponse = carrierResponse;
        this.carrier = carrier;
        this.simLocked = simLocked;
        this.errorServerResponse = errorServerResponse;
    }
}
