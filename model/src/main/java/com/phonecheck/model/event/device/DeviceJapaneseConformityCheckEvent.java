package com.phonecheck.model.event.device;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when japanese conformity check is required
 */
@Getter
public class DeviceJapaneseConformityCheckEvent extends AbstractDeviceEvent {
    public DeviceJapaneseConformityCheckEvent(final Object source, final Device device) {
        super(source, device);
    }
}
