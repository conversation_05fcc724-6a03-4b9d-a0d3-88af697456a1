package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.status.RestoreResponseCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class IosDeviceRestoreResponseEvent extends AbstractDeviceEvent {
    private final DeviceRestoreStatus restoreStatus;
    private final Long timeTaken;
    private final RestoreResponseCode responseCode;
    private String errorCode;
    private boolean isManualRestore;
    private int attempt;

    public IosDeviceRestoreResponseEvent(final Object source,
                                         final Device device,
                                         final DeviceRestoreStatus restoreStatus,
                                         final RestoreResponseCode responseCode,
                                         final Long timeTaken) {
        super(source, device);
        this.restoreStatus = restoreStatus;
        this.responseCode = responseCode;
        this.timeTaken = timeTaken;
    }


}
