package com.phonecheck.model.event.transaction;

import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.transaction.Transaction;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when continue transaction is requested
 */
@Getter
@Setter
public class TransactionContinueRequestEvent extends AbstractEvent {
    private Transaction transaction;

    public TransactionContinueRequestEvent(final Object source,
                                           final Transaction transaction) {
        super(source);
        this.transaction = transaction;
    }
}

