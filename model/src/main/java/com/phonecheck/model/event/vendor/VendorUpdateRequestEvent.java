package com.phonecheck.model.event.vendor;

import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;

/**
 * Event to update the vendor details
 */
@Getter
public class VendorUpdateRequestEvent extends AbstractEvent {
    private final String vendorName;
    private final String invoiceNo;
    private final String qty;
    private final String boxNo;

    public VendorUpdateRequestEvent(final Object source, final String vendorName,
                                    final String invoiceNo,
                                    final String qty,
                                    final String boxNo) {
        super(source);
        this.vendorName = vendorName;
        this.invoiceNo = invoiceNo;
        this.qty = qty;
        this.boxNo = boxNo;
    }
}
