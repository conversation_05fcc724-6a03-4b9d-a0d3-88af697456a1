package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when device prepare status is notified to UI
 */
@Getter
public class AndroidPrepareStatusEvent extends AbstractDeviceEvent {
    final String failureReason;

    public AndroidPrepareStatusEvent(final Object source,
                                     final AndroidDevice device,
                                     final String failureReason) {
        super(source, device);

        this.failureReason = failureReason;
    }
}
