package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

@Getter
public class IosAppInstallEvent extends AbstractDeviceEvent {
    private final boolean isManualInstall;

    public IosAppInstallEvent(final Object source, final IosDevice device, final boolean isManualInstall) {
        super(source, device);
        this.isManualInstall = isManualInstall;
    }
}
