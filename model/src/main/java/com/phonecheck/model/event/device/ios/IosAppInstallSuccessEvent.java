package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when a device finishes app installation successfully
 */
@Getter
public class IosAppInstallSuccessEvent extends AbstractDeviceEvent {
    public IosAppInstallSuccessEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
