package com.phonecheck.model.event.transaction;

import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.transaction.Transaction;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when transaction details are requested
 */
@Getter
@Setter
public class TransactionDetailsRequestEvent extends AbstractEvent {
    private Transaction transaction;
    private int recordsOffset;

    public TransactionDetailsRequestEvent(final Object source,
                                          final Transaction transaction,
                                          final int recordsOffset) {
        super(source);
        this.transaction = transaction;
        this.recordsOffset = recordsOffset;
    }
}

