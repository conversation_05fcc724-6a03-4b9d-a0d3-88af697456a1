package com.phonecheck.model.event.device.iwatch;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.print.PrintOperation;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised to notify error received post 2-way API call
 *
 */
@Setter
@Getter
public class IWatchTwoWayApiErrorDecisionRequestEvent extends AbstractDeviceEvent {
    private ObjectNode responseObject;
    private ObjectNode requestObject;
    private String errorTitle;
    private boolean fromRetry;
    private PrintOperation printOperation;

    public IWatchTwoWayApiErrorDecisionRequestEvent(final Object source,
                                                    final Device device,
                                                    final ObjectNode responseObject,
                                                    final ObjectNode requestObject,
                                                    final String errorTitle,
                                                    final boolean fromRetry,
                                                    final PrintOperation printOperation) {
        super(source, device);
        this.responseObject = responseObject;
        this.requestObject = requestObject;
        this.errorTitle = errorTitle;
        this.fromRetry = fromRetry;
        this.printOperation = printOperation;
    }
}
