package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Raised when device info collection succeeded
 */
@Getter
@Setter
public class AndroidInfoCollectionSuccessEvent extends AbstractDeviceEvent {
    private List<String> deviceColorList;
    private final boolean shouldInstallApps;

    public AndroidInfoCollectionSuccessEvent(final Object source, final AndroidDevice device,
                                             final boolean shouldInstallApps) {
        super(source, device);
        this.shouldInstallApps = shouldInstallApps;
    }
}
