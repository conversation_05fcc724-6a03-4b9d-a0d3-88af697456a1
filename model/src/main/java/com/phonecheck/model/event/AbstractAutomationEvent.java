package com.phonecheck.model.event;

import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.Device;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.Objects;

/**
 * Abstract Automation Event
 */
@Getter
@Setter
public class AbstractAutomationEvent extends ApplicationEvent implements IDeviceEvent {
    protected Device device;
    protected AutomationWorkflow automationWorkflow;

    public AbstractAutomationEvent(final Object source, final Device device,
                                   final AutomationWorkflow automationWorkflow) {
        super(source);

        this.device = device;
        this.automationWorkflow = automationWorkflow;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AbstractAutomationEvent event = (AbstractAutomationEvent) o;
        return Objects.equals(device.getId(), event.getDevice().getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(device.getId());
    }
}
