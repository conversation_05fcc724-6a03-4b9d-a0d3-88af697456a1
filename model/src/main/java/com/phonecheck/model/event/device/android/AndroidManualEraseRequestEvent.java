package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when a manual erase is requested
 */
@Getter
public class AndroidManualEraseRequestEvent extends AbstractDeviceEvent {
    public AndroidManualEraseRequestEvent(final Object source, final AndroidDevice device) {
        super(source, device);
    }
}
