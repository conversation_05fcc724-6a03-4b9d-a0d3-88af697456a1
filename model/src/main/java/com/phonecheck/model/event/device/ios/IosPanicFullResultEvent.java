package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when panic full result is available.
 */
@Getter
@Setter
public class IosPanicFullResultEvent extends AbstractDeviceEvent {
    private String panicFullResult;

    public IosPanicFullResultEvent(final Object source, final Device device) {
        super(source, device);
    }
}