package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;

/**
 * Raised when device battery info collection succeeds
 */
public class AndroidBatteryInfoSuccessEvent extends AbstractDeviceEvent {
    public AndroidBatteryInfoSuccessEvent(final Object source, final AndroidDevice device) {
        super(source, device);
    }
}
