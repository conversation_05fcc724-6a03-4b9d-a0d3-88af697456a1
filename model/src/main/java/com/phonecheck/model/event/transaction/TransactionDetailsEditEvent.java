package com.phonecheck.model.event.transaction;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.transaction.Transaction;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TransactionDetailsEditEvent extends AbstractEvent {
    private Device device;
    private String transactionId;
    private Transaction transaction;

    public TransactionDetailsEditEvent(final Object source, final Device device,
                                       final String transactionId,
                                       final Transaction transaction) {
        super(source);
        this.device = device;
        this.transactionId = transactionId;
        this.transaction = transaction;
    }
}
