package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;

/**
 * Raised when device battery info collection succeeds
 */
public class IosBatteryInfoSuccessEvent extends AbstractDeviceEvent {
    public IosBatteryInfoSuccessEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
