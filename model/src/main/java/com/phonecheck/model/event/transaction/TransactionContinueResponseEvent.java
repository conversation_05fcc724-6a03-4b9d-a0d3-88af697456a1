package com.phonecheck.model.event.transaction;

import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when continue transaction response is received in UI
 */
@Getter
@Setter
public class TransactionContinueResponseEvent extends AbstractEvent {
    private boolean continueTransactionSuccessStatus;

    public TransactionContinueResponseEvent(final Object source,
                                            final boolean continueTransactionSuccessStatus) {
        super(source);
        this.continueTransactionSuccessStatus = continueTransactionSuccessStatus;
    }
}

