package com.phonecheck.model.event.device;

import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractAutomationEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Event to trigger automated steps on the device, like print erase and power off
 */
@Getter
@Setter
public class DeviceTestResultAutomationEvent extends AbstractAutomationEvent {
    public DeviceTestResultAutomationEvent(final Object source,
                                           final Device device) {
        super(source, device, AutomationWorkflow.TEST_RESULTS);
    }

    @Override
    public String toString() {
        return "DeviceTestResultAutomationEvent{" +
                "automationWorkflow=" + automationWorkflow +
                ", source=" + source.getClass() +
                '}';
    }
}
