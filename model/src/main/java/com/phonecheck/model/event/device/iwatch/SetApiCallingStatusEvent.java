package com.phonecheck.model.event.device.iwatch;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised to set Api calling status for iWatch
 */
@Getter
public class SetApiCallingStatusEvent extends AbstractDeviceEvent {
    private final String callingStatus;

    public SetApiCallingStatusEvent(final Object source, final IosDevice device, final String callingStatus) {
        super(source, device);
        this.callingStatus = callingStatus;
    }
}
