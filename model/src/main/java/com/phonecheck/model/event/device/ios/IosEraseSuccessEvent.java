package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class IosEraseSuccessEvent extends AbstractDeviceEvent {
    private boolean erasedForMdm;
    public IosEraseSuccessEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}