package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when Android device root info is successfully retrieved
 */
@Getter
public class AndroidDeviceRootInfoSuccessEvent extends AbstractDeviceEvent {
    public AndroidDeviceRootInfoSuccessEvent(final Object source, final AndroidDevice device) {
        super(source, device);
    }
}

