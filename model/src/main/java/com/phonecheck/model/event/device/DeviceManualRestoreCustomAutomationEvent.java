package com.phonecheck.model.event.device;

import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractAutomationEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * Event to trigger manual restore custom automation steps on the device
 */
@Getter
@Setter
public class DeviceManualRestoreCustomAutomationEvent extends AbstractAutomationEvent {

    public DeviceManualRestoreCustomAutomationEvent(final Object source, final Device device) {
        super(source, device, AutomationWorkflow.MANUAL_RESTORE);
    }

    @Override
    public String toString() {
        return "DeviceManualRestoreCustomAutomationEvent{" +
                "automationWorkflow=" + automationWorkflow +
                ", source=" + source.getClass() +
                '}';
    }
}
