package com.phonecheck.model.event.syslog;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when new transaction is returned from backend
 */
@Getter
@Setter
public class StopDeviceSysLogEvent extends AbstractEvent {
    private Device device;

    public StopDeviceSysLogEvent(final Object source, final Device device) {
        super(source);
        this.device = device;
    }
}

