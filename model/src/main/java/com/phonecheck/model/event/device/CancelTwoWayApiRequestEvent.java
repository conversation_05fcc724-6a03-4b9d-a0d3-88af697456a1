package com.phonecheck.model.event.device;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.twowayapi.ApiCallType;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised to cancel bStar api pop-up
 */
@Getter
@Setter
public class CancelTwoWayApiRequestEvent extends AbstractDeviceEvent {
    private ObjectNode apiResponseObject;
    private String errorTitle;
    private ApiCallType apiCallType;
    private PrintOperation printOperation;

    public CancelTwoWayApiRequestEvent(final Object source,
                                      final Device device,
                                      final ObjectNode apiResponseObject,
                                      final String errorTitle,
                                      final ApiCallType apiCallType,
                                      final PrintOperation printOperation) {
        super(source, device);
        this.apiResponseObject = apiResponseObject;
        this.errorTitle = errorTitle;
        this.apiCallType = apiCallType;
        this.printOperation = printOperation;
    }
}