package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Represents an event indicating show/hide manual app install instructions pop-up.
 **/
@Getter
@Setter
public class ShowHideManualAppInstallEvent extends AbstractDeviceEvent {
    private final boolean showManualAppInstallInstructions;

    public ShowHideManualAppInstallEvent(final Object source,
                                         final AndroidDevice device,
                                         final boolean showManualAppInstallInstructions) {
        super(source, device);
        this.showManualAppInstallInstructions = showManualAppInstallInstructions;
    }
}