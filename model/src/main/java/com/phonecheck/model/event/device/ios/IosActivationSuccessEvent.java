package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

@Getter
public class IosActivationSuccessEvent extends AbstractDeviceEvent {
    private final DeviceLock deviceLock;
    private final boolean shouldInstallApp;

    public IosActivationSuccessEvent(final Object source, final IosDevice device, final DeviceLock deviceLock,
                                     final boolean shouldInstallApp) {
        super(source, device);
        this.shouldInstallApp = shouldInstallApp;
        this.deviceLock = deviceLock;
    }
}
