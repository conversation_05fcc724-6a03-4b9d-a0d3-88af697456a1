package com.phonecheck.model.event.grading;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Event class representing response to get final grade information
 */
@Setter
@Getter
public class GetFinalGradeResponseEvent extends AbstractDeviceEvent {
    private String finalGrade;

    public GetFinalGradeResponseEvent(final Object source, final Device device, final String finalGrade) {
        super(source, device);
        this.finalGrade = finalGrade;
    }
}