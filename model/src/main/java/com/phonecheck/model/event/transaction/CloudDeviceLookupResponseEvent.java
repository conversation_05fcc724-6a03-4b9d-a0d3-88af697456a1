package com.phonecheck.model.event.transaction;

import com.phonecheck.model.cloudapi.TransactionResponse;
import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when transaction is returned from cloud device lookyp
 */
@Getter
@Setter
public class CloudDeviceLookupResponseEvent extends AbstractEvent {
    private TransactionResponse transactionResponse;

    public CloudDeviceLookupResponseEvent(final Object source,
                                          final TransactionResponse transactionResponse) {
        super(source);
        this.transactionResponse = transactionResponse;
    }
}

