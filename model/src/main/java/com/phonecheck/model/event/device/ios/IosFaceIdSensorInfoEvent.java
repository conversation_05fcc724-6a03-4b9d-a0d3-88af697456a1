package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import lombok.Getter;

@Getter
public class IosFaceIdSensorInfoEvent extends AbstractDeviceEvent {
    private final IosSysLogKey iosSysLogKey;

    public IosFaceIdSensorInfoEvent(final Object source, final IosDevice device, final IosSysLogKey iosSysLogKey) {
        super(source, device);
        this.iosSysLogKey = iosSysLogKey;
    }
}
