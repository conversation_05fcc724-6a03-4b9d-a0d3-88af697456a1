package com.phonecheck.model.event.device.ios.precheck;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.PreCheckInfo;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PreCheckSemiResultManualFailedEvent extends AbstractDeviceEvent {
    private PreCheckInfo.SemiAutoResults semiResults;
    public PreCheckSemiResultManualFailedEvent(final Object source, final IosDevice device,
                                               final PreCheckInfo.SemiAutoResults semiResults) {
        super(source, device);
        this.semiResults = semiResults;
    }
}
