package com.phonecheck.model.event.transaction;

import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when cloud device lookup are requested
 */
@Getter
@Setter
public class CloudDeviceLookupRequestEvent extends AbstractEvent {
    private String lookupType;
    private String lookupValue;

    public CloudDeviceLookupRequestEvent(final Object source,
                                         final String lookupType,
                                         final String lookupValue) {
        super(source);
        this.lookupType = lookupType;
        this.lookupValue = lookupValue;
    }
}

