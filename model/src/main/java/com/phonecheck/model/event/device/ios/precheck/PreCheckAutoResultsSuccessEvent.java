package com.phonecheck.model.event.device.ios.precheck;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Event when preCheck auto results retrieved
 */
@Getter
public class PreCheckAutoResultsSuccessEvent extends AbstractDeviceEvent {
    public PreCheckAutoResultsSuccessEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
