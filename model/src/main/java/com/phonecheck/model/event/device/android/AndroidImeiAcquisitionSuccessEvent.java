package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

@Getter
public class AndroidImeiAcquisitionSuccessEvent extends AbstractDeviceEvent {
    private final boolean shouldInstallApps;

    public AndroidImeiAcquisitionSuccessEvent(final Object source, final AndroidDevice device,
                                              final boolean shouldInstallApps) {
        super(source, device);
        this.shouldInstallApps = shouldInstallApps;
    }
}
