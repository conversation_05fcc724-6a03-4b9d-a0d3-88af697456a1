package com.phonecheck.model.event.device;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.twowayapi.ApiCallType;
import lombok.Getter;
import lombok.Setter;

/** Event to be Raised to resume processing for Two way API*/
@Getter
@Setter
public class ResumeTwoWayApiEvent extends AbstractDeviceEvent {
    private ObjectNode responseObject;
    private String errorTitle;
    private ApiCallType apiCallType;
    private PrintOperation printOperation;

    public ResumeTwoWayApiEvent(final Object source,
                                final Device device,
                                final ObjectNode responseObject,
                                final String errorTitle,
                                final ApiCallType apiCallType,
                                final PrintOperation printOperation) {
        super(source, device);
        this.responseObject = responseObject;
        this.errorTitle = errorTitle;
        this.apiCallType = apiCallType;
        this.printOperation = printOperation;
    }
}