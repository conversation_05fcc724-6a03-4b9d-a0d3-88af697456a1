package com.phonecheck.model.event;

import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FirmwareDownloadResponseEvent extends AbstractEvent {
    private String firmwareId;
    private int progress;
    private FirmwareDownloadStatus firmwareDownloadStatus;

    public FirmwareDownloadResponseEvent(final Object source,
                                         final String firmwareId,
                                         final FirmwareDownloadStatus firmwareDownloadStatus,
                                         final int progress) {
        super(source);
        this.firmwareId = firmwareId;
        this.firmwareDownloadStatus = firmwareDownloadStatus;
        this.progress = progress;
    }

}
