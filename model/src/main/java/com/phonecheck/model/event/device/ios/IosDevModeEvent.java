package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Event to notify that the dev mode pop up is on the device.
 */
@Getter
public class IosDevModeEvent extends AbstractDeviceEvent {

    public IosDevModeEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
