package com.phonecheck.model.event.device;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Event raised as response of device guid received
 */
@Getter
public class DeviceGuidResponseEvent extends AbstractDeviceEvent {
    private final String deviceGuid;

    public DeviceGuidResponseEvent(final Object source,
                                   final Device device,
                                   final String deviceGuid) {
        super(source, device);
        this.deviceGuid = deviceGuid;
    }
}