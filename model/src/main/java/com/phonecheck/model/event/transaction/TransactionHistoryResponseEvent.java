package com.phonecheck.model.event.transaction;

import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.transaction.Transaction;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Raised when transaction history is returned from backend
 */
@Getter
@Setter
public class TransactionHistoryResponseEvent extends AbstractEvent {
    private List<Transaction> transactionList;

    public TransactionHistoryResponseEvent(final Object source,
                                           final List<Transaction> transactionList) {
        super(source);
        this.transactionList = transactionList;
    }
}

