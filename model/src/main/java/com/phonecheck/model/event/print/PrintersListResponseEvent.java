package com.phonecheck.model.event.print;

import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Event that stores connected printer names list
 */
@Getter
@Setter
public class PrintersListResponseEvent extends AbstractEvent {
    private List<String> printersList;

    public PrintersListResponseEvent(final Object source, final List<String> printersList) {
        super(source);
        this.printersList = printersList;
    }
}
