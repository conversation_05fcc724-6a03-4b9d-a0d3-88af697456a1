package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when device recovery is requested from UI
 */
@Getter
@Setter
public class IosManualDeviceRestoreEvent extends AbstractDeviceEvent {

    public IosManualDeviceRestoreEvent(final Object source,
                                       final IosDevice device) {
        super(source, device);
    }
}
