package com.phonecheck.model.event.device.iwatch;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when an iWatch basic info retrieved
 */
@Getter
public class IWatchBasicInfoSuccessEvent extends AbstractDeviceEvent {
    public IWatchBasicInfoSuccessEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
