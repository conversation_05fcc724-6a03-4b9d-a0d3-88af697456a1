package com.phonecheck.model.objectmapper;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

public class ListToCommaSeparatedStringSerializer extends JsonSerializer<List<String>> {
    private static final Logger LOGGER =
            LoggerFactory.getLogger(ListToCommaSeparatedStringSerializer.class);

    @Override
    public void serialize(List<String> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {

        String result;
        // Join the list elements with commas and write as a string
        if (value != null && !value.isEmpty()) {
            result = String.join(",", value);
        } else {
            result = "";
        }
        gen.writeString(result);

        LOGGER.info("Serialized {} to {}", value, result);
    }
}

