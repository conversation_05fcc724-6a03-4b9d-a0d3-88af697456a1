package com.phonecheck.model.grading;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorGradingCriteria {
    @JsonProperty("status")
    private String status;
    private List<GradingData> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GradingData {
        private int vendorId;
        private String vendorName;
        private String vendorFields;
        private List<Grade> grades;

    }

    // Grade Class
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Grade {
        private int gradeId;
        private String gradeTitle;
        private boolean isReasonRequired;
        private List<GradeCriteria> criteria;
        private boolean isQualified;

        public Grade(Grade grade) {
            this.criteria = new ArrayList<>(grade.getCriteria());
            this.gradeId = grade.getGradeId();
            this.gradeTitle = grade.getGradeTitle();
            this.isQualified = grade.isQualified();
            this.isReasonRequired = grade.isReasonRequired();
        }
    }

    // GradeCriteria Class
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GradeCriteria {
        private String testTitle;
        private String cloudDbCol;
        private String unit;
        private String tests;
        private String answers;
        private VendorCriteriaStatus criteriaResult;
    }
}

