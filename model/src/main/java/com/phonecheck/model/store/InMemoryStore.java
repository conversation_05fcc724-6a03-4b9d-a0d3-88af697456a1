package com.phonecheck.model.store;

import com.phonecheck.model.cloudapi.*;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.grading.VendorGradingCriteria;
import com.phonecheck.model.login.UserLoginDetails;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.wifi.WifiConfig;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Component
public class InMemoryStore {
    private boolean isEuServer;
    private String masterToken;
    private String userToken;
    private String masterId;
    private int licenseId;
    @Setter(AccessLevel.NONE)
    private final DesktopLicenseResponse desktopLicenseResponse = new DesktopLicenseResponse();
    // To check CheckMend support for client
    private boolean personalCheckMend;
    private String vppToken;
    private String iosAppBundleIdentifier;
    private String userName;
    private String userPassword;
    private String testerId;
    private String testerName;
    private String warehouseName;
    private String warehouseId;
    private String apiKey;
    private Transaction transaction;
    private String iosAppUrl;
    // Wi-fi config to be stored in memory, so we can retrieve it for PEO
    private WifiConfig wifiConfig;
    private Set<String> eeeCodesSet;
    private IosColorCodeResponse iosColorCodes;
    private String buildNo;
    private boolean appStartedFromInstaller;
    // default mode is PROCESS
    private DeviceConnectionMode deviceConnectionMode = DeviceConnectionMode.PROCESS;
    private UserLoginDetails userLoginDetails;
    private LocalCustomizations localCustomizations;
    private boolean isPortMapReInitialized = false;
    private String iosAppVersion;
    private String androidAppVersion;
    private Map<String, String> fccIdMap;
    private String masterName;
    private String masterUserName;
    private String masterPassword;
    private Map<String, LabelFxmlResponse.Data> labelDataMap;
    private CloudCustomizationResponse assignedCloudCustomization;
    private SkuSchemaResponse skuSchemaResponse;
    private Map<String, SelectPackageResponse.ProfilesConfiguration> selectPackageResponse;
    private Map<String, FirmwareModel.FirmwareResponse> firmwareModels;
    private String buildUpdateFileUrl;
    private boolean resumeProcessingForOta = false;
    private String currentLanguage;
    private int daysForImei;
    private String firmwareDownloadPath;
    private boolean isFirmwaresValidatedPostLogin = false;
    private volatile boolean isAndroidUnlockCodesDownloaded = false;
    private Map<String, AndroidConfigResponse> androidDeviceFeatures;
    private Map<String, DeviceFeaturesResponse> iosDeviceFeatures;
    private ShopfloorCustomizationResponse shopfloorResponse;
    private List<String> shopfloorGrades;
    private List<ShopfloorCustomizationResponse.DefectMapping> defectsList;
    private List<ShopfloorCustomizationResponse.WhiteListedDefectsMapping> whiteListedDefects;
    private GradingResponse grading;
    private VendorGradingCriteria gradingCriteria;
    private String customClientLogoUrl;

}
