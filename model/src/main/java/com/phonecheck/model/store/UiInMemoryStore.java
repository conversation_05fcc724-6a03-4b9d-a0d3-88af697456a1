package com.phonecheck.model.store;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.SkuSchemaResponse;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import com.phonecheck.model.transaction.Transaction;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@Component
public class UiInMemoryStore {
    private boolean isEuServer;
    private boolean appStartedFromInstaller;
    private Transaction transaction;
    private LocalCustomizations customizations;
    private String loggedInUserId;
    private String loggedInTesterName;
    private String masterUserName;
    private Map<String, LabelFxmlResponse.Data> labelDataMap;
    private CloudCustomizationResponse assignedCloudCustomization;
    private SkuSchemaResponse skuSchemaResponse;
    private int licenseId;
    private String masterToken;
    private String userToken;
    private Map<String, FirmwareModel.FirmwareResponse> firmwareModels;
    private String currentLanguage;
    private String firmwareDownloadPath;
    private String warehouseName;
    private String masterId;
    private String testerId;
    private int daysForImei;
    private String customClientLogoUrl;
    private List<Transaction> transactionList;
    private List<String> shopfloorGrades;
    // default mode is PROCESS
    private DeviceConnectionMode deviceConnectionMode = DeviceConnectionMode.PROCESS;

    private Map<String, SelectPackageResponse.ProfilesConfiguration> selectPackageResponses;
}
