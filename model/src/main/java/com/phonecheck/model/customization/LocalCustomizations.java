package com.phonecheck.model.customization;

import lombok.*;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class LocalCustomizations {
    private String userId;

    //print customisations
    private String defaultLabel1;
    private String defaultPrinter1;
    private String rollTypePrinter1;
    private String selectedTrayPrinter1;
    private String selectedPaperType1;
    private boolean isDymoTwinTurboPrinter1;
    private String defaultLabel2;
    private String defaultPrinter2;
    private String rollTypePrinter2;
    private String selectedTrayPrinter2;
    private String selectedPaperType2;
    private boolean isDymoTwinTurboPrinter2;
    private LabelWorkFlowPrintSettings labelWorkFlowPrintSettings;

    private boolean enableDevMode;
    private boolean enableAutoOpenIosApp;
    private int restoreThreads;
    private int restoreDelay;
    private String firmwarePath;

    private boolean autoExport;
    private boolean exportFormatJson;
    private boolean exportFormatXml;
    private boolean exportOnSuccessfulErase;
    private boolean exportOnAppResults;
    private String exportFilePath;
    private boolean fetchXiaomiAlternateSerial;
    private boolean ignoreNaForOem;
    private ShopfloorSettings shopfloorSettings;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class ShopfloorSettings implements Serializable {
        private List<String> selectedImageGrades;
        private String imageGradeFolderPath;
        private String selectedCamera;
    }
}
