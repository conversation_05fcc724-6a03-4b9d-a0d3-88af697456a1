package com.phonecheck.model.service;

import com.phonecheck.model.cloudapi.EsnResponse;
import lombok.*;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class EsnCheckInfo {
    private List<EsnResponse.EsnApiResponse> esnApiResults;
    private EsnResponse.EsnLicensesExpiredResponse esnLicensesExpiredResponse;
    private EsnResponse.UsInsuranceBlackListInfo usInsuranceBlackListInfo;
    private EsnResponse.TracFoneStraightTalkAPIResponse
            tracFoneStraightTalkAPIResponse;
}
