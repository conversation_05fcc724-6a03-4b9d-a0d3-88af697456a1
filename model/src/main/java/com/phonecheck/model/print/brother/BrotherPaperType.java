package com.phonecheck.model.print.brother;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Brother paper type enum represents possible
 * brother printer types i.e Monochrome and Black/Red
 */
@Getter
public enum BrotherPaperType {
    MONO("Monochrome", "Mono"),
    BLACKRED("Black/Red", "RB");

    private final String displayName;
    private final String value;

    BrotherPaperType(String printName, String value) {
        this.displayName = printName;
        this.value = value;
    }

    public static BrotherPaperType getByDisplayName(String displayName) {
        for (BrotherPaperType paperType : values()) {
            if (StringUtils.containsIgnoreCase(paperType.displayName, displayName)) {
                    return paperType;
            }
        }
        return null;
    }
}
