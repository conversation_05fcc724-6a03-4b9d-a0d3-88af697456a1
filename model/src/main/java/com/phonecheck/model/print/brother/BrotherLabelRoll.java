package com.phonecheck.model.print.brother;

import com.phonecheck.model.print.label.LabelOrientation;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Brother label roll enum represents all the available label
 * types in which a label can be printed.
 */
@Getter
public enum BrotherLabelRoll {
    NA("N/A", "N/A", null, null),
    ROLL_1_5x3_5_OR_3_5x1_5_TAPE("1.5x3.5 or 3.5x1.5 tape", "Custom.38x91mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_1_5x3_5_OR_3_5x1_5_LABEL("1.5x3.5 or 3.5x1.5 label", "DC04",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_1_25x4_OR_4x1_25_TAPE("1.25x4 or 4x1.25 tape", "Custom.38x100mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_1x2_OR_2x1_TAPE("1x2 or 2x1 tape", "Custom.29x54mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_1x2_OR_2x1_LABEL("1x2 or 2x1 label", "DC24",
            BrotherRollType.LABEL, LabelOrientation.LANDSCAPE),
    ROLL_1x3_OR_3x1_TAPE("1x3 or 3x1 tape", "Custom.29x77mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_1x3_OR_3x1_LABEL("1x3 or 3x1 label", "DC03",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_2x3_OR_3x2_TAPE("2x3 or 3x2 tape", "Custom.62x77mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_2x3_OR_3x2_LABEL("2x3 or 3x2 label", "DCNB",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_2x4_OR_4x2_TAPE("2x4 or 4x2 tape", "Custom.62x100mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_2x4_OR_4x2_LABEL("2x4 or 4x2 label", "DC07",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_32x57_OR_57x32_MM_TAPE("32x57mm or 57x32mm tape", "Custom.38x62mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_62x29_OR_29x62_MM_TAPE("62x29mm or 29x62mm tape", "Custom.29x62mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_62x29_OR_29x62_MM_LABEL("62x29mm or 29x62mm label", "DC06",
            BrotherRollType.LABEL, LabelOrientation.LANDSCAPE),
    ROLL_36x89_OR_89x36_MM_TAPE("36x89mm or 89x36mm tape", "Custom.38x91mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_36x89_OR_89x36_MM_LABEL("36x89mm or 89x36mm label", "DC04",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_59_9x38_OR_38x59_9_MM_LABEL("59.9x38mm or 38x59.9mm label", "DC04",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_59_9x38_OR_38x59_9_MM_TAPE("59.9x38mm or 38x59.9mm tape", "Custom.60x38mm",
            BrotherRollType.TAPE, LabelOrientation.LANDSCAPE),
    ROLL_62x101_OR_101x62_TAPE("62x101mm or 101x62mm tape", "Custom.62x101mm",
            BrotherRollType.TAPE, LabelOrientation.PORTRAIT),
    ROLL_62x101_OR_101x62_LABEL("62x101mm or 101x62mm label", "DC07",
            BrotherRollType.LABEL, LabelOrientation.PORTRAIT),
    ROLL_62x52_OR_52x62_MM_TAPE("62x52mm or 52x62mm tape", "Custom.62x52mm",
            BrotherRollType.TAPE, LabelOrientation.LANDSCAPE);

    private final String labelSize;
    private final String mediaSize;
    private final BrotherRollType rollType;
    private final LabelOrientation orientation;

    BrotherLabelRoll(String labelSize, String mediaSize, BrotherRollType rollType, LabelOrientation orientation) {
        this.labelSize = labelSize;
        this.mediaSize = mediaSize;
        this.rollType = rollType;
        this.orientation = orientation;
    }

    public static BrotherLabelRoll getByLabelSizeAndType(String size, String type) {
        for (BrotherLabelRoll roll : values()) {
            if (StringUtils.contains(roll.labelSize, size) && StringUtils.contains(roll.labelSize, type)) {
                return roll;
            }
        }

        return null;
    }
}
