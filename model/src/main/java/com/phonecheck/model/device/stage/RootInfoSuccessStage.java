package com.phonecheck.model.device.stage;

import com.phonecheck.model.status.RootedStatus;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RootInfoSuccessStage implements IStage {
    private final DeviceStage stage = DeviceStage.FRP_INFO_SUCCEEDED;
    private String id;
    private String transactionId;
    private RootedStatus rooted;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}
