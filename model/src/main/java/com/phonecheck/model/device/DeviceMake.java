package com.phonecheck.model.device;

import java.util.Locale;

public enum DeviceMake {
    APPLE("Apple");

    private String displayName;

    DeviceMake(final String displayName) {
        this.displayName = displayName;
    }

    /**
     * @return this manufacturer's standard display name
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Finds the {@link DeviceMake} corresponding with the specified display name
     *
     * @param displayName display name to find
     * @return {@link DeviceMake} corresponding with the specified display name
     */
    public static DeviceMake fromString(String displayName) {
        if (null == displayName) {
            return null;
        }
        displayName = displayName.toLowerCase(Locale.ROOT);
        for (DeviceMake make : values()) {
            if (displayName.equalsIgnoreCase(make.displayName)) {
                return make;
            }
        }
        return null;
    }
}
