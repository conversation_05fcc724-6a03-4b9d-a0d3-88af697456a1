package com.phonecheck.model.device;

import com.phonecheck.model.device.stage.DeviceStage;

public interface IDevice {
    /**
     * @return this device's unique ID
     */
    String getId();

    DeviceType getDeviceType();
    DeviceFamily getDeviceFamily();

    String getSerial();

    String getImei();

    String getImei2();

    String getModelNo();
    String getMake();

    String getWifiAddress();

    /**
     * Method to get the License identifier for the device
     * For Android devices it would be device serial and for
     * Apple device it would be ECID
     *
     * @return license identifier string
     */
    String getLicenseIdentifier();

    /**
     * @return last stage this device encountered
     */
    DeviceStage getStage();

    void setStage(DeviceStage stage);
}
