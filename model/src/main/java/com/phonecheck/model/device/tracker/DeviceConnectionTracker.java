package com.phonecheck.model.device.tracker;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.stage.DeviceState;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Tracks the devices that are currently connected.
 * Consumers can register to receive notifications when a device connects or disconnects.
 */
@Component
@Getter
public class DeviceConnectionTracker {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceConnectionTracker.class);
    private final Map<String, Device> connectedDevices = new ConcurrentHashMap<>();

    // Listeners interested in connect/disconnect events for any device
    private final Set<IDeviceConnectionListener> listeners = Collections.synchronizedSet(new HashSet<>());
    // Listeners interested in connect/disconnect events for a specific device
    private final Map<String, Set<IDeviceConnectionListener>> deviceListeners = new HashMap<>();

    /**
     * Retrieves a connected device. If the device with the given ID isn't currently connected, <code>null</code>
     * is returned.
     *
     * @param id target device's ID
     * @return the corresponding device, or <code>null</code> if the device is not connected
     */
    public Device getDevice(@NonNull final String id) {
        return connectedDevices.get(id);
    }

    /**
     * Retrieves a connected device. If the device with the given serial isn't currently connected, <code>null</code>
     * is returned.
     *
     * @param serial target device's serial
     * @return the corresponding device, or <code>null</code> if the device is not connected
     */
    public Device getDeviceBySerial(final String serial) {
        Device outputDevice = null;
        if (StringUtils.isNotBlank(serial)) {
            for (Map.Entry<String, Device> deviceEntry : connectedDevices.entrySet()) {
                Device device = deviceEntry.getValue();
                if (serial.equals(device.getSerial())) {
                    outputDevice = device;
                    break;
                }
            }
        }
        return outputDevice;
    }

    /**
     * Retrieves a connected device. If the device with the given portName isn't currently connected, <code>null</code>
     * is returned.
     *
     * @param portId target device's Modem
     * @return the corresponding device, or <code>null</code> if the device is not connected
     */
    public Device getDeviceByAtId(final String portId) {
        Device outputDevice = null;
        if (StringUtils.isNotBlank(portId)) {
            for (Map.Entry<String, Device> deviceEntry : connectedDevices.entrySet()) {
                Device device = deviceEntry.getValue();
                if (device instanceof AndroidDevice androidDevice
                        && androidDevice.getAtPort() != null
                        && androidDevice.getAtPort().getAPort() != null
                        && portId.equals(androidDevice.getAtPort().getAPort().getDeviceID())) {
                    outputDevice = device;
                    break;
                }
            }
        }
        return outputDevice;
    }


    /**
     * Retrieves a connected device. If the device with the given serial isn't currently connected, <code>null</code>
     * is returned.
     *
     * @param ecid: target device's ECID
     * @return the corresponding device, or <code>null</code> if the device is not connected
     */
    public Device getDeviceByEcid(final String ecid) {
        LOGGER.debug("Input ECID:{}", ecid);
        Device outputDevice = null;
        if (StringUtils.isNotBlank(ecid)) {
            for (Map.Entry<String, Device> deviceEntry : connectedDevices.entrySet()) {
                Device device = deviceEntry.getValue();
                if (device instanceof IosDevice) {

                    String ecidFromDevice = ((IosDevice) device).getEcid();
                    String ecidOriginalForm = null;
                    try {
                        if (device.isConnectedInRecovery()) {
                            ecidOriginalForm = Long.valueOf(ecidFromDevice, 16).toString();
                        }
                    } catch (Exception e) {
                        LOGGER.error("Error while converting ECID in other form : {}", ecidFromDevice);
                    }
                    LOGGER.debug("Map key: {}, Connected In Recovery?:{}, Normal:{}, Modified:{}", deviceEntry.getKey(),
                            device.isConnectedInRecovery(), ecidFromDevice, ecidOriginalForm);
                    if (ecid.equals(ecidFromDevice) || ecid.equals(ecidOriginalForm)) {
                        outputDevice = device;
                        break;
                    }
                }
            }
        }
        return outputDevice;
    }


    /**
     * Retrieves a connected device on the given port number. If device is some same port
     * and was in recovery then returned.
     *
     * @param portNumber: portNumber of device connected
     * @return the corresponding device, or <code>null</code> if the device is not connected
     */
    public Device getDeviceByPortMapping(@NonNull final int portNumber) {
        LOGGER.info("Checking the device on port: {}", portNumber + 1);
        return connectedDevices.values().stream().filter(s -> s.getPortNumber() == portNumber
                && s.getDeviceState() == DeviceState.DFU).findAny().orElse(null);
    }


    /**
     * Adds a listener to be notified of any new device connections or existing device disconnections
     *
     * @param listener listener to be notified
     */
    public void addListener(@NonNull final IDeviceConnectionListener listener) {
        listeners.add(listener);
    }

    /**
     * Removes a listener from the set of listeners to be notified of device connections or disconnections
     *
     * @param listener listener to remove
     */
    public void removeListener(@NonNull final IDeviceConnectionListener listener) {
        listeners.remove(listener);
    }

    /**
     * Adds a device-specific listener to be notified of device-specific connections or disconnections
     *
     * @param deviceId ID of the device for which to listen
     * @param listener listener to add
     */
    public void addListener(@NonNull final String deviceId, @NonNull final IDeviceConnectionListener listener) {
        Set<IDeviceConnectionListener> existingListeners =
                deviceListeners.computeIfAbsent(deviceId, k -> new HashSet<>());
        existingListeners.add(listener);
    }

    /**
     * Removes a device-specific listener
     *
     * @param deviceId ID of the device for which the listener is listening
     * @param listener listener to remove
     */
    public void removeListener(@NonNull final String deviceId, @NonNull final IDeviceConnectionListener listener) {
        Set<IDeviceConnectionListener> existingListeners = deviceListeners.get(deviceId);
        if (null != existingListeners) {
            existingListeners.remove(listener);
        }
    }

    /**
     * Registers a device as connected. Notifies any interested listeners.
     *
     * @param device newly-connected device
     */
    public void deviceConnected(@NonNull final Device device) {
        if (device.getId() == null) {
            LOGGER.info("Device cannot be added with null id. Device: {}", device);
            return;
        }
        connectedDevices.put(device.getId(), device);
        // Notify splat listeners
        synchronized (listeners) {
            for (IDeviceConnectionListener listener : listeners) {
                listener.onConnected(device);
            }
        }

        // Notify device-specific listeners
        if (deviceListeners.containsKey(device.getId())) {
            Set<IDeviceConnectionListener> thisDeviceListeners = deviceListeners.get(device.getId());
            if (null == thisDeviceListeners) {
                return;
            }
            for (IDeviceConnectionListener listener : thisDeviceListeners) {
                listener.onConnected(device);
            }
        }
    }

    /**
     * Registers a device as disconnected. Notifies any interested listeners. If the device is not currently
     * registered as connected, a disconnection event is sent anyway, but contains only the given ID.
     *
     * @param id newly-disconnected device's ID. If the device is currently registered as connected, listeners will
     *           receive both the device's ID and the last-known device object.
     */
    public void deviceDisconnected(@NonNull final String id) {
        Device device = connectedDevices.get(id);
        if (null != device) {
            LOGGER.info("Notifying listeners interested in device disconnected events");
            device.setStage(DeviceStage.DISCONNECTED);
            connectedDevices.remove(id);

            // Notify splat listeners
            synchronized (listeners) {
                for (IDeviceConnectionListener listener : listeners) {
                    listener.onDisconnected(id, device);
                }
            }

            // Notify device-specific listeners
            if (deviceListeners.containsKey(device.getId())) {
                Set<IDeviceConnectionListener> thisDeviceListeners = deviceListeners.get(device.getId());
                if (null == thisDeviceListeners) {
                    return;
                }
                for (IDeviceConnectionListener listener : thisDeviceListeners) {
                    listener.onDisconnected(id, device);
                }
            }
        }
    }

    public void removeRecoveryDeviceEntryFromMap(@NonNull final String id) {
        LOGGER.info("Removing device :{} from the connected device map", id);
        Device device = connectedDevices.get(id);
        if (device != null) {
            connectedDevices.remove(id);
        } else {
            LOGGER.info("Device entry not found in connected device map:{}", id);
        }
    }

    /**
     * Clears connected devices from tracker
     */
    public void clearConnectedDevices() {
        connectedDevices.clear();
    }

    public boolean hasKey(final String key) {
        return connectedDevices.containsKey(key);
    }
}
