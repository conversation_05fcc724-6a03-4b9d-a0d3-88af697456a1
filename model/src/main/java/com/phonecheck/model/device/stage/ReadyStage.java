package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models device Ready state
 */
@Data
@Builder
public class ReadyStage implements IStage {
    private final DeviceStage stage = DeviceStage.READY;
    private String id;
    private Integer transactionId;
    private Integer licenseId;
    private String deviceSerial;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}