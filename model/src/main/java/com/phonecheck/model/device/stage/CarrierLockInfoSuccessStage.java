package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models a carrier lock info collection success
 */
@Data
@Builder
public class CarrierLockInfoSuccessStage implements IStage {
    private final DeviceStage stage = DeviceStage.CARRIER_LOCK_INFO_SUCCEEDED;
    private String id;
    private String transactionId;
    private String simLockStatus;
    private String carrier;
    private String carrierLockResponse;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }

}
