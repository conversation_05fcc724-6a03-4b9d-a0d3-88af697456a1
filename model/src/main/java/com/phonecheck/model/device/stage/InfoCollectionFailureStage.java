package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models a device info collection failure stage
 */
@Data
@Builder
public class InfoCollectionFailureStage implements IStage {
    private final DeviceStage stage = DeviceStage.INFO_COLLECTION_FAILED;
    private String id;
    private String transactionId;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}