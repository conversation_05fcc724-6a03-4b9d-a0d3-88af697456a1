package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models app install failure stage
 */
@Data
@Builder
public class AppInstallFailureStage implements IStage {
    private final DeviceStage stage = DeviceStage.APP_INSTALL_FAILURE;
    private String id;
    private String transactionId;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}
