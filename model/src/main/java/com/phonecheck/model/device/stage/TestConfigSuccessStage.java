package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models a test config push success
 */
@Data
@Builder
public class TestConfigSuccessStage implements IStage {
    private final DeviceStage stage = DeviceStage.TEST_CONFIG_SUCCESS;
    private String id;
    private String transactionId;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}