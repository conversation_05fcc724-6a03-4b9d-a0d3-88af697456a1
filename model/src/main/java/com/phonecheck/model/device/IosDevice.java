package com.phonecheck.model.device;

import com.phonecheck.model.ios.SerialNumbers;
import com.phonecheck.model.simcarrier.SimCarrierBundleInfo;
import com.phonecheck.model.status.*;
import com.phonecheck.model.test.OemPartsData;
import com.phonecheck.model.test.OemStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@ToString(callSuper = true)
public class IosDevice extends Device {
    private String ecid;
    private String productType; // Product type sample: for IOS, this is like "iPhone5,3"
    private String productVersion; // Product version sample: Complete OS version e.g. 15.1.1
    private String releaseType;
    private ActivationStatus activationStatus;
    private boolean fileServerRunning;
    private String oldAppleId;
    private String currentAppleId;
    private String regionInfo;
    private SetupDoneStatus setupDoneStatus;
    // TODO: Implement a way to detect if sd card present
    private boolean sdCardPresent;
    private boolean trueToneSupported;
    private String microphone;
    private String frontMicrophone;
    private String videoMicrophone;
    private String brightStarFunctionality;
    private String screenTime;
    private String regulatoryModelNumber;
    private boolean erasedForMdm;
    private boolean skipMdmInitialDefectWorkflow;
    private boolean batteryScreenOpened;
    private MountStatus imageMountStatus;
    private Boolean isBootCompleted;

    // Device parts working status
    private Boolean touchIdSupported;
    private WorkingStatus touchIdSensor;
    private Boolean faceIdSupported;
    private WorkingStatus faceIdSensor;
    // Device raw oem serial numbers fetched from multiple sources
    private SerialNumbers oemRawSerialNumbers = SerialNumbers.builder().build();
    // Device Oem parts details
    private OemPartsData oemPartsCurrent;
    private OemPartsData oemPartsFactory;
    // Device Oem parts notices
    private String oemDisplayNotice;
    private String oemBatteryNotice;
    private String oemBatteryServiceNotice;
    private String oemFrontCameraNotice;
    private String oemBackCameraNotice;
    // Device OEM parts status
    private OemStatus oemSerialStatus;
    private OemStatus oemMainBoardStatus;
    private OemStatus oemBatteryStatus;
    private OemStatus oemFrontCameraStatus;
    private OemStatus oemBackCameraStatus;
    private OemStatus oemLcdStatus;
    private OemStatus overallOemStatus;

    private SimCarrierBundleInfo simCarrierBundleInfo;
    private boolean isUsbMode;

    // IWatch properties
    private IWatchInfo iWatchInfo;
    private InstallBatteryProfileStatus iWatchInstallBatteryProfileStatus;

    // Flags to check different device statuses
    private boolean manualFaceId;
    private boolean isFaceIdManuallyChanged;
    private boolean isTouchIdManuallyChanged;
    private boolean isRecoveryMode;
    private boolean isRestartedFor16;
    private boolean openBatteryScreen;

    private String cpid;
    private String bdid;
    private String idInRecoveryMode;
    private String eraserType;
    private String restoreType;
    private PreCheckInfo preCheckInfo;
    

    @Override
    public DeviceType getDeviceType() {
        if (StringUtils.isNotBlank(productType)) {
            if (StringUtils.containsIgnoreCase(productType, "ipad")) {
                return DeviceType.IPAD;
            } else if (StringUtils.containsIgnoreCase(productType, "ipod")) {
                return DeviceType.IPOD;
            } else if (StringUtils.containsIgnoreCase(productType, "iwatch")) {
                return DeviceType.IWATCH;
            }
        }
        return DeviceType.IPHONE;
    }

    @Override
    public DeviceFamily getDeviceFamily() {
        return DeviceFamily.IOS;
    }

    @Override
    public String getOperatingSystem() {
        return "iOS";
    }

    @Override
    public String getMake() {
        return "Apple";
    }
}
