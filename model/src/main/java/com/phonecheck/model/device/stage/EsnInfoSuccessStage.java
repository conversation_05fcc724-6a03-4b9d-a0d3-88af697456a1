package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models an ESN info collection success
 */
@Data
@Builder
public class EsnInfoSuccessStage implements IStage {
    private final DeviceStage stage = DeviceStage.ESN_SUCCESS;
    private String id;
    private String transactionId;
    private String esnStatus;
    private String esnResponse;
    private String usInsuranceBlackListResponse;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }

}
