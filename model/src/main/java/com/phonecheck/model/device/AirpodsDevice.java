package com.phonecheck.model.device;

import com.phonecheck.model.test.OemStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class AirpodsDevice extends Device {

    private String id;
    private OemStatus overallOemStatus;
    private int leftBattery;
    private int rightBattery;
    private int caseBattery;

    @Override
    public DeviceType getDeviceType() {
        return DeviceType.AIRPOD;
    }

    @Override
    public String getMake() {
        return "Apple";
    }

    @Override
    public DeviceFamily getDeviceFamily() {
        return DeviceFamily.AIRPOD;
    }
}
