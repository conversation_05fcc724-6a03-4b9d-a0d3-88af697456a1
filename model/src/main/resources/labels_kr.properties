#Korean
forceAutoExport=ìë ë´ë³´ë´ê¸°
login.button = ë¡ê·¸ì¸
username=ì¬ì©ì ì´ë¦
password=ë¹ë°ë²í¸
optional=(ì í ì¬í­)
downloading=ë¤ì´ë¡ë ì¤
station=ì¤íì´ì
tester=íì¤í°
downloadApps=ì± ë¤ì´ë¡ë
rememberMe=ë´ ì ë³´ ê¸°ìµíê¸°
selectAll=ëª¨ë ì í
selectNone=ì í í´ì 
erase=ì§ì°ê¸°
print=ì¸ì
powerOff=ì ì ëê¸°
releaseBox=ë°ì¤ í´ì 
restore=ë³µì
prepare=ì¤ë¹íë¤
transaction=í¸ëì­ì: 
warehouse=ì°½ê³ : 
stationIdColon=ì¤íì´ì ID: 
thisHour=ì´ ìê°: 
lastHour=ì§ë ìê°: 
total=ì ì²´: 
erased=ì§ìì§: 
passedColon=íµê³¼ë¨: 
pendingColon=ëê¸° ì¤: 
deviceName=ì¥ì¹ ì´ë¦
localCustomization=ì§ì­ ì¬ì©ì ì ì
enableDevModeOnIos16=iOS 16+ìì ê°ë° ëª¨ë íì±í
printCustomization=ì¸ì ì¬ì©ì ì ì
setDefaultLabelOne=ê¸°ë³¸ ë ì´ë¸ 1 ì¤ì 
setDefaultLabelTwo=ê¸°ë³¸ ë ì´ë¸ 2 ì¤ì 
setPrinterLabelOne=ë ì´ë¸ 1ì© íë¦°í° ì¤ì 
rollType=ë¡¤ ì í
tape=íì´í
label=ë¼ë²¨
dymoTwinTurbo=Dymo Twin Turbo
selectTray=í¸ë ì´ ì í
left=ì¼ìª½
right=ì¤ë¥¸ìª½
setPrinterLabelTwo=ë ì´ë¸ 2ì© íë¦°í° ì¤ì 
firmware=íì¨ì´
saveSettings=ì¤ì  ì ì¥
search=ê²ì
show=íì:
installed=ì¤ì¹ë¨
available=ì¬ì© ê°ë¥
autoDownloadAsNeeded=íìí  ë ìëì¼ë¡ ë¤ì´ë¡ëë¨
directoryDir=ë¤ì´ë¡ë ëë í°ë¦¬:
whereToDownload=ì´ëì ë¤ì´ë¡ëí ì§
change=ë³ê²½
open=ì´ê¸°
downloadSelected=ì íí í­ëª© ë¤ì´ë¡ë
networkDir=ë¤í¸ìí¬ ëë í°ë¦¬:
download=ë¤ì´ë¡ë ìë£
downloadBtn=ë¤ì´ë¡ë
queued=ëê¸° ì¤
failed=ì¤í¨í¨
extracting=ì¶ì¶ ì¤
stop=ì¤ì§
downloadAgain=ë¤ì ë¤ì´ë¡ë
grade=ë±ê¸
carrier=ìºë¦¬ì´
color=ìì
battery=ë°°í°ë¦¬
lock=ì ê¸
eraseTime=ì§ì°ê¸° ìê°:
comments=ì½ë©í¸
simTechnologyColon=SIM ê¸°ì :
summary=ìì½
deviceDetails=ì¥ì¹ ì¸ë¶ ì ë³´
diagnostics=ì§ë¨
deviceInitialization=ì¥ì¹ ì´ê¸°í
connecting=ì°ê²° ì¤
pairingInProgress=ì¥ì¹ ì ë¢°
pairingFailed=íì´ë§ ì¤í¨
pairingSuccessful=íì´ë§ ì±ê³µ
deviceInfoCollected=ì¥ì¹ ì ë³´ ìì§ë¨
deviceInfoFailed=ì¥ì¹ ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
deviceImeiCollected=ì¥ì¹ IMEI ìì§ë¨
failedToGetImei=IMEI ê°ì ¸ì¤ê¸° ì¤í¨
activationInProgress=íì±í ì§í ì¤
activationFailed=íì±í ì¤í¨
activationSuccessful=íì±í ì±ê³µ
activationLockOn=íì±í ì ê¸ì´ ì¼ì ¸ ìì
pushWifiProfile=Wifi íë¡í í¸ì ì¤
fetchMdmInfoFailed=MDM ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
fetchMdmInfoSuccessful=MDM ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchKnoxInfoSuccessful=Knox ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchFrpInfoSuccessful=FRP ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchBatteryInfoSuccessful=ë°°í°ë¦¬ ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchBatteryInfoFailed=ë°°í°ë¦¬ ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
wifiPushSuccessful=Wifi í¸ì ì±ê³µ
wifiPushFailed=Wifi í¸ì ì¤í¨
devImageMounting=ê°ë° ì´ë¯¸ì§ ë§ì´í ì¤
devImageMountingFailed=ê°ë° ì´ë¯¸ì§ ë§ì´í ì¤í¨
devImageMountingSucceeded=ê°ë° ì´ë¯¸ì§ ë§ì´í ì±ê³µ
checkIfAppInstallRequired=ì± ì¤ì¹ ì§í ì¤
appInstallFailed=ì± ì¤ì¹ ì¤í¨
appInstallSuccess=ì± ì¤ì¹ ì±ê³µ
appAlreadyInstalled=ì± ì´ë¯¸ ì¤ì¹ë¨
configPushFailed=ì¤ì  í¸ì ì¤í¨
configPushSuccess=ì¤ì  í¸ì ì±ê³µ
allSyncableData=  â¢ ëª¨ë  ëê¸°í ê°ë¥í ë°ì´í°
batteryApi=  â¢ ë°°í°ë¦¬ API
testList=  â¢ íì¤í¸ ëª©ë¡
config=  â¢ êµ¬ì±
failureReasons=  â¢ ì¤í¨ ì´ì 
gradeConfig=â¢ ë±ê¸ êµ¬ì±
clientCustomization=â¢ í´ë¼ì´ì¸í¸ ì¬ì©ì ì ì
desktopResults=â¢ ë°ì¤í¬í± ê²°ê³¼
ready=ì¤ë¹ ìë£
readyHyphen=- ì¤ë¹ ìë£
appTestingDone=ì± íì¤í¸ ìë£
checkingEsn=ESN íì¸ ì¤
esnSuccessful=ESN ì±ê³µ
printing=ì¸ì ì¤
eraseInProgress=ì§ì°ê¸° ì§í ì¤
eraseSuccessful=ì§ì°ê¸° ì±ê³µ
shuttingDownPhone=í° ì¢ë£ ì¤
restoreInProgress=ë³µì ì§í ì¤
restoreSuccessful=ë³µì ì±ê³µ
batteryInfo=ë°°í°ë¦¬ ì ë³´
batteryHealth=ë°°í°ë¦¬ ìí
batteryChargePercentage=ë°°í°ë¦¬ ì¶©ì  ë°±ë¶ì¨
batteryCycles=ë°°í°ë¦¬ ì¬ì´í´
designedCapacity=ëìì¸ë ì©ë
currentCapacity=íì¬ ì©ë
batterySerialNo=ë°°í°ë¦¬ ì¼ë ¨ë²í¸
batteryModelNo=ë°°í°ë¦¬ ëª¨ë¸ ë²í¸
batteryResistance=ë°°í°ë¦¬ ì í­ (R=V/I)
batteryDrainPercent=ë°°í°ë¦¬ ë°°ì¶ ë°±ë¶ì¨
batteryDrainDuration=ë°°í°ë¦¬ ë°°ì¶ ê¸°ê°
batteryDrainResult=ë°°í°ë¦¬ ë°°ì¶ ê²°ê³¼
touchId=í°ì¹ ID
manualTouchIdRequiredMsg=ìë í°ì¹ IDê° íìí©ëë¤.\n\n ì¥ì¹ìì ë¤ìì¼ë¡ ì´ëíì­ìì¤:\n\n   >ì¤ì \n    \
  >í°ì¹ ID ë° ìí¸\n     >ì§ë¬¸ ì¶ê°\n      >í°ì¹ ID ì¼ìë¥¼ í­
faceId=ì¼êµ´ ID
done=ìë£
enterLpn=LPN ìë ¥ ëë ì¤ìº
customOneField=ì¬ì©ì ì ì 1 íë
enterCustomOneField=ì¬ì©ì ì ì 1 ìë ¥ ëë ì¤ìº
manulFaceIdRequiredMsg=ìë ì¼êµ´ IDê° íìí©ëë¤.\n\n ì¥ì¹ìì ë¤ìì¼ë¡ ì´ëíì­ìì¤:\n\n   >ì¤ì \n    \
  >Face ID ë° ìí¸\n     >Face ID ì¤ì  \n      >ìì
detectFaceIdMsg=ì¼êµ´ ID ìí ê°ì§ ì¤.\n\n ì¼êµ´ ID ë±ë¡ì ìë£íì¸ì.
appNotInstalledDueRestrictions=ì±ì´ ì±ê³µì ì¼ë¡ í¸ìëìì§ë§\nì¼ë¶ ì íì¼ë¡ ì¸í´ ì¥ì¹ì ì¤ì¹ëì§ ìììµëë¤.
appInstallationInstructionsHeading=ì± ì¤ì¹ ì§ì¹¨:
appInstallationInstructions=1. ë¤ì´ë¡ëë¡ ì´ë \n2. android_lite.apkë¥¼ ìëì¼ë¡ ì¤ì¹\n3. android. apkë¥¼ ìëì¼ë¡ \
  ì¤ì¹
cosmetics=íì¥í
cancel=ì·¨ì
save=ì ì¥
manualBatteryHealthRetrieval=ìë ë°°í°ë¦¬ ìí ê²ì
deviceNavigationForBattery=ì´ ì¥ì¹ìì ë°°í°ë¦¬ ìí ë° ì¶©ì ì ìí´ ì¤ì  ì± ë´ì ë°°í°ë¦¬ ìí ë° ì¶©ì  ì¤ì ì¼ë¡ ì´ëí´ì¼í©ëë¤.
navigateToBatterySettings=1  ë°°í°ë¦¬ ì¤ì ì¼ë¡ ì´ë
navigateToBatteryHealthCharging=2  ë°°í°ë¦¬ ìí ë° ì¶©ì ì¼ë¡ ì´ë
close=ë«ê¸°
fail=ì¤í¨
restoreError=ë³µì ì¤ë¥
ok=íì¸
reconnectDevice=ì¥ì¹ ë¤ì ì°ê²°
error=ì¤ë¥: 
pushWifi=Wifi í¸ì
installApp=ì± ì¤ì¹
pushFiles=íì¼ í¸ì
syncResults=ê²°ê³¼ ëê¸°í
failDevice=ì¥ì¹ ì¤í¨
restoreDevice=ì¥ì¹ ë³µì
copyInfoToAllDevices=ëª¨ë  ì¥ì¹ì ì ë³´ ë³µì¬
os=ì´ì ì²´ì 
mdm=MDM
imei=IMEI
oem=OEM
skuCode=SKU ì½ë
lpn=LPN
customOne=ì¬ì©ì ì ì1
jailbreakRoot=Jailbreak/ë£¨í¸
manualEntry=ìë ìë ¥
serialIdOrImeiRequired=ìë¦¬ì¼ ëë IMEIê° íìí©ëë¤.
serialNumber=ì¼ë ¨ë²í¸
submit=ì ì¶
printLabel=ë¼ë²¨ ì¸ì
edit=í¸ì§
part=ë¶í
currentSerial=íì¬ ìë¦¬ì¼
factorySerial=ê³µì¥ ìë¦¬ì¼
status=ìí
notice=ììì±ë¤
portMapping=í¬í¸ ë§¤í
hub=íë¸
next=ë¤ì
finishMapping=ë§¤í ìë£
remap=ì¬ë§¤í
enterVendorInvoiceInfo=ê³µê¸ ìì²´ ì´ë¦ ë° ì¡ì¥ ë²í¸ ìë ¥
vendorName=ê³µê¸ ìì²´ ì´ë¦
invoiceNo=ì¡ì¥ ë²í¸
qty=ìë
boxNo=ìì ë²í¸
transactionDetails=í¸ëì­ì ì¸ë¶ ì ë³´
continueTransaction=â¢â¢â¢ í¸ëì­ì ê³ì
currentTransaction=íì¬ í¸ëì­ì
bestBuy=ë² ì¤í¸ ë°ì´
invoice=ì¡ì¥ # 
box=ìì-
qtyFormatted=â¢ ìë-
of=ì¤
details=ì¸ë¶ ì ë³´
exportDetails=ì¸ë¶ ì ë³´ ë´ë³´ë´ê¸°
transactionHistory=í¸ëì­ì ê¸°ë¡
name=ì´ë¦
deviceLockLabel=ì¥ì¹ ì ê¸
notes=ë¸í¸
snColon=ìë¦¬ì¼ ë²í¸:
sn=ìë¦¬ì¼ ë²í¸
imeiColon=IMEI: 
eraseColon=ì§ì°ê¸°:
title=ì ëª©
modelNo=ëª¨ë¸ ë²í¸ 
customElement=ì¬ì©ì ì ì ìì
serialNo=ìë¦¬ì¼ ë²í¸ 
serial=ìë¦¬ì¼
gradeColon=ë±ê¸ : 
colorColon=ìì : 
batteryColon=ë°°í°ë¦¬ : 
bbc=BBC: 
versionColon=ë²ì  : 
version=ë²ì 
firmNetworkColon=íì¨ì´/ë¤í¸ìí¬ : 
firmNetwork=íì¨ì´/ë¤í¸ìí¬
esnColon=ESN : 
esn=ESN
fmiFrp=FMI/FRP : 
fmiFrpStatus=FMI/FRP ìí
cosmeticsColon:íì¥í: 
erasureColon:ì§ì: 
oemParts:OEM ë¶í : 
functionality=ê¸°ë¥ : 
seeNotes=ë¸í¸ ì°¸ì¡°
customOneColon:CUSTOM 1 : 
custom=ì¬ì©ì ì ì
port=í¬í¸ : 
userColon=ì¬ì©ì : 
user=ì¬ì©ì
dateColon=ë ì§ : 
vendorColon=ë²¤ë : 
vendor=ë²¤ë
fullFuctionalOrSeeNotes=ì ì²´ ê¸°ë¥ ëë ì°¸ê³  ì¬í­
lpnColon=LPN: 
jailbreakRooted=Jailbreak/ë£¨í : 
jailbreak=Jailbreak
deviceOffline=ì¥ì¹ ì¤íë¼ì¸ìëë¤. ë¤ì ì°ê²°íì¸ì
make=ì ì¡°ì¬:  
erasing=ì§ì°ë ì¤: 
eraseSuccess=ì§ì°ê¸° ì±ê³µ:
recoveryMode:ë³µêµ¬ ëª¨ë
verifyingFirmware=íì¨ì´ë¥¼ ê²ì¦ ì¤
restoreCompleted=ë³µì ìë£: 
deviceDisconnected=ì¥ì¹ ì°ê²° í´ì ë¨
restoreFailed=ë³µì ì¤í¨
startDeviceTesting=ì¥ì¹ íì¤í¸ ìì
appInstallFailedManualInstallReqd=ì± ì¤ì¹ ì¤í¨, ìëì¼ë¡ ì¤ì¹íì¸ì
pairingFailedReconnect=íì´ë§ ì¤í¨, ë¤ì ì°ê²°íì¸ì!
passed=íµê³¼ë¨
eraseRestricted=ì§ì°ê¸° ì íë¨
printRestricted=ì¸ì ì íë¨
deviceOperations=ì¥ì¹ ìì
esnCheck=ESN íì¸
esnCheckBlacklist=ESN ë¸ëë¦¬ì¤í¸ íì¸
esnCheckAll=ëª¨ë  ESN íì¸
provisionOrQRPrint=íë¡ë¹ì ë QR ì¸ì 
settings=ì¤ì 
openCustomizations=ì¬ì©ì ì ì ì´ê¸°
openCloudCustomizations=í´ë¼ì°ë ì¬ì©ì ì ì ì´ê¸°
about=ì ë³´
quit=ì¢ë£ 
actions=ìì
view=ë³´ê¸°
appearance=ì¸ê´
transactions=í¸ëì­ì
newTransaction=ì í¸ëì­ì
viewTransaction=í¸ëì­ì ë³´ê¸°
selectPackage=í¨í¤ì§ ì í
updateVendorInfo=ë²¤ë ì ë³´ ìë°ì´í¸
help=ëìë§
readyInAtMode=AT ëª¨ëìì ì¤ë¹ë¨
deviceInfoCollectionFailed=ì¥ì¹ ì ë³´ ìì§ ì¤í¨
imeiCollectionFailed=IMEI ìì§ ì¤í¨
batteryInfoCollected=ë°°í°ë¦¬ ì ë³´ ìì§ë¨
batteryInfoCollectionFailed=ë°°í°ë¦¬ ì ë³´ ìì§ ì¤í¨
oemDataCollected=OEM ë°ì´í° ìì§ë¨
oemDataCollectionFailed=OEM ë°ì´í° ìì§ ì¤í¨
icloudInfoCollected=ìì´í´ë¼ì°ë ì ë³´ ìì§ë¨
simInfoCollected=Sim ì ê¸ ì ë³´ ìì§ë¨
carrierLockInfoCollected=ì´ëíµì  ì ê¸ ì ë³´ ìì§ë¨
peoInitialized=PEO ì´ê¸°íë¨
startingTests=íì¤í¸ ìì ì¤
testStartedSuccessfully=íì¤í¸ê° ì±ê³µì ì¼ë¡ ììëììµëë¤
startTestsFailed=íì¤í¸ ìì ì¤í¨
fetchingMdm=MDM ì ë³´ ê°ì ¸ì¤ë ì¤
mdmSucceeded=MDM ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
mdmFailed=MDM ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
testConfigInProgress=íì¤í¸ ì¤ì  ì ì¡ ì¤
testConfigPushSuccess=íì¤í¸ êµ¬ì± í¸ì ì±ê³µ
testConfigFailed=íì¤í¸ êµ¬ì± í¸ì ì¤í¨
eraseFailed=ì§ì°ê¸° ì¤í¨
enableDevMode=ê°ë° ëª¨ë íì±í ì¤
devModeEnabled=ê°ë° ëª¨ë íì±íë¨
devModeEnableFailed=ê°ë° ëª¨ë íì±í ì¤í¨
initiatePeo=PEO ìì ì¤
preparingDevice=ì¥ì¹ ì¤ë¹ ì¤
prepareDeviceFailed=ì¥ì¹ ì¤ë¹ ì¤í¨
prepareDeviceSuccess=ì¥ì¹ë¥¼ ì±ê³µì ì¼ë¡ ì¤ë¹íì­ìì¤
esnCheckSuccessful=ESN íì¸ ì±ê³µ
uninstallAppSuccess=ì± ì ê±° ì±ê³µ
appNotInstalled=ì±ì´ ì¤ì¹ëì§ ìììµëë¤
printRequestSent=ì¸ì ìì²­ ë³´ë
notReady=ì¤ë¹ëì§ ìì
editTransaction=í¸ëì­ì í¸ì§
frpInfoCollected=FRP ì ë³´ ìì§ë¨
meidInfoCollected=MEID ì ë³´ ìì§ë¨
pcUtilityInfoCollected=PC ì í¸ë¦¬í° ì ë³´ ìì§ë¨
knoxInfoCollected=ë¹ì¤ ì ë³´ ìì§ë¨
initializing=ì´ê¸°í ì¤â¦
eraseRequested=ì§ì°ê¸° ìì²­ë¨
preparing=ì¤ë¹ ì¤
preparingFailedReconnect=ì¤ë¹ ì¤í¨, ë¤ì ì°ê²°
imeiNotFound=IMEIë¥¼ ì°¾ì ì ìì
readyStartTesting=ì¤ë¹ë¨, ì¥ì¹ìì íì¤í¸ ìì
deviceDisconnectedDuringRestore=ë³µì ì¤ì ì¥ì¹ê° ì°ê²° í´ì ë¨
deviceDisconnectedDuringErase=ì§ì°ê¸° ì¤ì ì¥ì¹ê° ì°ê²° í´ì ë¨
completeRequiredFields=íì íëë¥¼ ìì±íì¸ì
eraseRequestSent=ì§ì°ê¸° ìì²­ ë³´ë
pending=ëê¸° ì¤
defect=_ê²°í¨
portMappingInstructionStep1=í¹ì  USB í¬í¸ì í¬í¸ ë²í¸ë¥¼ í ë¹íê¸° ìí´ í¬í¸ ë§¤íì ì¤ì íì¸ì.\n \
  ì´ê¸° ì¤ì ììë ê³µì ëë MFI ì¸ì¦ë ì¼ì´ë¸ì´ ìë Apple ì¥ì¹ë¥¼ ì¬ì©íë ê²ì´ ì¢ìµëë¤.\n \
  í¬í¸ ë§¤í íìë ì¥ì¹ê° ìêµ¬íë ëª¨ë  ì¼ì´ë¸ì ì°ê²°í  ì ììµëë¤.
portMappingInstructionStep2=ì¥ì¹ìì ì¼ì´ë¸ì ì²« ë²ì§¸ USB í¬í¸ì ì°ê²°íì¬ í¬í¸ ë§¤íì ììíì¸ì. \
  USB í¬í¸ë¥¼ ì±ê³µì ì¼ë¡ ë§¤ííë©´ ìë ê·¸ë¦¼ì´ ë¹ìì¼ë¡ ë³ê²½ë©ëë¤.
portMappingInstructionStep3=í¬í¸ ë§¤íì´ ì´ë¯¸ ììµëë¤. ì¬ë§¤ííë ¤ë©´ ìë ì¬ë§¤í ë²í¼ì í´ë¦­íì¸ì.
noDeviceRecordTransaction=ì´ í¸ëì­ìì ëí ì¥ì¹ ê¸°ë¡ ìì
eraseLicenseExpired=ì§ì°ê¸° ë¼ì´ì¼ì¤ ë§ë£ë¨
eraseNotPermitted=ì§ì°ê¸°ê° íì©ëì§ ìì
eraseFailedEcidMissing=ì§ì°ê¸° ì¤í¨, ECID ëë½ë¨
eraseStoppedDeviceDisconnected=ì§ì°ê¸° ì¤ì§ë¨, ì¥ì¹ ì°ê²° í´ì ë¨
eraseStoppedTryAgain=ì§ì°ê¸° ì¤ì§ë¨, ë¤ì ìëíì¸ì
erasedWaitingForBootup=ì§ìì§, ë¶í ëê¸° ì¤
acceptDevModeOnDevice=ê°ë° ëª¨ëë¥¼ íì©íê³  ë¤ì ì°ê²°íì¸ì!
oemPartsDetails=OEM ë¶í ì¸ë¶ ì ë³´
newTransactionCreated=ì í¸ëì­ì ìì±ë¨
dmgAvailable=ì ë²ì ì© DMG ì¬ì© ê°ë¥
transactionContinued=í¸ëì­ì ê³ìë¨
failedToContinueTransaction=í¸ëì­ì ê³ì ì¤í¨
updateCloudCustomizations=í´ë¼ì°ë ì¬ì©ì ì ì ìë°ì´í¸
noManualEntryAvailable=ìë ìë ¥ ê¸°ë¡ ìì
carrierSimLock=ì´ëíµì  ì ê¸ ì ë³´
simLockInfo=Sim ì ê¸ ì ë³´
esnInfo=ESN ì ë³´
firmwareNotAvailable=í´ë¹ ëª¨ë¸ì íì¨ì´ë¥¼ ì°¾ì ì ìì
firmwareDownloadRequested=í´ë¹ ëª¨ë¸ì© íì¨ì´ ë¤ì´ë¡ë ìì²­ë¨
unableToDownloadFirmware=í´ë¹ ì¥ì¹ ëª¨ë¸ì© íì¨ì´ë¥¼ ë¤ì´ë¡ëí  ì ìì
restoreInitiated=ì¥ì¹ ë³µì íë¡ì¸ì¤ ììë¨
firmwareDownloadFailed=í´ë¹ ëª¨ë¸ì© íì¨ì´ ë¤ì´ë¡ë/ì¶ì¶ ì¤í¨
waitForFirmwareVerification=íì¨ì´ê° ìì í ê²ì¦ë  ëê¹ì§ ê¸°ë¤ë¦¬ì¸ì
firmwareDownloadStopped=ì¬ì©ìê° í´ë¹ ëª¨ë¸ì© íì¨ì´ë¥¼ ì¤ì§íìµëë¤
restoreStarted=ì¥ì¹ ë³µì íë¡ì¸ì¤ ììë¨
restoreProcessFailed=ì¥ì¹ ë³µì íë¡ì¸ì¤ ì¤í¨
dfuPushFailed=ì¥ì¹ê° ë³µêµ¬ ëª¨ëì ìì§ ììµëë¤. ìëì¼ë¡ ë³µêµ¬ ëª¨ëë¡ ì ííê³  ë¤ì ìëíì­ìì¤
dfuPushSuccess=ì¥ì¹ê° ë³µêµ¬ ëª¨ëë¡ ì´ëí¨
restoreFailedToCreateStateMachine=ë³µìì ìí ì ìí ë¨¸ì  ìì± ì¤í¨, ì¥ì¹ë¥¼ ìëì¼ë¡ ë³µêµ¬ ëª¨ëë¡ ì ííì¸ì
unableToUpdateDevice=ì¥ì¹ ìë°ì´í¸ë¥¼ ìíí  ì ììµëë¤. ì¥ì¹ê° ë¹ë°ë²í¸ë¡ ì ê²¨ ìì¼ë©° ìë°ì´í¸ë ë³µìì ìí´ ì ê¸ í´ì ëì§ ìììµëë¤.\
  ì¥ì¹ë¥¼ ì ê¸ í´ì íì¸ì
firmwareAuthorizationFailed=í´ë¹ ì¥ì¹ì ëí ì´ íì¨ì´ì ê¶í ë¶ì¬ë¥¼ ê±°ë¶íìµëë¤. ìµì  íì¨ì´ ë¤ì´ë¡ë
firmwareExtractionField=íì¨ì´ê° ì¬ë°ë¥´ê² ì¶ì¶ëì§ ìììµëë¤. íì¨ì´ë¥¼ ì¶ì¶íì¸ì
firmwareUpdaterReqFailed=íì¨ì´ ìë°ì´í° ìì²­ ì²ë¦¬ ì¤í¨
restoreFailedWithErrorCode=ì¤ë¥ë¡ ì¸í ë³µì ì¤í¨. ì¤ë¥ ì½ëë¥¼ íì¸íì¸ì
restoreFailedWithUnknownErr=ì¥ì¹ë¥¼ ë³µìí  ì ìì, ì ì ìë ì¤ë¥ê° ë°ìíìµëë¤ (ë¡ê·¸ì ì¤ë¥ ì½ë íì¸)
restoreFailedTimeout=ë³µì íë¡ì¸ì¤ ìê° ì´ê³¼, ì¥ì¹ìì ìëµì´ ìì
firmwareFileCorrupted=ë³µì ì¤ì§ë¨. ììë íì¼ ê°ì§ë¨. íì¨ì´ë¥¼ ìëì¼ë¡ ë¤ì´ë¡ëíì¸ì
dfuState=DFU
preparingState=P / ë³µìì ìí´ ì¥ì¹ ì¤ë¹ ì¤
recoveryModeState=RM
appleLogoState=AL / ë³µìì ìí´ ì¥ì¹ ì¤ë¹ ì¤
lostState=LT / ë³µìì ìí´ ì¥ì¹ ì¤ë¹ ì¤
muxState=MX / ë³µìì ìí´ ì¥ì¹ ì¤ë¹ ì¤
sendingPayload=SP / ìíí¸ì¨ì´ ë³µì ì¤
copyingFirmware=Copying Firmware
restoreCompleteState=RC
verifyingRestore=VR / ìíí¸ì¨ì´ ë³µì ì¤
flashingFirmware=FF / ìíí¸ì¨ì´ ë³µì ì¤
kernelCache=KC / ìíí¸ì¨ì´ ë³µì ì¤
flashingKernelCache=FKC / ìíí¸ì¨ì´ ë³µì ì¤
baseBand=BB / ìíí¸ì¨ì´ ë³µì ì¤
noSpaceLeftOnDevice=ì¥ì¹ì ê³µê°ì´ ë¨ì§ ìì
restoreFailedInRecoveryMode=ë³µêµ¬ ëª¨ëìì ì¥ì¹ë¥¼ ë³µìí  ì ììµëë¤. ì¥ì¹ì ë°ë¥¸ ì¥ì¹ ì´ê¸°í ì ì°¨ë¥¼ ë°ë¥´ì¸ì
serverNotReachable=ìë²ì ì°ê²°í  ì ììµëë¤. ì¸í°ë·ì íì¸íê³  ë¤ì ìëíì¸ì
restoreFailedTryAgain=ë³µì ì¤í¨, ë¤ì ìëíì¸ì
restoreFailedCableIssue=íëì¨ì´ ì¤ë¥ë¡ ë³µìì´ ì¤í¨íìµëë¤. USB í¬í¸, ì¼ì´ë¸ ëë ì»´í¨í°ë¥¼ íì¸íì­ìì¤
restoreAttemptExhausted=ë³µì ì¤í¨. ìµë ìëê° ìì§ëììµëë¤
waitForHelloScreen=ì¥ì¹ê° hello íë©´ì¼ë¡ ë³µê·íê¸°ë¥¼ ê¸°ë¤ë¦¬ë ì¤
deviceRestoreCompleted=ì¥ì¹ê° ìì í ë³µìë¨
deviceRebooting=ì¥ì¹ê° ì¬ë¶ííë ì¤
deviceNotFoundInConnection=ì°ê²°ë ì¥ì¹ìì ì¥ì¹ë¥¼ ì°¾ì ì ìì¼ë¯ë¡ ë³µìì ë¤ì ìëí©ëë¤
transactionId=í¸ëì­ì ID
transactionDate=ìì±ë¨
portNumber=í¬í¸ ë²í¸
model=ëª¨ë¸
imei2=IMEI2
countryOfOrigin=ìì°ì§
regulatoryModelNumber=ê·ì  ëª¨ë¸ ë²í¸
memory=GB
ram=ë¨
initialCarrier=ì´ê¸° íµì ì¬
pEsn=ìì¬ ESN
pEsn2=ìì¬ ESN2
meid=MEID
meid2=MEID2
decimalMeid=10ì§ MEID
decimalMeid2=10ì§ MEID2
eid=EID
simTechnology=Sim ê¸°ì 
simSerial=Sim ìë¦¬ì¼
simSerial2=Sim ìë¦¬ì¼2
wifiMacAddress=Wifi ë§¥ ì´ëë ì¤
eSimPresent=eSim ì¡´ì¬
compatibleSim=í¸íëë Sim
notCompatibleSim=í¸íëì§ ìë Sim
network=ë¤í¸ìí¬
networkDetails=ë¤í¸ìí¬ ì¸ë¶ì ë³´
deviceLock=FMI / FRP
mdmResponse=MDM ìëµ
knox=Knox
rooted=ë£¨íë¨
appleId=Apple Id
batteryHealthPercentage=ë°°í°ë¦¬ ê±´ê° ìí í¼ì¼í¸
oemBatteryHealth=OEM ë°°í°ë¦¬ ê±´ê° ìí
cocoBatteryHealth=Coco ë°°í°ë¦¬ ê±´ê° ìí
batteryCycle=ë°°í°ë¦¬ ì¬ì´í´ ì
batteryCurrentMaxCapacity=ë°°í°ë¦¬ íì¬ ì©ë
cocoCurrentCapacity=Coco íì¬ ì©ë
batteryDesignMaxCapacity=ë°°í°ë¦¬ ëìì¸ ì©ë
cocoDesignCapacity=Coco ëìì¸ ì©ë
batterySerial=ë°°í°ë¦¬ ìë¦¬ì¼
batteryModel=ë°°í°ë¦¬ ëª¨ë¸
batterySource=ë°°í°ë¦¬ ì¶ì²
batteryTemperature=ë°°í°ë¦¬ ì¨ë
batteryDrainType=ë°°í°ë¦¬ ë°©ì  ì í
startHeat=ì´ ìì
endHeat=ì´ ë
batteryChargeStart=ë°°í°ë¦¬ ì¶©ì  ìì
batteryChargeEnd=ë°°í°ë¦¬ ì¶©ì  ì¢ë£
batteryDrain=ë°°í°ë¦¬ ë°©ì 
startBatteryCharge=ë°°í°ë¦¬ ì¶©ì  ìì
endBatteryCharge=ë°°í°ë¦¬ ì¶©ì  ì¢ë£
totalBatteryDrain=ì´ ë°°í°ë¦¬ ë°©ì 
batteryDrainInfo=ë°°í°ë¦¬ ë°©ì  ì ë³´
batteryHealthGrade=ë°°í°ë¦¬ ê±´ê° ë±ê¸
deviceCreatedDate=ì¶ê°ë ë ì§
deviceUpdatedDate=ìë°ì´í¸ë ë ì§
testPlanName=íì¤í¸ ê³í ì´ë¦
working=ìë
defectsCode=ê²°í¨ ì½ë
manualFailure=ìë ì¤í¨
simErased=Sim ì§ìì§
eSimErased=eSim ì§ìì§
type=ì§ìì§ ì í
startTime=ì§ì°ê¸° ìì ìê°
endTime=ì§ì°ê¸° ì¢ë£ ìê°
eraserDiff=ì§ì°ê¸° ì´ ìê°
erasedNotes=ì§ì°ê¸° ë¸í¸
restoreCode=ë³µì ì½ë
deviceState=ì¥ì¹ ìí
productCode=ì í ì½ë
bMic=BMic
vMic=VMic
fMic=FMic
simLock=Sim ì ê¸
unlockStatus=ì ê¸ í´ì  ìí
simLockResponse=Sim ì ê¸ ìëµ
carrierLockResponse=ì´ëíµì  ì ê¸ ìëµ
apiResponse=API ìëµ
errorCode=í¨í¤ì§ ì´ë¦
screenTime=íë©´ ìê°
stationId=ì¬ì©ì ID
testerName=íì¤í° ì´ë¦
licenseId=ë¼ì´ì ì¤ ID
deviceShutdown=FCCID
oemStatus=OEM ìí
parts=OEM ë¶í
iftCodes=IFT ì½ë
preCheckWorking=ì¬ì  íì¸ ìë
preCheckFailed=ì¬ì  íì¸ ì¤í¨
preCheckPassed=ì¬ì  íì¸ íµê³¼
preCheckPending=ì¬ì  íì¸ ëê¸° ì¤
transactionType=í¸ëì­ì ì í
eBayRefurbished=eBay ì¬ìí
eBayRejection=eBay ê±°ë¶
dataVerification=ë°ì´í° ê²ì¦
genuine=ì§í
notGenuine=ë³¸í ìë
na=N/A
mainBoard=ë©ì¸ ë³´ë
frontCamera=ì ë©´ ì¹´ë©ë¼
backCamera=íë©´ ì¹´ë©ë¼
lcd=LCD
downloadingUpdates=ìë°ì´í¸ ë¤ì´ë¡ë ì¤
build=ë¹ë
invalidUserNameAndPwd=ì í¨í ì¬ì©ì ì´ë¦ê³¼ ìí¸ë¥¼ ìë ¥íì¸ì
simlockLicenseExpired=ì¬ë½ ë¼ì´ì ì¤ ë§ë£
simlockCheckError=ì¬ë½ íì¸ ì¤ë¥
deviceEcidMissing=ì¥ì¹ ECID ëë½ë¨
checkInternetConnection=ì¸í°ë· ì°ê²°ì íì¸íì¸ì
esnLicenseExpired=ESN íì¸ ë¼ì´ì ì¤ ë§ë£ë¨
esnCheckFailed=ESN íì¸ ì¤í¨
failedToFetchDeviceLicenses=ì¥ì¹ ë¼ì´ì ì¤ë¥¼ ê°ì ¸ì¤ì§ ëª»íìµëë¤
deviceEraseLicenseMasterDateNotStarted=ë§ì¤í° ë ì§ê° ìì§ ììëì§ ììê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceEraseLicenseMasterExpired=ë§ì¤í°ê° ë§ë£ëìê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceEraseLicenseUserDateNotStarted=ì¬ì©ì ë ì§ê° ìì§ ììëì§ ììê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceEraseLicenseUserExpired=ì¬ì©ìê° ë§ë£ëìê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceConnectionLicenseDeviceEraseLicenseLicenseExpired=ì¥ì¹ ë¼ì´ì ì¤ ë§ë£ë¡ ì¤í¨íìµëë¤
deviceConnectionLicenseMasterDateNotStarted=ë§ì¤í° ë ì§ê° ìì§ ììëì§ ììê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceConnectionLicenseMasterExpired=ë§ì¤í°ê° ë§ë£ëìê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceConnectionLicenseUserDateNotStarted=ì¬ì©ì ë ì§ê° ìì§ ììëì§ ììê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceConnectionLicenseUserExpired=ì¬ì©ìê° ë§ë£ëìê¸° ëë¬¸ì ì¤í¨íìµëë¤
deviceConnectionLicenseLicenseExpired=ì¥ì¹ ë¼ì´ì ì¤ ë§ë£ë¡ ì¤í¨íìµëë¤
powerOffSuccess=ì ì ëê¸° ì±ê³µ
powerOffFailedNoDevice=ì¥ì¹ê° ìì´ ì ì ëê¸° ì¤í¨
powerOffFailedUnknownErr=ì ì ìë ì´ì ë¡ ì ì ëê¸° ì¤í¨
uninstallAppFailed=ì± ì ê±° ì¤í¨
uninstallProfileFailed=íë¡í ì ê±° ì¤í¨
uninstallAppProfileFailed=ì± ë° íë¡í ì ê±° ì¤í¨
failedToGetInfoReconnect=ì ë³´ë¥¼ ê°ì ¸ì¤ë ë° ì¤í¨íìµëë¤. ë¤ì ì°ê²°íì¸ì!
cantFindDeviceReconnect=ì¥ì¹ë¥¼ ì°¾ì ì ììµëë¤. ë¤ì ì°ê²°íì¸ì!
noTestResultYet=ìì§ íì¤í¸ ê²°ê³¼ê° ììµëë¤
errorOccurredInPeo=PEOìì ì¤ë¥ê° ë°ìíìµëë¤
errorInStartingPhonecheck=Phonecheck ìì ì¤ ì¤ë¥ê° ë°ìíìµëë¤. %d ì´ í ì íë¦¬ì¼ì´ìì´ ë«íëë¤
mqttClientNotConnected=MQTT í´ë¼ì´ì¸í¸ê° ë¸ë¡ì»¤ì ì°ê²°ëì§ ìììµëë¤.\s ê°ë¥í ì´ì :\s 1- ë°ëª¬ ì íë¦¬ì¼ì´ìì´ ì¤íëì§ ìì\
  2- MQTT ë¸ë¡ì»¤ì ì°ê²°íë ëì ì¤ë¥ê° ë°ìíìµëë¤ \s %d ì´ í ì íë¦¬ì¼ì´ìì´ ë«íëë¤
serialColon=ìë¦¬ì¼:
lidarSerialColon=ë¼ì´ë ìë¦¬ì¼: 
syncFailed=ëê¸°í ì¤í¨
prepareFailed=ì¤ë¹ ì¤í¨
prepareFailedTryManually=ì¤ë¹ì ì¤í¨íìµëë¤. ìëì¼ë¡ ìëí´ ë³´ì¸ì
restartingForNotices=ìë¦¼ì ìí ì¬ìì
deviceInUsbMode=USB ëª¨ëìì ì¥ì¹
appInstallFailedHyphen=- ì± ì¤ì¹ ì¤í¨
connectingHyphen=- ì°ê²° ì¤
pairingInProgressHyphen=- íì´ë§ ì§í ì¤
pairingFailedHyphen=- íì´ë§ ì¤í¨
pairingSuccessfulHyphen=- íì´ë§ ì±ê³µ
deviceInfoCollectedHyphen=- ì¥ì¹ ì ë³´ ìì§ë¨
deviceInfoFailedHyphen=- ì¥ì¹ ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
deviceImeiCollectedHyphen=- ì¥ì¹ IMEI ìì§ë¨
failedToGetImeiHyphen=- IMEI ê°ì ¸ì¤ê¸° ì¤í¨
activationInProgressHyphen=- íì±í ì§í ì¤
activationFailedHyphen=- íì±í ì¤í¨
activationSuccessfulHyphen=- íì±í ì±ê³µ
activationLockOnHyphen=- íì±í ì ê¸ ì¼ì§
pushWifiProfileHyphen=- ìì´íì´ íë¡í í¸ì ì¤
fetchMdmInfoFailedHyphen=- MDM ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
fetchMdmInfoSuccessfulHyphen=- MDM ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchKnoxInfoSuccessfulHyphen=- Knox ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchFrpInfoSuccessfulHyphen=- FRP ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchBatteryInfoSuccessfulHyphen=- ë°°í°ë¦¬ ì ë³´ ê°ì ¸ì¤ê¸° ì±ê³µ
fetchBatteryInfoFailedHyphen=- ë°°í°ë¦¬ ì ë³´ ê°ì ¸ì¤ê¸° ì¤í¨
wifiPushSuccessfulHyphen=- ìì´íì´ í¸ì ì±ê³µ
wifiPushFailedHyphen=- ìì´íì´ í¸ì ì¤í¨
devImageMountingHyphen=- ê°ë° ì´ë¯¸ì§ ë§ì´í¸ ì¤
devImageMountingFailedHyphen=- ê°ë° ì´ë¯¸ì§ ë§ì´í¸ ì¤í¨
devImageMountingSucceededHyphen=- ê°ë° ì´ë¯¸ì§ ë§ì´í¸ ì±ê³µ
checkIfAppInstallRequiredHyphen=- ì± ì¤ì¹ ì§í ì¤
appInstallSuccessHyphen=- ì± ì¤ì¹ ì±ê³µ
appAlreadyInstalledHyphen=- ì± ì´ë¯¸ ì¤ì¹ë¨
configPushInProgressHyphen=- êµ¬ì± ì ì¡ ì§í ì¤
configPushFailedHyphen=- êµ¬ì± í¸ì ì¤í¨
configPushSuccessHyphen=- êµ¬ì± í¸ì ì±ê³µ
appTestingDoneHyphen=- ì± íì¤í¸ ìë£
checkingEsnHyphen=- ESN íì¸ ì¤
esnSuccessfulHyphen=- ESN ì±ê³µ
printingHyphen=- íë¦°í
eraseInProgressHyphen=- ì§ì°ê¸° ì§í ì¤
eraseSuccessfulHyphen=- ì§ì°ê¸° ì±ê³µ
shuttingDownPhoneHyphen=- ì í ì¢ë£ ì¤
restoreInProgressHyphen=- ë³µì ì§í ì¤
restoreSuccessfulHyphen=- ë³µì ì±ê³µ
clickOkAfterUpdates=ë¸ë¼ì°ì ìì ì¬ì©ì ì ìë¥¼ ìë°ì´í¸í í <b>íì¸</b>ì í´ë¦­íì¸ì.
browserActionNotSupported1=ë¸ë¼ì°ì ìì ì¬ì©ì ì ìë¥¼ ì´ ì ììµëë¤. ë¸ë¼ì°ì§ ììì´ ì§ìëì§ ììµëë¤. ì´ë:
browserActionNotSupported2=  ê·¸ë¦¬ê³  í ë¹ë íë¡íì ìë°ì´í¸íì¸ì.
vendorInformation=ê³µê¸ì ì ë³´
dataMissing=ë°ì´í° ëë½
manualPEO=ìë PEO
eSimPresentColon=eSim íì¬:
dualSimColon=ëì¼ SIM:
simCarrierColon=Sim ì´ì¡ì:
customizations=ì¬ì©ì ì ì
labelPrinting=ë¼ë²¨ ì¸ì
labelOne=ë¼ë²¨ 1
labelTwo=ë¼ë²¨ 2
printerOneForLabelOne=ë¼ë²¨ 1ì© íë¦°í° 1
printerTwoForLabelTwo=ë¼ë²¨ 2ì© íë¦°í° 2
printToPdf=PDFë¡ ì¸ì
pleaseSelect=ì íí´ ì£¼ì¸ì
none=ìì
restoring=ë³µì ì¤:
pairTheDeviceAgain=ëë°ì´ì¤ ë¤ì íì´ë§
carrierLock=ì´ìì ì ê¸
exportLog=ë¡ê·¸ ë´ë³´ë´ê¸°
sourceApiCalling=- ìì¤ API í¸ì¶
reProcessDeviceAction=ì¥ì¹ ìì ì¬ì²ë¦¬
previouslyProcessedDevice=ì´ ì¥ì¹ë ì´ì ì ì²ë¦¬ëììµëë¤
rePrintLabel=ë¼ë²¨ ì¬ì¸ì
reProcessDevice=ì¥ì¹ ì¬ì²ë¦¬
eraseForMdm=MDMì ìí ì­ì 
mdmErasedWaitingForBootup=MDM ì­ì ë¨, ë¶í ëê¸° ì¤
pushWiFi=í¸ì Wi-Fi
appInstall=ì± ì¤ì¹
removeAppAndProfiles=ì± ë° íë¡í ì ê±°
eraseFailedAppNotInstalled=ì­ì  ì¤í¨, ì±ì´ ì¤ì¹ëì§ ìììµëë¤
phonecheckIsStarting=Phonecheckê° ìì ì¤ìëë¤...
updateAvailable=ìë°ì´í¸ ê°ë¥
newVersionAvailable=ì¬ì© ê°ë¥í Phonecheckì ì ë²ì ì´ ììµëë¤.
likeToUpdateNow=ì§ê¸ ìë°ì´í¸íìê² ìµëê¹?
updateNow=ì§ê¸ ìë°ì´í¸
later=ëì¤ì
installingUpdate=ìë°ì´í¸ ì¤ì¹ ì¤
eraseFailedSerialPortClosed=ì§ì°ê¸°ì ì¤í¨íìµëë¤. ì§ë ¬ í¬í¸ë¥¼ ì´ ì ììµëë¤.
restoreSuccess=ë³µì ì±ê³µ:
queuedForRestore=ì¥ì¹ê° ë³µìì ìí´ ëê¸° ì¤ìëë¤
restoreNeeded=ì¥ì¹ì ëí ë³µìì´ íìí©ëë¤
simCardError=SIM ì¹´ë ê°ì§ë¨
simCardErrorMsg=ê³ìíë ¤ë©´ SIM ì¹´ëë¥¼ ì ê±°íì­ìì¤
skip=ê±´ëë°ê¸°
removeSimCardMsg=SIM ì¹´ëë¥¼ ì ê±°íê³  ì­ì ë¥¼ ë¤ì ììíì­ìì¤
warningSkip=ê±´ëë°ê¸° í ì­ì  ìì²­ ì ì¡
simCardWarnMsg=ì¥ì¹ì SIM ì¹´ëê° ììµëë¤
sdCardError=SD ì¹´ë ê°ì§ë¨
sdCardErrorMsg=ê³ìíë ¤ë©´ SD ì¹´ëë¥¼ ì ê±°íì­ìì¤
removeSdCardMsg=SD ì¹´ëë¥¼ ì ê±°íê³  ë¤ì ì§ì°ê¸°ë¥¼ ììíì­ìì¤
sdCardWarnMsg=ì¥ì¹ì SD ì¹´ëê° ììµëë¤
restoreThreads=ë³µì ì¤ë ë ì:
restoreReqSent=ë³µì ìì²­ì´ ì ì¡ëììµëë¤
headsetPort=í¤ëì í¬í¸
headsetLeft=í¤ëì-ì¼ìª½
headsetRight=í¤ëì-ì¤ë¥¸ìª½
audioInput=ì¤ëì¤ ìë ¥
autoLS=ìë LS
loudSpeaker=ì¤í¼ì»¤
loudSpeakerM=ì¤í¼ì»¤-M
micLSTest=ë§ì´í¬ LS íì¤í¸
microphone=ë§ì´í¬
videoMicrophone=ë¹ëì¤ ë§ì´í¬
headsetLightPort=í¤ëì ë¼ì´í¸ í¬í¸
bottomMicQuality=íë¨ ë§ì´í¬ íì§
earpieceQuality=ì´ì´í¼ì¤ íì§
frontMicQuality=ì ë©´ ë§ì´í¬ íì§
microphoneQuality=ë§ì´í¬ íì§
rearMicQuality=íë©´ ë§ì´í¬ íì§
recordingQuality=ë¹ì íì§
videoMicrophoneQuality=ë¹ëì¤ ë§ì´í¬ íì§
bottomMicPB=íë¨ ë§ì´í¬ PB
ESPlayback=ES ì¬ì
frontMicPB=ì ë©´ ë§ì´í¬ PB
manualRecording=ìë ë¹ì
micPlayback=ë§ì´í¬ ì¬ì
microphonePB=ë§ì´í¬ PB
rearMicPB=íë©´ ë§ì´í¬ PB
videoMicPB=ë¹ëì¤ ë§ì´í¬ PB
manualAudioQuality=ìë ì¤ëì¤ íì§
manualRingtoneQuality=ìë ë²¨ìë¦¬ íì§
sPen=Sí
sPenBackButton=Sí ë¤ë¡ ë²í¼
sPenHover=Sí í¸ë²
sPenMenuButton=Sí ë©ë´ ë²í¼
sPenRemove=Sí ì ê±°
spenPlusButtons=Sí íë¬ì¤ ë²í¼
backButton=ë¤ë¡ ë²í¼
buttonsTest=ë²í¼ íì¤í¸
flipSwitch=ì¤ìì¹
homeButton=í ë²í¼
menuButton=ë©ë´ ë²í¼
powerButton=ì ì ë²í¼
volumeDownButton=ë³¼ë¥¨ ë®ì¶ê¸° ë²í¼
volumeUpButton=ë³¼ë¥¨ ëì´ê¸° ë²í¼
sDCardDetect=SD ì¹´ë ê°ì§
sDCardRemove=SD ì¹´ë ì ê±°
alertSlider=ìë¦¼ ì¬ë¼ì´ë
bixbyButton=ë¹ì¤ë¹ ë²í¼
autoSnapFront=ìë ì ë©´ ì´¬ì
cameraTest=ì¹´ë©ë¼ íì¤í¸
frontCameraQuality=ì ë©´ ì¹´ë©ë¼ íì§
frontVideoCamera=ì ë©´ ë¹ëì¤ ì¹´ë©ë¼
autoSnapRear=ìë íë©´ ì´¬ì
cameraAutoFocus=ì¹´ë©ë¼ ìë ì´ì 
flash=íëì
flashlight=ìì ë±
rearCamera=íë©´ ì¹´ë©ë¼
rearCameraQuality=íë©´ ì¹´ë©ë¼ íì§
rearVideoCamera=íë©´ ë¹ëì¤ ì¹´ë©ë¼
ultraWideCamera=ì´ê´ê° ì¹´ë©ë¼
ultraWideCameraQuality=ì´ê´ê° ì¹´ë©ë¼ íì§
telephotoCamera=ë§ì ì¹´ë©ë¼
telephotoCameraQuality=ë§ì ì¹´ë©ë¼ íì§
rearCamtoGallery=íë©´ ì¹´ë©ë¼ ê°¤ë¬ë¦¬ë¡
barcodeScan=ë°ì½ë ì¤ìº
imageCaptureTime=ì´ë¯¸ì§ ìº¡ì² ìê°
manualCameraTest=ìë ì¹´ë©ë¼ íì¤í¸
manualCameras=ìë ì¹´ë©ë¼
qRTest=QR íì¤í¸
fingerprintSensor=ì§ë¬¸ ì¼ì
manualVibration=ìë ì§ë
proximitySensor=ê·¼ì  ì¼ì
vibration=ì§ë
accelerometer=ê°ìëê³
autoAccelerometer=ìë ê°ìëê³
gyroscope=ìì´ë¡ì¤ì½í
screenRotation=íë©´ íì 
barometer=ê¸°ìê³
compass=ëì¹¨ë°
nonSamsungFingerprint=ë¹ì¼ì± ì§ë¬¸
samsungFingerprint=ì¼ì± ì§ë¬¸
3DTouch=3D í°ì¹
digitizer=ëì§íì´ì 
edgeScreen=ì£ì§ ì¤í¬ë¦°
fingerTrailDigitizer=ìê°ë½ ì¶ì  ëì§íì´ì 
forceTouch=í¬ì¤ í°ì¹
glassCondition=ì ë¦¬ ìí
glassCracked=ì ë¦¬ ê· ì´
lCD=LCD
autoQRCode=ìë QR ì½ë
digitizerNPattern=ëì§íì´ì  N í¨í´
manualQRCode=ìë QR ì½ë
multiTouchTest=ë©í° í°ì¹ íì¤í¸
multiTouch=ë©í°í°ì¹
samDigi=ì¼ì± ëì§
brightness=ë°ê¸°
customTests=ì¬ì©ì ì ì íì¤í¸
sWVersion=SW ë²ì 
IMEIScreenshot=IMEI ì¤í¬ë¦°ì·
callTest=íµí íì¤í¸
networkConnectivity=ë¤í¸ìí¬ ì°ê²°
simReader=SIM ë¦¬ë
bluetooth=ë¸ë£¨í¬ì¤
WiFi=ìì´íì´
dualCallTest=ëì¼ íµí íì¤í¸
networkConnectivity1=ë¤í¸ìí¬ ì°ê²° 1
networkConnectivity2=ë¤í¸ìí¬ ì°ê²° 2
simReader1=SIM ë¦¬ë 1
simReader2=SIM ë¦¬ë 2
enhancedBluetooth=í¥ìë ë¸ë£¨í¬ì¤
gPS=GPS
nFC=NFC
dualSimReaderTest=ëì¼ SIM ë¦¬ë íì¤í¸
simReaderKey=SIM ë¦¬ë í¤
simReaderTest=SIM ë¦¬ë íì¤í¸
simRemove=SIM ì ê±°
simRemoveKey=SIM ì ê±° í¤
chargePort=ì¶©ì  í¬í¸
wirelessCharging=ë¬´ì  ì¶©ì 
colors=ìì
grading=ë±ê¸
housing=íì°ì§
earSpeaker=ì´ì´ ì¤í¼ì»¤
frontCameraFocus=ì ë©´ ì¹´ë©ë¼ ì´ì 
rearCameraFocus=íë©´ ì¹´ë©ë¼ ì´ì 
frontMicrophone=ì ë©´ ë§ì´í¬
headsetMediaButton=í¤ëì ë¯¸ëì´ ë²í¼
headsetVolumeDown=í¤ëì ë³¼ë¥¨ ë®ì¶ê¸°
headsetVolumeUp=í¤ëì ë³¼ë¥¨ ëì´ê¸°
lightSensor=ë¹ ì¼ì
loudspeakerQuality=ì¤í¼ì»¤ íì§
micRecording=ë§ì´í¬ ë¹ì
batteryWarning=ë°°í°ë¦¬ ê²½ê³ 
faceID=ì¼êµ´ ID
earpiece=ì´ì´í°
validateImei=IMEI/ì¼ë ¨ë²í¸ ì¤ìº
sdCardDetected=SD ì¹´ë ê°ì§ë¨
simCardDetected=SIM ì¹´ë ê°ì§ë¨
refreshCustomizations=ì¬ì©ì ì ìê° ê²ìëì§ ìììµëë¤. <b>ìë¡ ê³ ì¹¨</b>ì í´ë¦­íì­ìì¤.
refresh=ìë¡ ê³ ì¹¨
refreshCloudCustomizations=í´ë¼ì°ë ì¬ì©ì ì ì ìë¡ ê³ ì¹¨
dataFound=ê¸°ê¸°ìì ë°ì´í° ë°ê²¬
dataNotFound=ê¸°ê¸°ìì ë°ì´í° ë°ê²¬ëì§ ìì
bHA=BH-A
bHB=BH-B
bHC=BH-C
bHD=BH-D
iMEI/SerialMismatch=IMEI/SerialMismatch
cloudDeviceLookup=í´ë¼ì°ë ê¸°ê¸° ì¡°í
yes=ì
no=ìëì
areYouSure=íì¤í©ëê¹?
removeDevice=ì¥ì¹ ì ê±°
gradeComplete=íê° ìë£
doesTheDevicePowerUpAndFunctionNormally=ì¥ì¹ê° ì ìì ì¼ë¡ ì¼ì§ê³  ìëí©ëê¹?
retry=ë¤ì ìë
rawResponse=ìì ìëµ
routeError=ê²½ë¡ ì¤ë¥
shopFloorCantBeEstablished=ìì° íì¥ì ì¤ì í  ì ììµëë¤
profileName=íë¡í ì´ë¦
sourceApiError=ìì¤ API - ì¤ë¥
labelApiError=ë¼ë²¨ API - ì¤ë¥
resultsApiError=ê²°ê³¼ API - ì¤ë¥
labelApiCalling=- ë¼ë²¨ API í¸ì¶
resultsApiCalling=- ê²°ê³¼ API í¸ì¶

tryingNextRestoreAttempt=ìëì í¨ê» ë³µì ë¤ì ìë ì¤: 
downloadFirmwareTo=íì¨ì´ ë¤ì´ë¡ë ìì¹
mDM=MDM
authenticity=ì§ì ì± (ì§ì ì±)
insertSIM=SIM ì½ì
noUnlockCodeFound=ì ê¸ í´ì  ì½ëê° ììµëë¤
networkUnlockSuccessful=ë¤í¸ìí¬ ì ê¸ í´ì  ì±ê³µ
networkUnlockFailed=ë¤í¸ìí¬ ì ê¸ í´ì  ì¤í¨
failedToUnlock=ì ê¸ í´ì  ì¤í¨
imeiUnlockCodeNotFound=IMEI/ì ê¸ í´ì  ì½ëë¥¼ ì°¾ì ì ììµëë¤
deviceAlreadyUnlocked=ê¸°ê¸°ê° ì´ë¯¸ ì ê¸ í´ì ëììµëë¤
performingNetworkUnlock=ë¤í¸ìí¬ ì ê¸ í´ì  ìí ì¤
performNetworkUnlock=ì½ëë¡ Android ì ê¸ í´ì 
modifiedSoftware=ì¥ì¹ì ì¹ì¸ëì§ ìì ìíí¸ì¨ì´ ìì ì´ ìì ì ììµëë¤
downgradingSoftware=ì´ì  ë²ì ì iOSë¥¼ ì¤ì¹íë ¤ê³  í  ì ììµëë¤
mdmOffDevicePrepared=MDM êº¼ì§, ì¥ì¹ ì¤ë¹ë¨
mdmOnDeviceNotPrepared=MDM ì¼ì§, ì¥ì¹ ì¤ë¹ ì¤í¨
paperType=ì©ì§ ì¢ë¥
corrupted=ììë¨
extract=ì¶ì¶íë¤
verify=íì¸
verifying=íì¸ ì¤
restartUsbmuxd=usbmuxd ì¬ìì
usbmuxdMessage=ì¥ì¹ë¥¼ ë¶ë¦¬íê³  íì¸ì í´ë¦­íì¬ usbmuxdë¥¼ ì¬ììíì­ìì¤.
oEMParts=OEM ë¶í
adminPass=ê´ë¦¬ì ë¹ë°ë²í¸
eraseOverridden=ë®ì´ì´ ë´ì© ì§ì°ê¸°
eraseStarted=ì§ì°ê¸° ììë¨
printer=íë¦°í°
waitForFirmwareDownload=íì¨ì´ê° ìì í ë¤ì´ë¡ëë  ëê¹ì§ ê¸°ë¤ë¦¬ì­ìì¤
downloadingFirmware=íì¨ì´ ë¤ì´ë¡ë ì¤
updatedOs=ê¸°ê¸°ê° ìë°ì´í¸ë ì´ì ì²´ì ë¥¼ ê°ì§ê³  ììµëë¤. ë³µìí  íìê° ììµëë¤
deviceOsUpdated=ìë°ì´í¸ë ì´ì ì²´ì ê° ììµëë¤
restoreProcessFailedIntermittent=ìëì ëí ë³µìì´ ì¤í¨íìµëë¤
esnCheckUsInsuranceBlacklist=ë¯¸êµ­ ë³´í ë¸ëë¦¬ì¤í¸ íì¸
androidUnlockCodesHeader=Android ì ê¸ í´ì  ì½ë ì ë³´
androidUnlockCodesMsg=Android ì ê¸ í´ì  ì½ëê° ë¤ì´ë¡ëëë ëì ê¸°ë¤ë ¤ ì£¼ì­ìì¤
firmwareAvailable=íì¨ì´ ì¬ì© ê°ë¥
firmwareVerified=íì¨ì´ê° íì¸ë¨/ì±ê³µì ì¼ë¡ ë¤ì´ë¡ëë¨
restoreLicenseExpired=ë¼ì´ì ì¤ ë§ë£ ë³µì
exportLocation=ë´ë³´ë´ê¸° ìì¹:
autoExportDeviceData=ì¥ì¹ ë°ì´í°ë¥¼ ìëì¼ë¡ ë´ë³´ë´ê¸°
browse=ì°¾ìë³´ê¸°
format=íì:
successfulEraseRestore=ì±ê³µì ì¸ ì­ì /ë³µì ì
onAppResults=ì± ê²°ê³¼ ì
enableAutoOpenIOSApp=iOS ì± ìë ì¤í íì±í
conformityMark=ê¸°ì ì í©ì±ë§í¬
manualConformityMark=M-ê¸°ì ì í©ì±ë§í¬
iosDevice=IOS ì¥ì¹
mode=ëª¨ë
defaultMode=ê¸°ë³¸ ëª¨ë 
airpodsMode=ìì´í ì§ë¨
iWatchMode=iWatch ì§ë¨
preCheckMode=ì¬ì  ì ê² ëª¨ë
pinLockColon=í ì ê¸ íì±íë¨:
killAdbProcess=adb íë¡ì¸ì¤ ì¢ë£
removeDeviceLockAndContinue=ê¸°ê¸°ìì ê³ì ì ì­ì íê³  ê³ìíì¸ì.
removePinLockAndContinue=ê¸°ê¸°ìì í ì ê¸ ì¥ì¹ë¥¼ ì ê±°íê³  ê³ìíì¸ì.
labelWorkFlow=ìí¬íë¡ì° ë¼ë²¨
oneFlowForAllTestResults=ëª¨ë  íì¤í¸ ê²°ê³¼ë¥¼ ì²ë¦¬íë ë¨ì¼ íë¡ì°
separateFlowForTestResultsWorkingOrFailed=ìë ì¤ì¸ íì¤í¸ ê²°ê³¼ íë¡ì°/ì¤í¨í íì¤í¸ ê²°ê³¼ íë¡ì°
simSerialColon=SIM ì¼ë ¨ ë²í¸: 
restoreDelay=ë³µì ì§ì°
waitForFirmwareExtraction=íì¨ì´ê° ìì í ì¶ì¶ë  ëê¹ì§ ê¸°ë¤ë¦¬ì­ìì¤
firmwareExtractionReq=íì¨ì´ê° ì¶ì¶ ì¤ìëë¤
fetchXiaomiAlternateSerial=ì¤ì¤ë¯¸ ëì²´ ìë¦¬ì¼ ê°ì ¸ì¤ê¸°
noUserToken=í í°ì´ ìì±ëì§ ìììµëë¤. ì§ìíì ë¬¸ìíì¸ì.
osNotSupported=ì§ìëì§ ìë OS ë²ì 
airpodsNotSupportedMessage=AirPods ì§ë¨ì ì¤ííë ¤ë©´ macOS 14 ì´ìì´ íìí©ëë¤. macOS ìë¸ë§ ëë ê·¸ ì´ìì¼ë¡ ìë°ì´í¸íì­ìì¤.
changeTransaction=ê±°ë ë³ê²½
selectTransaction=ê±°ë ì í
move=ì´ë
transactionMovedSuccessfully=ê±°ëê° ì±ê³µì ì¼ë¡ ì´ëëììµëë¤
cannotMoveTransactionMsg=ìë³¸ ë° ëì ê±°ë IDë ëì¼í  ì ììµëë¤
transactionChangeError=ì¼ë ¨ë²í¸ì ëí ê±°ë ë³ê²½ ì¤í¨: 
icloudLocked=ìì´í´ë¼ì°ë ì ê¹
prepareFailedDueToMdm=MDMë¡ ì¸í´ ì¤ë¹ì ì¤í¨íìµëë¤
prepareFailedNoCustomization=ê³ìíë ¤ë©´ ìëì¼ë¡ ì¤ë¹íê±°ë ì¥ì¹ë¥¼ ì§ì°ì­ìì¤
prepareFailedMsg=ì¤ë¹ ì¤í¨
delete=ì­ì 
refreshCache=ìºì ìë¡ ê³ ì¹¨
eraseTitle=ì­ì  ìì íì¸
eraseContentText=ì­ì ë¥¼ ì§ííìê² ìµëê¹?
ignoreNaForOem=OEMì ëí N/A ë¬´ì
recheckBatteryInfo=ë°°í°ë¦¬ ì ë³´ë¥¼ ë¤ì íì¸
cloudUnReachable=í´ë¼ì°ë ìë¹ì¤ë¥¼ ì¬ì©í  ì ììµëë¤. ì§ìíì ë¬¸ìíì­ìì¤.
photoOfDeviceRequired=ê¸°ê¸° ì¬ì§ íì
openCamera=ì¹´ë©ë¼ ì´ê¸°
performShopfloor=ììì¥ì ìííë¤
readyInPreCheckMode=ì¬ì  ì ê² ëª¨ëìì ì¤ë¹ ìë£
panicFull=í¨ë ê°ëí

#PreCheck test labels keys
preCheckAllButton=ëª¨ë  ë²í¼ì ëë¥´ì¸ì
preCheckPower=ì ì ë²í¼ì ëë¥´ì¸ì
preCheckHome=í ë²í¼ì ëë¥´ì¸ì
preCheckProximity=ì¼ì ììì ìì íëì¸ì
preCheckRingerOff=ë²¨ìë¦¬ë¥¼ ëì¸ì
preCheckRingerOn=ë²¨ìë¦¬ë¥¼ ì¼ì¸ì
preCheckTouchScreen=íë©´ì í­íì¸ì
preCheckVolumeDown=ë³¼ë¥¨ ìê² ë²í¼ì ëë¥´ì¸ì
preCheckVolumeUp=ë³¼ë¥¨ í¬ê² ë²í¼ì ëë¥´ì¸ì
lcdColor=íë©´ì´ í°ìê³¼ ê²ìì ì¬ì´ë¥¼ ì íí©ëê¹?
digitizerTest=ìì´ì½ì ì´ëíì¸ì
speaker=ì¤ëì¤ íì¤í¸
wifi=WiFi
wiFi=WiFi
nfc=NFC
Panic=í¨ë ì²´í¬
alsSensor=ALS ì¼ì
frontIRCamera=ì ë©´ IR ì¹´ë©ë¼
frontDotProjector=ì ë©´ ëí¸ íë¡ì í°
backCameraTele=íë©´ ë§ì ì¹´ë©ë¼
backCameraWide=íë©´ ê´ê° ì¹´ë©ë¼
appLicenseExpired=ì± ë¼ì´ì¼ì¤ê° ë§ë£ëììµëë¤
charging=ì¶©ì  ì¤
dontSupportBypass=ì¥ì¹ê° AT ë°ì´í¨ì¤ë¥¼ ì§ìíì§ ììµëë¤
activateDeviceAdminApp=ê¸°ê¸° ê´ë¦¬ì ì±ì íì±íí´ì¼ í©ëë¤. \nì§ì°ê¸°ë¥¼ ìííë ¤ë©´ ê´ë¦¬ì ê¶íì´ íìí©ëë¤.
tapOnActivateButton=íì±í ë²í¼ì í­íì¸ì
averageTemperature=íê·  ì¨ë
cycleCount=ì¬ì´í´ ì
eeeeCode=EEEE ì½ë
maximumDischargeCurrent=ìµë ë°©ì  ì ë¥
nominalChargeCapacity=ì ê²© ì¶©ì  ì©ë
serviceOption=ìë¹ì¤ ìµì
totalOperatingTime=ì´ ìë ìê°
weekMfd=ì ì¡° ì£¼
batteryServiceFlags=ë°°í°ë¦¬ ìë¹ì¤ íëê·¸
designCapacity=ì¤ê³ ì©ë
fetchWatchBatteryInfo=ìì¹ ë°°í°ë¦¬ ì ë³´ ê°ì ¸ì¤ê¸°
installProfileOnAppleWatch=íìì´ ëíëë©´ Apple Watchì íë¡íì ì¤ì¹íì¸ì.
enterYourPassword = ë¹ë°ë²í¸ë¥¼ ìë ¥íì¸ì:
authenticationRequired = ì¸ì¦ì´ íìí©ëë¤
incorrectPasswordMsg = ë¹ë°ë²í¸ê° ì¬ë°ë¥´ì§ ììµëë¤. ë¤ì ìëí´ì£¼ì¸ì.
preview=ë¯¸ë¦¬ ë³´ê¸°
batteryDrainInfoCollected=ë°°í°ë¦¬ ìëª¨ ì ë³´ê° ìì§ë