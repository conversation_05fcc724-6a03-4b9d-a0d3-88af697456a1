#russian
forceAutoExport=ÐÐ²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¸Ð¹ ÑÐºÑÐ¿Ð¾ÑÑ
login.button = ÐÑÐ¾Ð´
username=ÐÐ¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
password=ÐÐ°ÑÐ¾Ð»Ñ
optional=(ÐÐµÐ¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½Ð¾)
downloading=ÐÐ°Ð³ÑÑÐ·ÐºÐ°
station=Ð¡ÑÐ°Ð½ÑÐ¸Ñ
tester=Ð¢ÐµÑÑÐµÑ
downloadApps=ÐÐ°Ð³ÑÑÐ·Ð¸ÑÑ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
rememberMe=ÐÐ°Ð¿Ð¾Ð¼Ð½Ð¸ÑÑ Ð¼ÐµÐ½Ñ
selectAll=ÐÑÐ±ÑÐ°ÑÑ Ð²ÑÐµ
selectNone=ÐÐµ Ð²ÑÐ±Ð¸ÑÐ°ÑÑ Ð½Ð¸ÑÐµÐ³Ð¾
erase=Ð¡ÑÐµÑÐµÑÑ
print=ÐÐµÑÐ°ÑÑ
powerOff=ÐÑÐºÐ»ÑÑÐ¸ÑÑ Ð¿Ð¸ÑÐ°Ð½Ð¸Ðµ
releaseBox=ÐÑÐ¿ÑÑÑÐ¸ÑÑ ÐºÐ¾ÑÐ¾Ð±ÐºÑ
restore=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ
prepare=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð¸ÑÑ
transaction=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ: 
warehouse=Ð¡ÐºÐ»Ð°Ð´: 
stationIdColon=ÐÐ´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ ÑÑÐ°Ð½ÑÐ¸Ð¸: 
thisHour=Ð­ÑÐ¾Ñ ÑÐ°Ñ: 
lastHour=ÐÑÐ¾ÑÐ»ÑÐ¹ ÑÐ°Ñ: 
total=ÐÑÐµÐ³Ð¾: 
erased=Ð¡ÑÐµÑÑÐ¾: 
passedColon=ÐÑÐ¾Ð¹Ð´ÐµÐ½Ð¾: 
pendingColon=ÐÐ¶Ð¸Ð´Ð°ÐµÑ: 
deviceName=ÐÐ¼Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
localCustomization=ÐÐ¾ÐºÐ°Ð»ÑÐ½ÑÐµ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸
enableDevModeOnIos16=ÐÐºÐ»ÑÑÐ¸ÑÑ ÑÐµÐ¶Ð¸Ð¼ ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ° Ð½Ð° iOS 16+
printCustomization=ÐÐµÑÐ°ÑÑ Ð½Ð°ÑÑÑÐ¾ÐµÐº
setDefaultLabelOne=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¼ÐµÑÐºÑ 1 Ð¿Ð¾ ÑÐ¼Ð¾Ð»ÑÐ°Ð½Ð¸Ñ
setDefaultLabelTwo=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¼ÐµÑÐºÑ 2 Ð¿Ð¾ ÑÐ¼Ð¾Ð»ÑÐ°Ð½Ð¸Ñ
setPrinterLabelOne=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¿ÑÐ¸Ð½ÑÐµÑ Ð´Ð»Ñ Ð¼ÐµÑÐºÐ¸ 1
rollType=Ð¢Ð¸Ð¿ ÑÑÐ»Ð¾Ð½Ð°
tape=ÐÐµÐ½ÑÐ°
label=ÐÐµÑÐºÐ°
dymoTwinTurbo=Dymo Twin Turbo
selectTray=ÐÑÐ±ÑÐ°ÑÑ Ð»Ð¾ÑÐ¾Ðº
left=ÐÐµÐ²ÑÐ¹
right=ÐÑÐ°Ð²ÑÐ¹
setPrinterLabelTwo=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¿ÑÐ¸Ð½ÑÐµÑ Ð´Ð»Ñ Ð¼ÐµÑÐºÐ¸ 2
firmware=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ°
saveSettings=Ð¡Ð¾ÑÑÐ°Ð½Ð¸ÑÑ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸
search=ÐÐ¾Ð¸ÑÐº
show=ÐÐ¾ÐºÐ°Ð·Ð°ÑÑ:
installed=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾
available=ÐÐ¾ÑÑÑÐ¿Ð½Ð¾
autoDownloadAsNeeded=ÐÐ²ÑÐ¾Ð·Ð°Ð³ÑÑÐ·ÐºÐ° Ð¿ÑÐ¸ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ÑÑÐ¸
directoryDir=ÐÐ¸ÑÐµÐºÑÐ¾ÑÐ¸Ñ Ð·Ð°Ð³ÑÑÐ·ÐºÐ¸:
whereToDownload=ÐÑÐ´Ð° Ð·Ð°Ð³ÑÑÐ¶Ð°ÑÑ
change=ÐÐ·Ð¼ÐµÐ½Ð¸ÑÑ
open=ÐÑÐºÑÑÑÑ
downloadSelected=ÐÐ°Ð³ÑÑÐ·Ð¸ÑÑ Ð²ÑÐ±ÑÐ°Ð½Ð½Ð¾Ðµ
networkDir=Ð¡ÐµÑÐµÐ²Ð°Ñ Ð´Ð¸ÑÐµÐºÑÐ¾ÑÐ¸Ñ:
download=ÐÐ°Ð³ÑÑÐ·ÐµÐ½Ð¾
downloadBtn=Ð¡ÐºÐ°ÑÐ°ÑÑ
queued=Ð Ð¾ÑÐµÑÐµÐ´Ð¸
failed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ
extracting=ÐÐ·Ð²Ð»ÐµÑÐµÐ½Ð¸Ðµ
stop=Ð¡ÑÐ¾Ð¿
downloadAgain=ÐÐ°Ð³ÑÑÐ·Ð¸ÑÑ ÑÐ½Ð¾Ð²Ð°
grade=ÐÑÐµÐ½ÐºÐ°
carrier=ÐÐ¿ÐµÑÐ°ÑÐ¾Ñ
color=Ð¦Ð²ÐµÑ
battery=ÐÐ°ÑÐ°ÑÐµÑ
lock=ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ°
eraseTime=ÐÑÐµÐ¼Ñ ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ:
comments=ÐÐ¾Ð¼Ð¼ÐµÐ½ÑÐ°ÑÐ¸Ð¸
simTechnologyColon=Ð¢ÐµÑÐ½Ð¾Ð»Ð¾Ð³Ð¸Ñ SIM-ÐºÐ°ÑÑÑ:
summary=Ð¡Ð²Ð¾Ð´ÐºÐ°
deviceDetails=ÐÐµÑÐ°Ð»Ð¸ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
diagnostics=ÐÐ¸Ð°Ð³Ð½Ð¾ÑÑÐ¸ÐºÐ°
deviceInitialization=ÐÐ½Ð¸ÑÐ¸Ð°Ð»Ð¸Ð·Ð°ÑÐ¸Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
connecting=ÐÐ¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ðµ
pairingInProgress=ÐÐ¾Ð²ÐµÑÐ¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
pairingFailed=Ð¡Ð±Ð¾Ð¹ ÑÐ¾Ð¿ÑÑÐ¶ÐµÐ½Ð¸Ñ
pairingSuccessful=Ð¡Ð¾Ð¿ÑÑÐ¶ÐµÐ½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾
deviceInfoCollected=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ ÑÐ¾Ð±ÑÐ°Ð½Ð°
deviceInfoFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
deviceImeiCollected=IMEI ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° ÑÐ¾Ð±ÑÐ°Ð½
failedToGetImei=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ IMEI
activationInProgress=ÐÐºÑÐ¸Ð²Ð°ÑÐ¸Ñ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
activationFailed=Ð¡Ð±Ð¾Ð¹ Ð°ÐºÑÐ¸Ð²Ð°ÑÐ¸Ð¸
activationSuccessful=ÐÐºÑÐ¸Ð²Ð°ÑÐ¸Ñ ÑÑÐ¿ÐµÑÐ½Ð°
activationLockOn=ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° Ð°ÐºÑÐ¸Ð²Ð°ÑÐ¸Ð¸ Ð²ÐºÐ»ÑÑÐµÐ½Ð°
pushWifiProfile=ÐÑÐ¿ÑÐ°Ð²ÐºÐ° Ð¿ÑÐ¾ÑÐ¸Ð»Ñ Wi-Fi
fetchMdmInfoFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ MDM
fetchMdmInfoSuccessful=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ MDM Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchKnoxInfoSuccessful=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Knox Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchFrpInfoSuccessful=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ FRP Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchBatteryInfoSuccessful=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchBatteryInfoFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ
wifiPushSuccessful=Wi-Fi Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½ ÑÑÐ¿ÐµÑÐ½Ð¾
wifiPushFailed=ÐÑÐ¸Ð±ÐºÐ° Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ¸ Wi-Fi
devImageMounting=ÐÐ¾Ð½ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¾Ð±ÑÐ°Ð·Ð° ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ°
devImageMountingFailed=Ð¡Ð±Ð¾Ð¹ Ð¼Ð¾Ð½ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð¾Ð±ÑÐ°Ð·Ð° ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ°
devImageMountingSucceeded=ÐÐ±ÑÐ°Ð· ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ° ÑÑÐ¿ÐµÑÐ½Ð¾ ÑÐ¼Ð¾Ð½ÑÐ¸ÑÐ¾Ð²Ð°Ð½
checkIfAppInstallRequired=Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÐ° Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
appInstallFailed=ÐÑÐ¸Ð±ÐºÐ° ÑÑÑÐ°Ð½Ð¾Ð²ÐºÐ¸ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
appInstallSuccess=ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾ ÑÑÐ¿ÐµÑÐ½Ð¾
appAlreadyInstalled=ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÐ¶Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾
configPushFailed=Ð¡Ð±Ð¾Ð¹ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ¸ ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ð¸
configPushSuccess=ÐÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ñ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð°
allSyncableData=  â¢ ÐÑÑ ÑÐ¸Ð½ÑÑÐ¾Ð½Ð¸Ð·Ð¸ÑÑÐµÐ¼Ð°Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ
batteryApi=  â¢ ÐÐ½ÑÐµÑÑÐµÐ¹Ñ API Ð±Ð°ÑÐ°ÑÐµÐ¸
testList=  â¢ Ð¡Ð¿Ð¸ÑÐ¾Ðº ÑÐµÑÑÐ¾Ð²
config=  â¢ ÐÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ñ
failureReasons=  â¢ ÐÑÐ¸ÑÐ¸Ð½Ñ Ð¾ÑÐºÐ°Ð·Ð°
gradeConfig=â¢ ÐÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ñ Ð¾ÑÐµÐ½ÐºÐ¸
clientCustomization=â¢ ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ° ÐºÐ»Ð¸ÐµÐ½ÑÐ°
desktopResults=â¢ Ð ÐµÐ·ÑÐ»ÑÑÐ°ÑÑ Ð½Ð° ÑÐ°Ð±Ð¾ÑÐµÐ¼ ÑÑÐ¾Ð»Ðµ
ready=ÐÐ¾ÑÐ¾Ð²Ð¾
readyHyphen=- ÐÐ¾ÑÐ¾Ð²Ð¾
appTestingDone=Ð¢ÐµÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
checkingEsn=ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN
esnSuccessful=ESN ÑÑÐ¿ÐµÑÐ½Ð¾
printing=ÐÐµÑÐ°ÑÑ
eraseInProgress=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
eraseSuccessful=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾
shuttingDownPhone=ÐÑÐºÐ»ÑÑÐµÐ½Ð¸Ðµ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
restoreInProgress=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
restoreSuccessful=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾
batteryInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ
batteryHealth=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryChargePercentage=ÐÐ°ÑÑÐ´ Ð±Ð°ÑÐ°ÑÐµÐ¸ (%)
batteryCycles=Ð¦Ð¸ÐºÐ»Ñ Ð±Ð°ÑÐ°ÑÐµÐ¸
designedCapacity=ÐÑÐ¾ÐµÐºÑÐ¸ÑÑÐµÐ¼Ð°Ñ ÐµÐ¼ÐºÐ¾ÑÑÑ
currentCapacity=Ð¢ÐµÐºÑÑÐ°Ñ ÐµÐ¼ÐºÐ¾ÑÑÑ
batterySerialNo=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryModelNo=ÐÐ¾Ð´ÐµÐ»Ñ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryResistance=Ð¡Ð¾Ð¿ÑÐ¾ÑÐ¸Ð²Ð»ÐµÐ½Ð¸Ðµ Ð±Ð°ÑÐ°ÑÐµÐ¸ (R=V/I)
batteryDrainPercent=ÐÑÐ¾ÑÐµÐ½Ñ ÑÐ°Ð·ÑÑÐ´Ð° Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryDrainDuration=ÐÐ»Ð¸ÑÐµÐ»ÑÐ½Ð¾ÑÑÑ ÑÐ°Ð·ÑÑÐ´Ð° Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryDrainResult=Ð ÐµÐ·ÑÐ»ÑÑÐ°Ñ ÑÐ°Ð·ÑÑÐ´Ð° Ð±Ð°ÑÐ°ÑÐµÐ¸
touchId=Touch ID
manualTouchIdRequiredMsg=Ð¢ÑÐµÐ±ÑÐµÑÑÑ Ð²Ð²Ð¾Ð´ Touch ID Ð²ÑÑÑÐ½ÑÑ\n\n ÐÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð¿ÐµÑÐµÐ¹Ð´Ð¸ÑÐµ Ð²:\n\n   >ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸\n    \
  >Touch ID & ÐÐ°ÑÐ¾Ð»Ñ\n     >ÐÐ¾Ð±Ð°Ð²Ð¸ÑÑ Ð¾ÑÐ¿ÐµÑÐ°ÑÐ¾Ðº\n      >ÐÐ¾ÑÐ½Ð¸ÑÐµÑÑ Ð´Ð°ÑÑÐ¸ÐºÐ° Touch ID
faceId=Face ID
done=ÐÐ¾ÑÐ¾Ð²Ð¾
enterLpn=ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð¸Ð»Ð¸ Ð¾ÑÑÐºÐ°Ð½Ð¸ÑÑÐ¹ÑÐµ LPN
customOneField=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¾Ðµ Ð¿Ð¾Ð»Ðµ 1
enterCustomOneField=ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð¸Ð»Ð¸ Ð¾ÑÑÐºÐ°Ð½Ð¸ÑÑÐ¹ÑÐµ ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¾Ðµ Ð¿Ð¾Ð»Ðµ 1
manulFaceIdRequiredMsg=Ð¢ÑÐµÐ±ÑÐµÑÑÑ Ð²Ð²Ð¾Ð´ Face ID Ð²ÑÑÑÐ½ÑÑ\n\n ÐÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð¿ÐµÑÐµÐ¹Ð´Ð¸ÑÐµ Ð²:\n\n   >ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸\n    \
  >Face ID & ÐÐ°ÑÐ¾Ð»Ñ\n     >ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ° Face ID \n      >ÐÐ°ÑÐ°ÑÑ
detectFaceIdMsg=ÐÐ¿ÑÐµÐ´ÐµÐ»ÐµÐ½Ð¸Ðµ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ñ Face ID.\n\n ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð·Ð°Ð²ÐµÑÑÐ¸ÑÐµ ÑÐµÐ³Ð¸ÑÑÑÐ°ÑÐ¸Ñ Face ID.
appNotInstalledDueRestrictions=ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð¾, Ð½Ð¾\nÐ½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾ Ð½Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð¸Ð·-Ð·Ð°\nÐ½ÐµÐºÐ¾ÑÐ¾ÑÑÑ Ð¾Ð³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð¸Ð¹.
appInstallationInstructionsHeading=ÐÐ½ÑÑÑÑÐºÑÐ¸Ð¸ Ð¿Ð¾ ÑÑÑÐ°Ð½Ð¾Ð²ÐºÐµ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ:
appInstallationInstructions=1. ÐÐµÑÐµÐ¹Ð´Ð¸ÑÐµ Ð² Ð·Ð°Ð³ÑÑÐ·ÐºÐ¸ \n2. Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐµ android_lite.apk Ð²ÑÑÑÐ½ÑÑ\n3. Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐµ android.apk Ð²ÑÑÑÐ½ÑÑ
cosmetics=ÐÐÐ¡ÐÐÐ¢ÐÐÐ
cancel=ÐÑÐ¼ÐµÐ½Ð°
save=Ð¡Ð¾ÑÑÐ°Ð½Ð¸ÑÑ
manualBatteryHealthRetrieval=Ð ÑÑÐ½Ð¾Ð¹ Ð·Ð°Ð¿ÑÐ¾Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ Ð¾ ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ð¸ Ð±Ð°ÑÐ°ÑÐµÐ¸
deviceNavigationForBattery=ÐÐ° ÑÑÐ¾Ð¼ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð¿ÐµÑÐµÐ¹ÑÐ¸\nÐº Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ°Ð¼ "Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ Ð±Ð°ÑÐ°ÑÐµÐ¸ Ð¸ Ð·Ð°ÑÑÐ´ÐºÐ°"\nÐ² Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ð¸ ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸.
navigateToBatterySettings=1  ÐÐµÑÐµÐ¹ÑÐ¸ Ðº Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ°Ð¼\n    Ð±Ð°ÑÐ°ÑÐµÐ¸
navigateToBatteryHealthCharging=2  ÐÐµÑÐµÐ¹ÑÐ¸ Ðº Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ°Ð¼\n    ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ñ Ð±Ð°ÑÐ°ÑÐµÐ¸ Ð¸ Ð·Ð°ÑÑÐ´ÐºÐ¸
close=ÐÐ°ÐºÑÑÑÑ
fail=ÐÐÐ£Ð
restoreError=ÐÑÐ¸Ð±ÐºÐ° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
ok=OK
reconnectDevice=ÐÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
error=ÐÑÐ¸Ð±ÐºÐ°: 
pushWifi=ÐÐµÑÐµÐ´Ð°ÑÑ Wi-Fi
installApp=Ð£ÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ
pushFiles=ÐÐµÑÐµÐ´Ð°ÑÑ ÑÐ°Ð¹Ð»Ñ
syncResults=Ð¡Ð¸Ð½ÑÑÐ¾Ð½Ð¸Ð·Ð¸ÑÐ¾Ð²Ð°ÑÑ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÑ
failDevice=ÐÐµÑÐ´Ð°ÑÐ½Ð¾Ðµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
restoreDevice=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
copyInfoToAllDevices=Ð¡ÐºÐ¾Ð¿Ð¸ÑÐ¾Ð²Ð°ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð½Ð° Ð²ÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
os=ÐÐ¡
mdm=ÐÐÐ
imei=IMEI
oem=OEM
skuCode=ÐÐ¾Ð´ SKU
lpn=ÐÐÐ
customOne=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¾Ðµ1
jailbreakRoot=Jailbreak/Root
manualEntry=Ð ÑÑÐ½Ð¾Ð¹ Ð²Ð²Ð¾Ð´
serialIdOrImeiRequired=ÐÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼ ÑÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ Ð¸Ð»Ð¸ IMEI Ð´Ð»Ñ Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÑÐ½Ð¾Ð¹ Ð·Ð°Ð¿Ð¸ÑÐ¸
serialNumber=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ
submit=ÐÑÐ¿ÑÐ°Ð²Ð¸ÑÑ
printLabel=ÐÐµÑÐ°ÑÑ ÑÑÐ¸ÐºÐµÑÐºÐ¸
edit=Ð ÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ
part=Ð§Ð°ÑÑÑ
currentSerial=Ð¢ÐµÐºÑÑÐ¸Ð¹ ÑÐµÑÐ¸Ð¹Ð½ÑÐ¹
factorySerial=ÐÐ°Ð²Ð¾Ð´ÑÐºÐ¾Ð¹ ÑÐµÑÐ¸Ð¹Ð½ÑÐ¹
status=Ð¡ÑÐ°ÑÑÑ
notice=Ð£Ð²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ðµ
portMapping=ÐÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ Ð¿Ð¾ÑÑÐ°
hub=Ð¦ÐµÐ½ÑÑ
next=ÐÐ°Ð»ÐµÐµ
finishMapping=ÐÐ°Ð²ÐµÑÑÐ¸ÑÑ Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ
remap=ÐÐµÑÐµÐ¼Ð°Ð¿Ð¸ÑÐ¾Ð²Ð°ÑÑ
enterVendorInvoiceInfo=ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð½Ð°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð¿Ð¾ÑÑÐ°Ð²ÑÐ¸ÐºÐ° Ð¸ Ð½Ð¾Ð¼ÐµÑ ÑÑÐµÑÐ°.
vendorName=ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ Ð¿Ð¾ÑÑÐ°Ð²ÑÐ¸ÐºÐ°
invoiceNo=ÐÐ¾Ð¼ÐµÑ ÑÑÐµÑÐ°
qty=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾
boxNo=ÐÐ¾Ð¼ÐµÑ ÐºÐ¾ÑÐ¾Ð±ÐºÐ¸
transactionDetails=ÐÐµÑÐ°Ð»Ð¸ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸
continueTransaction=â¢â¢â¢ ÐÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
currentTransaction=Ð¢ÐµÐºÑÑÐ°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
bestBuy=ÐÑÑÑÐ°Ñ Ð¿Ð¾ÐºÑÐ¿ÐºÐ°
invoice=Ð¡ÑÐµÑ # 
box=ÐÐ¾ÑÐ¾Ð±ÐºÐ°-
qtyFormatted=â¢ ÐÐ¾Ð»-Ð²Ð¾-
of=Ð¸Ð·
details=ÐÐµÑÐ°Ð»Ð¸
exportDetails=Ð­ÐºÑÐ¿Ð¾ÑÑÐ¸ÑÐ¾Ð²Ð°ÑÑ Ð´ÐµÑÐ°Ð»Ð¸
transactionHistory=ÐÑÑÐ¾ÑÐ¸Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¹
name=ÐÐ¼Ñ
deviceLockLabel=ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
notes=ÐÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ
snColon=S/N:
sn=S/N
imeiColon=IMEI: 
eraseColon=Ð¡ÑÐµÑÐµÑÑ:
title=ÐÐ°Ð³Ð¾Ð»Ð¾Ð²Ð¾Ðº
modelNo=ÐÐÐÐÐÐ¬ 
customElement=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¸Ð¹ ÑÐ»ÐµÐ¼ÐµÐ½Ñ
serialNo=Ð¡ÐÐ ÐÐÐÐ«Ð ÐÐÐÐÐ  
serial=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹
gradeColon=ÐÑÐµÐ½ÐºÐ° : 
colorColon=Ð¦ÐÐÐ¢ : 
batteryColon=ÐÐÐ¢ÐÐ ÐÐ¯ : 
bbc=BCC: 
versionColon=ÐÐÐ Ð¡ÐÐ¯ : 
version=ÐÐµÑÑÐ¸Ñ
firmNetworkColon=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ°/Ð¡ÐµÑÑ : 
firmNetwork=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ°/Ð¡ÐµÑÑ
esnColon=ESN : 
esn=ESN
fmiFrp=FMI/FRP : 
fmiFrpStatus=FMI/FRPÐ¡ÑÐ°ÑÑÑ
cosmeticsColon=COSMETICS: 
erasureColon=ERASURE: 
oemParts:OEM Ð§Ð°ÑÑÐ¸ : 
functionality=Ð¤ÑÐ½ÐºÑÐ¸Ð¾Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ : 
seeNotes=Ð¡Ð¼. ÐÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ
customOneColon=CUSTOM 1 : 
custom=Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
port=ÐÐÐ Ð¢ : 
userColon=ÐÐÐÐ¬ÐÐÐÐÐ¢ÐÐÐ¬ : 
user=Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
dateColon=ÐÐÐ¢Ð : 
vendorColon=ÐÐÐ¡Ð¢ÐÐÐ©ÐÐ : 
vendor=ÐÐ¾ÑÑÐ°Ð²ÑÐ¸Ðº
fullFuctionalOrSeeNotes=ÐÐ¾Ð»Ð½Ð°Ñ ÑÑÐ½ÐºÑÐ¸Ð¾Ð½Ð°Ð»ÑÐ½Ð¾ÑÑÑ Ð¸Ð»Ð¸ Ð¡Ð¼. ÐÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ
lpnColon=LPN: 
jailbreakRooted=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ°/Rooted : 
jailbreak=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ°
deviceOffline=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð² Ð°Ð²ÑÐ¾Ð½Ð¾Ð¼Ð½Ð¾Ð¼ ÑÐµÐ¶Ð¸Ð¼Ðµ. ÐÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÐµ
make=ÐÑÐ¾Ð¸Ð·Ð²Ð¾Ð´Ð¸ÑÐµÐ»Ñ:  
erasing=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ: 
eraseSuccess=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾:
recoveryMode:Ð ÐµÐ¶Ð¸Ð¼ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
verifyingFirmware=ÐÑÐ¾Ð²ÐµÑÐºÐ° Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸
restoreCompleted=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾: 
deviceDisconnected=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¾ÑÐºÐ»ÑÑÐµÐ½Ð¾
restoreFailed=ÐÑÐ¸Ð±ÐºÐ° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
startDeviceTesting=ÐÐ°ÑÐ°ÑÑ ÑÐµÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð½Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
appInstallFailedManualInstallReqd=ÐÑÐ¸Ð±ÐºÐ° ÑÑÑÐ°Ð½Ð¾Ð²ÐºÐ¸ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ, ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐµ ÐµÐ³Ð¾ Ð²ÑÑÑÐ½ÑÑ
pairingFailedReconnect=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ¾Ð¿ÑÑÐ¶ÐµÐ½Ð¸Ðµ, Ð¿Ð¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÐµÑÑ!
passed=ÐÑÐ¾Ð¹Ð´ÐµÐ½Ð¾
eraseRestricted=ÐÐ³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð¾ ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ
printRestricted=ÐÐ³ÑÐ°Ð½Ð¸ÑÐµÐ½Ð° Ð¿ÐµÑÐ°ÑÑ
deviceOperations=ÐÐ¿ÐµÑÐ°ÑÐ¸Ð¸ Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾Ð¼
esnCheck=ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN
esnCheckBlacklist=ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN Ð§ÐµÑÐ½ÑÐ¹ ÑÐ¿Ð¸ÑÐ¾Ðº
esnCheckAll=ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN Ð²ÑÐµ
provisionOrQRPrint=ÐÑÐµÐ´Ð¾ÑÑÐ°Ð²Ð»ÐµÐ½Ð¸Ðµ QR Ð¿ÐµÑÐ°ÑÐ¸ 
settings=ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸
openCustomizations=ÐÑÐºÑÑÑÑ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸
openCloudCustomizations=ÐÑÐºÑÑÑÑ Ð¾Ð±Ð»Ð°ÑÐ½ÑÐµ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸
about=Ð Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ðµ
quit=ÐÑÑÐ¾Ð´ 
actions=ÐÐµÐ¹ÑÑÐ²Ð¸Ñ
view=ÐÑÐ¾ÑÐ¼Ð¾ÑÑ
appearance=ÐÐ½ÐµÑÐ½Ð¸Ð¹ Ð²Ð¸Ð´
transactions=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸
newTransaction=ÐÐ¾Ð²Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
viewTransaction=ÐÑÐ¾ÑÐ¼Ð¾ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸
selectPackage=ÐÑÐ±ÑÐ°ÑÑ Ð¿Ð°ÐºÐµÑ
updateVendorInfo=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð¿Ð¾ÑÑÐ°Ð²ÑÐ¸ÐºÐµ
help=ÐÐ¾Ð¼Ð¾ÑÑ
readyInAtMode=ÐÐ¾ÑÐ¾Ð² Ð² AT-ÑÐµÐ¶Ð¸Ð¼Ðµ
deviceInfoCollectionFailed=Ð¡Ð±Ð¾Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð½Ðµ ÑÐ´Ð°Ð»ÑÑ
imeiCollectionFailed=Ð¡Ð±Ð¾Ñ IMEI Ð½Ðµ ÑÐ´Ð°Ð»ÑÑ
batteryInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ
batteryInfoCollectionFailed=Ð¡Ð±Ð¾Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ Ð½Ðµ ÑÐ´Ð°Ð»ÑÑ
oemDataCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ñ Ð´Ð°Ð½Ð½ÑÐµ OEM
oemDataCollectionFailed=Ð¡Ð±Ð¾Ñ Ð´Ð°Ð½Ð½ÑÑ OEM Ð½Ðµ ÑÐ´Ð°Ð»ÑÑ
icloudInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ iCloud
simInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ SIM-ÐºÐ°ÑÑÐµ
carrierLockInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐµ Ð¾Ð¿ÐµÑÐ°ÑÐ¾ÑÐ°
peoInitialized=PEO Ð¸Ð½Ð¸ÑÐ¸Ð°Ð»Ð¸Ð·Ð¸ÑÐ¾Ð²Ð°Ð½
startingTests=ÐÐ°Ð¿ÑÑÐº ÑÐµÑÑÐ¾Ð²
testStartedSuccessfully=Ð¢ÐµÑÑÑ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð·Ð°Ð¿ÑÑÐµÐ½Ñ
startTestsFailed=ÐÑÐ¸Ð±ÐºÐ° Ð·Ð°Ð¿ÑÑÐºÐ° ÑÐµÑÑÐ¾Ð²
fetchingMdm=ÐÐ¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ MDM
mdmSucceeded=ÐÐ¾Ð»ÑÑÐµÐ½Ð¸Ðµ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ MDM ÑÑÐ¿ÐµÑÐ½Ð¾
mdmFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ MDM
testConfigInProgress=ÐÑÐ¿ÑÐ°Ð²ÐºÐ° ÑÐµÑÑÐ¾Ð²Ð¾Ð¹ ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ð¸
testConfigPushSuccess=Ð£ÑÐ¿ÐµÑÐ½Ð¾ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½Ð° ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ñ ÑÐµÑÑÐ°
testConfigFailed=Ð¡Ð±Ð¾Ð¹ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ¸ ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ð¸ ÑÐµÑÑÐ°
eraseFailed=ÐÑÐ¸Ð±ÐºÐ° ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
enableDevMode=ÐÐºÐ»ÑÑÐµÐ½Ð¸Ðµ ÑÐµÐ¶Ð¸Ð¼Ð° ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ°
devModeEnabled=Ð ÐµÐ¶Ð¸Ð¼ ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ° Ð²ÐºÐ»ÑÑÐµÐ½
devModeEnableFailed=Ð¡Ð±Ð¾Ð¹ Ð²ÐºÐ»ÑÑÐµÐ½Ð¸Ñ ÑÐµÐ¶Ð¸Ð¼Ð° ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÑÐ¸ÐºÐ°
initiatePeo=ÐÐ½Ð¸ÑÐ¸Ð°Ð»Ð¸Ð·Ð°ÑÐ¸Ñ PEO
preparingDevice=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
prepareDeviceFailed=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð½Ðµ ÑÐ´Ð°Ð»Ð°ÑÑ
prepareDeviceSuccess=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÑÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ ÑÑÐ¿ÐµÑÐ½ÑÐ¼
esnCheckSuccessful=ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN ÑÑÐ¿ÐµÑÐ½Ð°
uninstallAppSuccess=Ð£ÑÐ¿ÐµÑÐ½Ð¾Ðµ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
appNotInstalled=ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾
printRequestSent=ÐÐ°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¿ÐµÑÐ°ÑÑ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½
notReady=ÐÐµ Ð³Ð¾ÑÐ¾Ð²Ð¾
editTransaction=Ð ÐµÐ´Ð°ÐºÑÐ¸ÑÐ¾Ð²Ð°ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
frpInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ FRP
meidInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ MEID
pcUtilityInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ PC-ÑÑÐ¸Ð»Ð¸ÑÐµ
knoxInfoCollected=Ð¡Ð¾Ð±ÑÐ°Ð½Ð° Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Knox
initializing=ÐÐ½Ð¸ÑÐ¸Ð°Ð»Ð¸Ð·Ð°ÑÐ¸Ñâ¦
eraseRequested=ÐÐ°Ð¿ÑÐ¾ÑÐµÐ½Ð¾ ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ
preparing=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ°
preparingFailedReconnect=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð½Ðµ ÑÐ´Ð°Ð»Ð°ÑÑ, Ð¿ÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÐµÑÑ
imeiNotFound=IMEI Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
readyStartTesting=ÐÐ¾ÑÐ¾Ð²Ð¾, Ð½Ð°ÑÐ°ÑÑ ÑÐµÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð½Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
deviceDisconnectedDuringRestore=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¾ÑÐºÐ»ÑÑÐµÐ½Ð¾ Ð²Ð¾ Ð²ÑÐµÐ¼Ñ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
deviceDisconnectedDuringErase=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¾ÑÐºÐ»ÑÑÐµÐ½Ð¾ Ð²Ð¾ Ð²ÑÐµÐ¼Ñ ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
completeRequiredFields=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð·Ð°Ð¿Ð¾Ð»Ð½Ð¸ÑÐµ Ð¾Ð±ÑÐ·Ð°ÑÐµÐ»ÑÐ½ÑÐµ Ð¿Ð¾Ð»Ñ
eraseRequestSent=ÐÐ°Ð¿ÑÐ¾Ñ Ð½Ð° ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½
pending=ÐÐ¶Ð¸Ð´Ð°Ð½Ð¸Ðµ
defect=_ÐÐµÑÐµÐºÑ
portMappingInstructionStep1=ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ° Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð¿Ð¾ÑÑÐ¾Ð² Ð´Ð»Ñ Ð½Ð°Ð·Ð½Ð°ÑÐµÐ½Ð¸Ñ Ð¾Ð¿ÑÐµÐ´ÐµÐ»ÐµÐ½Ð½ÑÑ USB-Ð¿Ð¾ÑÑÐ¾Ð² Ð¿Ð¾ÑÑÐ¾Ð²ÑÐ¼ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼.\n \
  ÐÑÐ¸ Ð½Ð°ÑÐ°Ð»ÑÐ½Ð¾Ð¹ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐµ ÑÐµÐºÐ¾Ð¼ÐµÐ½Ð´ÑÐµÑÑÑ Ð¸ÑÐ¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Apple Ñ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½ÑÐ¼ Ð¸Ð»Ð¸ ÑÐµÑÑÐ¸ÑÐ¸ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐ¼ ÐºÐ°Ð±ÐµÐ»ÐµÐ¼ MFI.\n \
  ÐÐ¾ÑÐ»Ðµ Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð¿Ð¾ÑÑÐ¾Ð² Ð²Ñ Ð¼Ð¾Ð¶ÐµÑÐµ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐ°ÑÑ Ðº ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ñ Ð»ÑÐ±Ð¾Ð¹ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼ÑÐ¹ ÐºÐ°Ð±ÐµÐ»Ñ.
portMappingInstructionStep2=ÐÐ°ÑÐ½Ð¸ÑÐµ Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ Ð¿Ð¾ÑÑÐ¾Ð², Ð¿Ð¾Ð´ÐºÐ»ÑÑÐ¸Ð² ÐºÐ°Ð±ÐµÐ»Ñ Ð¾Ñ Ð²Ð°ÑÐµÐ³Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð² Ð¿ÐµÑÐ²ÑÐ¹ USB-Ð¿Ð¾ÑÑ. \
  ÐÐ»Ð»ÑÑÑÑÐ°ÑÐ¸Ñ Ð½Ð¸Ð¶Ðµ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑÑ Ð½Ð° Ð·ÐµÐ»ÐµÐ½ÑÑ, ÐºÐ¾Ð³Ð´Ð° Ð²Ñ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¾ÑÐ¾Ð±ÑÐ°Ð·Ð¸ÑÐµ Ð¿Ð¾ÑÑ USB.
portMappingInstructionStep3=ÐÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ðµ Ð¿Ð¾ÑÑÐ¾Ð² ÑÐ¶Ðµ ÑÑÑÐµÑÑÐ²ÑÐµÑ. ÐÐ»Ñ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾Ð³Ð¾ Ð¾ÑÐ¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ Ð½Ð°Ð¶Ð¼Ð¸ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÑ "ÐÐµÑÐµÐ¼Ð°Ð¿Ð¸ÑÐ¾Ð²Ð°ÑÑ" Ð½Ð¸Ð¶Ðµ
noDeviceRecordTransaction=ÐÐ°Ð¿Ð¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð° Ð´Ð»Ñ ÑÑÐ¾Ð¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸
eraseLicenseExpired=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ Ð½Ð° ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð¸ÑÑÐµÐº
eraseNotPermitted=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ ÑÑÐµÑÐµÑÑ, Ð·Ð°Ð¿ÑÐµÑ Ð½Ð° ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ
eraseFailedEcidMissing=ÐÑÐ¸Ð±ÐºÐ° ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ, Ð¾ÑÑÑÑÑÑÐ²ÑÐµÑ ECID
eraseStoppedDeviceDisconnected=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð¿ÑÐµÑÐ²Ð°Ð½Ð¾, ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¾ÑÐºÐ»ÑÑÐµÐ½Ð¾
eraseStoppedTryAgain=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð¾ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾, Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ
erasedWaitingForBootup=Ð¡ÑÐµÑÑÐ¾, Ð¾Ð¶Ð¸Ð´Ð°ÐµÑÑÑ Ð·Ð°Ð³ÑÑÐ·ÐºÐ°
acceptDevModeOnDevice=ÐÑÐ¸Ð½ÑÑÑ ÑÐµÐ¶Ð¸Ð¼ ÑÐ°Ð·ÑÐ°Ð±Ð¾ÑÐºÐ¸ Ð¸ Ð¿ÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÑÑÑ!
oemPartsDetails=ÐÐÐ¢ÐÐÐ ÐÐÐ-Ð§ÐÐ¡Ð¢ÐÐ
newTransactionCreated=Ð¡Ð¾Ð·Ð´Ð°Ð½Ð° Ð½Ð¾Ð²Ð°Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
dmgAvailable=DMG Ð´Ð»Ñ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ð¾Ð¹ Ð²ÐµÑÑÐ¸Ð¸ Ð´Ð¾ÑÑÑÐ¿ÐµÐ½
transactionContinued=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶ÐµÐ½Ð°
failedToContinueTransaction=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
updateCloudCustomizations=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ Ð¾Ð±Ð»Ð°ÑÐ½ÑÐµ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸
noManualEntryAvailable=ÐÐµÑ Ð´Ð¾ÑÑÑÐ¿Ð½ÑÑ ÑÑÑÐ½ÑÑ Ð·Ð°Ð¿Ð¸ÑÐµÐ¹
carrierSimLock=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐµ SIM-ÐºÐ°ÑÑÑ
simLockInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐµ SIM-ÐºÐ°ÑÑÑ
esnInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾Ð± ESN
firmwareNotAvailable=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð½Ðµ Ð´Ð¾ÑÑÑÐ¿Ð½Ð° Ð´Ð»Ñ ÑÑÐ¾Ð¹ Ð¼Ð¾Ð´ÐµÐ»Ð¸
firmwareDownloadRequested=ÐÐ°Ð¿ÑÐ¾ÑÐµÐ½Ð° Ð·Ð°Ð³ÑÑÐ·ÐºÐ° Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸ Ð´Ð»Ñ Ð¼Ð¾Ð´ÐµÐ»Ð¸
unableToDownloadFirmware=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð·Ð°Ð³ÑÑÐ·Ð¸ÑÑ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÑ Ð´Ð»Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° ÑÑÐ¾Ð¹ Ð¼Ð¾Ð´ÐµÐ»Ð¸
restoreInitiated=ÐÐ°ÑÐ°Ñ Ð¿ÑÐ¾ÑÐµÑÑ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
firmwareDownloadFailed=Ð¡Ð±Ð¾Ð¹ Ð·Ð°Ð³ÑÑÐ·ÐºÐ¸/Ð¸Ð·Ð²Ð»ÐµÑÐµÐ½Ð¸Ñ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸ Ð´Ð»Ñ Ð¼Ð¾Ð´ÐµÐ»Ð¸
waitForFirmwareVerification=ÐÐ¾Ð´Ð¾Ð¶Ð´Ð¸ÑÐµ, Ð¿Ð¾ÐºÐ° Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð½Ðµ Ð±ÑÐ´ÐµÑ Ð¿Ð¾Ð»Ð½Ð¾ÑÑÑÑ Ð¿ÑÐ¾Ð²ÐµÑÐµÐ½Ð°
firmwareDownloadStopped=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð¾ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÐµÐ¼ Ð´Ð»Ñ Ð¼Ð¾Ð´ÐµÐ»Ð¸
restoreStarted=ÐÐ°ÑÐ°Ñ Ð¿ÑÐ¾ÑÐµÑÑ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
restoreProcessFailed=Ð¡Ð±Ð¾Ð¹ Ð¿ÑÐ¾ÑÐµÑÑÐ° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð´Ð»Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
dfuPushFailed=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð½Ðµ Ð½Ð°ÑÐ¾Ð´Ð¸ÑÑÑ Ð² ÑÐµÐ¶Ð¸Ð¼Ðµ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ. ÐÐµÑÐµÐ²ÐµÐ´Ð¸ÑÐµ ÐµÐ³Ð¾ Ð² ÑÐµÐ¶Ð¸Ð¼ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð²ÑÑÑÐ½ÑÑ Ð¸ Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ
dfuPushSuccess=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¿ÐµÑÐµÐ²ÐµÐ´ÐµÐ½Ð¾ Ð² ÑÐµÐ¶Ð¸Ð¼ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
restoreFailedToCreateStateMachine=Ð¡Ð±Ð¾Ð¹ ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ Ð½Ð¾Ð²Ð¾Ð³Ð¾ ÐºÐ¾Ð½ÐµÑÐ½Ð¾Ð³Ð¾ Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ° Ð´Ð»Ñ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ. ÐÐµÑÐµÐ²ÐµÐ´Ð¸ÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð² ÑÐµÐ¶Ð¸Ð¼ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð²ÑÑÑÐ½ÑÑ
unableToUpdateDevice=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ Ð¾Ð½Ð¾ Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð¾ ÐºÐ¾Ð´Ð¾Ð¼ Ð´Ð¾ÑÑÑÐ¿Ð° Ð¸ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð½Ðµ Ð±ÑÐ»Ð¾ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð¾ Ð´Ð»Ñ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ Ð¸Ð»Ð¸ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ.\
  ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÑÐ¹ÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
firmwareAuthorizationFailed=ÐÑÐºÐ°Ð·Ð°Ð½Ð¾ Ð² Ð°Ð²ÑÐ¾ÑÐ¸Ð·Ð°ÑÐ¸Ð¸ ÑÑÐ¾Ð¹ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸ Ð½Ð° ÑÑÐ¾Ð¼ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ. ÐÐ°Ð³ÑÑÐ·Ð¸ÑÐµ Ð¿Ð¾ÑÐ»ÐµÐ´Ð½ÑÑ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÑ
firmwareExtractionField=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð½ÐµÐ¿ÑÐ°Ð²Ð¸Ð»ÑÐ½Ð¾ Ð¸Ð·Ð²Ð»ÐµÑÐµÐ½Ð°. ÐÐ·Ð²Ð»ÐµÐºÐ¸ÑÐµ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÑ
firmwareUpdaterReqFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐ°ÑÑ Ð·Ð°Ð¿ÑÐ¾Ñ Ð½Ð° Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸
restoreFailedWithErrorCode=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð·Ð°Ð²ÐµÑÑÐ¸Ð»Ð¾ÑÑ Ð¾ÑÐ¸Ð±ÐºÐ¾Ð¹. ÐÑÐ¾Ð²ÐµÑÑÑÐµ Ð¶ÑÑÐ½Ð°Ð»Ñ Ð½Ð°Ð»Ð¸ÑÐ¸Ðµ ÐºÐ¾Ð´Ð° Ð¾ÑÐ¸Ð±ÐºÐ¸
restoreFailedWithUnknownErr=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾, Ð¿ÑÐ¾Ð¸Ð·Ð¾ÑÐ»Ð° Ð½ÐµÐ¸Ð·Ð²ÐµÑÑÐ½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ° (Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ ÐºÐ¾Ð´ Ð¾ÑÐ¸Ð±ÐºÐ¸ Ð² Ð¶ÑÑÐ½Ð°Ð»Ð°Ñ)
restoreFailedTimeout=Ð¢Ð°Ð¹Ð¼-Ð°ÑÑ Ð¿ÑÐ¾ÑÐµÑÑÐ° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ, Ð½ÐµÑ Ð¾ÑÐ²ÐµÑÐ° Ð¾Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
firmwareFileCorrupted=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¾ÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾, Ð¾Ð±Ð½Ð°ÑÑÐ¶ÐµÐ½ Ð¿Ð¾Ð²ÑÐµÐ¶Ð´ÐµÐ½Ð½ÑÐ¹ ÑÐ°Ð¹Ð». ÐÐ°Ð³ÑÑÐ·Ð¸ÑÐµ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÑ Ð²ÑÑÑÐ½ÑÑ
dfuState=DFU
preparingState=Ð / ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ðº Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
recoveryModeState=RM
appleLogoState=AL / ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ðº Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
lostState=LT / ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ðº Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
muxState=MX / ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ðº Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
sendingPayload=SP / ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
copyingFirmware=Copying Firmware
restoreCompleteState=RC
verifyingRestore=VR / ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
flashingFirmware=FF / ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
kernelCache=KC / ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
flashingKernelCache=FKC / ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
baseBand=BB / ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
noSpaceLeftOnDevice=ÐÐµÐ´Ð¾ÑÑÐ°ÑÐ¾ÑÐ½Ð¾ Ð¼ÐµÑÑÐ° Ð½Ð° Ð²Ð°ÑÐµÐ¼ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
restoreFailedInRecoveryMode=ÐÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶Ð½Ð¾ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð² ÑÐµÐ¶Ð¸Ð¼Ðµ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²ÑÐ¿Ð¾Ð»Ð½Ð¸ÑÐµ Ð¿ÑÐ¾ÑÐµÐ´ÑÑÑ ÑÐ±ÑÐ¾ÑÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°, ÑÐ¿ÐµÑÐ¸ÑÐ¸ÑÐ½ÑÑ Ð´Ð»Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
serverNotReachable=Ð¡ÐµÑÐ²ÐµÑ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½, ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð²Ð°ÑÐµ Ð¸Ð½ÑÐµÑÐ½ÐµÑ-ÑÐ¾ÐµÐ´Ð¸Ð½ÐµÐ½Ð¸Ðµ Ð¸ Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ
restoreFailedTryAgain=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÐ´Ð°Ð»Ð¾ÑÑ, Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ
restoreFailedCableIssue=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¸Ð·-Ð·Ð° Ð°Ð¿Ð¿Ð°ÑÐ°ÑÐ½Ð¾Ð¹ Ð¾ÑÐ¸Ð±ÐºÐ¸. ÐÑÐ¾Ð²ÐµÑÑÑÐµ USB-Ð¿Ð¾ÑÑ, ÐºÐ°Ð±ÐµÐ»Ñ Ð¸Ð»Ð¸ ÐºÐ¾Ð¼Ð¿ÑÑÑÐµÑ
restoreAttemptExhausted=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÐ´Ð°Ð»Ð¾ÑÑ. ÐÑÑÐµÑÐ¿Ð°Ð½ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÐ¹ Ð»Ð¸Ð¼Ð¸Ñ Ð¿Ð¾Ð¿ÑÑÐ¾Ðº
waitForHelloScreen=ÐÐ¶Ð¸Ð´Ð°Ð½Ð¸Ðµ Ð¿ÐµÑÐµÑÐ¾Ð´Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ðº ÑÐºÑÐ°Ð½Ñ Ð¿ÑÐ¸Ð²ÐµÑÑÑÐ²Ð¸Ñ
deviceRestoreCompleted=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾ Ð¿Ð¾Ð»Ð½Ð¾ÑÑÑÑ
deviceRebooting=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð·Ð°Ð½Ð¸Ð¼Ð°ÐµÑ Ð²ÑÐµÐ¼Ñ Ð´Ð»Ñ Ð¿ÐµÑÐµÐ·Ð°Ð³ÑÑÐ·ÐºÐ¸
deviceNotFoundInConnection=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾ Ð² Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð½ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°Ñ, Ð¿Ð¾ÑÑÐ¾Ð¼Ñ Ð¿Ð¾Ð²ÑÐ¾ÑÐ¸ÑÐµ Ð¿Ð¾Ð¿ÑÑÐºÑ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
transactionId=ÐÐ´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾Ñ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸
transactionDate=ÐÐ°ÑÐ° ÑÐ¾Ð·Ð´Ð°Ð½Ð¸Ñ
portNumber=ÐÐ¾ÑÑ â
model=ÐÐ¾Ð´ÐµÐ»Ñ
imei2=IMEI2
countryOfOrigin=Ð¡ÑÑÐ°Ð½Ð° Ð¿ÑÐ¾Ð¸ÑÑÐ¾Ð¶Ð´ÐµÐ½Ð¸Ñ
regulatoryModelNumber=Ð ÐµÐ³ÑÐ»ÑÑÐ¾ÑÐ½Ð°Ñ Ð¼Ð¾Ð´ÐµÐ»Ñ â
memory=GB
ram=ÐÐ¿ÐµÑÐ°ÑÐ¸Ð²Ð½Ð°Ñ Ð¿Ð°Ð¼ÑÑÑ
initialCarrier=ÐÐµÑÐ²Ð¾Ð½Ð°ÑÐ°Ð»ÑÐ½ÑÐ¹ Ð¾Ð¿ÐµÑÐ°ÑÐ¾Ñ
pEsn=ÐÑÐµÐ²Ð´Ð¾ ESN
pEsn2=ÐÑÐµÐ²Ð´Ð¾ ESN2
meid=MEID
meid2=MEID2
decimalMeid=ÐÐµÑÑÑÐ¸ÑÐ½ÑÐ¹ MEID
decimalMeid2=ÐÐµÑÑÑÐ¸ÑÐ½ÑÐ¹ MEID2
eid=EID
simTechnology=Ð¢ÐµÑÐ½Ð¾Ð»Ð¾Ð³Ð¸Ñ SIM-ÐºÐ°ÑÑÑ
simSerial=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ SIM-ÐºÐ°ÑÑÑ
simSerial2=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ SIM-ÐºÐ°ÑÑÑ2
wifiMacAddress=MAC-Ð°Ð´ÑÐµÑ Wi-Fi
eSimPresent=ÐÐ°Ð»Ð¸ÑÐ¸Ðµ eSIM
compatibleSim=Ð¡Ð¾Ð²Ð¼ÐµÑÑÐ¸Ð¼Ð°Ñ SIM-ÐºÐ°ÑÑÐ°
notCompatibleSim=ÐÐµÑÐ¾Ð²Ð¼ÐµÑÑÐ¸Ð¼Ð°Ñ SIM-ÐºÐ°ÑÑÐ°
network=Ð¡ÐµÑÑ
networkDetails=ÐÐµÑÐ°Ð»Ð¸ ÑÐµÑÐ¸
deviceLock=ÐÐ°Ð¼Ð¾Ðº ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
mdmResponse=ÐÑÐ²ÐµÑ MDM
knox=Knox
rooted=Rooted
appleId=Apple Id
batteryHealthPercentage=ÐÑÐ¾ÑÐµÐ½Ñ Ð·Ð´Ð¾ÑÐ¾Ð²ÑÑ Ð±Ð°ÑÐ°ÑÐµÐ¸
oemBatteryHealth=ÐÐ´Ð¾ÑÐ¾Ð²ÑÐµ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»ÑÐ½Ð¾Ð¹ Ð±Ð°ÑÐ°ÑÐµÐ¸
cocoBatteryHealth=ÐÐ´Ð¾ÑÐ¾Ð²ÑÐµ Ð±Ð°ÑÐ°ÑÐµÐ¸ Coco
batteryCycle=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ¸ÐºÐ»Ð¾Ð² Ð·Ð°ÑÑÐ´ÐºÐ¸ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryCurrentMaxCapacity=Ð¢ÐµÐºÑÑÐ°Ñ ÐµÐ¼ÐºÐ¾ÑÑÑ Ð±Ð°ÑÐ°ÑÐµÐ¸
cocoCurrentCapacity=Ð¢ÐµÐºÑÑÐ°Ñ ÐµÐ¼ÐºÐ¾ÑÑÑ Coco
batteryDesignMaxCapacity=ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ ÐµÐ¼ÐºÐ¾ÑÑÑ Ð±Ð°ÑÐ°ÑÐµÐ¸
cocoDesignCapacity=ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ ÐµÐ¼ÐºÐ¾ÑÑÑ Coco
batterySerial=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryModel=ÐÐ¾Ð´ÐµÐ»Ñ Ð±Ð°ÑÐ°ÑÐµÐ¸
batterySource=ÐÑÑÐ¾ÑÐ½Ð¸Ðº Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryTemperature=Ð¢ÐµÐ¼Ð¿ÐµÑÐ°ÑÑÑÐ° Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryDrainType=Ð¢Ð¸Ð¿ ÑÐ°Ð·ÑÑÐ´Ð° Ð±Ð°ÑÐ°ÑÐµÐ¸
startHeat=ÐÐ°ÑÐ°ÑÑ Ð½Ð°Ð³ÑÐµÐ²
endHeat=ÐÐ°Ð²ÐµÑÑÐ¸ÑÑ Ð½Ð°Ð³ÑÐµÐ²
batteryChargeStart=ÐÐ°ÑÐ°Ð»Ð¾ Ð·Ð°ÑÑÐ´ÐºÐ¸ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryChargeEnd=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð¸Ðµ Ð·Ð°ÑÑÐ´ÐºÐ¸ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryDrain=Ð Ð°Ð·ÑÑÐ´ Ð±Ð°ÑÐ°ÑÐµÐ¸
startBatteryCharge=ÐÐ°ÑÐ°Ð»Ð¾ Ð·Ð°ÑÑÐ´ÐºÐ¸ Ð±Ð°ÑÐ°ÑÐµÐ¸
endBatteryCharge=ÐÐ°Ð²ÐµÑÑÐµÐ½Ð¸Ðµ Ð·Ð°ÑÑÐ´ÐºÐ¸ Ð±Ð°ÑÐ°ÑÐµÐ¸
totalBatteryDrain=ÐÐ±ÑÐ¸Ð¹ ÑÐ°Ð·ÑÑÐ´ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryDrainInfo=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐ°Ð·ÑÑÐ´Ðµ Ð±Ð°ÑÐ°ÑÐµÐ¸
batteryHealthGrade=ÐÑÐµÐ½ÐºÐ° ÑÐ¾ÑÑÐ¾ÑÐ½Ð¸Ñ Ð±Ð°ÑÐ°ÑÐµÐ¸
deviceCreatedDate=ÐÐ°ÑÐ° Ð´Ð¾Ð±Ð°Ð²Ð»ÐµÐ½Ð¸Ñ
deviceUpdatedDate=ÐÐ°ÑÐ° Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
testPlanName=ÐÐ°Ð·Ð²Ð°Ð½Ð¸Ðµ ÑÐµÑÑÐ¾Ð²Ð¾Ð³Ð¾ Ð¿Ð»Ð°Ð½Ð°
working=Ð Ð°Ð±Ð¾ÑÐ°ÐµÑ
defectsCode=ÐÐ¾Ð´Ñ Ð´ÐµÑÐµÐºÑÐ¾Ð²
manualFailure=Ð ÑÑÐ½Ð°Ñ Ð¾ÑÐ¸Ð±ÐºÐ°
simErased=SIM-ÐºÐ°ÑÑÐ° ÑÑÐµÑÑÐ°
eSimErased=eSIM ÑÑÐµÑÑÐ°
type=Ð¢Ð¸Ð¿ ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
startTime=ÐÑÐµÐ¼Ñ Ð½Ð°ÑÐ°Ð»Ð° ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
endTime=ÐÑÐµÐ¼Ñ Ð¾ÐºÐ¾Ð½ÑÐ°Ð½Ð¸Ñ ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
eraserDiff=ÐÐ±ÑÐµÐµ Ð²ÑÐµÐ¼Ñ ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
erasedNotes=ÐÑÐ¸Ð¼ÐµÑÐ°Ð½Ð¸Ñ Ðº ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
restoreCode=ÐÐ¾Ð´ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
deviceState=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
productCode=ÐÐ¾Ð´ ÑÐ¾Ð²Ð°ÑÐ°
bMic=BMic
vMic=VMic
fMic=FMic
simLock=ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° SIM-ÐºÐ°ÑÑÑ
unlockStatus=Ð¡ÑÐ°ÑÑÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ¸
simLockResponse=ÐÑÐ²ÐµÑ Ð¾ Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐµ SIM-ÐºÐ°ÑÑÑ
carrierLockResponse=ÐÑÐ²ÐµÑ Ð¾ Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐµ Ð¾Ð¿ÐµÑÐ°ÑÐ¾ÑÐ°
apiResponse=ÐÑÐ²ÐµÑ API
errorCode=ÐÐ¼Ñ Ð¿Ð°ÐºÐµÑÐ°
screenTime=ÐÑÐµÐ¼Ñ ÑÐºÑÐ°Ð½Ð°
stationId=ID Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ
testerName=ÐÐ¼Ñ ÑÐµÑÑÐµÑÐ°
licenseId=ID Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸
deviceShutdown=FCCID
oemStatus=Ð¡ÑÐ°ÑÑÑ OEM
parts=ÐÐÐ-ÑÐ°ÑÑÑ
iftCodes=ÐÐ¾Ð´Ñ IFT
preCheckWorking=ÐÑÐµÐ´Ð²Ð°ÑÐ¸ÑÐµÐ»ÑÐ½Ð°Ñ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ°: Ð Ð°Ð±Ð¾ÑÐ°ÐµÑ
preCheckFailed=ÐÑÐµÐ´Ð²Ð°ÑÐ¸ÑÐµÐ»ÑÐ½Ð°Ñ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ°: Ð¡Ð±Ð¾Ð¹
preCheckPassed=ÐÑÐµÐ´Ð²Ð°ÑÐ¸ÑÐµÐ»ÑÐ½Ð°Ñ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ°: ÐÑÐ¾Ð¹Ð´ÐµÐ½Ð°
preCheckPending=ÐÑÐµÐ´Ð²Ð°ÑÐ¸ÑÐµÐ»ÑÐ½Ð°Ñ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ°: ÐÐ¶Ð¸Ð´Ð°Ð½Ð¸Ðµ
transactionType=Ð¢Ð¸Ð¿ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¸
eBayRefurbished=eBay Refurbished
eBayRejection=eBay Rejection
dataVerification=ÐÑÐ¾Ð²ÐµÑÐºÐ° Ð´Ð°Ð½Ð½ÑÑ
genuine=ÐÑÐ¸Ð³Ð¸Ð½Ð°Ð»
notGenuine=ÐÐµ Ð¾ÑÐ¸Ð³Ð¸Ð½Ð°Ð»
na=N/A
mainBoard=ÐÑÐ½Ð¾Ð²Ð½Ð°Ñ Ð¿Ð»Ð°ÑÐ°
frontCamera=ÐÐµÑÐµÐ´Ð½ÑÑ ÐºÐ°Ð¼ÐµÑÐ°
backCamera=ÐÐ°Ð´Ð½ÑÑ ÐºÐ°Ð¼ÐµÑÐ°
lcd=ÐÐ¸ÑÐ¿Ð»ÐµÐ¹
downloadingUpdates=ÐÐ°Ð³ÑÑÐ·ÐºÐ° Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¹
build=Ð¡Ð±Ð¾ÑÐºÐ°
invalidUserNameAndPwd=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²Ð²ÐµÐ´Ð¸ÑÐµ Ð´ÐµÐ¹ÑÑÐ²Ð¸ÑÐµÐ»ÑÐ½Ð¾Ðµ Ð¸Ð¼Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¸ Ð¿Ð°ÑÐ¾Ð»Ñ
simlockLicenseExpired=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ Ð½Ð° ÑÐ¸Ð¼Ð»Ð¾Ðº Ð¸ÑÑÐµÐº
simlockCheckError=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸ ÑÐ¸Ð¼Ð»Ð¾ÐºÐ°
deviceEcidMissing=ÐÑÑÑÑÑÑÐ²ÑÐµÑ ECID ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
checkInternetConnection=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿ÑÐ¾Ð²ÐµÑÑÑÐµ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ðµ Ðº Ð¸Ð½ÑÐµÑÐ½ÐµÑÑ
esnLicenseExpired=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ Ð½Ð° ESN Ð¿ÑÐ¾Ð²ÐµÑÐºÑ Ð¸ÑÑÐµÐº
esnCheckFailed=ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN Ð½Ðµ ÑÐ´Ð°Ð»Ð°ÑÑ
failedToFetchDeviceLicenses=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ Ð½Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
deviceEraseLicenseMasterDateNotStarted=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ Ð´Ð°ÑÐ° Ð¼Ð°ÑÑÐµÑÐ° ÐµÑÐµ Ð½Ðµ Ð½Ð°ÑÐ°Ð»Ð°ÑÑ
deviceEraseLicenseMasterExpired=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¼Ð°ÑÑÐµÑÐ° Ð¸ÑÑÐµÐº
deviceEraseLicenseUserDateNotStarted=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ Ð´Ð°ÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÐµÑÐµ Ð½Ðµ Ð½Ð°ÑÐ°Ð»Ð°ÑÑ
deviceEraseLicenseUserExpired=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¸ÑÑÐµÐº
deviceConnectionLicenseDeviceEraseLicenseLicenseExpired=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð¸ÑÑÐµÐº
deviceConnectionLicenseMasterDateNotStarted=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ Ð´Ð°ÑÐ° Ð¼Ð°ÑÑÐµÑÐ° ÐµÑÐµ Ð½Ðµ Ð½Ð°ÑÐ°Ð»Ð°ÑÑ
deviceConnectionLicenseMasterExpired=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¼Ð°ÑÑÐµÑÐ° Ð¸ÑÑÐµÐº
deviceConnectionLicenseUserDateNotStarted=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ Ð´Ð°ÑÐ° Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ ÐµÑÐµ Ð½Ðµ Ð½Ð°ÑÐ°Ð»Ð°ÑÑ
deviceConnectionLicenseUserExpired=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð¿Ð¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»Ñ Ð¸ÑÑÐµÐº
deviceConnectionLicenseLicenseExpired=Ð¡Ð±Ð¾Ð¹, Ð¿Ð¾ÑÐ¾Ð¼Ñ ÑÑÐ¾ ÑÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð¸ÑÑÐµÐº
powerOffSuccess=Ð£ÑÐ¿ÐµÑÐ½Ð¾Ðµ Ð²ÑÐºÐ»ÑÑÐµÐ½Ð¸Ðµ
powerOffFailedNoDevice=Ð¡Ð±Ð¾Ð¹ Ð²ÑÐºÐ»ÑÑÐµÐ½Ð¸Ñ, ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾
powerOffFailedUnknownErr=Ð¡Ð±Ð¾Ð¹ Ð²ÑÐºÐ»ÑÑÐµÐ½Ð¸Ñ, Ð½ÐµÐ¸Ð·Ð²ÐµÑÑÐ½Ð°Ñ Ð¿ÑÐ¸ÑÐ¸Ð½Ð°
uninstallAppFailed=Ð¡Ð±Ð¾Ð¹ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ñ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
uninstallProfileFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ´Ð°Ð»Ð¸ÑÑ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ
uninstallAppProfileFailed=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ´Ð°Ð»Ð¸ÑÑ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¸ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ
failedToGetInfoReconnect=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ. ÐÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÐµÑÑ!
cantFindDeviceReconnect=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ Ð½Ð°Ð¹ÑÐ¸ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾, Ð¿ÐµÑÐµÐ¿Ð¾Ð´ÐºÐ»ÑÑÐ¸ÑÐµÑÑ!
noTestResultYet=ÐÐ¾ÐºÐ° Ð½ÐµÑ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð² ÑÐµÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ
errorOccurredInPeo=ÐÑÐ¾Ð¸Ð·Ð¾ÑÐ»Ð° Ð¾ÑÐ¸Ð±ÐºÐ° Ð² PEO
errorInStartingPhonecheck=ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð·Ð°Ð¿ÑÑÐºÐµ Phonecheck. ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÑÑÐ²Ð°ÐµÑÑÑ ÑÐµÑÐµÐ· %d
mqttClientNotConnected=MQTT-ÐºÐ»Ð¸ÐµÐ½Ñ Ð½Ðµ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½ Ðº Ð±ÑÐ¾ÐºÐµÑÑ.\s ÐÐ¾Ð·Ð¼Ð¾Ð¶Ð½ÑÐµ Ð¿ÑÐ¸ÑÐ¸Ð½Ñ:\s 1- ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð´ÐµÐ¼Ð¾Ð½Ð° Ð½Ðµ Ð·Ð°Ð¿ÑÑÐµÐ½Ð¾\
  2- ÐÑÐ¸Ð±ÐºÐ° Ð¿ÑÐ¸ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ð¸ Ðº Ð±ÑÐ¾ÐºÐµÑÐ°Ð¼ MQTT \s ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð·Ð°ÐºÑÑÐ²Ð°ÐµÑÑÑ ÑÐµÑÐµÐ· %d
serialColon=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ:
lidarSerialColon=ÐÐ¸Ð´Ð°Ñ Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹: 
syncFailed=Ð¡Ð±Ð¾Ð¹ ÑÐ¸Ð½ÑÑÐ¾Ð½Ð¸Ð·Ð°ÑÐ¸Ð¸
prepareFailed=Ð¡Ð±Ð¾Ð¹ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ¸
prepareFailedTryManually=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð½Ðµ ÑÐ´Ð°Ð»Ð°ÑÑ, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ Ð²ÑÑÑÐ½ÑÑ
restartingForNotices=ÐÐµÑÐµÐ·Ð°Ð¿ÑÑÐº Ð´Ð»Ñ ÑÐ²ÐµÐ´Ð¾Ð¼Ð»ÐµÐ½Ð¸Ð¹
deviceInUsbMode=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð² ÑÐµÐ¶Ð¸Ð¼Ðµ USB
appInstallFailedHyphen=- Ð¡Ð±Ð¾Ð¹ ÑÑÑÐ°Ð½Ð¾Ð²ÐºÐ¸ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
connectingHyphen=- ÐÐ¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ðµ
pairingInProgressHyphen=- ÐÐ´ÐµÑ ÑÐ¾Ð¿ÑÑÐ¶ÐµÐ½Ð¸Ðµ
pairingFailedHyphen=- Ð¡Ð±Ð¾Ð¹ ÑÐ¾Ð¿ÑÑÐ¶ÐµÐ½Ð¸Ñ
pairingSuccessfulHyphen=- Ð£ÑÐ¿ÐµÑÐ½Ð¾Ðµ ÑÐ¾Ð¿ÑÑÐ¶ÐµÐ½Ð¸Ðµ
deviceInfoCollectedHyphen=- ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ ÑÐ¾Ð±ÑÐ°Ð½Ð°
deviceInfoFailedHyphen=- Ð¡Ð±Ð¾Ð¹ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
deviceImeiCollectedHyphen=- IMEI ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° ÑÐ¾Ð±ÑÐ°Ð½
failedToGetImeiHyphen=- Ð¡Ð±Ð¾Ð¹ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ IMEI
activationInProgressHyphen=- ÐÐ´ÐµÑ Ð°ÐºÑÐ¸Ð²Ð°ÑÐ¸Ñ
activationFailedHyphen=- Ð¡Ð±Ð¾Ð¹ Ð°ÐºÑÐ¸Ð²Ð°ÑÐ¸Ð¸
activationSuccessfulHyphen=- ÐÐºÑÐ¸Ð²Ð°ÑÐ¸Ñ ÑÑÐ¿ÐµÑÐ½Ð°
activationLockOnHyphen=- ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° Ð°ÐºÑÐ¸Ð²Ð°ÑÐ¸Ð¸ Ð²ÐºÐ»ÑÑÐµÐ½Ð°
pushWifiProfileHyphen=- ÐÑÐ¿ÑÐ°Ð²ÐºÐ° Ð¿ÑÐ¾ÑÐ¸Ð»Ñ Wi-Fi
fetchMdmInfoFailedHyphen=- Ð¡Ð±Ð¾Ð¹ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ MDM
fetchMdmInfoSuccessfulHyphen=- ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ MDM Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchKnoxInfoSuccessfulHyphen=- ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Knox Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchFrpInfoSuccessfulHyphen=- ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ FRP Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchBatteryInfoSuccessfulHyphen=- ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð° ÑÑÐ¿ÐµÑÐ½Ð¾
fetchBatteryInfoFailedHyphen=- Ð¡Ð±Ð¾Ð¹ Ð¿Ð¾Ð»ÑÑÐµÐ½Ð¸Ñ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ
wifiPushSuccessfulHyphen=- Ð£ÑÐ¿ÐµÑÐ½Ð°Ñ Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ° Wi-Fi
wifiPushFailedHyphen=- ÐÑÐ¸Ð±ÐºÐ° Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ¸ Wi-Fi
devImageMountingHyphen=- ÐÐ¾Ð½ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¾Ð±ÑÐ°Ð·Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
devImageMountingFailedHyphen=- Ð¡Ð±Ð¾Ð¹ Ð¼Ð¾Ð½ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ñ Ð¾Ð±ÑÐ°Ð·Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
devImageMountingSucceededHyphen=- Ð£ÑÐ¿ÐµÑÐ½Ð¾Ðµ Ð¼Ð¾Ð½ÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¾Ð±ÑÐ°Ð·Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
checkIfAppInstallRequiredHyphen=- Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÐ° Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
appInstallSuccessHyphen=- Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÐ° Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ ÑÑÐ¿ÐµÑÐ½Ð°
appAlreadyInstalledHyphen=- ÐÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ ÑÐ¶Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾
configPushInProgressHyphen=- ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ° Ð¿ÐµÑÐµÐ´Ð°ÑÐ¸ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
configPushFailedHyphen=- ÐÑÐ¸Ð±ÐºÐ° Ð¾ÑÐ¿ÑÐ°Ð²ÐºÐ¸ ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ð¸
configPushSuccessHyphen=- ÐÑÐ¿ÑÐ°Ð²ÐºÐ° ÐºÐ¾Ð½ÑÐ¸Ð³ÑÑÐ°ÑÐ¸Ð¸ ÑÑÐ¿ÐµÑÐ½Ð°
appTestingDoneHyphen=- Ð¢ÐµÑÑÐ¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð¾
checkingEsnHyphen=- ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN
esnSuccessfulHyphen=- ÐÑÐ¾Ð²ÐµÑÐºÐ° ESN ÑÑÐ¿ÐµÑÐ½Ð°
printingHyphen=- ÐÐµÑÐ°ÑÑ
eraseInProgressHyphen=- Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
eraseSuccessfulHyphen=- Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾
shuttingDownPhoneHyphen=- ÐÑÐºÐ»ÑÑÐµÐ½Ð¸Ðµ ÑÐµÐ»ÐµÑÐ¾Ð½Ð°
restoreInProgressHyphen=- ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð² Ð¿ÑÐ¾ÑÐµÑÑÐµ
restoreSuccessfulHyphen=- ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾
clickOkAfterUpdates=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ <b>OK</b>, Ð¿Ð¾ÑÐ»Ðµ ÑÐ¾Ð³Ð¾ ÐºÐ°Ðº Ð²Ñ Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÐµ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸ Ð² Ð±ÑÐ°ÑÐ·ÐµÑÐµ.
browserActionNotSupported1=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¾ÑÐºÑÑÑÑ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸ Ð² Ð±ÑÐ°ÑÐ·ÐµÑÐµ, Ð´ÐµÐ¹ÑÑÐ²Ð¸Ðµ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ. ÐÐµÑÐµÐ¹Ð´Ð¸ÑÐµ Ð¿Ð¾ ÑÑÑÐ»ÐºÐµ 
browserActionNotSupported2=  Ð¸ Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÐµ Ð²Ð°Ñ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ.
vendorInformation=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð¿Ð¾ÑÑÐ°Ð²ÑÐ¸ÐºÐµ
dataMissing=ÐÑÑÑÑÑÑÐ²Ð¸Ðµ Ð´Ð°Ð½Ð½ÑÑ
manualPEO=Ð ÑÑÐ½Ð¾Ð¹ PEO
eSimPresentColon=eSim Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÐµÑ:
dualSimColon=ÐÐ²Ðµ ÑÐ¸Ð¼-ÐºÐ°ÑÑÑ:
simCarrierColon=ÐÐ¿ÐµÑÐ°ÑÐ¾Ñ SIM-ÐºÐ°ÑÑÑ:
customizations=ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸
labelPrinting=ÐÐµÑÐ°ÑÑ ÑÑÐ¸ÐºÐµÑÐºÐ¸
labelOne=Ð­ÑÐ¸ÐºÐµÑÐºÐ° 1
labelTwo=Ð­ÑÐ¸ÐºÐµÑÐºÐ° 2
printerOneForLabelOne=ÐÑÐ¸Ð½ÑÐµÑ 1 Ð´Ð»Ñ Ð­ÑÐ¸ÐºÐµÑÐºÐ¸ 1
printerTwoForLabelTwo=ÐÑÐ¸Ð½ÑÐµÑ 2 Ð´Ð»Ñ Ð­ÑÐ¸ÐºÐµÑÐºÐ¸ 2
printToPdf=ÐÐµÑÐ°ÑÑ Ð² PDF
pleaseSelect=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð²ÑÐ±ÐµÑÐ¸ÑÐµ
none=ÐÐ¸ÑÐµÐ³Ð¾
restoring=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ:
pairTheDeviceAgain=Ð¡Ð½Ð¾Ð²Ð° ÑÐ²ÑÐ¶Ð¸ÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
carrierLock=ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° Ð¾Ð¿ÐµÑÐ°ÑÐ¾ÑÐ°
exportLog=Ð­ÐºÑÐ¿Ð¾ÑÑ Ð¶ÑÑÐ½Ð°Ð»Ð¾Ð²
sourceApiCalling=- ÐÑÐ·Ð¾Ð² Ð¸ÑÑÐ¾Ð´Ð½Ð¾Ð³Ð¾ API
reProcessDeviceAction=ÐÐµÐ¹ÑÑÐ²Ð¸Ðµ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾Ð¹ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐ¸ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
previouslyProcessedDevice=Ð­ÑÐ¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ ÑÐ°Ð½ÐµÐµ Ð±ÑÐ»Ð¾ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐ°Ð½Ð¾
rePrintLabel=ÐÐµÑÐµÐ¿ÐµÑÐ°ÑÐ°ÑÑ ÑÑÐ¸ÐºÐµÑÐºÑ
reProcessDevice=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾Ð¹ Ð¾Ð±ÑÐ°Ð±Ð¾ÑÐºÐ¸
eraseForMdm=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ Ð´Ð»Ñ MDM
mdmErasedWaitingForBootup=MDM Ð¡ÑÐµÑÑÐ¾, ÐÐ¶Ð¸Ð´Ð°Ð½Ð¸Ðµ Ð·Ð°Ð³ÑÑÐ·ÐºÐ¸
pushWiFi=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ Wi-Fi
appInstall=Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÐ° Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
removeAppAndProfiles=Ð£Ð´Ð°Ð»Ð¸ÑÑ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð¸ Ð¿ÑÐ¾ÑÐ¸Ð»Ð¸
eraseFailedAppNotInstalled=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÑÐµÑÐµÑÑ, Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¾
phonecheckIsStarting=ÐÑÐ¾Ð²ÐµÑÐºÐ° ÑÐµÐ»ÐµÑÐ¾Ð½Ð° Ð½Ð°ÑÐ¸Ð½Ð°ÐµÑÑÑâ¦
updateAvailable=ÐÐ¾ÑÑÑÐ¿Ð½Ð¾ Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ
newVersionAvailable=ÐÐ¾ÑÑÑÐ¿Ð½Ð° Ð½Ð¾Ð²Ð°Ñ Ð²ÐµÑÑÐ¸Ñ Phonecheck.
likeToUpdateNow=Ð¥Ð¾ÑÐ¸ÑÐµ Ð¾Ð±Ð½Ð¾Ð²Ð¸ÑÑ ÑÐµÐ¹ÑÐ°Ñ?
updateNow=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ ÑÐµÐ¹ÑÐ°Ñ
later=ÐÐ¾Ð·Ð¶Ðµ
installingUpdate=Ð£ÑÑÐ°Ð½Ð¾Ð²ÐºÐ° Ð¾Ð±Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
eraseFailedSerialPortClosed=ÑÑÐµÑÐµÑÑ Ð½Ðµ ÑÐ´Ð°Ð»Ð¾ÑÑ, Ð¿Ð¾ÑÐ»ÐµÐ´Ð¾Ð²Ð°ÑÐµÐ»ÑÐ½ÑÐ¹ Ð¿Ð¾ÑÑ Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¾ÑÐºÑÑÑ
restoreSuccess=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ ÑÑÐ¿ÐµÑÐ½Ð¾:
queuedForRestore=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ ÑÑÐ¾Ð¸Ñ Ð² Ð¾ÑÐµÑÐµÐ´Ð¸ Ð½Ð° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ
restoreNeeded=Ð¢ÑÐµÐ±ÑÐµÑÑÑ Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð´Ð»Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
simCardError=ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð° SIM-ÐºÐ°ÑÑÐ°
simCardErrorMsg=Ð£Ð´Ð°Ð»Ð¸ÑÐµ SIM-ÐºÐ°ÑÑÑ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ
skip=ÐÑÐ¾Ð¿ÑÑÑÐ¸ÑÑ
removeSimCardMsg=Ð£Ð´Ð°Ð»Ð¸ÑÐµ SIM-ÐºÐ°ÑÑÑ Ð¸ Ð½Ð°ÑÐ½Ð¸ÑÐµ ÑÑÐ¸ÑÐ°ÑÑ Ð·Ð°Ð½Ð¾Ð²Ð¾
warningSkip=ÐÑÐ¿ÑÐ°Ð²ÐºÐ° Ð·Ð°Ð¿ÑÐ¾ÑÐ° Ð½Ð° ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð¿Ð¾ÑÐ»Ðµ Ð¿ÑÐ¾Ð¿ÑÑÐºÐ°
simCardWarnMsg=SIM-ÐºÐ°ÑÑÐ° Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÐµÑ Ð² ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
sdCardError=ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð° SD-ÐºÐ°ÑÑÐ°
sdCardErrorMsg=ÐÐ·Ð²Ð»ÐµÐºÐ¸ÑÐµ SD-ÐºÐ°ÑÑÑ, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ
removeSdCardMsg=ÐÐ·Ð²Ð»ÐµÐºÐ¸ÑÐµ SD-ÐºÐ°ÑÑÑ Ð¸ Ð¿Ð¾Ð²ÑÐ¾ÑÐ½Ð¾ Ð½Ð°ÑÐ½Ð¸ÑÐµ ÑÑÐ¸ÑÐ°Ð½Ð¸Ðµ
sdCardWarnMsg=Ð ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÐµÑ SD-ÐºÐ°ÑÑÐ°
restoreThreads=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ Ð¿Ð¾ÑÐ¾ÐºÐ¾Ð² Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ:
restoreReqSent=ÐÐ°Ð¿ÑÐ¾Ñ Ð½Ð° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð¾ÑÐ¿ÑÐ°Ð²Ð»ÐµÐ½
headsetPort=ÐÐ¾ÑÑ Ð³Ð°ÑÐ½Ð¸ÑÑÑÑ
headsetLeft=ÐÐ°ÑÐ½Ð¸ÑÑÑÐ°-Ð»ÐµÐ²Ð¾
headsetRight=ÐÐ°ÑÐ½Ð¸ÑÑÑÐ°-Ð¿ÑÐ°Ð²Ð¾
audioInput=ÐÑÐ´Ð¸Ð¾ Ð²ÑÐ¾Ð´
autoLS=ÐÐ²ÑÐ¾ LS
loudSpeaker=ÐÑÐ¾Ð¼ÐºÐ¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÐµÐ»Ñ
loudSpeakerM=ÐÑÐ¾Ð¼ÐºÐ¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÐµÐ»Ñ-M
micLSTest=Ð¢ÐµÑÑ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð° LS
microphone=ÐÐ¸ÐºÑÐ¾ÑÐ¾Ð½
videoMicrophone=ÐÐ¸Ð´ÐµÐ¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½
headsetLightPort=ÐÐ¾ÑÑ Ð¿Ð¾Ð´ÑÐ²ÐµÑÐºÐ¸ Ð³Ð°ÑÐ½Ð¸ÑÑÑÑ
bottomMicQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð½Ð¸Ð¶Ð½ÐµÐ³Ð¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
earpieceQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð½Ð°ÑÑÐ½Ð¸ÐºÐ°
frontMicQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð¿ÐµÑÐµÐ´Ð½ÐµÐ³Ð¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
microphoneQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
rearMicQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð·Ð°Ð´Ð½ÐµÐ³Ð¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
recordingQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð·Ð°Ð¿Ð¸ÑÐ¸
videoMicrophoneQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð²Ð¸Ð´ÐµÐ¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
bottomMicPB=ÐÐ¸Ð¶Ð½Ð¸Ð¹ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½ PB
ESPlayback=ES Ð²Ð¾ÑÐ¿ÑÐ¾Ð¸Ð·Ð²ÐµÐ´ÐµÐ½Ð¸Ðµ
frontMicPB=ÐÐµÑÐµÐ´Ð½Ð¸Ð¹ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½ PB
manualRecording=Ð ÑÑÐ½Ð°Ñ Ð·Ð°Ð¿Ð¸ÑÑ
micPlayback=ÐÐ¾ÑÐ¿ÑÐ¾Ð¸Ð·Ð²ÐµÐ´ÐµÐ½Ð¸Ðµ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
microphonePB=ÐÐ¸ÐºÑÐ¾ÑÐ¾Ð½ PB
rearMicPB=ÐÐ°Ð´Ð½Ð¸Ð¹ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½ PB
videoMicPB=ÐÐ¸Ð´ÐµÐ¾ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½ PB
manualAudioQuality=Ð ÑÑÐ½Ð¾Ðµ ÐºÐ°ÑÐµÑÑÐ²Ð¾ Ð°ÑÐ´Ð¸Ð¾
manualRingtoneQuality=Ð ÑÑÐ½Ð¾Ðµ ÐºÐ°ÑÐµÑÑÐ²Ð¾ ÑÐ¸Ð½Ð³ÑÐ¾Ð½Ð°
sPen=SPen
sPenBackButton=ÐÐ°Ð´Ð½ÑÑ ÐºÐ½Ð¾Ð¿ÐºÐ° SPen
sPenHover=ÐÐ°ÑÐµÐ½Ð¸Ðµ SPen
sPenMenuButton=ÐÐ½Ð¾Ð¿ÐºÐ° Ð¼ÐµÐ½Ñ SPen
sPenRemove=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ SPen
spenPlusButtons=ÐÐ½Ð¾Ð¿ÐºÐ¸ SPen Plus
backButton=ÐÐ½Ð¾Ð¿ÐºÐ° Ð½Ð°Ð·Ð°Ð´
buttonsTest=Ð¢ÐµÑÑ ÐºÐ½Ð¾Ð¿Ð¾Ðº
flipSwitch=ÐÐµÑÐµÐºÐ»ÑÑÐ°ÑÐµÐ»Ñ
homeButton=ÐÐ½Ð¾Ð¿ÐºÐ° Ð´Ð¾Ð¼Ð¾Ð¹
menuButton=ÐÐ½Ð¾Ð¿ÐºÐ° Ð¼ÐµÐ½Ñ
powerButton=ÐÐ½Ð¾Ð¿ÐºÐ° Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
volumeDownButton=ÐÐ½Ð¾Ð¿ÐºÐ° ÑÐ¼ÐµÐ½ÑÑÐµÐ½Ð¸Ñ Ð³ÑÐ¾Ð¼ÐºÐ¾ÑÑÐ¸
volumeUpButton=ÐÐ½Ð¾Ð¿ÐºÐ° ÑÐ²ÐµÐ»Ð¸ÑÐµÐ½Ð¸Ñ Ð³ÑÐ¾Ð¼ÐºÐ¾ÑÑÐ¸
sDCardDetect=ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð¸Ðµ SD ÐºÐ°ÑÑÑ
sDCardRemove=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ SD ÐºÐ°ÑÑÑ
alertSlider=Ð¡Ð»Ð°Ð¹Ð´ÐµÑ Ð¾Ð¿Ð¾Ð²ÐµÑÐµÐ½Ð¸Ñ
bixbyButton=ÐÐ½Ð¾Ð¿ÐºÐ° Bixby
autoSnapFront=ÐÐ²ÑÐ¾SnapÐÐµÑÐµÐ´
cameraTest=Ð¢ÐµÑÑ ÐºÐ°Ð¼ÐµÑÑ
frontCameraQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð¿ÐµÑÐµÐ´Ð½ÐµÐ¹ ÐºÐ°Ð¼ÐµÑÑ
frontVideoCamera=ÐÐµÑÐµÐ´Ð½ÑÑ Ð²Ð¸Ð´ÐµÐ¾ÐºÐ°Ð¼ÐµÑÐ°
autoSnapRear=ÐÐ²ÑÐ¾SnapÐÐ°Ð´
cameraAutoFocus=ÐÐ²ÑÐ¾ÑÐ¾ÐºÑÑ ÐºÐ°Ð¼ÐµÑÑ
flash=ÐÑÐ¿ÑÑÐºÐ°
flashlight=Ð¤Ð¾Ð½Ð°ÑÐ¸Ðº
rearCamera=ÐÐ°Ð´Ð½ÑÑ ÐºÐ°Ð¼ÐµÑÐ°
rearCameraQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð·Ð°Ð´Ð½ÐµÐ¹ ÐºÐ°Ð¼ÐµÑÑ
rearVideoCamera=ÐÐ°Ð´Ð½ÑÑ Ð²Ð¸Ð´ÐµÐ¾ÐºÐ°Ð¼ÐµÑÐ°
ultraWideCamera=Ð£Ð»ÑÑÑÐ°ÑÐ¸ÑÐ¾ÐºÐ°Ñ ÐºÐ°Ð¼ÐµÑÐ°
ultraWideCameraQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ ÑÐ»ÑÑÑÐ°ÑÐ¸ÑÐ¾ÐºÐ¾Ð¹ ÐºÐ°Ð¼ÐµÑÑ
telephotoCamera=Ð¢ÐµÐ»ÐµÐ¾Ð±ÑÐµÐºÑÐ¸Ð²Ð½Ð°Ñ ÐºÐ°Ð¼ÐµÑÐ°
telephotoCameraQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ ÑÐµÐ»ÐµÐ¾Ð±ÑÐµÐºÑÐ¸Ð²Ð½Ð¾Ð¹ ÐºÐ°Ð¼ÐµÑÑ
rearCamtoGallery=ÐÐ°Ð´Ð½ÑÑ ÐºÐ°Ð¼ÐµÑÐ° Ð² Ð³Ð°Ð»ÐµÑÐµÑ
barcodeScan=Ð¡ÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð¸Ðµ ÑÑÑÐ¸Ñ-ÐºÐ¾Ð´Ð°
imageCaptureTime=ÐÑÐµÐ¼Ñ Ð·Ð°ÑÐ²Ð°ÑÐ° Ð¸Ð·Ð¾Ð±ÑÐ°Ð¶ÐµÐ½Ð¸Ñ
manualCameraTest=Ð ÑÑÐ½Ð¾Ð¹ ÑÐµÑÑ ÐºÐ°Ð¼ÐµÑÑ
manualCameras=Ð ÑÑÐ½ÑÐµ ÐºÐ°Ð¼ÐµÑÑ
qRTest=Ð¢ÐµÑÑ QR
fingerprintSensor=Ð¡ÐµÐ½ÑÐ¾Ñ Ð¾ÑÐ¿ÐµÑÐ°ÑÐºÐ¾Ð² Ð¿Ð°Ð»ÑÑÐµÐ²
manualVibration=Ð ÑÑÐ½Ð°Ñ Ð²Ð¸Ð±ÑÐ°ÑÐ¸Ñ
proximitySensor=ÐÐ°ÑÑÐ¸Ðº Ð¿ÑÐ¸Ð±Ð»Ð¸Ð¶ÐµÐ½Ð¸Ñ
vibration=ÐÐ¸Ð±ÑÐ°ÑÐ¸Ñ
accelerometer=ÐÐºÑÐµÐ»ÐµÑÐ¾Ð¼ÐµÑÑ
autoAccelerometer=ÐÐ²ÑÐ¾ Ð°ÐºÑÐµÐ»ÐµÑÐ¾Ð¼ÐµÑÑ
gyroscope=ÐÐ¸ÑÐ¾ÑÐºÐ¾Ð¿
screenRotation=ÐÑÐ°ÑÐµÐ½Ð¸Ðµ ÑÐºÑÐ°Ð½Ð°
barometer=ÐÐ°ÑÐ¾Ð¼ÐµÑÑ
compass=ÐÐ¾Ð¼Ð¿Ð°Ñ
nonSamsungFingerprint=ÐÐµ Samsung Ð¾ÑÐ¿ÐµÑÐ°ÑÐ¾Ðº Ð¿Ð°Ð»ÑÑÐ°
samsungFingerprint=Samsung Ð¾ÑÐ¿ÐµÑÐ°ÑÐ¾Ðº Ð¿Ð°Ð»ÑÑÐ°
3DTouch=3D ÐºÐ°ÑÐ°Ð½Ð¸Ðµ
digitizer=Ð¦Ð¸ÑÑÐ¾Ð²Ð°ÑÐµÐ»Ñ
edgeScreen=Ð­ÐºÑÐ°Ð½ Ñ Ð¸Ð·Ð³Ð¸Ð±Ð¾Ð¼
fingerTrailDigitizer=Ð¦Ð¸ÑÑÐ¾Ð²Ð°ÑÐµÐ»Ñ ÑÐ»ÐµÐ´Ð° Ð¿Ð°Ð»ÑÑÐ°
forceTouch=Ð¡Ð¸Ð»ÑÐ½Ð¾Ðµ ÐºÐ°ÑÐ°Ð½Ð¸Ðµ
glassCondition=Ð¡Ð¾ÑÑÐ¾ÑÐ½Ð¸Ðµ ÑÑÐµÐºÐ»Ð°
glassCracked=Ð¢ÑÐµÑÐ½ÑÐ²ÑÐµÐµ ÑÑÐµÐºÐ»Ð¾
lCD=ÐÐ-Ð´Ð¸ÑÐ¿Ð»ÐµÐ¹
autoQRCode=ÐÐ²ÑÐ¾ QR ÐºÐ¾Ð´
digitizerNPattern=Ð¦Ð¸ÑÑÐ¾Ð²Ð°ÑÐµÐ»Ñ N ÑÐ·Ð¾Ñ
manualQRCode=Ð ÑÑÐ½Ð¾Ð¹ QR ÐºÐ¾Ð´
multiTouchTest=Ð¢ÐµÑÑ MultiTouch
multiTouch=MultiTouch
samDigi=Ð¡Ð°Ð¼ Digi
brightness=Ð¯ÑÐºÐ¾ÑÑÑ
customTests=ÐÐ¾Ð»ÑÐ·Ð¾Ð²Ð°ÑÐµÐ»ÑÑÐºÐ¸Ðµ ÑÐµÑÑÑ
sWVersion=ÐÐµÑÑÐ¸Ñ ÐÐ
IMEIScreenshot=Ð¡ÐºÑÐ¸Ð½ÑÐ¾Ñ IMEI
callTest=Ð¢ÐµÑÑ Ð·Ð²Ð¾Ð½ÐºÐ°
networkConnectivity=Ð¡ÐµÑÐµÐ²Ð¾Ðµ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ðµ
simReader=Ð¡Ð¸Ð¼-ÑÐ¸Ð´ÐµÑ
bluetooth=Bluetooth
WiFi=WiFi
wiFi=WiFi
dualCallTest=Ð¢ÐµÑÑ Ð´Ð²Ð¾Ð¹Ð½Ð¾Ð³Ð¾ Ð·Ð²Ð¾Ð½ÐºÐ°
networkConnectivity1=Ð¡ÐµÑÐµÐ²Ð¾Ðµ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ðµ 1
networkConnectivity2=Ð¡ÐµÑÐµÐ²Ð¾Ðµ Ð¿Ð¾Ð´ÐºÐ»ÑÑÐµÐ½Ð¸Ðµ 2
simReader1=Ð¡Ð¸Ð¼-ÑÐ¸Ð´ÐµÑ 1
simReader2=Ð¡Ð¸Ð¼-ÑÐ¸Ð´ÐµÑ 2
enhancedBluetooth=Ð£Ð»ÑÑÑÐµÐ½Ð½ÑÐ¹ Bluetooth
gPS=GPS
nFC=NFC
dualSimReaderTest=Ð¢ÐµÑÑ Ð´Ð²Ð¾Ð¹Ð½Ð¾Ð³Ð¾ ÑÐ¸Ð¼-ÑÐ¸Ð´ÐµÑÐ°
simReaderKey=ÐÐ»ÑÑ ÑÐ¸Ð¼-ÑÐ¸Ð´ÐµÑÐ°
simReaderTest=Ð¢ÐµÑÑ ÑÐ¸Ð¼-ÑÐ¸Ð´ÐµÑÐ°
simRemove=Ð£Ð´Ð°Ð»ÐµÐ½Ð¸Ðµ ÑÐ¸Ð¼-ÐºÐ°ÑÑÑ
simRemoveKey=ÐÐ»ÑÑ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ñ ÑÐ¸Ð¼-ÐºÐ°ÑÑÑ
chargePort=ÐÐ¾ÑÑ Ð·Ð°ÑÑÐ´ÐºÐ¸
wirelessCharging=ÐÐµÑÐ¿ÑÐ¾Ð²Ð¾Ð´Ð½Ð°Ñ Ð·Ð°ÑÑÐ´ÐºÐ°
colors=Ð¦Ð²ÐµÑÐ°
grading=ÐÑÐ°Ð´Ð°ÑÐ¸Ñ
housing=ÐÐ¾ÑÐ¿ÑÑ
earSpeaker=Ð£ÑÐ½Ð¾Ð¹ Ð´Ð¸Ð½Ð°Ð¼Ð¸Ðº
frontCameraFocus=Ð¤Ð¾ÐºÑÑ Ð¿ÐµÑÐµÐ´Ð½ÐµÐ¹ ÐºÐ°Ð¼ÐµÑÑ
rearCameraFocus=Ð¤Ð¾ÐºÑÑ Ð·Ð°Ð´Ð½ÐµÐ¹ ÐºÐ°Ð¼ÐµÑÑ
frontMicrophone=ÐÐµÑÐµÐ´Ð½Ð¸Ð¹ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½
headsetMediaButton=ÐÐ½Ð¾Ð¿ÐºÐ° Ð¼ÑÐ»ÑÑÐ¸Ð¼ÐµÐ´Ð¸Ð° Ð³Ð°ÑÐ½Ð¸ÑÑÑÑ
headsetVolumeDown=Ð£Ð¼ÐµÐ½ÑÑÐµÐ½Ð¸Ðµ Ð³ÑÐ¾Ð¼ÐºÐ¾ÑÑÐ¸ Ð³Ð°ÑÐ½Ð¸ÑÑÑÑ
headsetVolumeUp=Ð£Ð²ÐµÐ»Ð¸ÑÐµÐ½Ð¸Ðµ Ð³ÑÐ¾Ð¼ÐºÐ¾ÑÑÐ¸ Ð³Ð°ÑÐ½Ð¸ÑÑÑÑ
lightSensor=ÐÐ°ÑÑÐ¸Ðº ÑÐ²ÐµÑÐ°
loudspeakerQuality=ÐÐ°ÑÐµÑÑÐ²Ð¾ Ð³ÑÐ¾Ð¼ÐºÐ¾Ð³Ð¾Ð²Ð¾ÑÐ¸ÑÐµÐ»Ñ
micRecording=ÐÐ°Ð¿Ð¸ÑÑ Ð¼Ð¸ÐºÑÐ¾ÑÐ¾Ð½Ð°
batteryWarning=ÐÑÐµÐ´ÑÐ¿ÑÐµÐ¶Ð´ÐµÐ½Ð¸Ðµ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ
faceID=Face ID
earpiece=Ð½Ð°ÑÑÐ½Ð¸Ðº
validateImei=Ð¡ÐºÐ°Ð½Ð¸ÑÐ¾Ð²Ð°ÑÑ IMEI/Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ
sdCardDetected=ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð° SD-ÐºÐ°ÑÑÐ°
simCardDetected=ÐÐ±Ð½Ð°ÑÑÐ¶ÐµÐ½Ð° SIM-ÐºÐ°ÑÑÐ°
refreshCustomizations=ÐÐ°ÑÑÑÐ¾Ð¹ÐºÐ¸ Ð½Ðµ Ð¿Ð¾Ð»ÑÑÐµÐ½Ñ. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð½Ð°Ð¶Ð¼Ð¸ÑÐµ <b>ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ</b>.
refresh=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ
refreshCloudCustomizations=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ Ð¾Ð±Ð»Ð°ÑÐ½ÑÐµ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸
dataFound=ÐÐ°Ð½Ð½ÑÐµ Ð½Ð°Ð¹Ð´ÐµÐ½Ñ Ð½Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ
dataNotFound=ÐÐ°Ð½Ð½ÑÐµ Ð½Ð° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½Ñ
bHA=BH-A
bHB=BH-B
bHC=BH-C
bHD=BH-D
iMEI/SerialMismatch=IMEI/SerialMismatch
cloudDeviceLookup=ÐÐ¾Ð¸ÑÐº Ð¾Ð±Ð»Ð°ÑÐ½Ð¾Ð³Ð¾ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
yes=ÐÐ°
no=ÐÐµÑ
areYouSure=ÐÑ ÑÐ²ÐµÑÐµÐ½Ñ?
removeDevice=Ð£Ð´Ð°Ð»Ð¸ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾
gradeComplete=ÐÑÐµÐ½ÐºÐ° Ð·Ð°Ð²ÐµÑÑÐµÐ½Ð°
doesTheDevicePowerUpAndFunctionNormally=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð²ÐºÐ»ÑÑÐ°ÐµÑÑÑ Ð¸ ÑÐ°Ð±Ð¾ÑÐ°ÐµÑ Ð½Ð¾ÑÐ¼Ð°Ð»ÑÐ½Ð¾?
retry=ÐÐ¾Ð²ÑÐ¾ÑÐ¸ÑÑ Ð¿Ð¾Ð¿ÑÑÐºÑ
rawResponse=Ð¡ÑÑÐ¾Ð¹ Ð¾ÑÐ²ÐµÑ
routeError=ÐÑÐ¸Ð±ÐºÐ° Ð¼Ð°ÑÑÑÑÑÐ¸Ð·Ð°ÑÐ¸Ð¸
shopFloorCantBeEstablished=ÐÐµ ÑÐ´Ð°ÐµÑÑÑ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¿ÑÐ¾Ð¸Ð·Ð²Ð¾Ð´ÑÑÐ²Ð¾
profileName=ÐÐ¼Ñ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ
sourceApiError=ÐÑÐ¸Ð±ÐºÐ° Ð¸ÑÑÐ¾Ð´Ð½Ð¾Ð³Ð¾ API
labelApiError=ÐÑÐ¸Ð±ÐºÐ° API ÑÑÐ»ÑÐºÐ¾Ð²
resultsApiError=ÐÑÐ¸Ð±ÐºÐ° API ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð²
labelApiCalling=- ÐÑÐ·Ð¾Ð² API ÑÑÐ»ÑÐºÐ¾Ð²
resultsApiCalling=- ÐÑÐ·Ð¾Ð² API ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð²

tryingNextRestoreAttempt=ÐÐ¾Ð²ÑÐ¾ÑÐ½Ð°Ñ Ð¿Ð¾Ð¿ÑÑÐºÐ° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ, Ð¿Ð¾Ð¿ÑÑÐºÐ°: 
downloadFirmwareTo=ÐÐ°Ð³ÑÑÐ·Ð¸ÑÑ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÑ Ð½Ð°
mDM=MDM
authenticity=ÐÐ¾Ð´Ð»Ð¸Ð½Ð½Ð¾ÑÑÑ
insertSIM=ÐÑÑÐ°Ð²ÑÑÐµ SIM
noUnlockCodeFound=ÐÐ¾Ð´ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
networkUnlockSuccessful=Ð Ð°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° ÑÐµÑÐ¸ ÑÑÐ¿ÐµÑÐ½Ð°
networkUnlockFailed=Ð Ð°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° ÑÐµÑÐ¸ Ð½Ðµ ÑÐ´Ð°Ð»Ð°ÑÑ
failedToUnlock=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°ÑÑ
imeiUnlockCodeNotFound=IMEI/ÐºÐ¾Ð´ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Ð½Ðµ Ð½Ð°Ð¹Ð´ÐµÐ½
deviceAlreadyUnlocked=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ ÑÐ¶Ðµ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½Ð¾
performingNetworkUnlock=ÐÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð¸Ðµ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ ÑÐµÑÐ¸
performNetworkUnlock=Ð Ð°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°ÑÑ Android Ñ Ð¿Ð¾Ð¼Ð¾ÑÑÑ ÐºÐ¾Ð´Ð°
modifiedSoftware=ÐÐ° Ð²Ð°ÑÐµÐ¼ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ðµ Ð¼Ð¾Ð³ÑÑ Ð±ÑÑÑ Ð½ÐµÑÐ°Ð½ÐºÑÐ¸Ð¾Ð½Ð¸ÑÐ¾Ð²Ð°Ð½Ð½ÑÐµ Ð¸Ð·Ð¼ÐµÐ½ÐµÐ½Ð¸Ñ Ð¿ÑÐ¾Ð³ÑÐ°Ð¼Ð¼Ð½Ð¾Ð³Ð¾ Ð¾Ð±ÐµÑÐ¿ÐµÑÐµÐ½Ð¸Ñ
downgradingSoftware=ÐÑ Ð¿ÑÑÐ°ÐµÑÐµÑÑ ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð±Ð¾Ð»ÐµÐµ ÑÐ°Ð½Ð½ÑÑ Ð²ÐµÑÑÐ¸Ñ iOS
mdmOffDevicePrepared=MDM ÐÐ«ÐÐ. Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð³Ð¾ÑÐ¾Ð²Ð¾
mdmOnDeviceNotPrepared=MDM ÐÐÐ. Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð½Ðµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¿Ð¾Ð´Ð³Ð¾ÑÐ¾Ð²Ð¸ÑÑ.
paperType=Ð¢Ð¸Ð¿ Ð±ÑÐ¼Ð°Ð³Ð¸
corrupted=ÐÐ¾Ð²ÑÐµÐ¶Ð´ÐµÐ½Ð¾
extract=ÐÐ·Ð²Ð»ÐµÑÑ
verify=ÐÑÐ¾Ð²ÐµÑÐ¸ÑÑ
verifying=ÐÑÐ¾Ð²ÐµÑÐºÐ°
restartUsbmuxd=ÐÐµÑÐµÐ·Ð°Ð¿ÑÑÑÐ¸ÑÑ usbmuxd
usbmuxdMessage=ÐÑÐºÐ»ÑÑÐ¸ÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð¸ Ð½Ð°Ð¶Ð¼Ð¸ÑÐµ OK, ÑÑÐ¾Ð±Ñ Ð¿ÐµÑÐµÐ·Ð°Ð¿ÑÑÑÐ¸ÑÑ usbmuxd.
oEMParts=OEM ÐÐ°Ð¿ÑÐ°ÑÑÐ¸
adminPass=ÐÐ°ÑÐ¾Ð»Ñ Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ°
eraseOverridden=Ð¡ÑÐµÑÐµÑÑ Ð¿ÐµÑÐµÐ¾Ð¿ÑÐµÐ´ÐµÐ»ÑÐ½Ð½Ð¾Ðµ
eraseStarted=Ð¡ÑÐ¸ÑÐ°Ð½Ð¸Ðµ Ð½Ð°ÑÐ°Ð»Ð¾ÑÑ
printer=Ð¿ÑÐ¸Ð½ÑÐµÑ
waitForFirmwareDownload=ÐÐ¾Ð¶Ð´Ð¸ÑÐµÑÑ Ð¿Ð¾Ð»Ð½Ð¾Ð¹ Ð·Ð°Ð³ÑÑÐ·ÐºÐ¸ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸
downloadingFirmware=ÐÐ°Ð³ÑÑÐ·ÐºÐ° Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸
updatedOs=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð¸Ð¼ÐµÐµÑ Ð¾Ð±Ð½Ð¾Ð²Ð»ÑÐ½Ð½ÑÑ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¾Ð½Ð½ÑÑ ÑÐ¸ÑÑÐµÐ¼Ñ. ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÑÐµÐ±ÑÐµÑÑÑ
deviceOsUpdated=ÐÐ±Ð½Ð¾Ð²Ð»ÐµÐ½Ð½Ð°Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¾Ð½Ð½Ð°Ñ ÑÐ¸ÑÑÐµÐ¼Ð° Ð¿ÑÐ¸ÑÑÑÑÑÐ²ÑÐµÑ
restoreProcessFailedIntermittent=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ðµ Ð½Ðµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð´Ð»Ñ Ð¿Ð¾Ð¿ÑÑÐºÐ¸
esnCheckUsInsuranceBlacklist=ÐÑÐ¾Ð²ÐµÑÑÑÐµ ÑÐµÑÐ½ÑÐ¹ ÑÐ¿Ð¸ÑÐ¾Ðº ÑÑÑÐ°ÑÐ¾Ð²ÑÑ ÐºÐ¾Ð¼Ð¿Ð°Ð½Ð¸Ð¹ Ð¡Ð¨Ð
androidUnlockCodesHeader=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÐºÐ¾Ð´Ð°Ñ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Android
androidUnlockCodesMsg=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿Ð¾Ð´Ð¾Ð¶Ð´Ð¸ÑÐµ, Ð¿Ð¾ÐºÐ° ÐºÐ¾Ð´Ñ ÑÐ°Ð·Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ¸ Android Ð·Ð°Ð³ÑÑÐ¶Ð°ÑÑÑÑ
firmwareAvailable=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð´Ð¾ÑÑÑÐ¿Ð½Ð°
firmwareVerified=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð¿ÑÐ¾Ð²ÐµÑÐµÐ½Ð°/ÑÑÐ¿ÐµÑÐ½Ð¾ Ð·Ð°Ð³ÑÑÐ¶ÐµÐ½Ð°
restoreLicenseExpired=ÐÐ¾ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÑ Ð¸ÑÑÐµÐºÑÑÑ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ñ
exportLocation=ÐÐµÑÑÐ¾ ÑÐºÑÐ¿Ð¾ÑÑÐ°:
autoExportDeviceData=ÐÐ²ÑÐ¾ÑÐºÑÐ¿Ð¾ÑÑ Ð´Ð°Ð½Ð½ÑÑ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
browse=ÐÐ±Ð·Ð¾Ñ
format=Ð¤Ð¾ÑÐ¼Ð°Ñ:
successfulEraseRestore=ÐÑÐ¸ ÑÑÐ¿ÐµÑÐ½Ð¾Ð¼ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ð¸/Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ð¸
onAppResults=ÐÐ¾ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ°Ð¼ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
enableAutoOpenIOSApp=ÐÐºÐ»ÑÑÐ¸ÑÑ Ð°Ð²ÑÐ¾Ð¼Ð°ÑÐ¸ÑÐµÑÐºÐ¾Ðµ Ð¾ÑÐºÑÑÑÐ¸Ðµ iOS-Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ
conformityMark=ÐÐ½Ð°Ðº ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ñ
manualConformityMark=M-ÐÐ½Ð°Ðº ÑÐµÑÐ½Ð¸ÑÐµÑÐºÐ¾Ð³Ð¾ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²Ð¸Ñ
iosDevice=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ IOS
mode=Ð ÐµÐ¶Ð¸Ð¼
defaultMode=Ð ÐµÐ¶Ð¸Ð¼ Ð¿Ð¾ ÑÐ¼Ð¾Ð»ÑÐ°Ð½Ð¸Ñ 
airpodsMode=ÐÐ¸Ð°Ð³Ð½Ð¾ÑÑÐ¸ÐºÐ° AirPods
iWatchMode=ÐÐ¸Ð°Ð³Ð½Ð¾ÑÑÐ¸ÐºÐ° iWatch
preCheckMode=Ð ÐµÐ¶Ð¸Ð¼ Ð¿ÑÐµÐ´Ð²Ð°ÑÐ¸ÑÐµÐ»ÑÐ½Ð¾Ð¹ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸
pinLockColon=ÐÐ»Ð¾ÐºÐ¸ÑÐ¾Ð²ÐºÐ° ÐºÐ¾Ð½ÑÐ°ÐºÑÐ¾Ð² Ð²ÐºÐ»ÑÑÐµÐ½Ð°:
killAdbProcess=Ð£Ð±Ð¸ÑÑ Ð¿ÑÐ¾ÑÐµÑÑ adb
removeDeviceLockAndContinue=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ´Ð°Ð»Ð¸ÑÐµ ÑÑÐµÑÐ½ÑÐµ Ð·Ð°Ð¿Ð¸ÑÐ¸ Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð¸ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð°Ð¹ÑÐµ.
removePinLockAndContinue=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ½Ð¸Ð¼Ð¸ÑÐµ ÑÐ¸ÐºÑÐ°ÑÐ¾Ñ Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð° Ð¸ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð°Ð¹ÑÐµ.
labelWorkFlow=Ð Ð°Ð±Ð¾ÑÐ¸Ð¹ Ð¿ÑÐ¾ÑÐµÑÑ Ð¼ÐµÑÐ¾Ðº
oneFlowForAllTestResults=ÐÐ´Ð¸Ð½ÑÐ¹ Ð¿ÑÐ¾ÑÐµÑÑ Ð´Ð»Ñ Ð²ÑÐµÑ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð² ÑÐµÑÑÐ¾Ð²
separateFlowForTestResultsWorkingOrFailed=ÐÑÐ´ÐµÐ»ÑÐ½ÑÐ¹ Ð¿ÑÐ¾ÑÐµÑÑ Ð´Ð»Ñ ÑÑÐ¿ÐµÑÐ½ÑÑ Ð¸Ð»Ð¸ Ð½ÐµÑÐ´Ð°ÑÐ½ÑÑ ÑÐµÐ·ÑÐ»ÑÑÐ°ÑÐ¾Ð² ÑÐµÑÑÐ¾Ð²
simSerialColon=Ð¡ÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ SIM: 
restoreDelay=ÐÐ°Ð´ÐµÑÐ¶ÐºÐ° Ð²Ð¾ÑÑÑÐ°Ð½Ð¾Ð²Ð»ÐµÐ½Ð¸Ñ
waitForFirmwareExtraction=ÐÐ¾Ð¶Ð´Ð¸ÑÐµÑÑ Ð¿Ð¾Ð»Ð½Ð¾Ð³Ð¾ Ð¸Ð·Ð²Ð»ÐµÑÐµÐ½Ð¸Ñ Ð¿ÑÐ¾ÑÐ¸Ð²ÐºÐ¸
firmwareExtractionReq=ÐÑÐ¾ÑÐ¸Ð²ÐºÐ° Ð¸Ð·Ð²Ð»ÐµÐºÐ°ÐµÑÑÑ
fetchXiaomiAlternateSerial=ÐÐ¾Ð»ÑÑÐ¸ÑÑ Ð°Ð»ÑÑÐµÑÐ½Ð°ÑÐ¸Ð²Ð½ÑÐ¹ ÑÐµÑÐ¸Ð¹Ð½ÑÐ¹ Ð½Ð¾Ð¼ÐµÑ Xiaomi
noUserToken=Ð¢Ð¾ÐºÐµÐ½ Ð½Ðµ Ð±ÑÐ» ÑÐ¾Ð·Ð´Ð°Ð½. Ð¡Ð²ÑÐ¶Ð¸ÑÐµÑÑ Ñ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¾Ð¹.
osNotSupported=ÐÐµÑÑÐ¸Ñ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ð¾Ð½Ð½Ð¾Ð¹ ÑÐ¸ÑÑÐµÐ¼Ñ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑÑÑ
airpodsNotSupportedMessage=ÐÐ»Ñ Ð´Ð¸Ð°Ð³Ð½Ð¾ÑÑÐ¸ÐºÐ¸ AirPods ÑÑÐµÐ±ÑÐµÑÑÑ macOS 14 Ð¸Ð»Ð¸ Ð²ÑÑÐµ. ÐÐ±Ð½Ð¾Ð²Ð¸ÑÐµ Ð²Ð°Ñ ÐºÐ¾Ð¼Ð¿ÑÑÑÐµÑ Ð´Ð¾ macOS Sonoma Ð¸Ð»Ð¸ Ð½Ð¾Ð²ÐµÐµ.
changeTransaction=ÐÐ·Ð¼ÐµÐ½Ð¸ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
selectTransaction=ÐÑÐ±ÑÐ°ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ
move=ÐÐµÑÐµÐ¼ÐµÑÑÐ¸ÑÑ
transactionMovedSuccessfully=Ð¢ÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ ÑÑÐ¿ÐµÑÐ½Ð¾ Ð¿ÐµÑÐµÐ¼ÐµÑÐµÐ½Ð°
cannotMoveTransactionMsg=ÐÐ´ÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¾ÑÑ Ð¸ÑÑÐ¾Ð´Ð½Ð¾Ð¹ Ð¸ ÑÐµÐ»ÐµÐ²Ð¾Ð¹ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ð¹ Ð½Ðµ Ð¼Ð¾Ð³ÑÑ ÑÐ¾Ð²Ð¿Ð°Ð´Ð°ÑÑ
transactionChangeError=ÐÐµ ÑÐ´Ð°Ð»Ð¾ÑÑ Ð¸Ð·Ð¼ÐµÐ½Ð¸ÑÑ ÑÑÐ°Ð½Ð·Ð°ÐºÑÐ¸Ñ Ð´Ð»Ñ ÑÐµÑÐ¸Ð¹Ð½ÑÑ Ð½Ð¾Ð¼ÐµÑÐ¾Ð²: 
icloudLocked=Icloud Ð·Ð°Ð±Ð»Ð¾ÐºÐ¸ÑÐ¾Ð²Ð°Ð½
prepareFailedDueToMdm=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð° Ð¸Ð·-Ð·Ð° MDM
prepareFailedNoCustomization=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÑÑÐµ Ð²ÑÑÑÐ½ÑÑ Ð¸Ð»Ð¸ Ð¾ÑÐ¸ÑÑÐ¸ÑÐµ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾, ÑÑÐ¾Ð±Ñ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ
prepareFailedMsg=ÐÐ¾Ð´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð½Ðµ Ð²ÑÐ¿Ð¾Ð»Ð½ÐµÐ½Ð°
delete=Ð£Ð´Ð°Ð»Ð¸ÑÑ
refreshCache=ÐÐ±Ð½Ð¾Ð²Ð¸ÑÑ ÐºÑÑ
eraseTitle=ÐÐ¾Ð´ÑÐ²ÐµÑÐ´Ð¸ÑÐµ Ð¾Ð¿ÐµÑÐ°ÑÐ¸Ñ ÑÑÐ¸ÑÐ°Ð½Ð¸Ñ
eraseContentText=ÐÑ ÑÐ²ÐµÑÐµÐ½Ñ, ÑÑÐ¾ ÑÐ¾ÑÐ¸ÑÐµ Ð¿ÑÐ¾Ð´Ð¾Ð»Ð¶Ð¸ÑÑ ÑÐ´Ð°Ð»ÐµÐ½Ð¸Ðµ?
ignoreNaForOem=ÐÐ³Ð½Ð¾ÑÐ¸ÑÐ¾Ð²Ð°ÑÑ N/A Ð´Ð»Ñ OEM
recheckBatteryInfo=ÐÐµÑÐµÐ¿ÑÐ¾Ð²ÐµÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ
cloudUnReachable=ÐÐ±Ð»Ð°ÑÐ½ÑÐ¹ ÑÐµÑÐ²Ð¸Ñ Ð½ÐµÐ´Ð¾ÑÑÑÐ¿ÐµÐ½. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÐ²ÑÐ¶Ð¸ÑÐµÑÑ ÑÐ¾ ÑÐ»ÑÐ¶Ð±Ð¾Ð¹ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶ÐºÐ¸.
photoOfDeviceRequired=Ð¢ÑÐµÐ±ÑÐµÑÑÑ ÑÐ¾ÑÐ¾Ð³ÑÐ°ÑÐ¸Ñ ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°
openCamera=ÐÑÐºÑÑÑÑ ÐºÐ°Ð¼ÐµÑÑ
performShopfloor=ÐÑÐ¿Ð¾Ð»Ð½Ð¸ÑÑ Ð¿ÑÐ¾Ð¸Ð·Ð²Ð¾Ð´ÑÑÐ²ÐµÐ½Ð½ÑÐ¹ ÑÐµÑ
readyInPreCheckMode=ÐÐ¾ÑÐ¾Ð² Ð² ÑÐµÐ¶Ð¸Ð¼Ðµ Ð¿ÑÐµÐ´Ð²Ð°ÑÐ¸ÑÐµÐ»ÑÐ½Ð¾Ð¹ Ð¿ÑÐ¾Ð²ÐµÑÐºÐ¸
panicFull=ÐÐ¾Ð»Ð½Ð°Ñ Ð¿Ð°Ð½Ð¸ÐºÐ° Ð²

#PreCheck test labels keys
preCheckAllButton=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ Ð²ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÐ¸
preCheckPower=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÑ Ð¿Ð¸ÑÐ°Ð½Ð¸Ñ
preCheckHome=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÑ Â«ÐÐ¾Ð¼Ð¾Ð¹Â»
preCheckProximity=ÐÑÐ¾Ð²ÐµÐ´Ð¸ÑÐµ ÑÑÐºÐ¾Ð¹ Ð½Ð°Ð´ Ð´Ð°ÑÑÐ¸ÐºÐ¾Ð¼
preCheckRingerOff=ÐÑÐºÐ»ÑÑÐ¸ÑÑ Ð·Ð²Ð¾Ð½Ð¾Ðº
preCheckRingerOn=ÐÐºÐ»ÑÑÐ¸ÑÑ Ð·Ð²Ð¾Ð½Ð¾Ðº
preCheckTouchScreen=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ Ð½Ð° ÑÐºÑÐ°Ð½
preCheckVolumeDown=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÑ ÑÐ¼ÐµÐ½ÑÑÐµÐ½Ð¸Ñ Ð³ÑÐ¾Ð¼ÐºÐ¾ÑÑÐ¸
preCheckVolumeUp=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÑ ÑÐ²ÐµÐ»Ð¸ÑÐµÐ½Ð¸Ñ Ð³ÑÐ¾Ð¼ÐºÐ¾ÑÑÐ¸
lcdColor=Ð­ÐºÑÐ°Ð½ Ð¿ÐµÑÐµÐºÐ»ÑÑÐ°ÐµÑÑÑ Ð¼ÐµÐ¶Ð´Ñ Ð±ÐµÐ»ÑÐ¼ Ð¸ ÑÐµÑÐ½ÑÐ¼?
digitizerTest=ÐÐµÑÐµÐ¼ÐµÑÑÐ¸ÑÐµ Ð·Ð½Ð°ÑÐ¾Ðº
speaker=Ð¢ÐµÑÑ Ð°ÑÐ´Ð¸Ð¾
wifi=WiFi
nfc=NFC
Panic=ÐÑÐ¾Ð²ÐµÑÐºÐ° Ð¿Ð°Ð½Ð¸ÐºÐ¸
alsSensor=ÐÐ°ÑÑÐ¸Ðº ALS
frontIRCamera=Ð¤ÑÐ¾Ð½ÑÐ°Ð»ÑÐ½Ð°Ñ ÐÐ-ÐºÐ°Ð¼ÐµÑÐ°
frontDotProjector=Ð¤ÑÐ¾Ð½ÑÐ°Ð»ÑÐ½ÑÐ¹ ÑÐ¾ÑÐµÑÐ½ÑÐ¹ Ð¿ÑÐ¾ÐµÐºÑÐ¾Ñ
backCameraTele=ÐÐ°Ð´Ð½ÑÑ ÑÐµÐ»ÐµÐ¾Ð±ÑÐµÐºÑÐ¸Ð²Ð½Ð°Ñ ÐºÐ°Ð¼ÐµÑÐ°
backCameraWide=ÐÐ°Ð´Ð½ÑÑ ÑÐ¸ÑÐ¾ÐºÐ¾ÑÐ³Ð¾Ð»ÑÐ½Ð°Ñ ÐºÐ°Ð¼ÐµÑÐ°
appLicenseExpired=Ð¡ÑÐ¾Ðº Ð´ÐµÐ¹ÑÑÐ²Ð¸Ñ Ð»Ð¸ÑÐµÐ½Ð·Ð¸Ð¸ Ð¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ñ Ð¸ÑÑÐµÐº
charging=ÐÐ°ÑÑÐ´ÐºÐ°

dontSupportBypass=Ð£ÑÑÑÐ¾Ð¹ÑÑÐ²Ð¾ Ð½Ðµ Ð¿Ð¾Ð´Ð´ÐµÑÐ¶Ð¸Ð²Ð°ÐµÑ AT Bypass
activateDeviceAdminApp=ÐÐ°Ð¼ Ð½ÐµÐ¾Ð±ÑÐ¾Ð´Ð¸Ð¼Ð¾ Ð°ÐºÑÐ¸Ð²Ð¸ÑÐ¾Ð²Ð°ÑÑ \nÐ¿ÑÐ¸Ð»Ð¾Ð¶ÐµÐ½Ð¸Ðµ Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ° ÑÑÑÑÐ¾Ð¹ÑÑÐ²Ð°. ÐÐ»Ñ \nÑÑÐµÑÐµÑÑ ÑÑÐµÐ±ÑÑÑÑÑ Ð¿ÑÐ°Ð²Ð° Ð°Ð´Ð¼Ð¸Ð½Ð¸ÑÑÑÐ°ÑÐ¾ÑÐ°.
tapOnActivateButton=ÐÐ°Ð¶Ð¼Ð¸ÑÐµ ÐºÐ½Ð¾Ð¿ÐºÑ ÐÐºÑÐ¸Ð²Ð¸ÑÐ¾Ð²Ð°ÑÑ
averageTemperature=Ð¡ÑÐµÐ´Ð½ÑÑ ÑÐµÐ¼Ð¿ÐµÑÐ°ÑÑÑÐ°
cycleCount=ÐÐ¾Ð»Ð¸ÑÐµÑÑÐ²Ð¾ ÑÐ¸ÐºÐ»Ð¾Ð²
eeeeCode=ÐÐ¾Ð´ EEEE
maximumDischargeCurrent=ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½ÑÐ¹ ÑÐ¾Ðº ÑÐ°Ð·ÑÑÐ´Ð°
nominalChargeCapacity=ÐÐ¾Ð¼Ð¸Ð½Ð°Ð»ÑÐ½Ð°Ñ ÑÐ¼ÐºÐ¾ÑÑÑ Ð·Ð°ÑÑÐ´Ð°
serviceOption=ÐÐ¿ÑÐ¸Ñ Ð¾Ð±ÑÐ»ÑÐ¶Ð¸Ð²Ð°Ð½Ð¸Ñ
totalOperatingTime=ÐÐ±ÑÐµÐµ Ð²ÑÐµÐ¼Ñ ÑÐ°Ð±Ð¾ÑÑ
weekMfd=ÐÐµÐ´ÐµÐ»Ñ Ð¿ÑÐ¾Ð¸Ð·Ð²Ð¾Ð´ÑÑÐ²Ð°
batteryServiceFlags=Ð¤Ð»Ð°Ð³Ð¸ Ð¾Ð±ÑÐ»ÑÐ¶Ð¸Ð²Ð°Ð½Ð¸Ñ Ð±Ð°ÑÐ°ÑÐµÐ¸
designCapacity=ÐÑÐ¾ÐµÐºÑÐ½Ð°Ñ ÑÐ¼ÐºÐ¾ÑÑÑ
fetchWatchBatteryInfo=ÐÐ¾Ð»ÑÑÐ¸ÑÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ Ð±Ð°ÑÐ°ÑÐµÐµ ÑÐ°ÑÐ¾Ð²
installProfileOnAppleWatch=ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, ÑÑÑÐ°Ð½Ð¾Ð²Ð¸ÑÐµ Ð¿ÑÐ¾ÑÐ¸Ð»Ñ Ð½Ð° Apple Watch, ÐºÐ¾Ð³Ð´Ð° Ð¿Ð¾ÑÐ²Ð¸ÑÑÑ Ð²ÑÐ¿Ð»ÑÐ²Ð°ÑÑÐµÐµ Ð¾ÐºÐ½Ð¾.
enterYourPassword = ÐÐ²ÐµÐ´Ð¸ÑÐµ Ð²Ð°Ñ Ð¿Ð°ÑÐ¾Ð»Ñ:
authenticationRequired = Ð¢ÑÐµÐ±ÑÐµÑÑÑ Ð°ÑÑÐµÐ½ÑÐ¸ÑÐ¸ÐºÐ°ÑÐ¸Ñ
incorrectPasswordMsg = ÐÐµÐ²ÐµÑÐ½ÑÐ¹ Ð¿Ð°ÑÐ¾Ð»Ñ. ÐÐ¾Ð¶Ð°Ð»ÑÐ¹ÑÑÐ°, Ð¿Ð¾Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐ½Ð¾Ð²Ð°.
preview=ÐÑÐµÐ´Ð¿ÑÐ¾ÑÐ¼Ð¾ÑÑ
batteryDrainInfoCollected=ÐÐ½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ñ Ð¾ ÑÐ°Ð·ÑÑÐ´Ðµ Ð±Ð°ÑÐ°ÑÐµÐ¸ ÑÐ¾Ð±ÑÐ°Ð½Ð°