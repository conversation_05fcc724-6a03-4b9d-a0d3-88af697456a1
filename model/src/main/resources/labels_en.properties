forceAutoExport=Auto export
login.button=Login
username=Username
password=Password
optional=(Optional)
downloading=Downloading
station=Station
tester=Tester
downloadApps=Download Apps
rememberMe=Remember Me
selectAll=Select All
selectNone=Select None
erase=Erase
print=Print
powerOff=Power Off
releaseBox=Release Box
restore=Restore
prepare=Prepare
transaction=Transaction: 
warehouse=Warehouse: 
stationIdColon=Station ID: 
thisHour=This Hour: 
lastHour=Last Hour: 
total=Total: 
erased=Erased: 
passedColon=Passed: 
pendingColon=Pending: 
deviceName=Device Name
localCustomization=Local Customizations
enableDevModeOnIos16=Enable DevMode on iOS 16+
printCustomization=Print Customizations
setDefaultLabelOne=Set default Label 1
setDefaultLabelTwo=Set default Label 2
setPrinterLabelOne=Set printer for Label 1
rollType=Roll Type
tape=Tape
label=Label
dymoTwinTurbo=Dymo Twin Turbo
selectTray=Select Tray
left=Left
right=Right
setPrinterLabelTwo=Set printer for Label 2
firmware=Firmware
saveSettings=Save Settings
search=Search
show=Show:
installed=Installed
available=Available
autoDownloadAsNeeded=Auto downloaded as needed
directoryDir=Download Dir:
whereToDownload=Where to download
change=Change
open=Open
downloadSelected=Download Selected
networkDir=Network Dir:
download=Downloaded
downloadBtn=Download
queued=Queued
failed=Failed
extracting=Extracting
stop=Stop
downloadAgain=Download Again
grade=Grade
carrier=Carrier
color=Color
battery=Battery
lock=Lock
eraseTime=Erase time:
comments=Comments
simTechnologyColon=Sim Technology:
eSimPresentColon=eSim Present:
dualSimColon=Dual Sim:
summary=Summary
deviceDetails=Device Details
diagnostics=Diagnostics
deviceInitialization=Device Initialization
connecting=Connecting
pairingInProgress=Trust the device
pairingFailed=Pairing failed
pairingSuccessful=Pairing Successful
deviceInfoCollected=Device Info Collected
deviceInfoFailed=Failed to get Device Info
deviceImeiCollected=Device IMEI Collected
failedToGetImei=Failed to get IMEI
activationInProgress=Activation in progress
activationFailed=Activation Failed
activationSuccessful=Activation succeeded
activationLockOn=Activation lock is ON
pushWifiProfile=Pushing wifi profile
fetchMdmInfoFailed=Fetch MDM info failed
fetchMdmInfoSuccessful=Fetch MDM info successful
fetchKnoxInfoSuccessful=Fetch Knox info successful
fetchFrpInfoSuccessful=Fetch FRP info successful
fetchBatteryInfoSuccessful=Fetch Battery info successful
fetchBatteryInfoFailed=Fetch Battery info failed
wifiPushSuccessful=Wifi push successful
wifiPushFailed=Wifi push failed
devImageMounting=Dev image mounting
devImageMountingFailed=Dev image mounting failed
devImageMountingSucceeded=Dev image mount succeeded
checkIfAppInstallRequired=App install in progress
appInstallFailed=App install failed
appInstallSuccess=App install success
appAlreadyInstalled=App already installed
configPushFailed=Config push failed
configPushSuccess=Config push success
allSyncableData=â¢ All Syncable Data
batteryApi=â¢ Battery Api
testList=â¢ Test List
config=â¢ Config
failureReasons=â¢ Failure Reasons
gradeConfig=â¢ Grade Config
clientCustomization=â¢ Client Customization
desktopResults=â¢ Desktop Results
ready=Ready
readyHyphen=- Ready
appTestingDone=App testing done
checkingEsn=Checking ESN
esnSuccessful=ESN successful
printing=Printing
eraseInProgress=Erase in progress
eraseSuccessful=Erase Successful
shuttingDownPhone=Shutting down phone
restoreInProgress=Restore in progress
restoreSuccessful=Restore successful
batteryInfo=Battery Info
batteryHealth=Battery Health
batteryChargePercentage=Battery Charge Percentage
batteryCycles=Battery Cycles
designedCapacity=Designed Capacity
currentCapacity=Current Capacity
batterySerialNo=Battery Serial No
batteryModelNo=Battery Model No
batteryResistance=Battery Resistance (R=V/I)
batteryDrainPercent=Battery Drain Percentage
batteryDrainDuration=Battery Drain Duration
batteryDrainResult=Battery Drain Result
touchId=Touch ID
manualTouchIdRequiredMsg=Manual Touch ID required\n\n On device, go to:\n\n   >Settings\n    \
  >Touch ID & Passcode\n     >Add a fingerprint\n      >Tap on touch ID sensor
faceId=Face ID
done=Done
enterLpn=Enter or scan LPN
customOneField=Custom 1 Field
enterCustomOneField=Enter or scan Custom 1
manulFaceIdRequiredMsg=Manual Face ID required\n\n On device, go to:\n\n   >Settings\n    \
  >Face ID & Passcode\n     >Setup Face ID \n      >Get Started
detectFaceIdMsg=Detecting the Face ID status.\n\n Please complete the Face ID enrollment.
appNotInstalledDueRestrictions=App Successfully Pushed but\nnot installed on device due to\nsome restrictions.
appInstallationInstructionsHeading=App Installation Instructions:
appInstallationInstructions=1. Go to downloads \n2. Install android_lite.apk manually\n3. Install android. apk \
  manually
cosmetics=COSMETICS
cancel=Cancel
save=Save
manualBatteryHealthRetrieval=Manual Battery Health Retrieval
deviceNavigationForBattery=On this device, you need to navigate\nto the Battery Health & Charging\nsettings within the \
  settings app.
navigateToBatterySettings=1  Navigate to the\n    Battery Settings
navigateToBatteryHealthCharging=2  Navigate to Battery\n    Health & Charging
close=Close
fail=FAIL
restoreError=Restore Error
ok=OK
reconnectDevice=Reconnect Device
error=Error: 
pushWifi=Push Wifi
installApp=Install App
pushFiles=Push Files
syncResults=Sync Results
failDevice=Fail Device
restoreDevice=Restore Device
copyInfoToAllDevices=Copy info to all devices
os=OS
mdm=MDM
imei=IMEI
oem=OEM
skuCode=SKU Code
lpn=LPN
customOne=Custom1
jailbreakRoot=Jailbreak/Root
manualEntry=Manual Entry
serialIdOrImeiRequired=Serial or IMEI is required to add a manual entry
serialNumber=Serial Number
submit=Submit
printLabel=Print Label
edit=Edit
part=Part
currentSerial=Current Serial
factorySerial=Factory Serial
status=Status
notice=Notice
portMapping=Port Mapping
hub=Hub
next=Next
finishMapping=Finish Mapping
remap=Remap
enterVendorInvoiceInfo=Enter Vendor Name and Invoice No.
vendorName=Vendor Name
invoiceNo=Invoice No.
qty=QTY
boxNo=Box No.
transactionDetails=Transaction Details
continueTransaction=â¢â¢â¢ Continue Transaction
currentTransaction=Current Transaction
bestBuy=BestBuy
invoice=Invoice # 
box=Box-
qtyFormatted=â¢ QTY-
of=of
details=Details
exportDetails=Export Details
transactionHistory=Transaction History
name=Name
deviceLockLabel=Device Lock
notes=Notes
snColon=S/N:
sn=S/N
imeiColon=IMEI: 
eraseColon=Erase:
title=Title
modelNo=MODEL NO. 
customElement=Custom Element
serialNo=SERIAL NO. 
serial=Serial
gradeColon=Grade : 
colorColon=COLOR : 
batteryColon=BATTERY : 
bbc=BCC: 
versionColon=VERSION : 
version=Version
firmNetworkColon=Firm/Network : 
firmNetwork=Firm/Network
esnColon=ESN : 
esn=ESN
fmiFrp=FMI/FRP : 
fmiFrpStatus=FMI/FRPStatus
cosmeticsColon:COSMETICS: 
erasureColon:ERASURE: 
oemParts:OEM Parts : 
functionality=FUNCTIONALITY : 
seeNotes=See Notes
customOneColon:CUSTOM 1 : 
custom=custom
port=PORT : 
userColon=USER : 
user=user
dateColon=DATE : 
vendorColon=VENDOR : 
vendor=Vendor
fullFuctionalOrSeeNotes=Full Functional or See Notes
lpnColon=LPN: 
jailbreakRooted=Jailbreak/Rooted : 
jailbreak=Jailbreak
deviceOffline=Device offline. Reconnect
make=Make:  
erasing=Erasing: 
eraseSuccess=Erase Successful:
recoveryMode=Recovery Mode
verifyingFirmware=Verifying firmware
restoreCompleted=Restore Completed
deviceDisconnected=Device disconnected
restoreFailed=Restore Failed
startDeviceTesting=Start testing on device
appInstallFailedManualInstallReqd=App not installed, manually install app
pairingFailedReconnect=Failed pairing, please reconnect!
passed=Passed
eraseRestricted=Erase restricted
printRestricted=Print restricted
deviceOperations=Device Operations
esnCheck=ESN Check
esnCheckBlacklist=ESN Check Blacklist
esnCheckAll=ESN Check All
provisionOrQRPrint=Provision Qr Print 
settings=Settings
openCustomizations=Open Customizations
openCloudCustomizations=Open Cloud Customizations
about=About
quit=Quit 
actions=Actions
view=View
appearance=Appearance
transactions=Transactions
newTransaction=New Transaction
viewTransaction=View Transaction
selectPackage=Select Package
updateVendorInfo=Update Vendor Info
help=Help
readyInAtMode=Ready in AT mode
deviceInfoCollectionFailed=Device info collection failed
imeiCollectionFailed=IMEI collection failed
batteryInfoCollected=Battery info collected
batteryInfoCollectionFailed=Battery info collection failed
oemDataCollected=OEM data collected
oemDataCollectionFailed=OEM data collection failed
icloudInfoCollected=Icloud info Collected
simInfoCollected=Sim Lock info Collected
carrierLockInfoCollected=Carrier Lock info Collected
peoInitialized=Peo initialized
startingTests=Starting tests
testStartedSuccessfully=Tests started successfully
startTestsFailed=Start tests failed
fetchingMdm=Fetching MDM info
mdmSucceeded=Fetch MDM info successful
mdmFailed=Fetch MDM info failed
testConfigInProgress=Pushing test config
testConfigPushSuccess=Test config push success
testConfigFailed=Test config push failed
eraseFailed=Erase failed
enableDevMode=Enabling dev mode
devModeEnabled=Dev mode enabled
devModeEnableFailed=Dev mode enable failed
initiatePeo=Initiating PEO
preparingDevice=Preparing device
prepareDeviceFailed=Prepare device failed
prepareDeviceSuccess=Prepare device successful
prepareFailedTryManually=Prepare failed, try manually
esnCheckSuccessful=ESN check successful
uninstallAppSuccess=Uninstall app success
appNotInstalled=App not installed
printRequestSent=Print request sent
notReady=Not Ready
editTransaction=Edit Transaction
frpInfoCollected=FRP info collected
meidInfoCollected=MEID info collected
pcUtilityInfoCollected=PC Utility info collected
knoxInfoCollected=Knox info collected
initializing=Initializingâ¦
eraseRequested=Erase Requested
preparing=Preparing
preparingFailedReconnect=Preparing failed, reconnect
imeiNotFound=IMEI Not Found
readyStartTesting=Ready, start testing on device
deviceDisconnectedDuringRestore=Device disconnected during restore
deviceDisconnectedDuringErase=Device disconnected during erase
completeRequiredFields=Please complete required fields
eraseRequestSent=Erase request sent
pending=Pending
defect=_Defect
portMappingInstructionStep1=Setup port mapping to assign specific USB ports with a port number.\n \
  In the initial setup, we recommend using an Apple device with genuine or MFI certified cable.\n \
  After port mapping, you can plug in any cable your device requires.
portMappingInstructionStep2=Begin port mapping by connecting the cable from your device into the first USB port. \
  The illustration below will change to green when you have successfully mapped the USB port.
portMappingInstructionStep3=Port mapping already exists. To remap please click the remap button below
noDeviceRecordTransaction=No device record available for this transaction
eraseLicenseExpired=Erase license expired
eraseNotPermitted=Erase not permitted
eraseFailedEcidMissing=Erase failed, ECID missing
eraseStoppedDeviceDisconnected=Erase Stopped, Device Disconnected
eraseStoppedTryAgain=Erase Stopped, Try Again
erasedWaitingForBootup=Erased, waiting for bootup
acceptDevModeOnDevice=Accept Dev mode & reconnect!
oemPartsDetails=OEM PARTS DETAILS
newTransactionCreated=New Transaction Created
dmgAvailable=DMG For Updated Version Available
transactionContinued=Transaction Continued
failedToContinueTransaction=Failed to continue transaction
updateCloudCustomizations=Update Cloud Customizations
noManualEntryAvailable=No manual entry record available
carrierSimLock=SimLock infoCarrier
simLockInfo=SimLock info
esnInfo=ESN Info
firmwareNotAvailable=Firmware not available for the model
firmwareDownloadRequested=Firmware download is requested for the model
unableToDownloadFirmware=Unable to download firmware for the device model
restoreInitiated=Restore process initiated for the device
firmwareDownloadFailed=Firmware download/extraction failed for the model
waitForFirmwareVerification=Wait until firmware gets verified completely
firmwareDownloadStopped=Firmware is stopped by the user for the model
restoreStarted=Device restore process started
restoreProcessFailed=Restore process failed for the device
dfuPushFailed=Device is not in recovery. Please push into recovery manually and retry
dfuPushSuccess=Device moved to the recovery mode
restoreFailedToCreateStateMachine=Failed to create new state machine for restore, Bring device to recover Mode Manually
unableToUpdateDevice=Unable to update device because it's passcode locked and the device wasn't unlocked for the update or restore.\
  \ Please Unlock device
firmwareAuthorizationFailed=Declined to authorize this firmware on this device. Download Latest Firmware
firmwareExtractionField=Firmware is not properly extracted. Please Extract firmware
firmwareUpdaterReqFailed=Failed to handle firmware updater request
restoreFailedWithErrorCode=Restore failed with error. Check logs for the error code
restoreFailedWithUnknownErr=Device could not be restored, unknown error occurred (check error code in logs)
restoreFailedTimeout=Restore process timeout, No response from device
firmwareFileCorrupted=Restore Stopped corrupt file detected. Download firmware manually
dfuState=DFU
preparingState=P / Preparing device for restore
recoveryModeState=RM
appleLogoState=AL / Preparing device for restore
lostState=LT / Preparing device for restore
muxState=MX / Preparing device for restore
sendingPayload=SP / Restoring software
copyingFirmware=Copying Firmware
restoreCompleteState=RC
verifyingRestore=VR / Restoring software
flashingFirmware=FF / Restoring software
kernelCache=KC / Restoring software
flashingKernelCache=FKC / Restoring software
baseBand=BB / Restoring software
noSpaceLeftOnDevice=No Space left on your device
restoreFailedInRecoveryMode=Cannot restore device in recovery mode, Please follow device reset procedure specific to device
serverNotReachable=Server not reachable, Please check your internet and try again
restoreFailedTryAgain=Restore Failed, Please try again
restoreFailedCableIssue=Restored failed with hardware error. Check USB port, cable or computer.
restoreAttemptExhausted=Restore Failed. Maximum attempt exhausted
waitForHelloScreen=Waiting for device to resume to hello screen
deviceRestoreCompleted=Device restored completely
deviceRebooting=Device taking time to reboot
deviceNotFoundInConnection=Restore completed but device not found in connected devices. Please reconnect.
transactionId=Transaction Id
transactionDate=Created
portNumber=Port No.
model=Model
imei2=IMEI2
countryOfOrigin=Country Of Origin
regulatoryModelNumber=Regulatory Model No.
memory=GB
ram=Ram
initialCarrier=Initial Carrier
pEsn=Pseudo ESN
pEsn2=Pseudo ESN2
meid=MEID
meid2=MEID2
decimalMeid=Decimal MEID
decimalMeid2=Decimal MEID2
eid=EID
simTechnology=Sim Technology
simSerial=Sim Serial
simSerial2=Sim Serial2
wifiMacAddress=Wifi Mac Address
eSimPresent=eSim Present
compatibleSim=Compatible Sim
notCompatibleSim=Not Compatible Sim
network=Network
networkDetails=Network Details
deviceLock=FMI / FRP
mdmResponse=MDM Response
knox=Knox
rooted=Rooted
appleId=Apple Id
batteryHealthPercentage=Battery Health Percentage
oemBatteryHealth=OEM Battery Health
cocoBatteryHealth=Coco Battery Health
batteryCycle=Battery Cycles Count
batteryCurrentMaxCapacity=Battery Current Capacity
cocoCurrentCapacity=Coco Current Capacity
batteryDesignMaxCapacity=Battery Design Capacity
cocoDesignCapacity=Coco Design Capacity
batterySerial=Battery Serial
batteryModel=Battery Model
batterySource=Battery Source
batteryTemperature=Battery Temperature
batteryDrainType=Battery Drain Type
startHeat=Start Heat
endHeat=End Heat
batteryChargeStart=Battery Charge Start
batteryChargeEnd=Battery Charge End
batteryDrain=Battery Drain
startBatteryCharge=Start Battery Charge
endBatteryCharge=End Battery Charge
totalBatteryDrain=Total Battery Drain
batteryDrainInfo=Battery Drain Info
batteryHealthGrade=Battery Health Grade
deviceCreatedDate=Date Added
deviceUpdatedDate=Date Updated
testPlanName=Test Plan Name
working=Working
defectsCode=Defects Code
manualFailure=Manual Failure
simErased=Sim Erased
eSimErased=eSim Erased
type=Erased Type
startTime=Erase Start Time
endTime=Erase End Time
eraserDiff=Erase Total Time
erasedNotes=Erase Notes
restoreCode=Restore Code
deviceState=Device State
productCode=Product Code
bMic=BMic
vMic=VMic
fMic=FMic
simLock=Sim Lock
unlockStatus=Unlock Status
simLockResponse=Sim Lock Response
carrierLockResponse=Carrier Lock Response
apiResponse=API Response
errorCode=Package Name
screenTime=Screen Time
stationId=User Id
testerName=Tester Name
licenseId=License Id
deviceShutdown=FCCID
oemStatus=OEM Status
parts=OEMPart
iftCodes=IFT Codes
preCheckWorking=Pre Check Working
preCheckFailed=Pre Check Failed
preCheckPassed=Pre Check Passed
preCheckPending=Pre Check Pending
transactionType=Transaction Type
eBayRefurbished=eBay Refurbished
eBayRejection=eBay Rejection
dataVerification=Data Verification
genuine=Genuine
notGenuine=Not Genuine
na=N/A
mainBoard=Main Board
frontCamera=Front Camera
backCamera=Back Camera
lcd=Lcd
downloadingUpdates=Downloading Updates
build=Build
invalidUserNameAndPwd=Please enter a valid Username and password
simlockLicenseExpired=Simlock license expired
simlockCheckError=Simlock check error
deviceEcidMissing=Device ECID is missing
checkInternetConnection=Please check your Internet connection
esnLicenseExpired=ESN check license expired
esnCheckFailed=ESN check failed
failedToFetchDeviceLicenses=Failed to fetch device licenses
deviceEraseLicenseMasterDateNotStarted=failed because the master date has not started yet
deviceEraseLicenseMasterExpired=failed because the master is expired
deviceEraseLicenseUserDateNotStarted=failed because the user date has not started yet
deviceEraseLicenseUserExpired=failed because this user is expired
deviceConnectionLicenseDeviceEraseLicenseLicenseExpired=failed because device License Expired
deviceConnectionLicenseMasterDateNotStarted=failed because the master date has not started yet
deviceConnectionLicenseMasterExpired=failed because the master is expired
deviceConnectionLicenseUserDateNotStarted=failed because the user date has not started yet
deviceConnectionLicenseUserExpired=failed because this user is expired
deviceConnectionLicenseLicenseExpired=failed because device License Expired
powerOffSuccess=Power off success
powerOffFailedNoDevice=Power off failed, no device found
powerOffFailedUnknownErr=Power off failed, unknown reason
uninstallAppFailed=Uninstall app failed
uninstallProfileFailed=Uninstall profile failed
uninstallAppProfileFailed=Uninstall app & profile failed
failedToGetInfoReconnect=Failed to get info. Reconnect!
cantFindDeviceReconnect=Cant find device, reconnect!
noTestResultYet=No test results yet
errorOccurredInPeo=Error occurred in PEO
errorInStartingPhonecheck=Error occurred while starting Phonecheck. Application is closing in %d
mqttClientNotConnected=MQTT client is not connected to broker.\s Possible reasons:\s 1- Daemon Application not runnings\
  2- Error occurred while connecting to MQTT brokers \s Application is closing in %d
serialColon=Serial:
lidarSerialColon=Lidar Serial: 
syncFailed=Sync Failed
prepareFailed=Prepare Failed
restartingForNotices=Restarting for notices
deviceInUsbMode=Device in USB mode
appInstallFailedHyphen=- App install failed
connectingHyphen=- Connecting
pairingInProgressHyphen=- Pairing in progress
pairingFailedHyphen=- Pairing failed
pairingSuccessfulHyphen=- Pairing Successful
deviceInfoCollectedHyphen=- Device Info Collected
deviceInfoFailedHyphen=- Failed to get Device Info
deviceImeiCollectedHyphen=- Device IMEI Collected
failedToGetImeiHyphen=- Failed to get IMEI
activationInProgressHyphen=- Activation in progress
activationFailedHyphen=- Activation Failed
activationSuccessfulHyphen=- Activation succeeded
activationLockOnHyphen=- Activation lock is ON
pushWifiProfileHyphen=- Pushing wifi profile
fetchMdmInfoFailedHyphen=- Fetch MDM info failed
fetchMdmInfoSuccessfulHyphen=- Fetch MDM info successful
fetchKnoxInfoSuccessfulHyphen=- Fetch Knox info successful
fetchFrpInfoSuccessfulHyphen=- Fetch FRP info successful
fetchBatteryInfoSuccessfulHyphen=- Fetch Battery info successful
fetchBatteryInfoFailedHyphen=- Fetch Battery info failed
wifiPushSuccessfulHyphen=- Wifi push successful
wifiPushFailedHyphen=- Wifi push failed
devImageMountingHyphen=- Dev image mounting
devImageMountingFailedHyphen=- Dev image mounting failed
devImageMountingSucceededHyphen=- Dev image mount succeeded
checkIfAppInstallRequiredHyphen=- App install in progress
appInstallSuccessHyphen=- App install success
appAlreadyInstalledHyphen=- App already installed
configPushInProgressHyphen=- Config push in progress
configPushFailedHyphen=- Config push failed
configPushSuccessHyphen=- Config push success
appTestingDoneHyphen=- App testing done
checkingEsnHyphen=- Checking ESN
esnSuccessfulHyphen=- ESN successful
printingHyphen=- Printing
eraseInProgressHyphen=- Erase in progress
eraseSuccessfulHyphen=- Erase Successful
shuttingDownPhoneHyphen=- Shutting down phone
restoreInProgressHyphen=- Restore in progress
restoreSuccessfulHyphen=- Restore successful
clickOkAfterUpdates=Click <b>OK</b> once you've updated the customizations on the browser.
browserActionNotSupported1=Could not open the customizations on the browser, browse action not supported. Go to 
browserActionNotSupported2=and update your assigned profile.
vendorInformation=Vendor Information
dataMissing=Data missing
manualPEO=Manual PEO
simCarrierColon=Sim Carrier:
customizations=Customizations
labelPrinting=Label Printing
labelOne=Label 1
labelTwo=Label 2
printerOneForLabelOne=Printer 1 for Label 1
printerTwoForLabelTwo=Printer 2 for Label 2
printToPdf=Print to PDF
pleaseSelect=Please Select
none=None
restoring=Restoring: 
pairTheDeviceAgain=Pair the device again
carrierLock=Carrier Lock
exportLog=Export Logs
sourceApiCalling=- Source Api Calling
reProcessDeviceAction=Reprocess Device Action
previouslyProcessedDevice=This device was previously processed
rePrintLabel=Reprint Label
reProcessDevice=Reprocess Device
eraseForMdm=Erasing for MDM
mdmErasedWaitingForBootup=MDM Erased, Waiting for bootup
pushWiFi=Push Wi-Fi
appInstall=App Install
removeAppAndProfiles=Remove App & Profiles
phonecheckIsStarting=Phonecheck is startingâ¦
updateAvailable=Update available
newVersionAvailable=There is a new version of Phonecheck available.
likeToUpdateNow=Would you like to update now?
updateNow=Update Now
later=Later
installingUpdate=Installing Update
restoreSuccess=Restore Success:
queuedForRestore=Device is queued for restore
restoreNeeded=Restore needed for the device
eraseFailedAppNotInstalled=Erase failed, App not installed
eraseFailedSerialPortClosed=Erase failed, Serial Port can not be opened
simCardError=Sim Card Detected
simCardErrorMsg=Remove sim card to continue
skip=Skip
removeSimCardMsg=Remove sim-card and initiate erase again
warningSkip=Sending erase request post skip
simCardWarnMsg=Sim-card is present in the device
sdCardError=SD Card Detected
sdCardErrorMsg=Remove SD card to continue
removeSdCardMsg=Remove SD-card and initiate erase again
sdCardWarnMsg=SD-card is present in the device
restoreThreads=Restore thread count:
restoreReqSent=Restore request sent
headsetPort=Headset Port
headsetLeft=Headset-Left
headsetRight=Headset-Right
audioInput=Audio Input
autoLS=Auto LS
loudSpeaker=Loud Speaker
loudSpeakerM=Loud Speaker-M
micLSTest=Mic LS Test
microphone=Microphone
videoMicrophone=Video Microphone
headsetLightPort=Headset Light_Port
bottomMicQuality=Bottom Mic Quality
earpieceQuality=Earpiece Quality
frontMicQuality=Front Mic Quality
microphoneQuality=Microphone Quality
rearMicQuality=Rear Mic Quality
recordingQuality=Recording Quality
videoMicrophoneQuality=Video Microphone Quality
bottomMicPB=Bottom Mic PB
ESPlayback=ES Playback
frontMicPB=Front Mic PB
manualRecording=Manual Recording
micPlayback=Mic Playback
microphonePB=Microphone PB
rearMicPB=Rear Mic PB
videoMicPB=Video Mic PB
manualAudioQuality=Manual Audio Quality
manualRingtoneQuality=Manual Ringtone Quality
sPen=SPen
sPenBackButton=SPen Back Button
sPenHover=SPen Hover
sPenMenuButton=SPen Menu Button
sPenRemove=SPen Remove
spenPlusButtons=Spen Plus buttons
backButton=Back Button
buttonsTest=Buttons Test
flipSwitch=Flip Switch
homeButton=Home Button
menuButton=Menu Button
powerButton=Power Button
volumeDownButton=Volume Down Button
volumeUpButton=Volume Up Button
sDCardDetect=SD Card Detect
sDCardRemove=SD Card Remove
alertSlider=Alert Slider
bixbyButton=Bixby Button
autoSnapFront=AutoSnapFront
cameraTest=Camera Test
frontCameraQuality=Front Camera
frontVideoCamera=Front Video Camera
autoSnapRear=AutoSnapRear
cameraAutoFocus=Camera AutoFocus
flash=Flash
flashlight=Flashlight
rearCamera=Rear Camera
rearCameraQuality=Rear Camera Quality
rearVideoCamera=Rear Video Camera
ultraWideCamera=UltraWide Camera
ultraWideCameraQuality=UltraWide Camera Quality
telephotoCamera=Telephoto Camera
telephotoCameraQuality=Telephoto Camera Quality
rearCamtoGallery=Rear Cam to Gallery
barcodeScan=Barcode Scan
imageCaptureTime=Image Capture Time
manualCameraTest=Manual Camera Test
manualCameras=Manual Cameras
qRTest=QR Test
fingerprintSensor=Fingerprint Sensor
manualVibration=Manual Vibration
proximitySensor=Proximity Sensor
vibration=Vibration
accelerometer=Accelerometer
autoAccelerometer=Auto Accelerometer
gyroscope=Gyroscope
screenRotation=Screen Rotation
barometer=Barometer
compass=Compass
nonSamsungFingerprint=Non Samsung FingerPrint
samsungFingerprint=Samsung FingerPrint
3DTouch=3D Touch
digitizer=Digitizer
edgeScreen=Edge Screen
fingerTrailDigitizer=FingerTrail Digitizer
forceTouch=Force Touch
glassCondition=Glass Condition
glassCracked=Glass Cracked
lCD=LCD
autoQRCode=Auto QR Code
digitizerNPattern=Digitizer N Pattern
manualQRCode=Manual QR Code
multiTouchTest=Multi Touch Test
multiTouch=MultiTouch
samDigi=Sam Digi
brightness=Brightness
customTests=Custom Tests
sWVersion=SW Version
IMEIScreenshot=IMEI Screenshot
callTest=Call Test
networkConnectivity=Network Connectivity
simReader=Sim Reader
bluetooth=Bluetooth
WiFi=WiFi
wifi=WiFi
dualCallTest=Dual Call Test
networkConnectivity1=Network Connectivity 1
networkConnectivity2=Network Connectivity 2
simReader1=Sim Reader 1
simReader2=Sim Reader 2
enhancedBluetooth=Enhanced Bluetooth
gPS=GPS
nFC=NFC
dualSimReaderTest=Dual Sim Reader Test
simReaderKey=Sim Reader Key
simReaderTest=Sim Reader Test
simRemove=Sim Remove
simRemoveKey=Sim Remove Key
chargePort=Chargeport
wirelessCharging=Wireless Charging
colors=Colors
grading=Grading
housing=Housing
earSpeaker=Ear Speaker
frontCameraFocus=Front Camera Focus
rearCameraFocus=Rear Camera Focus
frontMicrophone=Front Microphone
headsetMediaButton=Headset Media Button
headsetVolumeDown=Headset Volume Down
headsetVolumeUp=Headset Volume Up
lightSensor=Light Sensor
loudspeakerQuality=Loud Speaker Quality
micRecording=Mic Recording
batteryWarning=Battery Warning
faceID=Face ID
earpiece=Earpiece
validateImei=Scan IMEI/Serial#
sdCardDetected=SD-Card Detected
simCardDetected=Sim-Card Detected
refreshCustomizations=Customizations are not retrieved. Please Click <b>Refresh</b>.
refresh=Refresh
refreshCloudCustomizations=Refresh Cloud Customizations
dataFound=Data found on device
dataNotFound=No data found on device
bHA=BH-A
bHB=BH-B
bHC=BH-C
bHD=BH-D
iMEI/SerialMismatch=IMEI/SerialMismatch
cloudDeviceLookup=Cloud Device Lookup
yes=Yes
no=No
areYouSure=Are you sure?
removeDevice=Remove Device
gradeComplete=Grade Complete
doesTheDevicePowerUpAndFunctionNormally=Does the device power up and function normally ?
retry=Retry
rawResponse=Raw Response
routeError=Route Error
shopFloorCantBeEstablished=ShopFloorCantBeEstablished
profileName=Profile Name
sourceApiError=Source Api - Error
labelApiError=Labels Api - Error
resultsApiError=Results Api - Error
labelApiCalling=- Label API Calling
resultsApiCalling=- Results API Calling
tryingNextRestoreAttempt=Retrying restore with attempt: 
downloadFirmwareTo=Download Firmware to
mDM=MDM
authenticity=Authenticity
insertSIM=Insert SIM
noUnlockCodeFound=No Unlock Code found
networkUnlockSuccessful=Network Unlock Successful
networkUnlockFailed=Network unlock failed
failedToUnlock=Failed to Unlock
imeiUnlockCodeNotFound=IMEI/Unlock Code not found
deviceAlreadyUnlocked=Device already unlocked
performingNetworkUnlock=Performing network unlock
performNetworkUnlock=Android Unlock with Code
modifiedSoftware=Your device might have unauthorised software modifications
downgradingSoftware=You may be trying to install an earlier version of iOS
mdmOffDevicePrepared=MDM OFF, Device is prepared
mdmOnDeviceNotPrepared=MDM ON, Device failed to prepare
paperType=Paper Type
corrupted=Corrupted
extract=Extract
verify=Verify
verifying=Verifying
restartUsbmuxd=Restart usbmuxd
usbmuxdMessage=Disconnect devices and click OK to restart usbmuxd
oEMParts=OEM Parts
adminPass=Admin password
eraseOverridden=Erase overridden
eraseStarted=Erase Started
printer=printer
waitForFirmwareDownload=Wait until firmware gets downloaded completely
downloadingFirmware=Downloading firmware
updatedOs=Device has updated OS. No need of restore
deviceOsUpdated=Updated OS present
restoreProcessFailedIntermittent=Restore failed for the attempt 
esnCheckUsInsuranceBlacklist=Check US Insurance BlackList
androidUnlockCodesHeader=Android Unlock Codes Information
androidUnlockCodesMsg=Please wait while the android unlock codes are being downloaded
firmwareAvailable=Firmware Available
firmwareVerified=Firmware verified/downloaded successfully
restoreLicenseExpired=Restore license expired
exportLocation=Export location:
autoExportDeviceData=Auto export device data
browse=Browse
format=Format:
successfulEraseRestore=On successful erase/restore
onAppResults=On App Results
enableAutoOpenIOSApp=Enable Auto Open IOS App
conformityMark=Technical Conformity Mark
manualConformityMark=M-Technical Conformity Mark
iosDevice=IOS Device
mode=Mode
defaultMode=Default Mode
airpodsMode=Airpod Diagnostics
iWatchMode=IWatch Diagnostics
preCheckMode=PreCheck Mode
pinLockColon=Pin Lock Enabled:
killAdbProcess=Kill adb process
removeDeviceLockAndContinue=Please remove accounts from device and continue
removePinLockAndContinue=Please remove pin lock from device and continue
labelWorkFlow=Label Workflow
oneFlowForAllTestResults=One flow for all test results
separateFlowForTestResultsWorkingOrFailed=Separate flow for test results working/failed
simSerialColon=Sim Serial: 
restoreDelay=Restore Delay
waitForFirmwareExtraction=Wait until firmware gets extracted completely
firmwareExtractionReq=Firmware Being Extracted
fetchXiaomiAlternateSerial=Fetch Xiaomi Alternate Serial
noUserToken=Token not generated. Contact support.
osNotSupported=OS Version Not supported
airpodsNotSupportedMessage=Airpods Diagnostics needs OS X 14 or higher to run. Please update your machine to OS X Sonoma or newer
changeTransaction=Change Transaction
selectTransaction=Select Transaction
move=Move
transactionMovedSuccessfully=Transaction moved successfully
cannotMoveTransactionMsg=Source and destination transaction id cannot be same
transactionChangeError=Transaction change failed for serials : 
icloudLocked=iCloud locked
prepareFailedDueToMdm=Prepare failed due to MDM
prepareFailedMsg=Prepare Failed
prepareFailedNoCustomization=Prepare manually or erase device to continue
delete=Delete
refreshCache=Refresh Cache
eraseTitle=Confirm Erase Operation
eraseContentText=Are you sure you want to proceed with the erase?
ignoreNaForOem=Ignore N/A For OEM
recheckBatteryInfo=Recheck Battery Info
cloudUnReachable=Cloud service unavailable. Please contact support.
performShopfloor=Perform Shopfloor
photoOfDeviceRequired=Photo of Device Required
openCamera=Open Camera
readyInPreCheckMode=Ready in PreCheck mode
panicFull=Panic Full

#PreCheck test labels keys
preCheckAllButton=Press All Buttons
preCheckPower=Press Power Button
preCheckHome=Press Home Button
preCheckProximity=Wave Over Sensor
preCheckRingerOff=Switch Ringer Off
preCheckRingerOn=Switch Ringer On
preCheckTouchScreen=Tap On Screen
preCheckVolumeDown=Press Volume Down
preCheckVolumeUp=Press Volume Up
lcdColor=Screen cycling between White and Black?
digitizerTest=Move the icon
speaker=Audio Test
appLicenseExpired=App License Expired
charging=Charging

dontSupportBypass=Device don't support AT Bypass
activateDeviceAdminApp=You need to Activate Device \nAdmin App. Admin rights \nrequired to perform Erase.
tapOnActivateButton=Tap on Activate button
averageTemperature=Average Temperature
cycleCount=Cycle Count
eeeeCode=EEEE Code
maximumDischargeCurrent=Maximum Discharge Current
nominalChargeCapacity=Nominal Charge Capacity
serviceOption=Service Option
totalOperatingTime=Total Operating Time
weekMfd=Week Manufactured
batteryServiceFlags=Battery Service Flags
designCapacity=Design Capacity
fetchWatchBatteryInfo=Fetch Watch Battery Info
installProfileOnAppleWatch=Please install profile on Apple Watch when popup appears.
enterYourPassword = Enter your password:
authenticationRequired = Authetication Required
incorrectPasswordMsg = Incorrect password. Please try again.
preview=Preview
batteryDrainInfoCollected=Battery drain Info collected