package com.phonecheck.app.android;

import com.phonecheck.app.android.util.InstallAppUtil;
import com.phonecheck.command.device.android.app.*;
import com.phonecheck.command.device.android.files.AndroidPushFileCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.AndroidDevice;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidAppServiceTest {
    @InjectMocks
    private AndroidAppService appInstallService;
    @Mock
    private CommandExecutor executor;
    @Mock
    private InstallAppUtil installAppUtil;

    @Test
    public void testPushApkFileToDeviceSuccess() throws IOException {
        String deviceId = "device123";
        String filePath = "/path/to/apk";
        String appName = "test.apk";
        boolean pushFilesToDownloads = false;

        when(executor.execute(any(AndroidPushFileCommand.class))).
                thenReturn("file pushed successfully");

        boolean result = appInstallService.pushApkFileToDevice(deviceId, filePath, pushFilesToDownloads, appName);

        assertTrue(result);
        verify(executor).execute(any(AndroidPushFileCommand.class));
    }


    @Test
    public void testPushApkFileToDeviceFailure() throws IOException {
        String deviceId = "device123";
        String filePath = "/path/to/apk";
        boolean pushFilesToDownloads = false;
        String fileName = "file.apk";
        when(executor.execute(any(AndroidPushFileCommand.class))).thenReturn("file push failed with error");

        boolean result = appInstallService.pushApkFileToDevice(deviceId, filePath, pushFilesToDownloads, fileName);

        assertFalse(result);
        verify(executor).execute(any(AndroidPushFileCommand.class));
    }

    @Test
    public void testInstallApplicationSuccess() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device123");
        String appName = "test.apk";

        when(installAppUtil.shouldAddAbiWithCommand(device)).thenReturn(true);
        when(executor.execute(any(AndroidInstallAppCommand.class))).thenReturn("Installation successful");

        boolean result = appInstallService.installApplication(device, appName);

        assertTrue(result);
        verify(executor).execute(any(AndroidInstallAppCommand.class));
    }

    @Test
    public void testInstallApplicationFailure() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device123");
        String appName = "test.apk";

        when(installAppUtil.shouldAddAbiWithCommand(device)).thenReturn(false);
        when(executor.execute(any(AndroidInstallAppCommand.class))).thenReturn("Installation failed");

        boolean result = appInstallService.installApplication(device, appName);

        assertFalse(result);
        verify(executor).execute(any(AndroidInstallAppCommand.class));
    }

    @Test
    public void testGivePermissionsToApplicationSuccess() throws IOException {
        String deviceId = "device123";
        String packageName = "com.example.app";
        String permission = "android.permission.WRITE_EXTERNAL_STORAGE";
        when(executor.execute(any(AndroidGivePermissionsCommand.class),
                any(Long.class))).thenReturn("Permissions granted successfully");

        appInstallService.givePermissionsToApplication(deviceId, packageName, permission);
        verify(executor).execute(any(AndroidGivePermissionsCommand.class), any(Long.class));
    }

    @Test
    public void testGivePermissionsToApplicationFailure() throws IOException {
        String deviceId = "device123";
        String packageName = "com.example.app";
        String permission = "android.permission.WRITE_EXTERNAL_STORAGE";

        when(executor.execute(any(AndroidGivePermissionsCommand.class), any(Long.class)))
                .thenThrow(new IOException("Permission grant failed"));

        assertThrows(IOException.class, () ->
                appInstallService.givePermissionsToApplication(deviceId, packageName, permission));
        verify(executor).execute(any(AndroidGivePermissionsCommand.class), any(Long.class));
    }

    @Test
    public void testMakeDeviceAdminSuccess() throws IOException {
        String deviceId = "device123";
        String packageName = "com.example.app";

        when(executor.execute(any(AndroidMakeDeviceAdminCommand.class)))
                .thenReturn("Device admin granted successfully");

        boolean result = appInstallService.makeDeviceAdmin(deviceId, packageName);

        assertTrue(result);
        verify(executor).execute(any(AndroidMakeDeviceAdminCommand.class));
    }

    @Test
    public void testMakeDeviceAdminFailure() throws IOException {
        String deviceId = "device123";
        String packageName = "com.example.app";

        when(executor.execute(any(AndroidMakeDeviceAdminCommand.class)))
                .thenThrow(new IOException("Device admin grant failed"));

        boolean result = appInstallService.makeDeviceAdmin(deviceId, packageName);

        assertFalse(result);
        verify(executor).execute(any(AndroidMakeDeviceAdminCommand.class));
    }

    @Test
    public void testOpenAppSuccess() throws IOException {
        String deviceId = "device123";
        String appPackage = "com.example.app";

        when(executor.execute(any(AndroidOpenAppCommand.class))).thenReturn("App opened successfully");

        assertDoesNotThrow(() -> appInstallService.openApp(deviceId, appPackage));
        verify(executor).execute(any(AndroidOpenAppCommand.class));
    }

    @Test
    public void testAutoOpenAppFailure() throws IOException {
        String deviceId = "device123";
        String appPackage = "com.example.app";

        when(executor.execute(any(AndroidOpenAppCommand.class))).
                thenThrow(new IOException("Failed to open the app"));

        assertDoesNotThrow(() -> appInstallService.openApp(deviceId, appPackage));
        verify(executor, times(3)).execute(any(AndroidOpenAppCommand.class));
    }

    @Test
    public void testForceKillApp() throws IOException {
        String deviceId = "device123";
        when(executor.execute(any(AndroidKillApplicationCommand.class))).thenReturn(anyString());

        assertDoesNotThrow(() -> appInstallService.forceKillApp(deviceId, "com.example.com"));

        verify(executor).execute(any(AndroidKillApplicationCommand.class));
    }

    @Test
    public void testAppInstalled() throws IOException {
        String identifier = "device123";
        when(executor.execute(any(AndroidCheckAppInstalledCommand.class)))
                .thenReturn("Package: com.upgenicsint.phonechecklite");
        assertTrue(appInstallService.isAppInstalled(identifier, "com.upgenicsint.phonechecklite"));
    }

    @Test
    public void testAppNotInstalled() throws IOException {
        String identifier = "device123";
        when(executor.execute(any(AndroidCheckAppInstalledCommand.class)))
                .thenReturn("");
        assertFalse(appInstallService.isAppInstalled(identifier, "com.upgenicsint.phonechecklite"));
    }

    @Test
    public void testPushApkFileToDownloadsSuccess() throws IOException {
        String deviceId = "device123";
        String filePath = "/path/to/apk";
        String appName = "test.apk";
        boolean pushFilesToDownloads = true;

        when(executor.execute(any(AndroidPushFileCommand.class))).
                thenReturn("file pushed successfully");

        boolean result = appInstallService.pushApkFileToDevice(deviceId, filePath, pushFilesToDownloads, appName);

        assertTrue(result);
        verify(executor).execute(any(AndroidPushFileCommand.class));
    }


    @Test
    public void testPushApkFileToDownloadsFailure() throws IOException {
        String deviceId = "device123";
        String filePath = "/path/to/apk";
        boolean pushFilesToDownloads = true;
        String fileName = "file.apk";
        when(executor.execute(any(AndroidPushFileCommand.class))).thenReturn("file push failed with error");

        boolean result = appInstallService.pushApkFileToDevice(deviceId, filePath, pushFilesToDownloads, fileName);

        assertFalse(result);
        verify(executor).execute(any(AndroidPushFileCommand.class));
    }
}

