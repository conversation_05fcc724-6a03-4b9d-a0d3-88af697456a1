package com.phonecheck.app.android;

import com.phonecheck.command.device.android.fingerprint.AndroidFingerPrintPasswordAutomation;
import com.phonecheck.command.device.android.fingerprint.AndroidPutFingerPrintGestureCommand;
import com.phonecheck.command.device.android.fingerprint.AndroidRunFingerPrintActivityCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.constants.android.AndroidAppPackageConstants;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.syslog.android.AndroidSysLogKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AndroidFingerPrintServiceTest {

    @Mock
    private CommandExecutor executor;
    @Mock
    private AndroidAppService appService;
    @InjectMocks
    private AndroidFingerPrintService fingerPrintService;
    private AndroidDevice device;

    @BeforeEach
    void beforeEach() {
        device = new AndroidDevice();
        device.setId("12345");
    }

    @Test
    @DisplayName("FingerPrint test for samsung device with major os version as 6")
    public void fingerPrintTestForSamsungTest1() throws IOException {
        device.setOsMajorVersion(6f);
        fingerPrintService.performFingerPrintOperationForSamsung(device, AndroidSysLogKey.FINGER_PRINT_11);
        verify(appService).forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }

    @Test
    @DisplayName("FingerPrint test for samsung device with major os version as 8")
    public void fingerPrintTestForSamsungTest2() throws IOException {
        device.setOsMajorVersion(8f);
        fingerPrintService.performFingerPrintOperationForSamsung(device, AndroidSysLogKey.FINGER_PRINT_11);
        verify(appService).forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }

    @Test
    @DisplayName("FingerPrint test for samsung device with test key as START_FINGER_PRINT_ADMIN")
    public void fingerPrintTestForSamsungTest3() throws IOException {
        fingerPrintService.performFingerPrintOperationForSamsung(device, AndroidSysLogKey.START_FINGER_PRINT_PIN);
        verify(executor).execute(any(AndroidFingerPrintPasswordAutomation.class));
    }

    @Test
    @DisplayName("FingerPrint test for other device with major os version as 6")
    public void fingerPrintTestForOtherDevicesTest1() throws IOException {
        device.setOsMajorVersion(6f);
        fingerPrintService.performFingerPrintOperationForOtherDevices(device, AndroidSysLogKey.FINGER_PRINT_11);
        verify(appService).forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }

    @Test
    @DisplayName("FingerPrint test for other device with major os version as 8")
    public void fingerPrintTestForOtherDevicesTest2() throws IOException {
        device.setOsMajorVersion(8f);
        fingerPrintService.performFingerPrintOperationForSamsung(device, AndroidSysLogKey.FINGER_PRINT_11);
        verify(appService).forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }

    @Test
    public void putFingerPrintGestureInSettingsTest() throws IOException {
        fingerPrintService.putFingerPrintGestureInSettings(device.getId());
        verify(executor).execute(any(AndroidPutFingerPrintGestureCommand.class));
    }

    @Test
    public void runFingerPrintActivityTest() throws IOException {
        fingerPrintService.runFingerPrintActivity(device.getId());
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }

    @Test
    public void runFingerPrintMainActivityTest() throws IOException {
        fingerPrintService.runFingerPrintActivity(device.getId());
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }

    @Test
    public void runFingerPrintPasswordAutomationForUnderDisplayTest() throws IOException {
        fingerPrintService.runFingerPrintPasswordAutomationForUnderDisplay(device.getId());
        verify(executor).execute(any(AndroidFingerPrintPasswordAutomation.class));
    }

    @Test
    public void runFingerPrintPasswordAutomationForSamsungTest() throws IOException {
        fingerPrintService.runFingerPrintPasswordAutomationForSamsung(device.getId());
        verify(executor).execute(any(AndroidFingerPrintPasswordAutomation.class));
    }

    @Test
    public void runFingerPrintPasswordAutomationForGoogleTest() throws IOException {
        fingerPrintService.runFingerPrintPasswordAutomationForGoogle(device.getId());
        verify(executor).execute(any(AndroidFingerPrintPasswordAutomation.class));
    }

    @Test
    public void runFingerPrintDeviceAdminSuccessTest() throws IOException {
        fingerPrintService.runFingerPrintDeviceAdminSuccess(device.getId());
        verify(executor).execute(any(AndroidRunFingerPrintActivityCommand.class));
    }
}
