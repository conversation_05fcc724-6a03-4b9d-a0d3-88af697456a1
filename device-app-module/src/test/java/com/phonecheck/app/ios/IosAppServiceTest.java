package com.phonecheck.app.ios;

import com.phonecheck.command.device.ios.peo.IosInstallAppCommand;
import com.phonecheck.command.device.ios.peo.IosLaunchAppCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.license.LicenseService;
import com.phonecheck.model.constants.LicenseChargeStatus;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.AppInstallStatus;
import com.phonecheck.model.status.AppLaunchStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.ios.peo.IosInstallAppParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosAppServiceTest {
    @InjectMocks
    private IosAppService appService;

    @Mock
    private CommandExecutor executor;
    @Mock
    private LicenseService licenseService;
    @Mock
    private IosAppVersionService iosAppVersionService;
    @Mock
    private AssociateToAppleVppService associateToAppleVppService;
    @Mock
    private IosInstallAppParser iosInstallAppParser;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private OsChecker osChecker;
    @Mock
    private InMemoryStore inMemoryStore;

    private IosDevice device;
    private final String iosAppIdentifier = "com.phonecheckdiag3";
    private final String vppToken = "token";
    private final String commandOutput = "command Output";

    @BeforeEach
    void beforeEach() {
        device = new IosDevice();
        device.setEcid("12345");
    }

    @Test
    public void installDiagnosticAppSuccessfulTest1() throws IOException {
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.3");
        when(licenseService.checkAndSaveAppInstallLicence(device))
                .thenReturn(LicenseChargeStatus.PENDING);

        when(inMemoryStore.getUserName()).thenReturn("username");
        when(executor.execute(any(IosInstallAppCommand.class), anyLong())).thenReturn(commandOutput);
        when(iosInstallAppParser.parse(eq(commandOutput))).thenReturn(AppInstallStatus.SUCCESS);

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.4", "/",
                iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService).checkAndSaveAppInstallLicence(device);
        verify(executor, times(1)).execute(any(IosInstallAppCommand.class), anyLong());
        assertEquals(appInstallStatus, AppInstallStatus.SUCCESS);
    }

    @Test
    public void installDiagnosticAppSuccessfulTest2() throws IOException {
        device.setProductVersion("8");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.4");
        when(licenseService.checkAndSaveAppInstallLicence(device))
                .thenReturn(LicenseChargeStatus.PENDING);

        when(inMemoryStore.getUserName()).thenReturn("username");
        when(executor.execute(any(IosInstallAppCommand.class), anyLong())).thenReturn(commandOutput);
        when(iosInstallAppParser.parse(eq(commandOutput))).thenReturn(AppInstallStatus.SUCCESS);

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.3", "/",
                iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService).checkAndSaveAppInstallLicence(device);
        verify(executor, times(1)).execute(any(IosInstallAppCommand.class), anyLong());
        assertEquals(appInstallStatus, AppInstallStatus.SUCCESS);
    }

    @Test
    public void installDiagnosticAppRetryTest() throws IOException {
        device.setProductVersion("8");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.4");
        when(licenseService.checkAndSaveAppInstallLicence(device))
                .thenReturn(LicenseChargeStatus.PENDING);

        when(inMemoryStore.getUserName()).thenReturn("username");
        when(executor.execute(any(IosInstallAppCommand.class), anyLong())).thenReturn(commandOutput);
        when(iosInstallAppParser.parse(eq(commandOutput))).thenReturn(AppInstallStatus.FAILED_UNKNOWN);

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.3", "/",
                iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService).checkAndSaveAppInstallLicence(device);
        verify(executor, times(8)).execute(any(IosInstallAppCommand.class), anyLong());
        assertEquals(appInstallStatus, AppInstallStatus.FAILED_UNKNOWN);
    }

    @Test
    public void installDiagnosticAppLicenseExpiredTest() throws IOException {
        device.setProductVersion("15.1");
        when(inMemoryStore.getUserName()).thenReturn("username");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.4");
        when(licenseService.checkAndSaveAppInstallLicence(device))
                .thenReturn(LicenseChargeStatus.LICENSE_EXPIRED);

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.3", "/",
                iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService).checkAndSaveAppInstallLicence(device);
        verify(executor, never()).execute(any(IosInstallAppCommand.class));
        assertEquals(appInstallStatus, AppInstallStatus.FAILED_LICENSE_EXPIRED);
    }

    @Test
    public void installDiagnosticAppFileNotFoundTest() throws IOException {
        device.setProductVersion("15.1");
        when(inMemoryStore.getUserName()).thenReturn("username");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.4");
        when(licenseService.checkAndSaveAppInstallLicence(device))
                .thenReturn(LicenseChargeStatus.PENDING);

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.3",
                "This path is never found", iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService).checkAndSaveAppInstallLicence(device);
        verify(executor, never()).execute(any(IosInstallAppCommand.class));
        assertEquals(appInstallStatus, AppInstallStatus.FAILED_FILE_NOT_FOUND);
    }

    @Test
    public void installDiagnosticAppNotRequiredTest1() throws IOException {
        device.setProductVersion("9.0");
        when(inMemoryStore.getUserName()).thenReturn("username");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.4");

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.4",
                "This path is never found", iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService, never()).checkAndSaveAppInstallLicence(any());
        verify(executor, never()).execute(any(IosInstallAppCommand.class));

        assertEquals(appInstallStatus, AppInstallStatus.NOT_REQUIRED);
    }

    @Test
    public void installDiagnosticAppNotRequiredTest2() throws IOException {
        device.setProductVersion("6");
        when(inMemoryStore.getUserName()).thenReturn("username");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.4");

        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.4",
                "This path is never found", iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService, never()).checkAndSaveAppInstallLicence(any());
        verify(executor, never()).execute(any(IosInstallAppCommand.class));

        assertEquals(appInstallStatus, AppInstallStatus.NOT_REQUIRED);
    }

    @Test
    public void installDiagnosticAppEcidMissingTest() throws IOException {
        device.setEcid("");
        when(inMemoryStore.getUserName()).thenReturn("username");
        when(iosAppVersionService.getInstalledAppVersion(eq(device), eq(iosAppIdentifier))).thenReturn("2.3.3");


        AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, "2.3.4",
                "This path is never found", iosAppIdentifier, vppToken);

        verify(iosAppVersionService).getInstalledAppVersion(eq(device), eq(iosAppIdentifier));
        verify(licenseService, never()).checkAndSaveAppInstallLicence(any());
        verify(executor, never()).execute(any(IosInstallAppCommand.class));

        assertEquals(appInstallStatus, AppInstallStatus.FAILED_ECID_MISSING);
    }

    @Test
    public void openAppSuccessfulTest1() throws IOException {
        when(osChecker.isMac()).thenReturn(true);
        when(executor.execute(any(IosLaunchAppCommand.class))).thenReturn("pid");

        AppLaunchStatus appLaunchStatus = appService.openApp(device, "com.phonecheckdiag3");

        verify(executor).execute(any(IosLaunchAppCommand.class));
        assertEquals(appLaunchStatus, AppLaunchStatus.SUCCESS);
    }

    @Test
    public void openAppSuccessfulTest2() throws IOException {
        when(osChecker.isMac()).thenReturn(false);
        when(executor.execute(any(IosLaunchAppCommand.class))).thenReturn("1111222");

        AppLaunchStatus appLaunchStatus = appService.openApp(device, "com.phonecheckdiag3");

        verify(executor).execute(any(IosLaunchAppCommand.class));
        assertEquals(appLaunchStatus, AppLaunchStatus.SUCCESS);
    }

    @Test
    public void openAppFailedTest1() throws IOException {
        when(osChecker.isMac()).thenReturn(true);
        when(executor.execute(any(IosLaunchAppCommand.class))).thenReturn(null);

        AppLaunchStatus appLaunchStatus = appService.openApp(device, "com.phonecheckdiag3");

        verify(executor).execute(any(IosLaunchAppCommand.class));
        assertEquals(appLaunchStatus, AppLaunchStatus.FAILED_NO_DEVICE);
    }

    @Test
    public void openAppFailedTest2() throws IOException {
        when(osChecker.isMac()).thenReturn(false);
        when(executor.execute(any(IosLaunchAppCommand.class))).thenReturn(null);

        AppLaunchStatus appLaunchStatus = appService.openApp(device, "com.phonecheckdiag3");

        verify(executor).execute(any(IosLaunchAppCommand.class));
        assertEquals(appLaunchStatus, AppLaunchStatus.FAILED_NO_DEVICE);
    }
}
