package com.phonecheck.app.profile;

import com.phonecheck.command.device.ios.peo.IosGetInstalledProfilesList;
import com.phonecheck.command.device.ios.profile.IosPushProfileCommand;
import com.phonecheck.command.device.ios.profile.IosRemoveProfileCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.ios.IosConfigProfile;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.device.ios.peo.IosIsProfileInstalledParser;
import com.phonecheck.parser.device.ios.profile.IosPushProfileParser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;

@Service
@AllArgsConstructor
public class ConfigProfileService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigProfileService.class);
    private final CommandExecutor executor;
    private final IosPushProfileParser iosPushProfileParser;
    private final IosIsProfileInstalledParser iosIsProfileInstalledParser;
    private final OsChecker osChecker;

    /**
     * Checks if specific profile is already installed on device
     *
     * @param device  target device
     * @param profile Ios Profile type
     * @return profile installed status
     */
    public Boolean isConfigProfileInstalled(final IosDevice device, final IosConfigProfile profile) {
        try {
            final String output = executor.execute(new IosGetInstalledProfilesList(device.getId()), 10);
            if (output != null) {
                return iosIsProfileInstalledParser.parse(output, profile);
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.warn("Exception occurred while checking if profile is pushed to the device.", e);
        }

        return null;
    }

    /**
     * Pushes the specific profile to the target device
     *
     * @param device           target device
     * @param file             that needs to be pushed
     * @param iosConfigProfile ios Config profile type
     * @return status of push profile
     */
    public Boolean iosPushProfile(final IosDevice device,
                                  final File file,
                                  final IosConfigProfile iosConfigProfile) {
        if (IosConfigProfile.WIFI.equals(iosConfigProfile)
                && Boolean.TRUE.equals(isConfigProfileInstalled(device, IosConfigProfile.WIFI))) {
            return true;
        }
        try {
            final String output = executor.execute(new IosPushProfileCommand(device.getId(), file), 15);
            if (output != null) {
                return iosPushProfileParser.parse(output);
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.warn("Exception occurred while pushing profile to the device.", e);

            return null;
        }
    }

    /**
     * Remove Wi-Fi profile from device on the target device with specified ssid
     *
     * @param device    target device
     * @param profileId profile id or name of Wi-Fi network(ssid)
     * @return boolean for remove profile operation
     */
    public boolean removeProfile(final IosDevice device, final String profileId) {
        try {
            final String output = executor.execute(new IosRemoveProfileCommand(device.getId(), profileId));
            LOGGER.info("Wifi Profile removal device: {}, profileId: {}, isMac: {}, output: {}",
                    device, profileId, osChecker.isMac(), output);
            return StringUtils.isNotBlank(output) && output.contains("Profile-deleted");
        } catch (Exception e) {
            LOGGER.warn("Exception occurred while removing wifi profile from device.", e);

            return false;
        }
    }
}
