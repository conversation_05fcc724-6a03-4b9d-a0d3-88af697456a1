package com.phonecheck.app.ios;

import com.phonecheck.app.ios.util.IpaZipUtil;
import com.phonecheck.command.device.ios.peo.IosAppBundleIdCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.parser.device.ios.peo.IosAppBundleIdParser;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Component
@AllArgsConstructor
public class AppBundleIdentifierService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AppBundleIdentifierService.class);
    private static final String INFO_PLIST = "Info.plist";
    private static final String DEFAULT_IOS_APP_BUNDLE_IDENTIFIER = "com.phonecheck.phonecheckinternal";
    private final FileUtil fileUtil;
    private final CommandExecutor executor;
    private final IosAppBundleIdParser iosAppBundleIdParser;
    private final SupportFilePath supportFilePath;


    /**
     * Retrieves the ios app bundle identifier
     *
     * @param ipaFile ipa file
     * @return ios app bundle identifier
     */
    public String getIosAppBundleID(final File ipaFile) {
        String appBundleId = null;
        try {
            if (ipaFile.exists()) {
                ZipFile zipFile = new ZipFile(ipaFile);
                ZipEntry entry = IpaZipUtil.zipEntryParser(zipFile);
                if (entry != null) {
                    final InputStream inputStream = zipFile.getInputStream(entry);
                    String result = new BufferedReader(new InputStreamReader(inputStream))
                            .lines().collect(Collectors.joining("\n"));
                    appBundleId = iosAppBundleIdParser.parse(result);
                    if (appBundleId == null) {
                        File infoPlistFile =
                                fileUtil.createFile(supportFilePath.getPaths().getRootFolderPath()
                                        + "/apps/temp/" + INFO_PLIST);
                        // read the zipfile input stream into the plist file
                        FileUtils.copyInputStreamToFile(zipFile.getInputStream(entry), infoPlistFile);

                        // Execute command to retrieve IOS app bundle ID
                        final String output =
                                executor.execute(new IosAppBundleIdCommand(infoPlistFile.getAbsolutePath()));
                        if (output != null) {
                            appBundleId = iosAppBundleIdParser.parse(output);
                        }

                        infoPlistFile.delete();
                    }
                }
                zipFile.close();
            }
        } catch (IOException e) {
            LOGGER.error("Could not retrieve ios app bundle identifier", e);
        }
        return appBundleId != null ? appBundleId : DEFAULT_IOS_APP_BUNDLE_IDENTIFIER;
    }
}
