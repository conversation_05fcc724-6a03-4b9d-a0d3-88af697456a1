package com.phonecheck.app;


import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.util.SupportFilePath;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * The AppFileService to get application file (apk/ipa) path
 * based on device type.
 */
@Service
@AllArgsConstructor
public class AppFileService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AppFileService.class);
    private final SupportFilePath supportFilePath;
    public static final String SUPPORTING_IOS_IPA_DIR = "/ipa/";

    /**
     * Get a file path to download apps in
     *
     * @param family Device Family IOS / Android
     * @param extension any extension in app file name after apple / android
     * @return file
     */
    public File getAppFile(final DeviceFamily family, final String extension) {
        final File appsDirPath = new File(supportFilePath.getPaths().getRootFolderPath(), "apps");
        if (!appsDirPath.exists()) {
            appsDirPath.mkdirs();
        }
        if (family == DeviceFamily.IOS) {
            return new File(appsDirPath, "apple" + extension + ".ipa");
        }
        return new File(appsDirPath, "android.apk");
    }

    /**
     * Get file path of ipa placed in res/files/ipa
     *
     * @return file
     */
    public File get32BitAppFile() {
        return new File(supportFilePath.getPaths().getFilesRootFolderPath() + SUPPORTING_IOS_IPA_DIR,
                "apple.ipa");
    }
}
