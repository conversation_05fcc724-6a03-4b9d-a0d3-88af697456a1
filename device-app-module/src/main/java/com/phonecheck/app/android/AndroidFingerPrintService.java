package com.phonecheck.app.android;

import com.phonecheck.command.device.android.fingerprint.AndroidFingerPrintPasswordAutomation;
import com.phonecheck.command.device.android.fingerprint.AndroidPutFingerPrintGestureCommand;
import com.phonecheck.command.device.android.fingerprint.AndroidRunFingerPrintActivityCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.constants.android.AndroidAppPackageConstants;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.syslog.android.AndroidSysLogKey;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Set;

@Service
@AllArgsConstructor
public class AndroidFingerPrintService {
    public static final Logger LOGGER = LoggerFactory.getLogger(AndroidFingerPrintService.class);
    private final CommandExecutor executor;
    private final AndroidAppService appService;
    private static final String FINGERPRINT_ACTIVITY =
            AndroidAppPackageConstants.PC_UTILITY_PACKAGE + "/.activities.FingerPrintActivity";
    private static final String FINGERPRINT_MAIN_ACTIVITY =
            AndroidAppPackageConstants.PC_UTILITY_PACKAGE + "/.activities.MainActivity";
    public static final Set<String> FINGERPRINT_GESTURE_SUPPORTED_MODELS = Set.of(
            //s8
            "SM-G950", "SM-G950W", "SM-G950V", "SM-G950U", "SM-G950T", "SM-G950R4", "SM-G950P",
            //s8+
            "SM-G955", "SM-G955FD", "SM-G955A", "SM-G955F", "SM-G955N", "SM-G955P", "SM-G955R4", "SM-G955T",
            "SM-G955U", "SM-G955V", "SM-G955W",
            //s9
            "SM-G960", "SM-G9600", "SM-G960F", "SM-G960F/DS", "SM-G960U", "SM-G960W", "SM-G9608", "SM-G960N",
            //s9+
            "SM-G965", "SM-G9650", "SM-G965F", "SM-G965F/DS", "SM-G965U", "SM-G965W", "SM-G9658", "SM-G965N",
            //galaxy note 8
            "SM-N9508", "SM-N9500", "SM-N950FD", "SM-N950F", "SM-N950N", "SM-N950U", "SM-N950W", "SM-N950",
            //Galaxy note 9
            "SM-N960", "SM-N9600", "SM-N960F", "SM-N960T", "SM-N960N", "SM-N960U", "SM-N9608", "SM-N960FD", "SM-N960W");
    public static final Set<String> NON_MEID_SUPPORTED_MODELS = Set.of(
            //Note10 devices list
            "SM-N970W", "SM-N970WZSAXAC",
            "SM-N970WZKAXAC", "SM-N9700/DS", "SM-N9708", "SM-N970F", "SM-N970N", "SM-N970F/DS", "SM-N970FZSDBTU",
            "SM-N970FZKDBTU", "SM-N970UZSAATT", "SM-N970UZKAATT", "SM-N970UZWAATT", "SM-N970UZSASPR", "SM-N970UZKASPR",
            "SM-N970UZWASPR", "SM-N970UZSATMB", "SM-N970UZKATMB", "SM-N970UZWATMB", "SM-N970UZSAUSC", "SM-N970UZKAUSC",
            "SM-N970UZWAUSC", "SM-N970UZSAVZW", "SM-N970UZKAVZW", "SM-N970UZWAVZW",
            "SM-N970U1", "SM-N970U", "SM-N970XU", "SM-N976F", "SM-N976N", "SM-N976U", "SM-N975F", "SM-N975F/DS",
            "SM-N9750F/DS", "SM-N975N", "SM-N975U", "SM-N975U1", "SM-N970UZSAXAA", "SM-N970UZKAXAA", "SM-N970UZWAXAA",
            // S10 galaxy s10
            "SM-G973", "SM-G9730", "SM-G973F", "SM-G973U", "SM-SM-G973W", "SM-G977B",
            //galaxy s10e
            "SM-G970", "SM-G9700", "SM-G970F", "SM-G970U", "SM-G970W",
            //galaxy s10+
            "SM-G975", "SM-G9750", "SM-G975F", "SM-G975U", "SM-G975W",
            //galaxy s20 Series
            "SM-G980", "SM-G981", "SM-G985", "SM-G986", "SM-G988",
            //Galaxy A series Devices
            //A20
            "SM-A205F", "SM-A205G", "SM-A205GN", "SM-A205U", "SM-A205W", "SM-A205YN",
            //A80
            "SM-A805F", "SM-A8050", "SM-A805F/DS",
            //A71
            "SM-A715F/DS", "SM-A715F/DSN", "SM-A715F/DSM",
            //A70
            "SM-A7050ZWNTGY", "SM-A705F/DS", "SM-A705FN/DS", "SM-A705GM/DS", "SM-A705MN/DS", "SM-A705WZKAXAC",
            //A70s
            "SM-A707F/DS", "SM-A707FN/DS", "SM-A707GM/DS", "SM-A707MN/DS", "SM-A7070",
            //A51
            "SM-A515F/DSN", "SM-A515F/DS", "SM-A515F/DST", "SM-A515F/DSM", "SM-A515F/N",
            //A50
            "SM-A505F", "SM-A505FG", "SM-A505F/DS", "SM-A505FN/DS", "SM-A505GN/DS", "SM-A505FM/DS", "SM-A505YN",
            "SM-A505W", "SM-A505GT/DS", "SM-A505U1", "SM-A505U", "SM-A505G/DS",
            //A50s
            "SM-A507F/DS", "SM-A507FN/DS",
            //A30s
            "SM-A307F/DS", "SM-A307FN/DS", "SM-A307G/DS", "SM-A307GN/DS", "SM-A307GT/DS",
            //HUWAIE Devices
            "LIO-L09", "LIO-L29", "LIO-AL00", "LIO-TL00", "TAS-L09", "TAS-L29", "TAS-AL00", "TAS-TL00",
            "JNY-L21A", "JNY-L01A", "JNY-L21B", "JNY-L22A", "JNY-L02A", "JNY-L22B", "ELS-AN00", "ELS-TN00"
    );

    /**
     * Performs fingerprint operation for samsung devices
     *
     * @param device           target android device
     * @param androidSysLogKey syslog key
     */
    public void performFingerPrintOperationForSamsung(final AndroidDevice device,
                                                      final AndroidSysLogKey androidSysLogKey) {
        if (device.getOsMajorVersion() == 6f && androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_11) {
            try {
                appService.forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
            } catch (IOException e) {
                LOGGER.error("Exception occurred while force killing settings app", e);
            }
            runFingerPrintActivity(device.getId());
        } else if ((device.getOsMajorVersion() == 7f || device.getOsMajorVersion() == 8f ||
                device.getOsMajorVersion() == 9f) &&
                (androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_15
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_11 ||
                        androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_16)) {

            try {
                appService.forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
            } catch (IOException e) {
                LOGGER.error("Exception occurred while force killing settings app", e);
            }
            runFingerPrintActivity(device.getId());
        }
        if (androidSysLogKey == AndroidSysLogKey.START_FINGER_PRINT_ADMIN
                || androidSysLogKey == AndroidSysLogKey.END_FINGER_PRINT_ADMIN) {
            if (appService.makeDeviceAdmin(device.getId(), AndroidAppPackageConstants.PC_UTILITY_PACKAGE)) {
                runFingerPrintDeviceAdminSuccess(device.getId());
            }
        } else if (androidSysLogKey == AndroidSysLogKey.START_FINGER_PRINT_PIN
                || androidSysLogKey == AndroidSysLogKey.END_FINGER_PRINT_PIN) {

            runFingerPrintPasswordAutomationForSamsung(device.getId());
        }
    }

    /**
     * Performs fingerprint operation for devices other than samsung
     *
     * @param device           target android device
     * @param androidSysLogKey syslog key
     */
    public void performFingerPrintOperationForOtherDevices(final AndroidDevice device,
                                                           final AndroidSysLogKey androidSysLogKey) {
        if (device.getOsMajorVersion() == 6f && androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_11) {
            try {
                appService.forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
            } catch (IOException e) {
                LOGGER.error("Exception occurred while force killing settings app", e);
            }
            runFingerPrintActivity(device.getId());
        } else if ((device.getOsMajorVersion() == 7f || device.getOsMajorVersion() == 8f ||
                device.getOsMajorVersion() == 9f) &&
                (androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_12
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_13 ||
                        androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_14)) {
            try {
                appService.forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
            } catch (IOException e) {
                LOGGER.error("Exception occurred while force killing settings app", e);
            }
            runFingerPrintActivity(device.getId());
        }

        if (androidSysLogKey == AndroidSysLogKey.START_FINGER_PRINT_ADMIN
                || androidSysLogKey == AndroidSysLogKey.END_FINGER_PRINT_ADMIN) {
            if (appService.makeDeviceAdmin(device.getId(), AndroidAppPackageConstants.PC_UTILITY_PACKAGE)) {
                runFingerPrintDeviceAdminSuccess(device.getId());
            }
        } else if (androidSysLogKey == AndroidSysLogKey.START_FINGER_PRINT_PIN
                || androidSysLogKey == AndroidSysLogKey.END_FINGER_PRINT_PIN) {
            runFingerPrintPasswordAutomationForGoogle(device.getId());
        }
    }

    /**
     * Put fingerprint gesture in settings for devices that support fingerprint gestures
     *
     * @param deviceId target android device identifier
     */
    public void putFingerPrintGestureInSettings(final String deviceId) {
        try {
            executor.execute(new AndroidPutFingerPrintGestureCommand(deviceId));
        } catch (IOException e) {
            LOGGER.error("Error while trying to put finger print gesture in Settings", e);
        }
    }

    /**
     * Run fingerprint activity command on device
     *
     * @param identifier target android device identifier
     */
    public void runFingerPrintActivity(final String identifier) {
        try {
            executor.execute(new AndroidRunFingerPrintActivityCommand(identifier, FINGERPRINT_ACTIVITY,
                    "com.phonecheck.fingerprintsuccess"));
        } catch (IOException e) {
            LOGGER.error("Error while trying to run finger print activity command", e);
        }
    }

    /**
     * Run fingerprint main activity command on device
     *
     * @param identifier target android device identifier
     * @param key        syslog key
     */
    public void runFingerPrintMainActivity(final String identifier, final String key) {
        try {
            executor.execute(new AndroidRunFingerPrintActivityCommand(identifier, FINGERPRINT_MAIN_ACTIVITY,
                    key));
        } catch (IOException e) {
            LOGGER.error("Error while trying to run finger print main activity command", e);
        }
    }

    /**
     * Run fingerprint password automation command for underDisplay on device
     *
     * @param identifier target android device identifier
     */
    public void runFingerPrintPasswordAutomationForUnderDisplay(final String identifier) {
        try {
            executor.execute(new AndroidFingerPrintPasswordAutomation(identifier, "UnderDisplay"));
        } catch (IOException e) {
            LOGGER.error("Error while trying to run fingerprint under display password automation", e);
        }
    }

    /**
     * Run fingerprint password automation command with 'samsung' specific key on device
     *
     * @param identifier target android device identifier
     */
    public void runFingerPrintPasswordAutomationForSamsung(final String identifier) {
        try {
            executor.execute(new AndroidFingerPrintPasswordAutomation(identifier, "Samsung"));
        } catch (IOException e) {
            LOGGER.error("Error while trying to run fingerprint samsung password automation", e);
        }
    }

    /**
     * Run fingerprint password automation command with 'google' specific key on device
     *
     * @param identifier target android device identifier
     */
    public void runFingerPrintPasswordAutomationForGoogle(final String identifier) {
        try {
            executor.execute(new AndroidFingerPrintPasswordAutomation(identifier, "Google"));
        } catch (IOException e) {
            LOGGER.error("Error while trying to run fingerprint samsung password automation", e);
        }
    }

    /**
     * Run fingerprint admin success command on device
     *
     * @param identifier target android device identifier
     */
    public void runFingerPrintDeviceAdminSuccess(final String identifier) {
        try {
            executor.execute(new AndroidRunFingerPrintActivityCommand(identifier, FINGERPRINT_ACTIVITY,
                    "com.phonecheck.deviceadminsuccess"));
        } catch (IOException e) {
            LOGGER.error("Error while trying to run finger print activity command for device admin success", e);
        }
    }
}
