package com.phonecheck.device.erase;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.command.device.android.erase.AndroidAtEraseCommand;
import com.phonecheck.command.device.android.erase.AndroidDeviceEraseCommand;
import com.phonecheck.command.device.android.erase.AndroidSdCardEraseCommand;
import com.phonecheck.command.device.android.erase.AndroidWakeUpCommand;
import com.phonecheck.command.device.android.info.AndroidAtPortAvailabilityCommand;
import com.phonecheck.device.connection.android.AndroidDeviceConnectionService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.license.LicenseService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.constants.LicenseChargeStatus;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.status.DeviceEraseStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Set;

@Service
public class AndroidEraseService extends DeviceEraseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidEraseService.class);
    private static final int AT_ERASE_RETRIES = 20;
    private static final String SIM_ERASE = "SIM_ERASE";

    private final CommandExecutor executor;
    private final LicenseService licenseService;
    private final AndroidDeviceConnectionService androidDeviceConnectionService;
    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final AndroidEsimEraseService androidEsimEraseService;

    public AndroidEraseService(final ObjectMapper objectMapper,
                               final CommandExecutor executor,
                               final CloudApiRestClient cloudApiRestClient,
                               final LicenseService licenseService,
                               final AndroidDeviceConnectionService androidDeviceConnectionService,
                               final AndroidDeviceInfoService androidDeviceInfoService,
                               final AndroidEsimEraseService androidEsimEraseService) {
        super(objectMapper, cloudApiRestClient);

        this.executor = executor;
        this.licenseService = licenseService;
        this.androidDeviceConnectionService = androidDeviceConnectionService;
        this.androidDeviceInfoService = androidDeviceInfoService;
        this.androidEsimEraseService = androidEsimEraseService;
    }

    /**
     * Initiates device erase procedure
     *
     * @param device      target device to erase
     * @param userName    username of phonecheck desktop app user
     * @param buildNo     phonecheck desktop app build number
     * @param testerId    tester id of phonecheck desktop app user
     * @param warehouseId warehouse id of phonecheck desktop app user
     * @return DeviceEraseStatus
     * @throws IOException when the output couldn't be read
     */
    @Override
    public DeviceEraseStatus startErase(final Device device,
                                        final String userName,
                                        final String buildNo,
                                        final String testerId,
                                        final String warehouseId) throws IOException {
        final AndroidDevice androidDevice = (AndroidDevice) device;

        if (!shouldEraseDevice(androidDevice.getId(), androidDevice.getImei(), androidDevice.getSerial())) {
            LOGGER.debug("Device erase failed because device is filtered for erase");
            return DeviceEraseStatus.FAILED_ERASE_FILTERED;
        }

        LicenseChargeStatus eraseLicenseChargeStatus = licenseService.checkAndSaveDeviceEraseLicense(androidDevice);
        if (LicenseChargeStatus.PENDING != eraseLicenseChargeStatus) {
            return DeviceEraseStatus.ERASE_FAILED_LICENSE_EXPIRED;
        }

        // set stage to performing erase, so we can track the device after restart
        androidDevice.setStage(DeviceStage.ERASE_IN_PROGRESS);

        if (AndroidConnectionMode.ADB.equals(androidDevice.getAndroidConnectionMode())) {
            // TODO Check if pc utility app is installed and it's admin
            // TODO remove MDN if phone number is found
            // TODO get eraser level preference from cloud. for now we are using low level as default

            // Erase SD card if available
            LOGGER.info("Detecting SD card");
            String sdCardStoragePath = androidDeviceInfoService.getAdbSdCardStoragePathIfPresent(androidDevice);
            if (StringUtils.isNotBlank(sdCardStoragePath)) {
                LOGGER.info("SD card detected, erasing SD card");
                androidDevice.setSdCardErased(eraseAndroidSdCard(androidDevice, sdCardStoragePath));
            }

            if (androidDevice.isESimSupported()) {
                if (StringUtils.containsIgnoreCase(androidDevice.getMake(), "Samsung")
                        && androidDevice.isEsimActive()) {
                    LOGGER.info("eSim is active for samsung  device with, trying to erase eSim...");

                    for (int i = 0; i <= 3; i++) {
                        androidEsimEraseService.eraseESimFromSimCardManagerSettings(androidDevice.getId());
                        if (androidEsimEraseService.isAndroidESimRemoved(androidDevice.getId())) {
                            LOGGER.info("Removed eSim from device from Sim Card Manager settings");
                            androidDevice.setESimErased(true);
                            break;
                        } else {
                            LOGGER.info("Could not remove eSim from device from Sim Card Manager settings");
                            androidDevice.setESimErased(false);
                        }
                        androidEsimEraseService.eraseESimFromSimProfileSettings(androidDevice.getId());
                        if (androidEsimEraseService.isAndroidESimRemoved(androidDevice.getId())) {
                            LOGGER.info("Removed eSim from device from Sim Profile settings");
                            androidDevice.setESimErased(true);
                            break;
                        } else {
                            LOGGER.info("Could not remove eSim from device from Sim Profile settings");
                            androidDevice.setESimErased(false);
                        }
                    }
                } else {
                    LOGGER.info("{} device is eSim supported, hence it's assumed that it will be automatically erased",
                            androidDevice.getMake());
                }
            } else {
                LOGGER.info("Device does not support eSim.");
            }

            // TODO uninstall packages

            String output = startOperation(androidDevice, SIM_ERASE);
            if (!StringUtils.containsIgnoreCase(output, "not found")) {
                if (androidDevice.isEsimActive()) {
                    LOGGER.info("Sim erase complete. ESim erase will be attempted by the PC utility app.");
                }
                return DeviceEraseStatus.ERASE_IN_PROGRESS;
            } else {
                return DeviceEraseStatus.ERASE_FAILED_DISCONNECTED;
            }
        } else {
            LOGGER.info("Starting Android erase operation through AT");

            // Start AT Erase
            int attempt = 0;
            do {
                String output = executor
                        .execute(new AndroidAtEraseCommand(androidDevice.getPortName()));
                if (StringUtils.isNotBlank(output)) {
                    if (output.contains("Done Factory Reset")) {
                        return DeviceEraseStatus.ERASE_IN_PROGRESS;
                    }
                } else {
                    final Set<AndroidDevice> currentlyConnectedAtDevices = androidDeviceConnectionService
                            .getConnectedDevicesThroughAt();
                    if (!currentlyConnectedAtDevices.contains(androidDevice)) {
                        return DeviceEraseStatus.ERASE_FAILED_DISCONNECTED;
                    }
                }
            } while (++attempt < AT_ERASE_RETRIES);
        }

        return DeviceEraseStatus.ERASE_FAILED_UNKNOWN;
    }

    // ==================================================================
    //                  Android ADB Erase related Code
    // ==================================================================

    /**
     * Starts operations related to erase e.g Sim erase, factory reset, erase etc
     *
     * @param device    target device
     * @param operation erase operation type
     * @return erase command output
     * @throws IOException in case of failure
     */
    public String startOperation(final AndroidDevice device, final String operation) throws IOException {
        for (int i = 1; i <= 2; i++) {
            wakeUpAndroidDevice(device);
        }

        return executor.execute(new AndroidDeviceEraseCommand(
                device.getId(), operation));
    }

    /**
     * Erases the sd card
     *
     * @param device            target device
     * @param sdCardStoragePath sd card path
     * @return true if erased
     * @throws IOException in case of failure
     */
    private boolean eraseAndroidSdCard(final AndroidDevice device, final String sdCardStoragePath)
            throws IOException {
        final String output = executor.execute(new AndroidSdCardEraseCommand(device.getId(), sdCardStoragePath));

        return StringUtils.isNotBlank(output);
    }

    /**
     * Wakes up android device
     *
     * @param device target device
     * @throws IOException in case of failure
     */
    private void wakeUpAndroidDevice(final AndroidDevice device) throws IOException {
        executor.execute(new AndroidWakeUpCommand(
                device.getId()));
    }

    // ==================================================================
    //                   Android AT Erase related Code
    //  Note: All the AT commands take sometime to process and even in
    //  case of failure, so we will wait max 10 seconds for a command.
    // ==================================================================

    /**
     * Method to check if android AT erase started or not
     *
     * @param device target device.
     * @return boolean
     */
    public boolean checkIfAtEraseStarted(final AndroidDevice device) {
        try {
            int atEraseStartCheckCount = 0;

            do {
                Thread.sleep(2000);

                String output = executor
                        .executeOnce(new AndroidAtPortAvailabilityCommand(device.getPortName()), 2);
                if (StringUtils.isNotBlank(output)) {
                    if (output.contains("Not Available")) {
                        LOGGER.info("AT erase started");
                        return true;
                    }
                }
            }
            while (++atEraseStartCheckCount < AT_ERASE_RETRIES);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while check if AT erase started or not", e);
        }

        return false;
    }
}
