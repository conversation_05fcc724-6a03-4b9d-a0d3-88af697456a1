rootProject.name = 'multi-module-desktop3'

include 'model'
include 'dao-module'
include 'mqtt'
include 'executor'
include 'communicator'

include 'device-connection-module'
include 'device-peo-module'
include 'device-info-module'
include 'device-pair-module'
include 'device-results-module'
include 'device-activation-module'
include 'device-syslog-module'
include 'cloud-api-module'
include 'mount-image-module'
include 'device-operation-module'
include 'station-info-module'
include 'customization-module'
include 'license-module'
include 'print-module'
include 'commands-module'
include 'port-map-module'

include 'phonecheck-backend'
include 'phonecheck-daemon'
include 'phonecheck-ui'
include 'device-app-module'
include 'label-maker-check'
