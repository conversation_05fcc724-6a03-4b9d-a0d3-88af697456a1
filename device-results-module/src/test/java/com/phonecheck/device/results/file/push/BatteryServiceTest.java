package com.phonecheck.device.results.file.push;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static com.phonecheck.model.constants.FileConstants.BATTERY_API_FILE_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class BatteryServiceTest {
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private OsChecker osChecker;

    private BatteryService batteryService;
    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        batteryService = new BatteryService("PhoneCheck",
                supportFilePath,
                fileUtil,
                osChecker,
                mapper
        );
    }

    @Test
    public void testCreateBatteryApiFileForIos() throws IOException {
        int licenseId = 1222;
        int transactionId = 1333;
        IosDevice device = new IosDevice();
        device.setSerial("C32JXYCGDTWD");

        File batteryApiFile = new File(BATTERY_API_FILE_NAME);

        when(osChecker.getOs()).thenReturn(Os.MAC);
        when(fileUtil.createFile(anyString())).thenReturn(batteryApiFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
            @Override
            public String getRootFolderPath() {
                return "./";
            }

            @Override
            public String getToolsRootFolderPath() {
                return null;
            }

            @Override
            public String getFilesRootFolderPath() {
                return null;
            }

            @Override
            public String getWorkingDirectoryPath() {
                return null;
            }
        });

        batteryService.createBatteryApiFileForIos(licenseId, transactionId, device);

        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFileIfDifferent(any(), anyString());
        batteryApiFile.delete();
    }

    @Test
    public void testCreateBatteryApiFileForAndroid() throws IOException {
        int licenseId = 1222;
        int transactionId = 1333;
        AndroidDevice device = new AndroidDevice();
        device.setSerial("RF8MA0DZN8M");

        File batteryApiFile = new File(BATTERY_API_FILE_NAME);

        when(osChecker.getOs()).thenReturn(Os.MAC);
        when(fileUtil.createFile(anyString())).thenReturn(batteryApiFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
            @Override
            public String getRootFolderPath() {
                return "./";
            }

            @Override
            public String getToolsRootFolderPath() {
                return null;
            }

            @Override
            public String getFilesRootFolderPath() {
                return null;
            }

            @Override
            public String getWorkingDirectoryPath() {
                return null;
            }
        });

        batteryService.createBatteryApiFileForAndroid(licenseId, transactionId, device);

        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFileIfDifferent(any(), anyString());
        batteryApiFile.delete();
    }
}