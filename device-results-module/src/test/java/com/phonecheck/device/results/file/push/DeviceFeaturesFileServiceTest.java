package com.phonecheck.device.results.file.push;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.DeviceFeaturesResponse;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.ISupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DeviceFeaturesFileServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private SupportFilePath supportFilePath;
    private final ObjectMapper mapper = new ObjectMapper();

    private DeviceFeaturesFileService deviceFeaturesFileService;

    @BeforeEach
    public void setup() {
        deviceFeaturesFileService = new DeviceFeaturesFileService(
                cloudApiRestClient, fileUtil, supportFilePath, mapper);
    }

    @Test
    public void testCreateDeviceFeatuersFile() throws IOException {
        DeviceFeaturesResponse testResponse = new DeviceFeaturesResponse();

        File testFile = new File("DeviceFeatures.json");
        when(fileUtil.createFile(anyString())).thenReturn(testFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
            @Override
            public String getRootFolderPath() {
                return ".";
            }

            @Override
            public String getToolsRootFolderPath() {
                return null;
            }

            @Override
            public String getFilesRootFolderPath() {
                return null;
            }

            @Override
            public String getWorkingDirectoryPath() {
                return null;
            }
        });

        deviceFeaturesFileService.createDeviceFeaturesFileForIos(testResponse, "serial");

        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFileIfDifferent(any(), anyString());
        testFile.delete();
    }

    @Test
    public void testGetDeviceFeatures() {
        DeviceFeaturesResponse testResponse = new DeviceFeaturesResponse();
        when(cloudApiRestClient.getDeviceFeatures("iPhone9,3")).
                thenReturn(testResponse);

        deviceFeaturesFileService.getDeviceFeaturesForIos("iPhone9,3");

        verify(cloudApiRestClient).getDeviceFeatures(eq("iPhone9,3"));
    }
}

