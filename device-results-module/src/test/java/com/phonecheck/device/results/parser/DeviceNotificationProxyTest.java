package com.phonecheck.device.results.parser;

import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.parser.device.test.DevicePostNotificationProxyParser;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class DeviceNotificationProxyTest {
    @Test
    void postDeviceNotificationProxySuccess() {
        final String output = "< posting \"VHUgaGkgbWVyaSBtYW56aWw=\"\n";

        final NotificationStatus status = new DevicePostNotificationProxyParser().parse(output);
        Assertions.assertNotNull(status);
        Assertions.assertEquals(NotificationStatus.SUCCESS, status);
    }

    @Test
    void deviceNotificationProxyFailed() {
        final String output = "< posting \"AccessIds\"\n" +
                "No device found, is it plugged in?\n" +
                "> \n";

        final NotificationStatus status = new DevicePostNotificationProxyParser().parse(output);
        Assertions.assertNotNull(status);
        Assertions.assertEquals(NotificationStatus.FAILED_NO_DEVICE, status);
    }

    @Test
    void deviceNotificationProxyFailedEmptyString() {
        final String output = "ERROR: Could not connect to lockdownd ";

        final NotificationStatus status = new DevicePostNotificationProxyParser().parse(output);
        Assertions.assertNotNull(status);
        Assertions.assertEquals(NotificationStatus.LOCKDOWN_FAILED, status);
    }

}
