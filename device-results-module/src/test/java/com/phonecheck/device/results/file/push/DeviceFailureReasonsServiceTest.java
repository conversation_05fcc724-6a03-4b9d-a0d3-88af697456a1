package com.phonecheck.device.results.file.push;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.ISupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DeviceFailureReasonsServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;
    private DeviceFailureReasonsService deviceFailureReasonsService;

    @BeforeEach
    public void setup() {
        deviceFailureReasonsService = new DeviceFailureReasonsService(cloudApiRestClient, supportFilePath, fileUtil);
    }

    @Test
    public void getFailureReasonstest() throws IOException {
        final String masterId = "1361";
        final String apiKey = "c7d53b75-4e08-428c-a557-e5e1e11a83c5";
        final String expectedOutput = "{\"LCD\":[\"fail\",\"hardstop\"," +
                "\"hardstop 2\",\"fail 2\"],\"Pass\":[\"pass\",\"pass 2\"," +
                "\"LCD LOOKS FINE\"],\"Hardstop\":[\"hardstop\",\"hardstop 2\"]," +
                "\"Config\":{\"add_to_test_result\":true,\"add_to_note_field\":true}}";

        Mockito.when(cloudApiRestClient.getDeviceFailureReasons(masterId, apiKey)).thenReturn(expectedOutput);
        File testFile = new File("test.json");
        when(fileUtil.createFile(anyString())).thenReturn(testFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
            @Override
            public String getRootFolderPath() {
                return "./";
            }

            @Override
            public String getToolsRootFolderPath() {
                return null;
            }

            @Override
            public String getFilesRootFolderPath() {
                return null;
            }

            @Override
            public String getWorkingDirectoryPath() {
                return null;
            }
        });

        deviceFailureReasonsService.createDeviceFailureReasonsFile(masterId, apiKey);

        verify(cloudApiRestClient).getDeviceFailureReasons(anyString(), anyString());
        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFileIfDifferent(any(), anyString());
        testFile.delete();
    }
}
