package com.phonecheck.port;

import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.port.PortMapping;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.port.mappers.MacPortMapper;
import com.phonecheck.port.mappers.PortMapperFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PortMappingServiceTest {
    private final InMemoryStore inMemoryStore = new InMemoryStore();
    @Mock
    private PortMapperFactory portMapperFactory;
    @Mock
    private MacPortMapper portMapper;

    private PortMappingService service;

    @BeforeEach
    public void setup() {
        when(portMapperFactory.getMapper()).thenReturn(portMapper);
        when(portMapper.getSavedPortMapping()).thenReturn(new PortMapping());
        service = new PortMappingService(inMemoryStore, portMapperFactory);
    }

    @Test
    public void testResetPortNumber() {
        inMemoryStore.setDeviceConnectionMode(DeviceConnectionMode.PROCESS);

        service.resetPortMapping();

        assertEquals(0, service.getMappedPortNumber());
        assertEquals(inMemoryStore.getDeviceConnectionMode(), DeviceConnectionMode.PORT_MAP);
    }

    @Test
    public void testMapUsbPortLocationForDeviceIdForHubPortMapper1() {
        String serial = "12345";
        String locationId = "0x12314123";

        when(portMapper.getDeviceUsbPortLocationId(serial)).thenReturn(locationId);

        service.resetPortMapping();

        assertTrue(service.mapUsbPortLocationForDeviceId(serial));
        assertEquals(1, service.getMappedPortNumber());
    }

    @Test
    public void testMapUsbPortLocationForDeviceIdForHubPortMapper2() {
        String serial = "12345";

        when(portMapper.getDeviceUsbPortLocationId(serial)).thenReturn("");

        service.resetPortMapping();

        assertFalse(service.mapUsbPortLocationForDeviceId(serial));
        assertEquals(0, service.getMappedPortNumber());
    }
}
