package com.phonecheck.port.mappers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.command.port.mac.GetMacUsbPortInfoCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.port.Port;
import com.phonecheck.model.port.PortMapping;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.MacSupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.parser.port.mac.MacUsbPortLocationIdParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class HubPortMapperTest {
    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private CommandExecutor executor;

    @Mock
    private MacUsbPortLocationIdParser macUsbPortLocationIdParser;

    @Mock
    private FileUtil fileUtil;

    @Mock
    private MacSupportFilePathsStrategy macSupportFilePathsStrategy;

    @Mock
    private SupportFilePath supportFilePath;

    private HubPortMapper hubPortMapper;

    @BeforeEach
    public void setup() {
        when(macSupportFilePathsStrategy.getRootFolderPath()).thenReturn("root");
        when(supportFilePath.getPaths()).thenReturn(macSupportFilePathsStrategy);

        hubPortMapper = new MacPortMapper(objectMapper,
                executor,
                macUsbPortLocationIdParser,
                fileUtil,
                supportFilePath);
    }

    @Test
    public void testIsMappingAvailable() {
        PortMapping portMapping = PortMapping.builder()
                .ports(List.of(new Port()))
                .build();

        hubPortMapper.savePortMapping(portMapping);

        assertTrue(hubPortMapper.isMappingAvailable());
    }

    @Test
    public void testGetPortIfPortMapped() throws Exception {
        String serial = "123";
        String locationId = "0x12314523";
        Port port = new Port();
        port.setPortNumber(10);
        port.setLocationId(locationId);

        PortMapping portMapping = PortMapping.builder()
                .ports(List.of(port))
                .build();

        hubPortMapper.savePortMapping(portMapping);

        when(executor.execute(any(GetMacUsbPortInfoCommand.class))).thenReturn("output");
        when(macUsbPortLocationIdParser.parse(eq("output"), eq(serial))).thenReturn(locationId);

        assertEquals(10, hubPortMapper.getMappedPort(serial));
    }

    @Test
    public void testGetPortIfPortNotMapped() throws Exception {
        String serial = "123";
        String locationId = "0x12314523";

        PortMapping portMapping = PortMapping.builder()
                .ports(new ArrayList<>())
                .build();

        hubPortMapper.savePortMapping(portMapping);

        when(executor.execute(any(GetMacUsbPortInfoCommand.class))).thenReturn("output");
        when(macUsbPortLocationIdParser.parse(eq("output"), eq(serial))).thenReturn(locationId);

        assertEquals(-1, hubPortMapper.getMappedPort(serial));
    }

    @Test
    public void testGetPortCount() {
        PortMapping portMapping = PortMapping.builder()
                .ports(Arrays.asList(new Port(), new Port()))
                .build();

        hubPortMapper.savePortMapping(portMapping);

        assertEquals(2, hubPortMapper.getMappedPortsCount());
    }

    @Test
    public void testGetDeviceUsbPortLocationId() throws Exception {
        String serial = "123";

        when(executor.execute(any(GetMacUsbPortInfoCommand.class))).thenReturn("output");
        when(macUsbPortLocationIdParser.parse(eq("output"), eq(serial))).thenReturn("0x12314523");

        assertEquals("0x12314523", hubPortMapper.getDeviceUsbPortLocationId(serial));
    }

    @Test
    public void testGetSavedPortMapping() {
        PortMapping portMapping = PortMapping.builder()
                .ports(List.of(new Port()))
                .build();

        hubPortMapper.savePortMapping(portMapping);

        assertEquals(portMapping, hubPortMapper.getSavedPortMapping());
    }

    @Test
    public void testSavePortMapping() throws Exception {
        PortMapping portMapping = PortMapping.builder()
                .ports(List.of(new Port()))
                .build();

        File file = mock(File.class);
        when(fileUtil.createFile(anyString())).thenReturn(file);
        when(objectMapper.writeValueAsString(any(PortMapping.class))).thenReturn("mapping json");

        hubPortMapper.savePortMapping(portMapping);

        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFile(eq(file), eq("mapping json"));
        verify(objectMapper).writeValueAsString(any(PortMapping.class));
    }
}
