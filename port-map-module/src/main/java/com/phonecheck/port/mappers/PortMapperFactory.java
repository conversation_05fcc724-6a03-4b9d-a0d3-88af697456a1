package com.phonecheck.port.mappers;


import com.phonecheck.model.util.OsChecker;
import com.phonecheck.port.util.PythonUtil;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Factory class to return a port map instance based on the
 * current platform and thunder sync customization.
 */
@Component
@AllArgsConstructor
public class PortMapperFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(PortMapperFactory.class);

    private final OsChecker osChecker;
    private final PythonUtil pythonUtil;
    private final ThunderSyncPortMapper thunderSyncPortMapper;
    private final WindowsPortMapper windowsPortMapper;
    private final MacPortMapper macPortMapper;

    /**
     * Method to get the port mapper instance.
     *
     * @return IPortMapper
     */
    public IPortMapper getMapper() {
        if (osChecker.isMac()) {
            int pythonVersion = pythonUtil.getInstalledPythonVersion();

            if (pythonVersion == 3) { // Only Python 3 is supported
                if (thunderSyncPortMapper.isMappingAvailable()) {
                    LOGGER.info("Thunder Sync Hub found connected");
                    return thunderSyncPortMapper;
                } else {
                    LOGGER.info("Thunder Sync Hub not available. Using default Mac Port mapping");
                    return macPortMapper;
                }
            } else {
                LOGGER.info("Unsupported Python version: {}. Using default Mac Port mapping", pythonVersion);
                return macPortMapper;
            }
        } else {
            return windowsPortMapper;
        }
    }
}
