package com.phonecheck.port.mappers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.port.Port;
import com.phonecheck.model.port.PortMapping;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Optional;

import static com.phonecheck.model.constants.FileConstants.PORT_MAPPING_FILE_NAME;

/**
 * Port Mapper to manually map the usb port locations with
 * port numbers. And store it in PortMapping.json file.
 */
@Service
public abstract class HubPortMapper implements IPortMapper {
    private static final Logger LOGGER = LoggerFactory.getLogger(HubPortMapper.class);
    private static final int LOCATION_ID_RETRIEVAL_INTERVAL = 1000; // Milliseconds
    private static final int LOCATION_ID_RETRIEVAL_RETRIES = 3;

    private final FileUtil fileUtil;
    private final ObjectMapper objectMapper;
    private final String portMappingFilePath;
    private PortMapping portMapping = null;

    public HubPortMapper(final ObjectMapper objectMapper,
                         final FileUtil fileUtil,
                         final SupportFilePath supportFilePath) {
        this.objectMapper = objectMapper;
        this.fileUtil = fileUtil;
        this.portMappingFilePath = String.join("/",
                supportFilePath.getPaths().getRootFolderPath(), PORT_MAPPING_FILE_NAME);

        loadExistingPortMapping();
    }

    /**
     * Method to get usb port location id for the device with provided
     * serial number.
     *
     * @param serial of connected device
     * @return location id
     */
    public abstract String getDeviceUsbPortLocationId(String serial);

    /**
     * Method to check if port mapping already exists or not.
     *
     * @return boolean
     */
    @Override
    public boolean isMappingAvailable() {
        return portMapping != null
                && portMapping.getPorts() != null
                && !portMapping.getPorts().isEmpty();
    }

    /**
     * Method to load port number for the given device serial number from saved
     * port mapping. It loads the location id of the port on which the device is
     * connected and then retrieves the port number saved against that location id.
     *
     * @param serial of connected device
     * @return port number
     */
    @Override
    public int getMappedPort(final String serial) {
        int portNumber = -1;
        String locationId = null;

        try {
            for (int i = 0; i < LOCATION_ID_RETRIEVAL_RETRIES; i++) {
                locationId = getDeviceUsbPortLocationId(serial);
                if (locationId != null) {
                    break;
                }

                try {
                    Thread.sleep(LOCATION_ID_RETRIEVAL_INTERVAL);
                } catch (Exception e) {
                    // do nothing
                }
            }

            if (locationId != null && isMappingAvailable()) {
                String finalLocationId = locationId;
                Optional<Port> mappedPort = portMapping.getPorts()
                        .stream()
                        .filter(p -> p.getLocationId().equals(finalLocationId))
                        .findFirst();

                if (mappedPort.isPresent()) {
                    portNumber = mappedPort.get().getPortNumber();
                } else {
                    // Suppose the port mapping was configured with a device with usb 2.0 support but now a device with
                    // usb 3.0 is connected on the same port the initials(0x141) of location id HEX code may change in
                    // a rare case but the last 5 characters will remain the same. For example, if usb 3.0 device is
                    // currently connected on port with location id '0x14112000', this location id may not match with
                    // saved location ids in the PortMapping.json file. We will get last 5 characters from HEX code
                    // i.e. '12000' and check if any key in saved json end with this '12000' that will correspond to
                    // the port number.
                    String locationIdConsistentPart = locationId.substring(5); // From index 5 to 10

                    for (Port port : portMapping.getPorts()) {
                        if (port.getLocationId().endsWith(locationIdConsistentPart)) {
                            portNumber = port.getPortNumber();
                            break;
                        }
                    }
                }

                // There is a case where user might have deleted the PortMapping.json file by accident after launching
                // PhoneCheck application, so we will create the PostMapping.json and save previously loaded mapping.
                File portMappingFile = new File(portMappingFilePath);
                if (!portMappingFile.exists()) {
                    savePortMapping(portMapping);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while retrieving port number for device: {}", serial, e);
        }

        return portNumber;
    }

    /**
     * Method to get the total number of ports saved in the mapping file.
     *
     * @return ports count
     */
    @Override
    public int getMappedPortsCount() {
        if (portMapping != null && portMapping.getPorts() != null) {
            return portMapping.getPorts().size();
        }
        return 0;
    }

    /**
     * Method to load the existing port mapping in PortMapping instance
     * variable from PortMapping.json file saved in Application support.
     */
    private void loadExistingPortMapping() {
        try {
            File portMappingFile = new File(portMappingFilePath);
            if (portMappingFile.exists()) {
                JsonNode portMappingJson = objectMapper.readTree(portMappingFile);

                if (portMappingJson != null && !portMappingJson.isNull() && !portMappingJson.isEmpty()) {
                    portMapping = objectMapper.treeToValue(portMappingJson, PortMapping.class);
                } else {
                    LOGGER.info("Port mapping file is empty.");
                }
            } else {
                LOGGER.info("Port mapping file does not exist.");
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while loading port mapping from file.", e);
        }
    }

    /**
     * Method to return the saved port mapping.
     *
     * @return PortMapping object
     */
    public PortMapping getSavedPortMapping() {
        LOGGER.info("Saved port mapping: {}", portMapping);
        return portMapping;
    }

    /**
     * Method to save new port mapping in PortMapping.json file.
     *
     * @param newPortMapping object to be saved in json file
     */
    public void savePortMapping(final PortMapping newPortMapping) {
        try {
            File portMappingFile = fileUtil.createFile(portMappingFilePath);
            fileUtil.writeStringToFile(portMappingFile, objectMapper.writeValueAsString(newPortMapping));

            portMapping = newPortMapping;
        } catch (Exception e) {
            LOGGER.error("Error occurred while saving port mapping json file.", e);
        }
    }
}
