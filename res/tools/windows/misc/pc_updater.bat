@echo off
SETLOCAL EnableExtensions EnableDelayedExpansion

:: Check for administrative privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrative privileges.
    exit /b 1
)

:: Kill Java processes
call :killJavaProcess "pc-daemon"
call :killJavaProcess "pc-backend"

:: Terminate PhoneCheck3.exe
taskkill /f /im PhoneCheck3.exe
timeout /t 5 /nobreak >nul

:: Configure paths
SET "ZIP_FILE=%~1"
SET "DEST_DIR=%~2"
SET "SUPPORT_PATH=%~3"

:: Replace \s with space
set "ZIP_FILE=!ZIP_FILE:\\s= !"
set "DEST_DIR=!DEST_DIR:\\s= !"
set "SUPPORT_PATH=!SUPPORT_PATH:\\s= !"

:: Extract files using 7-Zip
echo Extracting "%ZIP_FILE%" to "%DEST_DIR%"
"%SUPPORT_PATH%\7za.exe" x "%ZIP_FILE%" -o"%DEST_DIR%" -y

:: Start application with proper path handling
start "" "%DEST_DIR%\PhoneCheck3.exe"

ENDLOCAL
goto :eof

:killJavaProcess
powershell -Command "Get-WmiObject Win32_Process -Filter 'Name=\"java.exe\"' | Where-Object { $_.CommandLine -like \"*%~1*\" } | ForEach-Object { Stop-Process -Id $_.ProcessId -Force }"
goto :eof
