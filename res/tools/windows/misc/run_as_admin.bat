@echo off
SETLOCAL EnableDelayedExpansion

:: Check if the executable path is provided as the first argument
if "%~1"=="" (
    echo No executable path provided.
    exit /b 1
)

:: Set the executable path and parameters
set "EXE_PATH=%~1"
set "EXE_PATH=!EXE_PATH:\\s= !"

:: Combine up to 8 parameters into one string
set "PARAMS=%~2 %~3 %~4 %~5 %~6 %~7 %~8 %~9"
set "PARAMS=!PARAMS:"=\"!"

:: Define the temporary VBS file path
set "VBS_FILE=%temp%\elevate.vbs"

:: Create the VBS script to run the executable with elevated privileges
echo Set UAC = CreateObject^("Shell.Application"^) > "%VBS_FILE%"
echo UAC.ShellExecute "%EXE_PATH%", "%PARAMS%", "", "runas", 1 >> "%VBS_FILE%"

:: Execute the VBS script without window popup
cscript //nologo "%VBS_FILE%"

:: Clean up the temporary VBS file
del "%VBS_FILE%"

endlocal
