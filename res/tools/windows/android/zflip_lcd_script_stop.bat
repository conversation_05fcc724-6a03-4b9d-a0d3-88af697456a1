@echo off
:: Check if the device ID parameter is provided
if "%~1"=="" (
    echo Usage: zflip_lcd_script_stop.bat [Device ID]
    exit /b 1
)
:: Assign the device ID parameter to a variable
set device_id=%~1
:: Run adb commands with the device ID specified
adb -s %device_id% shell settings put secure enabled_accessibility_services com.android.shell/com.android.shell.ShellAccessibilityService
adb -s %device_id% shell am force-stop com.sec.android.app.hwmoduletest
adb -s %device_id% shell am force-stop com.samsung.android.dialer
:: Success message and exit
echo success
exit /b 0