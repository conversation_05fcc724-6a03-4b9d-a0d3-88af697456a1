MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� "=�g D #	  � & ( 8   x     �        @                            ��  `                                             �  X   �  �   �               �                           �g  (                   X�  @                          .text   �6      8                 `  `.data   �   P      >              @  �.rdata  �   `      B              @  @.pdata     �      X              @  @.xdata  �   �      \              @  @.bss    �   �                      �  �.idata  X   �      `              @  �.CRT    `    �      r              @  �.tls        �      t              @  �.rsrc   �   �      v              @  �.reloc  �          |              @  B/4      `        ~              @  B/19     �       �              @  B/31     ,   0  .   �             @  B/45     �3   `  4   �             @  B/57     H
   �     �             @  B/70     e   �     �             @  B/81     �   �                   @  B/97     �!   �  "                @  B/113    �        @             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��\  1��    H��\  �    H��\  �    H�,\  f�8MZuHcP<HЁ8PE  tfH�o\  �
��  � ��tC�   �'  �4&  H�-]  ���&  H��\  ���T  H��[  �8tP1�H��(Ð�   �&  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�\  �\
  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��\  L�֎  H�׎  H�
؎  � ���  H�A\  D�H���  H�D$ ��"  �H��8��    ATUWVSH�� H��[  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5l[  1�����V  �����  ��     ����L  ���e  H��Z  H� H��tE1��   1����  H�
�[  ��  H��Z  H�
����H��5%  �  �ҍ  �{Hc�H��H���%  L�%��  H�Ņ��F  H��1�I��$  H�pH���c%  I��H�D I�H��H���%  H9�u�H�H�    H�-]�  �  H��Y  L�B�  �
L�  H� L� H�7�  �%  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5Z  �   ���������   �/"  ��������H�
Z  H�
�Y  �$  �   �������1�H�����f�     �#  ���  H�� [^_]A\�f.�     H��Y  H�
�Y  �   �#  �7���f�H�����������#  �H��(H��X  �    ������H��(� H��(H��X  �     �z�����H��(� H��(�!  H���H��(Ð�����������H�
	   �����@ Ð��������������M��I�к   �"  H��8H�T$(L�D$$H�D$(    �D$$    �l  H�L$(H��t�m"  �H��8��    VSH��(�ֺ/   H���#  H�PH��HEڅ�tGH�5ա  �   ��I��H�$K  H���  �   ��I��A�T  �   H�
8K  H��([^��!  H�5��  �   ��I��H��J  H����  �   ��I��뷐���������%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%Ҟ  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%�  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ��H��(H��:  H� H��t"D  ��H��:  H�PH�@H��:  H��u�H��(�fD  VSH��(H��U  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�c��� 1�fD  D�@��J�<� L��u��fD  �z�  ��t�D  �f�     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�U  �8t�    ��t��tN�   H��([^�f�H��  H�5�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�Q  Hc�H����    H��O  �DA �y�qH�q�   �s  �DD$0I��H��P  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH�iO  ��    H��O  ��    H��O  �s���@ H��O  �c���@ H��O  �S���H�P  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�  A�   �   H�
P  I����  H�t$(�   �k  H��H��I���  �@  ��    WVSH��PHc5V�  H�˅��  H�H�  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H���  H��H��H�H�x �     �#  �WA�0   H�H�ǆ  H�T$ H�L�g�  H���}   �D$D�P����t�P���u���  H��P[^_� ��H�L$ H�T$8A�@   �   DD�He�  H�KI��H�S���  ��u��  H�
#O  ���d���@ 1��!���H�*�  �WH�
�N  L�D�>���H��H�
�N  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%ԅ  E��tH�e[^_A\A]A^A_]�fD  ���     �9	  H�H��H��   H����  L�-[Q  H�dQ  �~�      H)�H�D$0H�s�  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�P  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�M  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �ރ  ������H�5{�  1�H�}�D  H���  H�D� E��t
H�PH�HI����A��H��(D;%��  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�N  �s�;H��L�>H���Z����>L9�r��������H�
�K  �����H�
�K  ���������H��XH���  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
Y�  �d  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��K  Hc�H��� 1ҹ   ��  H���>  H���  H���  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �q  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �  H���@����   �   ��  �f�     1ҹ   ��  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   �  �,����   �   �u  �����������ATUWVSH�� L�%߀  L���֔  H���  H��t6H�-��  H�=̔  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%��  WVSH�� �[�  ��H�օ�u
1�H�� [^_ú   �   �  H��H��t3H�pH�5>�  �8H���3�  H��  H��H��  H�C�8�  묃��멐VSH��(��  �˅�u1�H��([^�D  H�5�  H�����  H�
�  H��t'1��H��H��tH���9�H�Au�H��tH�B�}  H���ē  1�H��([^� H�q  ��ff.�     @ SH�� ����   w0��tL�N  ����   �<     �   H�� [�f�     ��u�  ��t��<�����f.�     �  ��uf��~  ��u�H��~  H��t�    H��H�[�  H��u�H�
�~  H��~      ��~      ���  �l����k����   H�� [������f�     H�
�~  ���  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���a  H��w{H�4I  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H����  ��u�H��H�� [^_��    1�H��H�� [^_� H��H  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�)H  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��G  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�iG  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�)G  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��F  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�@  �   H� H�8�  H��B  I��H���  �   �  I��H��H����
  �   �  �
   H���  �H��0[^_�ff.�      AWAVAUATUWVSH��H�&)  H�=)  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��(  �*  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���
  ��u�L���l
  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��'  ���.  ��'      �?   H��H[^_]A\A]A^A_�fD  D�
�'  E��t�H��$�   �8:t�I����H�
�A  �����@ L�D$0Ic�H��L�k��uRM��teD�f'  E��tH��$�   �8:��   H�{ �l  H��$�   �-/'  �8:�Z����:   �U���@ �U���wM����   L�w  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(��  L�D$(H�������fD  H��$�   �8:�����H��H�
@  ���������I����H�
?@  �����%������d���HcT$8H��$�   D�t$<H��A��D�5G&  H�Tv  H���3����
5&  ��t)H��$�   �8:tH��H�
:@  �E����&  ���D$81�H�{ tB��%  �D$8��%  H��$�   �8:�
��������k�����D$<��%  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
x%  �V%  E����  �����  �vu  ����  A�E <-�  D�"%  E����  <+��  H�6u      ����   ��$  ������$  ����f.�     �u      ��$  D9���  Hc�H�t� �>-��  H�7>  H��$  A����  A����  �5�$  ����5  �=�$  ���t%��I��A�؉������)����a$  ����)��]$  ���h$  H�=U$  ����Z���M��tkHcJ$  H9|� t]<-�r  A���w  <:�k  ��L���b
  H�������D$ M��L��H��L��$�   �����Ã����  H�=�#  �L���L�=�#  <:�,  ��-��   ��L���
  H���  �PM��t��Wu	��;��  ��:��   � ��  ��#  �}  f���s     �h#     1�E1�H�
�<  ��  �ls  �������&#  A�E <-�����A��I�������@ �F����  �-   L���O	  H���0����=�"  �t
�=�"  ���  L�~H���-   L�=�"  � �8  �-   L���	  H����   �x:�%���H��r      � ��"  ��  L�=�r  H��;  ��H�p"  �v"  �s�     �R"  �����D  A���<+�#��������    �5&"  H��;  �="  H�"  ���uP���t�="  ��!  ������!  �����������H��H[^_]A\A]A^A_�f���H�5�q  ��!  �   �ԉ�)�A��I���)��&�����!  �L���L�=�!  <:������ u��!  �
�!  ��tA�} :��   �x!  �?   �q���fD  A����2����
I!  ���t
�=9!  ��  H�~H�=4!  <-������~ ������5!  ��H��:  �!  H�!  ��������A�؉�I��L$<�[����L$<)�)��   ������     ��H�
g:  �����G���H��H�=�   1������ ��   ��   ����   D9�|e��   H��9  H�y   ��tA�} :t�W   H�
�:  �����[   W   A�} :������:   �I����,   �S����!   �����H�H�D� H�   �D$     M��L��H��L��$�   �������H�k9  H��  ������x:�\�������  A9�~Hc�H�D� H��o  �:���H�,9  H��  ��  ��tA�} :t��H�
:  �������  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�ATUWVSH��0H��H�l$pH��L�D$pH��H��L�L$xH�l$(�   �Å�x2D�`Mc�L���D  H�H��H��t%L��I��I���C   H�Hc�� ��H��0[^_]A\Ã��������H��8E1�L�D$ I��H��1��  H��8Ð�H��8L�L$(M��I��H��H�D$     �   �  H��8Ð�����H��8E1�H�T$(I��1ҹ   H�D$     �d  H��8Ð������H��HH�D$`L�D$`I������H�D$(H�D$     L�L$hI��H��1�H�D$8�&  H��HÐVSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �  � ��  H� H��w  H� H�M��t	A�$�3  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���  ���   ����H  �  � ��"  H� H��  H� H�M��t	A�$��  1�H�� [^_]A\�fD  SH�� H���  ���    HD�H�� [�f�H��7  �8 t1�Ð��  ff.�     SH�� �˹   �  A��H��6  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H��6  ��~   H�W  �j   H�C  �V   H�/  H��(�f.�     H��(H��6  ��>   H�  �*   H�  �   H��  H��(Ð����������%Ҁ  ���%Ҁ  ���%Ҁ  ���     �%��  ���%��  ���%��  ���%��  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���%�  ���     �%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���     �%"~  ���%"~  ���%"~  ���%"~  ���%�}  ���     �%�}  ���%�}  ���%�}  ���%�}  ���%�}  ���     �%b}  ���%b}  ���%B}  ���%2}  ���%"}  ���%}  ���%}  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���     AW��@  AVAUATUWVS�
���H)�L�-`  L�%�(  H�-.  ��H������H�D$h    H�D$p    H�D$x    HǄ$�       HǄ$�       HǄ$�       f�     M��M��H���H�D$     �����Ã����   ��d����  HcD� H���fD  ��f     �@ H��4  H� �8 ��  H��f  �@ H�y4  H� H��f  �z���f.�     ��f     �a������f     �Q����H�1������1�������    �   ������)���H�"4  �=cf   Hc ��  )ǅ��z  H�,�H�M �{�����H����  H�M�f���H��$�   �=f  H�f  H�L$h����D�@�]�������  H�L$hH�T$pL��'  �'������}  H�L$p1�L�(  1�L��$�   H��$�   H��$�   �����H��$�   H��t�X������  H��$�   E1�D�|$\D�|$`H���  L�L$`L�D$\1�H��'  ��������  �|$\H�L$puIE1�L��$�   L��'  Ƅ$�    H��'  L��$�   �V�������  ��$�    �H  H�L$pH��$�   H��'  I������H��$�   H��t
f�: ��   H�
�'  ����H��$�   H��t�O���H�L$pH��t�����H�L$h�����H��tH���9���H��$�   H��t�'�����H���@  [^_]A\A]A^A_�1��<�����H�
Y&  ����뉹   ��z  A�   �   H�
�%  I�������H��   �x����   �^���H�L$hL�D$x�������  H��$�   H��t����E1�L��$�   �=�c   ��  ��unH�L$pI��H�L'  ���������  H��$�   H����  f�: ��  H�L$hL��$�   ��������  H��$�   H��t����E1�L��$�   L��$�   H�-�x  H��L���Յ���  L��$�   H��$�   L���Յ��  H�L$p1��c����=c   H�l$pH��$�   �R  H�=�b   ��  H��b  H�L$xL��$�   ������Å���  �=�b   �f  H��$�   �����H��$�   H��t�l���H�L$x����H�L$x���������1��|$\@���#���H��$�   H��$�   ���������1��R���H��$�   I��H��#  �k���������H�
�#  ���������H��$�   H��$�   �����H��$�   ����������E1������H�
L%  �����!����   �7x  A�   �   H�
e%  I���u��������H�
9$  �l��������H��$�   L�=�%  L���0���I��H����  L��$�   I��A�    �   L������L��H�������H���w  L��H�������I��H���  L�=�%  E1�H��$�   L�
�%  L�K(  L��L��$�   �;�������  L��$�   E1�L��H��$�   L��'  L��$�   �	������n  H�=�`   ��  ���  L��$�   H��H�
%  H�-�'  ����H��$�   E1�H��L��$�   L��$�   ���������  1�H��$�    u
�"H���
���H��$�   H�)H��H��u������E1�H��$�   H��$�   L��$�   L��$�   A�   ��������  H��$�    L��$�   ��  M��A�    �   L������H��H����   H��$�   H��$�   �L���L���L���H�
u%  �h���������u  �������   H����u  I��I��H�V#  H���3�������H�
�%  �"����=[_   ������   ��u  H��$�   H�������H��$�   ����H�
&#  �������H��$�   �i���H� _  H����  H�
�   �c����w���H��"  H��^  �����H�D$dA��E1�H�D$H�
D|$dE��I9�sYH�D$H1�A��O�D5 �L$dH��$�   E)�H�D$ H��$�   ������t��   ��t  A�   �   H�
�#  I������I9��g����   ��t  A��E��H��#  H�������H��$�   H��$�   ����L�������W����t  ��k���H��$�   �   H���Ut  I��I��H��!  H����������H��$�   �   �(t  H�"  H��I���v����������s  �����H��$�   �   H����s  I��I��H��!  H���9�������H�
L  �(���������   ��s  A�   �   H�
�!  I��������x����3s  ������   H���~s  I��I��H��!  H��������C���H��   H��\  �.���H��$�   H���Q������h����   �.s  I��H��!  H���|����F���H��H�
v!  ������L�d$0M��M��H������l$ H�L$xH�D$(H�n\  �y���L�����������  H�
T"  ����H�
N"  ����H�L$xA��M��H��$�   H��$�   H�D$(H�\  H�D$ �.������W  H��$�   H���]���H�"  �����H��H��tW1�H��$�   H��$�   ����H��$�   H���g  H��!  �����Å��'  H�
�!  �e���H��$�   �H���H��$�   H��!  �\���H��H�������1�H��$�   H��$�   ����H��$�   H�������H�
�!  ����H��$�   �����H��$�   ����L������H��$�   �   �oq  H�8   H��I�������7����   �Mq  A�   �   H�
�  I�������
���H�
?  �����F���H�
!  �!���H��$�   �������������H�
   �O��������H�
�   �>����=wZ   t<H��$�   �����������H�
�   �����=MZ   t/H��$�   �^��������   ��p  H��$�   H�������봹   �qp  H��$�   H�������d���������������������������������������PF @           ��������                                                                                                                                                                                                                                                                                                                                                                                                g @                   h       g @                  u       g @                   n       &g @                   l       +g @                  t       5g @                   x       9g @                   d       ?g @                   v                                       �F @           ��������        ����                           ������������    �j @   ?                     ����            �2 @           3 @           @3 @           p3 @           �3 @            4 @           �� @   �� @   �5 @   �5 @   @4 @   p5 @   �4 @   P4 @   XR @   \R @   `R @      �p  tR @   pR @   PDT PST P5 @   05 @                                                                                                                                                                                                                                                                                                                                                                                           Usage: %s [OPTIONS] IMAGE_FILE IMAGE_SIGNATURE_FILE
    
Mounts the specified disk image on the device.

OPTIONS:
  -u, --udid UDID       target specific device by UDID
  -n, --network         connect to network device
  -l, --list            List mount information
  -t, --imagetype TYPE  Image type to use, default is 'Developer'
  -h, --help            prints usage information

PhoneCheck 2.0 hu:lt:xdnv ERROR: UDID must not be empty!
 ERROR: No IMAGE_FILE has been given! %s.signature Out of memory?!       No device found with udid %s.
 No device found. ideviceimagemounter     ERROR: Could not connect to lockdown, error code %d.
 ProductVersion %d.%d.%*d DeveloperModeStatus com.apple.security.mac.amfi  ERROR: You have to enable Developer Mode on the given device in order to allowing mounting a developer disk image.      com.apple.mobile.mobile_image_mounter   ERROR: Could not start mobile_image_mounter service!    ERROR: Could not connect to mobile_image_mounter! com.apple.afc Could not start com.apple.afc!
 Could not connect to AFC!
 ERROR: stat: %s: %s
 Developer       Error: lookup_image returned %d
 rb     Error opening signature file '%s': %s
  Could not read signature from file '%s'
        Error opening image file '%s': %s
 staging.dimage %s/%s Out of memory!?
 Uploading %s
 Uploading %s --> afc:///%s
      WARNING: Could not create directory '%s' on device!
    afc_file_open on '%s' failed!
 AFC Write error!
 Error: wrote only %d of %d
    ERROR: Device is locked, can't mount. Unlock device and try again.      ERROR: Unknown error occurred, can't mount. done. Mounting... Status Complete Done. unexpected status value: unexpected result: Error Error: %s
        Error: mount_image returned %d
 ���[���[���[�������[���[���[�������[�������[���[���[���[���[�����������[���[�������help udid network list imagetype xml debug version          /private/var/mobile/Media       PublicStaging   0 @                            � @   � @   �� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ����l������������������|���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      @���@���@���@���@�������@��� �����������                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               PQ @           �Q @           `F @              @           �t @           �t @           �g @           R @           P� @           �� @           �� @           �� @           �� @            � @           � @           `� @           h� @            � @           � @           � @           (� @           �� @           @Q @           Р @           p @           � @           p� @           �� @           |Q @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  `  t�  `  �  x�  �  7  ��     Z  ��  `  �  ��  �  �  ��  �  �  ��     /  ��  0  �  Ȑ  �  �  Ԑ  �  �  ؐ  �  �  �  �  I  ��  P  �   �  �    �     ^  $�  `  l  ,�  p  -   0�  0   �   8�  �   !  H�  !  �!  T�  �!  �"  `�  �"  �"  h�  �"   #  l�   #  �#  p�  �#  @$  |�  @$  w$  ��  �$  �$  ��   %  6%  ��  @%  �%  ��  �%  �&  ��  �&  �'  ��  �'  2(  ��  @(  �+  ��  �+  �1  ȑ   2  "2  ��  02  P2  �  P2  p2  �  p2  �2  ��  �2  3  �  3  :3  �  @3  i3  �  p3  �3   �  �3  �3  (�   4  24  4�  @4  C4  <�  P4  �4  @�  �4  *5  P�  05  N5  `�  P5  e5  h�  p5  �5  l�  �5  �5  t�  �5  6  |�   6  V6  ��  �8  DF  ��  PF  UF  ��                                                                                                                                                                                                                                                          B   b  
 
20`pP�	 B  �7     �  �  p  �  	 B  �7     �    p     B            b   B0`  
 0`pP
��	�� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b  
 
R0`pP� b   b   b   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                     �          ��  X�  ȱ          ľ  �  ر          �  �  8�          (�  x�  P�          T�  ��  `�          ��  ��  ��          ��  ȵ  ��          �  ص  ��          P�   �  `�          ��  ��  г          ��  �  ��          �  8�   �          D�  `�                      ��      ��      ��      Է      �      ��      �      $�      4�      P�      l�      ��      ��      ĸ      �      �       �      @�      d�      ��      ��              ȹ              �      ��      �      *�      :�      V�      n�      ��      ��      ��      ��              ĺ      Ժ              �              �      ��      �      �              �              .�      F�      P�      Z�              d�      r�      ��      ��      ��      ��      ʻ      �      �      ��       �      "�      B�      N�      ^�      ��      ��      ��      ��              ��      ��      ȼ      ּ      �      �      &�      @�      J�      R�      Z�      b�      l�              t�      ~�      ��      ��              ��      ��      ��      Ľ              н      �      ��      �      (�      @�              ��      ��      ��      Է      �      ��      �      $�      4�      P�      l�      ��      ��      ĸ      �      �       �      @�      d�      ��      ��              ȹ              �      ��      �      *�      :�      V�      n�      ��      ��      ��      ��              ĺ      Ժ              �              �      ��      �      �              �              .�      F�      P�      Z�              d�      r�      ��      ��      ��      ��      ʻ      �      �      ��       �      "�      B�      N�      ^�      ��      ��      ��      ��              ��      ��      ȼ      ּ      �      �      &�      @�      J�      R�      Z�      b�      l�              t�      ~�      ��      ��              ��      ��      ��      Ľ              н      �      ��      �      (�      @�               afc_client_free    afc_client_new     afc_file_close    	 afc_file_open  afc_file_write     afc_get_file_info  afc_make_directory    f idevice_free  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_get_value   � lockdownd_service_descriptor_free � lockdownd_start_service   � mobile_image_mounter_free � mobile_image_mounter_hangup   � mobile_image_mounter_lookup_image � mobile_image_mounter_mount_image  � mobile_image_mounter_new  � mobile_image_mounter_upload_image  plist_print_to_stream DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron & _stat64  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr  __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit # _errno  % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal  h strerror   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsprintf  __stdio_common_vsscanf  � fclose  � fopen � fputc � fread � fwrite  � puts  6 _strdup � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     plist_dict_get_item    plist_free    # plist_get_bool_val    ( plist_get_node_type   , plist_get_string_val  L plist_to_xml   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  libimobiledevice-glue-1.0.dll   (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  api-ms-win-crt-filesystem-l1-1-0.dll    d�  d�  d�  d�  api-ms-win-crt-heap-l1-1-0.dll  x�  api-ms-win-crt-math-l1-1-0.dll  ��  ��  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll Ȱ  Ȱ  Ȱ  Ȱ  api-ms-win-crt-string-l1-1-0.dll    ܰ  ܰ  ܰ  ܰ  api-ms-win-crt-time-l1-1-0.dll  �  �  �  �  �  �  libplist-2.0.dll                                                                                                                                                                                    0 @                    @                   0 @     @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     h�   P  H    � �@�`�������� �p�������С�� ���� �(�0�8�@�H�P�`�h�x���   `  L   ����������@�P�`�p�����������Ь�� �� �0�@�P�`�p�����������Э�� � �     � �8�@�                                                                                                                                                                                                                                                                                                                                                ,               @   $                      <    �&       P @   �       �8 @   �
                      ,    tY         @   �                           �_                           �e                       ,    df       � @                              �g                       ,    oh         @   �                           6p                           �p                       ,    ;r       � @   �                       ,    qu       � @                              v                       ,    �v       � @   =                      ,    �         @   L                           ڐ                       ,    b�       p @   �                      ,    C�       0  @   b                          �                           ��                       ,    o�       �" @   �                      ,    A�       �& @   �                          }�                       ,    �       p2 @   y                       ,    ��       �2 @                          ,    ��       3 @   *                       ,    7�       @3 @   )                       ,    ��       p3 @   ?                       ,    �       �3 @   H                       ,    ��        4 @   2                           n�                       ,    �       @4 @                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �2   �  ,GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  l          9  char {   size_t #,�   long long unsigned int ssize_t -#�   long long int short unsigned int int long int __time64_t {#�   unsigned int unsigned char double float long double 	�   _iobuf !
}  _Placeholder #}    -}  FILE /V  #optind �   #optarg �  	{   �  option  >  name @   has_arg A�   flag BQ  val C�    �  	�       GU  no_argument  required_argument optional_argument  short int signed char uint8_t $  uint16_t &�   uint32_t (  uint64_t *0�   _ino_t +�   _dev_t 3  long unsigned int _stat64 8S
�  st_dev T�   st_ino U�  st_mode V�   st_nlink WU  st_uid XU  
st_gid YU  st_rdev Z�  st_size [�   st_atime \�    st_mtime ]�   (st_ctime ^�   0 plist_t Y}    ir  PLIST_BOOLEAN  PLIST_UINT PLIST_REAL PLIST_STRING PLIST_ARRAY PLIST_DICT PLIST_DATE PLIST_DATA PLIST_KEY PLIST_UID 	PLIST_NULL 
PLIST_NONE  plist_type v�  �   |  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ plist_err_t ��  �   	'�  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 	0   o  	2   o  idevice_t 	3#  	   $idevice_options   	9�  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  �   
${  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t 
P�    
R)�    lockdownd_client_t 
S#�  	�  lockdownd_service_descriptor 
`$  port 
a�   ssl_enabled 
b
q  identifier 
c�   lockdownd_service_descriptor_t 
e.K  	�  �   &�  AFC_E_SUCCESS  AFC_E_UNKNOWN_ERROR AFC_E_OP_HEADER_INVALID AFC_E_NO_RESOURCES AFC_E_READ_ERROR AFC_E_WRITE_ERROR AFC_E_UNKNOWN_PACKET_TYPE AFC_E_INVALID_ARG AFC_E_OBJECT_NOT_FOUND AFC_E_OBJECT_IS_DIR 	AFC_E_PERM_DENIED 
AFC_E_SERVICE_NOT_CONNECTED AFC_E_OP_TIMEOUT AFC_E_TOO_MUCH_DATA 
AFC_E_END_OF_DATA AFC_E_OP_NOT_SUPPORTED AFC_E_OBJECT_EXISTS AFC_E_OBJECT_BUSY AFC_E_NO_SPACE_LEFT AFC_E_OP_WOULD_BLOCK AFC_E_IO_ERROR AFC_E_OP_INTERRUPTED AFC_E_OP_IN_PROGRESS AFC_E_INTERNAL_ERROR AFC_E_MUX_ERROR AFC_E_NO_MEM AFC_E_NOT_ENOUGH_DATA  AFC_E_DIR_NOT_EMPTY !AFC_E_FORCE_SIGNED_TYPE  afc_error_t DP    Ge  AFC_FOPEN_RDONLY AFC_FOPEN_RW AFC_FOPEN_WRONLY AFC_FOPEN_WR AFC_FOPEN_APPEND AFC_FOPEN_RDAPPEND  afc_file_mode_t N�  8  ]#�  8  afc_client_t ^�  	}  �   &�  MOBILE_IMAGE_MOUNTER_E_SUCCESS  MOBILE_IMAGE_MOUNTER_E_INVALID_ARG MOBILE_IMAGE_MOUNTER_E_PLIST_ERROR ~MOBILE_IMAGE_MOUNTER_E_CONN_FAILED }MOBILE_IMAGE_MOUNTER_E_COMMAND_FAILED |MOBILE_IMAGE_MOUNTER_E_DEVICE_LOCKED {MOBILE_IMAGE_MOUNTER_E_UNKNOWN_ERROR �~ mobile_image_mounter_error_t .�  K  04�  K  mobile_image_mounter_client_t 1.  	�  mobile_image_mounter_upload_cb_t 4G  	L  .�   e  }  �   }   _Float16 __bf16 %JOB_OBJECT_NET_RATE_CONTROL_FLAGS   
�M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	�  M  $tagCOINITBASE   ��  COINITBASE_MULTITHREADED   %VARENUM   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
list_mode $�   	H� @   
use_network %�   	D� @   
xml_mode &�   	@� @   
udid '  	8� @   
imagetype (  	0� @   �   �   �   
 �  
PKG_PATH *�  	pg @   �   �   �    �  
PATH_PREFIX +�  	Pg @     -L  DISK_IMAGE_UPLOAD_TYPE_AFC  DISK_IMAGE_UPLOAD_TYPE_UPLOAD_IMAGE  disk_image_upload_type_t 0�  
puts ��   �     
plist_to_xml �  �  �  M  �   	�  strrchr ]�  �    �    !idevice_set_debug_level 	l�  �    /exit � 
  �    getopt_long M�   ?  �   ?    D  Q   	�  	  idevice_free 	��  h     afc_client_free �
�  �  �   mobile_image_mounter_free _�  �  �   mobile_image_mounter_hangup ��  �  �   strcmp ?�          
plist_dict_get_item �
�  .  �     mobile_image_mounter_mount_image ��  z  �      �    z   	�  afc_file_close �
�  �  �  �   afc_file_write �
�  �  �  �    �  �   afc_file_open �
�  	  �    e  	   	�  "free "  }   
afc_make_directory ,
�  M  �     afc_get_file_info �
�  {  �    {   	M  mobile_image_mounter_upload_image ��  �  �    �     �    }   
fclose j�   �  �   	�  �  
fread ��       �   �   �   
fopen �  <       !plist_print_to_stream ;e  �  �   mobile_image_mounter_lookup_image o�  �  �    z   lockdownd_client_free 
�{  �  �   
fprintf ��   �  �     strerror R�    �    0_errno �Q  _stat64 a�   5    5   	�  afc_client_new n
�  e    $  e   	�  __acrt_iob_func ]�  �     
lockdownd_service_descriptor_free 
>{  �  $   mobile_image_mounter_new E�  �    $  �   	�  lockdownd_start_service 
�{  /  �    /   	$  !plist_free �
M  �   "plist_get_bool_val �
t  �  t   	q  
sscanf �   �       "plist_get_string_val �
�  �  M   
plist_get_node_type �r  �  �   lockdownd_get_value 
�{    �      z   lockdownd_client_new_with_handshake 
�{  ^    ^     	�  
printf ��   ~     idevice_new_with_options 	��  �  �    (   	  
asprintf �   �  R     strdup l�  �     1main ��   �8 @   �
      ��/  argc ��   �  �  argv �M  >  0  
device �  ���~
lckd ��  ���~ldret �{  �  �  
mim � �  ���~
afc ��  ���~
service �!$  ���~res ��   D  ,  image_path ��  �  �  image_size �	�   9    
image_sig_path ��  ���~&leave �; @   
pver �
�  ���~
product_version ��  ���~disk_image_upload_type �L  �  �  
product_version_major ��   ���~
product_version_minor ��   ���~err �  �  �  result 
�  ���~&error_out ��= @   �   �   
dev_mode_status �q  ���~
val ��  ���*; @   �  [   Q	�b @   X	�b @   Y��� 3> @   M  u   Q���~ @> @   4  �> @   �2  R	�b @     �   n"  
fst ��  ����< @   �  �   Q	�c @   Xv  �< @   :  �   X���~ �< @   �  �< @     !!  Rt Q}  = @     9!  Q}  i> @   j  P!  R2 �> @   �2  y!  R	�c @   Q1XO �@ @     �@ @   �  �@ @   j  �!  R2 �@ @   �  �!  Q	d @   Xt Yu  .B @     5B @   �  KB @   j  "  R2 `B @   �  1"  Q	d @   Xv Yu  SE @   j  H"  R2 mE @   �2  R	 d @   Q1XJ  �   [+  sig �/  ���~sig_length  
�   L  >  f !	�  �  �  targetname 3	�  ���~mountname 8	�  ���~J  *&  strs KM  ���~af Z�  ���~buf b
�/  ���amount c�     �  '�? @   "       y#  i R
�   .  (  �? @      @ @      'jA @   �       �$  written g�  ���~total g�  U  S  �A @   �  �#  X} ~ "Yv  w ���~ �A @   j  �#  R2 �A @   �2  '$  R	�e @   Q1XA �A @   j  >$  R2 B @   �  i$  Q	�e @   X Yv  B @     #B @   �  R|   �? @   c  �$  R	e @   Qt  �? @   M  �$  Qv X���~ .@ @   �  �$  X3Y���~ c@ @   �  %  R} Q1X
  Y|  �@ @     _C @   "  >%  Qv  rC @   j  U%  R2 �C @   �  z%  Q	8e @   Xv  �C @   c  �%  R		e @   Qt  �C @   �  �%  X~ Y} w v w(	P @   w0|  E @   �  �%  R|  1E @   j  &  R2 CE @   �  Q	pe @   Xu      Y(  node �
�  i  c  3  k'  status ��  ���hD @   �  &  Q��� �D @   �  �&  Q	Mf @    �D @   �2  �&  R	Vf @    �D @     �E @   �2  �&  R	\f @    �E @   90  �E @   �2  '  R	uf @    F @   90  F @   j  9'  R1 "F @   <  /F @   j  ]'  R1 ?F @   <   !  �'  error ��  ����@ @   �2  �'  R	uf @    �D @   �  �'  Q��� �D @   c  �'  R	�f @    	E @      �= @   90  �@ @   j  (  R1 A @   <  ID @     =(  Q	Ff @    �D @     Q	�f @     �> @     q(  Q  �> @   �  �(  R} Q1X
  Y|  �> @   �  �(  R|  �> @     �(  Rt Q  5? @   �  )  R���~Q X	pg @   Y	�d @    g? @   �  8)  R���~Q X	Pg @    �@ @   �  P)  R|  �@ @   �2  o)  R	f @    xB @   j  �)  R2 �B @   �  �)  Q	�d @   Xu  �B @     �B @   �  �B @   j  �)  R2 �B @   �  *  Q	hd @   Xv Yu  �B @   j  *  R2 C @   �2  G*  R	�d @   Q1X@ 
C @     C @   �  "C @   j  x*  R2 7C @   �  �*  Q	�d @   Xt Yu  �C @   �  �*  R|  �C @   �2  �*  R	4f @    �C @   �2  �*  R	:f @    "D @   .   +  X} Yv w(���~ �E @   c  ?+  R	�f @    �E @   �2  R	�e @     2�0  �8 @    h   ��,  �0  �  �  �0  �  �  3~   (1  �  �  9 @   
  �+  Ru Qt X| Y} w 0 �9 @   d1  �+  Q0/1  u ;1  t  �9 @   �  ,  R0 �9 @   �  &,  R1 < @   j  =,  R2 < @   �2  f,  R	�a @   Q1XO (< @   d1  �,  Q1/1  u ;1  t  2< @   �  R2   �8 @   �2  : @   �  : @   �  C: @   ~  �,  R���~ a: @     
-  Q���~X	0b @    �: @   �  6-  Q0X	~b @   Y���~ �: @   �  �: @   y  r-  Q	�b @   X���~Y���~ \; @   �  �-  Q	@c @   Xv  ; @   �2  �-  R	hc @    �; @   h  �; @   �  �; @   I  �; @     �-  Rt  �; @     �; @   c  !.  R	Hb @    A< @   �  ;.  X���~ [< @   �  -= @   �  n= @   e  o.  X���~ �= @   4  �= @   �  �= @   �  �= @   �  �.  Q���~ > @   �  �.  R���~Q	�a @   Xt  > @   �2  �.  R	�a @    Y> @   �2  /  R	�c @    $A @   c  :/  R	@d @    MA @   c  Y/  R	 b @    �B @   �2  x/  R	b @    ~E @   �2  R	�a @     {   �/  4�   � 5mim_upload_cb ��   P @          �90  buf �$}  �  �  size �0�   �  �  userdata �<}      6` @   �  R�RQ1X�QY�X  7print_xml |
` @   9       ��0  node |�  +  '  
xml ~�  �h
len �  �d� @   �  �0  R�RQ�hX�d � @   m   )parse_opts E
1  argc E�   argv E)M  
longopts G
1  	 P @   *c R�    �  1   �    )print_usage 2d1  argc 2�   argv 2*M  is_error 24�   *name 4�   81  � @   �       ��2  G1  L  B  (W1  z  v  ;1  �  �  /1  �  �  � @   �  �1  Rs Q/ � @   j  �1  R2 � @   �  
2  Q	 ` @   Xs  � @   j  $2  R2 9 @   �2  O2  R	8` @   Q1X
T  @   j  f2  R1 + @   �  �2  Q	 ` @   Xs  2 @   j  R1  +puts __builtin_puts +fwrite __builtin_fwrite :__main __main  ]   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 	  �    @   �       �
  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	P� @   atexit ��   �  Y   __main 5� @          ��  � @   �   	__do_global_ctors  ` @   j       �  
nptrs "�   �  �  
i #�   �  �  � @   j  R	  @     	__do_global_dtors   @   :       �[  p [  	 Q @    	   �   /
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
  �   �,  __uninitialized  __initializing __initialized    ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	4Q @   �  	0Q @   =  
"	h� @   [  	`� @    �    �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  s  _dowildcard  �   	@Q @   int  }     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  � @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   � @          � �    M  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 :  "    _newmode �   	p� @   int  �   {  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    @   �       =  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	�� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�g @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�g @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	�� @   __mingw_initltsdyn_force ��   	�� @   __mingw_initltssuo_force ��   	�� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@    @   /       �}  
�  �  �  �  
�  *M      
�  ;d  !    % @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  0 @   �       ��  7  /  �  _  W  �  �    �  �  �  ` @    ` @   +       L�  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   � @   �    �    b
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  S  _commode �   	�� @   int  w   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 N	  �	  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 
  �	  � @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  	  	  type 
�  8	  ,	  - @   b  �  R2 V @   7  Q	�h @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  � @          �  _fpreset 	� @          � �    #  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 a  I  �  __mingw_app_type �   	�� @   int  G   Q  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  -  � @   =        __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�� @   5mSecs �%  �	  �	  !�  E @   �  �4  �  �  �  6�  
�  �	  �	  
�  
  �	  
�  5    
�  �  �  

  �  �  
      "E  �  �  
F  d  \  
[  �  �  Y @   `  R	(j @   Xu w t     � @   � @          �;  �  �  �  �  �  �  �  �  �    � @   � @          �  �  �  �  �  �  �  
  
  � @   �  Ru    !  � @   �  ��  �  
  
  �  
  
  �  (
  &
  7  � @   �  �  2
  0
  �  =
  ;
  �  L
  J
  � @   �  Ru      g @   g @   
       �w  �  V
  T
  �  a
  _
  �  p
  n
    g @   g @   
       �  z
  x
  �  �
  �
  �  �
  �
  o @   �  Ru      � @   � @          �   �  �
  �
  �  �
  �
  �  �
  �
    � @   � @          �  �
  �
  �  �
  �
  �  �
  �
  � @   �  Ru    "$  �  �  
)  �
  �
  83  �  
4        � @   � @   
       s�      �      �  (  &    � @   � @   
       �  2  0  �  =  ;  �  L  J  � @   �  Rt      
 @   `    R	�i @     @   `  R	�i @      9�  � @   X       �|  
�  X  T  :�  �� @   
  Yu    @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �P @   b      �`  &addr ��  t  h  b �:  ��h �g  �  �  i �%  �  �  >0 @   P       �  new_protect �
u  �  �  
d @   
  �  Ys  n @    
  | @   `  R	�i @     
� @   �
  �  Rs  � @   n
  
 @   E
    Q��X0 
� @   `  >  R	`i @    � @   `  R	@i @   Qs   ?__report_error T� @   i       �/  &msg T      @argp ��   �X
 @     �  R2 
& @   /  �  R	 i @   Q1XK 
5 @       R2 
C @   �
  !  Qs Xt  I @   �
   Afwrite __builtin_fwrite   �   )  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ^
  F
    @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �` @          �P  f ,�  !    l @   �  R�R  __mingw_raise_matherr �  @   >       �typ !�   5  /  name 2�  M  I  a1 ?w   _  [  a2 Jw   s  o  rslt 
w   � ex 0  �@Y @   R�@   �    ;  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (    E  _fmode �   	Р @   int  �   i  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  p @   �        char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   p @   �      ��  'exception_data �-�  �  �  old_handler �
	  �  �  action ��   3    reset_fpu ��   �  �  
� @   �
  �  R8Q0 (� @   �  R�R 
' @   �
  �  R4Q0 = @   �  R4 
� @   �
    R8Q0 
� @   �
  7  R8Q1 
� @   �
  S  R;Q0 � @   f  R; � @   y  R8 
� @   �
  �  R;Q1 
  @   �
  �  R4Q1 
#  @   �
  �  R8Q1 )(  @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  0  @   b        char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	 � @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �! @   �       �n  	hDllHandle z�    �  	reason {<  �  m  	reserved |S    �   " @   K       �  
keyp �&�  q  k  
t �-�  �  �  4" @   �  
[" @   C  R	 � @     !n  �! @   �! @          �  �  �! @   )
   "n   " @   �  �E  #�  �  u" @   )
    e" @   6  
�" @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   ! @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�  �  �  @! @   �  B	  Rt  s! @   �  
|! @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �  @   o       �$
  	key *%<    �  	dtor *1.  9  +  
new_key ,$
  y  q  �  @   �  �	  R1QH �  @   �  
  Rt  
! @   �  Rt   �  &n  0  @   p       ��  �  �  '�  h  @          �
  �  �  �  l  @     q  @     (�  @   Rt   J  @   �  �
  R|  )�  @   �  R	 � @      �    H  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _CRT_MT �   	PQ @   int  �    v  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	a� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	`� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 S  ;  �" @   �      �  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��    �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	    �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �% @   �       �7
  i �(�   �  �     �	`  �  �	  �  �  importDesc �@  �  �  �  �^
  importsStartRVA �	I  �  �  �  �% @   	�  ��  �  �  �  �  �  	�  �% @    �  �  �  �  �  =  9  �  N  L      M  & @   & @   J       �q  Z  X  f  }  f  b  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  @% @   �       ��  pTarget �%`  �  �     �	`  rvaTarget �
�  �  �  �  �^
  �  �  �  @% @   �  �/  �  �  �  �  �  	�  P% @    �  �  �  �  �  �  �  �  �  �      M  t% @   t% @   I       �q    	  f  }      �      �  )  '    
_GetPEImageBase �`   % @   6       �0     �	`  	�   % @   k  �	�  k  �  �  �  	�  % @    {  �  {  �  �  6  2  �  G  E       
_FindPESectionExec y^
  �$ @   s       �%  eNo y�   U  Q     {	`  �  |	  f  d  �  }^
  p  n    ~�   z  x  	�  �$ @   P  �	�  P  �  �  �  	�  �$ @    `  �  `  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   @$ @   7       ��     i	`  �  j	  �  �  	�  @$ @   5  m	�  5  �  �  �  	�  P$ @    E  �  E  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �# @   �       �  p Y&s  �  �     [	`  rva \
�  �  �  �  �# @     _�  �    �  �  �  	�  �# @      �    �  �       �          	M  �# @   *  c
q  !    f  *  }  -  )  �  K  I  �  U  S     
_FindPESectionByName :^
   # @   �       �M  pName :#�  h  ^     <	`  �  =	  �  �  �  >^
  �  �    ?�   �  �  �  5# @     F  �    �  �  �  �  E# @    E# @          �  �  �  �  �  �  �  �     &/# @   �  -  Rt  '�# @   z  Rs Qt X8  _FindPESection $^
  �     $`  (rva $-�  �  &	  �  '^
    (�    _ValidateImageBase   �     `  pDOSHeader �  �  	  pOptHeader v   )�  �" @   ,       �~  �  �  �  �  �  �  �  �  	�  �" @    �  �  �  �  �  �  �      �         *M  �" @   P       �f  $     +q  Q}  7  3  �  V  T  �  `  \    8   0  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99    Q  �& @   �      C#  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	�Q @   �  	|Q @   �  	xQ @   &__mingw_optreset D#  	�� @   �  	�� @   
place f6  	pQ @   
nonopt_start i#  	hQ @   
nonopt_end j#  	dQ @   �   �  �   ! �  
recargchar m�  	�k @   
recargstring n�  	�k @   �   :  �    *  
ambig o:  	pk @   �   f  �   ' V  
noarg pf  	@k @   �   �  �    �  
illoptchar q�  	k @   
illoptstring r�  	�j @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  P2 @           ��
  ;  ,#  �  �  5  ,+�
  �  �  #  ,>/  �  �  A  -�
  �  �  idx --@  � k2 @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  02 @           �q  ;  #  
  	  5  &�
  #    #  9/  9  5  A   �
  O  K  idx  -@  � K2 @     R�RQ�QX�XY�Yw � w(1  getopt #   2 @   "       �  ;  #  e  a  5  !�
  {  w  #  4/  �  �  2 @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �+ @   (      ��  ;  C#  �  �  5  C*�
  �  �  #  C=/  !    A  D�
  M  E  idx D*@  � )flags D3#  {  m   oli F6  �  �   optchar G#  �  �  *+  G#  "    +posixly_correct H
#  	`Q @   ,start g�, @      C
  Rt Qu Xs Yv  ^- @   �	  [
  R}  �- @   �  �
  Rv Q} X~ Y�  �- @   �	  �
  R} Qs  &. @   �	  �
  R	�j @   Q0X0 q. @   �	  �
  R} Q- �. @   �	  	  R} Q- �/ @      '  Ru Yv  �0 @      T  R���Qt Xs Yv  �0 @   �  y  R	k @   Qs  1 @   �  �  R	�k @   QW t1 @   �  �  Rv Q} X~ Y� w 0 �1 @   �  R	�k @   Qs   -parse_long_options �#  @( @   �      �   5  �"�
  >  :  #  �5/  R  N  A  ��
  j  b  idx �*@  �  �  .+  �3#  � current_argv �6  �  �  has_equal �6  �  �  current_argv_len �	�       i �#  /  )  ambiguous �	#  X  F  match �#  �  �  �( @   �	  /  Ru Q= ) @   S	  S  Ru Q~ Xt  $) @   w	  k  R~  �) @   �  �  R	pk @   Qt Xu  �* @   w	  �  Ru  �* @   �  �  R	�j @   Qu  + @   �  �  R	@k @   Qt Xu  k+ @   �  R	�k @   Qu   !permute_args ��& @   �       ��  panonopt_start �#  �  �  panonopt_end �&#    �  opt_end �8#  %    5  ��
  C  ;  cstart �#  j  `  cyclelen �#  �  �  i �#  �  �  j �#  �  �  ncycle �#  �  �  nnonopts �&#  �  �  nopts �0#  �  �  pos �7#       swap �6  >  :  /�  �& @    �  ��  U  K  �  w  q  0�  1�  �  �     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�' @   �       ��  fmt ~/  �  �  
ap ��   �X4�  �' @    �' @   W       �  �  �    �  �  5�' @   @	  �' @   	  u  R2 �' @   �  �  Q	�j @   Xu  ( @   	  �  R2 ( @   �  �  Qs Xt  ( @   	  �  R2 *( @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 f  N  �,  _MINGW_INSTALL_DEBUG_MATHERR �   	�Q @   int  �   �  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  p2 @   y       �,  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   size_t #,�   long long unsigned int long long int short unsigned int int long int �   &  unsigned int long unsigned int unsigned char double float long double &  �  _vsnprintf   �  +  �   �  �    �   �  malloc �  �  �    
_vscprintf �    �  �    asprintf 7  p2 @   y       �ret #�  �  �  format 	0�      ap �   �Hlen   6  0  _end �2 @   	�2 @   �  �  Rt Qv  	�2 @   �  �  R|  �2 @   �  Q| Xt Yv    �   �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �2 @          �-  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	�Q @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �2 @          �_File *�  Z  T  _Format J�  s  m  _ArgList Z�   �  �  	3 @   �  R0Q�RX�QY0w �X   �   "  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  3 @   *       ".  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   size_t #,�   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(B  G  
threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   n  s  �   }  �    �   n  �   x  __imp__vsnprintf �  	�Q @   P  __stdio_common_vsprintf �  �  �   n  �   x  $  �    _vsnprintf   3 @   *       �_Dest ,s  �  �  _Count 9�   �  �  _Format Z}  �  �  _Args j�   �  �  53 @   �  R1Q�RX�QY�Xw 0w(�Y   a   ##  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @3 @   )       �.  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   size_t #,�   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(B  G  	threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  
threadmbcinfostruct 	localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   d  i  �    �   d  __imp__vscprintf �  	�Q @   P  __stdio_common_vsprintf �  �  �   �  �   d  $  �    �   _vscprintf �  @3 @   )       �
_Format 2i  #    
_ArgList C�   <  6  d3 @   �  R2Q0X0Y�Rw 0w(�Q   y   2$  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  p3 @   ?       /  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   size_t #,�   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(B  G  	threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   e  j  j  
 �   e  __imp_sscanf �  	�Q @   P  __stdio_common_vsscanf �  �  �   e  �   e  $  �    sscanf   p3 @   ?       �_Src .j  b  \  _Format Mj  {  u  
__ap �   �h__ret 
  �  �  �3 @   �  R0Q�RX	�Y�Qw 0w(�   �   k%  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �3 @   H       �/  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�Q @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �3 @   H       �_Format .�  �  �  
ap 
�   �Xret 	  �  �  �3 @   �  �  R1 �3 @   �  R0Xs Y0w t    �   �&  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  h   4 @   2       ,0  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�Q @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	   4 @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	        -4 @   �  R0Q�RX�QY0w �   �   9(  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 e  M  �0  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	R @     
	 R @    �   �(  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  j  @4 @         1  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	�R @   �      �   __imp_at_quick_exit g)  	xR @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	tR @   
initial_tzname1 }
V  	pR @   
initial_tznames ~�  	`R @   
initial_timezone 
%  	\R @   
initial_daylight �  	XR @   __imp_tzname ��  	PR @   __imp_timezone ��  	HR @   __imp_daylight ��  	@R @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	8R @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	0R @   �	  __imp__amsg_exit �V  	(R @   F  __imp__get_output_format �\
  	 R @   -
  __imp_tzset ��  	R @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	R @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �5 @   5       ��
  file �!�
        fmt �6�  :   4   
ap ��   �h+ret �  U   S   �5 @   �
  R4Q�RX�QY0w �  ,tzset "�5 @   6       �  -  �5 @   �5 @   -       ��5 @   +  �5 @     
6 @       ._tzset �/_get_output_format nF  @4 @          �0_amsg_exit ip5 @   .       ��  ret i  a   ]   �5 @   z  �  R2 �5 @   Q  �  Q	 l @   Xs  �5 @   <  R�  at_quick_exit �  P5 @          �   func ]*�  t   p   1e5 @   �   _onexit ��  05 @          �p  func V%�  �   �   =5 @   �  Rs   __wgetmainargs J  �4 @   j       �[  _Argc J"�  �   �   _Argv J5I  �   �   _Env JGI  �   �    N  JQ  !  !  !Z  Jl�	  � �4 @   0  �4 @   	  &  R	v  $0.# �4 @   �  �4 @   �  	5 @   �  5 @   U   __getmainargs >  P4 @   j       �E  _Argc >!�  '!  !!  _Argv >1S  @!  :!  _Env >@S  Y!  S!   N  >J  r!  l!  !Z  >e�	  � p4 @   �  �4 @   �    R	v  $0.# �4 @   �  �4 @   �  �4 @   u  �4 @   U   2   6 @   6       �26 @   +  >6 @     J6 @                                                                                                                                                                                                                                                                                                                                                                                  
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  (    I  H}  ( 
  H }   :;9I  .?:;9'I<  	 !I  
4 :!;9I  
 :;9I8  $ >  
.?:;9'I<  4 :!;9I  >!I:;9  H}  (   4 :!;9I�B  U   :!;9I�B  & I  4 :!;9I�B   1�B   :!;9I  :;9  7 I   :;9I   <  I     I �~   ! I/  !.?:;9'<  ".?:;9'<  #4 :!;9I?<  $>!!I:;9  %>!!I:;9  &
 :!;9!  '  (4 1�B  ).:!;9!
' !  *4 :!;9I  +. ?<n:!;!   ,%U  -   .'I  /.?:;9'�<  0. ?:;9'I<  1.?:;9'I@z  21R�BUXYW  3U  4! I/  5.:;9'I@z  6H}�  7.:;9'@z  8.1@z  9H}�  :. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >   $ >   I  I ~   :;9I   !I  7 I  .?:;9'I<   :!;9I�B  	H}  
%   I  & I  
   .?:;9'I@z     4 :;9I  4 :;9I�B  
 :;9  H}    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :!;!9I�B   :;9I   :!;9I  	& I  
!:!;9!  7 I  %  
 I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I  & I  	!:!;9!  
 :!;!9I�B  %   I  
 <  'I  7 I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I  & I  	!:!;9!  
    :!;!9I�B  %  
 I   <  'I  7 I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �	    �   �
        \  u  �  �  �  �      (  2  :  C  L  T  d  l    �  �  �  �  �  �  �  �  �  �    	P @   �	fY	X 	` @   vK���YXYg
��h
0
V>Y  � J t t � <Y  �
  sf W t t � <Y   	�8 @   �J��1t��� ��!��
�����
�nf	��	uny��h�qf�֟X(wq=c2Q�;=g �	�  t � X �	�% X�W/��Y� X X�����2 ��[U/=��s�Y��^h� X����YZXYZ�Y��Y[�}X.&X��. �  <Y���  ���YY��K	 <��& ��  ��YY
� f�� � �
\*Y
r�h�	���	�JY]��}�y��et f��(XK 	A� ��
X �  <YXd�X� 
���;=Y���	s=	�� X�	�=	� X���	"rY	�	=��	 X�.˃

�J\X
=s�
 ��n�	�K��X�~B f t X < f � <YX��
� �� <  �~�~��<X��t�� .� X=�yX�W/�K �	L �  <Y
� �� <ZK�X�F f t � < f � <YX% .t <�yXX f t � < f � <YX�� �  <YtXP f t X < f � <YX
.
	 �� �� <w��;Y:,F</9Z���	3����=Y��
	��
 ���	�
g�/��Y
	������ .t <�X� �  <YX���X��@X��	��}����
�	y  �� <	t �� < X #    K   �
      :  R  y  �  �  �  �  �  �  �   	  @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      #  ;  b  }  �  �  �  �  �  �  6     .   �
      1  I  p  {  R     .   �
      �  �       	� @    6     .   �
      l  �  �  �      K   �
      	  !  H  c  l  u    �  �  �   	  @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  	  8	  C	  6     .   �
      �	  �	  �	  �	  �     <   �
      3
  K
  r
  �
  �
  �
  �
   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
    3  >   	� @   		3 6     .   �
      �  �  �  �  |    s   �
      E  ]  �  �  �  �  �  �  �  �  �  �  
  
  
  )
  2
  ;
    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �
  �
  �
  �
  �
  	   	  @   
L�Z
�KTYZ
g=yuX 6     .   �
      Z  r  �  �  �    U   �
      �    ;  	V  d  r  z  �  �  �  �  �   	p @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      	  !  H  c  m  w  �  �  �  �  �  �  �  �   	0  @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      .  F  m  w  6     .   �
      �  �    '  G    K   �
      �  �  �  �  �  �  �         	�" @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
      i  �  �  �  �  �  �  �  �  �        $  -  :  C    	�& @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      �  �  �  �  �     F   �
      J  b  �  �  �  �  �  �  �   	p2 @   	�?U	�<7[Z	uL
J �>
>]�	<<N� H	w
	< n     A   �
      1  I  r  �  �  �  �  �   	�2 @   K
�<.Y o     A   �
        3  \  w  �  �  �  �   	3 @   K
�<<�Y s     A   �
      	  !  J  e  w  �  �  �   	@3 @   K
�
=.X� �     A   �
      �    4  O  ]  k  t  ~  Z 	p3 @   
KZU\ZF\f-YY �     A   �
      �  �    1  ?  M  V  `   	�3 @   g?	YT�Y	 X� <uX �     A   �
      �  �  �    #  2  ;  E   	 4 @   KU	\fp	\;Y	Y W     O   �
      �  �  �  �    +  3  @  I  T  `  |    h   �
      �  �  �    @  R  d  k  t  ~  �  �  �  �  �  �   	@4 @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	p5 @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                   ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  P @             P  ` @   9       D@t  4   P  � @   �       A�A�D@^
A�A�E    l   P  �8 @   �
      B�G�B �B(�A0�A8�A@�AH�	H��9
HA�@A�8A�0A�(B� B�B�B�A      ���� x �         H    @   :       D0u  4   H  ` @   j       A�A�D@@
A�A�H       H  � @             ���� x �         �  � @             ���� x �      $        @   /       D0R
JN    L      0 @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�          � @             ���� x �      <   �  � @   �       A�A�D�P�
���
���A�A�B    ���� x �            � @             ���� x �      $   0  � @   i       A�A�DP   <   0  P @   b      A�A�A �Dp�
 A�A�A�D   \   0  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �             @   >       D`y       ` @             ���� x �      4   `  p @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  0  @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �  @   o       A�A�A �D@U
 A�A�A�A    D   �  ! @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �! @   �       A�D0p
A�J�
A�A      ���� x �         �  �" @   ,          �  �" @   P       L   �   # @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  �# @   �          �  @$ @   7          �  �$ @   s          �   % @   6          �  @% @   �          �  �% @   �          ���� x �      T      �& @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <      �' @   �       A�A�A �DPw A�A�A�      l      @( @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l      �+ @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C           2 @   "       D@]        02 @           D@[        P2 @           D@[     ���� x �      L   �	  p2 @   y       B�A�A �A(�A0�D`c
0A�(A� A�A�B�A    ���� x �         X
  �2 @          D@Y     ���� x �         �
  3 @   *       D@e     ���� x �         �
  @3 @   )       D@d     ���� x �            p3 @   ?       DPz     ���� x �      ,   8  �3 @   H       A�A�D`A�A�   ���� x �         �   4 @   2       DPm     ���� x �         �  @4 @          L   �  P4 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �  �4 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �  05 @          A�D0WA�    �  P5 @             �  p5 @   .       A�D0   �  �5 @   5       DPp     �  �5 @   6       D0q     �   6 @   6       D0q                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion lockdownd_client_private afc_client_private mobile_image_mounter_client_private idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                                                            C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools ideviceimagemounter.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/sys C:/msys64/ucrt64/include/plist ../include/libimobiledevice C:/msys64/ucrt64/include/libimobiledevice-glue ideviceimagemounter.c ideviceimagemounter.c corecrt.h stdio.h getopt.h stdint.h types.h _mingw_stat64.h plist.h libimobiledevice.h lockdown.h afc.h mobile_image_mounter.h winnt.h combaseapi.h wtypes.h string.h stdlib.h utils.h stat.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/asprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include asprintf.c asprintf.c vadefs.h corecrt.h stdio.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt__vsnprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt__vsnprintf.c ucrt__vsnprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt__vscprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt__vscprintf.c ucrt__vscprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_sscanf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_sscanf.c ucrt_sscanf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                       �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� �                      �8 @    7R7�U��u p ����Rp ���u p ���U���Rp �             �8 @    7Q7�T��p  $ &3$t "���p  $ &3$t "���p  $ &3$�Q"���T��p  $ &3$t "�                      �8 @    � ����P��P�� ����P�� ���
�
 ���
�
P�� ���� ���� ��                    �8 @    �	����S��		���	�	S�	�	����S��	����S��	����S��	����S                      �8 @    �0���P��T��0���T��0���
T�
�
P�
�T��0���T                           �8 @    �0���0���	^�	�0���^��0���^��0���^��^��0���^��0���^            �: @    ?0�?�U��0���0���U��U               p= @    P�� ����P��P��
U�
�
P��P��U              �> @    G0�GKPK�V��V��0���	V�	�	V                           �> @    &P&I\I�P��\��\��\��\��P��\��\��P��
\�
�\        f@ @    P&V��P��V     �? @    vx3%�v 3%�"vx3%�   wA @   �_       LD @    Ps~P~�R    �8 @    �T��T    �8 @    �U��U   9 @   P     P @    R�R�       P @    QX�Q�       P @    XY�X�     ` @    #R#9�R�           � @    
Q
+T+k�Q�krTr��Q�     � @    PVcP   � @   ��;1  �   � @   ��/1  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
@h @   ���
 h @   ���
�h @   ���
hh @   ���
�h @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  �����     ��U  ��2�  �����     ��U  ��1�  �����     ��U  ��1�  �����     ��U  �	�	4�  �	�	���     �	�	U  �	�	4�  �	�	���     �	�	U  �	�	8�  �	�	���     �	�	U  �	�	8�  �	�	���     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�݈     �
�
T  �
�
4�  �
�
�݈     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S `                   R pUpt�R�tyU          QoTot�Q�tyT       1CPCgSty	�� T                RQ�R�        QX�Q�        X�`�X� m                 R $Q$*�R�        Q$X$*�Q�        X$Y$*�X�        Y$�h$*�Y� ;                R#Y#)�R�        Q#�h#)�Q� B                0R09Q9?�R�        .Q.9Y9?�Q�   :?P )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                            X         Z���� ���� ���� ���� ���� ���� ���� #        �8 @    ^��� �8 @    g��� �: @    P���� s< @    ��������� x= @    "������	�	�
�
����� x= @    "���
����� �@ @    �� QD @    W���� �? @    ��������� P @   ��8 @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                                                                                                                                                                                                                                                                                                                .file   a   ��  gcrtexe.c              �                                �              �   �                        �   �                        �    
                        %  p                        @  �                        `             k  �
                        �  �
                        �  P                        �  �
                        �  0          �  �
                    envp           argv            argc    (                          �
                        '  �          9  0
                        ^  @
                        �             �  �                        �  �
                        �   
                          �                    mainret            "  `
                        8  P
                        N  �
                        d  p
                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )       +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  @     +                 .file   7  ��  g    '                m  P                           {  `          �  �      main    �(          �          xml_mode@           �  �
                    udid    8           �  0           �  D           �  H           �                       PKG_PATHp          �  P      .text   P     �                .data                          .bss    0                       .xdata  t                       .pdata  l      $   	             .rdata         ~                   �  �(     �
  �                 	  �                             �                          �  �&  
   �2                  �  �     n                    �  �     �                   �  0      @                    �  \      '                     9     �	                          `                       �     +                    )  p     +                     4  P     �                .text   @      .idata$7�      .idata$5�      .idata$4�      .idata$6�	      .text   H      .idata$7�      .idata$5�      .idata$4�      .idata$6�	      .text   P      .idata$7�      .idata$5�      .idata$4�      .idata$6d	      .text   X      .idata$7�      .idata$5�      .idata$4�      .idata$6@	      .text   `      .idata$7�      .idata$5�      .idata$4�      .idata$6 	      .text   h      .idata$7�      .idata$5�      .idata$4�      .idata$6	      .text   p      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   x      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4x      .idata$6�      .text   �      .idata$7|      .idata$5�      .idata$4p      .idata$6�      .text   �      .idata$7x      .idata$5�      .idata$4h      .idata$6l      .text   �      .idata$7t      .idata$5�      .idata$4`      .idata$6P      .text   �      .idata$7p      .idata$5�      .idata$4X      .idata$64      .text   �      .idata$7l      .idata$5�      .idata$4P      .idata$6$      .text   �      .idata$7h      .idata$5�      .idata$4H      .idata$6      .text   �      .idata$7d      .idata$5�      .idata$4@      .idata$6�      .text   �      .idata$7`      .idata$5x      .idata$48      .idata$6�      .text   �      .idata$7\      .idata$5p      .idata$40      .idata$6�      .text   �      .idata$7X      .idata$5h      .idata$4(      .idata$6�      .text   �      .idata$7T      .idata$5`      .idata$4       .idata$6�      .text   �      .idata$7P      .idata$5X      .idata$4      .idata$6�      .text   �      .idata$7�      .idata$5      .idata$4�      .idata$6�	      .text   �      .idata$7@      .idata$5�      .idata$4H      .idata$6@      .text   �      .idata$7<      .idata$5�      .idata$4@      .idata$6(      .text          .idata$78      .idata$5x      .idata$48      .idata$6      .text         .idata$74      .idata$5p      .idata$40      .idata$6�
      .text         .idata$70      .idata$5h      .idata$4(      .idata$6�
      .text         .idata$7,      .idata$5`      .idata$4       .idata$6�
      .file   [  ��  ggccmain.c             =                          p.0                O  `          a  `                    __main  �          ~  P       .text         �                .data                         .bss    P                       .xdata  �                       .pdata  �      $   	                 �  tY  
   a                   �  �     ?                    �  �     5                     �  p      0                      �
     '                     �     �                     )  �     +                     4  H     �                .file   q  ��  gnatstart.c        .text   �                       .data   0                      .bss    `                           �  �_  
     
                 �  /
     �                     �  �                                  V   
                                               �                         )  �     +                 .file   �  ��  gwildcard.c        .text   �                       .data   @                      .bss    p                            �  �e  
   �                    �  �
     .                     �  �                             s     :                      �     �                     )        +                 .file   �  ��  gdllargv.c         _setargv�                       .text   �                      .data   P                       .bss    p                        .xdata  �                       .pdata  �                          �  df  
   �                   �       :                     �  �      0                      �     V                      �     �                     )  0     +                     4  �     0                .file   �  ��  g_newmode.c        .text                           .data   P                       .bss    p                           �  �g  
   �                    �  M     .                     �                                   :                      "     �                     )  `     +                 .file   �  ��  gtlssup.c              �                              �  0          �  @                    __xd_a  P       __xd_z  X           �  �      .text         �                .data   P                       .bss    �                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  oh  
   �  6                 �  {     �                    �  �                        �  0     0                      =                          �                            �     �                     )  �     +                     4        �                .file     ��  gxncommod.c        .text   �                       .data   P                       .bss    �                           �  6p  
   �                    �  b
     .                     �  `                            S     :                      �     �                     )  �     +                 .file     ��  gcinitexe.c        .text   �                       .data   P                       .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �p  
   {                   �  �
     a                     �  �                            �     :                      N	     �                     )  �     +                 .file   =  ��  gmerr.c            _matherr�                       .text   �     �                .data   P                       .bss    �                        .rdata  �     @               .xdata  �                       .pdata  �                          �  ;r  
   6  
                 �  �
                         �  	     �                    �  �     0                      �     �                      �	     �                     )        +                     4  �     X                .file   Z  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   P                       .bss    �                        .xdata  �                       .pdata  �                          �  qu  
   �                    �  �     -                     �  �     0                      �     X                      �
     �                     )  P     +                     4        0                .file   n  ��  gmingw_helpers.    .text   �                       .data   P                       .bss    �                           �  v  
   �                    �  #     .                     �                               �     :                      I     �                     )  �     +                 .file   �  ��  gpseudo-reloc.c        �  �                           �  P	          �  �       the_secs�           �  �
            �           !  �                        R  �                    .text   �     =  &             .data   P                       .bss    �                       .rdata   	     [                .xdata  �      0                 .pdata       $   	                 �  �v  
   K  �                 �  Q     �                    �  �	     �  
                 �        0                    �  �     W                            �                     �     	                       �     O                    )  �     +                     4  0     �                .file   �  ��  gusermatherr.c                                       �  �           �  `      .text         L                .data   P                       .bss    �                       .xdata  $                      .pdata  ,                         �  �  
   �                   �  )                         �       r                     �  P     0                      �     �                      F
     �                     )  �     +                     4       P                .file   �  ��  gxtxtmode.c        .text   p                       .data   P                       .bss    �                           �  ڐ  
   �                    �  ;     .                     �  �                            E     :                           �                     )       +                 .file   �  ��  gcrt_handler.c         �  p                       .text   p     �               .data   P                       .bss    �                       .xdata  0                      .rdata  �
     (   
             .pdata  D                         �  b�  
   �                   �  i     ~                    �  �     _                    �  �     0                           �  
                   �                            �                         )  @     +                     4  `     P                .file     ��  gtlsthrd.c             �  0                           �             �             
  �          *            =            ]  �      .text   0     b  "             .data   P                       .bss          H                 .xdata  8     0                 .pdata  P     0                    �  C�  
   �
  A                 �  �     a                    �  �     �                    �  �     0                    �  �                                 x                     �     %                    )  p     +                     4  �     (               .file   +  ��  gtlsmcrt.c         .text   �                       .data   P                      .bss    `                           �  �  
   �                    �  H     .                     �                               �     :                      �     �                     )  �     +                 .file   ?  ��  g    q            .text   �                       .data   `                       .bss    `                          �  ��  
   �                    �  v     0                     �                               �     :                      �     �                     )  �     +                 .file   w  ��  gpesect.c              �  �                           �  �          �             �  �          �  @          �  �                         @          1  �      .text   �     �  	             .data   `                       .bss    p                       .xdata  h     ,                 .pdata  �     l                    �  o�  
   �  �                 �  �     �                    �  �     �                    �  @     0                    �  �     �                       �     K                     �     T                       ;     �                     )        +                     4  �     (               .text   �     2                 .data   `                       .bss    p                       .text   �                       .data   `                       .bss    p                           )  0     +                 .file   �  ��  ggetopt.c              S  �                       warnx   �          `  @      place   p      ambig   p          s  �
      noarg   @          �  �          �  �          �  `          �  d          �  h          �            �  �      getopt   "          �  0"          �  P"      .text   �     �  t             .data   `     $                .bss    �                      .xdata  �     d                 .pdata  �     T                .rdata  �
     B                    �  A�  
   <  �                 �  0     �                    �  �     ?                    �  p     0                    �  �                            C#     a	                     #     +                             .                    )  `     +                     4        �               .file   �  ��  gmingw_matherr.    .text   p"                       .data   �                      .bss    �                           �  }�  
   �                    �  �     .                     �  �                            �,     :                      N     �                     )  �     +                 .file   �  ��  gasprintf.c        asprintfp"                       .text   p"     y                .data   �                       .bss    �                       .xdata  �                      .pdata  @                         �  �  
   �                   �  �     �                     �  �     d                     �  �     0                      �,     �   
                   �     �                     )  �     +                     4  �	     h                .file   �  ��  gucrt_vfprintf.    vfprintf�"                       .text   �"                     .data   �                     .bss    �                       .xdata                        .pdata  L                         �  ��  
   �                   �  �      8                    �  H     X                     �  �     0                      �-     r   	                   �     �                     )  �     +                     4  X
     8                .file     ��  gucrt__vsnprint        �  #                       .text   #     *                .data   �                     .bss    �                       .xdata                        .pdata  X                         �  ��  
   �                   �  "                         �  �     q                     �        0                      ".     s   	                   �     �                     )        +                     4  �
     8                .file   7  ��  gucrt__vscprint        	  @#                       .text   @#     )                .data   �                     .bss    �                       .xdata                        .pdata  d                         �  7�  
   e                   �  ##                         �       ?                     �  P     0                      �.     w   	                   �     �                     )  P     +                     4  �
     8                .file   U  ��  gucrt_sscanf.c     sscanf  p#                       .text   p#     ?                .data   �                     .bss    �                       .xdata                         .pdata  p                         �  ��  
   }                   �  2$     9                    �  P     F                     �  �     0                      /     �   	                   �     �                     )  �     +                     4        8                .file   s  ��  gucrt_printf.c     printf  �#                       .text   �#     H                .data   �                     .bss    �                       .xdata  (                      .pdata  |                         �  �  
   �  
                 �  k%     l                    �  �     -                     �  �     0                      �/     �   	                   �     �                     )  �     +                     4  8     H                .file   �  ��  gucrt_fprintf.c    fprintf  $                       .text    $     2                .data   �                     .bss    �                       .xdata  4                      .pdata  �                         �  ��  
   �                   �  �&     b                    �  �     F                     �  �     0                      ,0     �   	                   h     �                     )  �     +                     4  �     8                .file   �  ��  g__initenv.c           	  �           	  �      .text   @$                       .data                         .bss    �                          �  n�  
   �                   �  9(     �                     �                              �0     [                      M                         )       +                 .file   �  ��  gucrtbase_compa        /	  @$                           B	  P$          P	  �$      _onexit 0%          _	  P%          m	  
                        �	  p%          �	  �%      tzset   �%          �	  �                    _tzset   &          �	  X          �	  \          �	  `          �	  t          	
  p      .text   @$       "             .data        x   
             .bss    �                       .xdata  <     P                 .pdata  �     l                .rdata                             �  �  
   �  Y                 �  �(                          �  	      |                    �  0     0                      1     �                     N                            j     `                    )  @     +                     4  �     �               .text   `&      .data   �      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�
      .text   h&      .data   �      .bss    �      .idata$7       .idata$5@      .idata$4       .idata$6�
      .text   p&      .data   �      .bss    �      .idata$7      .idata$5H      .idata$4      .idata$6�
      .text   x&      .data   �      .bss    �      .idata$7      .idata$5P      .idata$4      .idata$6�
      .file     ��  gfake              hname   �      fthunk  8      .text   �&                       .data   �                       .bss    �                       .idata$2�                      .idata$4�      .idata$58      .file   .  ��  gfake              .text   �&                       .data   �                       .bss    �                       .idata$4                      .idata$5X                      .idata$7                      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6t
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6~
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�
      .file   <  ��  gfake              hname   �      fthunk        .text   �&                       .data   �                       .bss    �                       .idata$2�                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   �&                       .data   �                       .bss    �                       .idata$4�                      .idata$50                      .idata$7�     !                 .text   �&      .data   �      .bss    �      .idata$7t      .idata$5�      .idata$4`      .idata$6�      .text   �&      .data   �      .bss    �      .idata$7x      .idata$5�      .idata$4h      .idata$6�      .text   �&      .data   �      .bss    �      .idata$7|      .idata$5�      .idata$4p      .idata$6�      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4x      .idata$6�      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6&
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6@
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6J
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6R
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6Z
      .text   �&      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6b
      .text    '      .data   �      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6l
      .file   �  ��  gfake              hname   `      fthunk  �      .text   '                       .data   �                       .bss    �                       .idata$2�                      .idata$4`      .idata$5�      .file   F  ��  gfake              .text   '                       .data   �                       .bss    �                       .idata$4�                      .idata$5                      .idata$7�                       .text   '      .data   �      .bss    �      .idata$7      .idata$5       .idata$4�      .idata$6d      .text   '      .data   �      .bss    �      .idata$7      .idata$5      .idata$4�      .idata$6r      .text    '      .data   �      .bss    �      .idata$7      .idata$5      .idata$4�      .idata$6�      .text   ('      .data   �      .bss    �      .idata$7      .idata$5      .idata$4�      .idata$6�      .text   0'      .data   �      .bss    �      .idata$7      .idata$5       .idata$4�      .idata$6�      .text   8'      .data   �      .bss    �      .idata$7      .idata$5(      .idata$4�      .idata$6�      .text   @'      .data   �      .bss    �      .idata$7      .idata$50      .idata$4�      .idata$6�      .text   H'      .data   �      .bss    �      .idata$7       .idata$58      .idata$4�      .idata$6�      .text   P'      .data   �      .bss    �      .idata$7$      .idata$5@      .idata$4       .idata$6�      .text   X'      .data   �      .bss    �      .idata$7(      .idata$5H      .idata$4      .idata$6�      .text   `'      .data   �      .bss    �      .idata$7,      .idata$5P      .idata$4      .idata$6       .text   h'      .data   �      .bss    �      .idata$70      .idata$5X      .idata$4      .idata$6"      .text   p'      .data   �      .bss    �      .idata$74      .idata$5`      .idata$4       .idata$6B      .text   x'      .data   �      .bss    �      .idata$78      .idata$5h      .idata$4(      .idata$6N      .text   �'      .data   �      .bss    �      .idata$7<      .idata$5p      .idata$40      .idata$6^      .text   �'      .data   �      .bss    �      .idata$7@      .idata$5x      .idata$48      .idata$6�      .text   �'      .data   �      .bss    �      .idata$7D      .idata$5�      .idata$4@      .idata$6�      .text   �'      .data   �      .bss    �      .idata$7H      .idata$5�      .idata$4H      .idata$6�      .text   �'      .data   �      .bss    �      .idata$7L      .idata$5�      .idata$4P      .idata$6�      .file   T  ��  gfake              hname   �      fthunk         .text   �'                       .data   �                       .bss    �                       .idata$2�                      .idata$4�      .idata$5       .file   ~  ��  gfake              .text   �'                       .data   �                       .bss    �                       .idata$4X                      .idata$5�                      .idata$7P     "                 .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6.      .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6F      .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6P      .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6Z      .file   �  ��  gfake              hname   �      fthunk  �      .text   �'                       .data   �                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   �'                       .data   �                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     "                 .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6      .file   �  ��  gfake              hname   �      fthunk  �      .text   �'                       .data   �                       .bss    �                       .idata$2x                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   �'                       .data   �                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�                      .text   �'      .data   �      .bss    �      .idata$7|      .idata$5�      .idata$4`      .idata$6�
      .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4h      .idata$6�
      .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4p      .idata$6      .text   �'      .data   �      .bss    �      .idata$7�      .idata$5�      .idata$4x      .idata$6      .file   �  ��  gfake              hname   `      fthunk  �      .text    (                       .data   �                       .bss    �                       .idata$2d                      .idata$4`      .idata$5�      .file   �  ��  gfake              .text    (                       .data   �                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�                      .text    (      .data   �      .bss    �      .idata$7P      .idata$5�      .idata$4P      .idata$6�
      .file   
  ��  gfake              hname   P      fthunk  �      .text   (                       .data   �                       .bss    �                       .idata$2P                      .idata$4P      .idata$5�      .file   &  ��  gfake              .text   (                       .data   �                       .bss    �                       .idata$4X                      .idata$5�                      .idata$7T     %                 .text   (      .data   �      .bss    �      .idata$7       .idata$5x      .idata$48      .idata$6�
      .text   (      .data   �      .bss    �      .idata$7$      .idata$5�      .idata$4@      .idata$6�
      .file   4  ��  gfake              hname   8      fthunk  x      .text    (                       .data   �                       .bss    �                       .idata$2<                      .idata$48      .idata$5x      .file   �  ��  gfake              .text    (                       .data   �                       .bss    �                       .idata$4H                      .idata$5�                      .idata$7(     &                 .text    (      .data   �      .bss    �      .idata$7      .idata$5h      .idata$4(      .idata$6�
      .text   ((      .data   �      .bss    �      .idata$7      .idata$5`      .idata$4       .idata$6�
      .text   0(      .data   �      .bss    �      .idata$7      .idata$5X      .idata$4      .idata$6�
      .text   8(      .data   �      .bss    �      .idata$7       .idata$5P      .idata$4      .idata$6�
      .text   @(      .data   �      .bss    �      .idata$7�      .idata$5H      .idata$4      .idata$6n
      .text   H(      .data   �      .bss    �      .idata$7�      .idata$5@      .idata$4       .idata$6V
      .text   P(      .data   �      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6:
      .text   X(      .data   �      .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6*
      .text   `(      .data   �      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6
      .text   h(      .data   �      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�	      .text   p(      .data   �      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�	      .file   �  ��  gfake              hname   �      fthunk        .text   �(                       .data   �                       .bss    �                       .idata$2(                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   �(                       .data   �                       .bss    �                       .idata$40                      .idata$5p                      .idata$7     
                 .file   �  ��  gcygming-crtend        
  P6                       .text   �(                       .data   �                       .bss    �                           �  P6                         	  �                                                      -
  h6                         )  p     +                 .idata$2        .idata$5X      .idata$4      .idata$2       .idata$5      .idata$4�      .idata$2�       .idata$5`      .idata$4       .idata$4�      .idata$5       .idata$7�      .idata$4�      .idata$5      .idata$7�      .idata$4P      .idata$5�      .idata$7D      .rsrc       
    __xc_z             :
  �          U
  �          t
  `&          
  �          �
  �&          �
  x          �
            �
  (          �
  x                          x6             H          /  D          F  �          e  �           �  @(          �  X          �  �      strerror�'          �  �            �                       #             7  P          D      	        S  @
          r  �'          �        __xl_a  0           �  X(          �  X          �  �          �  �          �  �           
  �          (
  x      _cexit  ('          B
  `  ��       Z
     ��       s
            �
              �
  �          �
      ��       �
  �               ��          0           2        __xl_d  @           N  p      _tls_end   	        s  `      __tznamep&          �  �          �             �   (          �            �             �  (          �  0           �  
            �&          *  @          7  �          O      	    memcpy  �'          Z  �          v  �
      puts     '          �  �          �  �           �  x          �  �
      malloc  �'      _CRT_MT P      optarg  �          �  0(            �          "            /              =  �          X  �          {  H          �     ��       �  �          �  �          �  0          �  �
          �  P            �           4  �      opterr  �          ?  (          Z  x          n  �          �  (          �  �          �  �           �  p          �  ((          �  <           1  P          >  P           P  x          e  @          �  �'          �  �          �  �          �  h          �  �          �   
      abort   �'          �  �          (            <            G  P           W  �      __dll__     ��       u      ��       �  '          �  �          �  �          �  h(          �  �                      #             .  �'          <  p
          K  @          [  x           �  H          �     ��       �  4      strrchr �'          �  �      calloc  �'          �  @            �            �&          6  �          ^  �          j  �           �  �          �  �          �  p          �  �      Sleep   8(          �  �
      _commode�           �  �            �            `6          !  �          2  �      _stat64  (          F  d           r  8          �  (       optind  |          �  �      __xi_z  (           �  �          �  0          �             �   
          
  T          A  �          i  �&          t  0
          �             �  �          �  �       signal  �'          �  �&          �             �  h             �          %              7  �      strncmp �&          M            `  `6          o  �          |  �          �  �          �  �           �  h          �  �            H          $  �          9  �          Z      ��       m  p          �  �          �  P          �            �             �  �            �          ,  �
          K  0          `  �          �  8      fread   �&          �     ��       �  8      strdup  �&          �  (          �  `(          �  `'          
  @'             P(          :  �      fopen   �&          E  �           r              �  �          �     ��       �              �            �  `          �  �          �  �            �       fclose  �&          '  X          5  `          J        __xl_z  H       __end__              W  �          v  H          �  P          �  �      strcmp  �&          �  x6          �  p      __xi_a             �  �            x'            P          "  H(      __xc_a              7  �          N     ��       g  P           y  P           �     ��       �  �      _fmode  �           �  X          �  �          �  �            �'          '  @          8  �
          I  �          W  8'          l             }  �          �  8          �  (          �  '          �  `          �  `            �      fputc   �&      __xl_c  8              �          G     	    optopt  x          T  �          i  h          |  `          �  �          �  �           �  @          �  �          �  �             h&             �      _newmodep           >   p'      fwrite  �&          H   (          V   �
          e   �          {       ��       �       ��       �   �          �   �          �   `           �   �&          �   �      exit    �'          �      ��       !      ��       '!  h          <!            X!  X      _errno  P'          n!  0          �!  �      _exit   X'          �!  �          �!  �          �!  P
          �!  0'      strlen  �&          �!  �
          �!  H'          �!  P          #"  �      strchr  �'          5"  p(          K"  h'          h"  �          �"  �          �"             �"  �           �"  �          #             '#  �          ?#  �          K#  �          Y#            m#  h          ~#  `
          �#             �#  P           �#  �&          �#  @          �#   '          �#  �      free    �'          �#  �          $  �       $  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame mim_upload_cb print_xml print_usage.isra.0 longopts.0 .rdata$.refptr.optarg imagetype use_network list_mode .rdata$.refptr.optind PATH_PREFIX .text.startup .xdata.startup .pdata.startup ideviceimagemounter.c __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only _vsnprintf _vscprintf local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 __imp_plist_get_string_val ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __imp__vsnprintf __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter mobile_image_mounter_lookup_image .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc plist_get_node_type __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment afc_file_write __rt_psrelocs_start afc_client_free __imp_lockdownd_service_descriptor_free __imp_plist_get_node_type __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __imp_mobile_image_mounter_free __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc .refptr.optind VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll __stdio_common_vsscanf __imp__errno .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler lockdownd_service_descriptor_free .refptr.optarg TlsGetValue __imp_mobile_image_mounter_new __imp_strcmp __bss_start__ __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force __imp_afc_file_open VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_afc_file_write __imp_LeaveCriticalSection __C_specific_handler afc_file_open lockdownd_get_value __imp_afc_file_close __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf plist_free ___crt_xp_end__ __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname mobile_image_mounter_upload_image _tls_used __stdio_common_vsprintf __imp_mobile_image_mounter_upload_image __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service __imp_strerror .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__vscprintf __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __lib64_libapi_ms_win_crt_filesystem_l1_1_0_a_iname __imp_mobile_image_mounter_lookup_image __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection afc_file_close _tls_index __acrt_iob_func _head_libimobiledevice_glue_1_0_dll __native_startup_state __imp_plist_to_xml ___crt_xc_start__ lockdownd_client_free plist_get_bool_val ___CTOR_LIST__ __imp_sscanf .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a mobile_image_mounter_free __imp_lockdownd_get_value mobile_image_mounter_new plist_get_string_val .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen libimobiledevice_glue_1_0_dll_iname __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_dict_get_item afc_client_new afc_make_directory _head_libplist_2_0_dll __IAT_start__ __imp_afc_client_new __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter mobile_image_mounter_mount_image __imp__onexit __DTOR_LIST__ __imp_plist_get_bool_val __imp_afc_make_directory __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ _head_lib64_libapi_ms_win_crt_filesystem_l1_1_0_a __subsystem__ __imp___stdio_common_vsprintf __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc mobile_image_mounter_hangup __imp_VirtualProtect idevice_free __imp_mobile_image_mounter_mount_image ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __imp_fclose __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp_plist_print_to_stream __imp_afc_client_free __imp__crt_at_quick_exit __imp___stdio_common_vsscanf __imp_printf __imp_fread .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR afc_get_file_info DeleteCriticalSection _initialize_wide_environment __imp_mobile_image_mounter_hangup __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_afc_get_file_info __imp_fopen __imp__stat64 plist_dict_get_item __imp_plist_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv plist_print_to_stream plist_to_xml __mingw_app_type 