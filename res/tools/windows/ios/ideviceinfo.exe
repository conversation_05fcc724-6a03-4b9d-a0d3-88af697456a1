MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �9�g   "  � & ( 4   |     �        @                       �    1�  `                                             �  �   �  �   �  �           �  �                          �q  (                   س  �                          .text   (3      4                 `  `.data       P      :              @  �.rdata  �   `      @              @  @.pdata  �   �      ^              @  @.xdata  x   �      b              @  @.bss    �   �                      �  �.idata  �   �      f              @  �.CRT    `    �      v              @  �.tls        �      x              @  �.rsrc   �   �      z              @  �.reloc  �   �      �              @  B/4      �         �              @  B/19     ��     �   �              @  B/31     �'      (   v             @  B/45     <0   0  2   �             @  B/57     �   p     �             @  B/70     5   �     �             @  B/81     �   �     �             @  B/97     �    �  "   �             @  B/113    �   �                  @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��f  1��    H��f  �    H��f  �    H�Lf  f�8MZuHcP<HЁ8PE  tfH��f  �
��  � ��tC�   �&  �&  H�Mg  ����%  H�g  ���T  H��e  �8tP1�H��(Ð�   �v&  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�f  �\  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��f  L�֎  H�׎  H�
؎  � ���  H�af  D�H���  H�D$ �"  �H��8��    ATUWVSH�� H��e  H�-�  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�e  1�����V  �����  ��     ����L  ���e  H��d  H� H��tE1��   1����	  H�
�e  �;�  H�e  H�
����H���$  �  �ҍ  �{Hc�H��H���P%  L�%��  H�Ņ��F  H��1�I���#  H�pH���#%  I��H�D I�H��H���$  H9�u�H�H�    H�-]�  �  H�d  L�B�  �
L�  H� L� H�7�  �R%  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�59d  �   ���������   �"  ��������H�-d  H�
d  ��#  �   �������1�H�����f�     �c#  ���  H�� [^_]A\�f.�     H��c  H�
�c  �   �o#  �7���f�H����������y#  �H��(H�c  �    ������H��(� H��(H��b  �     �z�����H��(� H��(��   H���H��(Ð�����������H�
	   �����@ Ð��������������ATUWVSH�� �պ/   H���#  H�PH��HEڅ���   H�==�  �   ��I��H��K  H���D  �   ��A�	  �   H�
{K  I����!  �   ��I��A�   �   H�
aN  ��!  H�5�[  H�K  L�%^N  �% �   ��H��I��L��H����  H�^�H��t��uڹ   ��H����fD  ��t{�   ��I��A�   �   H�
N  H�� [^_]A\�Q!  H�=Z�  �   ��I��H��J  H���a  �   ��A�	  �   H�
�J  I���!  �   ��I�������   ��I���D  SH��0L�L$(L��H�D$(    ��   ��u0H�|$( t(H��H�
yM  �  �   ���  H�L$(H����   �H��0[�ff.�     UWVSH���  H�5�9  H��H�|$ �U   �H�H�\$ H��$�  �H�3H��H���   H��tH��H���   H��H9�u�H���  [^_]Ð�������������%��  ���%z�  ���%j�  ���%Z�  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%J�  ���%��  ���%��  ���%z�  ���%j�  ���%Z�  ���%J�  ���%:�  ��H��(H��<  H� H��t"D  ��H��<  H�PH�@H��<  H��u�H��(�fD  VSH��(H�_  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�c��� 1�fD  D�@��J�<� L��u��fD  �Z�  ��t�D  �F�     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�#^  �8t�    ��t��tN�   H��([^�f�H��  H�5�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�,Z  Hc�H����    H�Y  �DA �y�qH�q�   �S  �DD$0I��H��Y  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH��X  ��    H��X  ��    H��X  �s���@ H�	Y  �c���@ H��X  �S���H�#Y  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�t  A�   �   H�
"Y  I���  H�t$(�   �K  H��H��I���-  ��  ��    WVSH��PHc56�  H�˅��  H�(�  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�Յ  H��H��H�H�x �     �#  �WA�0   H�H���  H�T$ H�L���  H���}   �D$D�P����t�P���u�o�  H��P[^_� ��H�L$ H�T$8A�@   �   DD�HE�  H�KI��H�S�$�  ��u���  H�
CX  ���d���@ 1��!���H�
�  �WH�
�W  L�D�>���H��H�
�W  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%��  E��tH�e[^_A\A]A^A_]�fD  ���     �9	  H�H��H��   H����  L�-{Z  H��Z  �^�      H)�H�D$0H�S�  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�Y  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�V  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ���  ������H�5��  1�H�}�D  H���  H�D� E��t
H�PH�HI����A��H��(D;%v�  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�W  �s�;H��L�>H���Z����>L9�r��������H�

U  �����H�
�T  ���������H��XH�u�  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
9�  �$  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��T  Hc�H��� 1ҹ   �  H���>  H���  H�ڀ  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �)  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   ��  H���@����   �   �  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   �U  �@����   �   �A  �,����   �   �-  �����������ATUWVSH�� L�%�  L�����  H��  H��t6H�-#�  H�=��  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%Ȓ  WVSH�� �;  ��H�օ�u
1�H�� [^_ú   �   ��  H��H��t3H�pH�5  �8H���[�  H��~  H��H��~  H�C�`�  묃��멐VSH��(��~  �˅�u1�H��([^�D  H�5�~  H����  H�
�~  H��t'1��H��H��tH���9�H�Au�H��tH�B�=  H����  1�H��([^� H�Q~  ��ff.�     @ SH�� ����   w0��tL�.~  ����   �~     �   H�� [�f�     ��u��}  ��t��<�����f.�     ��}  ��uf��}  ��u�H��}  H��t�    H��H�[�|  H��u�H�
�}  H��}      ��}      ��  �l����k����   H�� [������f�     H�
y}  �Ӑ  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���9  H��w{H�TR  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H����  ��u�H��H�� [^_��    1�H��H�� [^_� H��Q  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�IQ  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�	Q  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��P  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�IP  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��O  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�   �   H� H�8�
  H��K  I��H����
  �   �|
  I��H��H���^
  �   �d
  �
   H���
  �H��0[^_�ff.�      AWAVAUATUWVSH��H��*  H�=�*  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��*  ��
  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���X  ��u�L���D  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��)  ���.  ��)      �?   H��H[^_]A\A]A^A_�fD  D�
q)  E��t�H��$�   �8:t�I����H�
�J  �����@ L�D$0Ic�H��L�k��uRM��teD�&)  E��tH��$�   �8:��   H�{ �l  H��$�   �-�(  �8:�Z����:   �U���@ �U���wM����   L��u  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(�
  L�D$(H�������fD  H��$�   �8:�����H��H�
%I  ���������I����H�
_I  �����%������d���HcT$8H��$�   D�t$<H��A��D�5(  H�4u  H���3����
�'  ��t)H��$�   �8:tH��H�
ZI  �E�����'  ���D$81�H�{ tB��'  �D$8��'  H��$�   �8:�
��������k�����D$<��'  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
8'  �'  E����  �����  �Vt  ����  A�E <-�  D��&  E����  <+��  H�t      ����   ��&  ������&  ����f.�     ��s      ��&  D9���  Hc�H�t� �>-��  H�WG  H�x&  A����  A����  �5V&  ����5  �=C&  ���t%��I��A�؉������)����!&  ����)��&  ���(&  H�=&  ����Z���M��tkHc
&  H9|� t]<-�r  A���w  <:�k  ��L���	  H�������D$ M��L��H��L��$�   �����Ã����  H�=�%  �L���L�=�%  <:�,  ��-��   ��L���  H���  �PM��t��Wu	��;��  ��:��   � ��  �C%  �}  f��nr     �(%     1�E1�H�
�E  �*�  �Lr  ��������$  A�E <-�����A��I�������@ �F����  �-   L����  H���0����=�$  �t
�=�$  ���  L�~H���-   L�=�$  � �8  �-   L���  H����   �x:�%���H��q      � �Z$  ��  L�=�q  H�E  ��H�0$  �6$  �s�     �$  �����D  A���<+�#��������    �5�#  H��D  �=�#  H��#  ���uP���t�=�#  ��#  ������#  �����������H��H[^_]A\A]A^A_�f���H�5�p  ��#  �   �ԉ�)�A��I���)��&����|#  �L���L�=`#  <:������ u�W#  �
U#  ��tA�} :��   �8#  �?   �q���fD  A����2����
	#  ���t
�=�"  ��  H�~H�=�"  <-������~ ������5�"  ��H��C  ��"  H��"  ��������A�؉�I��L$<�[����L$<)�)��"  ������     ��H�
�C  �����G���H��H�=r"  1������ ��   �g"  ���^"  D9�|e�W"  H�C  H�9"  ��tA�} :t�W   H�
�C  �����"  W   A�} :������:   �I�����!  �S�����!  �����H�H�D� H��!  �D$     M��L��H��L��$�   �������H��B  H��!  ������x:�\�������!  A9�~Hc�H�D� H��n  �:���H�LB  H�m!  �w!  ��tA�} :t��H�
#C  ������P!  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1��  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �k  � ��j  H� H��G  H� H�M��t	A�$�  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���`  ���   ����(  ��  � ��  H� H���  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H��A  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H��@  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H�A  ��~   H�  �j   H��  �V   H��  H��(�f.�     H��(H��@  ��>   H��  �*   H��  �   H��  H��(Ð����������%�  ���%�  ���%�  ���     �%�  ���%�  ���%�  ���     �%:  ���%:  ���%:  ���%:  ���%:  ���%:  ���%:  ���%:  ���%:  ���     �%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���%Z~  ���     �%�}  ���%�}  ���%�}  ���%�}  ���%�}  ���     �%Z}  ���     �%"}  ���%"}  ���%"}  ���%"}  ���%�|  ���%�|  ���%�|  ���     �%�|  ���%�|  ���%�|  ���%z|  ���%j|  ���%Z|  ���%J|  ���%:|  ���%*|  ���%|  ���%
|  ���     AWAVAUATUWVSH��  H�5f  E1�E1�H��4  A��H��L��$�   �V����D$0   L��$   H�D$`    H�D$h    H�D$p    H�D$x    �D$P    �D$T   �D$L    �D$H�����D$<�����D$D�����H��D$@    1�H�5�0  �D$8    �D$4    fD  M��I��H��D��H�D$     ���������  ��a��w.Hc�H���   �y|  A�#   �   H�
�/  I���O���H�M �   �����A�   D��H�ĸ  [^_]A\A]A^A_��D$8   �o����D$@   �b����D$0   �U���H�V>  H��9 �Q  �����D$H�4���H�5>  H� �8 �v  L��H�D$X����H�L$X�b���I������H�M 1��?���E1��\����   ����������D$L   �����H��=  H��9 ��  �����D$D����H��=  H� �8 ��  L��H�D$X�:���H�L$X�����I�������D$4   �s���H�t=  H� �8 �����H��H�D$X�����H�L$X����H���A���H�B=  H��9 �=  ������D$<� ����D$P   �����D$T   ����D�D$0H�L$hL��D$X����D�T$X���K  �|$4 D�T$0H�T$`L��.  H�L$h�B  �m���D�T$0�Å��=  H�?5  H��u�>�     H��H������H��u%H�H��u�   �,z  I��H�Z1  H���:����|$8 �   �|$@ ��  �|$D��  �|$H�  �|$<�r  �|$< ��   H�t$xH�L$`M��H��I��������u^H�L$xH��tT�D$T����  ���q  L��$�   H�T$p����H�T$pH�
�/  �I���H�L$p�_���H�L$x����1�H�D$xH��tH���A���H�L$`�G���H�L$h�U��������#���D�T$0��������4  H�T.  �K���H�L$`L��.  H��I�������H�5�w  ��  ��1��'���H�L$`L��.  H��I�������d   ��1�����H�L$`L��.  H��I������1������H�L$`L��.  H��I���e�����  ��1�����H�L$`L�c.  H��I���@���1�����H�L$`L�e.  H��I���"���f���y���L��-  H��I��H�L$`�����Å���  H�
�-  �����`����   H�[,  �>���H�L$`H��L�/,  I������H�
[,  ����H�L$`H��L�H,  I�������Å��}  H�
?,  ����������   ��w  A�!   �   H�
+  I���t���H�M �   ����� ����!3  H��,  ����H�L$`H��L��,  I������H�5�u  ��  �ֹ   �n���H�L$`H��L��,  I���������   �ֹ   �F���H�L$`H��L��,  I��������   �%���H�L$`H��L��,  I�������   ����H�L$`H��L��,  I��������  ���օ��|����   ��v  A��H��+  H�����������1�����H�L$`L��*  H��*  I���0����Å���   H�
+  �B��������   �2v  A��H��*  H���@����l����L$D�R���H�L$`L�$+  H�;+  I��������Å��Q���H�
:+  ������-����L$H����L�P+  H�+  I�������   ��u  A��H��*  H�������������   ��u  A�   �   H�
�(  I���f���H�M �   ����������   �Xu  A�    �   H�
�(  I���.���H�M �   ����������M��������   �u  H�L$xH���*���H�L$x�����|$L ��   �   L�%�t  A��H�L$xH�������M����   �|$P t�H��/  L�-�+  L���   H�+L��H���v���H�L$`E1�I��H���s�����u'H�|$x t�   A��H�L$xH�������
   �`���H��I9�u��P��������[���D�T$0M���\  L��H�
 (  ����D�T$0�����1�H�L$`L��$�   H��$�   H��$�   H�P*  �����H�L$`H�U*  L��$�   �����H��$�   H��tH�
I*  ����H��$�   ����H��$�   H��tH�
>*  �v���H��$�   �����|$4�����H�L$`L�/*  H�:*  �v���H�L$`L�>*  H��  �^���H�L$`L�=*  H�@*  �F���H�L$`L�E*  H�K*  �.����Y�����D�T$0������   H���s  A��I��H�'  H������H�L$h����D�T$0����H�
�&  �����D�T$0�����;�����������������������B @           ��������                                                                                                                                                                                                                                oc @   �c @   �c @   �c @   �c @   �c @   �c @   �c @   d @   d @   0d @   Ed @   Td @   ed @   md @   xd @   �d @   �d @   �d @   �d @   �d @   e @   e @   e @   &e @   .e @   5e @   <e @   Ie @   Ue @   `e @   fe @   ve @   �e @   �e @   �e @   �e @   �e @   �e @   �d @   �d @   �e @   �e @   �e @   f @   f @   $f @   4f @   >f @   Xf @   ef @   rf @   �f @   �f @   �f @   �f @   �f @   �f @   �f @   �f @   e @   &e @   �f @   g @   g @   3g @   Ng @   Pg @   dg @   zg @   �g @   �g @   �g @   �g @   �g @   �g @   �g @   h @   0h @   \h @   rh @   �h @   �h @   �h @   �h @                           *m @                   d       0m @                   h       5m @                  u       :m @                   n       Bm @                  q       Im @                  k       Mm @                   s       Tm @                   x                                        C @           ��������        ����                           ������������    u @   ?                     ����            p3 @           �3 @           �3 @           �� @   �� @   �5 @   �5 @    4 @   P5 @   �4 @   04 @   �T @   �T @   �T @      �p  U @    U @   PDT PST 05 @   5 @                                                                                                                                                                                                                                           com.apple.mobile.battery Usage: %s [OPTIONS]
   Show information about a connected device.

  -s, --simple       use a simple connection to avoid auto-pairing with the device
  -u, --udid UDID    target specific device by UDID
  -n, --network      connect to network device even if available via USB
  -w, --on wifi      enable wifi sync (bonjour) on device over usb 
  -o, --off wifi     disable wifi sync (bonjour) on device over usb 
  -q, --domain NAME  set domain of query to NAME. Default: None
  -k, --key NAME     only query key specified by NAME. Default: All keys.
  -c, --clean        to clean extra information from device info plist
  -x, --xml          output information as xml plist instead of key/value pairs
  -h, --help         prints usage information
  -d, --debug        enable communication debugging

   Known domains are:

   %s
 
PhoneCheck 2.0.1
 %s:  ActivationStateAcknowledged      BasebandActivationTicketVersion BasebandCertId BasebandChipID BasebandKeyHashInformation AKeyStatus SKeyHash SKeyStatus BasebandMasterKeyHash BasebandRegionSKU BasebandSerialNumber BasebandStatus BluetoothAddress BoardId BrickState BuildVersion CPUArchitecture    CarrierBundleInfoArrayCFBundleIdentifier CFBundleVersion IntegratedCircuitCardIdentity  InternationalMobileSubscriberIdentity MCC MNC SIMGID1 SIMGID2 CertID ChipID ChipSerialNo DeviceClass DeviceName DieID EthernetAddress FirmwareVersion FusingStatus GID1 GID2 HardwarePlatform HasSiDP HostAttached MLBSerialNumber MobileSubscriberCountryCode MobileSubscriberNetworkCode NonVolatileRAM auto-boot backlight-level boot-args com.apple.System.tz0-size oblit-begins obliteration ota-snapshot-update PartitionType PasswordProtected PhoneNumber PkHash ProductName ProductionSOC ProtocolVersion ProximitySensorCalibration SIMTrayStatus SoftwareBehavior SoftwareBundleVersion SupportedDeviceFamilies[1] 0 TelephonyCapability TimeIntervalSince1970 TimeZone TimeZoneOffsetFromUTC TrustedHostAttached UniqueDeviceID UseRaptorCerts Uses24HourClock WirelessBoardSerialNumber kCTPostponementInfoPRIVersion kCTPostponementInfoPRLName   kCTPostponementInfoServiceProvisioningState kCTPostponementStatus SupportedDeviceFamilies SBLockdownEverRegisteredKey SDIOManufacturerTuple DevicePublicKey SDIOProductInfo     ERROR: UDID must not be empty!
 ERROR: 'domain' must not be empty!
     ERROR: 'key' must not be empty!
        ERROR: 'Mode' must not be empty!
 dhu:npq:k:a:v:m:swocx ERROR: Device %s not found!
 ERROR: No device found! ideviceinfo        ERROR: Could not connect to lockdownd: %s (%d)
 EnableWifiConnections   com.apple.mobile.wireless_lockdown  WirelessBuddyID WifiSync Enabled!   ERROR: Could not Enable WifiSync, lockdown error %d
 WifiSync Disabled! ERROR: Could not Disable WifiSync, lockdown error %d
 AssistiveTouchEnabledByiTunes com.apple.Accessibility Operation Successful! ERROR: lockdown error %d
 InvertDisplayEnabledByiTunes Success! MasterStereoBalance MonoAudioEnabled VoiceOverTouchEnabledByiTunes    ClosedCaptioningEnabledByiTunes SpeakAutoCorrectionsEnabledByiTunes VoiceOverTouchEnabled %s RegulatoryModelNumber DeviceEnclosureColor RegulatoryModelNumber: %s
 DeviceEnclosureColor: %s
 TotalDiskCapacity com.apple.disk_usage BatteryCurrentCapacity SetupDone com.apple.purplebuddy IsAssociated com.apple.fmip 
<< %s >>
       WARNING: Sending query with unknown domain "%s".
 debug help udid network domain key simple xml ����	�����������	���	���	�������	���	���|���	���[���N���A�������=���	���0���	�������o���4�������com.apple.iqagent com.apple.PurpleBuddy com.apple.mobile.chaperone      com.apple.mobile.third_party_termination com.apple.mobile.lockdownd     com.apple.mobile.lockdown_cache com.apple.xcode.developerdomain com.apple.mobile.data_sync      com.apple.mobile.tethered_sync  com.apple.mobile.mobile_application_usage com.apple.mobile.backup com.apple.mobile.nikita com.apple.mobile.restriction  com.apple.mobile.user_preferences       com.apple.mobile.sync_data_class        com.apple.mobile.software_behavior      com.apple.mobile.iTunes.SQLMusicLibraryPostProcessCommands      com.apple.mobile.iTunes.accessories com.apple.mobile.internal com.apple.fairplay com.apple.iTunes com.apple.mobile.iTunes.store com.apple.mobile.iTunes                  ` @   �m @   �l @   �m @   �m @    n @   )n @   Hn @   hn @   �n @   �n @   �n @   �n @   
o @   "o @   @o @   ho @   �o @   �o @   �o @   p @   j @   6p @   Ip @   Zp @   xp @                 �?      �                        0 @                            � @   � @   l� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ����L������l���|�������\���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
       ��� ��� ��� ��� ������� ����������{���                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               T @           PT @            C @              @           �} @           �} @           �q @           �T @           H� @           �� @           h� @           d� @           `� @            � @           �� @           @� @           H� @            � @           � @           � @           (� @           p� @            T @           �� @           p @           � @           P� @           p� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  �  t�  �    ��     �  ��     Z  ��  `  �  ��  �  �  Ȑ  �  �  ̐     /  А  0  �  ؐ  �  �  �  �  �  �  �  �   �  �  I  �  P  �  �  �    �     ^  4�  `  l  <�  p  -!  @�  0!  �!  H�  �!  "  X�  "  �"  d�  �"  �#  p�  �#  �#  x�  �#   $  |�   $  �$  ��  �$  @%  ��  @%  w%  ��  �%  �%  ��   &  6&  ��  @&  �&  ��  �&  �'  ��  �'  �(  ��  �(  2)  ��  @)  �,  ��  �,  �2  ؑ   3  "3  �  03  P3  ��  P3  p3   �  p3  �3  �  �3  �3  �  �3  4  �   4  #4  $�  04  �4  (�  �4  
5  8�  5  .5  H�  05  E5  P�  P5  ~5  T�  �5  �5  \�  �5  �5  d�   6  66  l�  @8  �B  ��  �B  �B  t�                                                                                                                                                                                                                                                                                                          B   b  
 
20`pP�	 B  `7     �  �  p  �  	 B  `7     �    p     B        
 
20`pP� R0 [ 0`pP
 7 0`
p	P���� B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                             �          �  س  p�          $�  0�  ��          p�  @�  �          ��  ��  �          ��  ��  �          �  ȴ  0�          �  �  @�          @�   �  p�          ��  0�   �          �  ��  P�          �  �  p�          P�  0�  ��          ��  X�                      ��      ��      Ķ      �      ��      �      8�      X�      p�      ��              ��              ��      з      �      �      �      .�      F�      d�      l�      z�      ��              ��              ��      ��              ĸ      Ը      ޸      �              �              �      �      &�      0�      :�              D�      R�      `�      n�      x�      ��      ��      ��      ι      ֹ      ��      �      $�      4�      V�      ^�      f�              p�      ��      ��      ��      ��      ֺ      ޺      �      �              ��      �      �              �      &�      4�      @�              L�      d�      ��      ��      ��      ��      ̻              ��      ��      Ķ      �      ��      �      8�      X�      p�      ��              ��              ��      з      �      �      �      .�      F�      d�      l�      z�      ��              ��              ��      ��              ĸ      Ը      ޸      �              �              �      �      &�      0�      :�              D�      R�      `�      n�      x�      ��      ��      ��      ι      ֹ      ��      �      $�      4�      V�      ^�      f�              p�      ��      ��      ��      ��      ֺ      ޺      �      �              ��      �      �              �      &�      4�      @�              L�      d�      ��      ��      ��      ��      ̻              f idevice_free  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new  � lockdownd_client_new_with_handshake   � lockdownd_get_device_value    � lockdownd_get_value   � lockdownd_set_value   � lockdownd_strerror     plist_print_to_stream DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  W atoi   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr ~strstr   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fputc � fwrite  � putchar � puts  6 _strdup � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     plist_dict_get_item    plist_dict_remove_item     plist_free    5 plist_new_bool    : plist_new_real    ; plist_new_string  L plist_to_xml   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  libimobiledevice-glue-1.0.dll   (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  api-ms-win-crt-convert-l1-1-0.dll   P�  P�  api-ms-win-crt-environment-l1-1-0.dll   d�  d�  d�  d�  api-ms-win-crt-heap-l1-1-0.dll  x�  api-ms-win-crt-math-l1-1-0.dll  ��  ��  ��  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll Ȱ  Ȱ  Ȱ  api-ms-win-crt-string-l1-1-0.dll    ܰ  ܰ  ܰ  ܰ  api-ms-win-crt-time-l1-1-0.dll  �  �  �  �  �  �  �  libplist-2.0.dll                                                                                                                                                                                                                                                                                                                                                                            0 @                    @                   0 @     @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     �   P  �    ���� �(�0�8�@�H�P�X�`�h�p�x�������������������ȠРؠ����� ���� �(�0�8�@�H�P�X�`�h�p�x�������������������ȡСء����� ���� �(�0�8�@�H�P�X�`�h�p�x�������������� � �@�`������0�`�p�����������������ȤФؤ������   p  �   ����������ȠРؠ����� ���� �(�0�8�@�H�P�X�`�h�����ȡСء`�p�����������Ц�� �� �0�@�P�`�p�����������Ч�� ��   �     � �8�@�                                                                                                                        ,               @   $                      <    �&       P @   3      @8 @   �
                      ,    /M         @   �                           �S                           �Y                       ,    Z       � @                              �[                       ,    *\         @   �                           �c                           {d                       ,    �e       � @   �                       ,    ,i       � @                              �i                       ,    Uj       � @   =                      ,    ��         @   L                           ��                       ,    �       p @   �                      ,    ��       0! @   b                          џ                           Z�                       ,    *�       �# @   �                      ,    ��       �' @   �                          8�                       ,    ��       p3 @                          ,    s�       �3 @   H                       ,    &�       �3 @   2                           ��                       ,    j�        4 @                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   �&   �  +GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  h           9  double char �   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int ,unsigned char 
�     DWORD ��   float 
�   signed char short int long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �W  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I
VT_BSTR_BLOB �
VT_VECTOR  
VT_ARRAY   
VT_BYREF  @
VT_RESERVED  �
VT_ILLEGAL ��
VT_ILLEGALMASKED �
VT_TYPEMASK �  _iobuf !
C  _Placeholder #    FILE /  !optind �   !optarg    option  >�  name @�   has_arg A�   flag B?  val C�    l  
�   "�  �   G
  no_argument  required_argument optional_argument  uint8_t $
  uint32_t (�   plist_t 	Y  �   	|�  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ plist_err_t 	�;  �   
'�  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 
0�  #?  
2 �  $?  idevice_t 
3�  
�  idevice_options �   
9L  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  �   $1
  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t PL  #  R)W
  $  lockdownd_client_t S#w
  
K
  �  �
  �    domains #|
  	�p @   %plist_dict_remove_item 	�
�
  +  �   plist_dict_get_item 	�
+  �
  +  �   strstr `    �  �   strrchr ]  9  �  �    lockdownd_client_free �1
  a  \
   plist_free 	�
z  +   lockdownd_get_device_value �1
  �  \
    W   plist_print_to_stream 
;�  +  �   
C  "�  plist_to_xml 	��    +  W     
  lockdownd_get_value �1
  H  \
  �  �  H   
+  Sleep a  (   plist_new_real 	�
+  �  {    plist_new_string 	�
+  �  �   lockdownd_set_value �1
  �  \
  �  �  +   plist_new_bool 	�
+  �  
   idevice_free 
��    �   fprintf ��   ;  �  �  & lockdownd_strerror G
�  a  1
   lockdownd_client_new_with_handshake �1
  �  �  �  �   
\
  lockdownd_client_new �1
  �  �  �  �   printf ��   �  �  & idevice_new_with_options 
��  '  '  �  �   
�  getopt_long M�   ^  �   ^  �  c  ?   
#  
�  atoi ��   �  �   strdup l  �  �   %free �     __acrt_iob_func ]�  �  �    idevice_set_debug_level 
l�  �    'ClearExtraKeys   @   c       ��  node H  �  �  extrakeys #�  ��z-max �	�   U.B @   5       (i �
�   �  �  ^ @   �
  �  Rv Qt  n @   �
  Rv Qt    �  �  �   T 'GetValueFromLockDown � @   U       ��  client .\
  C  ?  /8  B�  ^  Z  key V�  {  u  node 
+  �h� @     m  R�RQ�QXs Y�h � @   �  �  R	jc @   Qs   @   �  �  R1  @   �   0main p�   @8 @   �
      �?#  )argc p�   �  �  )argv pW  �  �  client r\
  ��|	ldret s1
  A    device t�  ��|	ret u�    
  	simple v�   r  n  	wifisync w	�   �  �  	off_wifisync x	�   �  �  	assistive_touch {
�   �  �  	voiceover |
�   �  �  	monocolor }
�   �  �  	clean 	�       	format ��   4  2  	udid �  T  B  18  �  �  �  	alldomain �	�   �  �  	key �  �  �  xml_doc �  ��|xml_length �  ��}node �
+  ��|	lookup_opts ��      	c ��   1  %  longopts �?#  ��}*�A @   �       �  RegulatoryModel �  ��}EnclosureColor �  ��}�A @   z  S  Q	
l @   X��} �A @   z  y  Q	#l @   X��} �A @   �  �  R	8l @    B @   �  B @   �  �  R	Sl @    'B @   �   *A @   Z       r  (i �!�   o  i  A @   �    R} Qv  -A @     :  Qv X0Yt  AA @   �  Q  R1 NA @   �  XA @   ?&  R:  2�#  p; @          �  3�#  �#  �  �  |; @   �
  Ru   z8 @   Z&  "9 @   ,  �  R} Qv Xt Y| w 0 G9 @   �  
  R2 a9 @   i&  4  R	 i @   Q1X# o9 @   �#  [  Q1d#  } o#  v  �9 @   h  �9 @   �  �  R  �9 @   �  �  R��| : @   �#  �  Q0d#  } o#  v  #: @   �  �  R1 M: @   h  v: @   �  �  R~  �: @   �    R��| �: @   �  /  Ru  �: @   �  I  R��| �: @   h  ; @   �  ~  R��|Q~ X��|� K; @   �  �  Q��|X	�i @    �; @   �  �  R2 �; @     �  Q	�l @   Xu   < @       Qu X Yt  6< @   �  $  Q��|X��} G< @   �  C  R	
l @    Q< @   �  [< @   a  o< @   �  u  Ru  y< @   9  �< @   �  �< @   a  �< @   a  �  a�{      � �< @   �  �  Qs X	jk @    �< @   M  �  R
� �< @   �    R0 �< @   �  7  Qs X	~k @    �< @   M  O  Rd �< @   �  f  R0 = @   �  �  Qs X	�k @    = @   �  �  R0 3= @   �  �  Qs X	�k @    := @   M  �  R
� A= @   �  �  R0 X= @   �    Qs X	�k @    _= @   �  3  R0 v= @   �  X  Qs X	�k @    = @   a  y  a�{         �= @   �  �= @   �&  �  R	ak @    �= @   �  �  R1 �= @   �  �  Qs X	 j @    �= @   �     R	;j @    �= @   �  %  Qs X	<j @    > @   �&  D  R	Lj @    "> @   �  [  R2 <> @   i&  �  R	Pi @   Q1X! J> @   �#  �  Q1d#  } o#  v  c> @   a  �  a�{      �? z> @   �  �  Qs X	jk @    �> @   M    R
� �> @   �  "  R1 �> @   �  G  Qs X	~k @    �> @   M  _  R� �> @   �  v  R1 �> @   �  �  Qs X	�k @    �> @   �  �  R1 �> @   �  �  Qs X	�k @    �> @   �  �  R1 ? @   �    Qs X	�k @    ? @   M  ,  R
� /? @   �  C  R2 A? @     h  Q	*k @   Xs  M? @   �    R0 h? @   �  �  Q	j @   X	 j @    ~? @   �&  �  R	�j @    �? @   �  �  R2 �? @        Q	`j @   Xs  �? @   �  !   R��|� �? @   �  M   Q	�j @   X	�j @    �? @   �&  l   R	k @    �? @   �  �   R��|� @ @   �  �   R2  @ @     �   Q	�j @   Xs  0@ @   �  �   R2 J@ @   i&  !  R	�h @   Q1XO X@ @   �#  *!  Q1d#  } o#  v  h@ @   �  A!  R2 �@ @   i&  k!  R	(i @   Q1X  �@ @   �#  �!  Q1d#  } o#  v  �@ @   �  �!  R1 �@ @   �  �@ @   �  �!  R1 �@ @   �  kA @   �  �A @   �  "  R	�i @   Q~  JB @   �  8"  Q	l @   X	ml @    bB @   �  d"  Q	 ` @   X	�l @    zB @   �  �"  Q	�l @   X	�l @    �B @   �  �"  Q	�l @   X	�l @    �B @   ;  �"  Rs  �B @   �  �"  R2 �B @     #  Q	�i @   Xt Ys  �B @   �  �B @   �&  R	�i @     �  O#  �    4print_usage Q
�#  argc �   argv *W  is_error 4�   i S	�   name T   5is_domain_known F�   �#  68  F"  i H	�    7O#  P @   k      �?&  z#       �#  V  F  �#  �  �  o#      d#  0  .  i @     L$  Rs Q/ � @   �  c$  R2 � @     �$  Q	` @   Xs  � @   �  �$  R2 � @   i&  �$  R	0` @   Q1X
	 � @   �  �$  R2 � @   i&  
%  R	:c @   Q1XF � @   �  !%  R2  @     ?%  Q| Xs  % @   �  V%  R1 ; @   �  m%  R2 8_ @   i&  �%  R	Wc @   Q1XB m @   �  �%  R1  @     �%  Q	` @   Xs  � @   �  �%  R1 � @   i&  &  R	0` @   Q1X
	 � @   �  +&  R1 � @   �  R1  putchar __builtin_putchar 9__main __main fwrite __builtin_fwrite puts __builtin_puts  ]   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	0� @   atexit ��   �  Y   __main 5� @          ��  � @   �   	__do_global_ctors  ` @   j       �  
nptrs "�   U  O  
i #�   m  i  � @   j  R	  @     	__do_global_dtors   @   :       �[  p [  	�S @    	   �   (
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  u    char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
O  �   �,  __uninitialized  __initializing __initialized  O  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	�S @   �  	�S @   =  
"	H� @   [  	@� @    �    �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  e  _dowildcard  �   	 T @   int  }     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 :  "  � @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   � @          � �    F  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _newmode �   	P� @   int  �   t  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 u  ]    @   �       /  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	l� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�q @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�q @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	h� @   __mingw_initltsdyn_force ��   	d� @   __mingw_initltssuo_force ��   	`� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@    @   /       �}  
g  �  �  �  
}  *M  �  �  
r  ;d  �  �  % @   �   __tlregdtor m�   � @          ��  func m  R __dyn_tls_init L@  	  g  �  }  *M  r  ;d  pfunc N
$  ps O
�    �  0 @   �       ��  �  �  �  �  �  �  	  
	  �  �  �  ` @    ` @   +       L�  �  6	  2	  �  G	  E	  �  S	  O	  �  f	  b	  �  z	  v	   � @   �    �    [
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 c  K  E  _commode �   	p� @   int  w   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  	    char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  � @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   � @   �       �0  pexcept 0  �	  �	  type 
�  �	  �	  - @   b  �  R2 V @   7  Q	�r @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ^
  F
  � @          w  _fpreset 	� @          � �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �  __mingw_app_type �   	�� @   int  G   J  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  � @   =      	  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �� @   ]      ��  4was_init �%  	�� @   5mSecs �%  (
  &
  !�  E @   �   �4  �  �  �  6�   
�  @
  0
  
�  �
  y
  
�  �  �  
�  A  ;  

  e  Y  
  �  �  "E  �   �  
F  �  �  
[   
  
  Y @   `  R	Ht @   Xu w t     � @   � @          �;  �  Q
  O
  �  \
  Z
  �  k
  i
    � @   � @          �  u
  s
  �  �
  ~
  �  �
  �
  � @   �  Ru    !  � @   �   ��  �  �
  �
  �  �
  �
  �  �
  �
  7  � @   �   �  �
  �
  �  �
  �
  �  �
  �
  � @   �  Ru      g @   g @   
       �w  �  �
  �
  �  �
  �
  �  �
  �
    g @   g @   
       �      �      �      o @   �  Ru      � @   � @          �   �  )  '  �  4  2  �  C  A    � @   � @          �  M  K  �  X  V  �  g  e  � @   �  Ru    "$  �   �  
)  u  o  83  �   
4  �  �    � @   � @   
       s�  �  �  �  �  �  �  �  �    � @   � @   
       �  �  �  �  �  �  �  �  �  � @   �  Rt      
 @   `    R	t @     @   `  R	�s @      9�  � @   X       �|  
�  �  �  :�  �� @   
  Yu    @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �P @   b      �`  &addr ��  �  �  b �:  ��h �g  ;  /  i �%  l  f  >0 @   P       �  new_protect �
u  �  �  
d @   
  �  Ys  n @    
  | @   `  R	�s @     
� @   �
  �  Rs  � @   n
  
 @   E
    Q��X0 
� @   `  >  R	�s @    � @   `  R	`s @   Qs   ?__report_error T� @   i       �/  &msg T  �  �  @argp ��   �X
 @     �  R2 
& @   /  �  R	@s @   Q1XK 
5 @       R2 
C @   �
  !  Qs Xt  I @   �
   Afwrite __builtin_fwrite   �   "  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �` @          �P  f ,�  �  �  l @   �  R�R  __mingw_raise_matherr �  @   >       �typ !�   �  �  name 2�  �  �  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@Y @   R�@   �    4  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  7  _fmode �   	�� @   int  �   b  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 c  K  p @   �      q  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   p @   �      ��  'exception_data �-�  (    old_handler �
	  m  ]  action ��   �  �  reset_fpu ��   8  *  
� @   �
  �  R8Q0 (� @   �  R�R 
'  @   �
  �  R4Q0 =  @   �  R4 
�  @   �
    R8Q0 
�  @   �
  7  R8Q1 
�  @   �
  S  R;Q0 �  @   f  R; �  @   y  R8 
�  @   �
  �  R;Q1 
! @   �
  �  R4Q1 
#! @   �
  �  R8Q1 )(! @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 t  \  0! @   b      �  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �" @   �       �n  	hDllHandle z�  �  y  	reason {<    �  	reserved |S  �  w   # @   K       �  
keyp �&�  �  �  
t �-�      4# @   �  
[# @   C  R	 � @     !n  �" @   �" @          �  �  �" @   )
   "n   # @   �   �E  #�   �  u# @   )
    e# @   6  
�# @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   " @   �       �d	  	key A(<  $    
prev_key C�  J  D  
cur_key D�  i  a  @" @   �  B	  Rt  s" @   �  
|" @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �! @   o       �$
  	key *%<  �  �  	dtor *1.  �  �  
new_key ,$
    �  �! @   �  �	  R1QH �! @   �  
  Rt  
" @   �  Rt   �  &n  0! @   p       ��  #  !  '�  h! @          �
  �  -  )  l! @     q! @     (�! @   Rt   J! @   �  �
  R|  )�! @   �  R	 � @      �    A  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  v  _CRT_MT �   	T @   int  �    o  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 5    �  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �# @   �      �  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �& @   �       �7
  i �(�   H  D  �  �	`  �  �	  [  W  importDesc �@  y  w  �  �^
  importsStartRVA �	I  �  �  �  �& @   	�  ��  �  �  �  �  �  	�  �& @    �  �  �  �  �  �  �  �  �  �      M  ' @   ' @   J       �q  �  �  f  }  �  �  �    
  �  -  +    
_IsNonwritableInCurrentImage �  @& @   �       ��  pTarget �%`  >  6  �  �	`  rvaTarget �
�  c  a  �  �^
  m  k  �  @& @   �  �/  �  �  �  �  �  	�  P& @    �  �  �  �  �  y  u  �  �  �      M  t& @   t& @   I       �q  �  �  f  }  �  �  �  �  �  �  �  �    
_GetPEImageBase �`   & @   6       �0  �  �	`  	�   & @   g  �	�  g  �  �  �  	�  & @    w  �  w  �  �  �  �  �  �  �       
_FindPESectionExec y^
  �% @   s       �%  eNo y�   �  �  �  {	`  �  |	  �  �  �  }^
  �  �  �  ~�       	�  �% @   L  �	�  L  �  �  �  	�  �% @    \  �  \  �  �      �  #  !       
__mingw_GetSectionCount g�   @% @   7       ��  �  i	`  �  j	  /  -  	�  @% @   1  m	�  1  �  �  �  	�  P% @    A  �  A  �  �  ;  7  �  L  J       
__mingw_GetSectionForAddress Y^
  �$ @   �       �  p Y&s  ^  V  �  [	`  rva \
�  �  �  �  �$ @     _�  �    �  �  �  	�  �$ @      �    �  �  �  �  �  �  �      	M  �$ @   &  c
q  �  �  f  &  }  �  �  �  �  �  �  �  �     
_FindPESectionByName :^
   $ @   �       �M  pName :#�  �  �  �  <	`  �  =	      �  >^
  )  '  �  ?�   3  1  �  5$ @      F  �     �  �  �  �  E$ @    E$ @          �  �  �  >  <  �  H  F     &/$ @   �  -  Rt  '�$ @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �# @   ,       �~  �  V  R  �  h  d  �  �  	�  �# @    �   �  |  v  �   �  �  �  �  �  �  �     *M  �# @   P       �f  �  �  +q  Q}  �  �  �  �  �  �  �  �    8   )  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �' @   �      5"  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	@T @   �  	<T @   �  	8T @   &__mingw_optreset D#  	x� @   �  	p� @   
place f6  	0T @   
nonopt_start i#  	(T @   
nonopt_end j#  	$T @   �   �  �   ! �  
recargchar m�  	 v @   
recargstring n�  	�u @   �   :  �    *  
ambig o:  	�u @   �   f  �   ' V  
noarg pf  	`u @   �   �  �    �  
illoptchar q�  	0u @   
illoptstring r�  	u @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  P3 @           ��
    ,#  @  <    ,+�
  V  R  �  ,>/  l  h    -�
  �  ~  idx --@  � k3 @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  03 @           �q    #  �  �    &�
  �  �  �  9/  �  �     �
  �  �  idx  -@  � K3 @     R�RQ�QX�XY�Yw � w(1  getopt #   3 @   "       �    #  �  �    !�
      �  4/      3 @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#  �, @   (      ��    C#  :  .    C*�
  t  h  �  C=/  �  �    D�
  �  �  idx D*@  � )flags D3#    �   oli F6  F  <   optchar G#  z  n  *�  G#  �  �  +posixly_correct H
#  	 T @   ,start g�- @      C
  Rt Qu Xs Yv  ^. @   �	  [
  R}  �. @   �  �
  Rv Q} X~ Y�  �. @   �	  �
  R} Qs  &/ @   �	  �
  R	�t @   Q0X0 q/ @   �	  �
  R} Q- �/ @   �	  	  R} Q- �0 @      '  Ru Yv  �1 @      T  R���Qt Xs Yv  �1 @   �  y  R	0u @   Qs  2 @   �  �  R	 v @   QW t2 @   �  �  Rv Q} X~ Y� w 0 �2 @   �  R	 v @   Qs   -parse_long_options �#  @) @   �      �     �"�
  �  �  �  �5/  �  �    ��
  �  �  idx �*@      .�  �3#  � current_argv �6  -  )  has_equal �6  L  <  current_argv_len �	�   �  �  i �#  �  �  ambiguous �	#  �  �  match �#  :  .  �) @   �	  /  Ru Q= * @   S	  S  Ru Q~ Xt  $* @   w	  k  R~  �* @   �  �  R	�u @   Qt Xu  �+ @   w	  �  Ru  �+ @   �  �  R	u @   Qu  , @   �  �  R	`u @   Qt Xu  k, @   �  R	�u @   Qu   !permute_args ��' @   �       ��  panonopt_start �#  o  i  panonopt_end �&#  �  �  opt_end �8#  �  �    ��
  �  �  cstart �#  �  �  cyclelen �#      i �#  !    j �#  2  (  ncycle �#  Y  W  nnonopts �&#  e  _  nopts �0#  �  �  pos �7#  �  �  swap �6  �  �  /�  �' @    �  ��  �  �  �    �  0�  1�         2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�( @   �       ��  fmt ~/  9  3  
ap ��   �X4�  �( @    �( @   W       �  T  R    `  \  5�( @   @	  �( @   	  u  R2 �( @   �  �  Q	�t @   Xu  ) @   	  �  R2 ) @   �  �  Qs Xt  ) @   	  �  R2 *) @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �+  _MINGW_INSTALL_DEBUG_MATHERR �   	PT @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  p3 @          �+  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	`T @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  p3 @          �_File *�  �  {  _Format J�  �  �  _ArgList Z�   �  �  �3 @   �  R0Q�RX�QY0w �X   �   !  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �3 @   H       B,  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	pT @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �3 @   H       �_Format .�  �  �  
ap 
�   �Xret 	  �  �  �3 @   �  �  R1 �3 @   �  R0Xs Y0w t    �   }"  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 |  d  �3 @   2       �,  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�T @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �3 @   2       �_File )�       _Format I�      
ap 
�   �hret 	  4  2  
4 @   �  R0Q�RX�QY0w �   �   �#  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 a  I  a-  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�T @     
	�T @    �   �$  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ~  f   4 @         �-  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	U @   �      �   __imp_at_quick_exit g)  	U @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	U @   
initial_tzname1 }
V  	 U @   
initial_tznames ~�  	�T @   
initial_timezone 
%  	�T @   
initial_daylight �  	�T @   __imp_tzname ��  	�T @   __imp_timezone ��  	�T @   __imp_daylight ��  	�T @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�T @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�T @   �	  __imp__amsg_exit �V  	�T @   F  __imp__get_output_format �\
  	�T @   -
  __imp_tzset ��  	�T @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�T @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �5 @   5       ��
  file �!�
  L  F  fmt �6�  k  e  
ap ��   �h+ret �  �  �  �5 @   �
  R4Q�RX�QY0w �  ,tzset "�5 @   6       �  -  �5 @   �5 @   -       ��5 @   +  �5 @     �5 @       ._tzset �/_get_output_format nF   4 @          �0_amsg_exit iP5 @   .       ��  ret i  �  �  a5 @   z  �  R2 s5 @   Q  �  Q	@v @   Xs  ~5 @   <  R�  at_quick_exit �  05 @          �   func ]*�  �  �  1E5 @   �   _onexit ��  5 @          �p  func V%�  �  �  5 @   �  Rs   __wgetmainargs J  �4 @   j       �[  _Argc J"�  �  �  _Argv J5I  �  �  _Env JGI           JQ  9   3   !*  Jl�	  � �4 @   0  �4 @   	  &  R	v  $0.# �4 @   �  �4 @   �  �4 @   �  �4 @   U   __getmainargs >  04 @   j       �E  _Argc >!�  X   R   _Argv >1S  q   k   _Env >@S  �   �      >J  �   �   !*  >e�	  � P4 @   �  `4 @   �    R	v  $0.# e4 @   �  n4 @   �  y4 @   u  �4 @   U   2   6 @   6       �6 @   +  6 @     *6 @                                                                                                                                                                                                                                                                                        
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}  (    I  ( 
  H }  $ >  .?:;9'I<  	4 :!;9I�B  
 !I   :;9I  I �~  
(   4 :!;9I  .?:;9'I<  
 :;9I8  H}  >!I:;9  .?:;9'<  4 :!;9I  & I  I  ! I/   :!;9I�B  4 1�B   :!;!� 9I  4 :!;9I   1�B  . ?<n:!;!   >!!I:;9  >!!I:;9   :;9  !4 :!;9I?<  "7 I  # :;9I  $ <  %.?:;9'<  &   '.?:!;9!'@z  (4 :!;9I�B  ) :!;9I�B  *  +%U  ,   -4 :;9I  .  / :;9I�B  0.?:;9'I@z  14 :;9I�B  21XYW  3 1  4.:;9'   5.:;9'I   6 :;9I  7.1@z  8H}�  9. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                            5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    �   �
        T  m  �  �  �   �   �  �        %  .  7  ?  R  ]  f  n  y  �   
 	P @   � �
2
T@Y  � � � � <Y  t   <X  �gt /	 �e	 KWJ	Y% J" J �$f. *! lX �! � <Y!  t!   <X!  ��YV
=� X	L
�! � � X <iX��q?�� <	 	� �
Z  �  J`�<  	@8 @   � .yt9<Utff�Y�g����
�	x	�
����<	]
�.	\t�P%�P ��<� �  <Y�q�fȐ�} 
�Z�@X-�	���[X	���Y
�/X����
� X�UX	���WX	���Y�;X

�LX	���Y
�X	���HX� ���Y	�8 �8 f Xs�t	�~�
 <
J XW<X� �� <	_	�	�	�	�	#�'� tKXYZ!�� XY	xY���	�}XX�� �t X� <Y� t� <Yw t� <Y t� <Zw t� <Y t� <Y$ �� �<��
� Xt X� <
Y �� <
v���X �  <Y��X �t X� <Y� �� <Yy �� <Y �� <Y �� <] S30+� �� <�� t. <u��qX �� <� �. <u��X ��V� �� <�~� �  <Y�X �  <Y��X� �X < X��� �X <Z$�Jy�  .!K$%� �X <!\; t�*  J �U�~�X����1�����Z���Z���uuu�~�t XX < f � <Y�
v��
 #    K   �
      �  �    0  :  D  L  Y  b  l   	  @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �  �    D  O  W  d  m  x  6     .   �
      �  �      R     .   �
      k  �  �  �   	� @    6     .   �
           G  R      K   �
      �  �  �  �        '  1  9   	  @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  �  �  6     .   �
      4	  L	  s	  ~	  �     <   �
      �	  �	  
  )
  0
  7
  >
   	� @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
  �
  �
  �
   	� @   		3 6     .   �
      4  L  s  �  |    s   �
      �  �     ;  J  Y  b  l  x  �  �  �  �  �  �  �  �  �    	� @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	� @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      /
  G
  n
  �
  �
  �
   	  @   
L�Z
�KTYZ
g=yuX 6     .   �
      �
    5  @  �    U   �
      �  �  �  	�         "  ,  =  J  S   	p @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �  �  �  �  	      )  3  ;  H  S  \  p   	0! @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �  	    6     .   �
      p  �  �  �  G    K   �
        7  ^  y  �  �  �  �  �  �   	�# @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< ]	    n   �
          E  `  i  r  {  �  �  �  �  �  �  �  �  �  �    	�' @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      :  R  z  �  n     A   �
      �    ,  G  W  g  p  z   	p3 @   K
�<.Y �     A   �
      �  �    -  ;  I  R  \   	�3 @   g?	YT�Y	 X� <uX �     A   �
      �  �  �      .  7  A   	�3 @   KU	\fp	\;Y	Y W     O   �
      �  �  �  �    '  /  <  E  P  \  |    h   �
      �  �  �    <  N  `  g  p  z  �  �  �  �  �  �   	 4 @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	P5 @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      L   P  P @   k      B�A�A �A(�A0�DP�
0A�(A� A�A�B�E $   P  � @   U       A�D@NA�D   P    @   c       A�A�A �A(�G�S(A� A�A�A�    l   P  @8 @   �
      B�B�B �B(�A0�A8�A@�AH�	G�,
HA�@A�8A�0A�(B� B�B�B�A       ���� x �         �    @   :       D0u  4   �  ` @   j       A�A�D@@
A�A�H       �  � @             ���� x �            � @             ���� x �      $   P    @   /       D0R
JN    L   P  0 @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       P  � @             ���� x �      <   �  � @   �       A�A�D�P�
���
���A�A�B    ���� x �         P  � @             ���� x �      $   �  � @   i       A�A�DP   <   �  P @   b      A�A�A �Dp�
 A�A�A�D   \   �  � @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         `    @   >       D`y     `  ` @             ���� x �      4   �  p @   �      A�D0}
A�Mf
A�I     ���� x �      L      0! @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <      �! @   o       A�A�A �D@U
 A�A�A�A    D      " @   �       A�A�D@R
A�A�FR
A�A�D      4      �" @   �       A�D0p
A�J�
A�A      ���� x �         (  �# @   ,          (  �# @   P       L   (   $ @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       (  �$ @   �          (  @% @   7          (  �% @   s          (   & @   6          (  @& @   �          (  �& @   �          ���� x �      T   P  �' @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <   P  �( @   �       A�A�A �DPw A�A�A�      l   P  @) @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l   P  �, @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C       P   3 @   "       D@]     P  03 @           D@[     P  P3 @           D@[     ���� x �         @
  p3 @          D@Y     ���� x �      ,   x
  �3 @   H       A�A�D`A�A�   ���� x �         �
  �3 @   2       DPm     ���� x �         �
   4 @          L   �
  04 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �
  �4 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �
  5 @          A�D0WA�    �
  05 @             �
  P5 @   .       A�D0   �
  �5 @   5       DPp     �
  �5 @   6       D0q     �
   6 @   6       D0q                                                                                                                                                                                                                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion lockdownd_client_private domain idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                                                                                                            C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> ideviceinfo.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice C:/msys64/ucrt64/include/libimobiledevice-glue ideviceinfo.c ideviceinfo.c winnt.h combaseapi.h wtypes.h minwindef.h stdio.h getopt.h stdint.h plist.h libimobiledevice.h lockdown.h string.h utils.h synchapi.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                           �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� w                @    RbVbc�R�      B @    
0�
,s � #�3%�,0
s � #�3%#�0=
s � #�3%#�     � @    RU�R�     � @    QU�Q�       � @    XTSTU�X�           @8 @    9R9�]��]��]��]��]           @8 @    9Q9�V��V��V��V��V                                               �8 @    � ���� ���� ����S�� ���
�
P�
�
S�
�
P�
�S�� ���
�
P�
�
S�
�
P�
�
S�
�
P�
�S��P��S��P��S�� ���� ����S�� ��                 �8 @    �	����	����	����P��P�
�	����	����P��P   �8 @    f0���1�   �8 @    f0���1�   �8 @    f0���1�    �8 @    f	����P    �8 @    f	����P    �8 @    f	����P   �8 @    f0���1� 	 �8 @   f1� 
                �8 @    f0�f�^��^��^��P��^��^��^��^          �8 @    f0�f�U��U��P��U  �8 @   f0� 
         �8 @    f0�f�_��_��P��_  �8 @   T6�            �8 @    T0�t�P��P��
��|����|����|     A @    Ls �p @   3%�LPs �p @   3%#�PZs �p @   3%#�       h; @     s �p @   3%� s �p @   3%#�s �p @   3%#�!s �p @   3%�         P @    Q�V���Q���V              Z @    �0���t �p @   3%#���t �p @   3%#���t �p @   3%���t �p @   3%#���t �p @   3%#���0���t �p @   3%#�      Z @    0�/P��P   Z @   ��o#  �   Z @   ��d#  � 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
`r @   ���
@r @   ���
�r @   ���
�r @   ���
�r @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ���Vy     ��U  ��2�  ���Vy     ��U  ��1�  ���Vy     ��U  ��1�  ���Vy     ��U  �	�	4�  �	�	�Vy     �	�	U  �	�	4�  �	�	�Vy     �	�	U  �	�	8�  �	�	�Vy     �	�	U  �	�	8�  �	�	�Vy     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
��|     �
�
T  �
�
4�  �
�
��|     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                           X         Z���� ���� ���� ���� ���� ���� ����          P @   �@8 @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����          6��                                                    .file   a   ��  gcrtexe.c              �                                �              �                            �                           �                            %  �                        @  �                        `             k  �                        �  �                        �  p                        �  �                        �  0          �                       envp           argv            argc    (                          �                        '  �          9  P                        ^  `                        �             �  �                        �  �                        �  @                          �                    mainret            "  �                        8  p                        N  �                        d  �                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )        +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  P     +                 .file   �   ��  gideviceinfo.c         m  P                       domains �          �  �          �         main    @(          �                      .text   P     3               .data          �  ]             .bss    0                        .rdata         �  2             .xdata  t      (                 .pdata  l      $   	                 �  @(     �
  �                 �  �                           �  �                          �  �&  
   �&                  �  �     g                    �  �     {  +                 �  0      @                    �  \      #                      9     �                          0                       �     �                    )  �     +                     4  P     H               .text   �      .idata$7       .idata$5       .idata$4`      .idata$6�      .text   �      .idata$7�      .idata$5      .idata$4X      .idata$6p      .text   �      .idata$7�      .idata$5      .idata$4P      .idata$6X      .text   �      .idata$7�      .idata$5      .idata$4H      .idata$68      .text   �      .idata$7�      .idata$5       .idata$4@      .idata$6      .text   �      .idata$7�      .idata$5�      .idata$48      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$40      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4(      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4       .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   �      .idata$7       .idata$50      .idata$4p      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5x      .idata$4�      .idata$6�      .text          .idata$7|      .idata$5p      .idata$4�      .idata$6�      .text         .idata$7x      .idata$5h      .idata$4�      .idata$6�      .text         .idata$7t      .idata$5`      .idata$4�      .idata$6d      .text         .idata$7p      .idata$5X      .idata$4�      .idata$6L      .file      ��  ggccmain.c             �                          p.0     �          �  `          
  �                    __main  �          '  0       .text         �                .data   �                     .bss    0                       .xdata  �                       .pdata  �      $   	                 �  /M  
   a                   �  �     ?                    �  C     5                     �  p      0                      �     '                     �     �                     )  �     +                     4  �     �                .file   6  ��  gnatstart.c        .text   �                       .data   �                      .bss    @                           �  �S  
     
                 �  (
     �                     �  �                                  V   
                   O                            u                         )  �     +                 .file   J  ��  gwildcard.c        .text   �                       .data                          .bss    P                            �  �Y  
   �                    �  �
     .                     �  �                             e     :                      �     �                     )       +                 .file   f  ��  gdllargv.c         _setargv�                       .text   �                      .data                          .bss    P                        .xdata  �                       .pdata  �                          �  Z  
   �                   �       :                     �  �      0                      �     V                      "     �                     )  @     +                     4        0                .file   z  ��  g_newmode.c        .text                           .data                          .bss    P                           �  �[  
   �                    �  F     .                     �                              �     :                      �     �                     )  p     +                 .file   �  ��  gtlssup.c              3                              B  0          Q  `                    __xd_a  P       __xd_z  X           h  �      .text         �                .data                          .bss    `                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  *\  
   �  6                 �  t     �                    �  x                        �  0     0                      /                          g                            ]     �                     )  �     +                     4  P     �                .file   �  ��  gxncommod.c        .text   �                       .data                          .bss    p                           �  �c  
   �                    �  [
     .                     �  `                            E     :                      K     �                     )  �     +                 .file   �  ��  gcinitexe.c        .text   �                       .data                          .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  {d  
   {                   �  �
     a                     �  �                                 :                      �     �                     )        +                 .file     ��  gmerr.c            _matherr�                       .text   �     �                .data                          .bss    �                        .rdata        @               .xdata  �                       .pdata  �                          �  �e  
   6  
                 �  �
                         �  �	     �                    �  �     0                      �     �                      �	     �                     )  0     +                     4  �     X                .file     ��  gCRT_fp10.c        _fpreset�	                       fpreset �	      .text   �	                      .data                          .bss    �                        .xdata                         .pdata  �                          �  ,i  
   �                    �  �     -                     �  �     0                      w     X                      F
     �                     )  `     +                     4  P     0                .file   3  ��  gmingw_helpers.    .text   �	                       .data                          .bss    �                           �  �i  
   �                    �       .                     �                               �     :                      �
     �                     )  �     +                 .file   `  ��  gpseudo-reloc.c        t  �	                           �  P
          �  �       the_secs�           �  �          �  �           �  �                        �  �                    .text   �	     =  &             .data                          .bss    �                       .rdata  @     [                .xdata       0                 .pdata       $   	                 �  Uj  
   K  �                 �  J     �                    �  
     �  
                 �        0                    �        W                       	     �                     �     	                       �     O                    )  �     +                     4  �     �                .file   �  ��  gusermatherr.c         (                              >  �           L  `      .text         L                .data                          .bss    �                       .xdata  4                      .pdata  ,                         �  ��  
   �                   �  "                         �  �     r                     �  P     0                      �     �                      �     �                     )  �     +                     4  `     P                .file   �  ��  gxtxtmode.c        .text   p                       .data                          .bss    �                           �  ��  
   �                    �  4     .                     �  �                            7     :                      �
     �                     )        +                 .file   �  ��  gcrt_handler.c         c  p                       .text   p     �               .data                          .bss    �                       .xdata  @                      .rdata  �     (   
             .pdata  D                         �  �  
   �                   �  b     ~                    �       _                    �  �     0                      q     �  
                   �                            K                         )  P     +                     4  �     P                .file   �  ��  gtlsthrd.c             z  0                           �             �  �           �  �          �  �           �              �      .text   0     b  "             .data                          .bss    �      H                 .xdata  H     0                 .pdata  P     0                    �  ��  
   �
  A                 �  �     a                    �  m     �                    �  �     0                    �  �                             �     x                     \     %                    )  �     +                     4        (               .file   �  ��  gtlsmcrt.c         .text   �                       .data                         .bss    @                           �  џ  
   �                    �  A     .                     �                               v     :                      �     �                     )  �     +                 .file     ��  g                .text   �                       .data                           .bss    @                          �  Z�  
   �                    �  o     0                     �                               �     :                           �                     )  �     +                 .file   <  ��  gpesect.c              .  �                           A  �          P             e  �          �  @          �  �          �             �  @          �  �      .text   �     �  	             .data                           .bss    P                       .xdata  x     ,                 .pdata  �     l                    �  *�  
   �  �                 �  �     �                    �  8     �                    �  @     0                    �  �      �                       �     K                     �     T                       �     �                     )       +                     4  (     (               .text   �     2                 .data                           .bss    P                       .text   �                       .data                           .bss    P                           )  @     +                 .file   p  ��  ggetopt.c              �  �                       warnx   �          	  @      place   0      ambig   �                  noarg   `          )  �          6  �          F             X  $          c  (          p  0          {         getopt   #          �  0#          �  P#      .text   �     �  t             .data         $                .bss    p                      .xdata  �     d                 .pdata  �     T                .rdata  �     B                    �  ��  
   <  �                 �  )     �                    �  0     ?                    �  p     0                    �  �                            5"     a	                     �     +                       �     .                    )  p     +                     4  P     �               .file   �  ��  gmingw_matherr.    .text   p#                       .data   P                      .bss    �                           �  8�  
   �                    �  �     .                     �  �                            �+     :                      �     �                     )  �     +                 .file   �  ��  gucrt_vfprintf.    vfprintfp#                       .text   p#                     .data   `                     .bss    �                       .xdata                        .pdata  @                         �  ��  
   �                   �  �     8                    �  o     X                     �  �     0                      �+     r   	                   �     �                     )  �     +                     4  @
     8                .file   �  ��  gucrt_printf.c     printf  �#                       .text   �#     H                .data   p                     .bss    �                       .xdata                        .pdata  L                         �  s�  
   �  
                 �  !     l                    �  �     -                     �  �     0                      B,     �   	                   �     �                     )        +                     4  x
     H                .file   �  ��  gucrt_fprintf.c    fprintf �#                       .text   �#     2                .data   �                     .bss    �                       .xdata                        .pdata  X                         �  &�  
   �                   �  }"     b                    �  �     F                     �        0                      �,     �   	                   d     �                     )  0     +                     4  �
     8                .file   �  ��  g__initenv.c           �  �          �  �      .text    $                       .data   �                     .bss    �                          �  ��  
   �                   �  �#     �                     �  P                            a-     [                      I                         )  `     +                 .file   C  ��  gucrtbase_compa        �   $                           �  0$          �  �$      _onexit %          �  0%           	  0                        %	  P%          0	  �%      tzset   �%          >	  �                    _tzset   &          Z	  �          k	  �          |	  �          �	            �	         .text    $       "             .data   �     x   
             .bss    �                       .xdata  $     P                 .pdata  d     l                .rdata  @                          �  j�  
   �  Y                 �  �$                          �  :     |                    �  p     0                      �-     �                                                 f     `                    )  �     +                     4  �
     �               .text   @&      .data          .bss    �      .idata$7@      .idata$50      .idata$4p      .idata$6      .text   H&      .data          .bss    �      .idata$7D      .idata$58      .idata$4x      .idata$6&      .text   P&      .data          .bss    �      .idata$7H      .idata$5@      .idata$4�      .idata$64      .text   X&      .data          .bss    �      .idata$7L      .idata$5H      .idata$4�      .idata$6@      .file   Q  ��  gfake              hname   p      fthunk  0      .text   `&                       .data                           .bss    �                       .idata$2�                      .idata$4p      .idata$50      .file   t  ��  gfake              .text   `&                       .data                           .bss    �                       .idata$4�                      .idata$5P                      .idata$7P                      .text   `&      .data          .bss    �      .idata$7      .idata$5      .idata$4P      .idata$6�
      .text   h&      .data          .bss    �      .idata$7      .idata$5      .idata$4X      .idata$6      .text   p&      .data          .bss    �      .idata$7      .idata$5       .idata$4`      .idata$6      .file   �  ��  gfake              hname   P      fthunk        .text   �&                       .data                           .bss    �                       .idata$2�                      .idata$4P      .idata$5      .file   �  ��  gfake              .text   �&                       .data                           .bss    �                       .idata$4h                      .idata$5(                      .idata$7     !                 .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4       .idata$6p
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4       .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4(      .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$40      .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5�      .idata$48      .idata$6�
      .text   �&      .data          .bss    �      .idata$7�
      .idata$5       .idata$4@      .idata$6�
      .file   �  ��  gfake              hname          fthunk  �      .text   �&                       .data                           .bss    �                       .idata$2�                      .idata$4       .idata$5�      .file   b  ��  gfake              .text   �&                       .data                           .bss    �                       .idata$4H                      .idata$5                      .idata$7�
                       .text   �&      .data          .bss    �      .idata$7d
      .idata$50      .idata$4p      .idata$6D	      .text   �&      .data          .bss    �      .idata$7h
      .idata$58      .idata$4x      .idata$6R	      .text   �&      .data          .bss    �      .idata$7l
      .idata$5@      .idata$4�      .idata$6`	      .text   �&      .data          .bss    �      .idata$7p
      .idata$5H      .idata$4�      .idata$6n	      .text   �&      .data          .bss    �      .idata$7t
      .idata$5P      .idata$4�      .idata$6x	      .text   �&      .data          .bss    �      .idata$7x
      .idata$5X      .idata$4�      .idata$6�	      .text    '      .data          .bss    �      .idata$7|
      .idata$5`      .idata$4�      .idata$6�	      .text   '      .data          .bss    �      .idata$7�
      .idata$5h      .idata$4�      .idata$6�	      .text   '      .data          .bss    �      .idata$7�
      .idata$5p      .idata$4�      .idata$6�	      .text   '      .data          .bss    �      .idata$7�
      .idata$5x      .idata$4�      .idata$6�	      .text    '      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6�	      .text   ('      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6
      .text   0'      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6$
      .text   8'      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$64
      .text   @'      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6V
      .text   H'      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6^
      .text   P'      .data          .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6f
      .file   p  ��  gfake              hname   p      fthunk  0      .text   `'                       .data                           .bss    �                       .idata$2�                      .idata$4p      .idata$50      .file   �  ��  gfake              .text   `'                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7�
     "                 .text   `'      .data          .bss    �      .idata$7,
      .idata$5       .idata$4@      .idata$6	      .text   h'      .data          .bss    �      .idata$70
      .idata$5      .idata$4H      .idata$6	      .text   p'      .data          .bss    �      .idata$74
      .idata$5      .idata$4P      .idata$6&	      .text   x'      .data          .bss    �      .idata$78
      .idata$5      .idata$4X      .idata$60	      .text   �'      .data          .bss    �      .idata$7<
      .idata$5       .idata$4`      .idata$6:	      .file   �  ��  gfake              hname   @      fthunk         .text   �'                       .data                           .bss    �                       .idata$2�                      .idata$4@      .idata$5       .file   �  ��  gfake              .text   �'                       .data                           .bss    �                       .idata$4h                      .idata$5(                      .idata$7@
     "                 .text   �'      .data          .bss    �      .idata$7
      .idata$5�      .idata$40      .idata$6�      .file   �  ��  gfake              hname   0      fthunk  �      .text   �'                       .data                           .bss    �                       .idata$2x                      .idata$40      .idata$5�      .file   �  ��  gfake              .text   �'                       .data                           .bss    �                       .idata$48                      .idata$5�                      .idata$7
                      .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4       .idata$6�      .file   
  ��  gfake              hname         fthunk  �      .text   �'                       .data                           .bss    �                       .idata$2d                      .idata$4      .idata$5�      .file   &  ��  gfake              .text   �'                       .data                           .bss    �                       .idata$4(                      .idata$5�                      .idata$7�                      .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .file   4  ��  gfake              hname   �      fthunk  �      .text   �'                       .data                           .bss    �                       .idata$2P                      .idata$4�      .idata$5�      .file   I  ��  gfake              .text   �'                       .data                           .bss    �                       .idata$4                       .idata$5�                      .idata$7�     &                 .text   �'      .data          .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .file   W  ��  gfake              hname   �      fthunk  �      .text   �'                       .data                           .bss    �                       .idata$2<                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   �'                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     "                 .text   �'      .data          .bss    �      .idata$7l      .idata$5�      .idata$4�      .idata$6�      .text   �'      .data          .bss    �      .idata$7h      .idata$5�      .idata$4�      .idata$6z      .text   �'      .data          .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6l      .text   �'      .data          .bss    �      .idata$7`      .idata$5x      .idata$4�      .idata$6d      .text    (      .data          .bss    �      .idata$7\      .idata$5p      .idata$4�      .idata$6F      .text   (      .data          .bss    �      .idata$7X      .idata$5h      .idata$4�      .idata$6.      .text   (      .data          .bss    �      .idata$7T      .idata$5`      .idata$4�      .idata$6      .text   (      .data          .bss    �      .idata$7P      .idata$5X      .idata$4�      .idata$6      .text    (      .data          .bss    �      .idata$7L      .idata$5P      .idata$4�      .idata$6�      .text   ((      .data          .bss    �      .idata$7H      .idata$5H      .idata$4�      .idata$6�      .text   0(      .data          .bss    �      .idata$7D      .idata$5@      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  @      .text   @(                       .data                           .bss    �                       .idata$2(                      .idata$4�      .idata$5@      .file   �  ��  gfake              .text   @(                       .data                           .bss    �                       .idata$4�                      .idata$5�                      .idata$7p     
                 .file   �  ��  gcygming-crtend        �	  �2                       .text   @(                       .data                           .bss    �                           �  �2                         �  t                          �  �                         �	  3                         )  �     +                 .idata$2        .idata$5�      .idata$4      .idata$2       .idata$50      .idata$4p      .idata$2�       .idata$5X      .idata$4�      .idata$4h      .idata$5(      .idata$7      .idata$4x      .idata$58      .idata$7$      .idata$4�      .idata$5�      .idata$7�      .rsrc       
    __xc_z         putchar �&          �	  �          �	  @&          �	  �&          
  �          
  p          8
  P          V
  �          i
              x
  3          �
  �          �
  �          �
  �          �
  �           �
   (                       9  �          F  �          W  0          h  �          u      	        �  `          �  8'          �  �      __xl_a  0           �  (          �  �          �  �      _cexit  �&            `  ��       *     ��       C  P          q              �  �          �      ��       �     ��       �  0           �  @          �  p      __xl_d  @           
  �      _tls_end   	        8
  �      __tznameP&          N
  �          Z
  �'          g
  8          x
             �
  �          �
  0           �
  0          �
  �          �
      	    memcpy  h'          �
  �            �      puts    �&            �          ?  �          P  �           i        malloc  �'      _CRT_MT       optarg  p          x  �'          �              �             �  �          �  �          �  @          �     ��         �          #            1  X          D  �          X  x          }  h           �  �      opterr  @          �  X          �            �  
          �  �'            �          %  `           >  �          Q  �'          `  P           �  H          �  P           �  h          �  `'          �              �            x          (  @      abort   @'          I  �          s  �          �            �  P       __dll__     ��       �      ��       �  �&          �            �                        (  ((          =  P          Z            g             r  �'          �  �          �  `          �  �          �  x           �  p          �     ��       �  �      strrchr x'            @
      calloc  �'          A             Z  �          d  �            �          �  �           �            �  �          �         Sleep   �'          �         _commodep                          �             3          *  �          >  d           j  �          ~  (       optind  <          �  �      __xi_z  (           �  �          �             �  �          �             �         strstr  �'            �&            P          ;  H          V  l       signal  P'          a  �&          q             �  H           �  �          �              �  �      strncmp p&          �   3          �  �            �          #  �           Q            k  <           �            �  �          �      ��       �  �          �             '  �
          X  �          m  @                      �  $          �  �          �  �          �  �          �  �            0          &     ��       9  `      strdup  `&          Y  �'          g   (            '          �   '          �  (          �  �          �  �                         "  `          1     ��       F              U  �          e  X            �          �  �           �  �          �  H      __xl_z  H       __end__              �  �          �  p          	              �          &  3      __xi_a             4  0'          C  x          O  (      __xc_a              d  �          {     ��       �  P           �     ��   _fmode  �           �  �          �  �          �  �          �  �'            8            �          &  �          4  �&          I             Z  x          o  �          �  h          �  �          �  �&          �  �          �  �      fputc   �&      __xl_c  8           �  `               	    optopt  8             �          Q  �          f  �          y  �          �  d           �             �  �
          �  �          �  H&             �      _newmodeP           .  ('      fwrite  �&          8             F  �          U  �          k      ��       �      ��       �            �            �  �          �  �          �  @           �  �&          �  �      exit    H'               ��       -      ��       E  �          Z  0      atoi    �'          v  `      _exit   '          �  p          �  p          �  �&      strlen  h&          �  �          �  '          �  p      strchr  p'            0(             '          5  �          S  P          p  �           �  �          �  �          �            �  h          �  �             �             P           /   �&          G   �          V   �&          b   �      free    �'          x   �          �   �       �   .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_usage.isra.0 GetValueFromLockDown ClearExtraKeys .rdata$.refptr.optarg .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp_plist_new_bool __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ plist_new_string __mingw_oldexcpt_handler .refptr.optarg TlsGetValue __bss_start__ __imp___C_specific_handler __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force lockdownd_strerror VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler __imp_lockdownd_get_device_value lockdownd_get_value __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf plist_free ___crt_xp_end__ __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname plist_dict_remove_item EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT __imp_atoi _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname __imp_lockdownd_strerror _tls_used __imp_lockdownd_client_new __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ plist_new_bool .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __imp_strstr __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func _head_libimobiledevice_glue_1_0_dll __native_startup_state __imp_plist_to_xml ___crt_xc_start__ lockdownd_client_free ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_lockdownd_get_value _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force lockdownd_get_device_value __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname lockdownd_client_new __imp___p___wargv __imp_strlen libimobiledevice_glue_1_0_dll_iname __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_dict_get_item __imp_plist_new_string _head_libplist_2_0_dll __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit plist_new_real __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp_plist_new_real __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free __imp_plist_dict_remove_item ___tls_end__ __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr __imp_lockdownd_set_value ___chkstk_ms lockdownd_set_value __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp___set_app_type __imp_plist_print_to_stream __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv plist_dict_get_item __imp_plist_free .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv plist_print_to_stream plist_to_xml __mingw_app_type 