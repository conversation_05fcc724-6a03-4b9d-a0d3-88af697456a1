MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� M>f � �  � & ( 6   t     �        @                       �    ��  `                                             �  �   �  �   �  �             �                           �e  (                   ��  �                          .text   (4      6                 `  `.data   0   P      <              @  �.rdata  p   `      >              @  @.pdata  �   �      P              @  @.xdata     �      T              @  @.bss    �   �                      �  �.idata  �   �      X              @  �.CRT    `    �      n              @  �.tls        �      p              @  �.rsrc   �   �      r              @  �.reloc  �          x              @  B/4      `        z              @  B/19     ��      �   �              @  B/31     �!   �  "   6             @  B/45     �         X             @  B/57     �	   0  
   x             @  B/70     �   @     �             @  B/81     �   P     �             @  B/97     B   p     �             @  B/113    �   �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�uY  1��    H�vY  �    H�yY  �    H��X  f�8MZuHcP<HЁ8PE  tfH�Y  �
��  � ��tC�   �2  �2  H��Y  ����1  H��Y  ���  H�]X  �8tP1�H��(Ð�   �f2  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�Y  �%  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�UY  L�֎  H�׎  H�
؎  � ���  H��X  D�H���  H�D$ �.  �H��8��    ATUWVSH�� H�OX  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5X  1�����V  �����  ��     ����L  ���e  H�AW  H� H��tE1��   1�����   H�
AX  ��  H��W  H�
����H���0  ��  �ҍ  �{Hc�H��H��� 1  L�%��  H�Ņ��F  H��1�I���/  H�pH����0  I��H�D I�H��H���0  H9�u�H�H�    H�-]�  �H  H��V  L�B�  �
L�  H� L� H�7�  ��  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5�V  �   ���������   ��-  ��������H��V  H�
�V  �/  �   �������1�H�����f�     �S/  ���  H�� [^_]A\�f.�     H��V  H�
rV  �   �_/  �7���f�H����������i/  �H��(H��U  �    ������H��(� H��(H��U  �     �z�����H��(� H��(��,  H���H��(Ð�����������H�
	   �����@ Ð��������������UH��H�� �MH�UH��;  H��H��K  H���+  �H�� ]�UH��H��0H�MH�E�    �E�    H�M�H�U�H�EI��H���W  H�E�H��tH�E�H����-  �H��0]�USH��h  H��$�   ��   H��  �   Hǅ�       ǅ�   ����H�Ex    H�Ep    H�Eh    H�E`    H�EX    H�EP    H�EH    H�E@    H�E8    H�E0    H�E(    Hǅ�       Hǅ�       ǅ�       ǅ�       ǅ�      ǅ�       ǅ�       ǅ�      ǅ�       ǅ�      ��  ���   H�H��    H��  H�H� H�;J  H���\,  ��t0���   H�H��    H��  H�H� H�J  H���,,  ��u�   �  �   �  �h  ���   H�H��    H��  H�H� H��I  H����+  ��t4���   H�H��    H��  H�H� H��I  H���+  ����   ���   ���   H�H��    H��  H�H� H��t$���   H�H��    H��  H�H� � ��uH��  ��   ���J����   �  ���   H�H��    H��  H�H� H���   �p  ���   H�H��    H��  H�H� H��H  H����*  ��t0���   H�H��    H��  H�H� H��H  H���*  ��uǅ�      �  ���   H�H��    H��  H�H� H�}H  H���|*  ��t0���   H�H��    H��  H�H� H�PH  H���L*  ��up���   ���   H�H��    H��  H�H� H��uH��  ��   �������   �Y  ���   H�H��    H��  H�H� H���   �1  ���   H�H��    H��  H�H� H��G  H���)  ��t0���   H�H��    H��  H�H� H��G  H���|)  ��uǅ�       ��  ���   H�H��    H��  H�H� H�VG  H���=)  ��t0���   H�H��    H��  H�H� H�)G  H���
)  ��uǅ�      �S  ���   H�H��    H��  H�H� H��F  H����(  ��t0���   H�H��    H��  H�H� H��F  H���(  ��uH��  ��   �������    ��  ���   H�H��    H��  H�H� H�}F  H���P(  ��uǅ�      �   ���   H�H��    H��  H�H� H�GF  H���(  ��uǅ�      �Z���   H�H��    H��  H�H� H�F  H����'  ��uǅ�      �H��  ��   �������    ��  ���   ���   ;�   �������    tL�    H�y�  ��H���'  ����'  ��t!�   H�X�  ��H���'  ���'  ��u
ǅ�       ���    uH��  ��   �������   �j  H���    t`���    t�   ��   H���   H���   A��H���z  ���   ���    t_H���   H��H��D  H����#  �   �   H���   �    H���<  ���   ���    tH��D  H����&  �   ��  H���   H�UxL��D  H����  ��t:�   H�B�  ��I��A�   �   H��D  H���&  ǅ�      �  H�E     ǅ�       H�ExH�U I��L��D  �    H���S  ����   �E    �E    �E    H�E     H�E H��H���  H�E H��t]H�E H�UL�BH�MH�UH��H�T$ M��I��H�[D  H���d"  ��~&�E��%  � �E����	E��	Љ��   H�E H���m&  H�E H���  ���   ��   ���   �
 v|H�E�    H�ExH�U�I��L��C  �    H���a  H�E�H��tLH�E�    H�E�H�U�H���  H�E�H��tH�E�H��C  H���$  H�E�H����%  H�E�H���|  H�E    H�ExH�UI��H��C  H����  ��u}H�UH���   H�MpI��H���  ���   H�EH���  H�E    ���    t6�   H�-�  ��H��L�%C  H�:C  H���!  ǅ�      �
  ǅ�      ���   t���   ��  ��   ���    t`H�EpH����  ��t:�   H���  ��I��A�   �   H��B  H����#  ǅ�      �
  H�EpH���  H�Ep    �JH�ExH����  ��t:�   H�Y�  ��I��A�   �   H�{B  H���#  ǅ�      �  ǅ�       H�rB  H���z#  �  ���    �!  H�yB  H���Y#  H�E�    ���   ��	 wH�EpH�U�H����
  ��t
ǅ�      H�EpH����
  H�Ep    ���    ��  H�E�    H���   H�UpL�A  H���
  ��t6�   H�l�  ��H��L�dA  H�yA  H����  ǅ�      ��  H��A  H���"  H�EpH�U�H���;
  ��t:�   H��  ��I��A�:   �   H��A  H���T"  ǅ�      �t  H��A  H���>"  H�EpH���

  H�Ep    H�EhH�¹    �  ��t:�   H���  ��I��A�'   �   H��A  H����!  ǅ�      �  H��A  H����!  H�U�H�EhH���4  H�E�H���(
  H�EhH�U`H���  H�E�    H�E`H�U�H����  H�E`H����  H�E`    H���   H�UpL��?  H���"  ��t6�   H��  ��H��L��?  H��?  H���G  ǅ�      �G
  H�IA  H���!  ǅ�       ǅ�       �HH�U�H�EpH�M�I��H���  ��u&H�E�H��tH�E�H���;  ��uǅ�      ����   ���   ~����   t:�   H�-�  ��I��A�3   �   H��@  H���n   ǅ�      �	  H� A  H���X   H�EpH���$  H�Ep    �TH�E�H��tH�E�H���  ��t:�   H���  ��I��A�3   �   H�m@  H����  ǅ�      �	  ���    tH�E�H��������  H�EhH�¹    �K
  ��t:�   H�K�  ��I��A�%   �   H�l@  H���  ǅ�      �  ��
  H���   H�U�H���   I��H�Z@  H����
  H�EhH���   H����	  �VH�ExH�UhI��H�¹    �	  ��t:�   H���  ��I��A�%   �   H��?  H����  ǅ�      �  H�ExH���
  H�Ex    H�EhH��tH���    tH�EhH���   H���	  H�EhH�U`H���  ��t:�   H�/�  ��I��A�-   �   H��?  H���p  ǅ�      �  H�E`H���}  ����   �   H��  ��I��A�"   �   H�j?  H���"  H�E`H�UXH���B  H�EXH��t'H�]X�   H���  ��H��I��H�M?  H���  H�E`H�UPH���"  H�EPH��t'H�]P�   H�Y�  ��H��I��H�
?  H����  ǅ�      ��  H�E`H�U(H����  H�E(H���{  H���   H�UxL��;  H���|  ��t:�   H��  ��I��A�   �   H��;  H���-  ǅ�      �M  ���    ��  H�E    H�ExH�UI��H��;  H����  ��t6�   H��  ��H��L�w;  H�4>  H����  ǅ�      ��  H�UH���   H�MpI��H���s  ���   H�EH���  H�E    ���    t6�   H��  ��H��L�;  H�;  H���q  ǅ�      �q  ���    ��   H�E�    H�E`H�U�H���]  H�M�H�U(H�EpI��H����  ��tFH�E�H���v  �   H���  ��I��A�'   �   H�[=  H����  ǅ�      ��  H�E�H���0  �F  H�U(H�EpH���  ���.  �   H�'�  ��I��A�'   �   H��<  H���h  ǅ�      �  H�U(H�ExH���y  ����   H�E�    H�ExH�U�I��L��9  �    H���*  ǅ�       H�E�H��t_H�E�H���s  ��uNH�E�    H�E�H�U�H���N  H�E�H��t!H�E�H�b9  H���b  ��t
ǅ�      H�E�H���x  ���    u:�   H�3�  ��I��A�'   �   H�<  H���t  ǅ�      �  �   ��  H��H�ExI��L��;  �    H���=  ����  �   H�Ɏ  ��I��A�5   �   H��;  H���
  ǅ�      �*  H�E`H���  ��tH��;  H����  ǅ�       ��  H�E`H�UXH����  H�EXH��t'H�]X�   H�D�  ��H��I��H��;  H���  H�E`H�UPH����  H�EPH��t'H�]P�   H��  ��H��I��H��;  H���m  H�E`H�U8H���  H�E8H��tH�E8H����  ��u:�   H���  ��I��A�   �   H�Y;  H����  ǅ�      �  H�E8H�U0H���r  H�E0H��u:�   H�e�  ��I��A�   �   H�;  H���  ǅ�      ��  H�EhH���#  H�Eh    H�EhH�¹    �  ��t:�   H��  ��I��A�   �   H��:  H���C  ǅ�      �c  H�U`H�EhH���  H�EH    H�U0H�E8H�MHA�    I��H���  H�EHH��tIH�UHH�E`H���N  ��t5H�UHH�E`H�M@I��H���  H�L:  H���  ǅ�      ��   H�E0H���q  H�E0    H�E`H����  H�E`    ������ǅ�       �   H�E�    ���    tH�EpH�U�H����  �H�ExH�U�I��L��5  �    H���>  H�E�H���  ��u<H�E�    H�E�H�U�H���u  H�E�H��H��9  H���  H�E�H���  �H��9  H����  �H�EhH��tH�EhH���T  H�E`H��tH�E`H���  H�E8H��tH�E8H���  H�E@H��tH�E@H���M  H�E0H��tH�E0H���8  H�E(H��tH�E(H����  H�EpH��tH�EpH���  H�ExH��tH�ExH���Q  H���   H��tH���   H���^  ���   H��h  []Ð��������������%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%҇  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%҇  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ډ  ���%ʉ  ���%��  ���%��  ���%��  ���     H��(H��!  H� H��t"D  ��H��!  H�PH�@H��!  H��u�H��(�fD  VSH��(H�c:  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�#��� 1�fD  D�@��J�<� L��u��fD  �q  ��t�D  �q     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�s9  �8t�    ��t��tN�   H��([^�f�H���  H�5��  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H��6  Hc�H����    H��5  �DA �y�qH�q�   �  �DD$0I��H�z6  �|$(H��I���t$ �;  �t$@|$P1�DD$`H��x[^ÐH�I5  ��    H��5  ��    H�i5  �s���@ H��5  �c���@ H��5  �S���H��5  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�$  A�   �   H�
�5  I���J  H�t$(�   ��  H��H��I���
  �  ��    WVSH��PHc5�n  H�˅��  H��n  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��n  H��H��H�H�x �     �#  �WA�0   H�H�gn  H�T$ H�L��  H���}   �D$D�P����t�P���u�/n  H��P[^_� ��H�L$ H�T$8A�@   �   DD�Hn  H�KI��H�S���  ��u��b�  H�
5  ���d���@ 1��!���H��m  �WH�
�4  L�D�>���H��H�
t4  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%tm  E��tH�e[^_A\A]A^A_]�fD  �Nm     �9	  H�H��H��   H����  L�-�5  H��5  �m      H)�H�D$0H�m  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5N5  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�3  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �~k  ������H�5�  1�H�}�D  H�ak  H�D� E��t
H�PH�HI����A��H��(D;%6k  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5 3  �s�;H��L�>H���Z����>L9�r��������H�
�1  �����H�
�1  ���������H��XH�5j  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
�i  �  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��1  Hc�H��� 1ҹ   �4  H���>  H���  H��i  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   ��  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �t  H���@����   �   �[  �f�     1ҹ   �D  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   ��
  �,����   �   ��
  �����������ATUWVSH�� L�%h  L���~}  H�Oh  H��t6H�-�}  H�=l}  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%@}  WVSH�� ��g  ��H�օ�u
1�H�� [^_ú   �   �i
  H��H��t3H�pH�5�g  �8H����|  H��g  H��H��g  H�C��|  묃��멐VSH��(��g  �˅�u1�H��([^�D  H�5�g  H����|  H�
Yg  H��t'1��H��H��tH���9�H�Au�H��tH�B��	  H���d|  1�H��([^� H�g  ��ff.�     @ SH�� ����   w0��tL��f  ����   ��f     �   H�� [�f�     ��u��f  ��t��<�����f.�     ��f  ��uf��f  ��u�H��f  H��t�    H��H�[�	  H��u�H�
�f  H�Uf      �Sf      �e{  �l����k����   H�� [������f�     H�
9f  �K{  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H����  H��w{H��-  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���  ��u�H��H�� [^_��    1�H��H�� [^_� H�-  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��,  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�Y,  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��+  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��+  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�	+  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��8E1�L�D$ I��H��1��O  H��8Ð�H��HH�D$`L�D$`I������H�D$(H�D$     L�L$hI��H��1�H�D$8�  H��HÐVSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �k  � ��j  H� H��'  H� H�M��t	A�$��  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���`  ���   ����(  ��  � ��  H� H��  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H��(  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H�u'  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H��'  ��~   H�'  �j   H�  �V   H��  H��(�f.�     H��(H�u'  ��>   H��  �*   H��  �   H��  H��(Ð����������%�u  ���%�u  ���%�u  ���     �%Ru  ���%Ru  ���%Ru  ���     �%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���     �%Rs  ���%Rs  ���%2s  ���     �%�r  ���%�r  ���%�r  ���%�r  ���%�r  ���%�r  ���%�r  ���%�r  ���%�r  ���%rr  ���%br  ���%Rr  ���%Br  ���%2r  ���%"r  ���%r  ���;�����������������������C @           ��������                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 ` @            D @           ��������        ����                           ����             ? @           @? @           �? @           �? @           p� @   x� @   pA @   �A @   @ @   @A @   �@ @    @ @   �P @   �P @    Q @      �p  Q @   Q @   PDT PST  A @    A @                                                                                                                                                                                                                           COPYIGHT 2024 Circtek.IO %s Ver 1.0.0
 -d --debug -u --udid -n --network -s --service -b --batch -i --info -h --help activate deactivate state  No device found with UDID %s, is it plugged in?
        No device found, is it plugged in? ideviceactivation    Failed to connect to lockdownd
 ProductVersion %d.%d.%d ActivationState Unactivated com.apple.mobileactivationd Failed to connect to %s
 Failed to deactivate device.
  Successfully deactivated device.        using mobileactivation with session_mode        session_mode start service sucess       Failed to get ActivationSessionInfo from mobileactivation
      session_mode create sessioninfo success Failed to create drmHandshake request.
 session_mode create DRMhandshakre success       use handshake response for session_mode started service Failed to get ActivationInfo from mobileactivation
     session_mode create activation info success     Failed to create activation request.
 activation-info   Failed to send request or retrieve response.
   Activation server reports errors.
 	%s
 Failed to start service %s
     Failed to activate device with record.
 ActivationStateAcknowledged     Failed to set ActivationStateAcknowledged on device.
   Activation server reports that device is already activated. Server reports:
%s
 Unknown error.
 Could not create new request.
  This iPhone is linked to an Apple ID ActivationState: %s
       Error getting activation state.         p/ @                            � @   � @   l� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ��������$�������������������Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      `���`���`���`���`�������`��� �����������        runtime error %d
               @P @           PP @            D @              @           pp @           pp @           �e @           �P @           � @           �� @           h� @           d� @           `� @            � @           �� @           @� @           H� @            � @           � @           � @           (� @           p� @           0P @           �� @           �6 @           0 @           P� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P    t�    �  ��  �  �,  ��  `.  �.  ��  �.  
/  ��  /  //  ��  0/  3/  ��  @/  o/  ��  p/  �/  ��   0  0  ̐  0  1  А  1  1  �   1  �1  �  �1  �2  ��   3  ]6  �  `6  �6  �  �6  �6  $�  �6  m8  (�  p8  �8  0�  �8  O9  @�  P9  �9  L�  �9  �:  X�  �:  ;  `�  ;  `;  d�  `;  �;  h�   <  �<  t�  �<  �<  x�  �<  3=  |�  @=  v=  ��  �=  	>  ��  >  �>  ��   ?  >?  ��  @?  ?  ��  �?  �?  ��  �?  @  ��  @  @  ��   @  �@  ��  �@  �@  đ   A  A  ԑ   A  5A  ܑ  @A  nA  ��  pA  �A  �  �A  �A  �  �A  &B  ��  �C  �C   �                                                                                                                                                                                                                                                                                                                                                                                              B   b  
 
20`pP�	 B  PC     �  �  �6  �  	 B  PC     �    �6     B        2P  RP  �	- 0P   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   b   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 �          ��  ��  ��          d�  H�  `�          ��   �  ��          ��  X�  в          ��  p�  ��          �  ��  �          D�  ��   �          ��  ��  ��          ��  P�  �          $�  ��  (�          X�  ȷ  P�          ��  �                          H�      x�      ��      ��      �      �      P�      x�      ��      й      �      4�      `�      ��      ��      ܺ      �      D�      h�              ��      ��      ��      ̻      �       �      �      @�      X�      p�      ��      ��      ȼ      �      �      0�      P�      x�      ��      ܽ      �      0�              X�      p�      ��      ��      ��      ̾      �      �       �      �              "�      2�              B�      R�      \�      d�              n�              ��      ��              ��      ��      ��      ο      ؿ      �      
�       �      .�      6�      X�      x�      ��      ��      ��      ��      ��              ��      ��      ��       �      �      6�      P�      Z�      d�      n�              v�      ��      ��              ��      ��      ��      ��              ��      ��      ��      �      (�      8�      P�      h�      |�      ��              H�      x�      ��      ��      �      �      P�      x�      ��      й      �      4�      `�      ��      ��      ܺ      �      D�      h�              ��      ��      ��      ̻      �       �      �      @�      X�      p�      ��      ��      ȼ      �      �      0�      P�      x�      ��      ܽ      �      0�              X�      p�      ��      ��      ��      ̾      �      �       �      �              "�      2�              B�      R�      \�      d�              n�              ��      ��              ��      ��      ��      ο      ؿ      �      
�       �      .�      6�      X�      x�      ��      ��      ��      ��      ��              ��      ��      ��       �      �      6�      P�      Z�      d�      n�              v�      ��      ��              ��      ��      ��      ��              ��      ��      ��      �      (�      8�      P�      h�      |�      ��               idevice_activation_drm_handshake_request_new   idevice_activation_request_free    idevice_activation_request_new     idevice_activation_request_new_from_lockdownd 	 idevice_activation_request_set_fields 
 idevice_activation_request_set_fields_from_response    idevice_activation_request_set_url     idevice_activation_response_field_requires_input   idevice_activation_response_free   idevice_activation_response_get_activation_record  idevice_activation_response_get_description    idevice_activation_response_get_fields     idevice_activation_response_get_headers    idevice_activation_response_get_label  idevice_activation_response_get_title  idevice_activation_response_has_errors     idevice_activation_response_is_activation_acknowledged     idevice_activation_send_request    idevice_activation_set_debug_level    f idevice_free  k idevice_new   l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_activate    � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_deactivate  � lockdownd_get_value   � lockdownd_service_descriptor_free � lockdownd_set_value   � lockdownd_start_service   � mobileactivation_activate � mobileactivation_activate_with_session    � mobileactivation_client_free  � mobileactivation_client_new   � mobileactivation_client_start_service � mobileactivation_create_activation_info   � mobileactivation_create_activation_info_with_session  � mobileactivation_create_activation_session_info   � mobileactivation_deactivate   � mobileactivation_get_activation_state DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsscanf  1 _fileno Q _isatty � fwrite  � puts  � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     plist_dict_get_size    plist_dict_new_iter    plist_dict_next_item   plist_dict_set_item    plist_free    ( plist_get_node_type   , plist_get_string_val  5 plist_new_bool    8 plist_new_dict    L plist_to_xml   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libideviceactivation-1.0.dll    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  libimobiledevice-1.0.dll    (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  P�  P�  P�  api-ms-win-crt-heap-l1-1-0.dll  d�  api-ms-win-crt-math-l1-1-0.dll  x�  x�  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    Ȱ  Ȱ  Ȱ  Ȱ  api-ms-win-crt-time-l1-1-0.dll  ܰ  ܰ  ܰ  ܰ  ܰ  ܰ  ܰ  ܰ  ܰ  ܰ  libplist-2.0.dll                                                                                        0 @                    @                   p/ @   @/ @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     �   P  4    ��`�p���������������ȠРؠ��� ��� �   `  H   ����ȥХإ� �� �0�@�P�`�p�����������Щ�� �� �0�@�P�`�p����� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                        ,               @   $                      ,    �&       `. @   �                           �,                           �2                       ,    �3       0/ @                              5                       ,    �5       @/ @   �                           Y=                           �=                       ,    ^?       0 @   �                       ,    �B       1 @                              +C                       ,    �C        1 @   =                      ,    [       `6 @   L                           �]                       ,    �^       �6 @   �                      ,    fn       p8 @   b                          9y                           �y                       ,    �z       �: @   �                          d�                       ,    �        ? @                          ,    ��       @? @   ?                       ,    �       �? @   H                       ,    Ϛ       �? @   2                           q�                       ,    �       @ @                                                                                                                                                                                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   ]   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  `. @   �       9  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	0� @   atexit ��   �  Y   __main 5/ @          ��  // @   �   	__do_global_ctors  �. @   j       �  
nptrs "�   �  �  
i #�   �  �  �. @   j  R	`. @     	__do_global_dtors `. @   :       �[  p [  	P @    	   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  `  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
  �   �,  __uninitialized  __initializing __initialized    ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	$P @   �  	 P @   =  
"	H� @   [  	@� @    �    w  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _dowildcard  �   	0P @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 s  [  0/ @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   0/ @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  F  _newmode �   	P� @   int  �   
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @/ @   �       �  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	l� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�e @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�e @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	h� @   __mingw_initltsdyn_force ��   	d� @   __mingw_initltssuo_force ��   	`� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  @/ @   /       �}  
7  �  
  	  
M  *M      
B  ;d  1  -  e/ @   �   __tlregdtor m�    0 @          ��  func m  R __dyn_tls_init L@  	  7  �  M  *M  B  ;d  pfunc N
$  ps O
�    �  p/ @   �       ��  G  ?  �  o  g  �  �  �  �  �  �  �/ @    �/ @   +       L�  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �/ @   �    �    �	  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _commode �   	p� @   int  w   "
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 #  U  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  0 @   �       
  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   0 @   �       �0  pexcept 0  &     type 
�  H  <  m0 @   b  �  R2 �0 @   7  Q	�f @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �    1 @          �  _fpreset 	1 @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 6	  	   	  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  
   1 @   =      Z	  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /V  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0V  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator � 3 @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  �3 @   h   �4  �  �  �  6h   
�  �  �  
�  .  �  
�  E  -  
�  �  �  

  �  �  
  '    "E  x   �  
F  t  l  
[  �  �  �4 @   `  R	Hh @   Xu w t     '4 @   '4 @          �;  �  �  �  �  �  �  �  �  �    '4 @   '4 @          �  �  �  �      �      /4 @   �  Ru    !  �4 @   �   ��  �      �  )  '  �  8  6  7  �4 @   �   �  B  @  �  M  K  �  \  Z  �4 @   �  Ru      �5 @   �5 @   
       �w  �  f  d  �  q  o  �  �  ~    �5 @   �5 @   
       �  �  �  �  �  �  �  �  �  �5 @   �  Ru      �5 @   �5 @          �   �  �  �  �  �  �  �  �  �    �5 @   �5 @          �  �  �  �  �  �  �  �  �  �5 @   �  Ru    "$  �   �  
)  �  �  83  �   
4        .6 @   .6 @   
       s�      �  )  '  �  8  6    .6 @   .6 @   
       �  B  @  �  M  K  �  \  Z  66 @   �  Rt      
P6 @   `    R	h @    ]6 @   `  R	�g @      9�  5 @   X       �|  
�  h  d  :�  ��O5 @   
  Yu   G3 @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable ��1 @   b      �`  &addr ��  �  x  b �:  ��h �g  �  �  i �%  �  �  >p2 @   P       �  new_protect �
u  
	  	  
�2 @   
  �  Ys  �2 @    
  �2 @   `  R	�g @     
�1 @   �
  �  Rs  2 @   n
  
A2 @   E
    Q��X0 
�2 @   `  >  R	�g @    �2 @   `  R	`g @   Qs   ?__report_error T 1 @   i       �/  &msg T  	  	  @argp ��   �X
L1 @     �  R2 
f1 @   /  �  R	@g @   Q1XK 
u1 @       R2 
�1 @   �
  !  Qs Xt  �1 @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 3    `6 @   L       �  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr ��6 @          �P  f ,�  1	  -	  �6 @   �  R�R  __mingw_raise_matherr �`6 @   >       �typ !�   E	  ?	  name 2�  ]	  Y	  a1 ?w   o	  k	  a2 Jw   �	  	  rslt 
w   � ex 0  �@�6 @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _fmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �6 @   �      �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  _  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  _  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	�� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   �6 @   �      ��  'exception_data �-�  �	  �	  old_handler �
	  �	  �	  action ��   C
  )
  reset_fpu ��   �
  �
  
7 @   �
  �  R8Q0 (67 @   �  R�R 
g7 @   �
  �  R4Q0 }7 @   �  R4 
�7 @   �
    R8Q0 
�7 @   �
  7  R8Q1 
�7 @   �
  S  R;Q0 8 @   f  R; '8 @   y  R8 
;8 @   �
  �  R;Q1 
O8 @   �
  �  R4Q1 
c8 @   �
  �  R8Q1 )h8 @   �
   �	   �
   y  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  p8 @   b      O  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �9 @   �       �n  	hDllHandle z�    �
  	reason {<  �  }  	reserved |S    �   U: @   K       �  
keyp �&�  �  {  
t �-�  �  �  t: @   �  
�: @   C  R	 � @     !n  %: @   %: @          �  �  4: @   )
   "n  @: @   �   �E  #�   �  �: @   )
    �: @   6  
�: @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   P9 @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�  �  �  �9 @   �  B	  Rt  �9 @   �  
�9 @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �8 @   o       �$
  	key *%<  
  
  	dtor *1.  I
  ;
  
new_key ,$
  �
  �
  9 @   �  �	  R1QH -9 @   �  
  Rt  
H9 @   �  Rt   �  &n  p8 @   p       ��  �
  �
  '�  �8 @          �
  �  �
  �
  �8 @     �8 @     (�8 @   Rt   �8 @   �  �
  R|  )�8 @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _CRT_MT �   	@P @   int  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 n  V    __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	A� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	@� @    �   8  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (    �: @   �      ;  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  x  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  �  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  �  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   x  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  > @   �       �7
  i �(�   �
  �
  �  �	`  �  �	  �
  �
  importDesc �@  �
  �
  o  �^
  importsStartRVA �	I      �  > @   	z  ��  �  z  �  �  �  	�  "> @    �  �  �  �  �  M  I  �  ^  \      M  T> @   T> @   J       �q  j  h  f  }  v  r  �  �  �  �  �  �    
_IsNonwritableInCurrentImage �  �= @   �       ��  pTarget �%`  �  �  �  �	`  rvaTarget �
�  �  �  o  �^
  �  �  �  �= @   _  �/  �  _  �  �  �  	�  �= @    o  �  o  �  �  �  �  �    
      M  �= @   �= @   I       �q      f  }  %  #  �  /  -  �  9  7    
_GetPEImageBase �`  @= @   6       �0  �  �	`  	�  @= @   D  �	�  D  �  �  �  	�  P= @    T  �  T  �  �  F  B  �  W  U       
_FindPESectionExec y^
  �< @   s       �%  eNo y�   e  a  �  {	`  �  |	  v  t  o  }^
  �  ~  �  ~�   �  �  	�  �< @   )  �	�  )  �  �  �  	�  �< @    9  �  9  �  �  �  �  �  �  �       
__mingw_GetSectionCount g�   �< @   7       ��  �  i	`  �  j	  �  �  	�  �< @     m	�    �  �  �  	�  �< @      �    �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
   < @   �       �  p Y&s  �  �  �  [	`  rva \
�      �   < @   �   _�  �  �   �  �  �  	�  < @    �   �  �   �  �      �  %  #      	M  9< @     c
q  1  /  f    }  =  9  �  [  Y  �  e  c     
_FindPESectionByName :^
  `; @   �       �M  pName :#�  x  n  �  <	`  �  =	  �  �  o  >^
  �  �  �  ?�   �  �  �  u; @   �   F  �  �   �  �  �  �  �; @    �; @          �  �  �  �  �  �  �  �     &o; @   �  -  Rt  '�; @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  o  '^
  �  (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �: @   ,       �~  �  �  �  �  �  �  �  �  	�  �: @    �   �    �  �   �  �      �  (  &     *M  ; @   P       �f  4  0  +q  Q}  G  C  �  f  d  �  p  l    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 
  �  �  _MINGW_INSTALL_DEBUG_MATHERR �   	PP @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   ? @          �  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	`P @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	   ? @          �_File *�  �  �  _Format J�  �  �  _ArgList Z�   �  �  9? @   �  R0Q�RX�QY0w �X   y   (  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @? @   ?       2  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   size_t #,�   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(B  G  	threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   e  j  j  
 �   e  __imp_sscanf �  	pP @   P  __stdio_common_vsscanf �  �  �   e  �   e  $  �    sscanf   @? @   ?       �_Src .j      _Format Mj  8  2  
__ap �   �h__ret 
  M  K  z? @   �  R0Q�RX	�Y�Qw 0w(�   �   a  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  o  �? @   H       �  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�P @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �? @   H       �_Format .�  e  _  
ap 
�   �Xret 	  z  x  �? @   �  �  R1 �? @   �  R0Xs Y0w t    �   �  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 i  Q  �? @   2       R  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�P @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  �? @   2       �_File )�  �  �  _Format I�  �  �  
ap 
�   �hret 	  �  �  �? @   �  R0Q�RX�QY0w �   �   /  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 N  6  �  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	x� @   local__winitenv 
`  	p� @   '  
	�P @     
	�P @    �   �  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 k  S  @ @         7  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	 Q @   �      �   __imp_at_quick_exit g)  	Q @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	Q @   
initial_tzname1 }
V  	Q @   
initial_tznames ~�  	 Q @   
initial_timezone 
%  	�P @   
initial_daylight �  	�P @   __imp_tzname ��  	�P @   __imp_timezone ��  	�P @   __imp_daylight ��  	�P @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�P @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�P @   �	  __imp__amsg_exit �V  	�P @   F  __imp__get_output_format �\
  	�P @   -
  __imp_tzset ��  	�P @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�P @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   pA @   5       ��
  file �!�
  �  �  fmt �6�  �  �  
ap ��   �h+ret �      �A @   �
  R4Q�RX�QY0w �  ,tzset "�A @   6       �  -  �A @   �A @   -       ��A @   +  �A @     �A @       ._tzset �/_get_output_format nF  @ @          �0_amsg_exit i@A @   .       ��  ret i      QA @   z  �  R2 cA @   Q  �  Q	�h @   Xs  nA @   <  R�  at_quick_exit �   A @          �   func ]*�  1  -  15A @   �   _onexit ��   A @          �p  func V%�  I  C  
A @   �  Rs   __wgetmainargs J  �@ @   j       �[  _Argc J"�  h  b  _Argv J5I  �  �  _Env JGI  �  �   �  JQ  �  �  !�  Jl�	  � �@ @   0  �@ @   	  &  R	v  $0.# �@ @   �  �@ @   �  �@ @   �  �@ @   U   __getmainargs >   @ @   j       �E  _Argc >!�  �  �  _Argv >1S  �  �  _Env >@S       �  >J  /  )  !�  >e�	  � @@ @   �  P@ @   �    R	v  $0.# U@ @   �  ^@ @   �  i@ @   u  }@ @   U   2  �A @   6       �B @   +  B @     B @                                                                                                               
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I  & I  	!:!;9!  
    :!;!9I�B  %  
 I   <  'I  7 I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|            5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg #    K   �
        '  N  i  s  }  �  �  �  �   	`. @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �    7  R  }  �  �  �  �  �  6     .   �
          E  P  R     .   �
      �  �  �  �   	0/ @    6     .   �
      A  Y  �  �      K   �
      �  �    8  A  J  T  `  j  r   	@/ @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  
    6     .   �
      m  �  �  �  �     <   �
           G  b  i  p  w   	0 @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �  �  	  	   	1 @   		3 6     .   �
      m	  �	  �	  �	  |    s   �
      
  2
  Y
  t
  �
  �
  �
  �
  �
  �
  �
  �
  �
  �
  �
  �
        	 1 @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	 3 @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      h  �  �  �  �  �   	`6 @   
L�Z
�KTYZ
g=yuX 6     .   �
      /  G  n  y  �    U   �
      �  �  
  	+
  9
  G
  O
  [
  e
  v
  �
  �
   	�6 @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �
  �
    8  B  L  V  b  l  t  �  �  �  �   	p8 @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
          B  L  6     .   �
      �  �  �  �  G    K   �
      X  p  �  �  �  �  �  �  �  �   	�: @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      E  ]  �  �  n     A   �
      �    7  R  b  r  {  �   	 ? @   K
�<.Y �     A   �
      �  �    8  F  T  ]  g  Z 	@? @   
KZU\ZF\f-YY �     A   �
      �  �  �    (  6  ?  I   	�? @   g?	YT�Y	 X� <uX �     A   �
      �  �  �  �      $  .   	�? @   KU	\fp	\;Y	Y W     O   �
      �  �  �  �        )  2  =  I  |    h   �
      �  �  �  �  )  ;  M  T  ]  g  p  x  �  �  �  �   	@ @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	@A @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                                             ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  `. @   :       D0u  4   P  �. @   j       A�A�D@@
A�A�H       P  / @             ���� x �         �  0/ @             ���� x �      $     @/ @   /       D0R
JN    L     p/ @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�          0 @             ���� x �      <   �  0 @   �       A�A�D�P�
���
���A�A�B    ���� x �           1 @             ���� x �      $   8   1 @   i       A�A�DP   <   8  �1 @   b      A�A�A �Dp�
 A�A�A�D   \   8   3 @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �           `6 @   >       D`y       �6 @             ���� x �      4   h  �6 @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  p8 @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �8 @   o       A�A�A �D@U
 A�A�A�A    D   �  P9 @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �9 @   �       A�D0p
A�J�
A�A      ���� x �         �  �: @   ,          �  ; @   P       L   �  `; @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �   < @   �          �  �< @   7          �  �< @   s          �  @= @   6          �  �= @   �          �  > @   �          ���� x �            ? @          D@Y     ���� x �         @  @? @   ?       DPz     ���� x �      ,   x  �? @   H       A�A�D`A�A�   ���� x �         �  �? @   2       DPm     ���� x �         �  @ @          L   �   @ @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �  �@ @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �   A @          A�D0WA�    �   A @             �  @A @   .       A�D0   �  pA @   5       DPp     �  �A @   6       D0q     �  �A @   6       D0q                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                                                                                                                                                                                       C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_sscanf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_sscanf.c ucrt_sscanf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                              �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
`f @   ���
@f @   ���
�f @   ���
�f @   ���
�f @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����R     ��U  ��2�  ����R     ��U  ��1�  ����R     ��U  ��1�  ����R     ��U  �	�	4�  �	�	��R     �	�	U  �	�	4�  �	�	��R     �	�	U  �	�	8�  �	�	��R     �	�	U  �	�	8�  �	�	��R     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
� V     �
�
T  �
�
4�  �
�
� V     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � T                RQ�R�        QX�Q�        X�`�X� B                0R09Q9?�R�        .Q.9Y9?�Q�   :?P )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                                                                                                                                               X         Z���� ���� ���� ���� ���� ���� ���� S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                                                                                                            .file   a   ��  gcrtexe.c              �                                �              �   �	                        �   �	                        �   �	                        %   	                        @  �	                        `             k  `
                        �  @
                        �   	                        �  �
                        �  0          �  �
                    envp           argv            argc    (                          P
                        '  �          9  �	                        ^  �	                        �             �  P	                        �  p
                        �  �	                          `	                    mainret            "  
                        8   
                        N  0
                        d   
                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �
     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �
     +                 .file   �  ��  g    �                m              |  P                           �        main    �      .text   P     b              .data                          .bss    0                        .rdata         �                .xdata  t      (                 .pdata  l      $   	                 )        +                 .text   �      .idata$7�      .idata$58      .idata$4�      .idata$6h      .text   �      .idata$7�      .idata$50      .idata$4�      .idata$6D      .text   �      .idata$7�      .idata$5(      .idata$4�      .idata$6      .text   �      .idata$7�      .idata$5       .idata$4�      .idata$6�
      .text   �      .idata$7�      .idata$5      .idata$4x      .idata$6�
      .text   �      .idata$7�      .idata$5      .idata$4p      .idata$6�
      .text   �      .idata$7�      .idata$5      .idata$4h      .idata$6`
      .text   �      .idata$7�      .idata$5       .idata$4`      .idata$64
      .text          .idata$7�      .idata$5�      .idata$4X      .idata$6
      .text         .idata$7�      .idata$5�      .idata$4P      .idata$6�	      .text         .idata$7�      .idata$5�      .idata$4H      .idata$6�	      .text         .idata$7�      .idata$5�      .idata$4@      .idata$6x	      .text          .idata$7�      .idata$5�      .idata$48      .idata$6P	      .text   (      .idata$7�      .idata$5�      .idata$40      .idata$6	      .text   0      .idata$7�      .idata$5�      .idata$4(      .idata$6�      .text   8      .idata$7�      .idata$5�      .idata$4       .idata$6�      .text   @      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   H      .idata$7�      .idata$5�      .idata$4      .idata$6x      .text   P      .idata$7�      .idata$5�      .idata$4      .idata$6H      .text   X      .idata$7`      .idata$5�      .idata$4P      .idata$60      .text   `      .idata$7\      .idata$5�      .idata$4H      .idata$6      .text   h      .idata$7X      .idata$5�      .idata$4@      .idata$6�
      .text   p      .idata$7T      .idata$5�      .idata$48      .idata$6�
      .text   x      .idata$7P      .idata$5�      .idata$40      .idata$6x
      .text   �      .idata$7L      .idata$5�      .idata$4(      .idata$6P
      .text   �      .idata$7H      .idata$5�      .idata$4       .idata$60
      .text   �      .idata$7D      .idata$5�      .idata$4      .idata$6
      .text   �      .idata$7@      .idata$5�      .idata$4      .idata$6�      .text   �      .idata$7<      .idata$5�      .idata$4      .idata$6�      .text   �      .idata$78      .idata$5�      .idata$4       .idata$6�      .text   �      .idata$74      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$70      .idata$5�      .idata$4�      .idata$6p      .text   �      .idata$7,      .idata$5�      .idata$4�      .idata$6X      .text   �      .idata$7(      .idata$5�      .idata$4�      .idata$6@      .text   �      .idata$7$      .idata$5x      .idata$4�      .idata$6      .text   �      .idata$7       .idata$5p      .idata$4�      .idata$6       .text   �      .idata$7      .idata$5h      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5`      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5X      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5P      .idata$4�      .idata$6�      .text          .idata$7      .idata$5H      .idata$4�      .idata$6�      .text         .idata$7�      .idata$58      .idata$4�      .idata$6�      .text         .idata$7�      .idata$50      .idata$4�      .idata$6|      .text         .idata$7�      .idata$5(      .idata$4�      .idata$6h      .text          .idata$7�      .idata$5       .idata$4�      .idata$6P      .text   (      .idata$7�      .idata$5      .idata$4x      .idata$68      .text   0      .idata$7�      .idata$5      .idata$4p      .idata$6(      .text   8      .idata$7�      .idata$5      .idata$4h      .idata$6      .text   @      .idata$7�      .idata$5       .idata$4`      .idata$6�      .text   H      .idata$7|      .idata$5�      .idata$4X      .idata$6�      .text   P      .idata$7x      .idata$5�      .idata$4P      .idata$6�      .file   �  ��  ggccmain.c             �  `                       p.0                �  �          �  	                    __main            �  0       .text   `     �                .data                         .bss    0                       .xdata  �                       .pdata  �      $   	                 �  �&  
   a                   �  �     ?                    �  �     5                     �  0      0                      9     '                     �     �                     )  0     +                     4  P     �                .file   �  ��  gnatstart.c        .text   0                       .data                           .bss    @                           �  �,  
     
                 �  �     �                     �  `                             `     V   
                                               �                         )  `     +                 .file   �  ��  gwildcard.c        .text   0                       .data   0                       .bss    P                            �  �2  
   �                    �  w     .                     �  �                             �     :                      �     �                     )  �     +                 .file   �  ��  gdllargv.c         _setargv0                       .text   0                      .data   @                        .bss    P                        .xdata  �                       .pdata  �                          �  �3  
   �                   �  �     :                     �  �      0                      �     V                      [     �                     )  �     +                     4  �     0                .file     ��  g_newmode.c        .text   @                       .data   @                        .bss    P                           �  5  
   �                    �  �     .                     �  �                             F     :                      �     �                     )  �     +                 .file   <  ��  gtlssup.c              �  @                             p            �                    __xd_a  P       __xd_z  X           (          .text   @     �                .data   @                        .bss    `                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  �5  
   �  6                 �  
     �                    �  �                        �  �      0                      �                          7                            �     �                     )        +                     4       �                .file   P  ��  gxncommod.c        .text                           .data   @                        .bss    p                           �  Y=  
   �                    �  �	     .                     �                               �     :                      �     �                     )  P     +                 .file   l  ��  gcinitexe.c        .text                           .data   @                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �=  
   {                   �  "
     a                     �  @                            �     :                      #     �                     )  �     +                 .file   �  ��  gmerr.c            _matherr                        .text         �                .data   @                        .bss    �                        .rdata        @               .xdata  �                       .pdata  �                          �  ^?  
   6  
                 �  �
                         �       �                    �  `     0                      
     �                      �     �                     )  �     +                     4  �     X                .file   �  ��  gCRT_fp10.c        _fpreset!                       fpreset !      .text   !                      .data   @                        .bss    �                        .xdata  �                       .pdata  �                          �  �B  
   �                    �  �     -                     �  �     0                      �     X                           �                     )  �     +                     4       0                .file   �  ��  gmingw_helpers.    .text    !                       .data   @                        .bss    �                           �  +C  
   �                    �  �     .                     �  �                             	     :                      	     �                     )  
     +                 .file   �  ��  gpseudo-reloc.c        4   !                           C  �!          Y  �       the_secs�           e   #            �           �  0	                        �  @	                    .text    !     =  &             .data   @                        .bss    �                       .rdata  @     [                .xdata  �      0                 .pdata  �      $   	                 �  �C  
   K  �                 �  �     �                    �  �     �  
                 �  �     0                    �  \      W                       Z	     �                     V     	                       �	     O                    )  @
     +                     4  8     �                .file   
  ��  gusermatherr.c         �  `&                           �  �             �&      .text   `&     L                .data   @                        .bss    �                       .xdata                        .pdata                            �  [  
   �                   �  �                         �  !	     r                     �       0                      �     �                           �                     )  p
     +                     4       P                .file     ��  gxtxtmode.c        .text   �&                       .data   @                        .bss    �                           �  �]  
   �                    �  �     .                     �  @                            �     :                      �     �                     )  �
     +                 .file   @  ��  gcrt_handler.c         #  �&                       .text   �&     �               .data   @                        .bss    �                       .xdata  (                      .rdata  �     (   
             .pdata  8                         �  �^  
   �                   �  �     ~                    �  �	     _                    �  `     0                      �     �  
                   _                            �                         )  �
     +                     4  h     P                .file   f  ��  gtlsthrd.c             :  p(                           Z             h  �           v  �(          �  �           �  P)          �  �)      .text   p(     b  "             .data   @                        .bss    �      H                 .xdata  0     0                 .pdata  D     0                    �  fn  
   �
  A                 �  y     a                    �  �
     �                    �  �     0                    �  �                             O     x                     �
     %                    )        +                     4  �     (               .file   z  ��  gtlsmcrt.c         .text   �*                       .data   @                       .bss    @                           �  9y  
   �                    �  �     .                     �  �                            �     :                      �     �                     )  0     +                 .file   �  ��  g    �            .text   �*                       .data   P                        .bss    @                          �  �y  
   �                    �       0                     �  �                                 :                      V     �                     )  `     +                 .file   �  ��  gpesect.c              �  �*                             +            `+          %   ,          B  �,          Z  �,          m  @-          }  �-          �  .      .text   �*     �  	             .data   P                        .bss    P                       .xdata  `     ,                 .pdata  t     l                    �  �z  
   �  �                 �  8     �                    �  �
     �                    �        0                    �  �      �                       ;     K                     o     T                            �                     )  �     +                     4  �     (               .text   �.     2                 .data   P                        .bss    P                       .text    /                       .data   P                        .bss    P                           )  �     +                 .file   �  ��  gmingw_matherr.    .text    /                       .data   P                       .bss    p                           �  d�  
   �                    �  �     .                     �  0                            �     :                      �     �                     )  �     +                 .file   �  ��  gucrt_vfprintf.    vfprintf /                       .text    /                     .data   `                      .bss    p                       .xdata  �                      .pdata  �                         �  �  
   �                   �  �     8                    �  �     X                     �  P     0                      �     r   	                   �     �                     )        +                     4       8                .file     ��  gucrt_sscanf.c     sscanf  @/                       .text   @/     ?                .data   p                      .bss    p                       .xdata  �                      .pdata  �                         �  ��  
   }                   �  (     9                    �  
     F                     �  �     0                      2     �   	                   �     �                     )  P     +                     4  @     8                .file   4  ��  gucrt_printf.c     printf  �/                       .text   �/     H                .data   �                      .bss    p                       .xdata  �                      .pdata  �                         �  �  
   �  
                 �  a     l                    �  S     -                     �  �     0                      �     �   	                   o     �                     )  �     +                     4  x     H                .file   R  ��  gucrt_fprintf.c    fprintf �/                       .text   �/     2                .data   �                      .bss    p                       .xdata  �                      .pdata                           �  Ϛ  
   �                   �  �     b                    �  �     F                     �  �     0                      R     �   	                   Q     �                     )  �     +                     4  �     8                .file   h  ��  g__initenv.c           �  p          �  x      .text   0                       .data   �                      .bss    p                          �  q�  
   �                   �  /     �                     �                              �     [                      6                         )  �     +                 .file   �  ��  gucrtbase_compa        �  0                           �   0          �  �0      _onexit  1             1            �	                        >  @1          I  p1      tzset   �1          W  p	                    _tzset  �1          s  �           �  �           �             �            �        .text   0       "             .data   �      x   
             .bss    �                       .xdata  �     P                 .pdata       l                .rdata  �                          �  �  
   �  Y                 �  �                          �  �     |                    �  0     0                      7     �                     �                            S     `                    )       +                     4  �     �               .text   02      .data   0      .bss    �      .idata$7H      .idata$5�      .idata$4(      .idata$6�      .text   82      .data   0      .bss    �      .idata$7L      .idata$5�      .idata$40      .idata$6�      .text   @2      .data   0      .bss    �      .idata$7P      .idata$5�      .idata$48      .idata$6�      .text   H2      .data   0      .bss    �      .idata$7T      .idata$5�      .idata$4@      .idata$6�      .file   �  ��  gfake              hname   (      fthunk  �      .text   P2                       .data   0                       .bss    �                       .idata$2�                      .idata$4(      .idata$5�      .file   �  ��  gfake              .text   P2                       .data   0                       .bss    �                       .idata$4H                      .idata$5�                      .idata$7X                      .text   P2      .data   0      .bss    �      .idata$7      .idata$5�      .idata$4      .idata$6v      .text   X2      .data   0      .bss    �      .idata$7      .idata$5�      .idata$4      .idata$6�      .text   `2      .data   0      .bss    �      .idata$7       .idata$5�      .idata$4      .idata$6�      .file   �  ��  gfake              hname         fthunk  �      .text   p2                       .data   0                       .bss    �                       .idata$2�                      .idata$4      .idata$5�      .file   J  ��  gfake              .text   p2                       .data   0                       .bss    �                       .idata$4                       .idata$5�                      .idata$7$     !                 .text   p2      .data   0      .bss    �      .idata$7�      .idata$5P      .idata$4�      .idata$6�      .text   x2      .data   0      .bss    �      .idata$7�      .idata$5X      .idata$4�      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5`      .idata$4�      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5h      .idata$4�      .idata$6       .text   �2      .data   0      .bss    �      .idata$7�      .idata$5p      .idata$4�      .idata$6      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5x      .idata$4�      .idata$66      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6P      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6Z      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6d      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6n      .file   X  ��  gfake              hname   �      fthunk  P      .text   �2                       .data   0                       .bss    �                       .idata$2�                      .idata$4�      .idata$5P      .file   �  ��  gfake              .text   �2                       .data   0                       .bss    �                       .idata$4                       .idata$5�                      .idata$7�                       .text   �2      .data   0      .bss    �      .idata$7h      .idata$5�      .idata$4       .idata$6�      .text   �2      .data   0      .bss    �      .idata$7l      .idata$5�      .idata$4(      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7p      .idata$5�      .idata$40      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7t      .idata$5�      .idata$48      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7x      .idata$5�      .idata$4@      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7|      .idata$5�      .idata$4H      .idata$6�      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4P      .idata$6
      .text   �2      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4X      .idata$6       .text    3      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4`      .idata$6.      .text   3      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4h      .idata$66      .text   3      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4p      .idata$6X      .text   3      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4x      .idata$6x      .text    3      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   (3      .data   0      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�      .text   03      .data   0      .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�      .text   83      .data   0      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�      .text   @3      .data   0      .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6�      .file   �  ��  gfake              hname          fthunk  �      .text   P3                       .data   0                       .bss    �                       .idata$2�                      .idata$4       .idata$5�      .file     ��  gfake              .text   P3                       .data   0                       .bss    �                       .idata$4�                      .idata$5H                      .idata$7�     "                 .text   P3      .data   0      .bss    �      .idata$7<      .idata$5�      .idata$4      .idata$6�      .text   X3      .data   0      .bss    �      .idata$7@      .idata$5�      .idata$4      .idata$6�      .file     ��  gfake              hname         fthunk  �      .text   `3                       .data   0                       .bss    �                       .idata$2x                      .idata$4      .idata$5�      .file   *  ��  gfake              .text   `3                       .data   0                       .bss    �                       .idata$4                      .idata$5�                      .idata$7D     "                 .text   `3      .data   0      .bss    �      .idata$7      .idata$5�      .idata$4�      .idata$6n      .file   8  ��  gfake              hname   �      fthunk  �      .text   p3                       .data   0                       .bss    �                       .idata$2d                      .idata$4�      .idata$5�      .file   b  ��  gfake              .text   p3                       .data   0                       .bss    �                       .idata$4                       .idata$5�                      .idata$7                      .text   p3      .data   0      .bss    �      .idata$7�      .idata$5p      .idata$4�      .idata$6B      .text   x3      .data   0      .bss    �      .idata$7�      .idata$5x      .idata$4�      .idata$6R      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6\      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6d      .file   p  ��  gfake              hname   �      fthunk  p      .text   �3                       .data   0                       .bss    �                       .idata$2P                      .idata$4�      .idata$5p      .file   �  ��  gfake              .text   �3                       .data   0                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�                      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5X      .idata$4�      .idata$6"      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5`      .idata$4�      .idata$62      .file   �  ��  gfake              hname   �      fthunk  X      .text   �3                       .data   0                       .bss    �                       .idata$2<                      .idata$4�      .idata$5X      .file   �  ��  gfake              .text   �3                       .data   0                       .bss    �                       .idata$4�                      .idata$5h                      .idata$7�     &                 .text   �3      .data   0      .bss    �      .idata$7�      .idata$5H      .idata$4�      .idata$6      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6       .text   �3      .data   0      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�      .text   �3      .data   0      .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4x      .idata$6�      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4p      .idata$6�      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4h      .idata$6p      .text   �3      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4`      .idata$6X      .file   �  ��  gfake              hname   `      fthunk         .text   �3                       .data   0                       .bss    �                       .idata$2(                      .idata$4`      .idata$5       .file   
  ��  gfake              .text   �3                       .data   0                       .bss    �                       .idata$4�                      .idata$5P                      .idata$7�     
                 .file   1  ��  gcygming-crtend        �  �3                       .text   �3                       .data   0                       .bss    �                           �  �3                         �                             �  |                         	  4                         )  @     +                 .idata$2        .idata$5�      .idata$4      .idata$2       .idata$5H      .idata$4�      .idata$2�       .idata$5�      .idata$4P      .idata$4�      .idata$5@      .idata$7�      .idata$4X      .idata$5�      .idata$7d      .idata$4�      .idata$5@      .idata$7�      .rsrc       
    __xc_z             	             -	  p          L	  02          W	            f	  �2          	  0          �	  �          �	  X          �	              �	            �	  4          
  �           
  �          ,
  X          K
  x           z
  �3          �
  �          �
  �	          �
  �          �
  �            x          !  `          2  �          C  (          W  �           d      	        s  �	          �  (3          �  �       __xl_a  0           �  �          �  �3          �            �  P          '  p          ;  �          c  �          �        _cexit  �2          �  `  ��       �     ��       �  X          
              )
  H          <
      ��       V
     ��       r
  0           �
             �
  (      __xl_d  @           �
  (      _tls_end   	        �
  	      __tzname@2          �
  �3          �
  �                          �           1  0           A  �	          _  0          �  �2          �  �          �  `	          �      	    memcpy  X3          �               p          .  �
      puts    �2          ?  @	          e  �           ~  �      malloc  �3      isatty  �2      _CRT_MT @           �  �3          �  �          �  �          �  �            �          8              F  �          a  p          �  �          �     ��       �  h          �            �  P
          �              h           /  �          :  �          U            i            �  �3          �  �          �  �	          �  `           �               �3          %  <           X  �          k  �          x  P           �            �             �  H          �  P3            �            �	      abort   03          ;  0	          e  �           y  (          �  0          �  P           �  �          �  �      __dll__     ��       !      ��       6  �2          A  �          [  d          z  $          �  �3          �  P           �  �          �  p3          �   
            0            �          )  d           U  P          g  �          �             �     ��       �  �          �  $           �  D      calloc  x3          $  �          .  �          `  �          �            �  H          �  �           �  �          �  p            �          3  �          P            _  H      Sleep   �3          s  �
      _commodep           �  0          �  �          �   4          �  p          �  P           �  �              (             �      __xi_z  (           &  �          >              V             f  �	          �            �             �  �2          �  �	          �              �          2  l       signal  @3          =  p2          M  H           d  8          w              �  �      strncmp `2          �  �          �   4          �  p           �  P	            @            �           <  �          V  �          �             �  �	          �      ��       �  8          �   	            x          +  �          \  x          �  �          �  �          �  �          �  �          �  �          �  p
            �           %  �          I  p          ~  @          �  �          �     ��       �            �  �          �  �3            3          +  �2          >  �3          X  8          c  �           �             �  �          �  �            `                ��       3              B  �           R  �          t  �          �  �          �  �           �  �          �  �                       ?  �          L  0      __xl_z  H       __end__              r  p          �  (          �  �          �             �  �      strcmp  P2          �  h              4      __xi_a             .   @          M    3          \   0          h   �3      __xc_a              }   �          �      ��       �   �          �   P           �      ��   _fmode  �           �   8          !  `           !  �          9!  `3          J!  �          [!  @
          l!  �           z!  �2          �!             �!  X          �!  �          �!  �          �!  �2          "  �          ("  @          ="  �          k"         __xl_c  8           x"  �          �"     	        �"  p	          �"  H          �"            �"  d           �"  0           �"  �          +#  �          C#  82          N#            h#  �          �#  �      _newmodeP           �#  3      fwrite  �2          �#  �          �#  �          $  `
          $  P          &$      ��       >$      ��       O$             r$  X          �$  �          �$  �.          �$  �          �$  @           �$  x2          �$            (%  �          K%  p          ]%        exit    83          �%     ��       �%      ��       �%  �          �%         fileno  �2          �%  P          &  �          &  x      _exit    3          8&  �           E&  (          �&   
          �&  �2      strlen  X2          �&  0
          �&  �2          �&   	          �&  �3          �&  3          '  8          D'  `          `'  `          ~'  �          �'  �           �'  p          �'  �           �'  �          (              @(  8          n(            (  �          �(  
          �(  �           �(  P           �(  �          )  �2          /)  h          H)  �           W)  �2      free    �3          c)            p)  �       �)  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame TOOL_COPYRIGHT print_usage print_xml ideviceactivation.c __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .text.startup .xdata.startup .pdata.startup .ctors.65535 __imp_plist_get_string_val ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight plist_new_dict __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ __imp_idevice_activation_response_get_headers ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter mobileactivation_activate_with_session .refptr.__mingw_initltsdrot_force idevice_new __imp_idevice_activation_request_set_url __imp_calloc __imp___p__fmode __imp___p___argc plist_get_node_type __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset __imp_fileno GetLastError __imp__initialize_wide_environment idevice_activation_drm_handshake_request_new __rt_psrelocs_start __imp_lockdownd_service_descriptor_free __imp_idevice_activation_response_field_requires_input __imp_plist_get_node_type __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp_plist_new_bool __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll idevice_activation_request_set_fields __stdio_common_vsscanf idevice_activation_response_get_title .refptr.__imp___initenv _tls_start __imp_idevice_activation_response_has_errors __imp_lockdownd_client_free .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler lockdownd_service_descriptor_free TlsGetValue idevice_activation_response_get_label idevice_activation_set_debug_level __imp_strcmp __imp_mobileactivation_create_activation_session_info __bss_start__ __imp___C_specific_handler ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ __imp_idevice_activation_request_new .refptr.__mingw_app_type __mingw_initltssuo_force __imp_plist_dict_next_item VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a lockdownd_activate __imp__tzset ___crt_xp_start__ __imp_idevice_activation_response_get_title __imp_LeaveCriticalSection idevice_activation_request_free __C_specific_handler lockdownd_get_value .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf idevice_activation_request_set_fields_from_response plist_free ___crt_xp_end__ __imp_lockdownd_start_service __imp_mobileactivation_create_activation_info_with_session __minor_os_version__ __p___argv __imp_plist_dict_get_size libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a __imp_plist_new_dict .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp_idevice_new __imp_lockdownd_deactivate __imp__exit __section_alignment__ idevice_activation_response_is_activation_acknowledged __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __imp_idevice_activation_response_get_description idevice_activation_response_get_fields idevice_activation_response_field_requires_input __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ lockdownd_start_service mobileactivation_client_free plist_new_bool plist_dict_new_iter .refptr._newmode __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force idevice_activation_response_free idevice_activation_response_get_description __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection __imp_plist_dict_new_iter _tls_index __acrt_iob_func __native_startup_state __imp_plist_to_xml ___crt_xc_start__ lockdownd_client_free __imp_idevice_activation_request_free ___CTOR_LIST__ __imp_sscanf .refptr.__dyn_tls_init_callback __imp_signal _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_lockdownd_get_value __imp_idevice_activation_request_set_fields_from_response plist_get_string_val .refptr.__mingw_initltsdyn_force __rt_psrelocs_size plist_dict_set_item .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname mobileactivation_create_activation_info __imp___p___wargv __imp_strlen lockdownd_deactivate __imp_idevice_activation_request_set_fields __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake mobileactivation_create_activation_info_with_session plist_dict_next_item __imp___daylight __file_alignment__ __imp_InitializeCriticalSection idevice_activation_response_has_errors __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_idevice_activation_response_get_activation_record idevice_activation_response_get_headers __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_mobileactivation_deactivate __imp_mobileactivation_client_start_service __imp_idevice_activation_response_free _head_libplist_2_0_dll __IAT_start__ mobileactivation_client_start_service __imp_idevice_activation_response_get_fields __imp__cexit __imp_idevice_activation_send_request __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp_mobileactivation_client_new __imp__onexit __imp_isatty mobileactivation_create_activation_session_info __DTOR_LIST__ idevice_activation_request_new __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ __imp_mobileactivation_activate_with_session ___crt_xt_start__ __subsystem__ __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_mobileactivation_client_free __imp_VirtualProtect __imp_mobileactivation_create_activation_info idevice_free mobileactivation_activate ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __imp_plist_dict_set_item idevice_activation_send_request __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_idevice_activation_drm_handshake_request_new __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ idevice_activation_request_set_url mobileactivation_get_activation_state __imp_lockdownd_set_value ___chkstk_ms lockdownd_set_value __native_startup_lock __p__commode idevice_activation_response_get_activation_record libideviceactivation_1_0_dll_iname __rt_psrelocs_end __imp_idevice_activation_response_get_label __minor_subsystem_version__ __minor_image_version__ mobileactivation_client_new __imp___set_app_type plist_dict_get_size __imp__crt_at_quick_exit __imp___stdio_common_vsscanf __imp_printf __imp_idevice_activation_response_is_activation_acknowledged .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp_idevice_activation_set_debug_level mobileactivation_deactivate __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_mobileactivation_activate _head_libideviceactivation_1_0_dll idevice_activation_request_new_from_lockdownd __imp_plist_free __imp_mobileactivation_get_activation_state .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __imp_idevice_activation_request_new_from_lockdownd __stdio_common_vfprintf __imp_lockdownd_activate __imp_daylight __p___wargv plist_to_xml __mingw_app_type 