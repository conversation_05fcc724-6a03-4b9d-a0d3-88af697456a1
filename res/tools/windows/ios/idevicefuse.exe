MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �=�g 6 		  � & ( 8   v     �        @                           G  `                                             �  0   �  �   �  �             �                           �i  (                   h�  P                          .text   �6      8                 `  `.data   0   P      >              @  �.rdata  �   `      @              @  @.pdata  �   �      V              @  @.xdata  x   �      Z              @  @.bss    �   �                      �  �.idata  0   �      ^              @  �.CRT    `    �      p              @  �.tls        �      r              @  �.rsrc   �   �      t              @  �.reloc  �          z              @  B/4               |              @  B/19     ��      �   �              @  B/31     �(      *   |             @  B/45     �4   P  6   �             @  B/57     �   �     �             @  B/70     b   �     �             @  B/81     r   �     �             @  B/97     �&   �  (   
             @  B/113    $         2             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��]  1��    H��]  �    H��]  �    H��\  f�8MZuHcP<HЁ8PE  tfH�/]  �
��  � ��tC�   �9)  �D(  H��]  ���,(  H��]  ���4  H�m\  �8tP1�H��(Ð�   ��(  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�]  �<  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�e]  L�֎  H�׎  H�
؎  � ���  H�]  D�H���  H�D$ ��$  �H��8��    ATUWVSH�� H�_\  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5,\  1�����V  �����  ��     ����L  ���e  H�Q[  H� H��tE1��   1����x  H�
Q\  ��  H��[  H�
����H��m'  �`  �ҍ  �{Hc�H��H���'  L�%��  H�Ņ��F  H��1�I��&  H�pH���'  I��H�D I�H��H���;'  H9�u�H�H�    H�-]�  ��  H��Z  L�B�  �
L�  H� L� H�7�  ��'  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5�Z  �   ���������   �/$  ��������H��Z  H�
�Z  �I&  �   �������1�H�����f�     ��%  ���  H�� [^_]A\�f.�     H��Z  H�
�Z  �   ��%  �7���f�H�����������%  �H��(H��Y  �    ������H��(� H��(H��Y  �     �z�����H��(� H��(�#  H���H��(Ð�����������H�
	   �����@ Ð��������������H��(H�
�K  ��$  H�
�K  ��$  �
   �$  H�
�K  �$  H�
�K  �$  H�
;L  �$  H�
�L  �$  H�
�L  �~$  H�
'M  �r$  H�
SM  �f$  H�
�M  �Z$  H�
�M  �N$  H�
�M  �B$  H�
N  �6$  H�
5N  �*$  H�
8N  H��(�$  f.�     UAWAVAUATWVSH��   H��$�   I��I��M��H����  H����  M����  H�E�L��H�E�    I��H�E�    H�E��>  f��H�E�    A��E�E�E����+  H�}�H���-  H��   L�=�M  H��uh�  D  H��M  H���"  ��t=H��M  H���"  ���.  H�7H��M  H���s"  ���{  A� ���fD�]�H�\7H��H��t.L��H���G"  ��u�H�7�$  H�}�H�E�H�\7H��H��u��]�H���g  f�� @�o  f�� ���  D����  f�� ��,  f�� �H��L  L��L  LE�� �  DD�H�
%O  L����  H�M��<�    H�M�H��t�E��
  D�M�A��
�  H�
:M  E��L���  H�M�H��t��	  �H�e[^_A\A]A^A_]��    H��L  H���I!  ���q  H��L  H���2!  �������H�7��"  H�}�H��H����&�.H��H��?H��H)�H�U�����f�     H�4L  H����   ��t}H��K  H����   ����  A� ���fD�M��V���fD  H���	  f�� @�����L�E�L��L����  ���>  ��
��  H�
�L  A��L���  H�M������f�A� @  fD�U������L�=K  f�� �r���f��  ��  L�K  �[����    H�
�L  ��   �   H�e�H�E�    ��  A�   L�M�H)�L��L��H�t$0�9  ����  A��  �  L���.   �E�����q  H�}�H�
�L  H��H�}���  H��H�
�L  ��  H���,  H�E�H�E�H�E�H�U�I��L���E�    A�   H�D$ �  ����  D�}�L��M��t�1��fD  H�I9���   �M�A��H�A)��  HcЃ��u�H��  �Ӌ0�Ӌ�^   A��L��H�
L  I���!  H�U�H����  H�e�H�M��\����    H�7�   H�}�f�E��Z���f.�     H���E��
  D�M�L�vI  �����D  H�
�I  L���  H�M�������     L)}�H�E�H�������H�
�K  �  �]���fD  L���o�  ����  H�E�A�   H�H��H��u$����D  �{ uJ�8H��I��H��������;.t�;.u�{.u�{ t��     H����  L��H����  �THc�H��H�U��R  H�\$ H�U�M��L�FJ  H��H���  L���  �THc�H��H�U��  H�\$ H�U�M��H��L�
J  H���S  I��H��L�������H��tH����  H��tH����  H�E�� ���f�L��A��H�
�I  �N  H�U�H����   ������g  ����f�H�LH  H����  ��t?H��G  H����  ����   �    f�M��h����     A� �  L��G  �����A� 0  fD�E��>���H�
H  L���  H�M�����H�_�  �Ӄ8�=����Ӌ0�Ӌ�  H�
�H  A��L��I���  H�M������H�"�  �Ӌ0�Ӌ�  L��A��H�
 I  I���P  H�U�H������L���k  H�E�    �����L���V  �
���H��F  H����  ��u�   f�U��p���H��F  H����  ���Y���� ���f�E��K���A��L��H�
�H  ��  ����A��  H��F  H�
�F  �*�  A��  H�rF  H�
}F  ��  A��  H�XF  H�
_F  ���  �D  ATUWVSH�� H��I���;  H��H���0  H�LH���  I��L��H��H���j  H�L�EH���Z  H��H�� [^_]A\�@ WVSH�� H�fH  H���R  H��H��t[A�   1�H���B  H���B  E1�1�H����+  Hc��+  H��H���	 �H��H����  ���u�� H��H�� [^_ù   1����  I��H��G  H����  ��fD  ATUWVSH��@H�/DocumenH�T$xH��L�d$x�J  H��H���  H�H�  H��H�(H�HH���@ts/ ��  H�
ց  H���  H����  H�H�e  H��H�(H�HH���@ts/ �  H�
��  H��M��A�   ��  �ƅ���   H������H��H����   H���  H�T$<H�
Z�  I��H�T$ H�T$xA���  H���]  H�H��  H��H�(H�HH���@ts/ �3  H�
�  M��H��A�   �k  ����   H��H�
�F  �,  �   �1�  H���a  H�T$xH�
̀  �8  �H��@[^_]A\�@ H�=�  ���  �   ��A��I��H��F  H���  H�T$xH�
��  ��   �   ��H����  �@ A��H��H�
,F  �  H�T$xH�
J�  �   �   ���  H���  �   ��  �J�������%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%ғ  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%��  ��H��(H�/  H� H��t"D  ��H��.  H�PH�@H��.  H��u�H��(�fD  VSH��(H��K  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^���� 1�fD  D�@��J�<� L��u��fD  ��~  ��t�D  ��~     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H��J  �8t�    ��t��tN�   H��([^�f�H��  H�5
�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�LH  Hc�H����    H�0G  �DA �y�qH�q�   �  �DD$0I��H��G  �|$(H��I���t$ ��  �t$@|$P1�DD$`H��x[^ÐH��F  ��    H��F  ��    H��F  �s���@ H�)G  �c���@ H��F  �S���H�CG  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(��  A�   �   H�
BG  I���  H�t$(�   �  H��H��I���-  �  ��    WVSH��PHc5�|  H�˅��  H��|  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H�e|  H��H��H�H�x �     �#  �WA�0   H�H�7|  H�T$ H�L���  H���}   �D$D�P����t�P���u��{  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H�{  H�KI��H�S�$�  ��u���  H�
cF  ���d���@ 1��!���H��{  �WH�
F  L�D�>���H��H�
�E  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%D{  E��tH�e[^_A\A]A^A_]�fD  �{     �9	  H�H��H��   H����  L�-;G  H�DG  ��z      H)�H�D$0H��z  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5�F  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
E  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  �Ny  ������H�5��  1�H�}�D  H�1y  H�D� E��t
H�PH�HI����A��H��(D;%y  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5pD  �s�;H��L�>H���Z����>L9�r��������H�
-C  �����H�
�B  ���������H��XH�x  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
�w  �  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�C  Hc�H��� 1ҹ   �$  H���>  H���  H�jw  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   ��  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �d  H���@����   �   �K  �f�     1ҹ   �4  H��t*H�������   ���i���f�     �   ���T����   �   ��  �@����   �   ��  �,����   �   ��  �����������ATUWVSH�� L�%?v  L�����  H�v  H��t6H�-#�  H�=�  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%ȉ  WVSH�� ��u  ��H�օ�u
1�H�� [^_ú   �   �Y  H��H��t3H�pH�5�u  �8H���[�  H�lu  H��H�bu  H�C�`�  묃��멐VSH��(�Lu  �˅�u1�H��([^�D  H�5Iu  H����  H�
u  H��t'1��H��H��tH���9�H�Au�H��tH�B�
  H����  1�H��([^� H��t  ��ff.�     @ SH�� ����   w0��tL��t  ����   ��t     �   H�� [�f�     ��u�}t  ��t��<�����f.�     �bt  ��uf�Xt  ��u�H�Dt  H��t�    H��H�[��  H��u�H�
@t  H�t      �t      ��  �l����k����   H�� [������f�     H�
�s  �ˇ  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���
  H��w{H�?  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���
  ��u�H��H�� [^_��    1�H��H�� [^_� H��>  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H�	>  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L��=  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H�I=  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H�	=  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�y<  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������AVAUATUWVSH�� 1�H��H���<�  f����G�@�ŀ�/��   ��\��   �Ѓ�߃�A<�4  �{:�\  L�%�  E1�1��>fD  1�1�E��t�~GfHn�H�G    fl�G�SA��H���Ƅ�tJ@ ��uĉ�A�ԅ���   �</��<\��	�D�����!���   H�_�SH��A��1���u�H�_ H�� [^_]A\A]A^��    �C</t<\�D����SH�s��tYL�-U�  E1�1�E1�� E1�1��VH����t4��u��A�Յ�uR���/����\��A��	���A!�tsA��A��u�H�7�H���������[���D  �   �����fD  E1�   �fD  H�CH��SH�Ä����������D  ����A!�����A���O���f�UWVSH��XH�59  H��H��t�9 uH��H��X[^_]��     H��H�L$ �����L�D$ �H��M��IE���/t��\t���/tH����\u�    H�-�8  H�T$(H��t� �8 uH9�tjH9D$@t�U �@ �H���u���f�     L��H�
fm  H)�H�W�  H��H��t*I��H��H��H�Bm  �U  �E L�>A�@ A� �$���H������SH��PH��H��t�9 uH�	8  H��P[ÐH��H�L$ �����H�T$ �H��HD�</t	<\t�: t�H�D$8H��t�  H�D$0H��u��: H��7  HE�H��P[Ð�����������H��(i��  ��  1�H��(Ð��������H��8E1�L�D$ I��H��1��_  H��8Ð�H��HH�D$hL�L$hM��I��H�D$(H�ʹ   H�D$     H�D$8�4  H��HÐ������VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H���(  ���   �����  �  � ��  H� H��  H� H�M��t	A�$�S  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �K  � ��R  H� H��?  H� H�M��t	A�$��  1�H�� [^_]A\�fD  SH�� H���C  ���    HD�H�� [�f�H�y6  �8 t1�Ð�  ff.�     SH�� �˹   �/  A��H�U5  H���m�����   ��  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8�   H��H�ff.�     H��(H��5  ��~   H��  �j   H��  �V   H��  H��(�f.�     H��(H�U5  ��>   H��  �*   H��  �   H��  H��(Ð����������%  ���%  ���%  ���     �%�~  ���%�~  ���%�~  ���%�~  ���%�~  ���     �%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���%
~  ���     �%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%�|  ���%|  ���%|  ���%�{  ���     �%�{  ���%�{  ���%�{  ���%�{  ���%�{  ���     �%z{  ���     �%R{  ���%R{  ���%*{  ���%*{  ���%
{  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%zz  ���%jz  ���%Zz  ��UAWAVAUATWVSH��hH�l$`A��H��������~e     A��~W�   L�5�+  Hc�H�4�L�,�    D�>A��-��   �~d�}   �~ uw�   �=����/e  ���&e  D9�|��=�  ���  D��  H��d  H�
'e  �
�����  ����  H��d  H����  H�
e+  �����   �]�L��H����������v���A��-u
�~u��   H��*  H��������uFJ�D/����d  H��t�8 u"H�
�*  �6���1ۉ�H�e[^_A\A]A^A_]ÐH�Id  ����H��*  H���U�������   H�q*  H���>�����uh�C�d  J�D/H���  �8 �  H��c  J�D/����c  H����   �8 t{H��c  �j     �����~ �-�������H�*  H���������ui�C��c  J�D/H����  �8 ��  H�hc  J�D/���rc  H��t	�8 �  H�
�)  � ��������E1��Jc  D��  ����H��)  H���G�������   H��)  H���0������C  �C�c  J�D/H���J  �8 �A  H��b  J�D/����b  H��b  J�D/H���b����8 �Y���H��b  �H     �x����C��b  J�D/H����  �8 ��  H�hb  J�D/���rb  H�������8 �����H�1b  ��     ����H�b  ��     ����H�
Ub  L�)  H�?b  ������a  �Å���  H�
#b  L�b  H�#)  �������a  �Å��  H�
�a  �����H��a  H�
�a  L��a  �������a  ����  �=A   �=  L�pa  H�
�a  H�=V)  H�������Ha  ��tY�   H�F)  �/����   H�5�w  ��A�   �   H��I�������L�a  H�
Ea  H���M�����`  ���r  1�H�U�H�M�H�
a  �1���H�M�H��(  ��`  ����H����  H�
�(  ����H�
�`  ����������H�B'  H���������u@J�D/����`  H���?����8 �6���H�k`  �%     �U����g�������H��&  H����������   J�D/���U`  H��������8 �����H�`  ��     ������   �zv  A��H�'  H������H�
1`  �����/���H�
�&  ��������H�
-&  �����o����   �*v  A��H�'  H���X���H�
�_  ����������H�
&  �k����0���H�&  H��������tH�	&  H��������������
  �g_  �3���H�
'  ����H�
w_  �b��������H�
N_  H�?_  ������^  ���;  ��  ����wNH�+(  Hc�H���H��^  H�
&'  �����H�
�^  H���,�����^  �Å��!  H�
'  ����H�
�^  H��t�����H�
�^  H��t�����@  �/���1�H�=�&  H�5�!  H�U�H�h^  H���P���H�
y^  L�E�H�������'^  �Å�t$�%  H�XH�PH�U�H��H���U�������  H�E�H�8 u�1��  H�
%^  1�L�E�H�>&  H�E�L�%�&  H�=�&  �I�����]  ��t2���t  H�
�&  ����H�
�]  ���������H��H�]�����H�]�H�3H��t�L��H��H��������tH��H��������u�H�]���H�
r]  H�5�%  H������H���)1�H��H���9���H��1�����H�
X]  H��H������H��u��a���H�
<]  H�[%  �h�����\  �Å�t	��
�`  H�
&  ������:���L��\  H��\  H�
�\  ���������   �0s  A�    �   H�
V$  I���v���H�
�\  �����������   �k����   ��A�   �   H��I���>���L�W\  H�
�\  H�������6\  ���C����   � ����   ��A�   �   H��I�������L�\  H�
=\  H���E�����[  ��������   ��H��A�   �   I������H�
\  ���������   �6r  A�   �   H�
�#  I���|���H�
�[  �����������   � r  A��H��#  H���.���H�
�[  ��������H���e�����H�\[  H���D���H�
m[  L�M�A�   H��������[  �Å���   ��E1�L�u������=B�D0 H�
�#  A�������L��H�
�#  �U���H�
�#  �����L��A9���   Ei�  ���  @ I��A�A9�DO�Mc�I�BL�U�H��������H�
�Z  H�U�H)�L�t$ L�l$0M������L�U؅��dZ  �d���H�
F#  �R���H�U�H�
�Z  �����L������H�
W#  �.����{����   ��p  A��H��"  H�������H�U�H�
AZ  �����U����   �|p  A��H��"  �   H������H�
.Z  �����������������������������������pF @           ��������                                                                                                                                                                                                                                                                                                                                                                   ����       �F @           ��������        ����                           ����            P5 @           p5 @           �5 @            6 @           � @   � @   �7 @   �7 @   @6 @   p7 @   �6 @   P6 @   �P @   �P @    Q @      �p  Q @   Q @   PDT PST P7 @   07 @                                                                                                                                                                                                                           Usage: idevicefuse [OPTIONS]    SandBox File Managment Tool For iOS.      -a, Return Responce For HeartBeat Client        -s  [FilePaths Comma Seprated] [BUNDLE-ID], Send File in Sandbox of [BUNDLE-ID]         -r  [SandboxFileName] [BUNDLE-ID], Read File Content in Sandbox of [BUNDLE-ID]          -t  [SandboxFileName] [BUNDLE-ID], Truncate File Sandbox of [BUNDLE-ID]         -c  [SandboxFilePath] [LocalFilePath] [BUNDLE-ID], Copy Content of SandBox File To Loca Directory       -e  [BUNDLE-ID],empty complete Sandbox of [BUNDLE-ID]   -l  [BUNDLE-ID], List All Files in Sandbox of [BUNDLE-ID]       -n, --network      connect to network device even if available via USB          -d, --debug		enable communication debugging     -u, --udid UDID	use UDID to target a specific device    -h, --help		print usage information PhoneCheck 2.0 Developed By AaterAli  UNKNOWN S_IFLNK S_IFCHR S_IFIFO S_IFSOCK idevicefuse.c afc dst_path src_path st_size st_blocks st_ifmt S_IFREG S_IFDIR S_IFBLK st_nlink st_mtime    Skipping '%s' due to permission denied when getting info.
      Skipping '%s' due to error getting info.  Error:%d
     Skipping '%s' due to permission denied when getting directory listing.
 Skipping '%s' due to error getting directory listing. Error: %d
        Error creating directory '%s': %s(%d)
 %s/%s init copy file ... Error opening file '%s':  %d
   Error creating file '%s': %s(%d)
 size is %d
 total_remaining is %d
 Error writing '%s': %s(%d)
 Error reading '%s': %d
 success!       Skipping '%s' due to unsupported file mode %s(%d).
 r Could not open file '%s'
 %s Push Success
 %s Push Fail , error code %d
  ERROR: Could not Open File %s, error code %d
 --debug --udid Wrong Udid -a -s Wrong FilePath Wrong BundleID -r Wrong FileName -t -c -l -e -n --network  No device found with udid %s, is it plugged in?
        No device found, is it plugged in? idevicefuse  ERROR: Could not connect to lockdownd, error code %d
 com.apple.mobile.house_arrest     ERROR: Could not Start HouseArrestService, error code %d
       HouseArrest Client Not Running!
 HouseArrest Client Running VendDocuments BundleID Not Accessable!
 Error BundleID Not Accessable! AFC Client Not Running!
 , /Documents/ File Truncate Success ERROR: Could not Truncate AFC File, error code %d
      ERROR: Could not Open AFC File, error code %d
 File Read Fail <FileContent> %s 
</FileContent> . .. BundleID Untrusted Fail To Read Files Success   [�����������I�����������    " @                            � @   � @   ܠ @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  x���,���ķ��L���\���l���<���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
       ��� ��� ��� ��� ������� �����������[���        \ .             runtime error %d
               @P @           PP @           �F @              @           �t @           �t @           �i @           �P @           �� @           � @           ؠ @           Ԡ @           Р @            � @           0� @           �� @           �� @            � @           � @           � @           (� @           � @           0P @            � @           P) @           �" @           �� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P    t�     ;  |�  @  �  ��  �  :  ��  @  M   ��   !  :!  ܐ  @!  �!  �  �!  �!  �  �!  �!  ��  �!  "  ��  "  �"   �  �"  �"  �  �"  �#  �  �#  �#  (�  �#  )$  ,�  0$  �%  8�  �%  �(  D�   )  >)  \�  @)  L)  d�  P)  
+  h�  +  �+  p�  �+  �+  ��  �+  q,  ��  �,  r-  ��  �-  �-  ��  �-   .  ��   .  �.  ��  �.   /  ��   /  W/  ��  `/  �/  ��  �/  0  ��   0  �0  đ  �0  v1  ȑ  �1  �3  ̑  �3  �4  ��  �4  $5  �  05  G5  ��  P5  n5   �  p5  �5  �  �5  �5  �   6  26  �  @6  C6  $�  P6  �6  (�  �6  *7  8�  07  N7  H�  P7  e7  P�  p7  �7  T�  �7  �7  \�  �7  8  d�   8  V8  l�  �:  lF  Đ  pF  uF  t�                                                                                                                                                                                                                                                                                                          B   b  
 
20`pP�	 B  �9     �  �  P)  �  	 B  �9     �    P)     B         B  � 0`
p	����P  
 
20`pP� 20`p
 
r0`pP�
e�0`
p	����P B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   2
0	`pP��� �0`pP   �0 B   b   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                                                                                                                                                                                                             �          p�  h�  ȱ          ��  �  0�          Ծ  ��  H�           �  ��  `�          ,�  ��  p�          h�  ��  ��          ��  �  ��          ��   �  Ȳ          (�  �  p�          ��  ��   �          ��  P�  0�          ��  ��  X�          �  ��                      ��      �      ��      �       �      0�      D�      X�      p�      ��      ��      ��      ܸ      ��      �      $�      @�      \�      t�      ��      ��              ܹ      �      �      �      .�      J�      ^�      v�      ��      ��      ��      ��              ̺      Ժ              ܺ      �              ��              �      �       �      (�      2�              <�              P�      h�              r�      ��      ��      ��      ��      ��      ʻ      �      ��      �      �      �      :�      Z�      f�      v�      ��      ��      ��      ��              ��      м      �      �      �      $�      >�      H�      P�      Z�      d�      l�      t�      |�      ��      ��      ��              ��      ��      ��      ��      Ƚ              ҽ      �      �      ��              �              ��      �      ��      �       �      0�      D�      X�      p�      ��      ��      ��      ܸ      ��      �      $�      @�      \�      t�      ��      ��              ܹ      �      �      �      .�      J�      ^�      v�      ��      ��      ��      ��              ̺      Ժ              ܺ      �              ��              �      �       �      (�      2�              <�              P�      h�              r�      ��      ��      ��      ��      ��      ʻ      �      ��      �      �      �      :�      Z�      f�      v�      ��      ��      ��      ��              ��      м      �      �      �      $�      >�      H�      P�      Z�      d�      l�      t�      |�      ��      ��      ��              ��      ��      ��      ��      Ƚ              ҽ      �      �      ��              �               afc_client_new_from_house_arrest_client    afc_dictionary_free    afc_file_close    	 afc_file_open 
 afc_file_read  afc_file_write     afc_get_file_info  afc_read_directory     afc_remove_path    afc_remove_path_and_contents  Q house_arrest_client_free  R house_arrest_client_new   T house_arrest_get_result   U house_arrest_send_command f idevice_free  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_service_descriptor_free � lockdownd_start_service   $ AreFileApisANSI DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �IsDBCSLeadByteEx  �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  W atoi  Y atoll  __p__environ   __p__wenviron  _mkdir   _set_new_mode  calloc   free   malloc   realloc 
 __setusermatherr   __C_specific_handler  xmemcpy   __p___argc   __p___argv   __p___wargv  _assert  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit # _errno  % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal  h strerror   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsprintf  _close  ] _open � _write  � fflush  � fgetc � fopen � fseek � ftell � fwrite  � putchar � puts  � strcmp  � strcpy  � strlen  � strncmp � strtok  	 __daylight   __timezone   __tzname  < _tzset   plist_dict_get_item    �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  (�  api-ms-win-crt-convert-l1-1-0.dll   <�  <�  api-ms-win-crt-environment-l1-1-0.dll   P�  api-ms-win-crt-filesystem-l1-1-0.dll    d�  d�  d�  d�  d�  api-ms-win-crt-heap-l1-1-0.dll  x�  api-ms-win-crt-math-l1-1-0.dll  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  api-ms-win-crt-string-l1-1-0.dll    ܰ  ܰ  ܰ  ܰ  api-ms-win-crt-time-l1-1-0.dll  �  libplist-2.0.dll                                                                                                                                                                                                                            0 @                    @                   " @   �! @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     ��   P  0   �`�p���������������ȠРؠ��� ��� � `  H   ����ȩЩة �� �0�@�P�`�p�����������Э�� �� �0�@�P�`�p������� �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   �      �: @   �                      ,    �b        ! @   �                           �h                           �n                       ,    �o       �! @                              
q                       ,    �q       �! @   �                           ^y                           �y                       ,    c{       �" @   �                       ,    �~       �# @                              0                       ,    �       �# @   =                      ,    
�        ) @   L                           �                       ,    ��       P) @   �                      ,    k�       + @   b                          >�                           ǵ                       ,    ��       �- @   �                      ,    i�       �1 @   d                          ��                       ,    U�       05 @                          ,    '�       P5 @                          ,    ��       p5 @   9                       ,    i�       �5 @   H                       ,    �        6 @   2                           ��                       ,    `�       @6 @                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   <   �  0GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  �          9  char {   size_t #,�   �   long long unsigned int �   ssize_t -#�   long long int short unsigned int int �   long int __time64_t {#�   time_t �
  unsigned int long unsigned int off64_t '�   off_t Q  _iobuf !
�  _Placeholder #�    1FILE /o  _ino_t +�   _dev_t 3,  _mode_t J�   mode_t M�  	{    �  unsigned char double float long double 	�   	�    $  signed char uint8_t $�  short int uint16_t &�   uint32_t (,  uint64_t *0�   _stat64 8S
Z  st_dev T�   st_ino U�  st_mode V�   st_nlink WM  st_uid XM  
st_gid YM  st_rdev Z�  st_size [�   st_atime \
   st_mtime ]
  (st_ctime ^
  0 �  plist_t Y�  �   	'7  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 	0o  �  	2 [  �  idevice_t 	3r  	O  2idevice_options ,  	9�  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  �   
$�	  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t 
P�  ;  
R)�	  ;  lockdownd_client_t 
S#
  	�	  lockdownd_service_descriptor 
`u
  port 
aZ   ssl_enabled 
b
=  identifier 
c�   lockdownd_service_descriptor_t 
e.�
  	
  �   &(
  AFC_E_SUCCESS  AFC_E_UNKNOWN_ERROR AFC_E_OP_HEADER_INVALID AFC_E_NO_RESOURCES AFC_E_READ_ERROR AFC_E_WRITE_ERROR AFC_E_UNKNOWN_PACKET_TYPE AFC_E_INVALID_ARG AFC_E_OBJECT_NOT_FOUND AFC_E_OBJECT_IS_DIR 	AFC_E_PERM_DENIED 
AFC_E_SERVICE_NOT_CONNECTED AFC_E_OP_TIMEOUT AFC_E_TOO_MUCH_DATA 
AFC_E_END_OF_DATA AFC_E_OP_NOT_SUPPORTED AFC_E_OBJECT_EXISTS AFC_E_OBJECT_BUSY AFC_E_NO_SPACE_LEFT AFC_E_OP_WOULD_BLOCK AFC_E_IO_ERROR AFC_E_OP_INTERRUPTED AFC_E_OP_IN_PROGRESS AFC_E_INTERNAL_ERROR AFC_E_MUX_ERROR AFC_E_NO_MEM AFC_E_NOT_ENOUGH_DATA  AFC_E_DIR_NOT_EMPTY !AFC_E_FORCE_SIGNED_TYPE  afc_error_t D�
  ,  G�
  AFC_FOPEN_RDONLY AFC_FOPEN_RW AFC_FOPEN_WRONLY AFC_FOPEN_WR AFC_FOPEN_APPEND AFC_FOPEN_RDAPPEND  afc_file_mode_t N<
  T  ]#�
  T  afc_client_t ^�
  	�
  �   '�  HOUSE_ARREST_E_SUCCESS  HOUSE_ARREST_E_INVALID_ARG HOUSE_ARREST_E_PLIST_ERROR ~HOUSE_ARREST_E_CONN_FAILED }HOUSE_ARREST_E_INVALID_MODE |HOUSE_ARREST_E_UNKNOWN_ERROR �~ house_arrest_error_t .�
    0,�    house_arrest_client_t 1&  	�  phone #`  	�� @   lockdown $�	  	�� @   lockdownservice % u
  	�� @   house_arrest &�  	x� @   afc '�
  	p� @   REININT_LIMIT )�   	l� @   res +�   	P @   OP ,�   	P @   i -�   	h� @   udid .
$  	`� @   FilePaths 0�  	X� @   SandBoxFile 2�  	P� @   LocalFilePath 3�  	H� @   BundleID 4�  	@� @   lockdownResult 8�	  	8� @   houseArrestResult 9�  	4� @   3�  :
(
  	0� @   lookup_opts ;w  	 P @   ,  >f  OP_CheckHouseArrestClient  OP_PUSHFILE OP_COPYFILE OP_LISTFILES OP_READFILE OP_TRUNCATEFILE OP_EMPTYDIR  
close 
J�     �    
write 
Z�   �  �   �  ,   	�  4
open 
T�   �  $  �    
atoll �(�   �  $   memset 5�    �  �   �    
afc_dictionary_free r
(
  )  )   	�  5free B  �   
snprintf G�   i  �  �   $   strerror R�  �  �    6_errno �  _mkdir "�   �  $   7_assert .�  $  $  ,   
lockdownd_service_descriptor_free 
>�	    u
   house_arrest_client_free _�  ,  �   
afc_remove_path_and_contents Z
(
  a  �
  $   afc_read_directory �
(
  �  �
  $  �   	)  afc_file_read �
(
  �  �
  |  �  k  �   	k  
atoi ��   �  $   afc_get_file_info �
(
    �
  $  �   strtok a�  2  �  )   afc_client_new_from_house_arrest_client �
(
  q  �  q   	�
  
plist_dict_get_item �
_  �  _  $   house_arrest_get_result ��  �  �  �   	_  house_arrest_send_command ��    �  $  $   house_arrest_client_new @�  @  `  u
  @   	�  lockdownd_client_free 
��	  m  �	   lockdownd_start_service 
��	  �  �	  $  �   	u
  idevice_free 	�7  �  `   lockdownd_client_new_with_handshake 
��	    `    $   	�	  idevice_new_with_options 	�7  ?  ?  $  w   	`  8idevice_set_debug_level 	lj  �    strcmp ?�   �  $  $   sleep .,  �  ,   afc_file_close �
(
  �  �
  |   
fflush s�   �  �   	�   �  
printf ��     $   afc_file_write �
(
  :  �
  |  $  k  �   afc_file_open �
(
  i  �
  $  �
  i   	|  
afc_remove_path 
(
  �  �
  $   basename !�  �  �   
fgetc t�   �  �   
ftell ��   �  �   
fseek ��     �  �   �    
fprintf ��   '  �  )   __acrt_iob_func ]�  I  ,   
fopen �  g  )  )   strcat >�  �  �  $   strcpy =�  �  �  $   
malloc �  �  �    strlen @�   �  $   9main ��   �: @   �      ��%  !argc ��   �  �  !argv �)  l  V  dict �
_  ��
node �
_  �  �  "�B @   N       �  &g  �|  U:�  ��  �  �  �B @     �  Qt  C @   F2  �  Rs Qu  C @     �  Ru Qt  #C @   �  Qu   y  +  fileinfo %)  ��
fileSize &k      
mode F�
  T  P  &g  G|  ���  �  
isSize 1�   q  m  +B @   j  �  Rs Qt  �D @   �  Rs   "%E @   �       ,  bytes_read Lk  ��bufferSize M�   ;%E @   �       
i N�   �  �  �  
remainder Q�   �  �  
currBufferSize R�       
currBuffer S�%  �  �  LE @   z;  r  R	.i @    [E @   �  �  R	<i @   Q}  gE @   z;  �  R	?i @    �E @   �  �  X} Y$ @B$t "@B$ @B$t " $@2$,( w ~  �E @   z;    R	i @    �E @   �     �A @   N8  D  Ru  B @   �  \  X�` �B @   �  �D @   N8  �  Ru  E @   :  �  X1Y�h F @   '  �  R2 $F @     �  Q	�h @   Xs  4F @   �  DF @   '  �  R2 [F @       Q	�h @    gF @   �   �  �  dirs t)  ��oB @   a  j  Q	�h @   X�h �B @   z;  �  R	gi @    �B @   z;  �  Rt  �B @   j  �  Rt Q|  �B @   j  �  Rt Qu  F @   z;  R	Ti @     <�   �  �  �: @   �;  3; @   D  *  R1 n; @   
  I  R	�� @    �; @   �  h  R	�f @    �; @   j  �  Rt Q~  �; @   j  �  Rt Q	�f @    �; @   z;  �  R	�f @    +< @   j  �  Rt Q	�f @    B< @   j    Rt Q	�f @    �< @   j  9  Rt Q	�f @    = @   z;  X  R	�f @    9= @   j  }  Rt Q	�f @    P= @   j  �  Rt Q	�f @    N> @   �  �  Q	�� @   X	Sg @    x> @   m  �  Q	�g @   X	�� @    �> @   E  �> @     &   X	x� @    �> @   �  >   Qu  ? @   �  U   R5 ? @   '  l   R2 %? @   �;  �   Rs Q1XI ;? @   �  �   Qu  _? @   �  �   Q�X u? @   v  �   Q	\h @    �? @   z;  �   R	bh @    �? @   �  �? @   j  .!  Rt Q	�f @    �? @   �8  �? @   j  `!  Rt Q	�f @    F@ @   '  w!  R2 X@ @     �!  Q	`g @   Xs  d@ @   �  u@ @   z;  �!  R	0g @    �@ @   z;  �!  R	�f @    �@ @   '  �!  R2 �@ @     #"  Q	�g @   Xs  �@ @   �  �@ @   z;  O"  R	�f @    �@ @   j  t"  Rt Q	�f @    �@ @   j  �"  Rt Q	�f @    A @   z;  �"  R	h @    A @   �  6A @   2  �"  Q	p� @    uA @   N8  #  R	�h @    �A @   n  �A @   z;  /#  R	�h @    �A @     �A @   �  @C @   ,  h#  Q	�h @    aC @   z;  �#  R	zi @    �C @   �%  �C @   '  �#  R2 �C @   �;  �#  R	�g @   Q1X  �C @   �  �C @   �  �#  R5 �C @   '  $  R2 �C @   �;  2$  Rs Q1XI �C @   �  J$  Qu  D @   �  a$  R5 D @   '  x$  R2 -D @   �;  �$  Rs Q1XI CD @   �  �$  Qu  XD @   '  �$  R2 nD @   �;  �$  Rs Q1XI zD @   �  �D @   '  %  R2 �D @   �;  8%  R	{h @   Q1XH �D @   �  �D @   '  \%  R2 �D @     �%  Q	�h @   Xs  �D @   �   #{   �%  '�   �   =copy_it �
  @         ��0  !afc �"�
  �  x  (n  �3$  �  �  (w  �$  V  @  
afcrc �(
  �  �  stbuf ��  ��~dirlist �)  ��~>cleanup � @   �   >)  
tmp_src ��  P  H  
tmp_dst ��    w  "I @         [(  
i ��   �  �  �   
dl �$  �  �  
dl_len ��       
tmp_src_len ��       
tmp_dst_len ��   ?  9  � @   �  1'  Rs  � @   �  I'  R|  � @   �  c'  R�� � @   B  �'  Ru Q��X	e @   Y| w s  � @   �  �'  R~  � @   �  �'  R��  @   B  	(  Rt Q��X	e @   Y~ w s  + @   �%  -(  R} Qt Xu  8 @   .  E(  Ru  E @   .  Rt    � @   a  �(  R} Q| X��  @   �  �(  R	�d @   Q|  A @   �  �(  R~  � @   �  �(  R	hd @   Q|   @   �   @   �   @   �   @   i  . @   �  R	�d @   Q~ Yt   $�1  n @    h   �
�+  �1  q  a  �1  �  �  �1  #	  	  h   �1  �	  i	  �1  .
  "
  %2  ��~� @   �  �)  R} Q| X�� � @   j  �)  Rs Q	�c @    � @   j  *  Rs Q	�c @    
 @   j  <*  Rs Q	�c @    9 @   j  Z*  Rs Q  F @   �  � @     7 @   j  �*  Rs Q	�c @    N @   j  �*  Rs Q	�c @    _ @   �  � @   j  �*  Rs Q	�c @    � @   j  +  Rs Q	dc @    � @     -+  Ru  � @   �  � @     R+  Ru  � @   j  w+  Rs Q	�c @    � @   j  �+  Rs Q	lc @    � @   j  �+  Rs Q	tc @    � @   j  Rs Q	|c @      $2  � @     �   �	,  -2  k
  c
  �   92  �
  �
    $�0  l @    �   �	V/  �0  �
  �
  �0      �0  D  :  �0  z  p  �   �0  �  �  %�0  ��~�0    �  1  Q  C  1  �  �  ? 1  �:  �  �  @,1  91  2
  (
  Q1  n
  h
  )]1    �-  %^1  ��~)o1  .  �-  p1  �
  �
  �1  �
  �
  ^ @     I-  R���Qt s "Xu s  o @   �  s @   �  z @   i  � @   �  R	�e @   Q~ Yt   " @   �  �-  R} Xt Y
  � @   �  R	�e @   Q|   � @   :  
.  R} Q| X1Y�� � @   �  0.  R~ Q
X
� � @   �  U.  R	ze @   Qu  � @   �  z.  R	�e @   Qu  - @   z;  �.  R	�e @    b @   �  �.  R	8e @   Q|  y @   f  �.  R	� @ @   �  D @   �  K @   i  ` @   �  (/  R	Xe @   Q~ Yt  u @   �  @/  R}  � @   �  R}    A�   �
  �
  � @   �  �/  R	�e @   Q|   @   �  �/  R	0d @   Q|   @     l @   z;  �/  R	%e @    � @   �  �/  R	�c @   Q|   @   �  10  R	�c @   Q	�c @   X
�   @   �  d0  R	�c @   Q	�c @   X
� ; @   �  R	�c @   Q	�c @   X
�  B_copy_file A
�1  �1  *afc A%�
  +n  A6$  +w  B$  *stbuf B.�1  ok H
�1  afd I|  fd J	�   afcrc K(
  BUF_SIZE L�   buf M
�1  Ccleanup �total_remaining `a  wrc d
�   ,cnt_read hk  ,cnt_to_write la  cnt_written ma     _Bool 	Z  #{   �1  D�    -_afc_stat �(
  2  afc �+�
  path �<$  stbuf �2  i �	�   ret �(
  info �)   	�  -mode_string �$  F2  mode �'�  name �$   EPushFileToDevice �@ @   
      ��6  .�  ��    
  .g  �3|  O  M  FileName ��  b  \  Fmode ��
  G�  �(
  �  ~  S  �4  content ��  �  �  f  �4  length �k  �  �  Hbytes_written �k  �L/N8  + @    + @   *       �+�3  l8  �  �  b8  �  �  v8      3 @   �  �3  Rs  < @   �  U @   �;  RtQs   
 @   �  �3  Rt  + @     �3  Xt w �L m @   :  4  Qt X1Y|  � @   �  44  R	0f @   Qs  � @   '  K4  R1 � @   �    @   �  }4  R	Af @   Qs  +  @   �  6  @   '  �4  R1 >  @   �  H  @   �  R1  � @   �6  Ru   IN8  l @    @  �'^5  l8  $     b8  :  8  @  v8  T  P  q @   �  55  Rs  z @   �  � @   �;  RtQs    /N8  � @    � @   *       �#�5  l8  j  h  b8  y  w  v8  �  �  � @   �  �5  Rs  � @   �  � @   �;  RtQs   f @   �  6  Ru  � @   n  6  Qt  � @   :  =6  Qt X3Y|  � @   �  � @   '  a6  R2 � @     �6  Q	`f @   Xs Yt  � @   �  � @   '  �6  R1   @   �   JreadFile g�  � @   �       �N8  KfileName g�  �  �  file j�  �  �  code r�  �  �  n s�       c t	�   $     f_size x
�   <  8  � @   I  �7  Rs Q	f @    � @   �  �7  Rt Q0X2 � @   �  �7  Rt  � @   �  �7  Rt Q0X0 � @   �  �7  Rs  $ &  @   �  8  Rt  & @   '  ,8  R2 8 @     Q	f @   Xs   Lconcat ^�  �8  s1 ^$  s2 ^*$  result `�   Mprint_usage I
P @   �       ��:  ` @   z;  �8  R	 ` @    l @   z;  �8  R	 ` @    v @   �;   9  R: � @   z;  9  R	H` @    � @   z;  >9  R	x` @    � @   z;  ]9  R	�` @    � @   z;  |9  R	(a @    � @   z;  �9  R	xa @    � @   z;  �9  R	�a @    � @   z;  �9  R	b @    � @   z;  �9  R	Xb @    � @   z;  :  R	�b @    � @   z;  6:  R	�b @    � @   z;  U:  R	c @     @   z;  t:  R	6c @    N @   z;  R	Ec @     #{   �:  '�   V/   ON8  @ @   \       �z;  b8  V  P  l8  x  r  v8  �  �  U @   �  �:  R|  ` @   �  ;  Ru  m @   �  2;  Rs v "# ~ @   �;  V;  Rt Q| Xs  � @   �;  Rt s "Qu Xv  puts __builtin_puts P__main __main fwrite __builtin_fwrite strcpy __builtin_strcpy putchar __builtin_putchar memcpy __builtin_memcpy  ]   l
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   ! @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	�� @   atexit ��   �  Y   __main 5�! @          ��  �! @   �   	__do_global_ctors  @! @   j       �  
nptrs "�   �  �  
i #�   �  �  �! @   j  R	 ! @     	__do_global_dtors  ! @   :       �[  p [  	P @    	   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �    char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
�  �   �,  __uninitialized  __initializing __initialized  �  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	$P @   �  	 P @   =  
"	�� @   [  	�� @    �    a  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  g  _dowildcard  �   	0P @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 Q  9  �! @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   �! @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �  _newmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  t  �! @   �       1  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	ܠ @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	�i @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�i @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	ؠ @   __mingw_initltsdyn_force ��   	Ԡ @   __mingw_initltssuo_force ��   	Р @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  �! @   /       �}  
�  �  �  �  
�  *M  
    
�  ;d      " @   �   __tlregdtor m�   �" @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  " @   �       ��  2  *  �  Z  R  �  �  z  �  �  �  @" @    @" @   +       L�  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �" @   �    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 z  b  G  _commode �   	� @   int  w     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 	  3	  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   m  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  �" @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   �" @   �       �0  pexcept 0      type 
�  3  '  
# @   b  �  R2 6# @   7  Q	�j @   Xs Yt w �ww(�ww0�w  5   �    r  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 u
  ]
  �# @          y  _fpreset 	�# @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �
  �  __mingw_app_type �   	� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �# @   =        __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	� @   	�  maxSections �%  	� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator ��% @   ]      ��  4was_init �%  	 � @   5mSecs �%  �  �  !�  %& @   �  �4  �  �  �  6�  
�  �  �  
�    �  
�  0    
�  �  �  

  �  �  
      "E    �  
F  _  W  
[  �  �  9' @   `  R	Hl @   Xu w t     �& @   �& @          �;  �  �  �  �  �  �  �  �  �    �& @   �& @          �  �  �  �  �  �  �  �  �  �& @   �  Ru    !  �' @   !  ��  �  	    �      �  #  !  7  �' @   !  �  -  +  �  8  6  �  G  E  �' @   �  Ru      G( @   G( @   
       �w  �  Q  O  �  \  Z  �  k  i    G( @   G( @   
       �  u  s  �  �  ~  �  �  �  O( @   �  Ru      `( @   `( @          �   �  �  �  �  �  �  �  �  �    `( @   `( @          �  �  �  �  �  �  �  �  �  h( @   �  Ru    "$  ,  �  
)  �  �  83  7  
4  �  �    �( @   �( @   
       s�  	    �      �  #  !    �( @   �( @   
       �  -  +  �  8  6  �  G  E  �( @   �  Rt      
�( @   `    R	l @    �( @   `  R	�k @      9�  �' @   X       �|  
�  S  O  :�  ���' @   
  Yu   �% @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �0$ @   b      �`  &addr ��  o  c  b �:  ��h �g  �  �  i �%  �  �  >% @   P       �  new_protect �
u  �  �  
D% @   
  �  Ys  N% @    
  \% @   `  R	�k @     
�$ @   �
  �  Rs  �$ @   n
  
�$ @   E
    Q��X0 
�% @   `  >  R	�k @    �% @   `  R	`k @   Qs   ?__report_error T�# @   i       �/  &msg T    �  @argp ��   �X
�# @     �  R2 
$ @   /  �  R	@k @   Q1XK 
$ @       R2 
#$ @   �
  !  Qs Xt  )$ @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 
  �   ) @   L       �   double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	� @   
__setusermatherr ��  �   __mingw_setusermatherr �@) @          �P  f ,�      L) @   �  R�R  __mingw_raise_matherr � ) @   >       �typ !�   0  *  name 2�  H  D  a1 ?w   Z  V  a2 Jw   n  j  rslt 
w   � ex 0  �@9) @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  9!  _fmode �   	 � @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 z  b  P) @   �      s!  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	0� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   P) @   �      ��  'exception_data �-�  �  �  old_handler �
	  �  �  action ��   .    reset_fpu ��   �  �  
�) @   �
  �  R8Q0 (�) @   �  R�R 
* @   �
  �  R4Q0 * @   �  R4 
l* @   �
    R8Q0 
�* @   �
  7  R8Q1 
�* @   �
  S  R;Q0 �* @   f  R; �* @   y  R8 
�* @   �
  �  R;Q1 
�* @   �
  �  R4Q1 
+ @   �
  �  R8Q1 )+ @   �
   �	   �
   c  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  s  + @   b       #  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	`� @   __mingwthr_cs_init �   	H� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	@� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �, @   �       �n  	hDllHandle z�    �  	reason {<  �  h  	reserved |S  �  �   �, @   K       �  
keyp �&�  l  f  
t �-�  �  �  - @   �  
;- @   C  R	`� @     !n  �, @   �, @          �  �  �, @   )
   "n  �, @   N  �E  #N  �  U- @   )
    E- @   6  
m- @   e  R	`� @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �+ @   �       �d	  	key A(<  �  �  
prev_key C�  �  �  
cur_key D�  �  �   , @   �  B	  Rt  S, @   �  
\, @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �+ @   o       �$
  	key *%<     �  	dtor *1.  4  &  
new_key ,$
  t  l  �+ @   �  �	  R1QH �+ @   �  
  Rt  
�+ @   �  Rt   �  &n  + @   p       ��  �  �  '�  H+ @          �
  �  �  �  L+ @     Q+ @     (c+ @   Rt   *+ @   �  �
  R|  )�+ @   �  R	`� @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  x%  _CRT_MT �   	@P @   int  �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 L  4  �%  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	�� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	�� @    �   "  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �- @   �      �%  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<     e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<    i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  3  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  3  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
      � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �0 @   �       �7
  i �(�   �  �  (  �	`    �	  �  �  importDesc �@  �  �  �  �^
  importsStartRVA �	I  �  �  �  �0 @   		  ��  �  	  �  �  �  	�  �0 @      �    �  �  8  4  �  I  G      M  �0 @   �0 @   J       �q  U  S  f  }  a  ]  �  �  }  �  �  �    
_IsNonwritableInCurrentImage �   0 @   �       ��  pTarget �%`  �  �  (  �	`  rvaTarget �
�  �  �  �  �^
  �  �  �   0 @   �  �/  �  �  �  �  �  	�  00 @    �  �  �  �  �  �  �  �  �  �      M  T0 @   T0 @   I       �q      f  }      �      �  $  "    
_GetPEImageBase �`  �/ @   6       �0  (  �	`  	�  �/ @   �  �	�  �  �  �  �  	�  �/ @    �  �  �  �  �  1  -  �  B  @       
_FindPESectionExec y^
  `/ @   s       �%  eNo y�   P  L  (  {	`    |	  a  _  �  }^
  k  i  B  ~�   u  s  	�  `/ @   �  �	�  �  �  �  �  	�  q/ @    �  �  �  �  �  �  ~  �  �  �       
__mingw_GetSectionCount g�    / @   7       ��  (  i	`    j	  �  �  	�   / @   �  m	�  �  �  �  �  	�  0/ @    �  �  �  �  �  �  �  �  �  �       
__mingw_GetSectionForAddress Y^
  �. @   �       �  p Y&s  �  �  (  [	`  rva \
�  �  �  �  �. @   w  _�  �  w  �  �  �  	�  �. @    �  �  �  �  �  �  �  �          	M  �. @   �  c
q      f  �  }  (  $  �  F  D  �  P  N     
_FindPESectionByName :^
   . @   �       �M  pName :#�  c  Y  (  <	`    =	  �  �  �  >^
  �  �  B  ?�   �  �  �  . @   l  F  �  l  �  �  �  �  %. @    %. @          �  �  �  �  �  �  �  �     &. @   �  -  Rt  'z. @   z  Rs Qt X8  _FindPESection $^
  �  (  $`  (rva $-�    &	  �  '^
  B  (�    _ValidateImageBase   �  (  `  pDOSHeader �    	  pOptHeader v   )�  �- @   ,       �~  �  �  �  �  �  �  �  �  	�  �- @    e  �  �  �  e  �  �        �           *M  �- @   P       �f        +q  Q}  2   .   �  Q   O   �  [   W     J   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �1 @   d      7+  char w   size_t #,�   long long unsigned int long long int short unsigned int int long int w   unsigned int long unsigned int unsigned char double float long double WINBOOL 
�   BYTE �  DWORD ��   v  UINT ��   signed char short int _Float16 __bf16 path_info (@,  prefix_end C�    base_sep_begin G�   base_sep_end H�   term_sep_begin L�   path_end O�     IsDBCSLeadByteEx �F  U  w  V   AreFileApisANSI �	F  memcpy 2D  �  D  q  �    realloc D  �  D  �    
basename �   �4 @   t       �)  path ��   �   �   	info ��  �@upath ��   �   �   
�4 @     R�@Qs   
dirname �   �3 @          �  path ��   �   �   	info ��  ��upath ��   .!  $!  top �  T!  R!  	static_path_copy ��   	С @   �3 @     �  R��Qs  v4 @   �  �  Qu 
�4 @   2  Rt Qs Xu      do_get_path_info V�1 @   �      �-  info V$-  d!  \!  path V0�   �!  �!  pos X�   �!  �!  unc_ncoms Y	�   �"  x"  cp Zc  �"  �"  dbcs_tb [	�   �"  �"  prev_dir_sep [�   K#  3#  dir_sep [ �   �#  �#  �1 @   U  q2 @   ,    Rv  
3 @   ,  Rv   �  memcpy __builtin_memcpy 	  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �.  _MINGW_INSTALL_DEBUG_MATHERR �   	PP @   int  �   >  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  05 @          )/  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char short int DWORD ��   float signed char double long double _Float16 __bf16 Sleep      sleep .�   05 @          �seconds "�   
$  $  @5 @   k  	R�R
�   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 v  ^  P5 @          �/  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	`P @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  P5 @          �_File *�  *$  $$  _Format J�  C$  =$  _ArgList Z�   \$  V$  i5 @   �  R0Q�RX�QY0w �X   �   !  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ^  F  p5 @   9       0  __gnuc_va_list �   __builtin_va_list �   char 	�   va_list w   size_t #,�   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(B  G  
threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   j  o  �   y   �   j  �   t  __imp_snprintf �  	pP @   P  __stdio_common_vsprintf �  �  �   j  �   t  $  �    snprintf G  p5 @   9       �__stream +o  �$  |$  __n <�   �$  �$  __format [y  �$  �$  ap 
�   �hret   �$  �$  �5 @   �  R2Q�RX�QY�Xw 0w(�   �   :"  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 F  .  �5 @   H       �0  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�P @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �5 @   H       �_Format .�  �$  �$  
ap 
�   �Xret 	  �$  �$  �5 @   �  �  R1 �5 @   �  R0Xs Y0w t    �   �#  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (     6 @   2       +1  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�P @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	   6 @   2       �_File )�  %  %  _Format I�  '%  !%  
ap 
�   �hret 	  <%  :%  -6 @   �  R0Q�RX�QY0w �   �   %  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 
  �  �1  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	� @   local__winitenv 
`  	� @   '  
	�P @     
	�P @    �   �%  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 *    @6 @         2  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	 Q @   �      �   __imp_at_quick_exit g)  	Q @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	Q @   
initial_tzname1 }
V  	Q @   
initial_tznames ~�  	 Q @   
initial_timezone 
%  	�P @   
initial_daylight �  	�P @   __imp_tzname ��  	�P @   __imp_timezone ��  	�P @   __imp_daylight ��  	�P @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�P @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�P @   �	  __imp__amsg_exit �V  	�P @   F  __imp__get_output_format �\
  	�P @   -
  __imp_tzset ��  	�P @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�P @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �7 @   5       ��
  file �!�
  T%  N%  fmt �6�  s%  m%  
ap ��   �h+ret �  �%  �%  �7 @   �
  R4Q�RX�QY0w �  ,tzset "�7 @   6       �  -  �7 @   �7 @   -       ��7 @   +  �7 @     
8 @       ._tzset �/_get_output_format nF  @6 @          �0_amsg_exit ip7 @   .       ��  ret i  �%  �%  �7 @   z  �  R2 �7 @   Q  �  Q	�l @   Xs  �7 @   <  R�  at_quick_exit �  P7 @          �   func ]*�  �%  �%  1e7 @   �   _onexit ��  07 @          �p  func V%�  �%  �%  =7 @   �  Rs   __wgetmainargs J  �6 @   j       �[  _Argc J"�  �%  �%  _Argv J5I  &  �%  _Env JGI  "&  &   K  JQ  A&  ;&  !W  Jl�	  � �6 @   0  �6 @   	  &  R	v  $0.# �6 @   �  �6 @   �  	7 @   �  7 @   U   __getmainargs >  P6 @   j       �E  _Argc >!�  `&  Z&  _Argv >1S  y&  s&  _Env >@S  �&  �&   K  >J  �&  �&  !W  >e�	  � p6 @   �  �6 @   �    R	v  $0.# �6 @   �  �6 @   �  �6 @   u  �6 @   U   2   8 @   6       �28 @   +  >8 @     J8 @                                                                                                                                                                                                                                                                                                  
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}   I  ( 
  (   H }  .?:;9'I<   :;9I  	 !I  
.?:;9'I<  4 :!;9I?  H}  
4 :!;9I�B   1�B  $ >  
 :;9I8  4 1�B  4 :!;9I  4 :!;9I�B  >!I:;9  4 :!;9I  U  U   :!;9I  & I  4 :!;9I  . ?<n:!;!    :;9I   <     :;9   7 I  ! :!;9I�B  "  #I  $1R�BUX!YW  %4 1  &4 :!;9!I  '! I/  ( :!;9I�B  )1U  * :!;9I  + :!;9I  ,  -.:!;9!'I !  . :!;9I�B  /1R�BX!YW  0%U  1   2>I:;9  34 :;9I?  4&   5.?:;9'<  6. ?:;9'I<  7.?:;9'�<  8.?:;9'<  9.?:;9'I@z  :4 :;9I�B  ;  <4 I4�B  =.:;9'@z  >
 :;9  ?4 1I�B  @
 1  A4 4I�B  B.:;9'I   C
 :;9  D! I  E.?:;9'@z  F4 :;9I  G4 :;9I�B  H4 :;9I  I1R�BUXYW  J.?:;9'I@z  K :;9I�B  L.?:;9'I   M.:;9'@z  NH}�  O.1@z  P. ?<n   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   $ >  I ~  4 :!;9I�B   I   :;9I  
 :!;9!I8   !I   :!;9I�B  	4 :!;9I  
H}  H}  .?:;9'I<  
.?:!;9!!'I@z  %  & I     &   :;9  . ?:;9'I<  .?:;9'I<  .:;9'@z  H }  . ?<n:;   %  4 :;9I?  $ >   $ >  %   :;9I  .?:;9'<   I  .?:;9'I@z   :;9I�B  H}  	I ~    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!     7 I  
%   I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                                                                                                                                                                                   5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg �    �   �
        T  m  �  �  �   �   �  �  �    
    #  +  >  I  O  ^  c  l  u  ~  �  �  �    	P @   � K��������������	�u	I��������~t���~:��k�<�& � J �  <� �N �� .�$  v�   X J
Y �K"  �    H X J2X� �J�� 
1�	��}J�	 ���~�	J	�Y� 	K
�	�  J	Yg�..�~� �� ��,# �p* �L ��$	%X�� 
	�	�
�� 	3�~�$�CrtX.	��
�~
vXK�

[
�
?
c[
Y�
.=�K�KY�9	�	u�	Y
�
u.V	�!.y<�  =Y*t-"/- t� <Z
	J�J	� �J��#! �	.fY� 
1	�}J
�	� �9�J9E�f	1
 � �   J  f f � f&     J < J
�& X" �
��;
=
Y9 L<
�W
=Y
�9 L<t
Y�
=��Y�
Y��	�!0	J	��t� �L ��$X� � $���	6& S�# �
�"
-/
- t� <Z	*
VJX	�	�/	- t� <Z(	J	��
�	U�X� �K$� ��$�
� !X  f t t e f t t e f t t�}��g& X<& < X& X <Z d>��� <[vr>X=_���/Y�<	Ye �\
>�	j.Y	- f� <Y��W&� 
�yX& UX+<>M&  X�;= G>uY -�L&  ��;= G>uY .�	��	>
�!
�
��&  ��;= G>uY ?
v�� � X"X	w�r	v t� <Y �o�! �Y  	�: @   �J� �( �	t<J� t
g� y f ��X�	 �g	�
t
��( �~�$ ��- �) �
LW
� XZ��.
�|<
uZ �� �
K
�X �
�
uW
� �
^
u
� ^X%� �
K
�X �
�
uW
� Xn��
pX j<f
u*X �� �
�
�X �
�
u!Y�
uX �
�
u
�[X
�X �
�
uW
� �
�
u
�kX
u
�� X1/ �1f .�1/ �1f .��86 � f��9J6 � f	M YsY �� <Z=: X f�
/
IK� �gY	����~X �
KW
� �
�
u
�XYlX �
�W
� �
�
u
�	*X �� <Y�
wX�~�	�X �� <Y��~X�� X �- J) �
�
u �~
�f	<X��:X'% . f	�
�0 .0t < X
g /�
	��tZZtYY
�~�/3#{tt
K/ �/� < X
g /�#I�#��
y�t
� s/�
K2 to X
gJ�
�_�X.<JT�.hH X2 J/ �Pf
�~"
�0� � �= SXX
� .
g/�

��~X�	�X �  <Y�
X� t� <ZA> X f�� t� <ZEB X f� t� <Y�	X �  <Y��X �� <Y��X$�/
, �, < X
g /# �. .. t; <X0gA otJY�A m�# �OuU>O=u<!�!>VZ!� dhh��   �-_ �� <
[TJ ��V <Y� #    K   �
      �    ,  G  Q  [  c  p  y  �   	 ! @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �    0  [  f  n  {  �  �  6     .   �
      �  �  #  .  R     .   �
      �  �  �  �   	�! @    6     .   �
        7  ^  i      K   �
      �  �  �      (  2  >  H  P   	�! @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �  �  �  �  6     .   �
      K	  c	  �	  �	  �     <   �
      �	  �	  %
  @
  G
  N
  U
   	�" @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �
  �
  �
  �
   	�# @   		3 6     .   �
      K  c  �  �  |    s   �
      �    7  R  a  p  y  �  �  �  �  �  �  �  �  �  �  �    	�# @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	�% @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      F
  ^
  �
  �
  �
  �
   	 ) @   
L�Z
�KTYZ
g=yuX 6     .   �
      
  %  L  W  �    U   �
      �  �  �  		    %  -  9  C  T  a  j   	P) @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �  �  �       *  4  @  J  R  _  j  s  �   	+ @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �     *  6     .   �
      �  �  �  �  G    K   �
      6  N  u  �  �  �  �  �  �  �   	�- @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< �    Z   �
        5  ]  
x  �  �  �  �  �  �  �  �  �    	�1 @   � �% y.h
i% q1O% x<M
% 
�
��ot
�1.	Y��	�kN<kJ.g�	L X	� A	<�NnJuJ<
.gJKX.  �� J�
zJMOyt;	n.�?	.
jJjJ	KL XM..	@</N

f<o<<��O�A�OCtJ< �X"��ytB X-X�V��YY/ ;�	# �  <	 X  <	 �xXY@	_^uKI/q7
<7t><
JX=[�uYIKW=jX�Y@ XYtXe.�Y\/ 8x! �]XY@X_�X 6     .   �
      +  C  k  {  q     A   �
      �  �    .  6  >  J  U   	05 @   Kfg n     A   �
      �  �  �      +  4  >   	P5 @   K
�<.Y �     A   �
      �  �  �  �        &   	p5 @   KU	\f�X�Y	Y �     A   �
      }  �  �  �  �  �  �     	�5 @   g?	YT�Y	 X� <uX �     A   �
      `  x  �  �  �  �  �  �   	 6 @   KU	\fp	\;Y	Y W     O   �
      A  Y  �  �  �  �  �  �  �  �    |    h   �
      c  {  �  �  �  �        &  /  7  D  M  X  j   	@6 @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	p7 @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                                                                                                                                    ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �         P  P @   �       D0� \   P    @         A�B�B �B(�B0�A8�A@�AH�	G�HP�
�A�A�B�B�B�B�A�HL   P  @ @   \       B�A�A �A(�A0�DPK0A�(A� A�A�B�    <   P  � @   �       A�A�A �D@n
 A�A�A�A   L   P  @ @   
      B�A�A �A(�A0�Dpc
0A�(A� A�A�B�E\   P  �: @   �      A�B�B �B(�B0�A8�A@�AH�	D�EP.
�A�A�B�B�B�B�A�B   ���� x �         (   ! @   :       D0u  4   (  @! @   j       A�A�D@@
A�A�H       (  �! @             ���� x �         �  �! @             ���� x �      $   �  �! @   /       D0R
JN    L   �  " @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       �  �" @             ���� x �      <   �  �" @   �       A�A�D�P�
���
���A�A�B    ���� x �         �  �# @             ���� x �      $     �# @   i       A�A�DP   <     0$ @   b      A�A�A �Dp�
 A�A�A�D   \     �% @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         �   ) @   >       D`y     �  @) @             ���� x �      4   @  P) @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  + @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  �+ @   o       A�A�A �D@U
 A�A�A�A    D   �  �+ @   �       A�A�D@R
A�A�FR
A�A�D      4   �  �, @   �       A�D0p
A�J�
A�A      ���� x �         �  �- @   ,          �  �- @   P       L   �   . @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  �. @   �          �   / @   7          �  `/ @   s          �  �/ @   6          �   0 @   �          �  �0 @   �          ���� x �      d   �  �1 @   �      B�B�B �A(�A0�A8�A@�D`�
@A�8A�0A�(A� B�B�B�H       D   �  �3 @          A�A�A �A(�D�[
(A� A�A�A�I  4   �  �4 @   t       A�D`X
A�BRA�         ���� x �         �	  05 @          D0R     ���� x �         
  P5 @          D@Y     ���� x �         P
  p5 @   9       DPt     ���� x �      ,   �
  �5 @   H       A�A�D`A�A�   ���� x �         �
   6 @   2       DPm     ���� x �           @6 @          L     P6 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L     �6 @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $     07 @          A�D0WA�      P7 @               p7 @   .       A�D0     �7 @   5       DPp       �7 @   6       D0q        8 @   6       D0q                                                                                                                                                                                                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion house_arrest_client_private lockdownd_client_private afc_client_private handle dst_path src_path FilePathitem afcResult idevice_private __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                                               C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> idevicefuse.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/sys C:/msys64/ucrt64/include/plist ../include/libimobiledevice idevicefuse.c idevicefuse.c corecrt.h _mingw_off_t.h stdio.h types.h stdint.h _mingw_stat64.h plist.h libimobiledevice.h lockdown.h afc.h house_arrest.h io.h stdlib.h string.h direct.h assert.h unistd.h libgen.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/dirname.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include dirname.c dirname.c corecrt.h minwindef.h winnls.h string.h stdlib.h libgen.h winbase.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/sleep.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include sleep.c sleep.c minwindef.h synchapi.h unistd.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_snprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_snprintf.c ucrt_snprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                               �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� �                              �: @    R�\���R���
\�
��R���\���R���\���R���\���R���\                       �: @    Q�U���Q���U���Q���U��	�Q��	�U���Q���U���Q�     u? @    P��P         �B @    P&S&*P*5S          �A @    c0���0���P��T��T��	0�    �D @    �1���1�     +B @    P��P       %E @    0�"_"E�E�_          6E @     @B$t "�=@B$t "�LO @B$t "�OVYV� @B$t "�         6E @    % @B$t "@B$ @B$t " $@2$,( �=%@B$t "@B$@B$t " $@2$,( �V�Y��% @B$t "@B$ @B$t " $@2$,( �     6E @    =} ��}           6E @    - @B$t "@B$ @B$t " $@2$,(  $ &1�=-@B$t "@B$@B$t " $@2$,(  $ &1�VYy  $ &1�Y�z���- @B$t "@B$ @B$t " $@2$,(  $ &1�                       @    aRa�]���R���]��R��]��R��]��R��]                       @    FQF�^���Q���^��Q��^��Q��^��Q��^                         @    QXQaQa�\���X���\��X��\��X��\��X��\                   _ @    �1���0���1�������1���0���P��1���1���0��	�	P��1���
1��
�
P��1�       I @    �0���P��U��0�       I @    �0���P��T��0�         I @    0�%x3%�%0x3%#�09p3%#�9�x3%�    c @    S�S   � @   `T       � @    p t "#�QG��       � @    p t "#�Qb��            n @    �V��V��V��V��V��V��V��V                n @    XQ�\��\��\��\��\��\��\��\              n @    R�]��]��]��]��]��]��]��]                        � @    0�ntx3%�nwtx3%#�w|th3%#�|�tx3%���tx3%#���th3%#���tx3%���tx3%���tx3%���0���tx3%���tx3%��
�tx3%�          n @    #1�#]P��P������P����        � @    1�����������
�
��     	   � @    10�11X��0���0��
�
0�          l @    �V��V��V��V��	V          l @    �\��\��\��\��	\          l @    �^��^��^��^��	^          l @    �]��]��]��]��	]            u @    �0���1���0���0���0���1���0�            } @    @	��@_P_�����	����P������	��             } @    "1�"<Po�0���P��0���P��P          } @    �
 ���
 ���
 ���
 ���
 �                 � @    �t ��w0��t ��t ��w0��t ��w0��t           � @    Q%U%E��~Y���~u �����~u �     � @     0�NWQu�Q   1 @    ^U��U      1 @    	0�	<S��S          } @    �
����
����
����
����
��             @ @    %R%�U���R���U���R���U   @ @   %Q       l @    P�S��S             � @    PT��P��P��T��P     � @    PPT   
 @    P + @   *S + @   *
�h @   �    I @    PT   l @    P'S l @   '
�h @   �    � @    PT � @   *S � @   *
�h @   �    � @    PT         � @    R@S@y�R�y�S         � @    P^T`lPl�T    � @    PU  � @   70�    � @    	PP     � @    PS       @ @    R[\[\�R�       @ @    QXUX\�Q�       v @    P!T!&P 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
`j @   ���
@j @   ���
�j @   ���
�j @   ���
�j @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ���Î     ��U  ��2�  ���Î     ��U  ��1�  ���Î     ��U  ��1�  ���Î     ��U  �	�	4�  �	�	�Î     �	�	U  �	�	4�  �	�	�Î     �	�	U  �	�	8�  �	�	�Î     �	�	U  �	�	8�  �	�	�Î     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
��     �
�
T  �
�
4�  �
�
��     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � V                    ��R��S���R���R��S���R�   ��Q           ��R��S���R���R��S           ��P��P��T��X��P   ��V          R�U���R���U                    Q\S\��Q���S��t~����Q���S��p~����Q�                               Q\Sj�S��s���S��u��S��u ���R# ��S��T��S��T��S��P��S��T            \0���0���^��^��0���^     0�v ����v ��                   jrT��P��T��0���T��0���S��S��0���0���0�                     jt]��]��P��0���\��\��| 1'���0���]��\��0���]                 jt0���0���T��0���0���Q��0���T��Q               
R
�R� T                RQ�R�        QX�Q�        X�`�X� [                !R!3Q39�R�        Q3X39�Q�        X3Y39�X�   49P )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                                                                                                                                                   X         Z���� ���� ���� ���� ���� ���� ���� �        _ @    ���������������
�� � @    1�����
�
 � @    2�������� h @    � l @    �����������	 � @    ����� * @    e�� J @    
"I � @    ���  @    ��� �A @    w������	 �A @    >]�� 6E @    
4=� DB @    J[��� P @   ��: @   � S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                                                                                                                                                                                                                             .file   a   ��  gcrtexe.c              �                                �              �   �
                        �   �
                        �   �
                        %  0
                        @  �
                        `             k  p                        �  P                        �  
                        �  �                        �  0          �  �                    envp           argv            argc    (                          `                        '  �          9  �
                        ^                           �             �  `
                        �  �                        �  �
                          p
                    mainret            "                           8                          N  @                        d  0                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file     ��  gidevicefuse.c         m  P                       copy_it        concat  @
      readFile�
          y  @      main    �*      .text   P     �  �             .data                           .bss    0      h                 .rdata         �	               .xdata  t      P                 .pdata  l      <                    �  �*     �  �                 �  �                           �  �                          �  �&  
   <                  �  �     �                    �  �     �  G                 �  0      @                    �  \      �                     9     �                          �                       �     �                    )       +                     4  P     �               .text   P      .idata$7l      .idata$5      .idata$4�      .idata$6�	      .text   X      .idata$7h      .idata$5       .idata$4�      .idata$6�	      .text   `      .idata$7d      .idata$5�      .idata$4�      .idata$6t	      .text   h      .idata$7`      .idata$5�      .idata$4�      .idata$6\	      .text   p      .idata$7\      .idata$5�      .idata$4�      .idata$6@	      .text   x      .idata$7X      .idata$5�      .idata$4�      .idata$6$	      .text   �      .idata$7T      .idata$5�      .idata$4�      .idata$6	      .text   �      .idata$7P      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7L      .idata$5�      .idata$4x      .idata$6�      .text   �      .idata$7H      .idata$5�      .idata$4p      .idata$6�      .text   �      .idata$7D      .idata$5�      .idata$4h      .idata$6�      .text   �      .idata$7@      .idata$5�      .idata$4`      .idata$6�      .text   �      .idata$7<      .idata$5�      .idata$4X      .idata$6p      .text   �      .idata$78      .idata$5�      .idata$4P      .idata$6X      .text   �      .idata$74      .idata$5�      .idata$4H      .idata$6D      .text   �      .idata$70      .idata$5�      .idata$4@      .idata$60      .text   �      .idata$7,      .idata$5�      .idata$48      .idata$6       .text   �      .idata$7(      .idata$5�      .idata$40      .idata$6      .text   �      .idata$7$      .idata$5x      .idata$4(      .idata$6�      .text   �      .idata$7       .idata$5p      .idata$4       .idata$6�      .text   �      .idata$7      .idata$5h      .idata$4      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4X      .idata$6      .file   3  ��  ggccmain.c             �                          p.0                �  @          �   
                    __main  �          �  �       .text         �                .data                         .bss    �                       .xdata  �                       .pdata  �      $   	                 �  �b  
   a                   �  l
     ?                    �  �     5                     �  p      0                      �     '                     �     �                     )  @     +                     4  (     �                .file   I  ��  gnatstart.c        .text   �                       .data                           .bss    �                           �  �h  
     
                 �  �     �                     �  �                                  V   
                   �                            �                         )  p     +                 .file   ]  ��  gwildcard.c        .text   �                       .data   0                       .bss    �                            �  �n  
   �                    �  a     .                     �  �                             g     :                      �     �                     )  �     +                 .file   y  ��  gdllargv.c         _setargv�                       .text   �                      .data   @                        .bss    �                        .xdata  �                       .pdata  �                          �  �o  
   �                   �  �     :                     �  �      0                      �     V                      9     �                     )  �     +                     4  �     0                .file   �  ��  g_newmode.c        .text   �                       .data   @                        .bss    �                           �  
q  
   �                    �  �     .                     �                              �     :                      �     �                     )        +                 .file   �  ��  gtlssup.c                �                                       !   
                    __xd_a  P       __xd_z  X           8  �      .text   �     �                .data   @                        .bss    �                       .xdata  �                       .pdata  �      $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �	     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  �q  
   �  6                 �  �     �                    �  �                        �  0     0                      1                          �                            t     �                     )  0     +                     4  �     �                .file   �  ��  gxncommod.c        .text   �                       .data   @                        .bss    �                           �  ^y  
   �                    �  �     .                     �  `                            G     :                      b     �                     )  `     +                 .file   �  ��  gcinitexe.c        .text   �                       .data   @                        .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  �y  
   {                   �       a                     �  �                            �     :                      	     �                     )  �     +                 .file     ��  gmerr.c            _matherr�                       .text   �     �                .data   @                        .bss    �                        .rdata   
     @               .xdata                        .pdata                           �  c{  
   6  
                 �  m                         �  �     �                    �  �     0                      �     �                      �	     �                     )  �     +                     4  �     X                .file   2  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   @                        .bss    �                        .xdata  (                      .pdata                           �  �~  
   �                    �  r     -                     �  �     0                      y     X                      ]
     �                     )  �     +                     4  �     0                .file   F  ��  gmingw_helpers.    .text   �                       .data   @                        .bss    �                           �  0  
   �                    �  �     .                     �                               �     :                      �
     �                     )        +                 .file   s  ��  gpseudo-reloc.c        D  �                           S  0          i        the_secs          u  �          �             �  @
                        �  P
                    .text   �     =  &             .data   @                        .bss                           .rdata  @     [                .xdata  ,     0                 .pdata        $   	                 �  �  
   K  �                 �  �     �                    �  �     �  
                 �        0                    �  �     W                            �                     �     	                       �     O                    )  P     +                     4       �                .file   �  ��  gusermatherr.c         �                                            @      .text         L                .data   @                        .bss                          .xdata  \                      .pdata  D                         �  
�  
   �                   �  �                         �       r                     �  P     0                      �      �                      �     �                     )  �     +                     4  �     P                .file   �  ��  gxtxtmode.c        .text   P                       .data   @                        .bss                               �  �  
   �                    �  �     .                     �  �                            9!     :                      �
     �                     )  �     +                 .file   �  ��  gcrt_handler.c         3  P                       .text   P     �               .data   @                        .bss    0                      .xdata  h                      .rdata  �     (   
             .pdata  \                         �  ��  
   �                   �  �     ~                    �  ~     _                    �  �     0                      s!     �  
                   �                            b                         )  �     +                     4  @     P                .file   �  ��  gtlsthrd.c             J                             j  `          x  @          �  �          �  H          �  �          �  �      .text        b  "             .data   @                        .bss    @     H                 .xdata  p     0                 .pdata  h     0                    �  k�  
   �
  A                 �  c     a                    �  �     �                    �  �     0                    �  B                             #     x                     s     %                    )       +                     4  �     (               .file     ��  gtlsmcrt.c         .text   �                       .data   @                       .bss    �                           �  >�  
   �                    �  �     .                     �                               x%     :                      �     �                     )  @     +                 .file     ��  g    �            .text   �                       .data   P                        .bss    �                          �  ǵ  
   �                    �  �     0                     �                               �%     :                      4     �                     )  p     +                 .file   O  ��  gpesect.c              �  �                             �                        5  �          R             j  `          }  �          �              �  �       .text   �     �  	             .data   P                        .bss    �                       .xdata  �     ,                 .pdata  �     l                    �  ��  
   �  �                 �  "     �                    �  �     �                    �  @     0                    �  Y     �                       �%     K                     �     T                       �     �                     )  �     +                     4  �     (               .text   �!     2                 .data   P                        .bss    �                       .text   �!                       .data   P                        .bss    �                           )  �     +                 .file   r  ��  gdirname.c             �  �!                       dirname �#          �  �      basename�$      .text   �!     d               .data   P                        .bss    �                      .xdata  �     ,                 .pdata       $   	             .rdata  �                          �  i�  
   N  *                 �  �     d                    �  �      Z                    �  p     0                      7+     �                     �                         )        +                     4  �                     .file   �  ��  gmingw_matherr.    .text   0%                       .data   P                       .bss    �                           �  ��  
   �                    �       .                     �  �                            �.     :                      �     �                     )  0     +                 .file   �  ��  gsleep.c           sleep   0%                       .text   0%                     .data   `                        .bss    �                       .xdata  �                      .pdata  (                         �  U�  
   �  	                 �  >     �                     �  �#                          �  �     0                      )/     u   	                   �     �                     )  `     +                     4  �	     8                .file   �  ��  gucrt_vfprintf.    vfprintfP%                       .text   P%                     .data   `                      .bss    �                       .xdata                         .pdata  4                         �  '�  
   �                   �  �     8                    �  $     X                     �  �     0                      �/     r   	                   ^     �                     )  �     +                     4  
     8                .file   �  ��  gucrt_snprintf.    snprintfp%                       .text   p%     9                .data   p                      .bss    �                       .xdata                        .pdata  @                         �  ��  
   �                   �  !     9                    �  p$     _                     �        0                      0     �   	                   F     �                     )  �     +                     4  P
     8                .file   �  ��  gucrt_printf.c     printf  �%                       .text   �%     H                .data   �                      .bss    �                       .xdata                        .pdata  L                         �  i�  
   �  
                 �  :"     l                    �  �$     -                     �  P     0                      �0     �   	                   .     �                     )  �     +                     4  �
     H                .file     ��  gucrt_fprintf.c    fprintf  &                       .text    &     2                .data   �                      .bss    �                       .xdata                        .pdata  X                         �  �  
   �                   �  �#     b                    �  �$     F                     �  �     0                      +1     �   	                        �                     )        +                     4  �
     8                .file   2  ��  g__initenv.c           �  �             �      .text   @&                       .data   �                      .bss    �                          �  ��  
   �                   �  %     �                     �  �                            �1     [                      �                         )  P     +                 .file   �  ��  gucrtbase_compa          @&                           "  P&          0  �&      _onexit 0'          ?  P'          M  �
                        r  p'          }  �'      tzset   �'          �  �
                    _tzset   (          �  �           �  �           �             �            �        .text   @&       "             .data   �      x   
             .bss    �                       .xdata  $     P                 .pdata  d     l                .rdata  �                          �  `�  
   �  Y                 �  �%                          �  B%     |                    �  �     0                      2     �                     K                                 `                    )  �     +                     4       �               .text   `(      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$40      .idata$6�
      .text   h(      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$48      .idata$6�
      .text   p(      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4@      .idata$6�
      .text   x(      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4H      .idata$6�
      .file   �  ��  gfake              hname   0      fthunk  �      .text   �(                       .data   0                       .bss    �                       .idata$2�                      .idata$40      .idata$5�      .file   �  ��  gfake              .text   �(                       .data   0                       .bss    �                       .idata$4P                      .idata$5�                      .idata$7�                      .text   �(      .data   0      .bss    �      .idata$7�      .idata$5P      .idata$4       .idata$6�
      .text   �(      .data   0      .bss    �      .idata$7�      .idata$5X      .idata$4      .idata$6�
      .text   �(      .data   0      .bss    �      .idata$7�      .idata$5`      .idata$4      .idata$6�
      .text   �(      .data   0      .bss    �      .idata$7�      .idata$5h      .idata$4      .idata$6�
      .text   �(      .data   0      .bss    �      .idata$7�      .idata$5p      .idata$4       .idata$6�
      .file   �  ��  gfake              hname          fthunk  P      .text   �(                       .data   0                       .bss    �                       .idata$2�                      .idata$4       .idata$5P      .file   S  ��  gfake              .text   �(                       .data   0                       .bss    �                       .idata$4(                      .idata$5x                      .idata$7�     !                 .text   �(      .data   0      .bss    �      .idata$7L      .idata$5�      .idata$4p      .idata$6�      .text   �(      .data   0      .bss    �      .idata$7P      .idata$5�      .idata$4x      .idata$6�      .text   �(      .data   0      .bss    �      .idata$7T      .idata$5�      .idata$4�      .idata$6�      .text   �(      .data   0      .bss    �      .idata$7X      .idata$5�      .idata$4�      .idata$6�      .text   �(      .data   0      .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6
      .text   �(      .data   0      .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6$
      .text   �(      .data   0      .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6>
      .text   �(      .data   0      .bss    �      .idata$7h      .idata$5�      .idata$4�      .idata$6H
      .text   �(      .data   0      .bss    �      .idata$7l      .idata$5       .idata$4�      .idata$6P
      .text   �(      .data   0      .bss    �      .idata$7p      .idata$5      .idata$4�      .idata$6Z
      .text    )      .data   0      .bss    �      .idata$7t      .idata$5      .idata$4�      .idata$6d
      .text   )      .data   0      .bss    �      .idata$7x      .idata$5      .idata$4�      .idata$6l
      .text   )      .data   0      .bss    �      .idata$7|      .idata$5       .idata$4�      .idata$6t
      .text   )      .data   0      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6|
      .text    )      .data   0      .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�
      .text   ()      .data   0      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�
      .text   0)      .data   0      .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6�
      .file   a  ��  gfake              hname   p      fthunk  �      .text   @)                       .data   0                       .bss    �                       .idata$2�                      .idata$4p      .idata$5�      .file   �  ��  gfake              .text   @)                       .data   0                       .bss    �                       .idata$4�                      .idata$5H                      .idata$7�                       .text   @)      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6r      .text   H)      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .text   P)      .data   0      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6�      .text   X)      .data   0      .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6�      .text   `)      .data   0      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�      .text   h)      .data   0      .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6�      .text   p)      .data   0      .bss    �      .idata$7�      .idata$5H      .idata$4�      .idata$6�      .text   x)      .data   0      .bss    �      .idata$7�      .idata$5P      .idata$4       .idata$6�      .text   �)      .data   0      .bss    �      .idata$7�      .idata$5X      .idata$4      .idata$6�      .text   �)      .data   0      .bss    �      .idata$7�      .idata$5`      .idata$4      .idata$6      .text   �)      .data   0      .bss    �      .idata$7       .idata$5h      .idata$4      .idata$6      .text   �)      .data   0      .bss    �      .idata$7      .idata$5p      .idata$4       .idata$6      .text   �)      .data   0      .bss    �      .idata$7      .idata$5x      .idata$4(      .idata$6:      .text   �)      .data   0      .bss    �      .idata$7      .idata$5�      .idata$40      .idata$6Z      .text   �)      .data   0      .bss    �      .idata$7      .idata$5�      .idata$48      .idata$6f      .text   �)      .data   0      .bss    �      .idata$7      .idata$5�      .idata$4@      .idata$6v      .text   �)      .data   0      .bss    �      .idata$7      .idata$5�      .idata$4H      .idata$6�      .text   �)      .data   0      .bss    �      .idata$7      .idata$5�      .idata$4P      .idata$6�      .text   �)      .data   0      .bss    �      .idata$7       .idata$5�      .idata$4X      .idata$6�      .text   �)      .data   0      .bss    �      .idata$7$      .idata$5�      .idata$4`      .idata$6�      .file   	  ��  gfake              hname   �      fthunk        .text   �)                       .data   0                       .bss    �                       .idata$2�                      .idata$4�      .idata$5      .file   %  ��  gfake              .text   �)                       .data   0                       .bss    �                       .idata$4h                      .idata$5�                      .idata$7(     "                 .text   �)      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6P      .text   �)      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6h      .file   3  ��  gfake              hname   �      fthunk         .text   �)                       .data   0                       .bss    �                       .idata$2�                      .idata$4�      .idata$5       .file   H  ��  gfake              .text   �)                       .data   0                       .bss    �                       .idata$4�                      .idata$5                      .idata$7�     "                 .text   �)      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6<      .file   V  ��  gfake              hname   �      fthunk  �      .text    *                       .data   0                       .bss    �                       .idata$2x                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text    *                       .data   0                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�                      .text    *      .data   0      .bss    �      .idata$7T      .idata$5�      .idata$4p      .idata$6      .text   *      .data   0      .bss    �      .idata$7X      .idata$5�      .idata$4x      .idata$6      .text   *      .data   0      .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6       .text   *      .data   0      .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6(      .text    *      .data   0      .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$62      .file   �  ��  gfake              hname   p      fthunk  �      .text   0*                       .data   0                       .bss    �                       .idata$2d                      .idata$4p      .idata$5�      .file   �  ��  gfake              .text   0*                       .data   0                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7h                      .text   0*      .data   0      .bss    �      .idata$7(      .idata$5�      .idata$4`      .idata$6�
      .file   �  ��  gfake              hname   `      fthunk  �      .text   @*                       .data   0                       .bss    �                       .idata$2P                      .idata$4`      .idata$5�      .file   �  ��  gfake              .text   @*                       .data   0                       .bss    �                       .idata$4h                      .idata$5�                      .idata$7,     %                 .text   @*      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4H      .idata$6�
      .text   H*      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$4P      .idata$6�
      .file   �  ��  gfake              hname   H      fthunk  �      .text   P*                       .data   0                       .bss    �                       .idata$2<                      .idata$4H      .idata$5�      .file   �  ��  gfake              .text   P*                       .data   0                       .bss    �                       .idata$4X                      .idata$5�                      .idata$7      &                 .text   P*      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$40      .idata$6�
      .text   X*      .data   0      .bss    �      .idata$7�      .idata$5�      .idata$48      .idata$6�
      .file     ��  gfake              hname   0      fthunk  �      .text   `*                       .data   0                       .bss    �                       .idata$2(                      .idata$40      .idata$5�      .file   n  ��  gfake              .text   `*                       .data   0                       .bss    �                       .idata$4@                      .idata$5�                      .idata$7�     "                 .text   `*      .data   0      .bss    �      .idata$7�      .idata$5p      .idata$4       .idata$6�
      .text   h*      .data   0      .bss    �      .idata$7�      .idata$5h      .idata$4      .idata$6�
      .text   p*      .data   0      .bss    �      .idata$7�      .idata$5`      .idata$4      .idata$6�
      .text   x*      .data   0      .bss    �      .idata$7�      .idata$5X      .idata$4      .idata$6�
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$5P      .idata$4       .idata$6v
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$5H      .idata$4�      .idata$6^
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6J
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6.
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6
      .text   �*      .data   0      .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�	      .text   �*      .data   0      .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6�	      .file   |  ��  gfake              hname   �      fthunk        .text   �*                       .data   0                       .bss    �                       .idata$2                      .idata$4�      .idata$5      .file   �  ��  gfake              .text   �*                       .data   0                       .bss    �                       .idata$4(                      .idata$5x                      .idata$7�     
                 .file   �  ��  gcygming-crtend        �  p6                       .text   �*                       .data   0                       .bss    �                           �  p6                         �  t                          �  �                         
	  �6                         )  �     +                 .idata$2        .idata$5h      .idata$4      .idata$2�       .idata$5�      .idata$4X      .idata$4�      .idata$5      .idata$7p      .idata$4`      .idata$5�      .idata$7      .rsrc       
    __xc_z         putchar ()      strcpy  �(          	  �          9	  `(          D	  4           V	  �(          o	  �          {	  �          �	  �          �	              �	  �6          �	  �           �	            �	  �          
  �*          
  �           M
  �          Z
  l           h
  �*          �
  �
      strerror�)          �
  �          �
  �      _mkdir  0*          �
            �
  �           �
      	        �
               �)          /  �       __xl_a  0           ;  �*          H  x          k  �          z  �          �         _cexit  `)          �  `  ��       �     ��       �  �                        !              7  �          J      ��       d     ��       �  0           �         __xl_d  @           �  �      _tls_end   	        �   
      __tznamep(          �  `*          �             
         udid    `           
  �           *
  0           :
  �
          X
  `          e
  p
          }
  (          �
      	    memcpy  �)          �
  �          �
  �          �
  �      puts    0)          �
  P
          �
  0            X      malloc  *      _CRT_MT @           :  p*      _assert X)          F  P          S              a  �          ~             �  8          �  �          �  0          �  �          �     ��       �  �            P           )  0          <  `          P  p          u  �           �  �          �  H          �            �  �          �  �            @*            �
          1  �       fflush  �(          J  �          ^  h*          m  <           �  �          �  P           �  �          �  �          �  H            �          !  p          ;  �)          P  �          ^  x          s  �
      abort   �)          �  @
          �  �           �  �           �  P           �        __dll__     ��             ��       %  H)          0  p          O  �            �*          �  P           �  @          �   *          �  0          �   
          �  �          �  x                         ,  h          8  �          W     ��       m  $           �  �      calloc  *          �  X          �  �	          �  �(          �  �          �  �       write   �(                      *  �          H  �          f  P          ~  �      Sleep   x*          �  �      _commode�           �  �          �  0      fseek   )      i       h           �  0          �  �6          �  �          �  d             �           (             B  �          e  �      __xi_z  (           q            �  p          �              �             �  �
          �  ,            �(          '  �
          E  (          `  �          o  �          z  �       signal  �)          �  �(          �  �           �              �  h      OP             strncmp �(          �  8           �  �6          �  p             `
          !  �          .  �*          >  �       realloc  *          l  �          x  (           �  �
          �      ��   strtok  �(          �  0
          �  �            (          J  (          \  `          i  �          v  �          �  �           �  `          �  �          �     ��       �  8            p            H*          -  h          [  �          u  �)          �  �          �  x)          �  �*          �  �      fopen   )          �  �          �  �                         6  `           E     ��       Z          ftell   )          i  @          �  �           �  �          �  �          �  �          �  �           �  h      lockdown�           �  8      __xl_z  H       __end__              
  �          )  P          K  �          i             w  X       strcmp  �(          �  �6      afc     p       __xi_a             �  �)          �  X          �  �*      __xc_a              �  �      fgetc    )          �     ��       �  P             P           3     ��       A  �      _fmode             _  `          q  �          �  x          �  �)          �  �          �  P          �  �           �  p)          �  �                       -  �          @  X          R             �  �          �  @)          �  h          �  �      __xl_c  8           �     	        �  0           �  �            �
          $  p          7  �          G  �           `  0           l  �          �  �	          �  h(          �  h          �  H       _newmode�           �  �)      fwrite   )            h            p          !  �          7      ��   phone   �           O      ��       `  �          l  �!          y  �           �  �(      res                �  �      exit    �)          �     ��       �            �      ��       �  �      _errno  �)      atoi    P*                          P      _exit   �)          )   �           6             E   h)      strlen  �(          \   @      open    �(          k   �)          w   
          �   �      atoll   X*          �   �          �   �*          �   �)          �   �          !  @          0!  �           _!  �          �!  �       BundleID@           �!  �          �!            �!  �          �!  x           �!             �!  �           �!  P       close   �(          "  �(          '"             3"  �           B"  P)          N"  �      free    *          n"  �       "  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_usage PushFileToDevice .text.startup .xdata.startup .pdata.startup __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names do_get_path_info static_path_copy.0 local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight houseArrestResult __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options IsDBCSLeadByteEx _head_lib64_libapi_ms_win_crt_private_l1_1_0_a __imp__mkdir REININT_LIMIT SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment afc_file_write __rt_psrelocs_start __imp_lockdownd_service_descriptor_free __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname lookup_opts __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll __imp__errno .refptr.__imp___initenv __imp_ftell _tls_start __imp_lockdownd_client_free house_arrest_get_result .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler lockdownd_service_descriptor_free TlsGetValue __imp_strcmp __bss_start__ afc_remove_path_and_contents __imp___C_specific_handler __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp__assert __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf SandBoxFile __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit afc_dictionary_free __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force __imp_afc_file_open VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ house_arrest_client_free __imp_afc_file_write __imp_LeaveCriticalSection __imp_afc_read_directory __imp_afc_dictionary_free __C_specific_handler afc_file_open __imp_afc_file_close .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf lockdownservice ___crt_xp_end__ __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT __imp_atoi _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp_write __imp__exit __imp_house_arrest_client_free __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname __imp_strcpy _tls_used __stdio_common_vsprintf __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ __imp_house_arrest_get_result lockdownd_start_service __imp_strerror .refptr._newmode afc_file_read __data_end__ __imp_fwrite __CTOR_LIST__ __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __imp_afc_remove_path_and_contents __bss_end__ __imp_AreFileApisANSI idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __lib64_libapi_ms_win_crt_filesystem_l1_1_0_a_iname __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection afc_file_close __imp_open _tls_index __acrt_iob_func __native_startup_state ___crt_xc_start__ lockdownd_client_free lockdownResult ___CTOR_LIST__ __imp_snprintf .refptr.__dyn_tls_init_callback __imp_signal AreFileApisANSI _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_atoll _head_lib64_libapi_ms_win_crt_convert_l1_1_0_a .refptr.__mingw_initltsdyn_force __rt_psrelocs_size .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname __imp___p___wargv __imp_strlen __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __imp_strtok __p__wenviron __imp_afc_client_new_from_house_arrest_client house_arrest_send_command _initialize_narrow_environment __imp_realloc _crt_at_quick_exit InitializeCriticalSection __imp_exit afc_remove_path _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp_IsDBCSLeadByteEx __imp___initenv __imp_plist_dict_get_item house_arrest_client_new __imp_afc_remove_path _head_libplist_2_0_dll __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp_house_arrest_client_new __imp__onexit FilePaths __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ _head_lib64_libapi_ms_win_crt_filesystem_l1_1_0_a __subsystem__ __imp___stdio_common_vsprintf __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv afc_client_new_from_house_arrest_client __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname afc_read_directory __p___argc __imp_VirtualProtect idevice_free ___tls_end__ afcResult __lib64_libapi_ms_win_crt_convert_l1_1_0_a_iname .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname LocalFilePath _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_close ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __imp_fflush __minor_image_version__ __imp___set_app_type __imp_fgetc __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_afc_file_read afc_get_file_info DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_afc_get_file_info __imp_fopen plist_dict_get_item house_arrest .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_fseek __imp_daylight __p___wargv __imp_house_arrest_send_command __mingw_app_type 