/* NSExecutableNotLoadableError */
"BundleErr3584" = "Paketet ”%@” kunde inte läsas in eftersom dess körbara fil inte är inläsningsbar.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "Paketets körbara fil går inte att läsa in.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Försök med att installera om paketet.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Paketet ”%@” kunde inte läsas in eftersom det inte innehåller en version för den aktuella arkitekturen.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "Paketet innehåller inte en version för aktuell arkitektur.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Försök att installera en Universal-version av paketet.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Paketet ”%@” kunde inte läsas in eftersom det inte fungerar med det aktuella programmet.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "Paketet är inte kompatibelt med det här programmet.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Försök med att installera en nyare version av paketet.";

/* NSExecutableLoadError */
"BundleErr3587" = "Paketet ”%@” kunde inte läsas in eftersom det är skadat eller saknar nödvändiga resurser.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "Paketet är skadat eller saknar nödvändiga resurser.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Försök med att installera om paketet.";

/* NSExecutableLinkError */
"BundleErr3588" = "Paketet ”%@” kunde inte läsas in.";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Paketet kunde inte läsas in.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Försök med att installera om paketet.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Paketet ”%@” kunde inte läsas in eftersom dess körbara fil inte hittades.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "Paketets körbara fil hittades inte.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Försök med att installera om paketet.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "Åtgärden kunde inte slutföras. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "Åtgärden kunde inte slutföras. (%1$@ fel %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "Åtgärden kunde inte slutföras. (%1$@ fel %2$ld.)";

