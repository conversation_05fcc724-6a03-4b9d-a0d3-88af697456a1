/* NSExecutableNotLoadableError */
"BundleErr3584" = "Berkas perisian “%@” tidak dapat dimuatkan kerana program boleh laksananya tidak boleh dimuatkan.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "Program boleh laksana bagi berkas perisian tidak boleh dimuatkan.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Cuba pasang semula berkas perisian.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Berkas perisian “%@” tidak dapat dimuatkan kerana ia tidak mengandungi versi bagi seni bina semasa.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "Berkas perisian tidak mengandungi versi bagi seni bina semasa.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Cuba pasang versi universal berkas perisian.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Berkas perisian “%@” tidak dapat dimuatkan kerana ia tidak serasi dengan aplikasi semasa.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "Berkas perisian tidak serasi dengan aplikasi ini.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Cuba pasang versi baru berkas perisian.";

/* NSExecutableLoadError */
"BundleErr3587" = "Berkas perisian “%@” tidak dapat dimuatkan kerana ia telah rosak atau kehilangan sumber yang diperlukan.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "Berkas perisian telah rosak atau kehilangan sumber yang diperlukan.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Cuba pasang semula berkas perisian.";

/* NSExecutableLinkError */
"BundleErr3588" = "Berkas perisian “%@” tidak dapat dimuatkan.";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Berkas perisian tidak dapat dimuatkan.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Cuba pasang semula berkas perisian.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Berkas perisian “%@” tidak dapat dimuatkan kerana program boleh laksananya tidak dapat dijumpai.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "Program boleh laksana bagi berkas perisian tidak dapat dijumpai.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Cuba pasang semula berkas perisian.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "Operasi tidak dapat diselesaikan. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "Operasi tidak dapat diselesaikan. (%1$@ ralat %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "Operasi tidak dapat diselesaikan. (%1$@ ralat %2$ld.)";

