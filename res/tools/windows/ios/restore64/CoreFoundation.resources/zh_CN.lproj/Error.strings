/* NSExecutableNotLoadableError */
"BundleErr3584" = "未能载入软件包“%@”，因为它的可执行文件不可载入。";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "该软件包的可执行文件不可载入。";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "请尝试重新安装软件包。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "未能载入软件包“%@”，因为它的版本不适用于当前架构。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "该软件包的版本不适用于当前架构。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "请尝试安装通用版本的软件包。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "未能载入软件包“%@”，因为它与当前应用程序不兼容。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "该软件包与此应用程序不兼容。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "请尝试安装较新版本的软件包。";

/* NSExecutableLoadError */
"BundleErr3587" = "未能载入软件包“%@”，因为它已损坏或丢失必要的资源。";

/* NSExecutableLoadError */
"BundleErr3587-C" = "软件包已损坏或丢失必要的资源。";

/* NSExecutableLoadError */
"BundleErr3587-R" = "请尝试重新安装软件包。";

/* NSExecutableLinkError */
"BundleErr3588" = "未能载入软件包“%@”。";

/* NSExecutableLinkError */
"BundleErr3588-C" = "未能载入该软件包。";

/* NSExecutableLinkError */
"BundleErr3588-R" = "请尝试重新安装软件包。";

/* NSFileNoSuchFileError */
"BundleErr4" = "未能载入软件包“%@”，因为未能找到它的可执行文件的位置。";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "未能找到该软件包的可执行文件的位置。";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "请尝试重新安装软件包。";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "未能完成该操作。%@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "未能完成该操作。（%1$@ 错误 %2$ld - %3$@）";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "未能完成该操作。（%1$@ 错误 %2$ld。）";

