/* NSExecutableNotLoadableError */
"BundleErr3584" = "実行可能ファイルを読み込めないため、バンドル“%@”を読み込めませんでした。";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "バンドルの実行可能ファイルを読み込めません。";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "バンドルを再インストールしてください。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "現在のアーキテクチャ用のバージョンが含まれていないため、バンドル“%@”を読み込めませんでした。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "バンドルに現在のアーキテクチャ用のバージョンが含まれていません。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "このバンドルのユニバーサルバージョンをインストールしてください。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "バンドル“%@”は、現在のアプリケーションと互換性がないため読み込めませんでした。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "このバンドルはこのアプリケーションと互換性がありません。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "このバンドルの新しいバージョンをインストールしてください。";

/* NSExecutableLoadError */
"BundleErr3587" = "バンドル“%@”は、壊れているか必要なリソースがないため読み込めませんでした。";

/* NSExecutableLoadError */
"BundleErr3587-C" = "バンドルが壊れているか、必要なリソースがありません。";

/* NSExecutableLoadError */
"BundleErr3587-R" = "バンドルを再インストールしてください。";

/* NSExecutableLinkError */
"BundleErr3588" = "バンドル“%@”を読み込めませんでした。";

/* NSExecutableLinkError */
"BundleErr3588-C" = "バンドルを読み込めませんでした。";

/* NSExecutableLinkError */
"BundleErr3588-R" = "バンドルを再インストールしてください。";

/* NSFileNoSuchFileError */
"BundleErr4" = "実行可能ファイルが見つからなかったため、バンドル“%@”を読み込めませんでした。";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "バンドルの実行可能ファイルが見つかりませんでした。";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "バンドルを再インストールしてください。";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "操作を完了できませんでした。
%@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "操作を完了できませんでした。（%1$@ エラー %2$ld - %3$@）";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "操作を完了できませんでした。（%1$@ エラー %2$ld）";

