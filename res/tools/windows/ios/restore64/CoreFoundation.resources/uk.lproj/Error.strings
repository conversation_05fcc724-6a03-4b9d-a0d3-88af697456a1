/* NSExecutableNotLoadableError */
"BundleErr3584" = "Не можу завантажити пакет «%@», оскільки не вдалося завантажити його виконуваний файл.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "Виконуваний файл пакета завантажити не можна.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Спробуйте переінсталювати пакет.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Не можу завантажити пакет «%@», оскільки в ньому немає версії для поточної архітектури.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "У цьому пакеті немає версії для поточної архітектури.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Спробуйте інсталювати універсальну версію пакета.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Не можу завантажити пакет «%@», оскільки він несумісний із поточною програмою.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "Пакет несумісний із цією програмою.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Спробуйте інсталювати новішу версію пакета.";

/* NSExecutableLoadError */
"BundleErr3587" = "Не можу завантажити пакет «%@», оскільки він пошкоджений або в ньому відсутні необхідні ресурси.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "Пакет пошкоджений, або в ньому відсутні необхідні ресурси.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Спробуйте переінсталювати пакет.";

/* NSExecutableLinkError */
"BundleErr3588" = "Не можу завантажити пакет «%@».";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Не можу завантажити пакет.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Спробуйте переінсталювати пакет.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Не можу завантажити пакет «%@», оскільки не можу знайти виконуваний файл пакета.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "Не можу знайти виконуваний файл пакета.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Спробуйте переінсталювати пакет.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "Не вдається завершити операцію. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "Не вдається завершити операцію. (%1$@, помилка %2$ld — %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "Не вдається завершити операцію. (Помилка %2$ld у %1$@.)";

