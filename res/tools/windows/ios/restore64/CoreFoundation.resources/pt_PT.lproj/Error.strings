/* NSExecutableNotLoadableError */
"BundleErr3584" = "Não foi possível carregar o bundle “%@” porque o executável não pode ser carregado.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "O executável do bundle não pode ser carregado.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Tente reinstalar o bundle.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Não foi possível carregar o bundle “%@” porque não contém uma versão para a arquitectura actual.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "O bundle não contém uma versão para a arquitectura actual.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Tente instalar uma versão universal do bundle.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Não foi possível carregar o bundle “%@” porque não é compatível com a aplicação actual.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "O bundle não é compatível com esta aplicação.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Tente instalar uma versão mais recente do bundle.";

/* NSExecutableLoadError */
"BundleErr3587" = "Não foi possível carregar o bundle “%@” porque está danificado ou faltam os recursos necessários.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "O bundle está danificado ou faltam os recursos necessários.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Tente reinstalar o bundle.";

/* NSExecutableLinkError */
"BundleErr3588" = "Não foi possível carregar o bundle “%@”.";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Não foi possível carregar o bundle.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Tente reinstalar o bundle.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Não foi possível carregar o bundle “%@” porque o respectivo executável não foi localizado.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "Não foi possível localizar o executável do bundle.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Tente reinstalar o bundle.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "Não foi possível concluir a operação. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "Não foi possível concluir a operação. (%1$@, erro %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "Não foi possível concluir a operação. (%1$@, erro %2$ld.)";

