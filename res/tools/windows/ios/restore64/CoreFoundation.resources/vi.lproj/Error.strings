/* NSExecutableNotLoadableError */
"BundleErr3584" = "Không thể tải gói “%@” vì tệp thực thi không thể tải được.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "Tệp thực thi của gói này không thể tải được.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Thử cài đặt lại gói này.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Không thể tải gói “%@” vì không chứa phiên bản cho kiến trúc hiện tại.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "<PERSON><PERSON><PERSON> này không chứa phiên bản cho kiến trúc hiện tại.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Thử cài đặt phiên bản chung của gói này.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Không thể tải gói “%@” vì không tương thích với ứng dụng hiện tại.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "Gói này không tương thích với ứng dụng này.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Thử cài đặt phiên bản mới hơn của gói này.";

/* NSExecutableLoadError */
"BundleErr3587" = "Không thể tải gói “%@” vì đã bị hỏng hoặc thiếu các tài nguyên cần thiết.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "Gói này đã bị hỏng hoặc thiếu các tài nguyên cần thiết.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Thử cài đặt lại gói này.";

/* NSExecutableLinkError */
"BundleErr3588" = "Khong thể tải gói “%@”.";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Khong thể tải gói này.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Thử cài đặt lại gói này.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Không thể tải gói “%@” vì không thể định vị tệp thực thi.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "Không thể định vị tệp thực thi của gói này.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Thử cài đặt lại gói này.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "Không thể hoàn tất thao tác này. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "Không thể hoàn tất thao tác này. (lỗi %1$@ %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "Không thể hoàn tất thao tác này. (lỗi %1$@ %2$ld.)";

