/* NSExecutableNotLoadableError */
"BundleErr3584" = "Le paquet « %@ » n’a pas pu se charger car son exécutable n’est pas chargeable.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "L’exécutable du paquet n’est pas chargeable.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Réinstallez le paquet.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Le paquet « %@ » n’a pas pu se charger car il ne contient pas de version de l’architecture actuelle.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "Le paquet ne contient pas la version de l’architecture actuelle.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Réessayez en installant une version universelle du paquet.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Le paquet « %@ » n’a pas pu se charger car il n’est pas compatible avec l’application active.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "Le paquet n’est pas compatible avec cette application.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Réessayez en installant une version plus récente du paquet.";

/* NSExecutableLoadError */
"BundleErr3587" = "Le paquet « %@ » n’a pas pu se charger car il est endommagé ou des ressources nécessaires sont manquantes.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "Le paquet est endommagé ou des ressources nécessaires sont manquantes.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Réinstallez le paquet.";

/* NSExecutableLinkError */
"BundleErr3588" = "Le paquet « %@ » n’a pas pu se charger.";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Le paquet n’a pas pu se charger.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Réinstallez le paquet.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Le paquet « %@ » n’a pas pu se charger car son exécutable est introuvable.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "L’exécutable du paquet est introuvable.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Réinstallez le paquet.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "L’opération n’a pas pu s’achever. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "L’opération n’a pas pu s’achever. (%1$@ erreur %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "L’opération n’a pas pu s’achever. (%1$@ erreur %2$ld).";

