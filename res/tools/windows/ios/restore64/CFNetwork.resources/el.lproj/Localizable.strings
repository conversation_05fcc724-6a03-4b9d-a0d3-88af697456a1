/* This computer's Internet connection appears to be offline. */
"CONNECTION_DOWN" = "Η σύνδεση αυτού του υπολογιστή στο Διαδίκτυο φαίνεται ότι έχει διακοπεί.";

/* This computer's Internet connection may be offline. */
"CONNECTION_INDETERMINATE" = "Η σύνδεση αυτού του υπολογιστή στο Διαδίκτυο ίσως έχει διακοπεί.";

/* This computer's Internet connection appears to be online. */
"CONNECTION_UP" = "Η σύνδεση αυτού του υπολογιστή στο Διαδίκτυο φαίνεται ότι είναι ενεργή.";

/* This computer's DNS server is not responding. */
"NAMESERVER_DOWN" = "Ο διακομιστής DNS αυτού του υπολογιστή δεν αποκρίνεται.";

/* The DNS server could not find the server name. */
"NAMELOOKUP_FAILED" = "Ο διακομιστής DNS δεν βρήκε το όνομα διακομιστή.";

/* This computer's router is not responding. */
"ROUTER_DOWN" = "Ο δρομολογητής αυτού του υπολογιστή δεν αποκρίνεται.";

/* The server this computer is attempting to connect to is not responding. */
"SERVER_DOWN" = "Ο διακομιστής στον οποίο προσπαθεί να συνδεθεί ο υπολογιστής δεν αποκρίνεται.";

/* This computer's Internet connection appears to be online. */
"SERVER_UP" = "Η σύνδεση αυτού του υπολογιστή στο Διαδίκτυο φαίνεται ότι είναι ενεργή.";

/* Format string for label of a saved password in Keychain: URL (USERNAME) */
"KEYCHAIN_LABEL_FORMAT" = "%@ (%@)";

/* Kind string used for keychain items from forms-based logins */
"KEYCHAIN_WEB_FORM_PASSWORD" = "συνθηματικό φόρμας Ιστού";

/* HTTP result code string */
"accepted" = "αποδεκτό";

/* HTTP result code string */
"bad gateway" = "εσφαλμένη πύλη";

/* HTTP result code string */
"bad request" = "εσφαλμένο αίτημα";

/* HTTP result code string */
"client error" = "σφάλμα πελάτη";

/* HTTP result code string */
"conflict" = "διένεξη";

/* HTTP result code string */
"continue" = "συνέχεια";

/* HTTP result code string */
"created" = "δημιουργήθηκε";

/* HTTP result code string */
"expectation failed" = "αποτυχία αναμενόμενου";

/* HTTP result code string */
"forbidden" = "απαγορεύεται";

/* HTTP result code string */
"found" = "βρέθηκε";

/* HTTP result code string */
"gateway timed out" = "λήξη χρονικού ορίου πύλης";

/* HTTP result code string */
"informational" = "πληροφοριακά";

/* HTTP result code string */
"internal server error" = "εσωτερικό σφάλμα διακομιστή";

/* HTTP result code string */
"length required" = "απαιτείται μήκος";

/* HTTP result code string */
"method not allowed" = "η μέθοδος δεν επιτρέπεται";

/* HTTP result code string */
"moved permanently" = "μετακινήθηκε μόνιμα";

/* HTTP result code string */
"multiple choices" = "πολλές επιλογές";

/* HTTP result code string */
"needs proxy" = "απαιτείται μεσολαβητής";

/* HTTP result code string */
"no content" = "δεν υπάρχει περιεχόμενο";

/* HTTP result code string */
"no error" = "δεν υπάρχει σφάλμα";

/* HTTP result code string */
"non-authoritative information" = "ανεπίσημες πληροφορίες";

/* HTTP result code string */
"not found" = "δεν βρέθηκε";

/* HTTP result code string */
"not modified" = "δεν τροποποιήθηκε";

/* HTTP result code string */
"partial content" = "μερικό περιεχόμενο";

/* HTTP result code string */
"payment required" = "απαιτείται πληρωμή";

/* HTTP result code string */
"precondition failed" = "η προϋπόθεση απέτυχε";

/* HTTP result code string */
"proxy authentication required" = "απαιτείται έλεγχος ταυτότητας μεσολαβητή";

/* HTTP result code string */
"redirected" = "ανακατεύθυνση";

/* HTTP result code string */
"no longer exists" = "δεν υπάρχει πλέον";

/* HTTP result code string */
"request timed out" = "έληξε το χρονικό όριο αιτήματος";

/* HTTP result code string */
"request too large" = "αίτημα πολύ μεγάλο";

/* HTTP result code string */
"requested URL too long" = "η αιτούμενη διεύθυνση URL είναι πολύ μεγάλη";

/* HTTP result code string */
"requested range not satisfiable" = "αιτούμενο εύρος δεν είναι ικανοποιήσιμο";

/* HTTP result code string */
"reset content" = "επαναφορά περιεχομένου";

/* HTTP result code string */
"see other" = "δείτε άλλο";

/* HTTP result code string */
"server error" = "σφάλμα διακομιστή";

/* HTTP result code string */
"service unavailable" = "μη διαθέσιμη υπηρεσία";

/* HTTP result code string */
"success" = "επιτυχία";

/* HTTP result code string */
"switching protocols" = "πρωτόκολλα μεταγωγής";

/* HTTP result code string */
"temporarily redirected" = "προσωρινή ανακατεύθυνση";

/* HTTP result code string */
"unacceptable" = "μη αποδεχτό";

/* HTTP result code string */
"unauthorized" = "μη εξουσιοδοτημένο";

/* HTTP result code string */
"unimplemented" = "δεν έχει υλοποιηθεί";

/* HTTP result code string */
"unsupported media type" = "μη υποστηριζόμενος τύπος μέσου";

/* HTTP result code string */
"unsupported version" = "μη υποστηριζόμενη έκδοση";

/* kCFErrorHTTPProxyConnectionFailure description */
"Err306" = "Υπήρξε πρόβλημα στην επικοινωνία με το διακομιστή μεσολάβησης Ιστού (HTTP).";

/* kCFErrorHTTPSProxyConnectionFailure description */
"Err310" = "Υπήρξε πρόβλημα στην επικοινωνία με τον ασφαλή διακομιστή μεσολάβησης Ιστού (HTTPS).";

/* WebFoundationErrorUnknown description */
"Err-998" = "άγνωστο σφάλμα";

/* WebFoundationErrorCancelled description */
"Err-999" = "ακυρώθηκε";

/* WebFoundationErrorBadURLError description */
"Err-1000" = "εσφαλμένη διεύθυνση URL";

/* WebFoundationErrorTimedOut description */
"Err-1001" = "Έληξε το χρονικό όριο του αιτήματος.";

/* WebFoundationErrorUnsupportedURL description */
"Err-1002" = "μη υποστηριζόμενη διεύθυνση URL";

/* WebFoundationErrorCannotFindHostError description */
"Err-1003" = "Δεν βρέθηκε διακομιστής με το όνομα υπολογιστή υπηρεσίας που προσδιορίσατε.";

/* WebFoundationErrorCannotConnectToHostError description */
"Err-1004" = "Δεν ήταν δυνατή η σύνδεση με το διακομιστή.";

/* WebFoundationErrorNetworkConnectionLostError description */
"Err-1005" = "Η σύνδεση δικτύου χάθηκε.";

/* WebFoundationErrorDNSLookupError description */
"Err-1006" = "Σφάλμα αναζήτησης DNS";

/* WebFoundationErrorHTTPTooManyRedirectsError description */
"Err-1007" = "πάρα πολλές ανακατευθύνσεις HTTP";

/* WebFoundationErrorResourceUnavailableError description */
"Err-1008" = "μη διαθέσιμος πόρος";

/* WebFoundationErrorNotConnectedToInternetError description */
"Err-1009" = "Η σύνδεση στο Διαδίκτυο φαίνεται ότι έχει διακοπεί.";

/* WebFoundationErrorRedirectToNonExistentLocation description */
"Err-1010" = "ανακατεύθυνση στο πουθενά";

/* WebFoundationErrorBadServerResponseError description */
"Err-1011" = "Υπήρξε εσφαλμένη απόκριση από το διακομιστή.";

/* WebFoundationErrorZeroByteResourceError description */
"Err-1014" = "πόρος μηδενικών byte";

/* WebFoundationErrorCannotDecodeRawData description */
"Err-1015" = "δεν είναι δυνατή η αποκωδικοποίηση ανεπεξέργαστων δεδομένων";

/* WebFoundationErrorCannotDecodeContentData description */
"Err-1016" = "δεν είναι δυνατή η αποκρυπτογράφηση δεδομένων περιεχομένου";

/* WebFoundationErrorCannotParseResponse description */
"Err-1017" = "δεν είναι δυνατή η συντακτική ανάλυση απόκρισης";

/* kCFURLErrorInternationalRoamingOff description */
"Err-1018" = "Αυτήν τη στιγμή, η διεθνής περιαγωγή είναι απενεργοποιημένη.";

/* kCFURLErrorCallIsActive description */
"Err-1019" = "Δεν είναι δυνατή η δημιουργία σύνδεσης δεδομένων επειδή υπάρχει ενεργή κλήση.";

/* kCFURLErrorDataNotAllowed description */
"Err-1020" = "Αυτήν τη στιγμή, δεν επιτρέπεται σύνδεση δεδομένων.";

/* kCFURLErrorRequestBodyStreamExhausted description.  Should never be seen by an end user. */
"Err-1021" = "η ροή σώματος αιτήματος εξαντλήθηκε";
	
/* WebFoundationErrorFileDoesNotExist description */
"Err-1100" = "Η διεύθυνση URL που ζητήθηκε δεν βρέθηκε στο διακομιστή.";

/* WebFoundationErrorFileIsDirectory description */
"Err-1101" = "το αρχείο είναι κατάλογος";

/* WebFoundationErrorNoPermissionsToReadFile description */
"Err-1102" = "Δεν έχετε δικαίωμα πρόσβασης στο ζητούμενο πόρο.";

/* WebFoundationErrorDataLengthExceedsMaximum description */
"Err-1103" = "ο πόρος υπερβαίνει το μέγιστο μέγεθος";

/* WebFoundationErrorSecureConnectionFailed description */
"Err-1200" = "Παρουσιάστηκε σφάλμα SSL και δεν είναι δυνατή η ασφαλής σύνδεση με το διακομιστή.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201" = "Το πιστοποιητικό για αυτόν τον διακομιστή έχει λήξει.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201.w" = "Το πιστοποιητικό για αυτόν τον διακομιστή έχει λήξει. Ίσως συνδέεστε σε διακομιστή που προσποιείται ότι είναι ο «%@», γεγονός που μπορεί να διακυβεύσει τις απόρρητες πληροφορίες σας.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202" = "Το πιστοποιητικό για αυτόν τον διακομιστή δεν είναι έγκυρο.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202.w" = "Το πιστοποιητικό για αυτόν τον διακομιστή δεν είναι έγκυρο. Ίσως συνδέεστε σε διακομιστή που προσποιείται ότι είναι ο «%@», γεγονός που μπορεί να διακυβεύσει τις απόρρητες πληροφορίες σας.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203" = "Το πιστοποιητικό για αυτόν τον διακομιστή υπογράφηκε από άγνωστη αρχή πιστοποίησης.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203.w" = "Το πιστοποιητικό για αυτόν τον διακομιστή υπογράφηκε από άγνωστη αρχή πιστοποίησης. Ίσως συνδέεστε σε διακομιστή που προσποιείται ότι είναι ο «%@», γεγονός που μπορεί να διακυβεύσει τις απόρρητες πληροφορίες σας.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204" = "Το πιστοποιητικό για αυτόν τον διακομιστή δεν είναι ακόμη έγκυρο.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204.w" = "Το πιστοποιητικό για αυτόν τον διακομιστή δεν είναι ακόμη έγκυρο. Ίσως συνδέεστε σε διακομιστή που προσποιείται ότι είναι ο «%@», γεγονός που μπορεί να διακυβεύσει τις απόρρητες πληροφορίες σας.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205" = "Ο διακομιστής δεν δέχτηκε το πιστοποιητικό.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205.w" = "Ο διακομιστής «%@» δεν δέχτηκε το πιστοποιητικό.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206" = "Ο διακομιστής απαιτεί πιστοποιητικό πελάτη.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206.w" = "Ο διακομιστής «%@» απαιτεί πιστοποιητικό πελάτη.";

/* WebFoundationErrorCannotLoadFromNetworkError description */
"Err-2000" = "δεν είναι δυνατή η φόρτωση από το δίκτυο";

/* WebFoundationErrorCannotCreateFile description */
"Err-3000" = "Δεν είναι δυνατή η δημιουργία αρχείου";

/* WebFoundationErrorCannotOpenFile description */
"Err-3001" = "Δεν είναι δυνατό το άνοιγμα του αρχείου";

/* WebFoundationErrorCannotCloseFile description */
"Err-3002" = "Προέκυψε αποτυχία κατά το κλείσιμο του αρχείου";

/* WebFoundationErrorCannotWriteToFile description */
"Err-3003" = "Δεν είναι δυνατή η εγγραφή αρχείου";

/* WebFoundationErrorCannotRemoveFile description */
"Err-3004" = "Δεν είναι δυνατή η αφαίρεση αρχείου";

/* WebFoundationErrorCannotMoveFile description */
"Err-3005" = "Δεν είναι δυνατή η μετακίνηση αρχείου";

/* WebFoundationErrorDownloadDecodingFailedMidStream description */
"Err-3006" = "Απέτυχε η αποκωδικοποίηση της λήψης";

/* WebFoundationErrorDownloadDecodingFailedToComplete description */
"Err-3007" = "Απέτυχε η αποκωδικοποίηση της λήψης";

/* Recovery suggestion */
"Would you like to connect to the server anyway?" = "Θέλετε οπωσδήποτε να συνδεθείτε στο διακομιστή;";

/* Recovery suggestion */
"To change your proxy settings, open Network preferences, click Advanced, and then click Proxies. For help with this problem, contact your system administrator." = "Για να αλλάξετε τις ρυθμίσεις μεσολαβητή, ανοίξτε τις προτιμήσεις για το «Δίκτυο», κάντε κλικ στα «Προηγμένα» και μετά κάντε κλικ στους «Μεσολαβητές». Για βοήθεια σχετικά με αυτό το πρόβλημα, επικοινωνήστε με τον διαχειριστή του συστήματός σας.";

/* AuthBrokerAgent proxy credential user notification - alert header */
"Proxy Authentication Required" = "Απαιτείται έλεγχος ταυτότητας μεσολαβητή";

/* AuthBrokerAgent proxy credential user notification - alert message, args are 1) proxy type, 2) host name, 3) port number, e.g.:  Authentication for HTTP proxy csd3.apple.com:8080 */
"Authentication for %@ proxy\n%@:%d" = "Έλεγχος ταυτότητας για τον %@ μεσολαβητή\n%@:%d";

/* AuthBrokerAgent proxy credential user notification - Cancel button title */
"Cancel" = "Ακύρωση";

/* AuthBrokerAgent proxy credential user notification - Username title */
"Username" = "Όνομα χρήστη";

/* AuthBrokerAgent proxy credential user notification - Password title */
"Password" = "Συνθηματικό";
