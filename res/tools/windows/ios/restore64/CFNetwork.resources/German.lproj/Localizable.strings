/* This computer's Internet connection appears to be offline. */
"CONNECTION_DOWN" = "Die Verbindung dieses Computers zum Internet wurde offenbar getrennt.";

/* This computer's Internet connection may be offline. */
"CONNECTION_INDETERMINATE" = "Die Verbindung dieses Computers zum Internet wurde möglicherweise getrennt.";

/* This computer's Internet connection appears to be online. */
"CONNECTION_UP" = "Dieser Computer ist offenbar mit dem Internet verbunden.";

/* This computer's DNS server is not responding. */
"NAMESERVER_DOWN" = "Der DNS-Server dieses Computers antwortet nicht.";

/* The DNS server could not find the server name. */
"NAMELOOKUP_FAILED" = "Der DNS-Server konnte den Servernamen nicht finden.";

/* This computer's router is not responding. */
"ROUTER_DOWN" = "Der Router dieses Computers antwortet nicht.";

/* The server this computer is attempting to connect to is not responding. */
"SERVER_DOWN" = "Der Server, mit dem sich dieser Computer verbinden möchte, antwortet nicht.";

/* This computer's Internet connection appears to be online. */
"SERVER_UP" = "Dieser Computer ist offenbar mit dem Internet verbunden.";

/* Format string for label of a saved password in Keychain: URL (USERNAME) */
"KEYCHAIN_LABEL_FORMAT" = "%@ (%@)";

/* Kind string used for keychain items from forms-based logins */
"KEYCHAIN_WEB_FORM_PASSWORD" = "Kennwort des Webformulars";

/* HTTP result code string */
"accepted" = "Akzeptiert";

/* HTTP result code string */
"bad gateway" = "Ungültiges Gateway";

/* HTTP result code string */
"bad request" = "Ungültige Anfrage";

/* HTTP result code string */
"client error" = "Fehler beim Client";

/* HTTP result code string */
"conflict" = "Konflikt";

/* HTTP result code string */
"continue" = "Fortfahren";

/* HTTP result code string */
"created" = "Erstellt";

/* HTTP result code string */
"expectation failed" = "Erwartete Daten nicht erhalten";

/* HTTP result code string */
"forbidden" = "Verboten";

/* HTTP result code string */
"found" = "Gefunden";

/* HTTP result code string */
"gateway timed out" = "Das Gateway lieferte eine Zeitüberschreitung";

/* HTTP result code string */
"informational" = "Informell";

/* HTTP result code string */
"internal server error" = "Interner Server-Fehler";

/* HTTP result code string */
"length required" = "Länge benötigt";

/* HTTP result code string */
"method not allowed" = "Methode nicht erlaubt";

/* HTTP result code string */
"moved permanently" = "Dauerhaft bewegt";

/* HTTP result code string */
"multiple choices" = "Mehrfache Auswahl";

/* HTTP result code string */
"needs proxy" = "Proxy benötigt";

/* HTTP result code string */
"no content" = "Kein Inhalt";

/* HTTP result code string */
"no error" = "Kein Fehler";

/* HTTP result code string */
"non-authoritative information" = "Unerhebliche Information";

/* HTTP result code string */
"not found" = "Nicht gefunden";

/* HTTP result code string */
"not modified" = "Nicht verändert";

/* HTTP result code string */
"partial content" = "Teilweiser Inhalt";

/* HTTP result code string */
"payment required" = "Zahlung erforderlich";

/* HTTP result code string */
"precondition failed" = "Grundvoraussetzung fehlgeschlagen";

/* HTTP result code string */
"proxy authentication required" = "Proxy-Identifizierung erforderlich";

/* HTTP result code string */
"redirected" = "Umgeleitet";

/* HTTP result code string */
"no longer exists" = "Existiert nicht mehr";

/* HTTP result code string */
"request timed out" = "Zeitlimit der Anfrage überschritten";

/* HTTP result code string */
"request too large" = "Anforderung zu groß";

/* HTTP result code string */
"requested URL too long" = "Angeforderte URL zu lang";

/* HTTP result code string */
"requested range not satisfiable" = "Angeforderter Bereich nicht erfüllbar";

/* HTTP result code string */
"reset content" = "Inhalt zurückgesetzt";

/* HTTP result code string */
"see other" = "Siehe auch";

/* HTTP result code string */
"server error" = "Serverfehler";

/* HTTP result code string */
"service unavailable" = "Dienst nicht verfügbar";

/* HTTP result code string */
"success" = "Erfolgreich";

/* HTTP result code string */
"switching protocols" = "Protokoll wechseln";

/* HTTP result code string */
"temporarily redirected" = "Vorübergehend umgeleitet";

/* HTTP result code string */
"unacceptable" = "Nicht akzeptierbar";

/* HTTP result code string */
"unauthorized" = "Nicht berechtigt";

/* HTTP result code string */
"unimplemented" = "Nicht implementiert";

/* HTTP result code string */
"unsupported media type" = "Nicht unterstützer Medientyp";

/* HTTP result code string */
"unsupported version" = "Version nicht unterstützt";

/* kCFErrorHTTPProxyConnectionFailure description */
"Err306" = "Bei der Kommunikation mit dem Web-Proxy-Server ist ein Fehler aufgetreten (HTTPS).";

/* kCFErrorHTTPSProxyConnectionFailure description */
"Err310" = "Bei der Kommunikation mit dem sicheren Web-Proxy-Server ist ein Fehler aufgetreten (HTTPS).";

/* WebFoundationErrorUnknown description */
"Err-998" = "unbekannter Fehler";

/* WebFoundationErrorCancelled description */
"Err-999" = "Abgebrochen";

/* WebFoundationErrorBadURLError description */
"Err-1000" = "Ungültige URL";

/* WebFoundationErrorTimedOut description */
"Err-1001" = "Zeitüberschreitung bei der Anforderung.";

/* WebFoundationErrorUnsupportedURL description */
"Err-1002" = "URL nicht unterstützt";

/* WebFoundationErrorCannotFindHostError description */
"Err-1003" = "Es wurde kein Server mit dem angegebenen Hostnamen gefunden.";

/* WebFoundationErrorCannotConnectToHostError description */
"Err-1004" = "Verbindung zum Server konnte nicht hergestellt werden.";

/* WebFoundationErrorNetworkConnectionLostError description */
"Err-1005" = "Die Netzwerkverbindung wurde unterbrochen.";

/* WebFoundationErrorDNSLookupError description */
"Err-1006" = "Fehler bei DNS-Suche";

/* WebFoundationErrorHTTPTooManyRedirectsError description */
"Err-1007" = "Zu viele HTTP-Umleitungen";

/* WebFoundationErrorResourceUnavailableError description */
"Err-1008" = "Ressource nicht verfügbar";

/* WebFoundationErrorNotConnectedToInternetError description */
"Err-1009" = "Anscheinend besteht keine Verbindung zum Internet.";

/* WebFoundationErrorRedirectToNonExistentLocation description */
"Err-1010" = "Umleitung nicht gültig";

/* WebFoundationErrorBadServerResponseError description */
"Err-1011" = "Ungültige Antwort vom Server.";

/* WebFoundationErrorZeroByteResourceError description */
"Err-1014" = "Ressource mit 0 Byte";

/* WebFoundationErrorCannotDecodeRawData description */
"Err-1015" = "Decodieren der Rohdaten nicht möglich.";

/* WebFoundationErrorCannotDecodeContentData description */
"Err-1016" = "Decodieren der enthaltenen Daten nicht möglich.";

/* WebFoundationErrorCannotParseResponse description */
"Err-1017" = "Parsen der Antwort nicht möglich.";

/* kCFURLErrorInternationalRoamingOff description */
"Err-1018" = "Internationales Roaming ist zurzeit deaktiviert.";

/* kCFURLErrorCallIsActive description */
"Err-1019" = "Eine Datenverbindung kann nicht aufgebaut werden, da zurzeit ein Anruf aktiv ist.";

/* kCFURLErrorDataNotAllowed description */
"Err-1020" = "Eine Datenverbindung ist zurzeit nicht erlaubt.";

/* kCFURLErrorRequestBodyStreamExhausted description.  Should never be seen by an end user. */
"Err-1021" = "Angeforderter Hauptstream ausgeschöpft";
	
/* WebFoundationErrorFileDoesNotExist description */
"Err-1100" = "Die angeforderte URL wurde auf diesem Server nicht gefunden.";

/* WebFoundationErrorFileIsDirectory description */
"Err-1101" = "Die Datei ist ein Ordner";

/* WebFoundationErrorNoPermissionsToReadFile description */
"Err-1102" = "Sie sind nicht berechtigt, auf die angeforderte Ressource zuzugreifen.";

/* WebFoundationErrorDataLengthExceedsMaximum description */
"Err-1103" = "Ressource überschreitet die maximale Größe";

/* WebFoundationErrorSecureConnectionFailed description */
"Err-1200" = "Es ist ein SSL-Fehler aufgetreten. Eine sichere Verbindung zum Server kann nicht hergestellt werden.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201" = "Das Zertifikat für diesen Server ist abgelaufen.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201.w" = "Das Zertifikat für diesen Server ist abgelaufen. Möglicherweise werden Sie mit einem Server verbunden, der vorgibt, „%@“ zu sein, und Ihre vertraulichen Daten gefährdet.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202" = "Das Zertifikat für diesen Server ist ungültig.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202.w" = "Das Zertifikat für diesen Server ist ungültig. Möglicherweise werden Sie mit einem Server verbunden, der vorgibt, „%@“ zu sein, und Ihre vertraulichen Daten gefährdet.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203" = "Das Zertifikat für diesen Server wurde von einer unbekannten Zertifizierungsinstanz signiert.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203.w" = "Das Zertifikat dieses Servers wurde von einer unbekannten Zertifizierungsstelle herausgegeben. Möglicherweise werden Sie mit einem Server verbunden, der vorgibt „%@“ zu sein, und Ihre vertraulichen Daten gefährdet.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204" = "Das Zertifikat für diesen Server ist noch nicht gültig.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204.w" = "Das Zertifikat für diesen Server ist noch nicht gültig. Möglicherweise werden Sie mit einem Server verbunden, der vorgibt, „%@“ zu sein, und Ihre vertraulichen Daten gefährdet.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205" = "Der Server hat das Zertifikat nicht akzeptiert.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205.w" = "Der Server „%@“ hat das Zertifikat nicht akzeptiert.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206" = "Der Server benötigt ein Client-Zertifikat.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206.w" = "Der Server „%@“ benötigt ein Client-Zertifikat.";

/* WebFoundationErrorCannotLoadFromNetworkError description */
"Err-2000" = "Laden aus dem Netz nicht möglich";

/* WebFoundationErrorCannotCreateFile description */
"Err-3000" = "Datei kann nicht erstellt werden";

/* WebFoundationErrorCannotOpenFile description */
"Err-3001" = "Datei kann nicht geöffnet werden";

/* WebFoundationErrorCannotCloseFile description */
"Err-3002" = "Fehler beim Schließen der Datei";

/* WebFoundationErrorCannotWriteToFile description */
"Err-3003" = "Datei kann nicht geschrieben werden";

/* WebFoundationErrorCannotRemoveFile description */
"Err-3004" = "Datei kann nicht gelöscht werden";

/* WebFoundationErrorCannotMoveFile description */
"Err-3005" = "Datei kann nicht bewegt werden";

/* WebFoundationErrorDownloadDecodingFailedMidStream description */
"Err-3006" = "Decodierung der geladenen Daten fehlgeschlagen";

/* WebFoundationErrorDownloadDecodingFailedToComplete description */
"Err-3007" = "Decodierung der geladenen Daten fehlgeschlagen";

/* Recovery suggestion */
"Would you like to connect to the server anyway?" = "Möchten Sie die Verbindung zu dem Server trotzdem herstellen?";

/* Recovery suggestion */
"To change your proxy settings, open Network preferences, click Advanced, and then click Proxies. For help with this problem, contact your system administrator." = "Um die Proxy-Einstellungen zu ändern, öffnen Sie die Systemeinstellung „Netzwerk“, klicken dort auf „Weitere Optionen“ und dann auf „Proxies“. Wenn Sie Hilfe bei diesem Problem benötigen, wenden Sie sich an Ihren Systemadministrator.";

/* AuthBrokerAgent proxy credential user notification - alert header */
"Proxy Authentication Required" = "Proxy-Authentifizierung erforderlich";

/* AuthBrokerAgent proxy credential user notification - alert message, args are 1) proxy type, 2) host name, 3) port number, e.g.:  Authentication for HTTP proxy csd3.apple.com:8080 */
"Authentication for %@ proxy\n%@:%d" = "Authentifizierung für %@-Proxy\n%@:%d";

/* AuthBrokerAgent proxy credential user notification - Cancel button title */
"Cancel" = "Abbrechen";

/* AuthBrokerAgent proxy credential user notification - Username title */
"Username" = "Benutzername";

/* AuthBrokerAgent proxy credential user notification - Password title */
"Password" = "Kennwort";
