MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� =�g � �
  � & ( J   �     �        @                       �    k�  `                                             �  h     �   �  �            �                            |  (                   h�  �                          .text   xI      J                 `  `.data   @   `      P              @  �.rdata  �   p      T              @  @.pdata  �   �      n              @  @.xdata  l   �      r              @  @.bss    �   �                      �  �.idata  h   �      v              @  �.CRT    `    �      �              @  �.tls        �      �              @  �.rsrc   �         �              @  �.reloc  �         �              @  B/4                �              @  B/19     &7  0  8  �              @  B/31     p/   p  0   �             @  B/45     �A   �  B   
             @  B/57     X   �     L             @  B/70     �        `             @  B/81              d             @  B/97     @3   @  4   �             @  B/113       �     �             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H�Eq  1��    H�Fq  �    H�Iq  �    H��p  f�8MZuHcP<HЁ8PE  tfH��p  �
��  � ��tC�   �@  �d?  H��q  ���L?  H�}q  ���d  H�-p  �8tP1�H��(Ð�   ��?  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
Qq  �l!  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H�%q  L�֞  H�מ  H�
؞  � ���  H��p  D�H���  H�D$ ��;  �H��8��    ATUWVSH�� H�p  H�-��  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5�o  1�����V  �����  ��     ����L  ���e  H�o  H� H��tE1��   1����  H�
p  ��  H�do  H�
����H��E>  �  �ҝ  �{Hc�H��H���>  L�%��  H�Ņ��F  H��1�I��'=  H�pH���c>  I��H�D I�H��H���>  H9�u�H�H�    H�-]�  �  H�an  L�B�  �
L�  H� L� H�7�  ��>  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5�n  �   ���������   �?;  ��������H��n  H�
vn  �!=  �   �������1�H�����f�     �<  ���  H�� [^_]A\�f.�     H�Yn  H�
Bn  �   ��<  �7���f�H�����������<  �H��(H�um  �    ������H��(� H��(H�Um  �     �z�����H��(� H��(�':  H���H��(Ð�����������H�
	   �����@ Ð��������������H��(�A$��"wH��]  Hc�H���H���  A��H�
�]  H��(�:8  H�
�\  �;  H�
]  H��(�v;  H���  H�
�[  H��(�8  H�p�  H�
�[  H��(��7  H�Y�  H�
*\  H��(��7  H�B�  H�
[  H��(��7  H�+�  H�
<\  H��(�7  ff.�     AWAVAUATUWVSH��(L��L�ͅ�t&���E  ���  H��([^_]A\A]A^A_� H�
s]  1�A�$  �L7  �   L�-P�  A��H���m:  D�} L�%��  f.�     �;  �Ã�
wLI��r~��A�ԅ�uF��~��uܹ   ��A��A�   �   H�
]  I���:  �R;  �Ã�
v� ��A�ԅ�t�A�G�9�~	Hcƃ���   A�չ*   H����9  �t���@ Hcƹ
   � �u H��([^_]A\A]A^A_�9  @ M��H�n\  H�
�\  HD�H��H��([^_]A\A]A^A_�;6   H�
[\  �9  �   �1�  A�   H��H��H��([^_]A\A]A^A_�  VSH��(�ֺ/   H����9  H�PH��HEڅ���   H�5�  �   ��I��H�\  H���6  �   ��A��  �   H�
�[  I����8  �   ��A�o   �   H�
�]  I���8  �   ��A�2   �   H�
^  I���8  �   ��I��A�  �   H�
^  �|8  H�
'_  H��([^�z8  H�5+�  �   ��I��H�L[  H���R5  �   ��A��  �   H�
I[  I���)8  �   ��A�o   �   H�
 ]  I���8  �   ��A�2   �   H�
O]  I����7  �   ��I���E����%��  ���%z�  ���%j�  ���%Z�  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ڭ  ��UWVSH��H  �#   1�H�D$(    L�D$(�b�  �Å���   1�E1�L�$b  H�,b  H����  H�^�  H��H��t7H���6  �P�I����~%��H�D�H�A�H�H)��� H��H9�t�:\t�H����7  H��H��H  [^_]��     H�|$0H�L$(H�����  ���e���H��1��c6  H�Ņ���   �L@Hc��|7  I��H�ƍE�L�LD2�9@ ��f���� �
�K��?f���������?�A�HcшI��M9�tK�KA� Hc�L�Hc�H�f=�w�f��v����?��f�������@�
A��f�����f.�     Hc�� H�L$(�~�  ����f�     UWVSH��xE1��D$(    ��L��1�H�D$     H�l$0I��H��H�D$`    H���q  A��I��H��1��i  H��H��tH��A�����H���8  H���8  �H��x[^_]�fD  H���  H��t� �����ff.�     �
  ff.�     AWAVAUATUWVSH��8H��$�   I��H����  H�9 �  H��t�    �   ��5  H�
�  I�H���J  �  E1�1�1�H��L�%�_  H����    H��H�<�    �!  H����   H�XL��H��� 4  ��t�H�ٺ.   �75  H��H��t�H�{_  ��3  ��u�H����3  I�A�VI��H��H�@�H�D$(�35  H��H��tBI�I�M�H��5  H��H��t#L�D$(H����3  B�D(� H�E A�nI���I���H�E     �   ���  A�   �   H�
�^  I����3  @ H����"  H��$�    I�H�8    tH��$�   D�01�H��8[^_]A\A]A^A_�@ ����H�������������ff.�     �VSH��8H�ˉ�L��H�D$(    H�T$(L�D$$�D$$    �

  D�L$$H�ى�L�D$(�  H�L$(���4  ��u��H��8[^�D  � �����f�     VH��@H��L�D$4H�T$8H�D$8    �D$4    �@  ���D$,x0H�    �T$4I��H�L$8�  H�L$8�3  H�> t"1�H��@^�H�L$8�v3  �D$,���t�=v���t��������    �����H��@^�ff.�     f�H��(��
  ��uH��(�fD  � �����AWAVAUATUWVSH��   L�2L�jH��$�   M����  H���|  �?  H����
  H����
  H�ٺ  H���  E1�I��H��   ��
  E1�I�غ   H���
  H����
  ��
  I��   H��H����
  �
  I��   H��H���
  �4
  H���  1�H��H����
  H��H����	  H����
  �   H����	  L�p\  �W   H���[����
  L�%��  1�H��A��H��H���
  H��H���	  1�A��H��H�� ��m
  H��H���	  H���j
  H��H���w	  ��	  H��H��I���L	  �w	  H���O
  1�I��H���:
  L��H���7	  L���7
  �   H���	  L��[  �W   H������L��[  �S   H��������	  1�I��A��L��H����	  L��H����  1�A��L��H�� ��	  L��H����  L���	  H��H���  � 	  H��H��I���  H��tH��tH��t	H���B  �D$d    E1�H�D$P    �D$h    �D$\    H�D$@    �D$`    H�D$H    D��L��HǄ$�       ��  E1�E1�H��$�   I��H���  L����  �  H��$�    I���  H���  ��  1�I��H����  L��L����  L����  �   L���  L�TZ  �W   L���.����  1�I��A��L��H���f  L��L���  1�A��L��H�� ��G  L��L���t  L���D  ��  L��$�   �   I��H����  L��L���4  L���  L�Z  �R   L������L��Y  �S   L�������w  H��L��I����  ���  E1�E1��&  L����  H���V  H���N  H�ٻ������  H����  H�|$@ �  D�D$\E���  H�|$H �  �L$`����   H�|$P ��   �T$d����   M����   �D$h����   M����   E����   D��L��1��s  H��$�   H�Y  I���  �T$hL���P  H��$�   H��X  I���q  �T$dH�L$P�+  H��$�   H��X  I���L  �T$`H�L$H�  H��$�   H��X  I���'  �T$\H�L$@��  H��$�   H��X  I���  f�L����-  H�L$H�-  H�L$@�-  L���-  H�L$P�-  ��H�Ę   [^_]A\A]A^A_�@ ��  H����  H��H��I���  �D$\    H�D$@    ����  �  H���  E1�E1�H��H�D$0    H��I��H�D$(    �D$     �9  �D$`    H�D$H    ����  �c  H���k  H��H��I����  �D$d    H�D$P    ���<  �/  E1�H���4  E1�E1�H��H�D$0    H��H�D$(    �D$     H�D$p�  �D$h    �������H�L$pL��$�   E1��   ��  A���D$hL��L�D$x�V,  I��H��tL�D$xH��$�   H����+  H�L$p�  ����f��  H���  L��H��H�D$pI���  �������L��L��$�   E1��   HǄ$�       �\  A��L��L�D$xM����+  I��H��tL�D$xH��$�   H���l+  H�L$p�  �j���D  L��$�   E1��   L���   A���D$dL��L�D$h�l+  H�D$PH��H��tL�D$hH��$�   �
+  L���  �l����     L��$�   E1��   L���  A���D$`L��L�D$P�+  H�D$HH��H��tL�D$PH��$�   �*  L���]  ������     L��$�   E1��   L���@  A���D$\L��L�D$H�*  H�D$@H��H��tL�D$HH��$�   �M*  L����  �$����     ����������fD  VSH��(H��H�?U  �  H��H��t
H���C  ��t1�H��([^�D  H��H���  1�H��([^�@ SH�� H��H����  H��T  H��I���  1�H�� [�fD  WVSH��0L��H����   M����   H�D$     H�D$(    ��  H��H��t
H���  ��t H�L$ H��t�)  �����H��0[^_�@ H�T$ L�D$(H���v  H�\$(H�K�`)  H�|$ I��H�H��H���
)  H��� ���^�0)  1�H��0[^_�fD  ������f�     M��t�&���fD  1��ff.�     f�M��t����fD  1��ff.�     f�VSH��(H��H��H��t/M��t*A�PI��  H��H��I����  1�H��([^��    ������됐��������%ʢ  ���%��  ���%��  ���%��  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%Ҟ  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%ҝ  ���%  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���     �%B�  ���%2�  ���%"�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%Ҡ  ���%   ���%��  ���%��  ���%��  ���%��  ��H��(H��6  H� H��t"D  ��H��6  H�PH�@H��6  H��u�H��(�fD  VSH��(H�cV  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^�S��� 1�fD  D�@��J�<� L��u��fD  �j�  ��t�D  �V�     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�sU  �8t�    ��t��tN�   H��([^�f�H��  H�5ڴ  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H�|Q  Hc�H����    H�`P  �DA �y�qH�q�   �#  �DD$0I��H�
Q  �|$(H��I���t$ �   �t$@|$P1�DD$`H��x[^ÐH��O  ��    H�)P  ��    H��O  �s���@ H�YP  �c���@ H�!P  �S���H�sP  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�"  A�   �   H�
rP  I����"  H�t$(�   �"  H��H��I���M  �@#  ��    WVSH��PHc5F�  H�˅��  H�8�  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��  H��H��H�H�x �     �#  �WA�0   H�H���  H�T$ H�L�_�  H���}   �D$D�P����t�P���u��  H��P[^_� ��H�L$ H�T$8A�@   �   DD�HU�  H�KI��H�S���  ��u����  H�
�O  ���d���@ 1��!���H��  �WH�
8O  L�D�>���H��H�
O  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%ā  E��tH�e[^_A\A]A^A_]�fD  ���     �9	  H�H��H��   H����  L�-�Q  H��Q  �n�      H)�H�D$0H�c�  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5NQ  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
DN  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��  ������H�5s�  1�H�}�D  H��  H�D� E��t
H�PH�HI����A��H��(D;%�  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5 O  �s�;H��L�>H���Z����>L9�r��������H�
]L  �����H�
L  ���������H��XH��~  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
I~  �T  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H�<L  Hc�H��� 1ҹ   ��  H���>  H���  H��}  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �q  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   �  H���@����   �   ��  �f�     1ҹ   ��  H��t*H�������   ���i���f�     �   ���T����   �   �  �@����   �   �  �,����   �   �u  �����������ATUWVSH�� L�%�|  L���Ɠ  H��|  H��t6H�-�  H�=ē  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%��  WVSH�� �K|  ��H�օ�u
1�H�� [^_ú   �   �	  H��H��t3H�pH�5.|  �8H���#�  H��{  H��H��{  H�C�0�  묃��멐VSH��(��{  �˅�u1�H��([^�D  H�5�{  H���В  H�
�{  H��t'1��H��H��tH���9�H�Au�H��tH�B�m  H�����  1�H��([^� H�a{  ��ff.�     @ SH�� ����   w0��tL�>{  ����   �,{     �   H�� [�f�     ��u�
{  ��t��<�����f.�     ��z  ��uf��z  ��u�H��z  H��t�    H��H�[�  H��u�H�
�z  H��z      ��z      ���  �l����k����   H�� [������f�     H�
�z  ���  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���q  H��w{H��I  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H���  ��u�H��H�� [^_��    1�H��H�� [^_� H�I  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��H  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�YH  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��G  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��G  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L�	G  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������UWVSH��8  H���]  �     H����  �; �M  H����  �����  ��3  H�t$ H��A�  H����  H���]  H��K  H��H���  H��H���g  L��D  H�C����   H�L$ H��L  H���H��D  ��L�L�M�L�L��H)�H)�����H���D   t��C  ��\t	��/��   �*   �    fA�(L��0  1�Hǂ8  ����L��ǂ@      Hǂ(      �H�H���    H��8  [^_]�D  ���   ���y����L$ A���i�����D�L�fE�L��V���D  ��  1��    H��H��8  [^_]�@ �\   H��fA��>���fD  �  1��    �Ɛ�  1��    붐�L$ A���D�L�E�L�������  1��    �f�     WVSH��P  H���^  �     H����  ��@  ���  H�T$ ��   H��D  ��  H��H�����   �D$ �oD$(H�s$H�T$HA�  H��H�D$8CH�CH�D$@�C �E  H��8  ǃ@     H���  H��0  H��f��.  ��  H��(  H��P  [^_�@ H��8  �T  �ǃ����   �D$ �oL$(H�s$H�T$HA�  H��H�D$8KH�CH�D$@�C �  ����   ��@  ����@  ���^���1�H��P  [^_�D  H�{H��1�H�    Hǃ       H���H)���(  ���H�Hǃ8  ����ǃ@  ����� H�{H��1�H�    Hǃ       H���H)���(  ���H��|�  ��t?H��8  �C  Hǃ8  ����ǃ@  �����G���f�     �k  �    �.����[  �     � VSH��(H���B  �     H��t'H��8  1�H���t��  ��H���  ��H��([^��  ������    ��ff.�      SH�� H����  �     H��t8H��8  H���uHǃ8  ����ǃ@      H�� [��[  ��f�     �  �    �� SH�� H���  �     H��t��@  H�� [��g  �    �������f.�     VSH��(H�ˉ��@  �     H����   �����   u6H��8  H���t��  Hǃ8  ����ǃ@  ����H��([^��     ��  H��8  �     H���uHHǃ8  ����1�ǃ@      ��    H���@���H��t���@  9��H��([^�f.�     �;  �f�     �{  �    H��([^�fD  �c  �    �Y�����������AUATUWVS��D�Ɖ�L��)�)։�������   �� A�ՙA��D���u�D��)șA��E��~lHc�A��A�L�׉څ�~Yf�     M�1��f�     M��A��D�2A)�9�D��AL҃�Lc�N��M�M�M�9�u�A��I��E9�tD��뱐[^_]A\A]�A���u���ff.�     @ WVSH��0H�t$XL�L$hH��H�T$XL�D$`H�t$(�   �   H� H�8�
  H�!=  I��H����
  �   �
  I��H��H���^
  �   �
  �
   H���
  �H��0[^_�ff.�      AWAVAUATUWVSH��H��  H�=�  H��$�   H��M��H��$�   �=   �D$<��L��$�   �D$8��  ��
  I��H���  H��I��H)�M�4$M����   1�L�D$0I�\$ E1�D��$�   �L$(�����A���*Hc�H��L��P9S�tV�D$(   L�3H�� A��M��tfI��L��H���p  ��u�L���\  H9���   H��uE��u��u�D���fD  H�PH9S�u��@9C��   DD$(�D$(��     �L$(L�D$0��uKA����ux��$�   ���  ��  ���.  �|      �?   H��H[^_]A\A]A^A_�fD  D�
a  E��t�H��$�   �8:t�I����H�
�;  �����@ L�D$0Ic�H��L�k��uRM��teD�  E��tH��$�   �8:��   H�{ �l  H��$�   �-�  �8:�Z����:   �U���@ �U���wM����   L��l  H��$�    tH��$�   D�8H�S�CH�������1��
���f�     H��H�D$(��
  L�D$(H�������fD  H��$�   �8:�����H��H�
U:  ���������I����H�
�:  �����%������d���HcT$8H��$�   D�t$<H��A��D�5�  H�$l  H���3����
�  ��t)H��$�   �8:tH��H�
�:  �E�����  ���D$81�H�{ tB��  �D$8��  H��$�   �8:�
��������k�����D$<�s  �����������C��     AWAVAUATUWVSH��HD��$�   A��H��M��M��M���y  D�
(  �  E����  �����  �Fk  ����  A�E <-�  D��  E����  <+��  H�k      ����   ��  ������  ����f.�     ��j      ��  D9���  Hc�H�t� �>-��  H��8  H�h  A����  A����  �5F  ����5  �=3  ���t%��I��A�؉������)����  ����)��
  ���  H�=  ����Z���M��tkHc�  H9|� t]<-�r  A���w  <:�k  ��L���2	  H�������D$ M��L��H��L��$�   �����Ã����  H�=�  �L���L�=|  <:�,  ��-��   ��L����  H���  �PM��t��Wu	��;��  ��:��   � ��  �3  �}  f��^i     �     1�E1�H�
�6  ��  �<i  ��������  A�E <-�����A��I�������@ �F����  �-   L���  H���0����=�  �t
�=�  ���  L�~H���-   L�=}  � �8  �-   L����  H����   �x:�%���H��h      � �J  ��  L�=qh  H�B6  ��H�   �&  �s�     �  �����D  A���<+�#��������    �5�  H��5  �=�  H��  ���uP���t�=�  ��  ������  �����������H��H[^_]A\A]A^A_�f���H�5�g  ��  �   �ԉ�)�A��I���)��&����l  �L���L�=P  <:������ u�G  �
E  ��tA�} :��   �(  �?   �q���fD  A����2����
�  ���t
�=�  ��  H�~H�=�  <-������~ ������5�  ��H��4  ��  H��  ��������A�؉�I��L$<�[����L$<)�)��  ������     ��H�
�4  �����G���H��H�=b  1������ ��   �W  ���N  D9�|e�G  H�H4  H�)  ��tA�} :t�W   H�
"5  �����  W   A�} :������:   �I�����  �S�����  �����H�H�D� H��  �D$     M��L��H��L��$�   �������H��3  H��  ������x:�\�������  A9�~Hc�H�D� H��e  �:���H�|3  H�]  �g  ��tA�} :t��H�
S4  ������@  A�} :�
����0����     H��8E1��D$(    H�D$     ����H��8�ff.�      H��8H�D$`�D$(   H�D$ ����H��8�H��8H�D$`�D$(   H�D$ �e���H��8�H��8E1�L�D$ I��H��1��/  H��8Ð�VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �  � ��  H� H��  H� H�M��t	A�$�#  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���  ���   ����H  �  � ��"  H� H��  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H���  ���    HD�H�� [�f�H�)3  �8 t1�Ð��  ff.�     SH�� �˹   �?  A��H�2  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8�  H��H�ff.�     H��(H�E2  ��~   H��  �j   H��  �V   H��  H��(�f.�     H��(H�2  ��>   H��  �*   H��  �   H��  H��(Ð����������%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�z  ���%�y  ���%�y  ���%�y  ���%�y  ���%�y  ���%�y  ���%�y  ���%�y  ���%�y  ���%�y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%y  ���%Zx  ���%Zx  ���%Zx  ���%Zx  ���%*x  ���     �%�w  ���%�w  ���%�w  ���%�w  ���%�w  ���     �%�w  ���%�w  ���%�w  ���%�w  ���%Zw  ���%Zw  ���%:w  ���     �%w  ���%
w  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%�v  ���%zv  ���%jv  ���%by  ���%Ry  ��AWAVAUATUWVSH��hE1�E1�L�%#  H�-#&  H�=�(  ��H�������H�D$0    H�D$8    H�D$@    H�D$H    M��I��H�ډ�H�D$     �.��������  ����   ��d��w&Hc�H���   �pw  I��H�N&  H������H��   �A����   H�L$0�����H�L$8�����H�
�]  �L�����H��h[^_]A\A]A^A_�L�#%  H�/%  1�H�
2%  ������L�=�/  I��8 ��  H�
A]  �����I�����H�-]  ����H�1�����1������   �M��������H�A/  L�8A����T  <@tg<{��  L���^���L�D$HL����������������   �\v  A�I   �   H�
�#  I���z��������A�   �{���A�   �p���I�WH�L$H����H�|$H �V���H��.  �   H���u  H�A#  H��H��I���"�������H��.  Hc 9���   E���"  H�4�H�J$  H����������   H�8$  H���h������'  H�*$  H���Q�������  H�$  H���:������F  H�$  H���#������u  H��#  H�������¸   ���m  E��EE����  E1�H�L$XL�T$X����H�L$X�I���H�L$X����1������   ��t  A�&   �   H�
#  I������H��   �����h����   E����   �����$����   A�   H��Z  H�L$8�+������  H�=�Z   ��  ����  H�\$0H�L$8L�w"  H�����������  H�L$0H�T$@�������  H�t$@H�
�#  H�����������  H���(������O  ����  H�L$0E����  1�L�L$HE1�H�T$ H�"����M�������  ���>����h����   A�   E�����������   ��s  A�*   �   H�
�!  I������H��   �^��������   ����H��Y  H����  H�
`"  �3���������   �3s  A�.   �   H�
A   I���Q���������   �	s  A�   �   H�
�  I���'���H��   �ҿ�������   ��r  A�H   �   H�
�   I��������b����   �������H�
 "  �����E���1�H�T$P1�H�L$PH�L$XH�
�X  ����H�L$PH�T$X�(���H�L$X����H�L$X�t���H�L$P����������������E1�E1�H�T$P1�H�L$XL�D$XD�L$P�+�����ރ�H���S���H�D$XH������H�L$X;\$Pr������H�L$8H�AX  �������e���H�
!  �����p���H�
�   ������_���H�L$0�>���H�L$81�H��L��  H�D$0��������u3H��W  H�
�!  1��`�������H��H�
Y!  �L���H�t$@�B����ͻ�������H�L$01�蜿������u�H��W  H�
�!  1�����������   �������H�
�   ���������1��p������+���H�IW  H�
!  1�����������   ����H�L$0�2����ȹ   ��p  I��H�k  H�������H��   膽���@���������������������������@Y @           ��������                                                                                                                                                �z @                   h       �z @                  u       �z @                   w       �z @                   n       �z @                         �z @                   d       �z @                   v                                       pY @           ��������        ����                           ������������    h @   ?                     ����            �L @           �L @           M @           �� @   �� @   �N @   �N @   PM @   �N @   �M @   `M @   b @   b @   b @      �p  $b @    b @   PDT PST `N @   @N @                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ERROR: Could not validate with device %s because a passcode is set. Please enter the passcode on the device and retry.
 ERROR: Device %s is not paired with this host
  ERROR: Please accept the trust dialog on the screen of device %s, then attempt to pair again.
  ERROR: Device %s said that the user denied the trust dialog.
   ERROR: Pairing with device %s failed.
  ERROR: Pairing is not possible over this connection.    To perform a wireless pairing use the -w command line switch. See usage or man page for details.        ERROR: Device %s returned unhandled error code %d
  B���(���(���(���(���(���(���(���(���(���(���(���(���(���(���^���(���u�����������(���(���(���B���(���(���(���(���(���(���(���(�������(���^���(unknown) Enter PIN:    Device info: ERROR: %s
 Usage: %s [OPTIONS] COMMAND
  
Manage host pairings with devices and usbmuxd.

Where COMMAND is one of:
  systembuid   print the system buid of the usbmuxd host
  hostid       print the host id for target device
  pair         pair device with this host
  validate     validate if device is paired with this host
  unpair       unpair device with this host
  list         list devices paired with this host

The following OPTIONS are accepted:
  -u, --udid UDID  target specific device by UDID
          -w, --wireless   perform wireless pairing (see NOTE)
  -n, --network    connect to network device (see NOTE)
   -d, --debug      enable communication debugging
      
NOTE: Pairing over network (wireless pairing) is only supported by Apple TV
devices. To perform a wireless pairing, you need to use the -w command line
switch. Make sure to put the device into pairing mode first by opening
Settings > Remotes and Devices > Remote App and Devices.
 PhoneCheck 2.0        ERROR: UDID must not be empty!
 ERROR: --hostinfo argument must not be empty!
  ERROR: Could not read from file '%s'
   ERROR: --hostinfo argument not valid. Make sure it is a JSON dictionary.
       ERROR: --hostinfo argument not valid. To specify a path prefix with '@'
 1.3.0-225-gfefa2f3 idevicepair %s %s
 hu:wndv  ERROR: You need to specify a COMMAND!
  ERROR: You cannot use -w and -n together.
 pair validate unpair list hostid systembuid  ERROR: Invalid command '%s' specified
  ERROR: Command '%s' is not supported with -w
   No device found with udid %s.
 No device found. ERROR: Could not get device udid, error code %d
        ERROR: Could not connect to lockdownd, error code %d
   QueryType failed, error code %d
 com.apple.mobile.lockdown      WARNING: QueryType request returned '%s'
       SUCCESS: Paired with device %s
 SUCCESS: Validated pairing with device %s
      SUCCESS: Unpaired with device %s
   ��������������������������������'���������������������i���K���2���help udid wireless network hostinfo debug version   Apple\Lockdown \ SystemConfiguration.plist .plist ERROR: Out of memory
 critical,CA:TRUE critical,CA:FALSE      critical,digitalSignature,keyEncipherment hash DeviceCertificate HostPrivateKey HostCertificate RootPrivateKey RootCertificate HostID                           @+ @                            � @   � @   �� @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  H��������������,���<������Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      г��г��г��г��г��P���г������P���+���                        %s:     P O S I X L Y _ C O R R E C T           unknown option -- %s            unknown option -- %c                            option doesn't take an argument -- %.*s         ambiguous option -- %.*s                        option requires an argument -- %s                               option requires an argument -- %c                               runtime error %d
               0a @           pa @           PY @              @           �� @           �� @            | @           �a @           �� @           �� @           �� @           �� @           �� @            � @           � @           `� @           h� @            � @           � @           � @           (� @           �� @            a @           а @           �2 @           �+ @           p� @           �� @           \a @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                                                                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P    t�     �  |�  �  0  ��  �  '  ��  0  �  Ƞ  �  �  ؠ  �  �  ܠ  �  �  �  �    ��    �  �  �  �  �  �  �&  �  �&  �&  ,�  �&  
'  8�  '  �'  @�  �'  �'  L�   (  (  P�   (  g(  T�  0*  j*  `�  p*  �*  h�  �*  �*  t�   +  +  x�  +  ?+  |�  @+  �+  ��  �+  �+  ��  �+  �,  ��  �,  �,  ��  �,  Y-  ��  `-  �.  ��  �.  -2  ȡ  02  n2  �  p2  |2  �  �2  =4  �  @4  �4  ��  �4  5  �   5  �5  �  �5  �6  �  �6  �6  $�  �6  07  (�  07  �7  ,�  �7  P8  8�  P8  �8  <�  �8  9  @�  9  F9  D�  P9  �9  H�  �9  �:  L�  �:  �<  P�  �<  
?  `�  ?  b?  p�  p?  �?  |�  �?  @  ��  @  A  ��  A  �A  ��  �A  bB  ��  pB  �E  ��   F  (L  ̢  0L  RL  �  `L  �L  �  �L  �L  ��  �L  �L  ��  �L  M  �  M  BM  �  PM  SM  �  `M  �M  �  �M  :N  ,�  @N  ^N  <�  `N  uN  D�  �N  �N  H�  �N  �N  P�  �N  &O  X�  0O  fO  `�  �Q  ?Y  ��  @Y  EY  h�                                              B   b  
 
20`pP�	 B  �P     �  �  �2  �  	 B  �P     �    �2     B         B  	 B0`
p	P����   B0`  	 �0`
p	P����   I 0`pP �0`pP        	 b0`
p	P����   b0`   r` B  
  0`
p	P���� B0`   20 R0`p       B0`   B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   ' 0`pP
 
* 0`p   B0`   20 20 B0`   0`pP�� R0`p	 �0`
p	P����  	 �0`
p	P����   b   b   b   b   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                         ��          p�  h�  ��          �  ��  �          <�  ��  0�          ��  �  ��          ��  ��  ��          ��  ��  ��           �  ��  ��          <�  ��  �          `�   �  (�          ��  �  P�          ��  8�  ��          H�  ��  @�          ��  (�  ��          ��  p�  ��          ��  ��  ��          �  ��  (�          8�  �  @�          T�  (�                          P�      `�      t�      ��      ��      ��      ��      �      $�      8�      L�      d�              x�      ��      ��      ��      ��      ��      ��      ��       �      �       �      ,�      8�      D�      T�      h�      x�      ��      ��      ��      ��      ��      ��      �       �      8�      L�      d�      t�      ��      ��      ��      ��      ��      ��      ��              �      4�      P�              `�      x�      ��      ��      ��      ��      ��      �      "�      *�      8�      J�              Z�              d�      t�              ��      ��      ��      ��              ��      ��      ��      ��      ��              ��              �      �      (�      2�              <�      J�      X�      f�      p�      ��      ��      ��      ��      ��      ��      ��      �      &�      6�      X�      `�      h�              r�      ��      ��      ��      ��      ��      ��      ��      ��      ��              �      �      �      $�      .�      8�      B�      L�              V�      d�      r�      ~�      ��              ��              ��      ��      ��      ��      ��      �      $�      <�      T�      h�      |�              ��      ��              ��      ��      ��      �              P�      `�      t�      ��      ��      ��      ��      �      $�      8�      L�      d�              x�      ��      ��      ��      ��      ��      ��      ��       �      �       �      ,�      8�      D�      T�      h�      x�      ��      ��      ��      ��      ��      ��      �       �      8�      L�      d�      t�      ��      ��      ��      ��      ��      ��      ��              �      4�      P�              `�      x�      ��      ��      ��      ��      ��      �      "�      *�      8�      J�              Z�              d�      t�              ��      ��      ��      ��              ��      ��      ��      ��      ��              ��              �      �      (�      2�              <�      J�      X�      f�      p�      ��      ��      ��      ��      ��      ��      ��      �      &�      6�      X�      `�      h�              r�      ��      ��      ��      ��      ��      ��      ��      ��      ��              �      �      �      $�      .�      8�      B�      L�              V�      d�      r�      ~�      ��              ��              ��      ��      ��      ��      ��      �      $�      <�      T�      h�      |�              ��      ��              ��      ��      ��      �              f idevice_free  j idevice_get_udid  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new  � lockdownd_client_new_with_handshake   � lockdownd_cu_pairing_create   � lockdownd_pair    � lockdownd_pair_cu � lockdownd_query_type  � lockdownd_unpair  U ASN1_INTEGER_free Z ASN1_INTEGER_new  [ ASN1_INTEGER_set  � ASN1_TIME_free    � ASN1_TIME_new � ASN1_TIME_set WBIO_ctrl  wBIO_free  �BIO_new   �BIO_new_mem_buf   �BIO_s_mem DBN_free   �BN_new    �BN_set_word   EVP_PKEY_assign   'EVP_PKEY_free �EVP_PKEY_new  �	EVP_sha1  F
PEM_read_bio_RSAPublicKey �
PEM_write_bio_PrivateKey  �
PEM_write_bio_X509    �RSA_generate_key_ex   RSA_new   �X509V3_EXT_cleanup    �X509V3_EXT_conf_nid   �X509V3_set_ctx    X509_EXTENSION_free   wX509_add_ext  �X509_free �X509_new  �X509_set1_notAfter    �X509_set1_notBefore   �X509_set_pubkey   �X509_set_serialNumber �X509_set_version  �X509_sign  plist_print_to_stream_with_indentation     plist_read_from_filename  0 string_concat DeleteCriticalSection =EnterCriticalSection  KGetEnvironmentVariableW TGetFileAttributesA  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   _getch   __p__environ   __p__wenviron  _findclose  
 _findfirst64   _findnext64  _fullpath  _set_new_mode  calloc   free   malloc   realloc 
 __setusermatherr   __C_specific_handler  xmemcpy  |strchr  }strrchr  __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit # _errno  % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf  � fflush  � fputc � fwrite  � putchar � puts  6 _strdup � isprint � strcmp  � strcpy  � strlen  � strncmp � strncpy � wcslen  	 __daylight   __timezone   __tzname  7 _time64 < _tzset  � CoTaskMemFree    plist_dict_get_item    plist_dict_set_item    plist_free      plist_from_json   ! plist_from_memory % plist_get_data_val    ( plist_get_node_type   , plist_get_string_val  6 plist_new_data    ; plist_new_string  J plist_to_bin  � SHGetPathFromIDListW  � SHGetSpecialFolderLocation     usbmuxd_delete_pair_record     usbmuxd_read_buid 
 usbmuxd_read_pair_record   usbmuxd_save_pair_record_with_device_id    �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  libcrypto-3-x64.dll (�  (�  (�  libimobiledevice-glue-1.0.dll   <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  KERNEL32.dll    P�  api-ms-win-crt-conio-l1-1-0.dll d�  d�  api-ms-win-crt-environment-l1-1-0.dll   x�  x�  x�  x�  api-ms-win-crt-filesystem-l1-1-0.dll    ��  ��  ��  ��  ��  api-ms-win-crt-heap-l1-1-0.dll  ��  api-ms-win-crt-math-l1-1-0.dll  ��  ��  ��  ��  api-ms-win-crt-private-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    �  �  �  �  �  api-ms-win-crt-time-l1-1-0.dll  �  ole32.dll   ,�  ,�  ,�  ,�  ,�  ,�  ,�  ,�  ,�  ,�  ,�  libplist-2.0.dll    @�  @�  SHELL32.dll T�  T�  T�  T�  libusbmuxd-2.0.dll                                                                                                                                                                  0 @                    @                   @+ @   + @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          P     X�   `  @    � �@�`������� �P�������������ȡСء����� ���(�0�   p      � �(�0�8�   �  D   ��Р�� �� �0�@�P�`�p�����������С�� �� �0�@�P�`�p���   �     � �8�@�                                                                                                                                                                                                                                                                                                                                            ,               @   $                      <    �&       P @   �      �Q @                         ,     O       � @   �                      ,     �       0* @   �                           a�                           c�                       ,    �        + @                              q�                       ,    ��       + @   �                           ¢                           L�                       ,    Ǥ       �+ @   �                       ,    ��       �, @                              ��                       ,    &�       �, @   =                      ,    q�       02 @   L                           f�                       ,    ��       �2 @   �                      ,    ��       @4 @   b                          ��                           +�                       ,    ��       �6 @   �                      ,    ��       �: @                         ,    3       A @   �                          o                      ,    
      �L @                          ,    �      �L @   H                       ,    ]      M @   2                           �                      ,    �%      PM @                                                                                                                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   e(   �  *GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden   �  �           9  char {   
size_t #,�   long long unsigned int long long int short unsigned int int long int unsigned int _iobuf !
"  
_Placeholder #"    +
FILE /�   unsigned char double float long double 	�   optind �   optarg �  	{   �  option  >�  
name @�   
has_arg A�   
flag Bd  
val C�    �  	�    �  �   G-  no_argument  required_argument optional_argument  long unsigned int 	�   signed char short int _Float16 __bf16 !JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �K  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	�  "tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   !VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
uint32_t (�   
plist_t 	Y"  �   	|�  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ 
plist_err_t 	�1  �   
6�  USERPREF_E_SUCCESS  USERPREF_E_INVALID_ARG USERPREF_E_NOENT ~USERPREF_E_INVALID_CONF }USERPREF_E_SSL_ERROR |USERPREF_E_READ_ERROR {USERPREF_E_WRITE_ERROR zUSERPREF_E_UNKNOWN_ERROR �~ 
userpref_error_t 
?�  �   'z  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y 
idevice_error_t 0�  #  2 �  $  
idevice_t 3�  	�  "idevice_options �   9(	  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  �   $
  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ 
lockdownd_error_t P(	  #/  R)3  $/  
lockdownd_client_t S#S  	'  lockdownd_pair_record (U�  
device_certificate V�   
host_certificate W�  
root_certificate X�  
host_id Y�  
system_buid Z�    
lockdownd_pair_record_t ]'  	X  �   h�  LOCKDOWN_CU_PAIRING_PIN_REQUESTED  LOCKDOWN_CU_PAIRING_DEVICE_INFO LOCKDOWN_CU_PAIRING_ERROR  
lockdownd_cu_pairing_cb_type_t l  
lockdownd_cu_pairing_cb_t q�  	�  ,�  �  "  "  B   udid !�  	0� @   strrchr 
]�     �  �    -getch �   fputc ��   M  �   M   	$   M  isprint ~�   q  �    plist_print_to_stream_with_indentation <�  !  M  �    fflush s�   �  M   idevice_free �z  �  �   lockdownd_unpair x
    8  �   lockdownd_client_new_with_handshake �
  R  �  R  �   	8  lockdownd_client_free �
    8   lockdownd_pair B
  �  8  �   lockdownd_pair_cu �
  �  8   lockdownd_cu_pairing_create �
    8  �  "  !  !   lockdownd_query_type �
  :  8  K   lockdownd_client_new �
  k  �  R  �   plist_free 	�
�  !   pair_record_get_host_id 
P�  �  !  K   userpref_read_pair_record 
C�  �  �  �   	!  idevice_get_udid �z    �  K   idevice_new_with_options �z  G  G  �  �   	�  userpref_get_paired_udids 
V�  }  }  B   	K  userpref_read_system_buid 
B�   �  K   strcmp 
?�   �  �  �   getopt_long M�   �  �   �  �    d   	�  	�  printf ��   #  �  % idevice_set_debug_level lI  �    plist_from_json 	��  v  �    �   strlen 
@�   �  �   fprintf ��   �  R  �  % plist_read_from_filename 8�   �  �  �   strdup 
l�  �  �   .free 
  "   __acrt_iob_func ]M  /  �    /exit � C  �    0main ��   �Q @         �X   argc ��   �  �  argv �K  }  M  c ��   U  M  longopts �X   	 ` @   client �8  ��device ��  ��ret �z  �  x  lerr �
  $    result ��   �  v  type ��  ��use_network ��   t  p  wireless_pairing ��   �  �  host_info_plist �
!  ��cmd ��  �  �  �   ��  OP_NONE  OP_PAIR OP_VALIDATE OP_UNPAIR OP_LIST OP_HOSTID OP_SYSTEMBUID  
op_t ��  op ��    �  1leave ��R @   &�T @   &       �  systembuid D	�  ���T @   �  l  R�� �T @   �'  �T @   �   &�W @   O         2i P�   �  �  udids Q
K  ��count R�   ���W @   L  �  R��Q�� �W @   �'  �W @   �   3�   �  pair_record q!  ��hostid r	�  ��YW @   �  [  Q�� hW @   �  t  Q�� rW @   �'  |W @   �  �W @   k   �Q @   
(  2R @   �  �  Rt Qs Xv Y| w 0 `R @   
  �  R2 rR @   �    Q	�x @   Xt  R @   %  =  Q1z   �R�   s  �R @   W  �R @   �  �R @   �  �R @     �  R	 x @   Q	�w @   X	�w @    �R @   �  �R @   �  S @   %  �  Q0z   t �   s  S @   /  �  R0 #S @   #    R1 RS @   v  $  R  aS @   I  C  R X�� tS @   
  Z  R2 �S @   (  �  R	Hw @   Q1XI �S @   �  �  R��Q �S @   
  �  R2 �S @   �  �  Q	 w @   Xs  !T @   �    Rt Q	cx @    8T @   �  )  Rt Q	hx @    OT @   �  N  Rt Q	qx @    fT @   �  s  Rt Q	xx @    }T @   �  �  Rt Q	}x @    �T @   �  �  Rt Q	�x @    �T @   
  �  R2 U @   (  �  R	x @   Q1X& U @   %  %  Q1z   t �   s  MU @     >  R�� �U @   :  c  Qs X	�w @    �U @     |  Q�� �U @   �  �  R	�y @   Qt  �U @   �  �  Rt  V @   �  �  Q	  @   X0w 0 V @   �   ;V @   
    R2 UV @   (  1  R	8x @   Q1X* bV @   %  X  Q1z   t �   s  �V @     w  R	�x @    �V @   
  �  R2 �V @   (  �  R	�v @   Q1X. �V @   
  �  R2 �V @   (  �  R	�v @   Q1XO �V @   %    Q1z   t �   s  �V @   
  6  R2 W @   (  `  R	�w @   Q1XH 5W @       R	Py @    �W @   �  �  Q	0� @    
X @     �  R	y @    X @   �'  �  R	y @    *X @   W  EX @       Qs X	�w @    `X @     -  R	z @    tX @     R  R	�y @   Qt  �X @   �   �X @   �  v  Q0 �X @     �  R	Hz @    �X @     �  R	�y @    �X @     �  Q0 �X @     �  R	�y @    Y @   �  Y @   
     R2 -Y @   �  3   Q	�x @   Xt  :Y @   %  Q1z   �R�   s   4�  h   5�    'print_usage ��   argc ��   argv �*K  is_error �4�   name ��   6print_error_message b
P @   �       ��!  err b3
  �  �  � @     !  R	r @   X�R � @   �'  5!  R	pq @    � @   �'  T!  R	�q @    � @     s!  R	xp @    � @     �!  R	�p @    � @     �!  R	q @    � @     �!  R	 p @     @     R	Hq @     'pairing_cb O?"  cb_type O7�  user_data OF"  data_ptr OW"  data_size OoB   7get_hidden_input 8�   �"  buf 8#�  maxlen 8,�   pwlen :�   c ;�    (�!    @   �      �%  �!  X  L  "  �  �  "  �  �  -"  I  9  )�!  ` @     h   O
�$  �!  �  �  "  �  �  -"  �  �  "  �  �  )?"  � @     {   U2$  h"  �  �  ]"  	  
	  8{   v"  &	  	  �"  b	  V	  � @      � @   2(  �#  Rs  � @   
  �#  R2 � @   (  �#  R	�r @   Q1X3 � @      � @   2(  $  Rs   @   
  $  R2 ' @   /  R*   t @     Q$  R	�r @    � @   
  h$  R1 � @   �  T @   M(  R:  � @     �$  R	�r @   Q�X�r @   �X0.(  � @   �'  �$  R	�r @    � @   
  �$  R1 � @   q  R�XX2  (h   � @   p      ��'  �   �	  �	  �   �	  �	  �   �	  �	  z   �	  �	  � @     �%  Rs Q/ � @   
  �%  R2  @   �  �%  Q	s @   Xs   @   
  �%  R2 ) @   (  �%  R	 s @   Q1X
� 0 @   
  &  R2 J @   (  ?&  R	�t @   Q1Xo Q @   
  V&  R2 k @   (  �&  R	hu @   Q1X2 r @   
  �&  R2 � @   (  �&  R	�u @   Q1X
 � @   �'  �&  R	�v @    � @   
  �&  R1 � @   �  '  Q	s @   Xs  � @   
  4'  R1 � @   (  _'  R	 s @   Q1X
� � @   
  v'  R1   @   (  �'  R	�t @   Q1Xo  @   
  �'  R1 ! @   (  �'  R	hu @   Q1X2 ( @   
  R1  puts __builtin_puts 9__main __main fwrite __builtin_fwrite isprint __builtin_isprint putchar __builtin_putchar  �<   �  ?GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  � @   �      �  
char    size_t #,�   
long long unsigned int 
long long int intptr_t >#�   wchar_t b�   �   
short unsigned int 
int 
long int __time64_t {#�   time_t �  
unsigned int _iobuf !
n  _Placeholder #n    @FILE /G  
long double 
signed char 
unsigned char �  
short int uint32_t (7  uint64_t *0�   
double 
float �   _fsize_t 	  
long unsigned int #   /  2�    3_finddata64i32_t (5
�  attrib 67   time_create 7  time_access 8  time_write 9  size :�   name ;
  $    �  A�   ASN1_INTEGER 9�  asn1_string_st �.  length �	   type �	  data �`  flags �
	   ASN1_TIME D�  .  BIO YQ  bio_st BIGNUM Zh  bignum_st BN_GENCB _�  bn_gencb_st EVP_CIPHER h�  �  evp_cipher_st EVP_MD j�  �  evp_md_st EVP_PKEY n�  �  evp_pkey_st RSA �  rsa_st X509 �     x509_st X509_CRL �:  X509_crl_st X509V3_CTX �Z  v3_ext_ctx @	]�  flags 	c	   issuer_cert 	d~  subject_cert 	e~  subject_req 	f\  crl 	g�   db_meth 	ha  (db 	in  0issuer_pkey 	j�  8 pem_password_cb �
  *  3  �      n   E  BIO_METHOD 
(Q  8  bio_method_st �  �  �  �  �      )  .  X509_EXTENSION �"�  X509_extension_st �  X509_REQ �  X509_req_st �  �  Blh_CONF_VALUE_dummy F  +d1 n  +d2 	  +d3    lhash_st_CONF_VALUE FH  dummy F�    	    stack_st_CONF_VALUE R  X509V3_CONF_METHOD_st  	U�  get_string 	V
�   get_section 	W  free_string 	X'  free_section 	Y<   *�  �  n  �  �   �  *g    n  �   �  4'  n  �     4<  n  g   ,  X509V3_CONF_METHOD 	Zl  �  A  3dirent 
�  d_ino 
	   d_reclen 
�   d_namlen 
�   d_name 
   CH
&	  dd_dta 
)/   $dd_dir .f  ($dd_handle 1�   8$dd_stat 9  @$dd_name <	  D #   *  5�    DIR 
=�  USHORT �   WINBOOL 
  BOOL �  BYTE ��  LPVOID �n  7  LONG )	  WCHAR 1�   �  LPWSTR 5�  HRESULT ��  
_Float16 
__bf16 6JOB_OBJECT_NET_RATE_CONTROL_FLAGS 7  ��	  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  #b  �	  5�    HWND__ �	  unused     HWND �	  �	  DtagCOINITBASE 7  �(
  COINITBASE_MULTITHREADED   �   6VARENUM 7  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _SHITEMID =�  cb >6   abID ?
�	   SHITEMID @�  _ITEMIDLIST L
  mkid M�    ITEMIDLIST N�  
  LPITEMIDLIST \G
  
  LPCITEMIDLIST ]b
  -
  2
  plist_t Yn  ,7  i*  PLIST_BOOLEAN  PLIST_UINT PLIST_REAL PLIST_STRING PLIST_ARRAY PLIST_DICT PLIST_DATE PLIST_DATA PLIST_KEY PLIST_UID 	PLIST_NULL 
PLIST_NONE  plist_type v|
  ,  |�  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ plist_err_t �=  E	�  data  `   size !7   key_data_t "�  ,  6�  USERPREF_E_SUCCESS  USERPREF_E_INVALID_ARG USERPREF_E_NOENT ~USERPREF_E_INVALID_CONF }USERPREF_E_SSL_ERROR |USERPREF_E_READ_ERROR {USERPREF_E_WRITE_ERROR zUSERPREF_E_UNKNOWN_ERROR �~ userpref_error_t ?  F__config_dir r�  	@� @   plist_new_data �
l
  <  �  �   plist_get_data_val 
h  l
  �  h   �  plist_dict_set_item �
�  l
  �  l
   plist_new_string �
l
  �  �   plist_get_string_val �
�  l
  �   
plist_get_node_type �*  
  l
   
plist_dict_get_item �
l
  9  l
  �   X509_EXTENSION_free �\  �   
X509_add_ext B  �  ~  �     
X509V3_EXT_conf_nid 	��  �  M  �    �   G  X509V3_set_ctx 	��  �  ~  ~  \  �     _time64 �         X509_free �0  ~   GX509V3_EXT_cleanup 	�EVP_PKEY_free he  �   
PEM_read_bio_RSAPublicKey ��  �  3  �  �  n   �  �  �  
BIO_new_mem_buf 
�3  �  �     
PEM_write_bio_PrivateKey �  "  3  "  o  e    �  n   �  
BIO_free 
x  C  3   memcpy 2n  f  n  �  �    
BIO_ctrl 
�	  �  3    	  n   
PEM_write_bio_X509 �  �  3  y   
BIO_new 
w3  �  �   L  BIO_s_mem 
��  
X509_sign     ~  �  t   EVP_sha1 �t  
X509_set_pubkey R  P  ~  �   ASN1_TIME_free En  �   
X509_set1_notAfter Q  �  ~  �   @  
X509_set1_notBefore N  �  ~  �   
ASN1_TIME_set M�  �  �  (   ASN1_TIME_new E�  
X509_set_version D  0  ~  	   ASN1_INTEGER_free �Q  j   
X509_set_serialNumber E    ~  j   
ASN1_INTEGER_set l  �  j  	   ASN1_INTEGER_new �j  X509_new �~  
EVP_PKEY_assign 3    �    n   EVP_PKEY_new e�  BN_free +.  .   Y  
RSA_generate_key_ex 
  i  �    .  i   s  
BN_set_word '  �  .  �    7RSA_new ��  7BN_new �	.  
usbmuxd_delete_pair_record ;  �  �   
plist_from_memory ��    �  �     l
  
usbmuxd_read_pair_record   I  �  �  I   �  
usbmuxd_save_pair_record_with_device_id 2  �  �  �  �  �   
plist_to_bin ��  �  l
  �  I   closedir 
A  �  �   *  readdir 
@(�  �  �   f  strncpy Y	�  %  �  �  �    __acrt_iob_func ]G  G  7   p  
realloc n  l  n  �    strrchr ]�  �  �     strcmp ?  �  �  �   opendir 
?�  �  �   
usbmuxd_read_buid   �  �   
malloc n    �    free   n   strlen @�   /  �   string_concat )�  P  �  H CoTaskMemFree mm  o   wcslen ��   �  �   
SHGetPathFromIDListW jE  �  L
  �   
SHGetSpecialFolderLocation ��  �  �	    g
   8pair_record_set_item_from_key_data z�  O  -H  z=l
  name zV�  value zhO   ret |�   �  pair_record_get_item_as_key_data ]�  ' @   �       ��  H  ];l
  
  
  name ]T�  <
  6
  value ]fO  a
  U
  ret b�  �
  �
  buffer c�  �
  �
  %length d�  �Xnode f
l
  �
  �
  C' @   
  C  R�RQ�Q S' @   �  [  Rs  g' @     �' @   <  �  Rs Q�PX�X �' @   �  �  Rs �' @   �<  �  Qu Xs  �' @     Ru   pair_record_set_host_id V�  �& @   *       �s  H  V2l
  �
  �
  host_id VK�  �
  �
  �& @   �  Q  R�Q ' @   m  Rs Q	�{ @     pair_record_get_host_id K�  �& @   L       �B  H  K2l
      host_id KF�  .  $  node M
l
  ^  X  �& @   
    R�RQ	�{ @    �& @   �  '  Rs  �& @   �  Rs Qt   Ipair_record_import_crt_with_name 0�  �  -H  0;l
  name 0T�  cert 0fO   ret 9�   8pair_record_import_key_with_name �    -H  ;l
  name T�  key fO   ret �   pair_record_generate_keys_and_certs ��  � @   �      ��1  H  �>l
  z  t  public_key �V�  �  �  ret ��  �  �  dev_cert_pem �
�  #    root_key_pem �
�  �  �  root_cert_pem �
�  k
  W
  host_key_pem �
�  "    host_cert_pem �
�  �  �  e �
.  b  ^  root_keypair ��  u  q  host_keypair ��  �  �  root_pkey ��  �  �  host_pkey ��  �  �  root_cert �~  �  �  host_cert �~  �  �  %pubkey 4�  ��dev_cert =~      &� @   �       7"  sn �j  0  ,  .l  ��  C  ?  '�8  � @      �q   	�8  T  R  � @   �  R0  �8  � @    � @          ��   	�8  _  ]   @   �  R0  � @   �  � @     �   Rs Q0 � @   Q   !  Rt Qs  � @   0  !  Rs  � @     5!  Rt Q2 � @   �:  `!  Rt QWX	8{ @    � @   �  � @   �  �!  Rs  � @   �  �!  Rt Qs   @   �  �!  Rs   @   n  �!  Rt Qs  & @   P  �!  Rs  1 @   (  "  Rt Qu  6 @     D @   �  Rt Qu   &L @   �       �$  sn �j  l  h  .l  ��  �  {  '�8  � @      ��"  	�8  �  �  � @   �  R0  �8  � @    � @          ��"  	�8  �  �  � @   �  R0  Q @   �  ^ @     #  R Q0 i @   Q  ;#  Rs Q  q @   0  S#  R  ~ @     p#  Rs Q2 � @   �:  �#  Rs QWX	I{ @    � @   �:  �#  Rs QSX	`{ @    � @   �  � @   �  �#  R  � @   �  	$  Rs Q  � @   �  !$  R  � @   n  ?$  Rs Q  � @   P  W$  R  � @   (  u$  Rs Qv     @       @   �  Rs Qu   [  �'  membp 3  �  �  %bdata 	�  ��m# @   �  u# @   �  �# @   �  %  R Qt  �# @   �  �# @   �  �# @   �  Z%  R Qu X0Y0w 0w(0w00 �# @   �  �# @   �  $ @   �  �%  R Qs  )$ @   �  4$ @   �  d$ @   �  �%  R��Qv X Y w 0w( w0  �$ @   f  &  R��Q3X Y�� �$ @   �  2&  R�� �$ @   �<  R&  R X�� �$ @   '  l&  R�� x% @   f  �&  R Q3X��~�Y�� �% @   �  �&  R�� �% @   �<  �&  R��~X�� �% @   '  �&  R  �% @   f  '  R Q3X��~�Y�� �% @   �  4'  R��~ & @   �<  V'  R��~X��~ & @   '  n'  R  8& @   f  �'  R Q3X��~�Y�� L& @   �  �'  R��~ k& @   �<  �'  R��~X��~ s& @   '  R   %  g(  membp 63  ?  ;  {  @   �  )(  R~ Q}  �  @   e  R(  R} Q��X0Y0 �  @   '  R}   0  B,  sn @j  R  N  .l  H�  g  a  pkey O
�  �  }  &�$ @   �       �)  membp Z	3  �  �  &�$ @   h       u)  %bdata \�  ��% @   f  &)  R~ Q3X0Y�� /% @   �  >)  R~  L% @   �<  ^)  R| X�� V% @   '  R��  �$ @   �  �$ @   �  �$ @   �  R~ Q}   '�8  ! @    @  I�)  	�8  �  �  ! @   �  R0  �8  %! @    %! @          K+*  	�8  �  �  *! @   �  R0  �  @   �  �  @     U*  R~ Q0 �  @   Q  s*  R} Q~  �  @   0  �*  R~  �  @     �*  R} Q2 ! @   �:  �*  R} QWX	I{ @    ! @   �  ! @   �  �*  R~  %! @   �  +  R} Q~  9! @   �  .+  R~  D! @   n  L+  R} Q~  L! @   P  d+  R~  Q! @     i! @   �  �+  R| Q6 t! @   (  �+  R} Q|  |! @   H  �+  R|  �! @   �:  �+  R} QRX	�{ @    �! @   �:  ,  R} QSX	`{ @    �! @     �! @   �  R} Qu   '�  h" @    P  ��,  	3  �  �  	%  �  �  	  �  �  /P  B      u" @     �,  R| Q~ ���� �" @   m  R� Q	�{ @      �  �" @    �" @   #       ��-  	3      	%       	  3  1  B  >  <  �" @     ]-  R Q������� �" @   m  R� Q	�{ @     �  �" @    �" @   %       �&.  	3  I  G  	%  X  V  	  k  i  B  v  t  �" @     .  R��~Q������� �" @   m  R� Q	�{ @     �  �" @    �" @   %       ��.  	3  �    	%  �  �  	  �  �  B  �  �  �" @     �.  R��~Q������� �" @   m  R� Q	�{ @     �  �" @    �" @   %       �r/  	3  �  �  	%  �  �  	  �  �  B  �  �  # @     O/  R��~Q��~����� # @   m  R� Q	�{ @     	 @   �   @   �   @   �  ) @   n  �/  Rs Q   < @   3  �/  Rv Q
 Xs Y0 O @   3  0  Rt Q
 Xs Y0 W @     &0  Rs  \ @     o @   �  V0  Ru Q6Xv  t @     � @   �  �0  Rv Q6Xt  � @   �  I @   �  �  @   �  �! @   0  �! @     �0  R}  �! @   H  �0  Ru  �! @   H  1  Rv  �! @     �! @     '1  Rt  (# @     ?1  R|  2# @     Y1  R��~ <# @     s1  R��~ D# @     �1  R  N# @     R��~  9X509_add_ext_helper n  2  cert n&~  nid n0  value n;�   ex p�   ctx q
G   userpref_delete_pair_record f�  � @          �~2  udid f:�  �  �  res h  	    � @   �  R�R  userpref_read_pair_record B�   @   �       �h3  udid B8�      H  BG  8  .  (T  D�  �h(`  E�  �dres G  j  b  8 @     53  R�RQ�hX�d X @   �  M3  Xt  b @     z @      userpref_save_pair_record )�  � @   g       �e4  udid )8�  �  �  device_id )G�  �  �  H  )Zl
  �  �  (T  +�  �X(`  ,�  �Tres 0  �  �  � @   �  94  R�XQ�XX�T � @   N  W4  Rs Qt  � @      :userpref_get_paired_udids ��  � @   �      �77  0list �477      0count �H~  B  <  !config_dir ��  ]  Y  !config_path ��  r  l  !found �7  �  �  �   �6  !entry ��  �  �  �   �6  !ext �
�  �  �  �   ?6  len  �   �  �  newlist �  �  �  tmp �  �  �  � @     �5  Rs  � @   L  �5  Q
~����3$ � @   �  �5  R}{ � @     6  Qs X�� " @   %  6  R2 < @   �<  R	"{ @   Q1XE  � @   �  ]6  Rs Q|  � @   l  {6  Rs Q. � @   �  Q	{ @     h @   �  �6  Rt  H @   �  Rt   ;�7  - @    �   �7  /�   �7      �7  �7  8  
8  � @   �8    # @   �  )7  R8 ; @   �   �  :userpref_read_system_buid �  � @          ��7  0system_buid �&�  $     res �  <� @   �  R�R  Juserpref_get_config_dir �
�  "8  base_config_dir ��  path �
"8  hr �
�  pidl �2
  b �U  Ki �    #�   38  2�    Luserpref_utf16_to_utf8 u�  �8  )unistr .(
  )len ;	  )items_read FH  )items_written XH  outbuf x�  p y  i z  wc |
�    9time "(  �8  _Time /�8   (  "�7  � @   �      ��:  �7  :  6  1�7  ��{�7  R  H  18  ��{
8  x  r  M8  � @   7       u9  8  �  �  � @     Rs   ;38  h @     �   �
:  	n8  �  �  	8  �  �  	d8  �  �  	W8  �  �  /�   �8  �  �  �8  �  �  �8    �  �8  z  j  | @   �    � @   �  .:  R0Q#X��{ � @   /  e:  Rt Q	�z @   X	�z @   Y0 ) @     }:  Rt  S @   �  �:  Qu  e @   m  �:  Ru  " @   P   "�1  0 @   z       ��;  	�1  �  �  	�1  �  �  	�1  �  �  �1      1�1  ��o @   �  G;  Rv Q0Xt Y0w 0w(0  @   �  p;  R0Qv Xs Yu  � @   \  �;  Rt Qs X	� � @   9  Rs   "�7  � @          ��;  =�7  �7  �7  8  
8  N� @   �8   "�  �' @          �^<  	�  -  '  	�  L  F  	�  k  e  �  �  �  <�' @   T  R�RQ�QX�X  "�   ( @   G       ��<  	  �  �  	%  �  �  	3  �  �  =B  B( @     P( @   m  Rs Qt   >memcpy __builtin_memcpy >fwrite __builtin_fwrite  ]   �
  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  0* @   �       �  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	P� @   atexit ��   �  Y   __main 5�* @          ��  �* @   �   	__do_global_ctors  p* @   j       �  
nptrs "�       
i #�   )  %  �* @   j  R	0* @     	__do_global_dtors 0* @   :       �[  p [  	 a @    	   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �     char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
u  �   �,  __uninitialized  __initializing __initialized  u  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	a @   �  	a @   =  
"	h� @   [  	`� @    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  v  _dowildcard  �   	 a @   int  }   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 x  `   + @          �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �    + @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 	  �    _newmode �   	p� @   int  �   $  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  �	  + @   �       @  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	�� @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	 | @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	 | @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	�� @   __mingw_initltsdyn_force ��   	�� @   __mingw_initltssuo_force ��   	�� @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  + @   /       �}  
�  �  D  @  
�  *M  V  R  
�  ;d  h  d  5+ @   �   __tlregdtor m�   �+ @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  @+ @   �       ��  ~  v  �  �  �  �  �  �  �  �  �  p+ @    p+ @   +       L�  �  �  �  �      �      �  "    �  6  2   �+ @   �    �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  V  _commode �   	�� @   int  w   9  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 (  Z  �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �+ @   �       �  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   �+ @   �       �0  pexcept 0  ]  W  type 
�    s  =, @   b  �  R2 f, @   7  Q	X} @   Xs Yt w �ww(�ww0�w  5   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �, @          �  _fpreset 	�, @          � �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 ;
  #
  �  __mingw_app_type �   	�� @   int  G   �  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
    �, @   =        __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	�� @   	�  maxSections �%  	�� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator ��. @   ]      ��  4was_init �%  	�� @   5mSecs �%  �  �  !�  U/ @   �  �4  �  �  �  6�  
�  �  �  
�  e  5  
�  |  d  
�  �  �  

  !    
  ^  N  "E  �  �  
F  �  �  
[  �  �  i0 @   `  R	�~ @   Xu w t     �/ @   �/ @          �;  �  
    �      �  '  %    �/ @   �/ @          �  1  /  �  <  :  �  K  I  �/ @   �  Ru    !  �0 @   �  ��  �  U  S  �  `  ^  �  o  m  7  �0 @   �  �  y  w  �  �  �  �  �  �  �0 @   �  Ru      w1 @   w1 @   
       �w  �  �  �  �  �  �  �  �  �    w1 @   w1 @   
       �  �  �  �  �  �  �  �  �  1 @   �  Ru      �1 @   �1 @          �   �  �  �  �  �  �  �  �  �    �1 @   �1 @          �  	    �      �  #  !  �1 @   �  Ru    "$  �  �  
)  1  +  83  �  
4  K  I    �1 @   �1 @   
       s�  U  S  �  `  ^  �  o  m    �1 @   �1 @   
       �  y  w  �  �  �  �  �  �  2 @   �  Rt      
 2 @   `    R	x~ @    -2 @   `  R	@~ @      9�  �0 @   X       �|  
�  �  �  :�  ��1 @   
  Yu   / @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable �`- @   b      �`  &addr ��  �  �  b �:  ��h �g  �  �  i �%  (  "  >@. @   P       �  new_protect �
u  A  ?  
t. @   
  �  Ys  ~. @    
  �. @   `  R	~ @     
�- @   �
  �  Rs  �- @   n
  
. @   E
    Q��X0 
�. @   `  >  R	�} @    �. @   `  R	�} @   Qs   ?__report_error T�, @   i       �/  &msg T  M  I  @argp ��   �X
- @     �  R2 
6- @   /  �  R	�} @   Q1XK 
E- @       R2 
S- @   �
  !  Qs Xt  Y- @   �
   Afwrite __builtin_fwrite   �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 8     02 @   L       �#  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	�� @   
__setusermatherr ��  �   __mingw_setusermatherr �p2 @          �P  f ,�  h  d  |2 @   �  R�R  __mingw_raise_matherr �02 @   >       �typ !�   |  v  name 2�  �  �  a1 ?w   �  �  a2 Jw   �  �  rslt 
w   � ex 0  �@i2 @   R�@   �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  H$  _fmode �   	а @   int  �     GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �2 @   �      �$  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��   �2 @   �      ��  'exception_data �-�  �  �  old_handler �
	  )      action ��   z   `   reset_fpu ��   �   �   
�2 @   �
  �  R8Q0 (3 @   �  R�R 
73 @   �
  �  R4Q0 M3 @   �  R4 
�3 @   �
    R8Q0 
�3 @   �
  7  R8Q1 
�3 @   �
  S  R;Q0 �3 @   f  R; �3 @   y  R8 
4 @   �
  �  R;Q1 
4 @   �
  �  R4Q1 
34 @   �
  �  R8Q1 )84 @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  @4 @   b      &  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	 � @   __mingwthr_cs_init �   	� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	 � @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  �5 @   �       �n  	hDllHandle z�  M!  5!  	reason {<  �!  �!  	reserved |S  K"  3"   %6 @   K       �  
keyp �&�  �"  �"  
t �-�  �"  �"  D6 @   �  
k6 @   C  R	 � @     !n  �5 @   �5 @          �  �  6 @   )
   "n  6 @   �  �E  #�  �  �6 @   )
    u6 @   6  
�6 @   e  R	 � @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�    5 @   �       �d	  	key A(<  �"  �"  
prev_key C�  #   #  
cur_key D�  %#  #  P5 @   �  B	  Rt  �5 @   �  
�5 @   �  Rt   ___w64_mingwthr_add_key_dtor *�   �4 @   o       �$
  	key *%<  L#  B#  	dtor *1.  �#  r#  
new_key ,$
  �#  �#  �4 @   �  �	  R1QH �4 @   �  
  Rt  
5 @   �  Rt   �  &n  @4 @   p       ��  �#  �#  '�  x4 @          �
  �  �#  �#  |4 @     �4 @     (�4 @   Rt   Z4 @   �  �
  R|  )�4 @   �  R	 � @      �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �(  _CRT_MT �   	0a @   int  �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 s  [  �(  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	a� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	`� @    �   O  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 -    �6 @   �      �(  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<  �  e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  �  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��    �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	    �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  "�  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #�  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
   �  � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  �9 @   �       �7
  i �(�   $   $  �  �	`  �  �	  $  $  importDesc �@  5$  3$  �  �^
  importsStartRVA �	I  E$  =$  �  �9 @   	�  ��  �  �  �  �  �  	�  �9 @    �  �  �  �  �  �$  �$  �  �$  �$      M  $: @   $: @   J       �q  �$  �$  f  }  �$  �$  �  �$  �$  �  �$  �$    
_IsNonwritableInCurrentImage �  P9 @   �       ��  pTarget �%`  �$  �$  �  �	`  rvaTarget �
�  %  %  �  �^
  )%  '%  �  P9 @   �  �/  �  �  �  �  �  	�  `9 @    �  �  �  �  �  5%  1%  �  F%  D%      M  �9 @   �9 @   I       �q  R%  P%  f  }  \%  Z%  �  f%  d%  �  p%  n%    
_GetPEImageBase �`  9 @   6       �0  �  �	`  	�  9 @   g  �	�  g  �  �  �  	�   9 @    w  �  w  �  �  }%  y%  �  �%  �%       
_FindPESectionExec y^
  �8 @   s       �%  eNo y�   �%  �%  �  {	`  �  |	  �%  �%  �  }^
  �%  �%    ~�   �%  �%  	�  �8 @   L  �	�  L  �  �  �  	�  �8 @    \  �  \  �  �  �%  �%  �  �%  �%       
__mingw_GetSectionCount g�   P8 @   7       ��  �  i	`  �  j	  �%  �%  	�  P8 @   1  m	�  1  �  �  �  	�  `8 @    A  �  A  �  �  �%  �%  �  &  &       
__mingw_GetSectionForAddress Y^
  �7 @   �       �  p Y&s  &  &  �  [	`  rva \
�  ?&  =&  �  �7 @     _�  �    �  �  �  	�  �7 @      �    �  �  K&  G&  �  \&  Z&      	M  	8 @   &  c
q  h&  f&  f  &  }  t&  p&  �  �&  �&  �  �&  �&     
_FindPESectionByName :^
  07 @   �       �M  pName :#�  �&  �&  �  <	`  �  =	  �&  �&  �  >^
  �&  �&    ?�   �&  �&  �  E7 @      F  �     �  �  �  �  U7 @    U7 @          �  �  �  �&  �&  �  '  '     &?7 @   �  -  Rt  '�7 @   z  Rs Qt X8  _FindPESection $^
  �  �  $`  (rva $-�  �  &	  �  '^
    (�    _ValidateImageBase   �  �  `  pDOSHeader �  �  	  pOptHeader v   )�  �6 @   ,       �~  �  '  '  �  $'   '  �  �  	�  �6 @    �  �  8'  2'  �  �  �  R'  N'  �  _'  ]'     *M  �6 @   P       �f  k'  g'  +q  Q}  ~'  z'  �  �'  �'  �  �'  �'    b   �    GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �: @         F.  char w   
size_t #,�   long long unsigned int long long int 
intptr_t >#�   short unsigned int int long int 
__time64_t {#�   w   �   unsigned int long unsigned int unsigned char double float long double 
_fsize_t %  w   �  �    _finddata64i32_t (5
�  attrib 6   :  7�   .  8�     9�   size :m   name ;
~  $ __finddata64_t 0>
]  attrib ?   :  @�   .  A�     B�   size C�    name D
~  ( dirent �  d_ino �    d_reclen �   d_namlen �   d_name ~   !H&	  dd_dta )�   dd_dir .]  (dd_handle 1�   8dd_stat 9�   @dd_name <	  D w   "  "�     
DIR =�  #
DWORD �%  signed char short int CHAR 'w   Z  h  LPCSTR ]m  _Float16 __bf16 _TCHAR �w   �  $free 	�  .   _findnext64 ��   �  �   �   �  strncpy Y	  
    
  �       _findfirst64 ��   6  
  �   _findclose ��   S  �    GetLastError 00  memset 5.  �  .  �   �    strcpy =  �    
   malloc .  �  �    strlen @�   �  
   _fullpath f      
  �    GetFileAttributesA 
M0  &  r   _errno 	�  %seekdir "@ @   �       �  dirp "  (  �'  lPos "�   Z(  J(  &m  p@ @   �  ?�  	�  �(  �(  u@ @   &  �@ @   6    @ @   &  L@ @   6  �@ @     �  Rs  �@ @   &  �@ @   &   "  'telldir �   �? @   6       �m  dirp   �(  �(  �? @   &  �? @   &   (rewinddir ��  dirp �   closedir ��   ? @   R       �  dirp �  �(  �(  
rc ��   )  �(  ? @   &  =? @   6  G? @   �    Rs  U? @   &   readdir ��  �< @         ��  dirp �  ))  )  )�  �  
winerr �
0  ^)  Z)  �> @   S  �> @   6  ? @   &   �
  *= @   �  �)  	�
  q)  m)  	�
  �)  �)  �  �
  ��}�
  �)  �)  6= @       Rs�Q��} {= @   �  Rt Q��}X
   7
  �= @   �  ��  	i
  �)  �)  	T
  �)  �)  �  v
  ��}�
  �)  �)  �= @   �  > @   �  Rt Q��}X
   = @   &  �= @   �  �  Rt  �= @   �  �  Rs�Qt  �> @   &   ]  opendir *  �: @   �      �#
  szPath *#
  *  *  
nd ,
  S*  I*  
rc -  z*  v*  *szFullPath .
(
  ��}; @   &  $; @     �	  Rs  K; @   �  �	  Rt Qs X
 S; @   �  �	  Rt  e; @   �  �	  Rs� m< @   &  �< @   &  �< @   &  �< @   &   �  �  7
  �    +_findnext64i32 �   �
  ,_FindHandle 4�   -$  Y�
  fd �  __ret 	�    �  ._findfirst64i32 �!�   �
  _Filename �=
  /$  �`�
  fd ��  ret ��    0m  p? @   ]       �	�  �*  �*  1m  �? @    �? @   
       �J  	�  �*  �*  �? @   &   }? @   &  �? @   6    8   �#  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   B  A @   �      �3  
__gnuc_va_list �   #__builtin_va_list �   char �   
va_list w   
size_t #,�   long long unsigned int long long int 
wchar_t b
  short unsigned int int long int 	�   6  	#  unsigned int long unsigned int unsigned char double float long double 	�  	6  optind #  optopt #  opterr #  optarg 6  option  >*  name @/   has_arg A#  flag B@  val C#   �  	�   /  $E  G~  no_argument  required_argument optional_argument  _iobuf 0!
  _ptr %6   _cnt &	#  _base '6  _flag (	#  _file )	#  _charbuf *	#   _bufsiz +	#  $_tmpfname ,6  ( 
FILE /~  
DWORD �U  signed char short int WCHAR 1�   E  	E  	S  LPWSTR 5X  LPCWSTR 9]  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS E  �i  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  %tagCOINITBASE E  ��  COINITBASE_MULTITHREADED   VARENUM E  		+  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � �  	`a @   �  	\a @   �  	Xa @   &__mingw_optreset D#  	�� @   �  	�� @   
place f6  	Pa @   
nonopt_start i#  	Ha @   
nonopt_end j#  	Da @   �   �  �   ! �  
recargchar m�  	`� @   
recargstring n�  	 � @   �   :  �    *  
ambig o:  	� @   �   f  �   ' V  
noarg pf  	� @   �   �  �    �  
illoptchar q�  	� @   
illoptstring r�  	p @   vfprintf )#  �  �  4  �    	  �  fprintf "#  	  �  4   __acrt_iob_func ]�  @	  E   '__p___argv ��  strncmp 
V#  w	  /  /  �    strlen 
@�   �	  /   strchr 
D6  �	  /  #   GetEnvironmentVariableW ?  �	  q  b     getopt_long_only O#  �L @           ��
  ^  ,#  �*  �*  X  ,+�
  �*  �*  F  ,>/  �*  �*  d  -�
  +  +  idx --@  � �L @     R�RQ�QX�XY�Yw � w(5  	;  	*  getopt_long M#  `L @           �q  ^  #  "+  +  X  &�
  8+  4+  F  9/  N+  J+  d   �
  d+  `+  idx  -@  � {L @     R�RQ�QX�XY�Yw � w(1  getopt #  0L @   "       �  ^  #  z+  v+  X  !�
  �+  �+  F  4/  �+  �+  ML @     R�RQ�QX�XY0w 0w(0  (getopt_internal C#   F @   (      ��  ^  C#  �+  �+  X  C*�
  �+  �+  F  C=/  6,  ,,  d  D�
  b,  Z,  idx D*@  � )flags D3#  �,  �,   oli F6  �,  �,   optchar G#  -  �,  *N  G#  7-  1-  +posixly_correct H
#  	@a @   ,start g%G @      C
  Rt Qu Xs Yv  �G @   �	  [
  R}  �G @   �  �
  Rv Q} X~ Y�  �G @   �	  �
  R} Qs  VH @   �	  �
  R	H @   Q0X0 �H @   �	  �
  R} Q- �H @   �	  	  R} Q- �I @      '  Ru Yv  �J @      T  R���Qt Xs Yv  �J @   �  y  R	� @   Qs  CK @   �  �  R	`� @   QW �K @   �  �  Rv Q} X~ Y� w 0 L @   �  R	`� @   Qs   -parse_long_options �#  pB @   �      �   X  �"�
  S-  O-  F  �5/  g-  c-  d  ��
  -  w-  idx �*@  �-  �-  .N  �3#  � current_argv �6  �-  �-  has_equal �6  �-  �-  current_argv_len �	�   !.  .  i �#  D.  >.  ambiguous �	#  m.  [.  match �#  �.  �.  �B @   �	  /  Ru Q= HC @   S	  S  Ru Q~ Xt  TC @   w	  k  R~  "D @   �  �  R	� @   Qt Xu  �D @   w	  �  Ru   E @   �  �  R	p @   Qu  6E @   �  �  R	� @   Qt Xu  �E @   �  R	 � @   Qu   !permute_args �A @   �       ��  panonopt_start �#  �.  �.  panonopt_end �&#  /  /  opt_end �8#  :/  4/  X  ��
  X/  P/  cstart �#  /  u/  cyclelen �#  �/  �/  i �#  �/  �/  j �#  �/  �/  ncycle �#  �/  �/  nnonopts �&#  �/  �/  nopts �0#  0  0  pos �7#  50  -0  swap �6  S0  O0  /�  &A @      ��  j0  `0  �  �0  �0  0  1�  �0  �0     2gcd �#  �  a �	#  b �#  3c �#   !warnx ~�A @   �       ��  fmt ~/  �0  �0  
ap ��   �X4�  B @    B @   W       �  �0  �0    �0  �0  5B @   @	  B @   	  u  R2 *B @   �  �  Q	@ @   Xu  4B @   	  �  R2 BB @   �  �  Qs Xt  LB @   	  �  R2 ZB @   "  R:   6_vwarnx u"  fmt u/  ap u!�    7fputc __builtin_fputc 
  �    t'  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 W  ?  /=  _MINGW_INSTALL_DEBUG_MATHERR �   	pa @   int  �   �'  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �L @          i=  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	�a @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  �L @          �_File *�  1  1  _Format J�  $1  1  _ArgList Z�   =1  71  �L @   �  R0Q�RX�QY0w �X   �   �(  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �L @   H       �=  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�a @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	  �L @   H       �_Format .�  c1  ]1  
ap 
�   �Xret 	  x1  v1  �L @   �  �  R1 M @   �  R0Xs Y0w t    �   F*  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  M @   2       p>  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�a @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  M @   2       �_File )�  �1  �1  _Format I�  �1  �1  
ap 
�   �hret 	  �1  �1  =M @   �  R0Q�RX�QY0w �   �   �+  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �>  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	�� @   local__winitenv 
`  	�� @   '  
	�a @     
	�a @    �   P,  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  PM @         U?  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	0b @   �      �   __imp_at_quick_exit g)  	(b @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	$b @   
initial_tzname1 }
V  	 b @   
initial_tznames ~�  	b @   
initial_timezone 
%  	b @   
initial_daylight �  	b @   __imp_tzname ��  	 b @   __imp_timezone ��  	�a @   __imp_daylight ��  	�a @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�a @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�a @   �	  __imp__amsg_exit �V  	�a @   F  __imp__get_output_format �\
  	�a @   -
  __imp_tzset ��  	�a @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�a @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   �N @   5       ��
  file �!�
  �1  �1  fmt �6�  �1  �1  
ap ��   �h+ret �  2  2  �N @   �
  R4Q�RX�QY0w �  ,tzset "�N @   6       �  -  �N @   �N @   -       �O @   +  O @     O @       ._tzset �/_get_output_format nF  PM @          �0_amsg_exit i�N @   .       ��  ret i  2  2  �N @   z  �  R2 �N @   Q  �  Q	�� @   Xs  �N @   <  R�  at_quick_exit �  `N @          �   func ]*�  /2  +2  1uN @   �   _onexit ��  @N @          �p  func V%�  G2  A2  MN @   �  Rs   __wgetmainargs J  �M @   j       �[  _Argc J"�  f2  `2  _Argv J5I  �2  2  _Env JGI  �2  �2   q  JQ  �2  �2  !}  Jl�	  � �M @   0   N @   	  &  R	v  $0.# N @   �  N @   �  N @   �  -N @   U   __getmainargs >  `M @   j       �E  _Argc >!�  �2  �2  _Argv >1S  �2  �2  _Env >@S  3  3   q  >J  -3  '3  !}  >e�	  � �M @   �  �M @   �    R	v  $0.# �M @   �  �M @   �  �M @   u  �M @   U   2  0O @   6       �BO @   +  NO @     ZO @                                                                                                                                                                                                                                 
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}  (    I  ( 
  H }  .?:;9'I<  $ >  	 !I  
 :;9I   1�B  I �~  

 :;9I8  .?:;9'I<   :!;9I  (   4 :!;9I�B  H}�  >!I:;9  4 :!;9I  4 :!;9I  . ?<n:!;!   & I  :;9  .?:;9'<   :!;9I�B  H}  4 :!;9I  H}�  4 1�B  4 :!;9I?<   7 I  !>!!I:;9  ">!!I:;9  # :;9I  $ <  %   &  '.:!;9!
' !  (.1@z  )1R�BUX!YW  *%U  +   ,'  -. ?:;9'I<  ..?:;9'<  /.?:;9'�<  0.?:;9'I@z  1
 :;9  24 :;9I�B  3U  4I  5! I/  6.:;9'@z  7.:;9'I   8U  9. ?<n   I ~   I  H}  (    !I   :;9I  H }  
 :;9I8  	 1�B  
.?:;9'I<  4 :!;9I�B  H}  
$ >  4 1�B  .?:;9'I<   <  ( 
  .?:;9'<  4 :!;9I  & I   :;9I   :!;9I�B  :;9  (   4 1  .?:!;9!'I@z  1R�BX!YW   :;9I  . ?:;9'I<   :!;9I�B  U   4 :!;9I  !4 :!;9I�B  ".1@z  #I  $
 :!
;9I8  %4 :!;9I  &  '1R�BUX!YW!  (4 :!;9I  ) :!;!� 9I  *'I  +
 :!;!� 9!I  ,>!I:;9  - :!;9I  .4 :!;9!I�B  /U  0 :!;9I�B  14 1  2! I/  3:;9  4'  5! I/!   6>!!I:;9  7. ?:;9'I<  8.?:!;9!'I !  9.:;9'I   :.?:!;9'I@z  ;1R�BUX!YW  <H}�  =4 1!   >. ?<n:!;!   ?%  @   A&   B:;9  C:;9  D>I:;9  E:;9  F4 :;9I  G. ?:;9'<  H   I.?:;9'I  J.?:;9'I   K  L.:;9'I   M1  NH }�   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   H }   I  I ~  $ >  
 :;9I8   !I  H}  .?:;9'I<  	 1�B  
 :;9I  
 :!;9!I8  
 :!;9I8  
4 :!;9I�B  & I  I  :;9   :;9I   :!;9I�B  .?:!;9!'I@z   :!;9I�B  ! I/!�  . ?:;9'I<  .?:!	;9'I<   :;9I  1R�BUX!YW  U  4 1  4 1�B  H}  4 :!;9I  4 :!;9I   %  !:;9  "! I/  #   $.?:;9'<  %.?:;9'@z  &1R�BUXYW  '.?:;9'I@z  (.?:;9'   )U  *4 :;9I  +.?:;9'I   , :;9I  - :;9I  ..?:;9'I   / :;9I  0.1@z  11R�BXYW   I ~  (   H}  $ >   I   :!;9I�B  4 :!;9I�B  
 :;9I8  	 !I  
4 :!;9I  & I  (   
 :;9I  H}  .?:;9'I<   :!;9I�B  4 :!;9I?<  4 G  I  ! I/   :!;9I�B   1�B   :!;9I   :!;9I  .?:!;9!'I@z   :!;9I  :;9  7 I  >!!I:;9  .?:!;9!'I<      4 :!;9I�B  !.:!;9!'@z  "%  # I  $>I:;9  %>I:;9  &4 :;9I?  '. ?:;9'I<  (.:;9'I@z  ) :;9I�B  *4 :;9I�B  +4 :;9I  ,
 :;9  -.:;9'I@z  . :;9I  /1R�BUXYW  0U  14 1�B  2.:;9'I   34 :;9I  41R�BXYW  5H }  6.:;9'   7. ?<n:;   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                                                                                   5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg h    �   �
        T  m  �  �  �  �   �   �           -  6  ?  G  R  e  p  y  �  �  �   �   	P @   � KtGzXtZ{yJqX�jJ[�mJ[�pJvX�fJ
X
�sJ]��g	Q	��
q<ht�Y  ZcJ	�tX
i
 X
NJY
Y; <  <Y	vt
�
 XKJKf= �X <�<
tXK 	>t y��  � t J>� �tY ��@t  ~ X
 	� @   (h
0
V>Y  � � t t � <Y  t   <X  t   <]  t   <\  �nue ^X t t � <Y  t   <X  t   <]  t   <\   	�Q @   ��;<�Zt����
�FF ���� � �� <Y���X���t�~��-�L.	���	 �uuX�1t�aX	tt�		J�!	 � �� �  <YXXfX�6J��� ft t�%Xt<��L �	� �	� �	� �	� �	� W	�w�	=	WYZ�XZ�t �  <Y�
XX�< #� f	�	��	X=�	��X ���$W�����X fA� �  <Y�`6�t���X �  <YmX �  <Y�$X �  <Y+X� ��mX	0�X	� .��栟�XR�
==
Y-
VYYY/ -=� �  W �	/��vX�� X�
W/�Y
uK�~��.X`X,�XS
�K�~��.X�~X� ��Xv��~��.X�X� ���. �� <Y� P    �   �
      �  2  K  l  �   �   �   �  �  �  �  �  �  �          #  ,  8  @  I  V  _  i  q   |  �  �  �  �  �  �  �  �  �   
 	� @   ��q��t�. �v>Y �==�JY; �]:��e�/�4$ O<41.XM<� J�g<�,fY+.$=+;K<+;<g<7JY:K
<I
<;
j
g+$/<+;K;Yv.�<L���x�`.xB�<Y/]�X  �~�t&X<yX 	� @   �
� ��Y
hX�X � t>
��� sX
t=q
�<m�
 X�J �N� X �K�C<I= I�X=\=O7X=Y�hK
tS� �  <YJ���/
	�/�X�
P=.g��XV�Z>V0Z&K�& eX�Y@���Y�u�	j/Xl ��z�XX
�KZ&K& �X=<.=wJ 	�3#X=X=Z�>Z/0�Y�=ZY�=[�Y-=����??�~X��="�~8?< �?�~<< �Y�"�~8?X �孅� Xf <\�Y-=����???�~X�.="�~8?< �?�~<< �Y�"�~8?X �孅� Xf <[* �
�ց=����<:�Y�=4��
 Y�=
 �Y-=����>?�}X�.="�}8?< �?�}<< �Y�"�}8?X �孄Y�=���=? Xf < X �
�~<Q�<Y����~<�XY�� �� �� �� �� ����~f�. X� <Z�~� �� <Z�~� �� <Z�~� �� <Z�~� �� <Z�~1�����.�|t �Y e=X
� � 	� �Y I=X
� � 	� �Y e=X
� � 	�
�X� < <Y0
� � ��(�;K(�=YL�t* �Y eY = X�-�)��=)X=YL���)u;K)�Y)=Y"�p�(u;K(�Y(=Y"�p�)u;K)�Y)=Y"��
���g=tX> X �\t b�t [Yg X� <Ztwu=#��X> X �XXY	ZYfqf!!X� �=!=�:KgYn.f
h���[
s
��� �gi�J< Xf <Z	/X 
y� #    K   �
        ,  S  n  x  �  �  �  �  �   	0* @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �    <  W  �  �  �  �  �  �  6     .   �
        #  J  U  R     .   �
      �  �  �  �   	 + @    6     .   �
      F	  ^	  �	  �	      K   �
      �	  �	  "
  =
  F
  O
  Y
  e
  o
  w
   	+ @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      �
  �
      6     .   �
      r  �  �  �  �     <   �
      
  %  L  g  n  u  |   	�+ @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      �  �  

  
   	�, @   		3 6     .   �
      r
  �
  �
  �
  |    s   �
        7  ^  y  �  �  �  �  �  �  �  �  �  �  �          	�, @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	�. @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      m  �  �  �  �  �   	02 @   
L�Z
�KTYZ
g=yuX 6     .   �
      4  L  s  ~  �    U   �
      �  �    	0  >  L  T  `  j  {  �  �   	�2 @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      �  �  "  =  G  Q  [  g  q  y  �  �  �  �   	@4 @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
           G  Q  6     .   �
      �  �  �    G    K   �
      ]  u  �  �  �  �  �  �  �  �   	�6 @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< �    d   �
      C  [  �  �  �  �  �  �  �  �  �  �  �  �      	�: @   *�
A	 Xh�����\, �,t< <��tJ= �9�� X qX\u��x<��?9hc[8S
 3g� j. \ 8N�Y
 -g<rX
 .g<o[X
 %.g,��=	 Xi��� �Xj!� *�� tX=�Kj/VLK�Y �v�� �uYx�at� �/�Kj/VLK�Y
 ���O�<.�(��% ��� ��%�gZ��Xt
 Xg$X X"�g?	 Xg^xt6hx�ts.Y
 Wg�Y=	 Xh^tk��Xt�
 XgXY=	 Xh]gX
 XggY	 Xh��1
thZ�
�X ��`	 xthk��?( �% � X% fMX ���1
 XgX dt
 Xg ]	    n   �
      Z  r  �  �  �  �  �  �  �  �  �  �        +  4    	A @   ��/x<_//a</�< 9zXL  X�  J"�
y<	�8	�M 
A	t>> w<  G  J�
= <    TXpfuV"Ys	 X � � <Y �f <Y	 �X < f	X� �bur�p<�Vrt��X <� = I=   Ju�.	p�<d�Xk
��% j�  <% J JZ �N �
��
Y
 ��<�J�</X�J�
�
� Xf <�� ��f^�<KY ��� ��8<Z�	t��2K;�/
���X� � �P<	X�X�XJ�w� ��	��+̟^X!&��w�!!��
A�
tvf
�( � f�X�
 ��	������f���
���f	�fY�-0��
�N  �� �, X t5 t�� � ��
�0�t<J<t, �
�. ��3 J- �����~�
�	�Xt	fxfK  �gX�	M
J
�' I� �� ��'tXu
�. �!��
�nJfgt	u	;u��P�	K
<
ew
cu	^]h��
Q�X�...�X<ugit0,�,Z� �J<t
�gu ��
g �~�	K;Xf X � J+ ��
zf=sg
y�f*Je.	
�!t�f<�
e� Ju� ����k� X
.�
�	�
	X�<��z��	 �u�g
t
J
�
.L
h	XL
h 6     .   �
      �  �  �  �  n     A   �
      @  X  �  �  �  �  �  �   	�L @   K
�<.Y �     A   �
      &  >  g  �  �  �  �  �   	�L @   g?	YT�Y	 X� <uX �     A   �
      	  !  J  e  t  �  �  �   	M @   KU	\fp	\;Y	Y W     O   �
      �    *  E  p  |  �  �  �  �  �  |    h   �
        $  K  f  �  �  �  �  �  �  �  �  �  �       	PM @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	�N @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                               ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      D   P  P @   �       D0m
EW
ER
ER
ER
ER
ER      �   P    @   �      B�B�B �B(�A0�A8�A@�AH�	Dp`
HA�@A�8A�0A�(B� B�B�B�D�
HA�@A�8A�0A�(B� B�B�B�I\
HA�@A�8A�0A�(B� B�B�B�HgHA�@A�8A�0A�(B� B�B�B�       4   P  � @   p      A�A�D@�
A�A�E    l   P  �Q @         B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�A        ���� x �      D   8  � @   �      A�A�A �A(�G��
(A� A�A�A�I D   8  0 @   z       A�A�A �A(�D�m(A� A�A�A�       8  � @             8  � @          l   8  � @   �      B�B�B �B(�A0�A8�A@�AH�	D�
HA�@A�8A�0A�(B� B�B�B�E    4   8  � @   g       A�A�DPR
A�A�F    4   8   @   �       A�DPY
A�AqA�      $   8  � @          D0M
G       l   8  � @   �      B�B�B �B(�A0�A8�A@�AH�	G�t
HA�@A�8A�0A�(B� B�B�B�E    <   8  �& @   L       A�A�D@j
A�A�FQA�A�  $   8  �& @   *       A�D0cA� L   8  ' @   �       A�A�A �DPY
 A�A�A�EN
 A�A�A�G   8  �' @             8   ( @          4   8   ( @   G       A�A�D@p
A�A�H        ���� x �         �  0* @   :       D0u  4   �  p* @   j       A�A�D@@
A�A�H       �  �* @             ���� x �         0   + @             ���� x �      $   `  + @   /       D0R
JN    L   `  @+ @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       `  �+ @             ���� x �      <     �+ @   �       A�A�D�P�
���
���A�A�B    ���� x �         `  �, @             ���� x �      $   �  �, @   i       A�A�DP   <   �  `- @   b      A�A�A �Dp�
 A�A�A�D   \   �  �. @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �         p	  02 @   >       D`y     p	  p2 @             ���� x �      4   �	  �2 @   �      A�D0}
A�Mf
A�I     ���� x �      L   
  @4 @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   
  �4 @   o       A�A�A �D@U
 A�A�A�A    D   
   5 @   �       A�A�D@R
A�A�FR
A�A�D      4   
  �5 @   �       A�D0p
A�J�
A�A      ���� x �         8  �6 @   ,          8  �6 @   P       L   8  07 @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       8  �7 @   �          8  P8 @   7          8  �8 @   s          8  9 @   6          8  P9 @   �          8  �9 @   �          ���� x �      \   `  �: @   �      A�A�A �A(�G�+
(A� A�A�A�FO
(A� A�A�A�E T   `  �< @         A�A�A �G��
 A�A�A�Ew
 A�A�A�F       4   `  ? @   R       A�A�D@w
A�A�A     ,   `  p? @   ]       A�D0y
A�A      ,   `  �? @   6       A�D0]
A�A      T   `  @ @   �       A�A�D@O
A�A�IS
A�A�K_
A�A�G          ���� x �      T      A @   �       B�B�A �A(�A0�A8��
�0A�(A� A�B�B�A      <      �A @   �       A�A�A �DPw A�A�A�      l      pB @   �      B�B�B �B(�A0�A8�A@�AH�	D�e
HA�@A�8A�0A�(B� B�B�B�G    l       F @   (      B�B�B �B(�A0�A8�A@�AH�	D��
HA�@A�8A�0A�(B� B�B�B�C          0L @   "       D@]        `L @           D@[        �L @           D@[     ���� x �           �L @          D@Y     ���� x �      ,   H  �L @   H       A�A�D`A�A�   ���� x �         �  M @   2       DPm     ���� x �         �  PM @          L   �  `M @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L   �  �M @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $   �  @N @          A�D0WA�    �  `N @             �  �N @   .       A�D0   �  �N @   5       DPp     �  �N @   6       D0q     �  0O @   6       D0q                                                                                                                                                                                                                                                                                                                                                                                                                                          Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion idevice_private lockdownd_client_private pair_record record_data record_size asn1time __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection time_write _FindData time_access time_create options short_too nargv nargc long_options _DoWildCard _StartInfo                                                                                                                         C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools idevicepair.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../common ../include/libimobiledevice C:/msys64/ucrt64/include/libimobiledevice-glue idevicepair.c idevicepair.c corecrt.h stdio.h getopt.h winnt.h combaseapi.h wtypes.h stdint.h plist.h userpref.h libimobiledevice.h lockdown.h string.h ctype.h utils.h stdlib.h <built-in> conio.h C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\common userpref.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/common C:/msys64/ucrt64/include C:/msys64/ucrt64/include/openssl C:/msys64/ucrt64/include/plist C:/msys64/ucrt64/include/libimobiledevice-glue userpref.c userpref.c time.h corecrt.h stdio.h stdint.h io.h types.h asn1.h x509v3.h bio.h x509.h conf.h dirent.h minwindef.h winnt.h windef.h combaseapi.h wtypes.h shtypes.h plist.h userpref.h evp.h pem.h string.h bn.h rsa.h usbmuxd.h stdlib.h utils.h shlobj.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/dirent.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include dirent.c dirent.c io.h corecrt.h dirent.h minwindef.h winnt.h tchar.h string.h stdlib.h fileapi.h errhandlingapi.h C:/M/B/src/mingw-w64/mingw-w64-crt/misc/getopt.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include getopt.c getopt.c vadefs.h corecrt.h getopt.h stdio.h minwindef.h winnt.h combaseapi.h wtypes.h string.h processenv.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                                      �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� ?                              �Q @    4R4�T���R���T���R���T���R���	T�	�	�R��	�
T�
��R�                                               �Q @    4Q4�S���Q���S���Q���S���Q���S���Q���S���Q���
S�
�
�Q��
�S���Q���S���Q���S��
�Q��
�
S�
��Q���S���Q���S       �Q @    $0�=RP��P��P                           R @    x	����	����P��	����	P�	�
	���
�
P��	����P��P��P�
�
	���
�
	����	��                       �U @    P2P~�P��P��P��P��P��P��P��P��P                             R @     1�,S,8PTV0���1���1���0���1���0���1���1���1���1��	�	1��	�	1��
�
0���1���0���1���0���0���1���0��
�
0��
�
1�   R @    	0���1� R @   	0�                     UR @    T��T��T��T��T�	�
T��T��T�
�
T�
�
T                            R @    <0�<Yu���0���P��0���U��u���P��0���
0��
�P�
�
0��
�
0��
�0�      �W @    0�Ss�.S                               P @    -R-5X56�R�6=R=R�R�R`R`i�R�iwRw��R���R���R���R���R���R���R�               @    GRG��R���R���R���R���R�               @    SQS��Q���Q���Q���Q���Q�                     @    =X=�U���X���U��X���X���U��R���X�                   @    =Y=�V���Y���V��Y���Y���V���Y�  ] @   �0�    ] @    Q��Q�     ] @    �V���Y�     ] @    �U���X�  � @   �_  � @   �U       � @    0�=T=Zt�ZzTz�t���T            � @    PISIUPU�S��P��S           � @    
Q
/T/��Q���T���Q�     � @     P��P   � @   ���   �   � @   ��z   � �
              ��R���R���R       ��Q���Q���Q             ��X��T���X���T���X���T      ��0���	����0�  ��0�       ��P��S��S       ��R��S���R�     ��Q���Q�     ��R���R�           ��Q��T���Q���T���Q�       ��P��S��S       �
�
R�
�� ��R             �
�
q �
�
	^�q��
�^�]���^�]���^�]���^�]���^�]�     �
�	����S��	��               �
�
0��0�����\�^����
0��0�����	0��X����	0��^����P�^����\�^����
0��0���                   �
�
0��0�������~�������
0��0�������~�������
0��0�����	0��P����0��������
P���������~�������
0��0���                   �
�
0��0�������~���~����
0��0�������~���~����
0��0�����	0��P����0����~����
P���~������~���~����
0��0���               �
�
0��0�����
_�������
0��0�����	0��P����0��������
P�������
_�������
0��0���                   �
�
0��0�������~�������
0��0�������~�������
0��0�����	0��P����0��������
P���������~�������
0��0���     �
�P��S     ��P��V     ��P��T      ��P��U��U      ��P��V��V      ��P��T��T         �
�
P�
�S��R��S      ��P��]��]     ��P��S     ��P��
S  ��0�  ��0�     �
�
P�
�_       ��P��_��_  ��0�  ��0�                       ��P��_��P��_��P��_��P������0���_��0���_��0���_��0�     ��P��]     ��P��^       ��P��^��^       ��P��\��\       ��P��^������0�  ��0�  ��0� ����m    ��
�{ @   � ���   ��0� ����m    ��
�{ @   � ���   ��0� ����m    ��
�{ @   � ���   ��0� ����m    ��
�{ @   � ���   ��0� ����m    ��
�{ @   � ���   ��0�     �
�
R�
�
�R�     �
�
P�
�
P     �	�	R�	�
�R�           �	�	Q�	�	T�	�	�Q��	�
T�
�
�Q�         �	�	P�	�	�\�	�	P�	�
�\       ��R��S���R�         ��Q��T���Q���T       ��X��R���X�       ��P��S��S           ��R��_���R���_��R       ��Q�����Q     ��P��T      ��0���R��0�          ��0���^��V��^��0�     ��P��sx�   ��P     ��P����   ��P     ��P��P    ��0���0�     ��R���R�    2�T��T           (0P02S��P��S��R      00���0���P       ewQw{R{�x� ��0�   ��P  ��U    ��P��T     ��0���R��r�         ��0���
x � #�1%#���
x � #�1%#���x � #�1%���
x � #�1%#���
x � #�1%#�                ��P��x ��x~��P��R��x ��P��x~       ��R��T���R�       ��Q��S���Q�       ��X��U���X�     ��P��S       ��R���R���R       ��Q���Q���Q       ��X���X���X  ��	��         ��R��S���R���R         ��Q��T���Q���Q       ��X���X���X 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�| @   ���
�| @   ���
} @   ���
�| @   ���
F} @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ���'�     ��U  ��2�  ���'�     ��U  ��1�  ���'�     ��U  ��1�  ���'�     ��U  �	�	4�  �	�	�'�     �	�	U  �	�	4�  �	�	�'�     �	�	U  �	�	8�  �	�	�'�     �	�	U  �	�	8�  �	�	�'�     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�i�     �
�
T  �
�
4�  �
�
�i�     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � �                        �
�
R�
�
S�
�
�R��
�S���R���S���R���S                 �
�
Q�
�
T�
�
�Q��
�T���Q���T���Q���T    �
�S��S         �	�	R�	�
S�
�
�R��
�
S         ��R��S���R���S     ��0���T��0�             ��R��S��p�}���S���R���S     ��P��P  ��S��S      ��s����R��s����s��      ��P��U��P  ��S��S  ��s�      ��P��U��P              RmSm��R���S���R���S���R�           x�P��Q��Q��Q��P     4ZP��P         �	�	R�	�	S�	�	�R��	�	S �	�	0� ;            ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�     ��Y���Y�     ��R���R�     ��Q���Q�     ��X���X�             �	�
R�
�\��R��\���R���\           �	�
Q�
�V��Q��V���Q���V           �	�
X�
�]��X��]��]         �	�
Y�
�^���Y���^               �	�
�(���(��_���(��_���(��_       �
�
P�
�p���P��p���P             �
�
P�
�S��-���S��S��S     ��
0��
�
P��0�     ��R��	�      ��Q��	�         ��X��\���X���	\     ��Y��	�     ��U��	U               ��P��X����������P������	���	�	��         ��T��T��P��	T      ��0���_��_                  ��0�����������R��1�������0���	0��	�	0�            ��	����_��_��	����	_�	�	_        WRW��R���R          Q�S���Q���S        WXW��X���X          QYQ�U���Y���U          MW\WgQg�\��|���\   ?�P  ?W0�      Mg0�g�R��r���R��0�  6M]       �V���Q�R���V       �T���X�Q���T       MW\W�Q��Q��\     gsZ��Z       %T%%P%,Q,6]��T     %V%/P��V        ,Q,/]/6Q��Q       ��R��S���R�  ��T    ��R��S T                RQ�R�        QX�Q�        X�`�X� )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                                                                                                                 X         Z���� ���� ���� ���� ���� ���� ���� X         ` @    ��� g @    (��� :W @    	Q P @   ��Q @   � �         ���� ������ ���� ������ ���� ������ ������ ���� ������ ������ ���� �������������� S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ���� >         �������� ���� ���� ����          6��                                                                                                                                                                                                                                                  .file   a   ��  gcrtexe.c              �                                �              �   `                        �   p                        �   �                        %  �                        @  P                        `             k  0                        �                          �  �                        �  P                        �  0          �  `                    envp           argv            argc    (                                                   '  �          9  �                        ^  �                        �             �                           �  @                        �  �                          0                    mainret            "  �                        8  �                        N                           d  �                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  �     +                 .file   �   ��  gidevicepair.c         m  P                       udid    0           �             �  �      main    �A          �              �  p                        �  �                    .text   P     �  @             .data                          .bss    0                       .rdata         �
  7             .xdata  t      ,                 .pdata  l      $   	                 �  �A       ~                 �  �                           �  �                          �  �&  
   i(                  �  �     [                    �  �     C                   �  0      @                    �  \      \                      9     l                          )                       �     �                    )  �     +                     4  P     �               .text   0      .idata$7l      .idata$5�      .idata$4�      .idata$6d      .text   8      .idata$7h      .idata$5�      .idata$4�      .idata$6L      .text   @      .idata$7d      .idata$5�      .idata$4�      .idata$68      .text   H      .idata$7`      .idata$5�      .idata$4�      .idata$6$      .text   P      .idata$7\      .idata$5�      .idata$4�      .idata$6      .text   X      .idata$7X      .idata$5�      .idata$4�      .idata$6�      .text   `      .idata$7T      .idata$5�      .idata$4�      .idata$6�      .text   h      .idata$7P      .idata$5�      .idata$4�      .idata$6�      .text   p      .idata$7L      .idata$5�      .idata$4�      .idata$6�      .text   x      .idata$7H      .idata$5x      .idata$4�      .idata$6t      .text   �      .idata$7D      .idata$5p      .idata$4�      .idata$6`      .text   �      .idata$7@      .idata$5h      .idata$4�      .idata$6P      .file   �  ��  guserpref.c              �                           !  @           .  0
          I  �
          a  �
          {  �
          �  �          �  
          �  �
          �  �
          	  �          !  �          9            Z  �          {             �         .text   �     �  �             .data                           .bss    @                       .rdata  �
     �                 .xdata  �      �                 .pdata  �      �   -                 �   O  
    =  �                �  �     �                    �  
     �
  
                 �  p      0                    �  �      �                       �     T  '                   H     -                       �     &                    )        +                     4  8     p               .text   p      .idata$7P      .idata$5@      .idata$4X      .idata$6      .text   x      .idata$7L      .idata$58      .idata$4P      .idata$6�      .text   �      .idata$7H      .idata$50      .idata$4H      .idata$6�      .text   �      .idata$7D      .idata$5(      .idata$4@      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4       .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7       .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6t      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6d      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6L      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$68      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6       .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6�
      .text          .idata$7�      .idata$5x      .idata$4�      .idata$6�
      .text         .idata$7�      .idata$5p      .idata$4�      .idata$6�
      .text         .idata$7�      .idata$5h      .idata$4�      .idata$6�
      .text         .idata$7�      .idata$5`      .idata$4x      .idata$6�
      .text          .idata$7�      .idata$5X      .idata$4p      .idata$6�
      .text   (      .idata$7�      .idata$5P      .idata$4h      .idata$6x
      .text   0      .idata$7�      .idata$5H      .idata$4`      .idata$6h
      .text   8      .idata$7�      .idata$5@      .idata$4X      .idata$6T
      .text   @      .idata$7�      .idata$58      .idata$4P      .idata$6D
      .text   H      .idata$7�      .idata$50      .idata$4H      .idata$68
      .text   P      .idata$7�      .idata$5(      .idata$4@      .idata$6,
      .text   X      .idata$7�      .idata$5       .idata$48      .idata$6 
      .text   `      .idata$7�      .idata$5      .idata$40      .idata$6
      .text   h      .idata$7�      .idata$5      .idata$4(      .idata$6 
      .text   p      .idata$7�      .idata$5      .idata$4       .idata$6�      .text   x      .idata$7�      .idata$5       .idata$4      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4       .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6x      .text   �      .data          .bss    P       .idata$7�      .idata$5�
      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �
      .text   �                       .data                           .bss    P                        .idata$2                     .idata$4�      .idata$5�
      .file   E  ��  gfake              .text   �                       .data                           .bss    P                        .idata$4�                      .idata$5�
                      .idata$7�     
                 .text   �      .idata$78      .idata$5      .idata$4       .idata$6P      .text   �      .idata$74      .idata$5       .idata$4      .idata$64      .text   �      .idata$70      .idata$5�      .idata$4      .idata$6      .text   �      .idata$7      .idata$5       .idata$4      .idata$6|      .text   �      .idata$7      .idata$5�
      .idata$4      .idata$6h      .text   �      .idata$7      .idata$5�
      .idata$4      .idata$6T      .text   �      .idata$7      .idata$5�
      .idata$4       .idata$6<      .text   �      .idata$7      .idata$5�
      .idata$4�      .idata$6$      .text          .idata$7      .idata$5�
      .idata$4�      .idata$6      .text         .idata$7       .idata$5�
      .idata$4�      .idata$6�      .text         .idata$7�      .idata$5�
      .idata$4�      .idata$6�      .text         .idata$7�      .idata$5�
      .idata$4�      .idata$6�      .text          .idata$7�      .idata$5�
      .idata$4�      .idata$6�      .text   (      .idata$7�      .idata$5�
      .idata$4�      .idata$6�      .file   i  ��  ggccmain.c             �  0                       p.0                �  p          �  �                    __main  �             P       .text   0     �                .data                         .bss    P                       .xdata  `                      .pdata  P     $   	                 �   �  
   a                   �  �
     ?                    �  �     5                     �  �      0                      �     '                     �     �                     )  P     +                     4  �     �                .file     ��  gnatstart.c        .text                           .data                         .bss    `                           �  a�  
     
                 �  �     �                     �  �                                   V   
                   u                            �                         )  �     +                 .file   �  ��  gwildcard.c        .text                           .data                          .bss    p                            �  c�  
   �                    �  �     .                     �  �                             v     :                      �     �                     )  �     +                 .file   �  ��  gdllargv.c         _setargv                        .text                          .data   0                       .bss    p                        .xdata  x                      .pdata  t                         �  �  
   �                   �  �     :                     �       0                      �     V                      `     �                     )  �     +                     4  0     0                .file   �  ��  g_newmode.c        .text                          .data   0                       .bss    p                           �  q�  
   �                    �  �     .                     �  @                                 :                      �     �                     )       +                 .file   �  ��  gtlssup.c                                             @          *  �                    __xd_a  P       __xd_z  X           A  �      .text        �                .data   0                       .bss    �                       .xdata  |                      .pdata  �     $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata        H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  ��  
   �  6                 �  $     �                    �  4                        �  `     0                      @                          �                            �	     �                     )  @     +                     4  `     �                .file     ��  gxncommod.c        .text   �                       .data   0                       .bss    �                           �  ¢  
   �                    �       .                     �  �                            V     :                      �
     �                     )  p     +                 .file   +  ��  gcinitexe.c        .text   �                       .data   0                       .bss    �                        .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  L�  
   {                   �  9     a                     �  �                            �     :                      (     �                     )  �     +                 .file   K  ��  gmerr.c            _matherr�                       .text   �     �                .data   0                       .bss    �                        .rdata  `     @               .xdata  �                      .pdata  �                         �  Ǥ  
   6  
                 �  �                         �  K     �                    �  �     0                      �     �                      �     �                     )  �     +                     4       X                .file   h  ��  gCRT_fp10.c        _fpreset�                       fpreset �      .text   �                      .data   0                       .bss    �                        .xdata  �                      .pdata  �                         �  ��  
   �                    �  �     -                     �        0                      �     X                      �     �                     )        +                     4  `     0                .file   |  ��  gmingw_helpers.    .text   �                       .data   0                       .bss    �                           �  ��  
   �                    �  �     .                     �  0                            �     :                      #
     �                     )  0     +                 .file   �  ��  gpseudo-reloc.c        M  �                           \  `          r  �       the_secs�           ~  �          �  �           �                           �                      .text   �     =  &             .data   0                       .bss    �                       .rdata  �
     [                .xdata  �     0                 .pdata  �     $   	                 �  &�  
   K  �                 �  �     �                    �  �     �  
                 �  P     0                    �       W                            �                     �     	                       �
     O                    )  `     +                     4  �     �                .file   �  ��  gusermatherr.c           0"                             �           %  p"      .text   0"     L                .data   0                       .bss    �                       .xdata  �                      .pdata  �                         �  q�  
   �                   �  �                         �  X     r                     �  �     0                      �#     �                            �                     )  �     +                     4  p	     P                .file   �  ��  gxtxtmode.c        .text   �"                       .data   0                       .bss    �                           �  f�  
   �                    �  �     .                     �  �                            H$     :                      �     �                     )  �     +                 .file   �  ��  gcrt_handler.c         <  �"                       .text   �"     �               .data   0                       .bss    �                       .xdata  �                      .rdata        (   
             .pdata  �                         �  ��  
   �                   �       ~                    �  �     _                    �  �     0                      �$     �  
                   �                            �                         )  �     +                     4  �	     P                .file   %  ��  gtlsthrd.c             S  @$                           s             �             �  �$          �            �   %          �  �%      .text   @$     b  "             .data   0                       .bss          H                 .xdata  �     0                 .pdata       0                    �  ��  
   �
  A                 �  �     a                    �  )!     �                    �        0                    �  �                            &     x                     �     %                    )        +                     4  
     (               .file   9  ��  gtlsmcrt.c         .text   �&                       .data   0                      .bss    `                           �  ��  
   �                    �  �     .                     �  0                            �(     :                      �     �                     )  P     +                 .file   M  ��  g    �            .text   �&                       .data   @                       .bss    `                          �  +�  
   �                    �       0                     �  P                            �(     :                      [     �                     )  �     +                 .file   �  ��  gpesect.c              	  �&                           	  �&          )	  0'          >	  �'          [	  P(          s	  �(          �	  )          �	  P)          �	  �)      .text   �&     �  	             .data   @                       .bss    p                       .xdata  $     ,                 .pdata  4     l                    �  ��  
   �  �                 �  O     �                    �  �#     �                    �  p     0                    �  �     �                       �(     K                     �     T                            �                     )  �     +                     4  8     (               .text   �*     2                 .data   @                       .bss    p                       .text   �*                       .data   @                       .bss    p                           )  �     +                 .file   �  ��  gdirent.c          opendir �*                       readdir �,      closedir/          �	  p/      telldir �/      seekdir 0      .text   �*       #             .data   @                       .bss    �                       .xdata  P     H                 .pdata  �     H                    �  ��  
   f  h                 �  �                          �  �'     �                    �  �     0                    �  �     B                       F.     �                          -                       �                         )       +                     4  `     �               .file   �  ��  ggetopt.c              �	  1                       warnx   �1          �	  p2      place   P      ambig   �          �	  p      noarg   �          
             
   6          )
  @          ;
  D          F
  H          S
  �          ^
  `      getopt  0<          i
  `<          u
  �<      .text   1     �  t             .data   @     $                .bss    �                      .xdata  �     d                 .pdata  �     T                .rdata  @     B                    �  3  
   <  �                 �  �#     �                    �  �*     ?                    �  �     0                    �  �                            �3     a	                     F     +                            .                    )  @     +                     4        �               .file   �  ��  gmingw_matherr.    .text   �<                       .data   p                      .bss    �                           �  o 
   �                    �  t'     .                     �                               /=     :                      ?     �                     )  p     +                 .file     ��  gucrt_vfprintf.    vfprintf�<                       .text   �<                     .data   �                     .bss    �                       .xdata  �                      .pdata  <                         �  
 
   �                   �  �'     8                    �  �0     X                     �        0                      i=     r   	                   �     �                     )  �     +                     4       8                .file   0  ��  gucrt_printf.c     printf  �<                       .text   �<     H                .data   �                     .bss    �                       .xdata                        .pdata  H                         �  � 
   �  
                 �  �(     l                    �  Q1     -                     �  P     0                      �=     �   	                   �     �                     )  �     +                     4  H     H                .file   N  ��  gucrt_fprintf.c    fprintf =                       .text   =     2                .data   �                     .bss    �                       .xdata                        .pdata  T                         �  ] 
   �                   �  F*     b                    �  ~1     F                     �  �     0                      p>     �   	                   �     �                     )        +                     4  �     8                .file   d  ��  g__initenv.c           �
  �          �
  �      .text   P=                       .data   �                     .bss    �                          �  � 
   �                   �  �+     �                     �  �                            �>     [                      �                         )  0     +                 .file   �  ��  gucrtbase_compa        �
  P=                           �
  `=          �
  �=      _onexit @>          �
  `>          �
  �                          �>            �>      tzset   �>          !  @                    _tzset  0?          =            N            _            o  $                   .text   P=       "             .data   �     x   
             .bss    �                       .xdata       P                 .pdata  `     l                .rdata  �                          �  �% 
   �  Y                 �  P,                          �  �1     |                    �  �     0                      U?     �                     q                            �     `                    )  `     +                     4  �     �               .text   p?      .data   @      .bss    �      .idata$7�      .idata$5p
      .idata$4�      .idata$6V      .text   x?      .data   @      .bss    �      .idata$7�      .idata$5x
      .idata$4�      .idata$6d      .text   �?      .data   @      .bss    �      .idata$7�      .idata$5�
      .idata$4�      .idata$6r      .text   �?      .data   @      .bss    �      .idata$7�      .idata$5�
      .idata$4�      .idata$6~      .text   �?      .data   @      .bss    �      .idata$7�      .idata$5�
      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  p
      .text   �?                       .data   @                       .bss    �                       .idata$2                     .idata$4�      .idata$5p
      .file     ��  gfake              .text   �?                       .data   @                       .bss    �                       .idata$4�                      .idata$5�
                      .idata$7�                      .text   �?      .data   @      .bss    �      .idata$7h      .idata$5(
      .idata$4@      .idata$6      .text   �?      .data   @      .bss    �      .idata$7l      .idata$50
      .idata$4H      .idata$6      .text   �?      .data   @      .bss    �      .idata$7p      .idata$58
      .idata$4P      .idata$6      .text   �?      .data   @      .bss    �      .idata$7t      .idata$5@
      .idata$4X      .idata$6$      .text   �?      .data   @      .bss    �      .idata$7x      .idata$5H
      .idata$4`      .idata$6.      .text   �?      .data   @      .bss    �      .idata$7|      .idata$5P
      .idata$4h      .idata$68      .text   �?      .data   @      .bss    �      .idata$7�      .idata$5X
      .idata$4p      .idata$6B      .text   �?      .data   @      .bss    �      .idata$7�      .idata$5`
      .idata$4x      .idata$6L      .file     ��  gfake              hname   @      fthunk  (
      .text   �?                       .data   @                       .bss    �                       .idata$2�                      .idata$4@      .idata$5(
      .file   p  ��  gfake              .text   �?                       .data   @                       .bss    �                       .idata$4�                      .idata$5h
                      .idata$7�     !                 .text   �?      .data   @      .bss    �      .idata$7       .idata$5�	      .idata$4�      .idata$6r      .text   �?      .data   @      .bss    �      .idata$7$      .idata$5�	      .idata$4�      .idata$6�      .text   �?      .data   @      .bss    �      .idata$7(      .idata$5�	      .idata$4�      .idata$6�      .text   �?      .data   @      .bss    �      .idata$7,      .idata$5�	      .idata$4       .idata$6�      .text   �?      .data   @      .bss    �      .idata$70      .idata$5�	      .idata$4      .idata$6�      .text   �?      .data   @      .bss    �      .idata$74      .idata$5�	      .idata$4      .idata$6�      .text    @      .data   @      .bss    �      .idata$78      .idata$5 
      .idata$4      .idata$6�      .text   @      .data   @      .bss    �      .idata$7<      .idata$5
      .idata$4       .idata$6�      .text   @      .data   @      .bss    �      .idata$7@      .idata$5
      .idata$4(      .idata$6�      .text   @      .data   @      .bss    �      .idata$7D      .idata$5
      .idata$40      .idata$6�      .file   ~  ��  gfake              hname   �      fthunk  �	      .text    @                       .data   @                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�	      .file   
  ��  gfake              .text    @                       .data   @                       .bss    �                       .idata$48                      .idata$5 
                      .idata$7H                       .text    @      .data   @      .bss    �      .idata$7�      .idata$58	      .idata$4P      .idata$6<      .text   (@      .data   @      .bss    �      .idata$7�      .idata$5@	      .idata$4X      .idata$6J      .text   0@      .data   @      .bss    �      .idata$7�      .idata$5H	      .idata$4`      .idata$6X      .text   8@      .data   @      .bss    �      .idata$7�      .idata$5P	      .idata$4h      .idata$6f      .text   @@      .data   @      .bss    �      .idata$7�      .idata$5X	      .idata$4p      .idata$6p      .text   H@      .data   @      .bss    �      .idata$7�      .idata$5`	      .idata$4x      .idata$6�      .text   P@      .data   @      .bss    �      .idata$7�      .idata$5h	      .idata$4�      .idata$6�      .text   X@      .data   @      .bss    �      .idata$7�      .idata$5p	      .idata$4�      .idata$6�      .text   `@      .data   @      .bss    �      .idata$7�      .idata$5x	      .idata$4�      .idata$6�      .text   h@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6�      .text   p@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6�      .text   x@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6�      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6&      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$66      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6X      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6`      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5�	      .idata$4�      .idata$6h      .file     ��  gfake              hname   P      fthunk  8	      .text   �@                       .data   @                       .bss    �                       .idata$2�                      .idata$4P      .idata$58	      .file   B  ��  gfake              .text   �@                       .data   @                       .bss    �                       .idata$4�                      .idata$5�	                      .idata$7�     "                 .text   �@      .data   @      .bss    �      .idata$7�      .idata$5	      .idata$4(      .idata$6      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5	      .idata$40      .idata$6      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5 	      .idata$48      .idata$6(      .text   �@      .data   @      .bss    �      .idata$7�      .idata$5(	      .idata$4@      .idata$62      .file   P  ��  gfake              hname   (      fthunk  	      .text   �@                       .data   @                       .bss    �                       .idata$2�                      .idata$4(      .idata$5	      .file   e  ��  gfake              .text   �@                       .data   @                       .bss    �                       .idata$4H                      .idata$50	                      .idata$7�     "                 .text   �@      .data   @      .bss    �      .idata$7\      .idata$5 	      .idata$4      .idata$6�      .file   s  ��  gfake              hname         fthunk   	      .text   �@                       .data   @                       .bss    �                       .idata$2�                      .idata$4      .idata$5 	      .file   �  ��  gfake              .text   �@                       .data   @                       .bss    �                       .idata$4                       .idata$5	                      .idata$7`                      .text   �@      .data   @      .bss    �      .idata$7(      .idata$5�      .idata$4�      .idata$6�      .text   �@      .data   @      .bss    �      .idata$7,      .idata$5�      .idata$4�      .idata$6�      .text   �@      .data   @      .bss    �      .idata$70      .idata$5�      .idata$4�      .idata$6�      .text   �@      .data   @      .bss    �      .idata$74      .idata$5�      .idata$4       .idata$6�      .text    A      .data   @      .bss    �      .idata$78      .idata$5�      .idata$4      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   A                       .data   @                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   A                       .data   @                       .bss    �                       .idata$4                      .idata$5�                      .idata$7<                      .text   A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text    A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   (A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   0A                       .data   @                       .bss    �                       .idata$2x                      .idata$4�      .idata$5�      .file     ��  gfake              .text   0A                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7      %                 .text   0A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6d      .text   8A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6t      .file     ��  gfake              hname   �      fthunk  �      .text   @A                       .data   @                       .bss    �                       .idata$2d                      .idata$4�      .idata$5�      .file   )  ��  gfake              .text   @A                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�     &                 .text   @A      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4�      .idata$6Z      .file   7  ��  gfake              hname   �      fthunk  �      .text   PA                       .data   @                       .bss    �                       .idata$2P                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   PA                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�                       .text   PA      .data   @      .bss    �      .idata$7�      .idata$5p      .idata$4�      .idata$6J      .text   XA      .data   @      .bss    �      .idata$7�      .idata$5h      .idata$4�      .idata$68      .text   `A      .data   @      .bss    �      .idata$7�      .idata$5`      .idata$4x      .idata$6*      .text   hA      .data   @      .bss    �      .idata$7|      .idata$5X      .idata$4p      .idata$6"      .text   pA      .data   @      .bss    �      .idata$7x      .idata$5P      .idata$4h      .idata$6      .text   xA      .data   @      .bss    �      .idata$7t      .idata$5H      .idata$4`      .idata$6�      .text   �A      .data   @      .bss    �      .idata$7p      .idata$5@      .idata$4X      .idata$6�      .text   �A      .data   @      .bss    �      .idata$7l      .idata$58      .idata$4P      .idata$6�      .text   �A      .data   @      .bss    �      .idata$7h      .idata$50      .idata$4H      .idata$6�      .text   �A      .data   @      .bss    �      .idata$7d      .idata$5(      .idata$4@      .idata$6�      .text   �A      .data   @      .bss    �      .idata$7`      .idata$5       .idata$48      .idata$6x      .text   �A      .data   @      .bss    �      .idata$7\      .idata$5      .idata$40      .idata$6`      .file   �  ��  gfake              hname   0      fthunk        .text   �A                       .data   @                       .bss    �                       .idata$2<                      .idata$40      .idata$5      .file   �  ��  gfake              .text   �A                       .data   @                       .bss    �                       .idata$4�                      .idata$5x                      .idata$7�     
                 .text   �A      .data   @      .bss    �      .idata$74      .idata$5      .idata$40      .idata$6�      .text   �A      .data   @      .bss    �      .idata$70      .idata$5      .idata$4(      .idata$6�      .file   �  ��  gfake              hname   (      fthunk        .text   �A                       .data   @                       .bss    �                       .idata$2@                     .idata$4(      .idata$5      .file   �  ��  gfake              .text   �A                       .data   @                       .bss    �                       .idata$48                      .idata$5                       .idata$78                      .file   	  ��  gcygming-crtend        �  @I                       .text   �A                       .data   @                       .bss    �                           �  @I                         �  h                          �  �                         �  XI                         )  �     +                 .idata$2        .idata$5h      .idata$4�      .idata$2       .idata$5�      .idata$4�      .idata$2(       .idata$5�      .idata$4      .idata$2,      .idata$5�
      .idata$4�      .idata$2T      .idata$5(      .idata$4@      .idata$4�      .idata$5�      .idata$7p      .idata$4      .idata$5�      .idata$7      .idata$4(      .idata$5      .idata$7<      .idata$4       .idata$5      .idata$7      .idata$4`      .idata$5H      .idata$7T      .rsrc       
    __xc_z         putchar @          �  �      strcpy  �?          �  (A          �  �
          �  �          
  p?            0          0  �          C  �          U  �          c  �?          |  `          �  �          �  �	          �  �          �  p          �  (          �  �          
              !
  hI          0
  �          ?
            V
  �A          q
  �          �
  x          �
  �          �
  �           �
  pA          �
  �            �
          '  `          I  �          V  �	          g  �          }  8	          �  �          �            �  H          �  �          �             �      	        �  �            �            �@          <  �      __xl_a  0           H  �A          U  �	          x  T          �  �          �  �
      _cexit  8@          �  �          �  �
      wcslen  �?          �  @             `  ��            ��       1  �          _              u  h          �      ��       �     ��       �  0           �            �  8      __xl_d  @                       ,  �	      _tls_end   	        Q  �      __tzname�?          g   
          s  �          �  PA          �  @	          �  �          �  �          �             �  �          �  0           �  �            �          (  x	          5  0          M      	    memcpy  �@          X  �          t  �          �  �          �  X
          �  P      puts    @          �              �            �           +  �          E  p      malloc  �@      _CRT_MT 0          T  �      optarg  �          g  `A      X509_new�          s  8
          �              �  	          �  
          �  �          �  �
          �     ��         �	            (	          -  8          @             T  �	          y  �           �  �      opterr  `          �  8          �  `	          �  (          �            �  `          *  0A          7  �          D  P          ]  �           v  �
      fflush  �?          �  XA          �  d           �  �          �  �
          �  P             �            H          -  �@          B  �          P  �          a  �      abort   �@          �             �  @          �  �          �            �  P       __dll__     ��       �      ��       �  (@            p          &  �          V  �A          k  p          �  P           �  (
          �  
          �  �@          �  �          �  �          �  �       getch   @A          &  �	          2             L     ��       b  0          {            �  h          �  0      BIO_freep      strrchr �@          �  �          �  �      calloc  �@            @
                         �          :  �          H  P          T        EVP_sha1           �  	          �  �          �  �      BIO_ctrlx          �  �          �  �      RSA_new �          �  �            8      Sleep   hA            `      _commode�           &  �          5  @          B            Q  
          ^  PI          l  �          x  �          �   A          �            �  �          �  �           �  �            <       optind  \          &  �      __xi_z  (           2  x          L  �          e  p          }            �             �  �          �  @          �                         *  �      BIO_new h          E  �?          P  �          n             �  �          �  0          �  �       signal  �@          �  �?          �  (           �  h                           h          +  �      strncmp �?          ;  PI      strncpy �?          J             j  �	          w  �          �             �  �       realloc  A          �  �
          �  (          �  �            �            p          7  �A          L      ��       _  �          �         BN_free P          �  �          �  �          �  �          �  �          +  `          @             T  H	          f  H
          s  <          �  �          �            �  @          �  �          �  X             p
                ��       0   @      strdup  �?          P   �A          c   8A          q   �A          �   p@          �   �          �   P@          �   �A          �   �
          �   �	          !  �           /!              N!  �          ]!     ��       r!              �!  �          �!  �
          �!  �
          �!  P          �!  ,          �!  h          �!  �          !"  8          <"  A          I"  P	      __xl_z  H           V"  8      __end__              h"  �	          �"  P      isprint �?          �"  �          �"  �          �"  0          �"        strcmp  �?          �"  hI          #  H          #  p      __xi_a             >#  �@          M#  �          d#  X          p#  xA      __xc_a              �#   	          �#     ��       �#  P           �#  x           �#     ��   _fmode  �           $  �          $  `          *$  �          >$  x          W$  �          k$  p          �$  �@          �$  x
          �$            �$  �          �$  H@          �$  @          %             %  �	          *%  p	          <%  �          q%  �          �%  `          �%   @          �%  �          �%  h          �%            �%  �      fputc    @      __xl_c  8           &  �          &  @          (&             G&     	    optopt  X          T&  @          i&  p          |&  �	          �&  �          �&  �           �&             �&  �          �&  H          '  �          '             *'  x?          5'  �
          O'  <      _newmodep           }'  �@          �'  (      fwrite  @      _time64 �?          �'  P
          �'  0          �'  �	          �'      ��       �'      ��       �'   	          �'  �
          (  �          !(            <(  x          U(  �*          b(  �          x(  `           �(  �?          �(  X          �(  `
          �(            �(  �      exit    �@          �(            �(             )     ��       )  �	          +)             :)      ��       R)  �	      _errno  `@          g)  X          v)  h	      _exit   h@          �)  �          �)             �)  (          �)  �          �)  @@      strlen  �?          �)             *  X@          *  �          3*  �      strchr  �@          H*  �A          ^*  x@          {*  �          �*  X	          �*  �           �*  �          +  �          +  0
          &+  �          5+  T          N+  (          b+  �
          s+  A      BN_new  H          ~+            �+  �          �+  0          �+  �          �+  P           �+  �          �+  �          �+  �?          ,  �          ,  �          .,  0@      free    �@          :,  �           K,  �          \,  P      x,  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame print_error_message pairing_cb print_usage.isra.0 longopts.0 .rdata$.refptr.optarg .rdata$.refptr.optind .text.startup .xdata.startup .pdata.startup userpref_get_config_dir.part.0 __config_dir X509_add_ext_helper.isra.0 userpref_get_config_dir userpref_read_system_buid userpref_get_paired_udids userpref_save_pair_record userpref_read_pair_record userpref_delete_pair_record pair_record_generate_keys_and_certs pair_record_get_host_id pair_record_set_host_id pair_record_get_item_as_key_data pair_record_import_key_with_name pair_record_import_crt_with_name pair_record_set_item_from_key_data __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names rewinddir permute_args parse_long_options illoptstring recargstring getopt_internal posixly_correct.0 nonopt_end nonopt_start illoptchar recargchar getopt_long getopt_long_only local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 __imp_X509_set1_notAfter _fullpath __imp_plist_get_string_val ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight __imp_usbmuxd_read_buid __imp_X509_add_ext usbmuxd_read_buid ASN1_TIME_set __stdio_common_vfwprintf BIO_new_mem_buf X509_set1_notBefore __imp_abort __lib64_libkernel32_a_iname __imp_PEM_write_bio_X509 __imp_GetEnvironmentVariableW __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname SHGetSpecialFolderLocation X509V3_EXT_conf_nid __imp_idevice_new_with_options __imp_X509_free _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter ASN1_INTEGER_free __imp_plist_from_json .refptr.__mingw_initltsdrot_force __imp_calloc __imp___p__fmode X509_set_serialNumber __imp___p___argc plist_get_node_type __imp_BIO_new __imp_EVP_PKEY_free X509_EXTENSION_free __imp_tzname ___tls_start__ .refptr.__native_startup_state X509_free _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment libusbmuxd_2_0_dll_iname __rt_psrelocs_start __imp_plist_get_node_type __imp_lockdownd_unpair __imp_plist_from_memory lockdownd_pair_cu __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp_usbmuxd_read_pair_record __imp_SHGetSpecialFolderLocation __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ __imp_fputc .refptr.optind VirtualQuery __imp___p___argv string_concat ASN1_INTEGER_new ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll __imp_ASN1_INTEGER_free __imp__errno .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free __lib64_libole32_a_iname __lib64_libapi_ms_win_crt_conio_l1_1_0_a_iname __imp_strncpy .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ plist_new_string __mingw_oldexcpt_handler __imp_X509_set1_notBefore .refptr.optarg __imp__findfirst64 TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free lockdownd_query_type __imp__configure_wide_argv __imp_at_quick_exit PEM_read_bio_RSAPublicKey __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ X509_add_ext .refptr.__mingw_app_type __mingw_initltssuo_force __imp_plist_get_data_val VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__findnext64 __imp__tzset ___crt_xp_start__ __imp_X509_sign __imp_LeaveCriticalSection __C_specific_handler ASN1_TIME_new __mingw_optreset .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ BN_set_word __imp___ms_fwprintf plist_free ___crt_xp_end__ __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR _head_lib64_libapi_ms_win_crt_conio_l1_1_0_a __imp_strdup __imp_puts _set_new_mode .refptr.__xi_a .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit _head_libcrypto_3_x64_dll __section_alignment__ __imp_GetFileAttributesA __native_dllmain_reason __imp_PEM_write_bio_PrivateKey lockdownd_unpair __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname __imp_ASN1_TIME_new __imp_strcpy _tls_used __imp_lockdownd_client_new CoTaskMemFree __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy X509V3_set_ctx __RUNTIME_PSEUDO_RELOC_LIST__ __imp_X509_EXTENSION_free __imp_X509_new __imp_lockdownd_cu_pairing_create EVP_PKEY_assign .refptr._newmode plist_new_data __data_end__ __imp_BIO_free __imp_fwrite __CTOR_LIST__ __imp_getch __imp__set_new_mode _findnext64 PEM_write_bio_PrivateKey __imp_lockdownd_query_type _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __bss_end__ __imp_RSA_generate_key_ex __imp_X509V3_EXT_cleanup idevice_set_debug_level __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __imp_EVP_PKEY_assign __lib64_libapi_ms_win_crt_filesystem_l1_1_0_a_iname libcrypto_3_x64_dll_iname usbmuxd_delete_pair_record __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection __imp_X509_set_version __imp_BN_new _tls_index __acrt_iob_func _head_libimobiledevice_glue_1_0_dll __native_startup_state ___crt_xc_start__ lockdownd_client_free __imp__fullpath ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal plist_read_from_filename plist_get_data_val _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_plist_new_data EVP_PKEY_new plist_get_string_val __imp_RSA_new .refptr.__mingw_initltsdyn_force SHGetPathFromIDListW __rt_psrelocs_size __imp_plist_print_to_stream_with_indentation plist_dict_set_item .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __imp_X509_set_serialNumber __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname lockdownd_client_new RSA_generate_key_ex __imp___p___wargv __imp_strlen libimobiledevice_glue_1_0_dll_iname __imp_malloc plist_from_json .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection GetFileAttributesA __p__wenviron GetEnvironmentVariableW _initialize_narrow_environment __imp_realloc _crt_at_quick_exit InitializeCriticalSection __imp_CoTaskMemFree __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv __imp_plist_dict_get_item __imp_plist_new_string __imp_EVP_PKEY_new _head_libplist_2_0_dll __IAT_start__ plist_print_to_stream_with_indentation __lib64_libshell32_a_iname _findfirst64 __imp__cexit __imp_BN_set_word __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp_ASN1_INTEGER_set __imp_ASN1_TIME_free __imp__onexit __imp_BIO_new_mem_buf __DTOR_LIST__ lockdownd_pair usbmuxd_save_pair_record_with_device_id __set_app_type __imp_ASN1_INTEGER_new __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ _head_lib64_libapi_ms_win_crt_filesystem_l1_1_0_a __subsystem__ X509_set_version __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __imp_ASN1_TIME_set __imp_idevice_get_udid __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __imp_usbmuxd_save_pair_record_with_device_id __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __imp_X509V3_EXT_conf_nid __imp_PEM_read_bio_RSAPublicKey __p___argc __imp_lockdownd_pair_cu __imp_VirtualProtect plist_from_memory idevice_free plist_to_bin _head_lib64_libshell32_a __imp_plist_read_from_filename ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm X509_set_pubkey __mingw_initltsdyn_force _dowildcard ASN1_INTEGER_set __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname idevice_get_udid __dyn_tls_init_callback __timezone __imp_plist_dict_set_item __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname _initterm __imp_BN_free __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __imp_strchr __imp__time64 __imp_lockdownd_pair __imp_SHGetPathFromIDListW usbmuxd_read_pair_record ___chkstk_ms __imp_X509_set_pubkey __native_startup_lock __p__commode BIO_s_mem __imp_wcslen _head_lib64_libole32_a __rt_psrelocs_end __imp_string_concat __imp_plist_to_bin __minor_subsystem_version__ __imp_fflush __imp_BIO_ctrl __minor_image_version__ __imp___set_app_type __imp_EVP_sha1 __imp__crt_at_quick_exit __imp_printf __imp_BIO_s_mem __imp_usbmuxd_delete_pair_record .refptr.__xc_a _configure_narrow_argv .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR __imp_X509V3_set_ctx DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv __imp_isprint ASN1_TIME_free _head_libusbmuxd_2_0_dll plist_dict_get_item __imp_plist_free _findclose PEM_write_bio_X509 .refptr.__xc_z EVP_PKEY_free __imp__get_output_format ___crt_xt_end__ X509V3_EXT_cleanup X509_sign __stdio_common_vfprintf X509_set1_notAfter __imp_daylight __p___wargv __mingw_app_type __imp__findclose lockdownd_cu_pairing_create 