MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �=�g t �  � & ( @   |     �        @                       0    F�  `                                             �  L   �  �   �             �  �                            h  (                   ȳ  �                          .text   �?      @                 `  `.data   @   P      F              @  �.rdata  p   `      H              @  @.pdata     �      \              @  @.xdata  ,   �      b              @  @.bss    �   �                      �  �.idata  L   �      f              @  �.CRT    `    �      v              @  �.tls        �      x              @  �.rsrc   �   �      z              @  �.reloc  �    �      �              @  B/4      `         �              @  B/19     �      �              @  B/31     �-   0  .   �             @  B/45      =   `  >   �             @  B/57     �   �                  @  B/70     x   �                   @  B/81     &   �     $             @  B/97     �-   �  .   B             @  B/113    u         p             @  B                                                                                                                                                                                                                                                                                                                                                        �ff.�     @ H��(H��[  1��    H��[  �    H��[  �    H�[  f�8MZuHcP<HЁ8PE  tfH�_[  �
��  � ��tC�   �I/  �.  H�\  ���.  H��[  ����  H��Z  �8tP1�H��(Ð�   �/  �@ �Pf��tEf��u����   �{������   1Ʌ����i����    H�
�[  ��!  1�H��(�D  �xt�@���D���   1�E�����,���f�H��8H��[  L�֎  H�׎  H�
؎  � ���  H�1[  D�H���  H�D$ �M+  �H��8��    ATUWVSH�� H��Z  H�-Ȣ  1�eH�%0   H�p��    H9��g  ��  ��H���H�3H��u�H�5\Z  1�����V  �����  ��     ����L  ���e  H��Y  H� H��tE1��   1����(  H�
�Z  �#�  H��Y  H�
����H��}-  �  �ҍ  �{Hc�H��H����-  L�%��  H�Ņ��F  H��1�I��o,  H�pH���-  I��H�D I�H��H���K-  H9�u�H�H�    H�-]�  �  H��X  L�B�  �
L�  H� L� H�7�  �-  �
�  ��  ����   � �  ��ttH�� [^_]A\�f�     H�5	Y  �   ���������   �*  ��������H��X  H�
�X  �Y,  �   �������1�H�����f�     ��+  ���  H�� [^_]A\�f.�     H��X  H�
�X  �   ��+  �7���f�H����������	,  �H��(H��W  �    ������H��(� H��(H��W  �     �z�����H��(� H��(�)  H���H��(Ð�����������H�
	   �����@ Ð��������������WVSH��0H��H��K  H���/  H��H��tH���  H��H��K  H���
  H��H��t]H����  H��H��tMH��tH1�H�T$ H��H�D$ �  1�H��H�T$(H�T$(�  H�T$ H�L$(�)+  H�����f�     1�H��0[^_�fD  H��8H�T$(L�D$$H�D$(    �D$$    �  H�L$(H��t�%*  �   �R�  H����)  �H��8�@ WVSH�� H�Ϲ(   H��L����*  f��@@�@$    �    H�xH�pH�XH�� [^_�f�     UWVSH��X����   H�M  ��Hc�H���1�L�iK  H�
}K  1��v���H�-GJ  1�H��1��     H�T$(H�l$8H�\$@H�\$ �t$ H�D$0�^  H��H�-�  H���,  H��H���q  ��    ����  H�[ H��t�3��u�H�
ъ  I��H���  ��u�H�ˉ  H��X[^_]�1�H�-�J  1�1�1��f���L��I  H��J  1�1�����H��J  1�L�jI  H��H�-`I  ����1�H�C 1��#���L�[I  H�LJ  1�1��l���H�>J  1�L�~J  H��H�-1I  �M���1�H�C 1������L�mJ  1�H�
kJ  H�=�J  H�-I  ����L�dJ  H��1�H���	���H��L�hJ  1�H�C H�������1�H�F 1�1�����1�H�-�H  1Ҿ   H��H  �f���1�H�-�H  1�1�H�I  �M���1�H�-�H  1��   H��H  �1���1�H�-�H  1��   H��H  ����1�H�-H  1�1�1�����L�%H  1�H�
�I  �   �G���1�L�	H  H�
�I  H��H�-�G  �(���H�EH  H�C 1�����1�L��H  H��I  ����1�L�yH  H��I  H��H�-hH  �����1�L�ZH  H�zI  H�C H�������1�L�>H  H�kI  H�F H������1�L�"H  H�\I  H�G H������1�L�H  H�MI  H�F H���r���1�L��G  H�>I  H�G H���V���1�L��G  H�/I  H�F H���:���1�L��G  H� I  H�G H������1�L��G  H�I  H�F H������1�L�zG  H�I  H�G H�������1�L�^G  H��H  H�F H�������1�L�BG  H��H  H�G H������1�L�&G  H��H  H�F H������1�L�
G  H��H  H�G H���v���L��F  H��H  1�H�F H���Z���H��H  1�L��F  H�G H���>���H��F  H�F 1��   �����1�L��F  H��G  ����1�L��F  H��G  H��H�-�F  �����1�L��F  H��G  H�C H�������1�L�zF  H�|G  H�F H������1�L�^F  H�mG  H�G H������1�L�BF  H�^G  H�F H������1�L�&F  H�OG  H�G H���g���1�L�
F  H�@G  H�F H���K���1�L��E  H�1G  H�G H���/���1�L��E  H�"G  H�F H������1�L��E  H�G  H�G H�������1�L��E  H�G  H�F H�������1�L�~E  H��F  H�G H������1�L�bE  H��F  H�F H������1�L�FE  H��F  H�G H������L�,E  H��F  1�H�F H���k���H��F  1�L�E  H�G H���O���H��D  H�F 1��   �����1�H�-�C  1�1�1������@ H�SL�CI��H�
�  ��
  �������H�
�  H�fF  �6  H������������SH�� �/   H���~#  H�
�F  H��H�PHE�H���  H�
�F  �"  H�
�F  �"  H�
�F  �"  H�
G  �"  H�
G  �"  �
   �n"  H�
'G  �j"  H�
,G  H�� [�Y"  �SH�� H�
�  L�%G  H��  ��  �σ  ����   H�
��  L��  H�>G  �  ���  ��u7H�ȃ  H��tOf�: tIH�
��  L���  �  ����   H�� [�@ H�
��  L���  H�G  �>  �L�  ��t�H�
G  �!  �   �"  H�
O�  �B  � �  1��   H�6�  ���  A��H�VF  H���v  �   ��!  �   ���  A�(   �   H�
�F  I���!  �   �!  �ff.�     �UWVSH��(D�H��E��t>H�qH��H�=�F  fD  H��H��H���  D�F�H��H��E��u�)��H�H��E  H��([^_]Ð1�H�T$L�D$L�L$ �ff.�      WVSH��@E1���H��H��H�
��  H��H�PH9�s	H��,H9�rD��H��@[^_�D  Hy�  H�t$ �oD$ �B�|$ BTNC�D$0tr�U	  ����   �D$ H�{H�KH���H�t$$�H�D$$H)�H)΁�   H�CH��$  ��H���  �H�Hǃ     A�   D��H��@[^_��    ��  ��t6�F�H�FH��  ��@ H���p	  �oL$ �@�D$0�_��� H���	  H���ff.�     H��(H�
��  H��t�+   H�p�      �n�      H��(ÐWVSH�� H��H���N  H������   H��+D  �3��gfCSt.�gfCS�d  9�t H���  ��tRH9�r�1�H�� [^_� �sH��H�5�  �  I��H��H��H�ބ  �Q  �C�؄  �   H�� [^_� H���8  H��� H���(  H���^�����  H�Q��t&L���  A��M9�r#�  1�I9�r
H�o�  L�H����     1�H���f.�     ��  �f�     AVAUATUWVSH�� �4�  A��I�օ�tb1�L�%��  ��    ��tE9,$t^��;-�  s;L����1�������  ��u�L��L���  �A   H���H����f�     1ۉ�H�� [^_]A\A]A^�D  H���  I�~L��H���I�H�p�  I��  I)�A��  L)����H��ATUWVSH��0  L�d$ H��L��L��������tl�G  ���~   ��$$  H9�H��HF؋�$(  ��tZL��  ��I9�r4ȉ�I9�r+H�  I��H���q  ��H��0  [^_]A\�f�     �����H��0  [^_]A\�D  H�T$$뽐L��L���  �A   H���H����c����;
��  s����� 1��ff.�     f�VSH��(H��H�0~  L���(�����tLH��t:�!�  H�
~  ��t&L�?�  A��M9�r/�  1�I9�r
H�
+�  L�H�H��t��  �H��([^Ð1���ff.�     �H��8H�
�  H��tPH�T$(L�D$$H�D$(    �D$$    �  H�L$(H��t�  �   �Ƒ  H���n  H���  H��8�f�H��8�o  �    H�y�  H��tH��AVAUATUWVSH��P�9H��H�l$K�;  D�Ϻ   H��H�É�L�KC  ���D$0�������D$(���ĉD$ �  H����  �NI����  �NH����  �NI����  �NH���  �NI���  M��H��H��B  H����  M��H��H��B  ��  I��H��H��B  ��  M��H��H��B  �  I��H��H��B  �  I��H��H��B  �  I��H��B  H���x  H��H��P[^_]A\A]A^�fD  �+  ff.�     AWAVAUATUWVSH���  1�L��$�   H��$  �A   ��L���H��    �����   H��H��  H��t
H�=�  w��H���  [^_]A\A]A^A_��    L� L�HH�L$`H�@L��$�   L��$�   L�D$`L�L$hH��$�   H�D$p����H��A  H��I���  �|  ���>  H��$�   �D$O E1�H�D$XL�|$PH�T$PD������A�ń��  ��$�   ���B  H��$  H�
cA  H��  ����  ���D  ��$�  ��$�   A���.  ��$�  ��$�  ���  D�l$O��$�  �ƅ���   H��   �^  ��I��H�~  H��I���  A��H�|$XE��L��@  �D$0D��A���   ��H���D$(D�������D$ ��  H��L���5  H��H��I���W  L����  A��D;5<~  �����H�-~  1��;���D�l$O�   H��$�   ��  �@   D�l$OH��I���H��   �L��� ���!  ��$�  ��$�   A���  ��$�  ��$�  ����   ��$�  ��t��T$O=   ����и    Dи   HD��T$O������A��A��A����A��A��H��$  E��H�P�   � ��H��9�t5H�
D�E9�u�D�AE9�u�D�AE9�u��yD��A9�u�9�����D�l$O�����������H��1��D$gE#�|$g��H���D  ��f���f�     ��f���f�     ����ff.�     ����ff.�     H��H��f�     H��H�Ð��������SH�� H�ˋ	�����K������CH��H�� [�ff.�     SH�� H�ˋ	�����H��H�� [�fD  SH�� H�ˋ	�a�����  ��T�����  ��  �C�����  H��H�� [�@ SH�� H�ˋ	�!����K������K�C�����K�C�����K�C������CH��H�� [Ð����������%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ډ  ���%ʉ  ���%��  ���%��  ���%��  ���%��  ���%J�  ���%:�  ���%*�  ���%�  ���%
�  ���%��  ���%�  ���%ڋ  ���%ʋ  ���%��  ���%��  ���%��  ���%��  ���     H��(H�U%  H� H��t"D  ��H�?%  H�PH�@H�0%  H��u�H��(�fD  VSH��(H�S@  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^����� 1�fD  D�@��J�<� L��u��fD  �:z  ��t�D  �&z     �q����1�Ð������������H��(��t��t�   H��(�f�     �{
  �   H��(ÐVSH��(H�c?  �8t�    ��t��tN�   H��([^�f�H�a�  H�5Z�  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     ��	  �   H��([^�ff.�     @ 1�Ð������������VSH��xt$@|$PDD$`�9��   �H��<  Hc�H����    H��;  �DA �y�qH�q�   �S  �DD$0I��H�j<  �|$(H��I���t$ �  �t$@|$P1�DD$`H��x[^ÐH�9;  ��    H��;  ��    H�Y;  �s���@ H��;  �c���@ H��;  �S���H��;  �G�������������Ð������������VSH��8H��H�D$X�   H�T$XL�D$`L�L$hH�D$(�t  A�   �   H�
�;  I���  H�t$(�   �K  H��H��I���
  ��  ��    WVSH��PHc5x  H�˅��  H�x  E1�H��f�     L� L9�rH�P�RI�L9���   A��H��(A9�u�H���
  H��H����   H��w  H��H��H�H�x �     �#  �WA�0   H�H��w  H�T$ H�L��  H���}   �D$D�P����t�P���u�Ow  H��P[^_� ��H�L$ H�T$8A�@   �   DD�H%w  H�KI��H�S�|�  ��u��B�  H�
�:  ���d���@ 1��!���H��v  �WH�
�:  L�D�>���H��H�
d:  �/����ff.�      UAWAVAUATWVSH��HH�l$@D�%�v  E��tH�e[^_A\A]A^A_]�fD  �nv     �9	  H�H��H��   H����  L�-�;  H��;  �>v      H)�H�D$0H�3v  L��H)�H��~��H���  ����i  �C���^  �S����  H��L9��V���L�5>;  A������efD  ����   ���P  �7���   f����  H��  ��H)�L΅�uH�� ���|eH����  \H���a���f�7H��L9���   ��S�{L���L�L��� �  v���@��  H�7��H)�L΁��   �B  H��x�H�t$ ��I��H�
�9  �����    ���h  �C��S�����H���������7���   @���&  H�� ���H)�L΅�uH���   �H���|�H��H������@�7L9��5���fD  ��t  ������H�5��  1�H�}�D  H��t  H�D� E��t
H�PH�HI����A��H��(D;%Vt  |����� �7���   ��ytI�    ����L	�H)�L΅�uL9������H��������H9������H��������7�|���f.�     H�������H�7�b���H)�L΅��7����D���D  H)�L΅�t��@ H)�L΅�����������D  L9�����L�5�8  �s�;H��L�>H���Z����>L9�r��������H�
�7  �����H�
y7  ���������H��XH�Us  f�H��t%��$�   �L$ H�L$ H�T$(T$0�D$@�АH��X�f�H�
s  �
  ����SH�� H��H�ˉ������ ��CCG ��   =�  �wG=�  �vas��?��	��   H��7  Hc�H��� 1ҹ   �  H���>  H���  H��r  H��tuH��H�� [H��f.�     =  ���   vc=  �t,=  �u�1ҹ   �)  H����   H��t��   �� ������f�     �B�7�����@ 1�H�� [��     =  ��d����� 1ҹ   ��  H���@����   �   �  �f�     1ҹ   �  H��t*H�������   ���i���f�     �   ���T����   �   �U  �@����   �   �A  �,����   �   �-  �����������ATUWVSH�� L�%�q  L���^  H�_q  H��t6H�-{  H�=L  @ ���H����H��t
��u	H�CH����H�[H��u�L��H�� [^_]A\H�%   WVSH�� �q  ��H�օ�u
1�H�� [^_ú   �   ��
  H��H��t3H�pH�5�p  �8H����~  H��p  H��H��p  H�C��~  묃��멐VSH��(��p  �˅�u1�H��([^�D  H�5�p  H���h~  H�
ip  H��t'1��H��H��tH���9�H�Au�H��tH�B�-
  H���D~  1�H��([^� H�!p  ��ff.�     @ SH�� ����   w0��tL��o  ����   ��o     �   H�� [�f�     ��u��o  ��t��<�����f.�     ��o  ��uf��o  ��u�H��o  H��t�    H��H�[�l	  H��u�H�
�o  H�eo      �co      �E}  �l����k����   H�� [������f�     H�
Io  �+}  �0�����������������1�f�9MZuHcQ<Hс9PE  t��    1�f�y���@ HcA<H��AD�AH�DfE��t2A�H�H��L�L�(�     D�@L��L9�rHH9�rH��(L9�u�1��WVSH�� H���9  H��w{H��3  1�f�:MZuYHcB<HЁ8PE  uJf�xuB�PH�\�Pf��tB�B�H��H�|�(�
@ H��(H9�t'A�   H��H����  ��u�H��H�� [^_��    1�H��H�� [^_� H�	3  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�A�@H)�I�D E�@fE��t4A�P�H��L�L�(f.�     D�@L��L9�rPH9�r�H��(L9�u�1��H��2  1�f�8MZuHcP<HЁ8PE  t	���fD  f�xu��H���f�     L�I2  1�fA�8MZuIcP<L:PE  t��    f�zu��BD�BH�DfE��t,A�P�H��H�T�(�    �@' t	H��t�H��H��(H9�u�1��ff.�     f�H��1  1�f�8MZuHcH<H��9PE  t	H���D  f�yHD�H���f.�     H��1  1�f�:MZuLcB<I�A�8PE  t��    fA�xu�H)�E�HA�PI�TfE��t�A�A�H��L�L�(f.�     D�BL��L9�rBH9�rH��(L9�u�1�ËB$������    L��0  E1�fA�;MZuMcC<M�A�8PE  tL���f.�     fA�xu�A���   ��t�A�PE�PI�TfE��t�E�B�O��N�T�(f.�     D�JM��L9�r	DBL9�rH��(I9�u�E1�L��� L��
 ��H��D�@E��u�P��tׅ��D�HM�L��Ð���������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������H��8E1�L�D$ I��H��1��  H��8Ð�H��HH�D$`L�D$`I������H�D$(H�D$     L�L$hI��H�ʹ   H�D$8�k  H��HÐ�������������H��HH�D$hL�L$hM��I��H�D$(H�ʹ   H�D$     H�D$8�$  H��HÐ������VSH��HH��H�t$h�   H�T$hL�D$pL�L$xH�t$8��  H�t$ E1�I��H��1���  H��H[^Ð�������H��HH�D$`L�D$`I��H��H�D$ 1�L�L$hE1�H�D$8�  H��HÐ�������������1��ff.�     f�ATUWVSH�� L�d$pD��H��L��H����  ���   ����  �k  � ��j  H� H��7  H� H�M��t	A�$�  1�H�� [^_]A\�fD  ATUWVSH�� L�d$pD��H��L��H���`  ���   ����(  ��  � ��  H� H���  H� H�M��t	A�$�  1�H�� [^_]A\�fD  SH�� H����  ���    HD�H�� [�f�H�9.  �8 t1�Ð�  ff.�     SH�� �˹   �  A��H�-  H���m�����   �  �f�H��HH�D$`L�D$`I��H��H�D$ �   L�L$hE1�H�D$8��   H��H�ff.�     H��(H�U-  ��~   H��  �j   H��  �V   H�o  H��(�f.�     H��(H�-  ��>   H�W  �*   H�C  �   H�/  H��(Ð����������%2w  ���%2w  ���%2w  ���     �%�v  ���%�v  ���%�v  ���     �%zv  ���%zv  ���%zv  ���%zv  ���%zv  ���%zv  ���%zv  ���%zv  ���%zv  ���%zv  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���%�u  ���     �%�t  ���%�t  ���%�t  ���%�t  ���%�t  ���     �%zt  ���%zt  ���%zt  ���%zt  ���%Bt  ���%Bt  ���%"t  ���%t  ���%t  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ���%�s  ��UAWAVAUATWVSH��xH�l$p�   L�5]$  A��H������1��`     A����  �M��9f�     A�duBA� u;�   �������_  ����_  D9���   Hc�L�<�H�4�    E�/A��-t�L��L��������t�A��-tRH��#  L��������t^H��#  L����������   H��#  L���i�������   �[_  �t���fD  A�n��   A��-u�A�uu�A� u�H�D7���#_  H����   �8 ��   ��H��^  �_  D9��(����M�����   A�   H��^  H�
_  ���������   H��^  �   H���  �Qs  I��H��"  H�������   �}���D  H�����1�H�e[^_A\A]A^A_]�D  A� � ����E�   �X^  �q���A�   �a����S����   �����H���/
  H��"  H��H�������H��H���
  H�+  ����H����	  H��]  �Y���H�
^  �M���H�E��D����   H�������   H�E��q����   I���d����   I���W����   H���J����	   I���=����
   I���0����   H���#����   H�E�����L�E�M����	  L��H��!  �����H��H�j  H�E�H��tH�������I��H��t
H�U�H�M�����M����	  L��H��!  L�=�!  ����L�}�H��H��t H�&  ����I��H��tH�M�L���n���M����	  L��H�_!  L�5�!  �W���L�u�H��H��t H�  �?���I��H��tH�M�L���#���M���1	  L��H�!  L�-�  ����H��H��t L�������I��H���|	  H�M�L�������M����  L��H��  L�5c!  �����L�u�H��H��t H�*!  ����I��H��tH�M�L������H���c  H��H��  H�=W!  �z���H�}�H��H��t H�!  �b���I��H��tH�M�H���F���H���8  H��H�f  H�5>!  �/���H��H����   H�U�����H����   H�J[  H��H�������H�8[  H�
�   I���q���H�=![  vH�[     H�
[  H��   H����T���E1�H�5�   H)�L�t$0�2C�D'G�'K�$H��G�L'L�D$(C�D'I���D$ �n���L;%�Z  r�L������H��H�������I��H�M�H�5V   H���<���H��H�M�H����   H�)  �(���H��H����   H�  ����I��H����  H�}�H�   H��������   �����H�  H�������H��H��tEH��  �����H��H��t1H��Y  ����H��H��Y  � �U���H��  H��I������H�
�Y  H��  �x���H�
�Y  H��  H��Y  �^���H�
�Y  H��  H�aY  �D���H�
�Y  H��  H�?Y  �*���H�
kY  H�p  H�Y  ����H�
QY  H�[  H��X  �����H�
7Y  H�F  H��X  �����H�
Y  H�1  H��X  �����H��X  H���  H�
�X  H��t5H�X  ����H��H�pX  � �?���H�U�H��I��H��X  �i���H�
�X  H��t5H�6X  �I���H��H�'X  � �����H�U�H��I��H�MX  �(���H�
9X  H��t5H��W  ����H��H��W  � ����H�U�H��I��H�X  �����H�
�W  H��t4H��W  �����H��H��W  � �|���L��H��I��H��W  ����H�
�W  H��t5H�\W  ����H��H�MW  � �<���H�U�H��I��H�cW  �f���H�
_W  H��t5H�W  �F���H��H�W  � �����H�U�H��I��H�*W  �%���H�
W  H��t8H��V  ����H��H��V  � ����H�  H��I��H��V  �����H�
�V  H����   H�zV  H�e�����H�=iV  I��vH�YV     H�RV  H��   H����1���E1�H�=�  H)�L�d$0�:C�D7G�L7K�6H��G�D7L�D$(C�D7I���D$ �J���H��U  I9�r�E1�L��M�LԀ�����H��H��I��H�.V  ����H�e�H�E�H��tH�
�V  I��H�s  �����H��tH�
jV  I��H�P  ���������H�U�H�
KV  H������������H�)  H��I������H�U�H�
V  �Y������i���H�  H��I������H�U�H�
�U  �/������?���H��  H��I���U���H�
�U  L������������H��  H��I���,���H�U�H�
�U  ������������H��  H��I������H�U�H�
wU  �����������H��  H��I�������H�
QU  H������������H��  H��I�������=HT   ��  H�
U  I��H�r  ����H�
U  �����H�
U  H��t����H�
 U  ����E1�L��T  H�
�T  H��t����1�H�
�T  H�
�T  H��t����1�H��T  H�
�T  H��t�|���1�H��T  ���������   �	���H��H��tPH�������H��H��t@H�r  �����H��H��t,L�>T  I�P�����H�.T  H�
T  �:���1�1��Q���H�=�S   �����H�
�S  �����H�=�S   ������   �nh  A�.   �   H�
t  I�����������Ih  A�#   �   H�
  I��������   �m���H��  H�E������H��  H�E��t���H�5  �����H�}  H�E��0���L�-�  ����H�3  H�E�����H�
  H�E��O���H�
6S  H��  �j������z���H�r  H��I�����������H�
�R  H��  ����H�aR  ������   �r���H��  H���[���H��H���k���H��  �C���H��H���S���L���/���I���7����   H�=F  ����H��H������H��H���t���H�  �����H��H��t7H�R  �����H��H�R  � ����H�M�H��  I�������)����   ����H��H������H��H������H�=�  H������H��H�������H��Q  I���Y���H��I������H�D H��������H)�L��L�t$0L�������L�������H�M�H��I��������P     L������������������������������������������O @           ��������                                                                � @           �O @           ��������        ����                           ����            p; @           �; @           �; @            < @           p< @           Ц @   ئ @   > @   P> @   �< @   �= @   0= @   �< @   Q @   Q @   Q @      �p  $Q @    Q @   PDT PST �= @   �= @                                                                                                                                                                                                           Factory Current SysCfgDict SerialNumber SysCfg MLBSerialNumber BatterySerialNumber IOPMPowerSource MesaSerialNumber AppleBiometricSensor CoverglassSerialNumber product raw-panel-serial-number raw-panel-id lcd FrontCameraSerialNumberString AppleH0CamIn RearCameraSerialNumberString UNKNOWN_QUERY * device-tree AppleDiagnosticDataSysCfg  AppleDiagnosticDataAccessReadOnly mlb-serial-number Serial AppleARMPMUCharger battery-factory-id charger battery-id AppleMesa AppleSandDollar AppleH1CamIn AppleH2CamIn AppleH3CamIn AppleH4CamIn AppleH5CamIn AppleH6CamIn AppleH7CamIn AppleH8CamIn AppleH9CamIn AppleH10CamIn AppleH13CamIn AppleH16CamIn AppleH17CamIn AppleH18CamIn AppleH19CamIn AppleH20CamIn AppleH23CamIn IORegistry   ����������������@�����������Ӵ�����������j���Y��������������H���Usage: %s COMMAND [OPTIONS]
        Get ex-factory and read values from a device.
   The following OPTIONS are accepted:      -u, --udid UDID	target specific device by UDID          -h, --help		prints usage information    -n  connect to network device PhoneCheck 2.0.1 Developed By AaterAli  ideviceoem      ERROR: Could not connect to lockdownd, error code %d
   com.apple.mobile.diagnostics_relay      com.apple.iosdiagnostics.relay  Could not start diagnostics service!    Could not connect to diagnostics_relay!
 %02X --debug --udid -h --help  No device found with udid %s, is it plugged in?
        No device found, is it plugged in?
 MobileGestalt       Failed to retrieve System Configuration data.
 MainBoardSerialNumber BioSensorSerial BatteryData        FrontCameraModuleSerialNumString FrontCameraSerialNumber        BackCameraModuleSerialNumString BackCameraSerialNumber len : %d
 %02x%02x%02x%02x TouchIDSerialNumber LcdSerialNumber LcdCompleteSerial SrNm MLB# NvSn NSrN Batt BCMS FCMS LCM# MtSN SN_VALID MAINBOARD_VALID BIOSENSOR_VALID BATTERY_VALID FRONTCAM_VALID BACKCAM_VALID TOUCHID_VALID LCD_VALID Validation     %c%c%c%c magic version bigendian size maxsize endianess keycount header ext                     �+ @                            � @   � @   ܥ @   8� @                                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  ��������4�������������������Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      p���p���p���p���p�������p���0�����������        runtime error %d
               @P @           PP @           �O @              @           ps @           ps @           �g @           �P @            � @           � @           إ @           ԥ @           Х @            � @           0� @           �� @           �� @            � @           � @           � @           (� @           � @           0P @            � @            3 @           `, @           �� @           GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev6, Built by MSYS2 project) 13.1.0      GCC: (Rev7, Built by MSYS2 project) 13.1.0                                                                                                                                                            �    .  �  0  y  �  �  �  �  �  �  $�  �  
  D�    $  d�  0  <  l�  @  A  p�  P  �  t�     L  ��  P  �  ��  �  p  ��  p  �  ��     4  ��  @  �  ��  �  �  ܐ  �  �  ��  �     �      �   ��  �   &!   �  0!  7!  �  @!  "  �  "  �"  �  �"  �"  0�   #  t#  4�  �#  �#  @�  �#   $  H�   $  *%  L�  0%  5%  `�  @%  z(  d�  �(  �(  |�  �(  �(  ��  �(  �(  ��  �(  �(  ��  �(  �(  ��  �(  �(  ��  �(  �(  ��   )  %)  ��  0)  J)  ��  P)  �)  ��  �)  �)  ��  �*  �*  ��  �*  Z+  đ  `+  +  Б  �+  �+  ԑ  �+  �+  ؑ  �+  A,  ��  P,  S,  �  `,  X-  �  `-  c-  �  p-  �-  �  �-  B/  �  P/  �2  $�  �2  �2  <�  �2  �2  D�   3  �4  H�  �4  05  P�  05  �5  `�  �5  !6  l�  06  "7  x�  07  \7  ��  `7  �7  ��  �7  M8  ��  P8  �8  ��  �8  9  ��  9  �9  ��  �9  �9  ��  �9  Y:  ��  `:  &;  ��  p;  �;  ��  �;  �;  ��  �;  <  ��   <  h<  Ē  p<  �<  В  �<  �<  ؒ  �<  *=  ܒ  0=  �=  �  �=  �=  ��  �=  �=  �  �=  >  �  >  E>  �  P>  �>  �  �>  �>   �  �@  �O  Đ  �O  �O  (�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              B   b  
 
20`pP�	 B  �?     �  �   3  �  	 B  �?     �     3     B         R0`p b   20`p �0`pP   20 20 B0`pP  
u�0`
p	����P    r0`p B   20`p       2
0	`pP���
 
F 0`pP�      B0`   b      �
0	`pP���   
 Y 0`
p	P���� "                     20 20 20 20 B   B0`         B   B0`     	 � x h �0`      b0`   �0`p
E�0`
p	����P �      20
 
20`pP� 20`p B0`   20       20`p                   b   �   �   �0`   �     
 
20`pP�
 
20`pP� 20    20 �   B   B                                                                                                                                                                                                                         �          �  ȳ  X�          ,�  0�  ��          D�  ��  ȱ          |�  ��  �          ��  ȴ   �          н  ش  (�          8�   �  ��          ��  ��  �          ��  �  0�          �  �  X�          8�  0�                      ��      ��      �      ��      (�      P�      `�      |�      ��      ��      ط      ��              �      0�      H�      X�      t�      ��      ��      ��      ��      Ҹ              �      �              �      �      �      $�              .�              B�      Z�      d�      n�              x�      ��      ��      ��      ��      ƹ      ޹      ��      �      
�      ,�      L�      X�      h�      ��      ��      ��              ��      ��      ƺ      Ժ      �      
�      $�      .�      8�      B�              J�      T�      ^�              h�      v�      ��      ��              ��      ��      л      �       �      �      0�      D�      X�      l�      ��      ��      ��              ��      ��      �      ��      (�      P�      `�      |�      ��      ��      ط      ��              �      0�      H�      X�      t�      ��      ��      ��      ��      Ҹ              �      �              �      �      �      $�              .�              B�      Z�      d�      n�              x�      ��      ��      ��      ��      ƹ      ޹      ��      �      
�      ,�      L�      X�      h�      ��      ��      ��              ��      ��      ƺ      Ժ      �      
�      $�      .�      8�      B�              J�      T�      ^�              h�      v�      ��      ��              ��      ��      л      �       �      �      0�      D�      X�      l�      ��      ��      ��              ; diagnostics_relay_client_free < diagnostics_relay_client_new  > diagnostics_relay_goodbye ? diagnostics_relay_query_ioregistry_entry  A diagnostics_relay_query_mobilegestalt f idevice_free  l idevice_new_with_options  m idevice_set_debug_level   � lockdownd_client_free � lockdownd_client_new_with_handshake   � lockdownd_service_descriptor_free � lockdownd_start_service   DeleteCriticalSection =EnterCriticalSection  tGetLastError  zInitializeCriticalSection �LeaveCriticalSection  oSetUnhandledExceptionFilter Sleep �TlsGetValue �VirtualProtect  �VirtualQuery   __p__environ   __p__wenviron  _set_new_mode  calloc   free   malloc  
 __setusermatherr   __C_specific_handler  xmemcpy  }strrchr ~strstr   __p___argc   __p___argv   __p___wargv  _cexit   _configure_narrow_argv   _configure_wide_argv   _crt_at_quick_exit   _crt_atexit % _exit 6 _initialize_narrow_environment  8 _initialize_wide_environment  9 _initterm E _set_app_type K _set_invalid_parameter_handler  X abort Y exit  g signal   __acrt_iob_func  __p__commode   __p__fmode   __stdio_common_vfprintf  __stdio_common_vfwprintf   __stdio_common_vsprintf � fflush  � fwrite  � putchar � puts  � strcmp  � strlen  � strncmp 	 __daylight   __timezone   __tzname  < _tzset     plist_array_append_item    plist_dict_get_item    plist_dict_set_item   $ plist_get_data_ptr    % plist_get_data_val    , plist_get_string_val  4 plist_new_array   5 plist_new_bool    6 plist_new_data    8 plist_new_dict    ; plist_new_string  = plist_new_uint    L plist_to_xml   �   �   �   �   �   �   �   �   �   �   �   �  libimobiledevice-1.0.dll    �  �  �  �  �  �  �  �  �  �  KERNEL32.dll    (�  (�  api-ms-win-crt-environment-l1-1-0.dll   <�  <�  <�  <�  api-ms-win-crt-heap-l1-1-0.dll  P�  api-ms-win-crt-math-l1-1-0.dll  d�  d�  d�  d�  api-ms-win-crt-private-l1-1-0.dll   x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  x�  api-ms-win-crt-runtime-l1-1-0.dll   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  api-ms-win-crt-stdio-l1-1-0.dll ��  ��  ��  api-ms-win-crt-string-l1-1-0.dll    ��  ��  ��  ��  api-ms-win-crt-time-l1-1-0.dll  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  Ȱ  libplist-2.0.dll                                                                                                                                                                                                0 @                    @                   �+ @   �+ @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               �                  0  �                   H   X�  �          <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">
    <application>
      <!--The ID below indicates application support for Windows Vista -->
      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/>
      <!--The ID below indicates application support for Windows 7 -->
      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>
      <!--The ID below indicates application support for Windows 8 -->
      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>
      <!--The ID below indicates application support for Windows 8.1 -->
      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/> 
      <!--The ID below indicates application support for Windows 10 -->
      <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/> 
    </application>
  </compatibility>
</assembly>
                                                                                                                                                                                                                                                                                          @     ��   P  4    ��`�p�������������ȠРؠ����� ���(�0� `  H   � ����0�@�P�`�p�����������Ы�� �� �0�@�P�`�p�����������Ь �     � �8�@�                                                                                                                                                                                                                                                                                                                                                                        ,               @   $                      <    �&       P @   O
      �@ @   �                      ,    �e       � @   �	                      ,    �~       �( @   w                       ,    ��        ) @   �                       ,    �       �* @   �                           L�                           N�                       ,    ۓ       �+ @                              \�                       ,    �       �+ @   �                           ��                           7�                       ,    ��       `, @   �                       ,    �       `- @                              �                       ,    �       p- @   =                      ,    \�       �2 @   L                           Q�                       ,    پ        3 @   �                      ,    ��       �4 @   b                          ��                           �                       ,    ��       07 @   �                          ��                       ,    V�       p; @                          ,    ��       �; @   B                       ,    z�       �; @   9                       ,    �        < @   H                       ,    ��       p< @   2                           t                      ,          �< @                                                                                                                                                                                         �&       9GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99          @   $          char #w   
size_t #,�   long long unsigned int long long int 
uintptr_t K,�   
wchar_t b�   #�   short unsigned int int long int w   �   unsigned int long unsigned int unsigned char S  _EXCEPTION_RECORD �[�  ExceptionCode \
   ExceptionFlags ]
  �  ^!N  ExceptionAddress _
�  NumberParameters `
  ExceptionInformation a1
    :-�  	  ._CONTEXT �%�  P1Home 
y   P2Home 
y  P3Home 
y  P4Home 
y  P5Home 
y   P6Home 
y  (ContextFlags   0MxCsr   4SegCs 
  8SegDs 
  :SegEs 
  <SegFs 
  >SegGs 
  @SegSs 
  BEFlags   DDr0  
y  HDr1 !
y  PDr2 "
y  XDr3 #
y  `Dr6 $
y  hDr7 %
y  pRax &
y  xRcx '
y  �Rdx (
y  �Rbx )
y  �Rsp *
y  �Rbp +
y  �Rsi ,
y  �Rdi -
y  �R8 .
y  �R9 /
y  �R10 0
y  �R11 1
y  �R12 2
y  �R13 3
y  �R14 4
y  �R15 5
y  �Rip 6
y  �;�	   VectorRegister O
   VectorControl P
y  �DebugControl Q
y  �LastBranchToRip R
y  �LastBranchFromRip S
y  �LastExceptionToRip T
y  �LastExceptionFromRip U
y  � 
BYTE �=  
WORD ��   
DWORD �(  float -  <__globallocalestatus T�   signed char short int 
ULONG_PTR 1.�   
DWORD64 �.�   PVOID �  LONG )  LONGLONG �%�   ULONGLONG �.�   EXCEPTION_ROUTINE �)�  $�     N  �    �   PEXCEPTION_ROUTINE �    �  =_M128A �(S  Low ��   High ��   /M128A �%  !S  q  �    !S  �  �    �  �  �   _ 
_onexit_t 2�  �  >�   double long double �  ?
_invalid_parameter_handler ��  �  0          �    �       _Float16 __bf16 ._XMM_SAVE_AREA32  ��  ControlWord �
   StatusWord �
  TagWord �
�  Reserved1 �
�  ErrorOpcode  
  ErrorOffset   ErrorSelector 
  Reserved2 
  DataOffset   DataSelector 
  Reserved3 
  MxCsr   MxCsr_Mask   
FloatRegisters 	a   
XmmRegisters 
q  �Reserved4 
�  � /XMM_SAVE_AREA32 8  @�:�	  
Header ;�	   
Legacy <a   
Xmm0 =S  �
Xmm1 >S  �
Xmm2 ?S  �
Xmm3 @S  �
Xmm4 AS  �
Xmm5 BS  �Xmm6 CS   Xmm7 DS  Xmm8 ES   Xmm9 FS  0Xmm10 GS  @Xmm11 HS  PXmm12 IS  `Xmm13 JS  pXmm14 KS  �Xmm15 LS  � !S  �	  �    A 7
  1FltSave 8�  1FloatSave 9�  B�   !S  
  �    PCONTEXT V  g  A
  �    EXCEPTION_RECORD bS  PEXCEPTION_RECORD dv
  A
  _EXCEPTION_POINTERS y�
  �  z[
   ContextRecord {
   EXCEPTION_POINTERS |{
  {
  %E   Next F05  prev G05   _EXCEPTION_REGISTRATION_RECORD D5  &�
   &:      %Ib  Handler J  handler K   %\�  FiberData ]�  Version ^   _NT_TIB 8W#$  ExceptionList X.5   StackBase Y
�  StackLimit Z
�  SubSystemTib [
�  &b   ArbitraryUserPointer `
�  (Self a$  0 �  NT_TIB b�  PNT_TIB cJ  )  2JOB_OBJECT_NET_RATE_CONTROL_FLAGS   �!
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _IMAGE_DOS_HEADER @�v  e_magic �   e_cblp �  e_cp �  e_crlc �  e_cparhdr �  e_minalloc �  
e_maxalloc �  e_ss �  e_sp �  e_csum �  e_ip �  e_cs �  e_lfarlc    e_ovno   e_res v  e_oemid   $e_oeminfo   &e_res2 �  (e_lfanew �  <   �  �      �  �   	 IMAGE_DOS_HEADER !
  PIMAGE_DOS_HEADER �  !
  _IMAGE_FILE_HEADER b�  Machine c   NumberOfSections d  TimeDateStamp e
  PointerToSymbolTable f
  NumberOfSymbols g
  SizeOfOptionalHeader h  Characteristics i   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  VirtualAddress �
   Size �
   IMAGE_DATA_DIRECTORY ��  _IMAGE_OPTIONAL_HEADER ��  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  BaseOfData �
  q   �
  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   �
  H_   �
  L�   �
  P�   �
  T�  �
  X�  �
  \Q   ��  ` �  �  �    PIMAGE_OPTIONAL_HEADER32 �     _IMAGE_OPTIONAL_HEADER64 ���  Magic �   �   ��  �   ��  {   �
  �   �
    �
  (  �
     �
  q   ��  *   �
   |  �
  $`  �  (�  �  *
  �  ,�  �  .<  �  0;   �  2�  �
  4   �
  8R  �
  <
   �
  @    �  D�  �  F�   ��  H_   ��  P�   ��  X�   ��  `�  �
  h�  �
  lQ   ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � �    C_IMAGE_NT_HEADERS64 b  Signature 
   FileHeader �  OptionalHeader �   PIMAGE_NT_HEADERS64     PIMAGE_NT_HEADERS "!b  PIMAGE_TLS_CALLBACK S �  #�  �  0�  �    �   �  $�  �  �
   
PTOP_LEVEL_EXCEPTION_FILTER �  
LPTOP_LEVEL_EXCEPTION_FILTER %�  DtagCOINITBASE   	�p  COINITBASE_MULTITHREADED   2VARENUM   
	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � _dowildcard `�   _newmode a�   __imp___initenv i  EyR  newmode z	�     
_startupinfo {7  F�     ��  __uninitialized  __initializing __initialized  G�   �g  -�  __native_startup_state �+�  __native_startup_lock ��     H
_PVFV 
�  
_PIFV 
�    I_exception (�
  type �	�    name �  arg1 ��  arg2 ��  retval ��       _TCHAR �w   __ImageBase &�  _fmode -�   _commode .�     �  3 __xi_a 5$�  __xi_z 6$�    �  3 __xc_a 7$�  __xc_z 8$�  __dyn_tls_init_callback <"�  __mingw_app_type >�   argc @�   	(� @   argv B  	 � @   �  �  envp C  	� @   Jargret E�   mainret F�   	� @   managedapp G�   	� @   has_cctor H�   	� @   startinfo IR  	� @   __mingw_oldexcpt_handler J%  4__mingw_pcinit R  	 � @   4__mingw_pcppinit S  	� @   _MINGW_INSTALL_DEBUG_MATHERR U�   '__mingw_initltsdrot_force �   '__mingw_initltsdyn_force �   '__mingw_initltssuo_force �   K__mingw_module_is_dll Tw   	 � @   (_onexit ��  D  �   memcpy 2�  g  �  (  �    strlen @�   �     (malloc �  �  �    "_cexit C Lexit � �  �    main t�   �  �        "__main A
"_fpreset (
_set_invalid_parameter_handler �.�  #  �   _gnu_exception_handler M  L  L   �
  SetUnhandledExceptionFilter 4       "_pei386_runtime_relocator L
_initterm 1�       _amsg_exit m�  �    Sleep �     __getmainargs ~�           �      R  (_matherr �   <  <   "  __mingw_setusermatherr �f  f   k  $�   z  <   )_setargv o�   )__p__commode *  )__p__fmode �  __set_app_type ��  �    Matexit ��    @          �"  Nfunc O         @   )  	R�R  Oduplicate_ppstrings >
�  ac >&�   av >4�  avl @  i A�   n B  Pl G
�       Qcheck_managed_app �       pDOSHeader �  pPEHeader �  pNTHeader32 �  pNTHeader64 �   5__tmainCRTStartup ��   � @   P      ��"  lock_free ��  *   "   fiberid ��  L   H   nested �	�   e   [   *p%  � @      ��   RC&  � @   %   'I]&  �   �   +0   m&  �   �      *�%  � @   ;   ��   /&  �   �   &  �   �   6&   S"  V @    F   �!  L  �   �   @  �   �   +F   X  �   �   e  	    p  6  0  T{  Q   �!  |  N  L  � @   g  � @   �  �!  	Rt  � @   {&  	Xt   h @   �  	Ru    U�%  m @   m @          �
�!  �%  X  V  6�%   � @   �  "  	R
� V# @   4"  	R0	Q2	X0 ( @     5 @   Q  V"  R K @   �  u"  	R	  @    P @   �  � @   �  � @   �  A @   �  �"  	RO _ @   �  �"  RQ � @   �  � @   �  �"  RQ � @   �   7mainCRTStartup ��   � @          �J#  ret ��   e  a   @        7WinMainCRTStartup ��   � @          ��#  ret ��   z  v  � @        8pre_cpp_init �0 @   I       �$  t @   �  	R	(� @   	Q	 � @   	X	� @   	w 	� @     5pre_c_init j�    @         ��$  *�   @      lt$  +   W�  �  �  �  �  �  �  �  �  �    w @   �  �$  	R2 | @   �  � @   �  � @   z  � @   �  �$  	R1  @   A  R  8__mingw_invalidParameterHandler ]  @          �j%   expression ]2  R function ^  Q file _  X line `  Y pReserved a�   �  X_TEB YNtCurrentTeb '�%  j%  ,_InterlockedExchangePointer ��  �%  Target �3�%  Value �@�   �  ,_InterlockedCompareExchangePointer ��  C&  Destination �:�%  ExChange �M�  Comperand �]�   ,__readgsqword F�   {&  Offset F(  ret F�    Zmemcpy __builtin_memcpy   S?   �  5GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  �           9  char ({   size_t #,�   long long unsigned int long long int short unsigned int int (�   long int unsigned int _iobuf !
'  _Placeholder #'    6FILE /   unsigned char double float long double short int 
{   signed char uint8_t $6  uint16_t &�   uint32_t (�   uint64_t *0�   long unsigned int plist_t Y'  �   |x  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ plist_err_t ��  �   'S  IDEVICE_E_SUCCESS  IDEVICE_E_INVALID_ARG IDEVICE_E_UNKNOWN_ERROR ~IDEVICE_E_NO_DEVICE }IDEVICE_E_NOT_ENOUGH_DATA |IDEVICE_E_CONNREFUSED {IDEVICE_E_SSL_ERROR zIDEVICE_E_TIMEOUT y idevice_error_t 0�  p  2 w  p  idevice_t 3�  
k  idevice_options �   9  IDEVICE_LOOKUP_USBMUX IDEVICE_LOOKUP_NETWORK IDEVICE_LOOKUP_PREFER_NETWORK  
�   )  �   $�  LOCKDOWN_E_SUCCESS  LOCKDOWN_E_INVALID_ARG LOCKDOWN_E_INVALID_CONF ~LOCKDOWN_E_PLIST_ERROR }LOCKDOWN_E_PAIRING_FAILED |LOCKDOWN_E_SSL_ERROR {LOCKDOWN_E_DICT_ERROR zLOCKDOWN_E_RECEIVE_TIMEOUT yLOCKDOWN_E_MUX_ERROR xLOCKDOWN_E_NO_RUNNING_SESSION wLOCKDOWN_E_INVALID_RESPONSE vLOCKDOWN_E_MISSING_KEY uLOCKDOWN_E_MISSING_VALUE tLOCKDOWN_E_GET_PROHIBITED sLOCKDOWN_E_SET_PROHIBITED rLOCKDOWN_E_REMOVE_PROHIBITED qLOCKDOWN_E_IMMUTABLE_VALUE pLOCKDOWN_E_PASSWORD_PROTECTED oLOCKDOWN_E_USER_DENIED_PAIRING nLOCKDOWN_E_PAIRING_DIALOG_RESPONSE_PENDING mLOCKDOWN_E_MISSING_HOST_ID lLOCKDOWN_E_INVALID_HOST_ID kLOCKDOWN_E_SESSION_ACTIVE jLOCKDOWN_E_SESSION_INACTIVE iLOCKDOWN_E_MISSING_SESSION_ID hLOCKDOWN_E_INVALID_SESSION_ID gLOCKDOWN_E_MISSING_SERVICE fLOCKDOWN_E_INVALID_SERVICE eLOCKDOWN_E_SERVICE_LIMIT dLOCKDOWN_E_MISSING_PAIR_RECORD cLOCKDOWN_E_SAVE_PAIR_RECORD_FAILED bLOCKDOWN_E_INVALID_PAIR_RECORD aLOCKDOWN_E_INVALID_ACTIVATION_RECORD `LOCKDOWN_E_MISSING_ACTIVATION_RECORD _LOCKDOWN_E_SERVICE_PROHIBITED ^LOCKDOWN_E_ESCROW_LOCKED ]LOCKDOWN_E_PAIRING_PROHIBITED_OVER_THIS_CONNECTION \LOCKDOWN_E_FMIP_PROTECTED [LOCKDOWN_E_MC_PROTECTED ZLOCKDOWN_E_MC_CHALLENGE_REQUIRED YLOCKDOWN_E_UNKNOWN_ERROR �~ lockdownd_error_t P    R)	    lockdownd_client_t S#5	  
		  lockdownd_service_descriptor `�	  port a�   ssl_enabled b
�  identifier cv   lockdownd_service_descriptor_t e.�	  
:	  �   %�
  DIAGNOSTICS_RELAY_E_SUCCESS  DIAGNOSTICS_RELAY_E_INVALID_ARG DIAGNOSTICS_RELAY_E_PLIST_ERROR ~DIAGNOSTICS_RELAY_E_MUX_ERROR }DIAGNOSTICS_RELAY_E_UNKNOWN_REQUEST |DIAGNOSTICS_RELAY_E_UNKNOWN_ERROR �~ diagnostics_relay_error_t ,�	  8  :1�
  8  diagnostics_relay_client_t ;+�
  
�
  device_oem �   +  OEM_UNDEFINED  OEM_SN_SALESMODEL OEM_SN_DEVICE OEM_SN_MLB OEM_SN_BATTERY OEM_SN_BATTERY_1 OEM_SN_SCREEN OEM_SN_SCREEN_1 OEM_SN_SCREEN_2 OEM_SN_BIOSENSOR 	OEM_SN_TOUCHID 
OEM_SN_FRONTCAM OEM_SN_REARCAM OEM_SN_WMAC 
OEM_SN_BMAC OEM_SYSCFG OEM_SYSCFGDICT  idevice_oem_t ,�
  syscfg_data .r  data /v   len 0�   syscfg_data_t 1A  lockdown_client n	  	0� @   diagnostics_client o#�
  	(� @   service p'�	  	 � @   device q|  	� @   7Dont_Compare_LCD r�   	0� @   verificationReport t�  	� @   syscfgData wr  	 � @   oem_domain �   y�
  kMobileGestaltDomain  kIORegistryDomain  oem_domain_t |a
  8f  (~  domain �
   plane �v  entry �v  name �v  alternative �    
�
  oem_query_t ��
  fflush s�   K  K   
)  )K  plist_to_xml �x    �    �   
v  
�  strstr 	`v  �       plist_get_string_val ��  �     memset 	5'  �  '  �   �    malloc 
'    �    diagnostics_relay_query_ioregistry_entry ��
  U  �
      U   
�  diagnostics_relay_query_mobilegestalt ��
  �  �
  �  U   plist_array_append_item $�  �  �   *plist_new_array ��  do_syscfg ��   �    �    syscfgInitWithData O'  '  v  �   _Bool plist_get_data_val Z  �    Z   
�  strrchr 	]v  ~    �    lockdownd_client_free ��  �  	   lockdownd_service_descriptor_free >�  �  �	   diagnostics_relay_client_free e�
    �
   diagnostics_relay_goodbye r�
  7  �
   plist_new_bool �
�  X  �   strlen 	@�   q     plist_new_string �
�  �     printf ��   �     plist_get_data_ptr (  �  �  Z   plist_dict_set_item �  �    �   plist_dict_get_item �
�  1  �     *plist_new_dict ��  +syscfgPlistCopy �c  U   idevice_new_with_options �S  �  �    �   
|  +idevice_set_debug_level l�  �    strcmp 	?�   �       sprintf ��     v     diagnostics_relay_client_new I�
  :  |  �	  :   
�
  lockdownd_start_service ��  s  	    s   
�	  9exit 
� �  �    fprintf ��   �  P     __acrt_iob_func ]K  �  �    idevice_free �S  �  |   lockdownd_client_new_with_handshake ��  .  |  .     
	  ,print_usage 

p   argc 
�    argv 
*  name v   :main �   �@ @   �      �-  argc �   �  �  argv   1  #  ;result �   	i �   	� @   	udid   	� @   <tag �   use_network 	�   t  r  	syscfg P�  	� @   plCurrentValues b
�  �  �  plFactoryValues c
�  �  �  plCurrentSN l
�  �  �  plCurrentMLB n
�  (    plCurrentBAT o
�  x  `  plCurrentRCAM p
�  �  �  plCurrentFCAM r
�  0  "  plCurrentMESA s
�  v  h  plCurrentTouchID t
�  �  �  plCurrentLCD v
�  8    plCurrentCompleteLCD w
�  �  �  	plFactorySN z�  	�� @   	plFactoryMLB {�  	�� @   	plFactoryBAT |�  	�� @   	plFactoryRCAM }�  	�� @   	plFactoryFCAM ~�  	�� @   	plFactoryMESA �  	�� @   	plFactoryTouchID ��  	�� @   	plFactoryLCD ��  	�� @   validationRecord �
�  �  �  �E @   �       �  	len ��  	ؠ @   data �v    �  plCurrentTouchIDData �-      -�   a  j ��   .  &  i ��   _  Y  
"F @   �  R||1$~ "Qt   �E @   �  �  Q	ؠ @    �E @   �  �  R	�f @    3F @   q  �  R~  >F @   �<  �  Rt  
TF @   �  R�hQt   �F @   1       x  	len (�  	Р @   !Y  v  }  y  �F @   �  I  Q	Р @    �F @   q  

G @   �  Ru Q	�f @     �N @   2         	len 0)�  	Ƞ @   !Y  1v  �  �  �N @   �  �  Q	Ƞ @    �N @   q  
�N @   �  R�hQ	�f @      O @   d       �  	len B1�  	�� @   !Y  C'v  �  �  hex_raw_panel D&5-  �  �  /O @   �  �  Q	�� @    :O @   X  �  R  [O @   e-  �  R Q~  cO @   q  �  R~  
rO @   �  R�hQu   �G @   5         	len j�  	x� @   plFactorySNData kv  �  �  �G @   �  V  Q	x� @    H @   q  
'H @   �  Rs Q�`  3H @   5         	len r�  	p� @   plFactoryMLBData sv  �  �  ?H @   �  �  Q	p� @    RH @   q  
hH @   �  Rs Q�X  tH @   5       �  	len z�  	h� @   plFactoryMESAData {v      �H @   �    Q	h� @    �H @   q  
�H @   �  Rs Q�P  �H @   4       ;  	len ��  	`� @   plFactoryBATData �v      �H @   �    Q	`� @    �H @   q  
�H @   �  Rs Q}   �H @   5       �  	len ��  	X� @   plFactoryFCAMData �v  5  1  I @   �  �  Q	X� @    I @   q  
*I @   �  Rs Q�H  6I @   5       e  	len ��  	P� @   plFactoryRCAMData �v  M  I  BI @   �  <  Q	P� @    UI @   q  
kI @   �  Rs Q�@  wI @   8       �  	len ��  	H� @   plFactoryLCDData �v  e  a  �I @   �  �  Q	H� @    �I @   q  
�I @   �  Rs Q	�f @     �I @   �          	len ��  	@� @   data �0-  }  y  plFactoryTouchIDData �I-  �  �  -�   �  j ��   �  �  i ��   �  �  
FJ @   �  R~|1$| "Qu   �I @   �  �  Q	@� @    bJ @   q  �  R|  
wJ @   �  Rs Qt   . .  �B @    }   Q�!  .  �  �  /}   &.  '  %  0<.  �   �!  =.  8  4  0J.  �   �!  K.  R  N  "Z.  �L @   b       t!  [.  j  h  "h.  �L @   @       H!  i.  y  w  "|.  �L @   ,       ,!  =}.  �L @   0  !  Q	 � @   X	� @    �L @   �  
�L @   �  R0Q0  
�L @     Q	(` @     �L @   �.  _!  R? 
�L @     Qs   
�B @     Q	` @     
�B @     Qs   
�B @   �.  R@   �@ @   �>  A @   �  �!  R1 DA @   �  �!  R Q~  ]A @   �  !"  R Q	&e @    pA @   �  F"  R Q	-e @    �A @   �  k"  R Q	0e @    B @   c  �"  R	� @    ?B @   �  �"  R2 QB @   �  �"  Q	8e @   Xs  [B @   x  �"  R1 hB @   �=  �"  H  | U  u  �B @   �8  �B @   1  C @   1  C @   1  !C @   �.  J#  R2 /C @   �.  a#  R3 <C @   �.  x#  R4 IC @   �.  �#  R< VC @   �.  �#  R; cC @   �.  �#  R9 pC @   �.  �#  R: }C @   �.  �#  R6 �C @   �.  $  R7 �C @     ($  R�`Q	�e @    �C @     A$  Q�` �C @   �  a$  R�hQ�` �C @     �$  Q	�e @    D @     �$  Q	/` @    "D @   �  �$  R�hQ  AD @     �$  Q	�e @    YD @     �$  Q	c` @    mD @   �  %  R�hQ~  �D @     :%  Q	�e @    �D @     R%  Q}  �D @   �  q%  R�hQ}  �D @     �%  R| Q	�b @    �D @     �%  Q	f @    �D @   �  �%  R�hQ~  E @     �%  Q	�b @    6E @     &  Q	Pf @    JE @   �  1&  R�hQu  iE @     P&  Q	�b @    ~E @     i&  Q�` pF @     �&  R��Q	�e @    �F @     �&  Q	�` @    �F @   �  �&  Ru Q	�f @    �F @   �.  �&  R7 �F @     
'  Q	�b @    �F @     )'  Q	�` @     G @     H'  Q	�f @    :G @     g'  Q	�f @    TG @     �'  Q	�f @    nG @     �'  Q	�f @    �G @     �'  Q	�f @    �G @     �'  Q	�f @    �G @     (  Q	�f @    �G @     !(  Q	�f @    �J @   �  G(  Q	` @   X�h �J @   �  l(  Q	 ` @   Xs  �J @   1  �J @   �:  �(  Q�` �J @   7  �J @   �  �(  Rs Q	g @    �J @   �:  �(  Q�X �J @   7  K @   �  )  Rs Q	g @    !K @   �:  ()  Q�P )K @   7  ;K @   �  Z)  Rs Q	g @    JK @   �:  r)  Q}  RK @   7  dK @   �  �)  Rs Q	.g @    tK @   �:  �)  Q�H |K @   7  �K @   �  �)  Rs Q	<g @    �K @   �:  *  Q�@ �K @   7  �K @   �  :*  Rs Q	Kg @    �K @   �:  R*  Qt  �K @   7  �K @   �  �*  Rs Q	Yg @    L @   �  �*  Q	qg @   Xs  L @   �<  !L @     -L @   �  HL @   �  bL @   �  |L @   ~  	M @   F  +  R	� @    "M @   �  -+  R2 <M @   
?  W+  R	�e @   Q1X. GM @   �  aM @   
?  �+  R	pe @   Q1X# kM @   x  �+  R1 �M @   �:  �+  Q	�f @    �M @   7   N @   �  �+  Rs Q	gg @    N @     ,  Q	 g @    .N @   �.  ,,  R5 =N @     K,  Q	�b @    UN @     j,  Q	�e @    iN @     �,  Q}  �N @   �.  �,  R7 �N @     �,  Qu  �N @     �,  Q	�` @    �N @   �.  �,  R8 �N @     �,  Qu  
O @     Qu   #0-  0-  $�   ؠ @   1 
�  #{   I-  $�   p 1$ #0-  e-  $�   @� @   1 >string2hexString �@ @   _       � .  input �v  �  �  output �*v  �  �  loop 	�   �  �  i 	�   �  �  
w @   �  Rs Qu   ,copySysCfg ��.   syscfgOut �(U  mgestresponse �
�  dict ��  syscfg ��  resp ��  syscfgdata �!�  ret �'        ?f  ��  � @   �      ��8  tag �(+  	  	  	q �  ��query ��8  $	  	  keys �
�  H	  B	  	node ��  	8� @   .X:  � @    h   �8  y:  i	  c	  /h   �:  �	  �	  �:  �  V  � @   /=  �/  R	Pa @   Qt X	5a @   �:  1 � @   /=  �/  Rt Q	'a @   X	` @   �:  1 � @   /=   0  Rt Q	)a @   Xv �:  1 � @   /=  90  Rt Q	'a @   X	/` @   �:  1  @   /=  r0  Rt Q	)a @   X	ra @   �:  1 3 @   /=  �0  R	�a @   Q0X	�a @   �:  1 G @   /=  �0  R0Qu X	�a @   �:  1 _ @   /=  1  R0Qu X	�a @   �:  1 	 @   /=  D1  R	�a @   Q0X	` @   �:  1 ( @   /=  u1  R	�a @   Q0Xv �:  1 O @   /=  �1  R0Q	�a @   X	�` @   �:  1 n @   /=  �1  R0Q	�a @   Xv �:  1 � @   /=  2  R0Q	�a @   Xv �:  1 � @   /=  @2  R0Q	b @   Xv �:  1 � @   /=  q2  R0Q	b @   Xv �:  1 � @   /=  �2  R0Q	b @   Xv �:  1 � @   /=  �2  R0Q	,b @   Xv �:  1  @   /=  3  R0Q	9b @   Xv �:  1 2 @   /=  53  R0Q	Fb @   Xv �:  1 N @   /=  f3  R0Q	Sb @   Xv �:  1 j @   /=  �3  R0Q	ab @   Xv �:  1 � @   /=  �3  R0Q	ob @   Xv �:  1 � @   /=  �3  R0Q	}b @   Xv �:  1 � @   /=  *4  R0Q	�b @   Xv �:  1 � @   /=  [4  R0Q	�b @   Xv �:  1 � @   /=  �4  R0Q	�b @   Xv �:  1  @   /=  �4  R0Q	�b @   Xv �:  1 > @   /=  �4  R0Q	�a @   X	�` @   �:  1 ] @   /=  &5  R0Q	�a @   Xv �:  1 y @   /=  W5  R0Q	�a @   Xv �:  1 � @   /=  �5  R0Q	b @   Xv �:  1 � @   /=  �5  R0Q	b @   Xv �:  1 � @   /=  �5  R0Q	b @   Xv �:  1 � @   /=  6  R0Q	,b @   Xv �:  1  @   /=  L6  R0Q	9b @   Xv �:  1 ! @   /=  }6  R0Q	Fb @   Xv �:  1 = @   /=  �6  R0Q	Sb @   Xv �:  1 Y @   /=  �6  R0Q	ab @   Xv �:  1 u @   /=  7  R0Q	ob @   Xv �:  1 � @   /=  A7  R0Q	}b @   Xv �:  1 � @   /=  r7  R0Q	�b @   Xv �:  1 � @   /=  �7  R0Q	�b @   Xv �:  1 � @   /=  �7  R0Q	�b @   Xv �:  1 
 @   /=  R0Q	�b @   Xv �:  1    @   �  $ @   q  / @   �  58  Ru  b @   Z  S8  Qu Xv  G @     k8  Yv  
b @     Q	�b @     
  @StartService b  @   4      �X:  	err e�   	�� @    @   �  �8  Q	0� @   X	8d @    G @   ?  "9  Q	�d @   X	 � @    v @     A9  X	(� @    � @   ?  m9  Q	�d @   X	 � @    � @   &?  �9  R	�d @    � @   x  �9  R1 � @   �  � @   �  �9  R2 � @   �  �9  Q	Hd @   Xs   @   x  :  R1  @   �  :  R2 ) @   
?  D:  R	�d @   Q1X( 
4 @   x  R1  1oem_query_for_string �  �:  tag �7+  %query �  %next ��8   1oem_query_create ��8  �:  domain �:�
  plane �Hv  entry �Uv  name �bv  %out ��8   AverifyRecord B
'  P @   �       ��<  &report B"�  2  ,  &name B6  U  O  2fLen D�  2cLen E�  3factory G�  z  t  3current N�  �  �  factoryValue [v  �PcurrentValue ]v  �Xi @     �;  Rs Q	 ` @    y @     �;  Qu  � @     "<  Rs Q	` @    � @     :<  Qu  � @   �  X<  Rt Q�P � @   �  v<  Rs Q�X � @   �   Bprint_xml 3  @   L       �/=  &node 3&�  �  �  xml 5v  �hlen 6�  �d$ @   U  �<  R�RQ�hX�d 3 @   &?  > @   �  !=  R1 G @   1   4�:  P @   G       ��=  �:  �  �  �:  �  �  �:    
  �:  /  -  C�:  
j @   �  R(  43  p @   �       ��>  U  >  <  H  S  Q  � @   _  �=  Rs Q/ � @   �  >  R	c @   Qs  � @   &?  0>  R	8c @    � @   &?  O>  R	hc @    � @   &?  n>  R	�c @    � @   &?  �>  R	�c @    � @   &?  �>  R	�c @    � @   ;?  �>  R: � @   &?  �>  R	d @    D� @   &?  R	!d @     E__main __main 'fwrite __builtin_fwrite 'puts __builtin_puts 'putchar __builtin_putchar  �   �	  4GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  E  � @   �	      n  char     size_t #,�   long long unsigned int long long int short unsigned int int  �   long int unsigned int _iobuf !
+  	_Placeholder #+    5FILE /  unsigned char double float long double signed char uint8_t $:  short int uint32_t (�   uint64_t *0�   long unsigned int    plist_t Y+  6�   |m  PLIST_ERR_SUCCESS  PLIST_ERR_INVALID_ARG PLIST_ERR_FORMAT ~PLIST_ERR_PARSE }PLIST_ERR_NO_MEM |PLIST_ERR_UNKNOWN �~ plist_err_t ��  u_int32_t 
�  u_int8_t |  �   syscfgHeader (0  	shMagic *�   	shSize +�  	shMaxSize ,�  	shVersion -�  	shBigEndian .�  	shKeyCount /�   syscfgEntry 2e  	seTag 4�   	seData 5e   |  u  !�    syscfgEntryCNTB 8�  	seTag ;�   	seRealTag <�  "�  =�  "�  >�  	reserved ?�   7syscfgMemEntry B*  	seTag D�   	seData E*  #�  F�  #�  G�   �  ;  8�   � 9endianness �   d  BIG  LITTLE  endianness_t ;  :�   �  �   ;syscfg_debug_func )�  	 P @   y  syscfgKeyCount 3�  	�� @   syscfgData 4�  	�� @   |  syscfgDataLength 5�   	x� @   syscfgRootPlist 6�  	p� @   
memset 5+  Y  +  �   �    calloc 	+  x  �   �    
strcmp ?�   �  �  �   
plist_new_data �
�  �  �  �   $plist_dict_set_item �
�  �  �  �   
plist_new_uint �
�  
  �   
plist_new_bool �
�  +  |   
plist_new_string �
�  N  �   snprintf G�   u  �  �   �   fflush s�   �  �   -  
__acrt_iob_func ]�  �  �    plist_to_xml �m  �  �  �  �   �  �  %plist_new_dict �
�  
swap_syscfgEntry 

$  $  $   0  
swap_syscfgEntryCNTB 
P  P  P   u  
swap_syscfgMemEntry 
{  {  {   �  
memcpy 2+  �  +  �  �    �  <malloc 	+  �  �    
swap_uint32 
�  �  �   
swap_syscfgHeader 
	       �  %endian 	d  $free 	-  +   =do_syscfg �   @% @   :      ��  &args �  v  r  &nargs '�   �  �  'hdr �  ��{>�  �  ��{entryentry_temp �  index �   �  �  curArg 
�   �  �  size 	�   �  �  i �   j �   sfx �      isExt  �  I  G  lowExt !	�   T  R  highExt "	�   i  g  ?extSize #	�    buffer $�  �  |  showExt %�  �  �  plSyscfg '
�      plHeader 3
�  )  '  @  8  'tagName �  ��zplSyscfgNode ��  3  1  (&  & @   !  :w
  O  A  ;  B  a  [  )!  Z  f  w  
;& @   �  R~ Q��z   (�   ' @   1  ��
  �  z  x  �  �  �  )1  �  �  �  
+' @   �  R| Qt    m& @   x  �
  R	�g @    |& @   �    Rt  �& @   �  �& @   �  �& @   Y  F  R1Qt  �& @   u  d  R| Xt   ' @   N  �  Ru Q5X	�g @   Y H%w  @%�w( 	�$w0 � 9' @   �  �  Rv Qu  A' @     �  R|  x' @   �    R
  �' @   �    Rt  �' @   �  �' @   �   }% @   Y  �% @   �  ^  R��z 
& @   �  Rv Q	�g @     _Bool    �  !�    Aplist_syscfgNode 	�  �  *data  �  *length /�  syscfgNode 
�   plist_syscfgHeader �	�   $ @   *      �  hdr �0�  �  �  header �
�  �  �  magicStr �
�  ��magic  
�  �  �  endianess 
�  �  �  size 
�  �  �  maxSize 
�  �  �  version 
�      keyCount 
�  #    $ @   �  V$ @   N  >  Rv Q5X	�g @   Yu �w u 	�$w(u @%�w0u H% ^$ @   +  V  Rv  j$ @   
  s  Rt�� u$ @   �  �  R
t����� �$ @   �  �  R
t����� �$ @   �  �  Rt�
�� �$ @   �  �  R
t����� �$ @   �    Rs Q	�g @   X~  �$ @   �  G  Rs Q	�g @   X}  �$ @   �  r  Rs Q	�g @   Xu  �$ @   �  �  Rs Q	�g @   X|  �$ @   �  �  Rs Q	�g @   Xv  % @   �  �  Rs Q	�g @   Xu  
% @   �  Rs Q	�g @   Xt   +syscfgPlistCopy ��# @          �T  ,syscfg �T  R �  syscfgPlist �	�  �# @   i       �!  h  �# @   �# @   B       �  {  4  2  �  �h�  �d�# @   �  �  Q�hX�d �# @   �  �# @   �    R1 �# @   u   B�# @   �   syscfg_find_tag ��   # @   t       �!  tag ��  @  <  data_out �&!  Z  R  size_out �:�  �  z  -�  ��  	@� @   .�  !# @     ��  
  �  �   �  `# @   `# @          ��  �  �  �   
# @   �  R�RQ	@� @     +  syscfgFindByIndex ��  �  /index ��  �  �;{  C�  �0  entryCNTB �P  offset �	�    syscfgFindByTag ��  @! @   �       ��  tag ��  �  �  D�  �7{      temp ��  	`� @   0�  �
�  >  6  index ��  a  [  &  �! @   �! @          ��  O  z  x  B  �  �  Z  f  w  
�! @   �  Rv Q|   �! @   
  
�! @   U  R|   syscfgCopyDataForTag y�   " @   �       ��  tag y �  �  �  buffer y/�  �  �  size y>�   �  �  0�  {	�    
  -�  |�  ��{data }
+      .�  S" @   �   �	m  
  %  !   0" @   �  �  R�RQ|  9" @   
  �" @   u  �  Rv Xs  
�" @   U  Ru   �  syscfgGetSize s�  �  �  s&{   syscfgGetData c+    �  c&{   syscfgInitWithData A�     @   �       �4  data A�  >  4  dataLen A.�  m  e  hdr B  �  �  found C�  �  �  2  @   
  \  @   �  �  RgfCS i  @   
  �  @   �  �  Rt  �  @   u    Qs Xt  �  @   �    Rs  
�  @   �  Rs   +syscfg_reinit 8� @   /       �h    @      Eprint_xml �  /node &�  xml �  len  �   Fno_debug �   � @          ��  ,fmt !�  R &  � @   %      �r  B    �  O      Z  �@f  N  H  w  j  d  + @   
  � @   
  � @     ]  Rt  
� @   )  Rt   �  �  @   F       ��  1
  R �  0! @          ��  1�  R &  �" @          �  B  �  �  O  �  �  Z  f  w  2�" @   �  R�RQ�Q  �  0% @          �p  �  �  �  �  �  �  �  25% @   �  R�RQ�Q  G� 3memcpy __builtin_memcpy 3puts __builtin_puts  h   2  GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden �  �  �( @   w       �  char long long unsigned int long long int short unsigned int int long int unsigned int long double signed char uint8_t $  unsigned char int16_t %:  short int uint16_t &�   int32_t '�   uint32_t (�   h  int64_t )&�   uint64_t *0�   	endianness �   �  BIG  LITTLE  endianness_t �  swap_uint64 .
�  �( @          �  val . �       swap_int64 '	~  �( @          �V  val '~  �  �   swap_int32 !	X  �( @          ��  val !X       swap_uint32 
h  �( @          ��  val  h  8  4   swap_int16 	*  �( @          �  val *  R swap_uint16 
G  �( @          �<  val  G  R 
endian �  �( @          �i 	y  �l  �   �  GNU C17 13.1.0 -mtune=generic -march=nocona -g -O2 -fsigned-char -fvisibility=hidden \  s   ) @   �       �   char long long unsigned int long long int short unsigned int int long int unsigned int unsigned char double float long double signed char uint8_t $�   short int uint32_t (�   u_int32_t 
J  u_int8_t -  long unsigned int syscfgHeader (  shMagic *J   shSize +J  shMaxSize ,J  shVersion -J  shBigEndian .J  shKeyCount /J   syscfgEntry 2C  seTag 4J   seData 5C   	-  S  
�    syscfgEntryCNTB 8�  seTag ;J   seRealTag <J  
�  =J  
�  >J  reserved ?J   syscfgMemEntry B  seTag D[   seData E  �  F[  �  G[   	m    �   � swap_uint32 
J  0  J   swap_syscfgEntryCNTB �  �) @   F       ��  entry F�  j  d  �) @     �) @     �) @     �) @     �) @      S  swap_syscfgMemEntry ,  P) @   <       �,  entry C,  �  �  _) @     l) @     }) @      �  swap_syscfgEntry 
�  0) @          ��  entry 
:�  �  �  ?) @        swap_syscfgHeader �   ) @   %       ��  header =�  �  �  ) @     ) @      �   ]   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 f  N  �* @   �       �!  char long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double ^  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �G  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  
tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   		  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � func_ptr Y  	  %   __CTOR_LIST__   __DTOR_LIST__ 
  initialized 2�   	�� @   atexit ��   �  Y   __main 5`+ @          ��  + @   �   	__do_global_ctors  �* @   j       �  
nptrs "�   �  �  
i #�   �  �  =+ @   j  R	�* @     	__do_global_dtors �* @   :       �[  p [  	P @    	   �   >  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 N  6  ##  char long long unsigned int long long int short unsigned int int long int unsigned int �   long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �$  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  	tagCOINITBASE �   �\  COINITBASE_MULTITHREADED   VARENUM �   	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � 
�  �   �,  __uninitialized  __initializing __initialized  �  ��  ,  __native_startup_state �+8  __native_startup_lock �x  ~  
__native_dllmain_reason � �   __native_vcclrit_reason � �     	$P @   �  	 P @   =  
"	�� @   [  	�� @    �    �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 \  D  y#  _dowildcard  �   	0P @   int  }   "  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �+ @          �#  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float signed char short int double long double _Float16 __bf16 _setargv �   �+ @          � �    \  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �	  	  	$  _newmode �   	�� @   int  �   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 6
  
  �+ @   �       C$  char long long unsigned int long long int uintptr_t K,   short unsigned int int long int w   unsigned int long unsigned int unsigned char ULONG �   WINBOOL 
�   BOOL ��   DWORD ��   float LPVOID �   signed char short int ULONG_PTR 1.   PVOID    HANDLE �   ULONGLONG �.   double long double �  _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  	JOB_OBJECT_NET_RATE_CONTROL_ENABLE 	JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH 	JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 	JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  PIMAGE_TLS_CALLBACK S �  �  �    �  M  �   _IMAGE_TLS_DIRECTORY64 (U �  StartAddressOfRawData V �   EndAddressOfRawData W �  AddressOfIndex X �  AddressOfCallBacks Y �  SizeOfZeroFill Z 
M   Characteristics [ 
M  $ IMAGE_TLS_DIRECTORY64 \   IMAGE_TLS_DIRECTORY o #�  �  _PVFV �    _tls_index #"  	ܥ @   _tls_start )�   	 � @   _tls_end *�   	� @   __xl_a ,+�  	0� @   __xl_z -+�  	H� @   _tls_used /  	 h @   
__xd_a ?  	P� @   
__xd_z @  	X� @   _CRT_MT G�   __dyn_tls_init_callback g�  	�g @   __xl_c h+�  	8� @   __xl_d �+�  	@� @   __mingw_initltsdrot_force ��   	إ @   __mingw_initltsdyn_force ��   	ԥ @   __mingw_initltssuo_force ��   	Х @   __mingw_TLScallback 0    �  M  d   __dyn_tls_dtor �@  �+ @   /       �}  
�  �  
    
�  *M      
�  ;d  .  *  �+ @   �   __tlregdtor m�   P, @          ��  func m  R __dyn_tls_init L@  	  �  �  �  *M  �  ;d  pfunc N
$  ps O
�    �  �+ @   �       ��  D  <  �  l  d  �  �  �  �  �  �  �+ @    �+ @   +       L�  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   5, @   �    �    q  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 $    Y%  _commode �   	� @   int  w   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �%  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV   
  �     o     __xi_a 
  	� @   __xi_z   	(� @   __xc_a   	 � @   __xc_z 
  	� @    2      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 b  J  `, @   �       �%  double char 	�   long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char float long double _exception (��  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   �  _iobuf 0!*  _ptr %�    _cnt &	�   _base '�   _flag (	�   _file )	�   _charbuf *	�    _bufsiz +	�   $_tmpfname ,�   ( 
FILE /�  fprintf "�   X  ]  �   *  X  
__acrt_iob_func ]X  �  �    _matherr �   `, @   �       �0  pexcept 0  #    type 
�  E  9  �, @   b  �  R2 �, @   7  Q	8i @   Xs Yt w �ww(�ww0�w  5   �      GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 
  
  `- @          �&  _fpreset 	`- @          � �    2  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �
  �
  �&  __mingw_app_type �   	� @   int  G   `  'GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 T  �  p- @   =      '  __gnuc_va_list �   (__builtin_va_list �   char )�   va_list w   size_t #,�   long long unsigned int long long int ptrdiff_t X#�   short unsigned int int long int 	�   unsigned int long unsigned int unsigned char *ULONG M  WINBOOL 
%  BYTE �b  WORD �  DWORD �M  float PBYTE ��  	�  LPBYTE ��  PDWORD ��  	�  LPVOID �s  LPCVOID �  	  +signed char short int ULONG_PTR 1.�   SIZE_T �';  PVOID s  LONG ),  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS =  �x  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _MEMORY_BASIC_INFORMATION 0�:  BaseAddress �
\   AllocationBase �
\  AllocationProtect �
�  PartitionId ��  RegionSize �M  State �
�   Protect �
�  $Type �
�  ( MEMORY_BASIC_INFORMATION �x  PMEMORY_BASIC_INFORMATION �!}  	x  �  �  �    _IMAGE_DOS_HEADER @��  e_magic ��   e_cblp ��  e_cp ��  e_crlc ��  e_cparhdr ��  e_minalloc ��  
e_maxalloc ��  e_ss ��  e_sp ��  e_csum ��  e_ip ��  e_cs ��  e_lfarlc  �  e_ovno �  e_res �  e_oemid �  $e_oeminfo �  &e_res2 �  (e_lfanew j  < �  �  �    �    �   	 IMAGE_DOS_HEADER �  ,�T  PhysicalAddress ��  VirtualSize ��   _IMAGE_SECTION_HEADER (~g  Name �   Misc �	  VirtualAddress �
�  SizeOfRawData �
�  PointerToRawData �
�  PointerToRelocations �
�  PointerToLinenumbers �
�  NumberOfRelocations ��   NumberOfLinenumbers ��  "Characteristics �
�  $ PIMAGE_SECTION_HEADER ��  	T  -tagCOINITBASE =  ��  COINITBASE_MULTITHREADED   VARENUM =  	L
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � ._iobuf 0	!
�
  _ptr 	%8   _cnt 	&	%  _base 	'8  _flag 	(	%  _file 	)	%  _charbuf 	*	%   _bufsiz 	+	%  $_tmpfname 	,8  ( FILE 	/L
  __RUNTIME_PSEUDO_RELOC_LIST__ 1
�   __RUNTIME_PSEUDO_RELOC_LIST_END__ 2
�   __ImageBase 3  <r  addend =	�   target >	�   runtime_pseudo_reloc_item_v1 ?J  G�  sym H	�   target I	�  flags J	�   runtime_pseudo_reloc_item_v2 K�  M)  magic1 N	�   magic2 O	�  version P	�   runtime_pseudo_reloc_v2 Q�  /�  (��  old_protect �	�   base_address �	\  region_size �
M  sec_start �	�  hash �g    0�  �I  the_secs ��  	� @   	�  maxSections �%  	� @   GetLastError 0�  VirtualProtect 
G�  E
  �  M  �  �   VirtualQuery 
/M  n
  	  [  M   _GetPEImageBase ��  __mingw_GetSectionForAddress �g  �
  �   memcpy 2s  �
  s    �    1abort 
�(2vfprintf 	)%  	      �    	�
   	  	�      __acrt_iob_func 	]	  ?  =   __mingw_GetSectionCount �%  3_pei386_runtime_relocator �P/ @   ]      ��  4was_init �%  	 � @   5mSecs �%  �  �  !�  �/ @   H  �4  �  �  �  6H  
�  �  �  
�  +  �  
�  B  *  
�  �  �  

  �  �  
  $    "E  X  �  
F  q  i  
[  �  �  �0 @   `  R	�j @   Xu w t     w0 @   w0 @          �;  �  �  �  �  �  �  �  �  �    w0 @   w0 @          �  �  �  �         �        0 @   �  Ru    !  B1 @   r  ��  �        �  &   $   �  5   3   7  B1 @   r  �  ?   =   �  J   H   �  Y   W   N1 @   �  Ru      �1 @   �1 @   
       �w  �  c   a   �  n   l   �  }   {     �1 @   �1 @   
       �  �   �   �  �   �   �  �   �   �1 @   �  Ru      2 @   2 @          �   �  �   �   �  �   �   �  �   �     2 @   2 @          �  �   �   �  �   �   �  �   �   2 @   �  Ru    "$  }  �  
)  �   �   83  �  
4  !  !    ~2 @   ~2 @   
       s�  !  !  �  &!  $!  �  5!  3!    ~2 @   ~2 @   
       �  ?!  =!  �  J!  H!  �  Y!  W!  �2 @   �  Rt      
�2 @   `    R	Xj @    �2 @   `  R	 j @      9�  `1 @   X       �|  
�  e!  a!  :�  ���1 @   
  Yu   �/ @   ?   #do_pseudo_reloc 5p  start 5s  end 5's  base 53s  addr_imp 7
�   reldata 7�   reloc_target 8
�   v2_hdr 9p  r :!u  bits ;=  ;E  o k&z  $newval p
�    $max_unsigned ��   min_signed ��     	)  	�  	r  #__write_memory �  addr s  src )  len 5�    <restore_modified_sections ��  %i �%  %oldprot �	�   =mark_section_writable ��- @   b      �`  &addr ��  �!  u!  b �:  ��h �g  �!  �!  i �%  �!  �!  >�. @   P       �  new_protect �
u  "  "  
�. @   
  �  Ys  �. @    
  / @   `  R	�i @     
@. @   �
  �  Rs  m. @   n
  
�. @   E
    Q��X0 
2/ @   `  >  R	�i @    B/ @   `  R	�i @   Qs   ?__report_error Tp- @   i       �/  &msg T  "  "  @argp ��   �X
�- @     �  R2 
�- @   /  �  R	�i @   Q1XK 
�- @       R2 
�- @   �
  !  Qs Xt  �- @   �
   Afwrite __builtin_fwrite   �   8  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �2 @   L       �,  double char 	�   long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char float long double 
_exception (�
�  type �	�    name ��  arg1 �w   arg2 �w   retval �w     �   fUserMathErr 	�  �  �   �  �   0  stUserMathErr 
�  	� @   
__setusermatherr ��  �   __mingw_setusermatherr ��2 @          �P  f ,�  ."  *"  �2 @   �  R�R  __mingw_raise_matherr ��2 @   >       �typ !�   B"  <"  name 2�  Z"  V"  a1 ?w   l"  h"  a2 Jw   �"  |"  rslt 
w   � ex 0  �@�2 @   R�@   �    J  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  m  K-  _fmode �   	 � @   int  �   x  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 $     3 @   �      �-  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char     �      _EXCEPTION_RECORD �[�  ExceptionCode \
�   ExceptionFlags ]
�  �  ^!  ExceptionAddress _
+  NumberParameters `
�  ExceptionInformation a�    �  _CONTEXT �%�  P1Home 
   P2Home 
  P3Home 
  P4Home 
  P5Home 
   P6Home 
  (ContextFlags �  0MxCsr �  4SegCs 
�  8SegDs 
�  :SegEs 
�  <SegFs 
�  >SegGs 
�  @SegSs 
�  BEFlags �  DDr0  
  HDr1 !
  PDr2 "
  XDr3 #
  `Dr6 $
  hDr7 %
  pRax &
  xRcx '
  �Rdx (
  �Rbx )
  �Rsp *
  �Rbp +
  �Rsi ,
  �Rdi -
  �R8 .
  �R9 /
  �R10 0
  �R11 1
  �R12 2
  �R13 3
  �R14 4
  �R15 5
  �Rip 6
  ��   VectorRegister O�   VectorControl P
  �DebugControl Q
  �LastBranchToRip R
  �LastBranchFromRip S
  �LastExceptionToRip T
  �LastExceptionFromRip U
  � BYTE ��   WORD ��   DWORD ��   float signed char short int ULONG_PTR 1.   DWORD64 �.   PVOID �  LONG )�   LONGLONG �%�   ULONGLONG �.   _M128A �(�  Low �W   High �F   M128A �i  �  �  
    �  �  
    �  �  
   _ double long double _Float16 __bf16 _XMM_SAVE_AREA32  �c  ControlWord �
�   StatusWord �
�  TagWord �
�  Reserved1 �
�  ErrorOpcode  
�  ErrorOffset �  ErrorSelector 
�  Reserved2 
�  DataOffset �  DataSelector 
�  Reserved3 
�  MxCsr �  MxCsr_Mask �  FloatRegisters 	�   XmmRegisters 
�  �Reserved4 
�  � XMM_SAVE_AREA32   �:�  Header ;�   Legacy <�   Xmm0 =�  �Xmm1 >�  �Xmm2 ?�  �Xmm3 @�  �Xmm4 A�  �Xmm5 B�  �Xmm6 C�   Xmm7 D�  Xmm8 E�   Xmm9 F�  0Xmm10 G�  @Xmm11 H�  PXmm12 I�  `Xmm13 J�  pXmm14 K�  �Xmm15 L�  � �  �  
     7�  FltSave 8c  FloatSave 9c   {   �  �  
    PCONTEXT V�  	  	  
    EXCEPTION_RECORD b  PEXCEPTION_RECORD d?	  	  _EXCEPTION_POINTERS y�	  �  z%	   ContextRecord {�   EXCEPTION_POINTERS |D	  D	  JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �w
  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  |
  !9  �
  �	   PTOP_LEVEL_EXCEPTION_FILTER w
  LPTOP_LEVEL_EXCEPTION_FILTER %�
  "tagCOINITBASE �   �  COINITBASE_MULTITHREADED   VARENUM �   	�
  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I	VT_BSTR_BLOB �	VT_VECTOR  	VT_ARRAY   	VT_BYREF  @	VT_RESERVED  �	VT_ILLEGAL ��	VT_ILLEGALMASKED �	VT_TYPEMASK � __p_sig_fn_t 0	  #__mingw_oldexcpt_handler ��
  	0� @   $_fpreset 
%signal <�
    �   �
   &_gnu_exception_handler ��    3 @   �      ��  'exception_data �-�  �"  �"  old_handler �
	  �"  �"  action ��   @#  &#  reset_fpu ��   �#  �#  
\3 @   �
  �  R8Q0 (�3 @   �  R�R 
�3 @   �
  �  R4Q0 �3 @   �  R4 
4 @   �
    R8Q0 
54 @   �
  7  R8Q1 
L4 @   �
  S  R;Q0 b4 @   f  R; w4 @   y  R8 
�4 @   �
  �  R;Q1 
�4 @   �
  �  R4Q1 
�4 @   �
  �  R8Q1 )�4 @   �
   �	   �
   �  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 5    �4 @   b      /  char size_t #,�   long long unsigned int long long int short unsigned int int �   long int unsigned int long unsigned int unsigned char WINBOOL 
�   WORD ��   DWORD ��   float LPVOID �  signed char short int ULONG_PTR 1.�   LONG )�   HANDLE �  _LIST_ENTRY q�  Flink r�   Blink s�   �  LIST_ENTRY t�  double long double _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  _RTL_CRITICAL_SECTION_DEBUG 0�#�  Type �#/   CreatorBackTraceIndex �#/  CriticalSection �#%�  ProcessLocksList �#�  EntryCount �#
<   ContentionCount �#
<  $Flags �#
<  (CreatorBackTraceIndexHigh �#/  ,SpareWORD �#/  . _RTL_CRITICAL_SECTION (�#�  DebugInfo �##�   LockCount �#�  RecursionCount �#�  OwningThread �#�  LockSemaphore �#�  SpinCount �#~    �  PRTL_CRITICAL_SECTION_DEBUG �##�  �  RTL_CRITICAL_SECTION �#�  PRTL_CRITICAL_SECTION �#�  CRITICAL_SECTION � �  LPCRITICAL_SECTION �!�  3  >     __mingwthr_cs �  	`� @   __mingwthr_cs_init �   	H� @   __mingwthr_key_t �  �  __mingwthr_key  �  key !	<   dtor "
.  next #�   �  key_dtor_list '#�  	@� @   GetLastError 
0<  TlsGetValue 	#S  6  <   _fpreset %DeleteCriticalSection .e     InitializeCriticalSection p�     free �     LeaveCriticalSection ,�     EnterCriticalSection +�     calloc             __mingw_TLScallback z  06 @   �       �n  	hDllHandle z�  $  �#  	reason {<  �$  z$  	reserved |S  %  �$   �6 @   K       �  
keyp �&�  ~%  x%  
t �-�  �%  �%  �6 @   �  
�6 @   C  R	`� @     !n  u6 @   u6 @          �  �  �6 @   )
   "n  �6 @   �  �E  #�  �  7 @   )
    �6 @   6  
7 @   e  R	`� @     $__mingwthr_run_key_dtors c�  keyp e�  %value mS    ___w64_mingwthr_remove_key_dtor A�   �5 @   �       �d	  	key A(<  �%  �%  
prev_key C�  �%  �%  
cur_key D�  �%  �%  �5 @   �  B	  Rt  6 @   �  
6 @   �  Rt   ___w64_mingwthr_add_key_dtor *�   05 @   o       �$
  	key *%<  &  &  	dtor *1.  F&  8&  
new_key ,$
  �&  ~&  _5 @   �  �	  R1QH }5 @   �  
  Rt  
�5 @   �  Rt   �  &n  �4 @   p       ��  �&  �&  '�  �4 @          �
  �  �&  �&  �4 @     5 @     (5 @   Rt   �4 @   �  �
  R|  )05 @   �  R	`� @      �    W   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 Z  B  �1  _CRT_MT �   	@P @   int  �    �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �1  __RUNTIME_PSEUDO_RELOC_LIST_END__ �   	�� @   char __RUNTIME_PSEUDO_RELOC_LIST__ �   	�� @    �   �   GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  07 @   �      �1  long long unsigned int char  �   
size_t #,w   long long int short unsigned int int long int unsigned int long unsigned int unsigned char !
WINBOOL 
�   
BYTE �  
WORD ��   
DWORD ��   float 
PBYTE �n  /  
LPVOID �  signed char short int 
ULONG_PTR 1.w   
DWORD_PTR �'�  LONG )�   ULONGLONG �.w   double long double _Float16 __bf16 /     w    _IMAGE_DOS_HEADER @�t  e_magic �<   e_cblp �<  e_cp �<  e_crlc �<  e_cparhdr �<  e_minalloc �<  
e_maxalloc �<  e_ss �<  e_sp �<  e_csum �<  e_ip �<  e_cs �<  e_lfarlc  <  e_ovno <  e_res t  e_oemid <  $e_oeminfo <  &e_res2 �  (e_lfanew �  < <  �  w    <  �  w   	 IMAGE_DOS_HEADER    PIMAGE_DOS_HEADER �     _IMAGE_FILE_HEADER b�  Machine c<   NumberOfSections d<    e
I  PointerToSymbolTable f
I  NumberOfSymbols g
I  SizeOfOptionalHeader h<  .  i<   IMAGE_FILE_HEADER j�  _IMAGE_DATA_DIRECTORY ��  I  �
I   Size �
I   IMAGE_DATA_DIRECTORY ��  �    w    _IMAGE_OPTIONAL_HEADER64 ��0  Magic �<   MajorLinkerVersion �/  MinorLinkerVersion �/  SizeOfCode �
I  SizeOfInitializedData �
I  SizeOfUninitializedData �
I  AddressOfEntryPoint �
I  BaseOfCode �
I  ImageBase ��  SectionAlignment �
I   FileAlignment �
I  $MajorOperatingSystemVersion �<  (MinorOperatingSystemVersion �<  *MajorImageVersion �<  ,MinorImageVersion �<  .MajorSubsystemVersion �<  0MinorSubsystemVersion �<  2Win32VersionValue �
I  4SizeOfImage �
I  8SizeOfHeaders �
I  <CheckSum �
I  @Subsystem �<  DDllCharacteristics �<  FSizeOfStackReserve ��  HSizeOfStackCommit ��  PSizeOfHeapReserve ��  XSizeOfHeapCommit ��  `LoaderFlags �
I  hNumberOfRvaAndSizes �
I  lDataDirectory ��  p IMAGE_OPTIONAL_HEADER64 �  PIMAGE_OPTIONAL_HEADER64 � q    PIMAGE_OPTIONAL_HEADER &P  "_IMAGE_NT_HEADERS64 �  Signature 
I   FileHeader �  OptionalHeader 0   PIMAGE_NT_HEADERS64 	  �  PIMAGE_NT_HEADERS "!�  �b	  PhysicalAddress �I  VirtualSize �I   _IMAGE_SECTION_HEADER (~^
  Name    Misc �	/	  I  �
I  SizeOfRawData �
I  PointerToRawData �
I  PointerToRelocations �
I  PointerToLinenumbers �
I  NumberOfRelocations �<   NumberOfLinenumbers �<  ".  �
I  $ PIMAGE_SECTION_HEADER �|
  b	  | �
  #.  } I  OriginalFirstThunk ~ I   _IMAGE_IMPORT_DESCRIPTOR {    $�
     � 
I  ForwarderChain � 
I  Name � 
I  FirstThunk � 
I   IMAGE_IMPORT_DESCRIPTOR � �
  PIMAGE_IMPORT_DESCRIPTOR � 0a     %__ImageBase 
�  strncmp V�   �  �  �  �    �   strlen @�   �  �   
__mingw_enum_import_library_names ��  `: @   �       �7
  i �(�   �&  �&  >  �	`  $  �	  �&  �&  importDesc �@  �&  �&  
  �^
  importsStartRVA �	I  '  '  �  `: @   	Z  ��  �  Z  �  �  �  	�  r: @    j  �  j  �  �  J'  F'  �  ['  Y'      M  �: @   �: @   J       �q  g'  e'  f  }  s'  o'  �  �'  �'  �  �'  �'    
_IsNonwritableInCurrentImage �  �9 @   �       ��  pTarget �%`  �'  �'  >  �	`  rvaTarget �
�  �'  �'  
  �^
  �'  �'  �  �9 @   ?  �/  �  ?  �  �  �  	�  �9 @    O  �  O  �  �  �'  �'  �  (  
(      M  : @   : @   I       �q  (  (  f  }  "(   (  �  ,(  *(  �  6(  4(    
_GetPEImageBase �`  �9 @   6       �0  >  �	`  	�  �9 @   $  �	�  $  �  �  �  	�  �9 @    4  �  4  �  �  C(  ?(  �  T(  R(       
_FindPESectionExec y^
  9 @   s       �%  eNo y�   b(  ^(  >  {	`  $  |	  s(  q(  
  }^
  }(  {(  X  ~�   �(  �(  	�  9 @   	  �	�  	  �  �  �  	�  !9 @      �    �  �  �(  �(  �  �(  �(       
__mingw_GetSectionCount g�   �8 @   7       ��  >  i	`  $  j	  �(  �(  	�  �8 @   �  m	�  �  �  �  �  	�  �8 @    �  �  �  �  �  �(  �(  �  �(  �(       
__mingw_GetSectionForAddress Y^
  P8 @   �       �  p Y&s  �(  �(  >  [	`  rva \
�  )  )  �  P8 @   �  _�  �  �  �  �  �  	�  `8 @    �  �  �  �  �  )  
)  �  ")   )      	M  �8 @   �  c
q  .)  ,)  f  �  }  :)  6)  �  X)  V)  �  b)  `)     
_FindPESectionByName :^
  �7 @   �       �M  pName :#�  u)  k)  >  <	`  $  =	  �)  �)  
  >^
  �)  �)  X  ?�   �)  �)  �  �7 @   �  F  �  �  �  �  �  �  �7 @    �7 @          �  �  �  �)  �)  �  �)  �)     &�7 @   �  -  Rt  '*8 @   z  Rs Qt X8  _FindPESection $^
  �  >  $`  (rva $-�  $  &	  
  '^
  X  (�    _ValidateImageBase   �  >  `  pDOSHeader �  $  	  pOptHeader v   )�  07 @   ,       �~  �  �)  �)  �  �)  �)  �  �  	�  97 @    �  �  �)  �)  �  �  �  *  *  �  %*  #*     *M  `7 @   P       �f  1*  -*  +q  Q}  D*  @*  �  c*  a*  �  m*  i*    �    ?#  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  }  I7  _MINGW_INSTALL_DEBUG_MATHERR �   	PP @   int  �   m#  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 E  -  p; @          �7  __gnuc_va_list �   
__builtin_va_list �   char 	�   va_list w   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(3  8  
threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  �    j  �  �   �  __imp_vfprintf �  	`P @   w  __stdio_common_vfprintf �	    �   �  �    �    vfprintf �	  p; @          �_File *�  �*  �*  _Format J�  �*  �*  _ArgList Z�   �*  �*  �; @   �  R0Q�RX�QY0w �X   �   �$  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 -    �; @   B       �7  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   size_t #,�   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(B  G  	threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   e  j  t  
 �   e  �   o  __imp_sprintf �  	pP @   P  __stdio_common_vsprintf �  �  �   e  �   o  $  �    sprintf �  �; @   B       �_Dest )j  +  +  _Format It  5+  /+  
ap 
�   �hret   J+  H+  �; @   �  R2Q�RX	�Y�Qw 0w(�   �   �%  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99   �  �; @   9       �8  __gnuc_va_list �   __builtin_va_list �   char 	�   va_list w   size_t #,�   long long unsigned int long long int short unsigned int 	�   int long int pthreadlocinfo �(B  G  
threadlocaleinfostruct ��  _locale_pctype �;   _locale_mb_cur_max �  _locale_lc_codepage �@   pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �$  locinfo �+   mbcinfo ��   _locale_t �6  �    unsigned int   j  o  �   y   �   j  �   t  __imp_snprintf �  	�P @   P  __stdio_common_vsprintf �  �  �   j  �   t  $  �    snprintf G  �; @   9       �__stream +o  b+  \+  __n <�   {+  u+  __format [y  �+  �+  ap 
�   �hret   �+  �+  < @   �  R2Q�RX�QY�Xw 0w(�   �   '  GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �   < @   H       9  __gnuc_va_list �   
__builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  
 �   �  __imp_printf �  	�P @   w  __stdio_common_vfprintf �	  �  �   �  �    �    j  __acrt_iob_func ]�    1   printf �	   < @   H       �_Format .�  �+  �+  
ap 
�   �Xret 	  �+  �+  L< @   �  �  R1 a< @   �  R0Xs Y0w t    �   �(  
GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  p< @   2       �9  __gnuc_va_list �   __builtin_va_list �   char �   va_list w   long long unsigned int long long int short unsigned int �   int long int pthreadlocinfo �(3  8  	threadlocaleinfostruct ��  _locale_pctype �,   _locale_mb_cur_max �	  _locale_lc_codepage �1   pthreadmbcinfo �%�  �  threadmbcinfostruct 	localeinfo_struct �  locinfo �   mbcinfo ��   _locale_t �'  �    unsigned int _iobuf !
h  _Placeholder #h    FILE /A  	  �  �  �  
 j  �  �   �  __imp_fprintf �  	�P @   w  __stdio_common_vfprintf �	    �   �  �    �    fprintf �	  p< @   2       �_File )�  �+  �+  _Format I�  ,  ,  
ap 
�   �hret 	  ,  ,  �< @   �  R0Q�RX�QY0w �   �   �)  	GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  %:  char long long unsigned int long long int 
wchar_t b�   short unsigned int int long int g   �   unsigned int long unsigned int unsigned char float signed char short int double long double V  �   `  �   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS �   �M  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  tagCOINITBASE �   ��  COINITBASE_MULTITHREADED   VARENUM �   	  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � __imp___winitenv d[  __imp___initenv iQ  local__initenv 	V  	ئ @   local__winitenv 
`  	Ц @   '  
	�P @     
	�P @    �   �*  "GNU C99 13.1.0 -m64 -masm=att -mtune=generic -march=nocona -g -O2 -std=gnu99 �  �  �< @         �:  __gnuc_va_list �   #__builtin_va_list �   char �   va_list w   long long unsigned int long long int wchar_t b  �   short unsigned int   int long int pthreadlocinfo �(H  M  threadlocaleinfostruct ��  _locale_pctype �A   _locale_mb_cur_max �  _locale_lc_codepage �F   pthreadmbcinfo �%�  �  $threadmbcinfostruct localeinfo_struct �*  locinfo �1   mbcinfo ��   _locale_t �<  �    unsigned int [  %f     long unsigned int unsigned char �   float   %  signed char short int _onexit_t 2�  �    double long double �  &�   _Float16 __bf16 JOB_OBJECT_NET_RATE_CONTROL_FLAGS F  ��  JOB_OBJECT_NET_RATE_CONTROL_ENABLE JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS  �  'tagCOINITBASE F  �%  COINITBASE_MULTITHREADED   �   VARENUM F  	�  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � (_iobuf 0	K
<  
_ptr L�   
_cnt M	  
_base N�  
_flag O	  
_file P	  
_charbuf Q	   
_bufsiz R	  $
_tmpfname S�  ( FILE 	U�  N  %  �  )	yr  
newmode z	    _startupinfo 	{X  �  �  �    _PVFV 
�  __mingw_module_is_dll :
�   �  �  �   __imp__onexit [�  	0Q @   �      �   __imp_at_quick_exit g)  	(Q @   �  _tzset_func w�  __imp__tzset x.  �   f  �    
initial_tzname0 |
V  	$Q @   
initial_tzname1 }
V  	 Q @   
initial_tznames ~�  	Q @   
initial_timezone 
%  	Q @   
initial_daylight �  	Q @   __imp_tzname ��  	 Q @   __imp_timezone ��  	�P @   __imp_daylight ��  	�P @     �	  �  S  S    �	   r  __imp___getmainargs ��	  	�P @   k	    �	  �  I  I    �	   __imp___wgetmainargs �
  	�P @   �	  __imp__amsg_exit �V  	�P @   F  __imp__get_output_format �\
  	�P @   -
  __imp_tzset ��  	�P @     �
  �
  �   <  �
  __imp___ms_fwprintf ��
  	�P @   ~
  __stdio_common_vfwprintf )    �   �
  �  *  �    	__daylight x�  	__timezone z�  	__tzname {�  *_exit � Q     fprintf �  p  �
  u   �   p  __acrt_iob_func ]�
  �  F   _crt_at_quick_exit 
$  �  �   _crt_atexit 
#  �  �   	__p__wenviron �I  	__p___wargv �I  _configure_wide_argv 5  0     	_initialize_wide_environment 3  _set_new_mode 8  u     	__p__environ �S  	__p___argv �S  	__p___argc ��  _configure_narrow_argv 4  �     	_initialize_narrow_environment 2  __ms_fwprintf   > @   5       ��
  file �!�
  4,  .,  fmt �6�  S,  M,  
ap ��   �h+ret �  n,  l,  @> @   �
  R4Q�RX�QY0w �  ,tzset "P> @   6       �  -  T> @   T> @   -       �b> @   +  n> @     z> @       ._tzset �/_get_output_format nF  �< @          �0_amsg_exit i�= @   .       ��  ret i  z,  v,  �= @   z  �  R2 > @   Q  �  Q	k @   Xs  > @   <  R�  at_quick_exit �  �= @          �   func ]*�  �,  �,  1�= @   �   _onexit ��  �= @          �p  func V%�  �,  �,  �= @   �  Rs   __wgetmainargs J  0= @   j       �[  _Argc J"�  �,  �,  _Argv J5I  �,  �,  _Env JGI  -  �,   a  JQ  !-  -  !m  Jl�	  � P= @   0  `= @   	  &  R	v  $0.# e= @   �  n= @   �  y= @   �  �= @   U   __getmainargs >  �< @   j       �E  _Argc >!�  @-  :-  _Argv >1S  Y-  S-  _Env >@S  r-  l-   a  >J  �-  �-  !m  >e�	  � �< @   �  �< @   �    R	v  $0.# �< @   �  �< @   �  	= @   u  = @   U   2  �> @   6       ��> @   +  �> @     �> @                                                                                                            
 :!;9I8  
 :!;9I8  (    I   !I   :;9I  4 :;9I?<  $ >  	I ~  
 :;9I  H }  
 :!;9I�!8  

 :!;9I�!8  :!;9  ! I/  4 :;9I  H}  (    :;9I  4 1�B  
 :!;9I8  I  4 :!;9I  .?:;9'I<  
 :!;9I  
 :;9I8   1�B  I   .?:;9'<  H}  4 :!;9I�B    :!;9I  !I�!  ". ?:;9'<  #& I  $'I  %!:!;9!  &
 I8  '4 :!;9!I?<  (.?:;9'I<  ). ?:;9'I<  *1R�BUX!YW  +U  ,.?:!;9'I !  -5 I  .�!:!;9  / :!;9I�!  0'  1
 :!;9!I�!  2>!!I:;9  3!   44 :!;9!I?  5.:!;9!'I@z  6 1  7.?:!;9!'I@z  8.:!;9!'@z  9%  :   ;
 I�8  <&   =�:;9  > 'I  ? '  @�:;9  A�:;9  B
 I�  C:;9  D>I:;9  E:;9  F>I:;9  G :;9I  H5   I:;9  J4 :;9I  K4 :;9I?  L.?:;9'�<  M.?:;9'I@z  N :;9I�B  O.:;9'   P  Q.:;9'I   R1R�BUXYW  S1R�BUXYW  T1U  U1R�BXYW  VH}  W4 1  X <  Y. ?:;9'I   Z. ?<n:;   I ~  H}   I  ( 
  I �~  H }  4 :!;9I�B  (   	4 :!;9I  
H}  .?:;9'I<   :;9I  
 !I  $ >    
 :;9I8  4 :!;9I  .?:;9'I<  4 1�B  4 :!;9I   1�B   :!;9I�B     :!;9I  >!!I:;9  .?:!;9!
'<  :;9   :;9I   <  >!!I:;9       :!;9I  !4 :!;9I�B  "1  #I  $! I/  %4 :!;9I  & :!;9I�B  '. ?<n:!;!   (& I  )7 I  *. ?:!;9!
'I<  +.?:;9!'<  ,.:!;9'   -U  .1R�BUX!YW  /U  01U  1.:!;9'I   24 :!;9!I!   34 :!;9!
I�B  4.1@z  5%U  6   74 :;9I?  8:;9  9.?:;9'�<  :.?:;9'I@z  ;4 :;9I
  <4 :;9I  =4 1  >.?:;9'@z  ?.:;9'I@z  @.?:;9@z  A.:;9'I@z  B.:;9'@z  C 1  DH}�  E. ?<n   I ~   I  H}  4 :!;9I�B   1�B  $ >   !I  H }  	
 :;9I8  
.?:;9'I<   :;9I  4 1  
H}   :!;9I�B  4 :!;9I  .?:!;9'I@z  ( 
  .?:;9'I<  4 :!;9I�B  .1@z  :;9  4 :!;9I  4 :!;9I  (   I     4 1�B  1R�BX!YW  4 1  .?:!;9!'I !   :!;9I   & I  !! I/  "
 :!;9!I8  #
 :!;9!I8  $.?:;9'<  %. ?:;9'I<  & :!;!�9I�B  '4 :!;9I  (1R�BUX!YW  )U  * :!;!�9I  +.?:!;9!'@z  , :!;9I  -4 :!;9I  .1R�BUX!YW  / :!;9I  04 :!;9I�B  1 1  2H}�  3. ?<n:!;!   4%  5   6>I:;9  7:;9  8! I/  9>I:;9  :'I  ;4 :;9I?  <&   =.?:;9'I@z  >4 :;9I  ?4 :;9I  @U  A.?:;9'I   BH }�  C4 :;9I  D :;9I�B  E.:;9'   F.:;9'I@z  G6    $ >   :;9I  .?:!;9'I@z   :!;9I�B  (    :!;9I  %  5 I  	>I:;9  
.?:;9'I@z  4 :;9I   $ >  
 :!;9I8  H }   :;9I  .?:!;9'I@z   :!;9I�B   !I  :!;9!  	I  

 :!;9!I8  
 :!;9!I8  %  
! I/  :;9  ! I/  .?:;9'I<   I   (   $ >  (    :;9I   !I  >!!I:;9  4 :!;9!I?<  4 :!;9I  	.?:!;9!'@|  
4 :!;9!I�B  %   '  
>I:;9  I  !   .?:;9'I<   I  .?:;9'@z  H }�  H}�  I ~   (   $ >  (   4 :!;9I?<  4 G:!;9  5 I  >!!I:;9  %  	>I:;9  
>I:;9   :;9I   I  
5    %  4 :;9I?  $ >   $ >  %  . ?:;9'I@z   %  4 :;9I?  $ >   $ >  4 :!;9I?   :;9I   :!;9I   I  
 :!;9I8   1�B   !I  	(   
 :!;!�9I�B   :!;!� 9I  & I  
4 :!;9!$I  H }  4 :!;9I  4 1  4 1�B  %      '  >I:;9  '  :;9  4 :;9I?<  .?:;9'I<  .:;9'I@z  .?:;9'I@z   :;9I  .?:;9'I   .1@|  1R�BXYW   %  4 :;9I?  $ >   $ >  4 :!;9!I?  %   :;9I   I   '  I  ! I/   
 :;9I8  $ >  I ~   !I   I  :;9!
  7 I  %  	& I  
 :;9I  .?:;9'I<     
.?:;9'I<  .?:;9'I@z   :;9I�B  4 :;9I�B  H}  H}   %  . ?:;9'@z   %  4 :;9I?  $ >   (   
 :!;9I8   1�B  I ~  
 :;9I8   :;9I  $ >   I  	 !I  
4 1�B  H}  4 :!;9I  
H}  (    :!;9I   :!;9I  .?:;9'I<  1R�BX!YW  4 :!;9I  H }  :!;9!  I  ! I/  4 :!;9I?<  :!;9!	  . ?:;9'I<   1  1R�BX!YW!  4 :!;9I�B  >!!I:;9  
 :!;9!I   7 I  !1R�BUX!YW  "1U  #.:!;9!' !  $  %4 :!;9I  & :!;9I�B  '%  ( I  )& I  *   +&   ,:;9  ->I:;9  .:;9  /:;9  0 :;9I  1. ?:;9'�<  2.?:;9'I<  3.?:;9'@z  44 :;9I  54 :;9I�B  6U  71R�BUXYW  81U  91XYW  :4 1  ;  <.:;9'   =.:;9'@z  >  ?.:;9'�@z  @   A. ?<n:;   $ >  
 :!;9I8   :!;9I�B   !I   I  4 :!;9!I  I ~  %  	& I  
:;9   :;9I  'I  
.?:;9'<  .?:;9'@z  H}�  .?:;9'@z   :;9I  H}   %  4 :;9I?  $ >   
 :!;9I8  (   I ~  $ >  
 :!;9I�!8  
 :!;9I�!8   :;9I   :!;9I  	(   
H}   !I  
 :!;9I8  
! I/   I  I�!  4 :!;9I�B  H}  :!;9!  
 :!;9I8  �!:!;9   :!;9I�!  I  
 :!;9!I�!  >!!I:;9  %  '     
 I�8  �:;9  �:;9  �:;9   
 I�  !'I  ">I:;9  #4 :;9I?  $. ?:;9'<  %.?:;9'I<  &.?:;9'I@z  ' :;9I�B  (H}�  )H }   
 :!;9I8  $ >  I ~   :;9I   I  H }   :!;9I   !I  	 :!;9I�B  
4 :!;9I�B  (   .?:!;9!'<  
H}  H}  :!;9  4 :!;9I  
 :!;9I8  .?:!;9!'I@z  5 I  .?:;9'I<  4 1  4 :!;9I  4 1�B  %     >I:;9  '  :;9  . ?:;9'I<  . ?:;9'<  .?:;9'<     !1R�BXYW  "1R�BUXYW  #U  $.:;9'   %  &.1@z  '1  (H}  )H}�   %  4 :;9I?  $ >   4 :!;9!I?  %  $ >   
 :!;9I8  4 1  4 1�B   1  $ >  U   :!;9I  4 :!;9I  	1R�BUX!YW  
 :;9I  4 :!;9I�B   !I  
.?:!;9!'I@z  :!;9!  
 :!;9I8   1�B   :!;9I�B  I  ! I/   I  4 :!;9I�B  1R�BUX!YW!	  I ~  
 :!;9!I  1R�BX!YW  !:!;9  .?:!;9'I<  .?:!;9!'I !   :!;9I  4 :!;9I  %   & I  !   ":;9  #
 :;9I  $
 I8  %4 :;9I?<  &H}  'H}  ( :;9I  ).1@z  *.1@z  + 1   %  4 :;9I?  $ >    I  $ >   !I  
 :!;9I8  I ~   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!  7 I  %  
 I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}    I  $ >   !I  I ~  
 :!;9I8   :;9I   :!;9I   :!;!9I�B  	& I  
!:!;9!     7 I  
%   I   <  'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   .?:!;9'I<  %  
 I   <  :;9  
 :;9I8     'I  7 I  4 :;9I?  .?:;9'I@z   :;9I�B  4 :;9I  4 :;9I�B  H}  H}   $ >   !I   I  
 :!;9I8  I ~   :;9I   :!;9I  & I  	!:!;9!  
   7 I   :!;!9I�B  
%   I   <  :;9  
 :;9I8     'I  4 :;9I?  .?:;9'I<  .?:;9'I@z  4 :;9I  4 :;9I�B  H}   (   $ >  (    !I  >!!I:;9  4 :!;9I?<  4 :!;9I  4 G:!;9  	%  
 :;9I  >I:;9   (    I   !I  $ >  H }  I ~  4 :!;9I?   :!;9I�B  	. ?:;9'I<  

 :!	;9I8   :;9I  (   
4 :!;9I  .?:;9'I<  
 :!;9I8  'I  H}  & I   :!;9I     .?:;9'I@z  H}  !:!;9!   'I  >!!I:;9  I  ! I/  4 :!;9I?<  7 I  .?:!;9!'I<  .?:!;9!
'I@z    :!;9I�B  ! :!;9I  "%  # I  $ <  %'  & '  '>I:;9  (:;9  ):;9  *.?:;9'�<  +4 :;9I�B  ,.?:;9'@|  -1R�BXYW  .. ?:;9'   /. ?:;9'I@z  0.?:;9'@z  1H }�  2.1@|                                                                                      5    �   �
      H   `   �   �   �   �   �         #  /  9  B  S  `  i  q  |  �  �  �  �  �  �    	  @   � �K�zz.g��twB
J=��~ sgg� X� X�Z$t]m���J�
���~���
��~XKyE��� ���� 3���{t�t�x.� �{K
��yt���t 	X	ut. .
�%
.�� .��!�Y�Y  �x p@ZZ
:f;<<�r> �!
 �K� ;YI 9N T ^<ut�\�dh�
 eg�� P<
o] .�
� .�/h��X��t
ZfVJ�g � � �H��K�	Z.
�K�	Z.��K) Xg 1    q   �
        S  l  �  �  
�   �   �  �  �  �  �         (  1  :   < 	P @   � v<
A<oAX=[��?X=[�?�/�YY/;Y�	 �e".tItK���Z	X	YY �h� �v>VhY/gKKKX  ,����~ 
�!�!2Wr!1
=���/QX1�Y<nt= X< <Z	�	�iJY	.
L  J�.
�.�~�.�~t
�f�~X!�!2.^!z�
By.{wX, 	.
Kw
	.X!�!2Z�
=!wS 3
K

3[!�q!uZ�
=!Z �
K!
>!yX 5
Ky,
	.MX
� ��� .r-0
v[�� .s,Y
w[�� .s,Y
w]X�� .s--
2[!�!\Z�
=!wS y
K

3X!L�
=!wZ �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?!VX *t
KV,
,X\!L�
=!wZ �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>!Z �
K!
>![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?![ �
L!
?!VX *t
KV,
,X�~X.s--�
� f� .
��Z
U?Y  t ���������uW�zfZ&$ � f
�	 �h( Q
 �l	 .�	p�
 �j	K�m��e	/W	u f� <Y� �  <Y� f�yJC	�d	LY�
�
[.<K	 ! 	�@ @   � J( [!rt�	1 �w t
u� 
 f �	�<J�( �$ �	O- l) �J �- �) � g�� �
uW
� �
� l<
u k f �< � f .	�
uW
� f� <Yw�w�. �|X� wt  ��\�~�	� 	>
�
��v�
 Xu�	XwX	<ZVLYW=YW=ZV>YW= YW= Z V>$YWK$[	�	!tJY�=Y�	�X	u
Y�
=
Y�	�X	u
Y�
=
Z�	�X	=
Z�=
�
�	�	�
Y�
=
Y�	�X	u
Y�
=
Z�	�X	=
� �!�# �
=!Z!�>Y �  
�
u=r
�
/)0 �% 
� 
��ւ<	�	�
��
>�2(�s=(X>Z+�>[4�=*tK/Y/�=X>/ �uY �uY �uY �uY �uY �uY �u Xu�	t	Y(	�t	K	Ys <	uX	t	Y)	�t	K	Ys <	uX	t	Y*	� t	K	Ys <	uX	t	Y)	�t	K	Ye <	uX	t	Y*	� t	K	Ys <	uX	t	Y*	� t	K	Ys <	uX	t	Z)	�t	K	Y� <	uX	t#� p	L#Z#�	> . 	��	�
>q	�
	
0*, � ! t	](>(:	Z	Zd <	v�	�[	Y] EY �=E X � � <YL � � � <YL � � � <YJ � � � <YK � � � <YJ � � � <YJ � � � <Y�X�tZY��	t	YY�t	YY�tZY��{� �>Z�=^/�>_�$/�� ��	� �  <
g� f  <Y���tX�XtX���F . � � <�� .�~��s=X>�!�>�$�� �!YsY;=!X>�$�>[5�=+tK(Y(�=X](�;=(X=�,�>=�q!>=6Y=;!=6& X X"�& 9"[�1"�1s"=Y3 f    h   �
      �  �  �  �      &  0  8  A  I  R  [  d  m  }    	� @   ,���|
6>x.6D
K6s	JK+ X! JX�m�t�L	6 X	�K�/*	�g�p�	 XZ	Y'K
� �
zt��~�KtYY��7hu7>7@U ?(�<v
.A �- �OL XwX	X/Xlf=;uYe <uY	�[Xsf
�sX	�X
nxJKt<Ze/[�x�6� g�� a k
J& J/ zf <��l XY << X		�/�z�&�X.�Sk<YM X�t<<M[tKt.ZJ[t�	/�<	q�X�<
^��	 <<	 X(J���g?q? XKY�
nxtKt<Ze/[� � >Y�f � 5X �.� �LtZ�~���Z	X	YY � X�
xs	I�tZ?5"�5@U[�>.�UP�I=Y;=Y;=YI=Y;=Z�>Y!!!!!!�   ...7vX. 	@% @   0.��.U1�[U>t\� ��<��vV>����=Z�~����
X� <CXX� w�ft� f�u su=Y su/	` xX{	/z
�
%�.<t
:=Z-d�	�
� ��f=
� Z
��J�~��v	tif
Y
�Y
�==	Y�u su=Y su/ X{KT	W/@.JcX-;=K:L��- � <�.6<	J6Y: W6Z:W6ZJ:I]r�
 �     <   �
      �      8  A  J  S   	�( @   	K-	���g�g�K�K�g�g \    A   �
      �  �  �    #  3  <  E  D 	 ) @   YD=.Y ;/ X=�A�YA=. X/�JvYJ=.Y e/Y eg Xg�MZYM=.Y ;/Y ;=Y ;=Y ;= X=� #    K   �
      �  �  �  �  �    
    #  -   	�* @   K
>/
�M
q]�gBtY ] J X t� , �uex�0 �+ 0 f+ X0 <��c� R     J   �
      �  �  �  �        %  .  9  6     .   �
      �  �  �  �  R     .   �
      ,	  D	  k	  u	   	�+ @    6     .   �
      �	  �	  
  
      K   �
      f
  ~
  �
  �
  �
  �
  �
  �
  �
  �
   	�+ @   �P�,Y��gtYhZ
Xg�u 
<Y)* J X	sJ
X	� 6     .   �
      V  n  �  �  6     .   �
      �  
  4  ?  �     <   �
      �  �  �  �  �  �  �   	`, @   Y)� Xf <�fa<�u�u�u�u_u T     .   �
      Q
  i
  �
  �
   	`- @   		3 6     .   �
      �
  
  4  D  |    s   �
      �  �  �  �      #  -  9  C  K  X  a  i  u  �  �  �    	p- @   � >fA?<Z�X?�Z X  <Y �< <��u o A
��
<OY7t<! � J�<��tJtKg1 X>. d Jv VZ f�+J. =+W. =Xuq�#?"YT� tK=K
 fM
e
u-�
\tw�� 	P/ @   �KtXrf�Y.J
�~��� �u�~
gf-�$ � <�
<�L �<t�R(=	f�it=>
R��
�~�<�5� J�&	.*r<A	<x<z<A��
@*=>
�Y��' �=

�J:f=	f�dt=>
R�f
�~5� <�JX<�5�  � �~� p2��
<Z! � J J�0	fKo�=>
R�.�~�.���~�<�YX=>
x =>
x�=>
� �dt'(=
*M(=/��.
� �� I�
 �     7   �
      �    /  J  X  f   	�2 @   
L�Z
�KTYZ
g=yuX 6     .   �
      �  �  �    �    U   �
      Y  q  �  	�  �  �  �  �  �  �       	 3 @   �Y'<z.7B.f�-/�
�+ ��>V����
�[u,�<�
�� .X��2��
��N��
m�u?n�u�X�� MX�2fX�
 t    _   �
      f  ~  �  �  �  �  �  �  �  �  	      1   	�4 @   � ��
 th'.Y0X
KJZJ xX=XW�tuc[K.tr "XX�[Lq/�w:sKg
u.<<gb2Ji��hvUJs�>
.$1
G0
[LY�t
sX*tY�
f��a<XJf��]fJ 
fZ& t
�<
KY& ^r���Y�X�	etf 6     .   �
      �  �  �  �  6     .   �
      1  I  p  �  G    K   �
      �  �    :  C  L  V  b  l  t   	07 @   -yt	C
J=�~�^Q
J>!%KWY�
t&Y<UI
_/&uyC XiI-tS.yt	C
J=��-!JY%J��I[
 N�p�.�_�� t�.yt	C
J=�� ���� !J"6X=AY%X�1
t&Y<UIX32$�� t�.yt	C
J=�� ���� 
K.��� t�.y�	C
J=�� ���� !%KWY��
hZzJIS.��~�t�~.yt	C
J=���~�
����~�t�~.yt	C
J=���~���=�~%!WXY�
t&Y<UI� X4<���~�t�~<y�	C
J=���~���uM�~!%YWY�1
t&YJUIX�<mt=
X=xJ
* �w
K0JB< 6     .   �
      �  �  
    n     A   �
      ~  �  �  �  �  �    
   	p; @   K
�<.Y �     A   �
      e  }  �  �  �  �  �  �   	�; @   KU	\F	\fWY	Y �     A   �
      K  c  �  �  �  �  �  �   	�; @   KU	\f�X�Y	Y �     A   �
      1  I  r  �  �  �  �  �   	 < @   g?	YT�Y	 X� <uX �     A   �
        ,  U  p    �  �  �   	p< @   KU	\fp	\;Y	Y W     O   �
      �  
  5  P  {  �  �  �  �  �  �  |    h   �
        /  V  q  �  �  �  �  �  �  �  �  �         	�< @   � N��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   ��Y  �Y
 X
 ./
 X
 <= X	 <=Y�t   �Y
=/ X�XAt[I
X" 	�= @   Y"/X X� <Y,�KU	\f�	\;Y	Yr�K$R�  Xu"  Xu"  Xzt�K�  Xu"  Xu"  Xu                                                                                                                                                                                                                                                                    ���� x �               @          ,        @         D0�
BZ
F              0 @   I       D@D l       � @   P      B�A�A �A(�A0�DP�
0A�(A� A�A�B�Jo
0A�(A� A�A�B�K            � @          D0X         � @          D0X          @          D0O     ���� x �      <   P  P @   �       A�A�A �DP� A�A�A�         P    @   L       D@G <   P  P @   G       A�A�A �D@| A�A�A�       D   P  � @   �      A�A�A �A(�D��
(A� A�A�A�A $   P  p @   �       A�D0�A�,   P    @   4      A�D0}
A�E     D   P  @ @   _       A�A�A �A(�DPR(A� A�A�A�     \   P  �@ @   �      A�B�B �B(�B0�A8�A@�AH�	D�EP�
�A�A�B�B�B�B�A�F   ���� x �         P  � @          L   P  � @   %      A�A�A �D`p
 A�A�A�F�
 A�A�A�H    P  � @   /       D0j  L   P     @   �       A�A�A �D@R
 A�A�A�Dy
 A�A�A�D    P  �  @   F          P  0! @          d   P  @! @   �       B�B�B �A(�A0�A8�A@�D`z
@A�8A�0A�(A� B�B�B�F       l   P  " @   �       B�A�A �A(�A0�G�s
0A�(A� A�A�B�JL
0A�(A� A�A�B�F         P  �" @          4   P   # @   t       A�A�D@f
A�A�B    $   P  �# @   i       D@Y
CD      P  �# @          \   P   $ @   *      B�B�B �A(�A0�A8�A@�D�@A�8A�0A�(A� B�B�B�   P  0% @          l   P  @% @   :      B�B�B �B(�A0�A8�A@�AH�	G�Q
HA�@A�8A�0A�(B� B�B�B�H        ���� x �         �  �( @          D V     �  �( @             �  �( @             �  �( @             �  �( @             �  �( @             �  �( @             ���� x �      $   �   ) @   %       A�D0^A� $   �  0) @          A�D0SA� $   �  P) @   <       A�D0uA� $   �  �) @   F       A�D0A�    ���� x �         @  �* @   :       D0u  4   @  �* @   j       A�A�D@@
A�A�H       @  `+ @             ���� x �         �  �+ @             ���� x �      $   �  �+ @   /       D0R
JN    L   �  �+ @   �       A�A�D@e
A�A�Ct
A�A�JNA�A�       �  P, @             ���� x �      <   �	  `, @   �       A�A�D�P�
���
���A�A�B    ���� x �         �	  `- @             ���� x �      $   (
  p- @   i       A�A�DP   <   (
  �- @   b      A�A�A �Dp�
 A�A�A�D   \   (
  P/ @   ]      A�B�B �B(�B0�A8�A@�AH�	D�EPQ
�A�A�B�B�B�B�A�G     ���� x �           �2 @   >       D`y       �2 @             ���� x �      4   X   3 @   �      A�D0}
A�Mf
A�I     ���� x �      L   �  �4 @   p       B�A�A �A(�A0�DPY0A�(A� A�A�B�    <   �  05 @   o       A�A�A �D@U
 A�A�A�A    D   �  �5 @   �       A�A�D@R
A�A�FR
A�A�D      4   �  06 @   �       A�D0p
A�J�
A�A      ���� x �         �  07 @   ,          �  `7 @   P       L   �  �7 @   �       A�A�A �D@~
 A�A�A�HI A�A�A�       �  P8 @   �          �  �8 @   7          �  9 @   s          �  �9 @   6          �  �9 @   �          �  `: @   �          ���� x �         �
  p; @          D@Y     ���� x �         0  �; @   B       DP}     ���� x �         h  �; @   9       DPt     ���� x �      ,   �   < @   H       A�A�D`A�A�   ���� x �         �  p< @   2       DPm     ���� x �            �< @          L      �< @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    L      0= @   j       B�A�A �A(�A0�DPY0A�(A� A�A�B�    $      �= @          A�D0WA�       �= @                �= @   .       A�D0      > @   5       DPp        P> @   6       D0q        �> @   6       D0q                                                                                                                                                                                                                                                                                                                                                  Subsystem CheckSum SizeOfImage BaseOfCode SectionAlignment MinorSubsystemVersion DataDirectory SizeOfStackCommit ImageBase SizeOfCode MajorLinkerVersion SizeOfHeapReserve SizeOfInitializedData SizeOfStackReserve SizeOfHeapCommit MinorLinkerVersion __enative_startup_state SizeOfUninitializedData AddressOfEntryPoint MajorSubsystemVersion SizeOfHeaders MajorOperatingSystemVersion FileAlignment NumberOfRvaAndSizes ExceptionRecord DllCharacteristics MinorImageVersion MinorOperatingSystemVersion LoaderFlags Win32VersionValue MajorImageVersion lockdownd_client_private diagnostics_relay_client_private decoded_data oem_query idevice_private seDataOffset entry seDataSize result seDataOffset seDataSize __enative_startup_state hDllHandle lpreserved dwReason sSecInfo ExceptionRecord pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection _DoWildCard _StartInfo                                                                                                                                         C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crtexe.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include D:/a/msys64/ucrt64/include/psdk_inc C:/M/B/src/mingw-w64/mingw-w64-crt/include crtexe.c crtexe.c winnt.h intrin-impl.h corecrt.h minwindef.h basetsd.h stdlib.h errhandlingapi.h combaseapi.h wtypes.h ctype.h internal.h corecrt_startup.h math.h tchar.h string.h process.h synchapi.h <built-in> ideviceoem.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist ../include/libimobiledevice ./syscfg ideviceoem.c ideviceoem.c corecrt.h stdio.h stdint.h plist.h libimobiledevice.h lockdown.h diagnostics_relay.h string.h stdlib.h syscfg.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools syscfg/syscfg.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools syscfg C:/msys64/ucrt64/include C:/msys64/ucrt64/include/plist syscfg.c syscfg.c corecrt.h stdio.h stdint.h plist.h syscfg.h endian.h string.h stdlib.h syscfg_reader.h <built-in> C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools syscfg/endian.c C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools syscfg C:/msys64/ucrt64/include endian.c endian.c stdint.h endian.h syscfg/syscfg_reader.c C:\msys64\home\aaterali\build\libimobiledevice-phonecheck\tools C:/msys64/home/<USER>/build/libimobiledevice-phonecheck/tools syscfg C:/msys64/ucrt64/include syscfg_reader.c syscfg_reader.c stdint.h syscfg.h endian.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/gccmain.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include gccmain.c gccmain.c winnt.h combaseapi.h wtypes.h corecrt.h stdlib.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/natstart.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include natstart.c winnt.h combaseapi.h wtypes.h internal.h natstart.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/wildcard.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt wildcard.c wildcard.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/dllargv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt dllargv.c dllargv.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/_newmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt _newmode.c _newmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlssup.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlssup.c tlssup.c corecrt.h minwindef.h basetsd.h winnt.h corecrt_startup.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xncommod.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xncommod.c xncommod.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt cinitexe.c cinitexe.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/merr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include merr.c merr.c math.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/CRT_fp10.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt CRT_fp10.c CRT_fp10.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/mingw_helpers.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt mingw_helpers.c mingw_helpers.c C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc.c C:\M\B\src\build-UCRT64 C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pseudo-reloc.c pseudo-reloc.c vadefs.h corecrt.h minwindef.h basetsd.h winnt.h combaseapi.h wtypes.h stdio.h memoryapi.h errhandlingapi.h string.h stdlib.h <built-in> C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/usermatherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include usermatherr.c usermatherr.c math.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/xtxtmode.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt xtxtmode.c xtxtmode.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/crt_handler.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include crt_handler.c crt_handler.c winnt.h minwindef.h basetsd.h errhandlingapi.h combaseapi.h wtypes.h signal.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsthrd.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include tlsthrd.c tlsthrd.c corecrt.h minwindef.h basetsd.h winnt.h minwinbase.h synchapi.h stdlib.h processthreadsapi.h errhandlingapi.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/tlsmcrt.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt tlsmcrt.c tlsmcrt.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pseudo-reloc-list.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt pseudo-reloc-list.c pseudo-reloc-list.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/pesect.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include pesect.c pesect.c corecrt.h minwindef.h basetsd.h winnt.h string.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/mingw_matherr.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc mingw_matherr.c mingw_matherr.c C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_vfprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_vfprintf.c ucrt_vfprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_sprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_sprintf.c ucrt_sprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_snprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_snprintf.c ucrt_snprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_printf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_printf.c ucrt_printf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio/ucrt_fprintf.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/stdio D:/a/msys64/ucrt64/include ucrt_fprintf.c ucrt_fprintf.c vadefs.h corecrt.h stdio.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc/__initenv.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/misc D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include __initenv.c winnt.h combaseapi.h wtypes.h internal.h __initenv.c corecrt.h C:\M\B\src\build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt/ucrtbase_compat.c C:/M/B/src/build-UCRT64 C:/M/B/src/mingw-w64/mingw-w64-crt/crt D:/a/msys64/ucrt64/include C:/M/B/src/mingw-w64/mingw-w64-crt/include ucrtbase_compat.c ucrtbase_compat.c time.h vadefs.h corecrt.h stdlib.h winnt.h combaseapi.h wtypes.h internal.h corecrt_startup.h stdio.h                                                                                                                                                                                                                           �            ��R���R�        ��0���P��P��P     ��T��T          ��0���U��0���U��U ��0�  ��P  ��0�  ��T    ��
 � @   ���
 � @   �     ��S��S    ��\��\       ��0���s 3%���sx3%���0�       ��P��V��P   ��T  ��0�    ������P    ������P       RZP��P��P    ��p���p�  ��p� �                      �@ @    +R+�\���R���\���R���\���R�               �@ @    +Q+�U���Q���U���Q���U���Q�  �@ @   0�         C @    P��h���h���h         C @    P�S��S��S             *C @    P��`��P��R��P���`               7C @    P�_��R��P��P��_��_                         DC @    P�]��R��P��P��X��]��]��P��P��P��P                 QC @    P�U��R��P��P��U��U��U               ^C @    P�\��P��P��\��\��\               kC @    P�^��R��P��P��^��^                       xC @    P�T��R��P��P��P��T��X��T��T��T                                 �C @    P�����P��P����������P��P��P��P��P��P��P��P��P��P         �F @    PP,PMQP       �J @    P�S��S     �E @    P�_   �E @   i~       �E @    0�+| 2%�+4||2%�4i| 2%�     �E @    0�+\4i\     �F @    PR     �N @    PR     5O @    PO_   SO @   1~      H @    PR     BH @    PR     �H @    PR     �H @    PR     I @    PR     EI @    PR     �I @    PR     �I @    p���   J @   j|        J @    0�,~ 2%�,5~|2%�<j~ 2%�      J @    0�,^<j^   �B @    M
� @   ���
� @   �   �B @   P     �B @    P��P    �B @    P��P   �L @   P   �L @   P     @ @    "R"_�R�       @ @    "Q"RVR_�Q�     H @    0�*�R t "�*/	t �R2�  H @   0�     � @    3R3�
�R�      � @    ���sS��S        @    PUU��U      � @    +R+W�R���
�R�                            � @    0���0��0��0���@0���0��0��(` @   ���@WT��Q�P�V����0���0��0��0�����0���0��0��a @   �����0���0��0��0�����0���0��0��` @   �����0���0��0��0�����0���0��0��/` @   �����0���0��0��0�����0���0��0��?` @   �����0���0��0��0�����0���0���` @   ��0�����$0���0���` @   ���` @   �����0���0��0��0�����1���0��0��0�����1����` @   ��0��0�����$1����` @   ��0���` @   �����0���0��0��0�����1���0��0��0�����1����` @   ��0��0�����$1����` @   ��0���` @   �����0���0��0��0�����0���0��0��c` @   �����0���0��0��0�����1���0��0��0�����1���t` @   ��0��0�����$1���t` @   ��0��` @   �����0���0��0��0�����1���0��0��0�����1����` @   ��0��0�����	$1����` @   ��0���` @   ����	�	0���0��0��0����	�	1���0��0��0����	�	1����` @   ��0��0����	�$1����` @   ��0���` @   �����
0���0��0��0���                                                                                                                                                    � @    @0���0���P��S��0���P��S��P��T��0���P��S��0���P��S��P��T��P��U��P��T��P��U��P��T��P��U��P��T��P��U��P��T��P��U��P��T��P��U��P��T��P��U��P��T�	�	0��	�	P�	�	S�	�	P�	�	T�	�	P�	�
U�
�
P�
�
T�
�
P�
�
U�
�
P�
�
T�
�
P�
�
U�
�
P�
�T��P��U��P��T��P��U��P��T��P��U��P��T��P��U��P��T��
0�       P @    RNSN��R�       P @    Q�U���Q�       l @    PP{T       � @    PPYS       @    #R#L�R�       P @    RFUFGp       P @    QETEGp       P @    XDSDGp   j @   -P   u @   ��U  �   u @   ��H  � �            �
�
R�
��          �
�
Q�
�S���Q���S      ��0���^��^   ��P       ��T��
 ���T����������        �
�
0���0����^~   ���^~   ���^~     ��0� ����{������ ����{������ 	         �
�0���P��\��0���P��0� 
            �
�
0���0�����z��1�����z����z����z��1�       �
�
P�
�V��V   ��P  ��P     ��_����z����z     ��0���^��^ ��T ��\  ��P   �
�U��       ��P��
S�
�
P     ��P��
^     ��P��
U     ��P��
\     ��P��
V     ��P��
]     ��P��
T  �	�
R     ��R��	�R�         ��Q��	T�	�	�Q��	�	T         ��X��	S�	�	�X��	�	S    �	�	
@� @   ��	�	
@� @   �  �	�	
@� @   �         ��R��]���R���]           ��Q��^���Q���^���Q�         ��S��P��S��S      ��0���V��V  ��
`� @   �  ��V     ��R���R�             ��Q��V���Q���V���Q���V         ��X��S��S��S   ��P  ��Q    ��\��\           ��R��U���R���S��U         ��Q��T���Q���T                 ��R��U��S��S��S��P��S��P      ��0���0���1���0�      :R:��R�              BQBXSX[�Q�[�S���Q���S      ��T��T��P       BgQgxpl�x��R����52$#�       ��R���R���R       ��Q���Q���Q     �
�
R�
�
�R�     �
�
Q�
�
�Q� ^          ppRppr 8$�����࿀r 8%������!�pwTr 8$�����࿀r 8%������!@$������@r 8$�����࿀r 8%������!@%������?!�   ``R``r 8$�����࿀r 8&������!�`gTr 8$�����࿀r 8&������!@$������@r 8$�����࿀r 8&������!@&������?!�   PPRPUr 8$���xr 8&� � !�   @@R@Er 8$���xr 8%� � !� i               ��R��S��P       PZRZ�S��P       0:R:ISIJP        
R
$S$%P 1               RWP��P��R     W`R`gP              $R$/�R�      $Q$/�Q�      $X$/�X�         0sRs��R���R���R�         0sQs��Q���Q���Q�         0sXs��X���X���X�    `sRs��R�  `�2�    `sXs��X�    s�S��sx�    `g
P� @   �g�S �                XRX��R���R        ?�S��
�h @   ���
�h @   ���
�h @   ���
�h @   ���
&i @   � ~          ��P              ��Y��P��Y��Y��	Y�	�	Y�	�	Y�
�
Y                                         ��T��t ��|!���T��u �
����|!���T��u ��T��T��t  �!���T��u �� �!���T��T��t @L$!���T��u �����@L$!���	T�	�		u �
����	�	T�	�	u �������	�	T�	�	u ����	�	T�
�
0�                        ��P��U��U��P��} s ���} s #���U��	U�
�
s�����~ "��
�
s|�����~ "��
�
T�
�
U       ��S��st��
�
S           ��S��S��st���S��	S�
�
S         ��@���@���8���	 ��	�	@��	�	@��	�	 ��	�	8�     ��
�����	��������	�����     �� ����
�       ���	����	@K$�  ��2�  ����     ��U  ��2�  ����     ��U  ��1�  ����     ��U  ��1�  ����     ��U  �	�	4�  �	�	��     �	�	U  �	�	4�  �	�	��     �	�	U  �	�	8�  �	�	��     �	�	U  �	�	8�  �	�	��     �	�	U     �	�
S�
�
sx��
�
S   �
�
U  �
�
4�  �
�
�T�     �
�
T  �
�
4�  �
�
�T�     �
�
T     ��0���\             p�R��S���R���R���R���S            ��P��U��U��U��P��U      w�0���Y��0�   ��X      RiS n             @KRKL�R�        &R&8r 8>�R�      8Q8>�Q�      c>��w�      8d8>��w� [                       R�S��R���R���S���R���S                 \oP��P��P��P��P��P��P��P                o0���0���	����0���0���	����0���	����0���	����0���	����0�            P0�Pf1���0���0���0���0���1� �                                ��R���R���R���R���R���R���R���R���R���R���R���R�                         ��Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q���Q�                         ��X���X���X���X���X���X���X���X���X���X���X���X�      ��S��R��S   ��S         ��R��S���R���S     ��0���R��Q       ��R��P��R��R           p�R��U���R���R��U               p�Q���Q���Q��T��p���Q���T         ��P��S��P��S   !dS     ?@P@\T �            ��R��R    ��X��{<� $ &{ "�   ��P         ��P��{<� $ &{ "#���P��{<� $ &{ "#�     ��X��X  ��x�  ��P    ��X��{<� $ &{ "�      ��Q��q(���Q  ��0�         ��R���R���R���R�   ��R   ��Q     ��X��X  ��x�  ��R  ��X   ��Q  ��0�     ��R��R  ��r�     ��R��R  ��Q   ��P  ��0�     ��Q��Q  ��q�  ��P     ��P��P  ��p�         ��R���R���R���R�   ��R     ��X��X  ��x�  ��R    ��X��q<� $ &q "�   ��P  ��0�           ��R��T���R���T���R�  ��P   ��S  ��0�   ��P  ��p�      R,�R�     R,�R�       	R�R�,�R�     R,R  ,r�     07R7��R�     7ORO��R#<� $ &�R"�   EP   7X0�Xt:p �R#<� $ &�R"#�
���R#<� $ &�RH�w(�w� � T                RQ�R�        QX�Q�        X�`�X� B                3R3<Q<B�R�        .Q.<Y<B�Q�   =BP [                !R!3Q39�R�        Q3X39�Q�        X3Y39�X�   49P )                RFSFH�R�   AHP B                R,Q,2�R�        Q,X,2�Q�   -2P x              ��R��Q���R�       ��Q��X���Q�   ��P     ��R��S     ��R���R�       ��R��S���R�       ��R��U���R�       ��Q��T���Q�       ��X��S���X�       ��Y��V���Y�       /R/vUvz�R�       /Q/uTuz�Q�       /X/tStz�X�       /Y/wVwz�Y�                                                                                                   X         Z���� ���� ���� ���� ���� ���� ���� �         � @    W\e��
 �B @    M�� �B @    :�� �B @    �� �E @    
O �I @    
S P @   ��@ @   � N         ���� �	�	�	�	 �������� ������ ���� S         ������
 ����������	 ���� �	�	�
�
 �
�
�
�
          ���� �         	 + ���� ������ ���� ���� ������ ���� ������ ���� ������ ���� ������ ���� ������ ����                                                                                                                                                                                                                                                                                                                                                                                                            .file   a   ��  gcrtexe.c              �                                �              �   �                        �   �                        �   �                        %  `                        @  �                        `             k  �                        �  �                        �  @                        �  �                        �  0          �  �                    envp           argv            argc    (                          �                        '  �          9                           ^  0                        �             �  �                        �  �                        �                            �                    mainret            "  P                        8  @                        N  p                        d  `                        z  �          �  �      .l_endw �          �  �      .l_start�      .l_end        atexit        .text          $  A             .data                            .bss           ,                 .xdata         l   
             .pdata         T                    �                            �                             �      
   �&  �                 �         �                    �         �                   �         0                    �         \                              9                                                         �                    )  �     +                     4         P               .file   r   ��  gcygming-crtbeg        A  0                           V  @      .text   0                     .data                            .bss    0                        .xdata  l                       .pdata  T                          )  
     +                 .file   �   ��  gideviceoem.c          m  P                           z             �  P          �  �      node.0  8           �  (          �  p          �   
      device            �  0      err.26  �       service            �  @      main    �0      i.25    �       udid.24 �           �  �           	        len.22  �       len.21  �             �           +  �           ;  �           L  �           `  �           p  �           �  �           �  �       len.10  x       len.9   p       len.8   h       len.7   `       len.6   X       len.5   P       len.4   H       len.3   @           �         len.20  �       len.19  �       .text   P     O
  �             .data                            .bss    0                      .rdata         |               .xdata  t      P                 .pdata  l      T                    �  �0     �  V                �  �                           �  �                          �  �&  
   W?  P                �  �     C                    �  �     �  W                 �  0      @                    �  \      �                      9     5                          a                       �                         )  @
     +                     4  P                     .file   �   ��  gsyscfg.c          no_debug�                           �  �          �  x          �  �            �            �                        3  �          A  0          O  @      temp.1  `          _            t  �          �         result.0@          �  �          �  p          �  �          �             �  0          �  @      .text   �     �	  b             .data                          .bss    @     L                .xdata  �      �                 .pdata  �      �   -             .rdata  �     L                     �  �e  
   �  �                 �  �	     m                    �  f     �                   �  p      0                    �  �      R                       n     j                     �     %                       E     C                    )  p
     +                     4  P     p               .file     ��  gendian.c          endian  �                           �  �          �  �            �            �            �          )  �      .text   �     w                 .data                           .bss    �                       .xdata  |                       .pdata  �     T                    �  �~  
   l                   �  2     �                     �  �     b                    �  �      0                      �     �                      �     �                     )  �
     +                     4  �     �                .file   �  ��  gsyscfg_reader.        5                              G  0          X  P          l  �      .text         �                .data                           .bss    �                       .xdata  �                       .pdata  �     0                    �  ��  
   �                    �  �     	                    �  X     m                     �  �      0                      �      `  	                   �                            \     �                     )  �
     +                     4  �     �                .text   �      .idata$7�      .idata$5       .idata$4H      .idata$6�      .text   �      .idata$7�      .idata$5      .idata$4@      .idata$6�      .text   �      .idata$7�      .idata$5      .idata$48      .idata$6�      .text   �      .idata$7�      .idata$5      .idata$40      .idata$6�      .text          .idata$7�      .idata$5       .idata$4(      .idata$6|      .text         .idata$7�      .idata$5�      .idata$4       .idata$6`      .text         .idata$7�      .idata$5�      .idata$4      .idata$6P      .text         .idata$7�      .idata$5�      .idata$4      .idata$6(      .text          .idata$7�      .idata$5�      .idata$4      .idata$6�      .text   (      .idata$7�      .idata$5�      .idata$4       .idata$6�      .text   0      .idata$7�      .idata$5�      .idata$4�       .idata$6�      .text   8      .idata$7�      .idata$5�      .idata$4�       .idata$6�      .text   @      .idata$74      .idata$5�      .idata$4�      .idata$6�      .text   H      .idata$70      .idata$5�      .idata$4�      .idata$6�      .text   P      .idata$7,      .idata$5�      .idata$4�      .idata$6�      .text   X      .idata$7(      .idata$5x      .idata$4�      .idata$6l      .text   `      .idata$7$      .idata$5p      .idata$4�      .idata$6X      .text   h      .idata$7       .idata$5h      .idata$4�      .idata$6D      .text   p      .idata$7      .idata$5`      .idata$4�      .idata$60      .text   x      .idata$7      .idata$5X      .idata$4�      .idata$6      .text   �      .idata$7      .idata$5P      .idata$4x      .idata$6       .text   �      .idata$7      .idata$5H      .idata$4p      .idata$6�      .text   �      .idata$7      .idata$5@      .idata$4h      .idata$6�      .text   �      .idata$7      .idata$58      .idata$4`      .idata$6�      .text   �      .idata$7      .idata$50      .idata$4X      .idata$6�      .file   �  ��  ggccmain.c             �  �                       p.0                �  �          �  P                    __main  `          �  �      .text   �     �                .data                         .bss    �                      .xdata  �                      .pdata       $   	                 �  �  
   a                   �  �     ?                    �  �     5                     �        0                      �!     '                     N     �                     )        +                     4  @     �                .file   �  ��  gnatstart.c        .text   �                       .data                           .bss    �                          �  L�  
     
                 �  >     �                     �  0                            ##     V   
                   �                            6                         )  0     +                 .file     ��  gwildcard.c        .text   �                       .data   0                       .bss    �                           �  N�  
   �                    �  �     .                     �  P                            y#     :                      D     �                     )  `     +                 .file   (  ��  gdllargv.c         _setargv�                       .text   �                      .data   @                        .bss    �                       .xdata  �                      .pdata  (                         �  ۓ  
   �                   �  "     :                     �  p     0                      �#     V                      �     �                     )  �     +                     4  �     0                .file   <  ��  g_newmode.c        .text   �                       .data   @                        .bss    �                          �  \�  
   �                    �  \     .                     �  �                            	$     :                      	     �                     )  �     +                 .file   t  ��  gtlssup.c              �  �                           �  �          �  0                    __xd_a  P       __xd_z  X             P      .text   �     �                .data   @                        .bss    �                      .xdata  �                      .pdata  4     $   	             .CRT$XLD@                      .CRT$XLC8                      .rdata  �     H                .CRT$XDZX                       .CRT$XDAP                       .CRT$XLZH                       .CRT$XLA0                       .tls$ZZZ   	                    .tls        	                        �  �  
   �  6                 �  �     �                    �  �                        �  �     0                      C$                          �                            
     �                     )  �     +                     4  �     �                .file   �  ��  gxncommod.c        .text   `                       .data   @                        .bss    �                          �  ��  
   �                    �  q     .                     �  �                            Y%     :                           �                     )        +                 .file   �  ��  gcinitexe.c        .text   `                       .data   @                        .bss    �                       .CRT$XCZ                       .CRT$XCA                        .CRT$XIZ(                       .CRT$XIA                           �  7�  
   {                   �  �     a                     �                              �%     :                      �     �                     )  P     +                 .file   �  ��  gmerr.c            _matherr`                       .text   `     �                .data   @                        .bss    �                       .rdata  @     @               .xdata  �                      .pdata  X                         �  ��  
   6  
                 �                            �       �                    �  0     0                      �%     �                      J     �                     )  �     +                     4  �	     X                .file   �  ��  gCRT_fp10.c        _fpreset`                       fpreset `      .text   `                      .data   @                        .bss    �                       .xdata                        .pdata  d                         �  �  
   �                    �       -                     �  `     0                      �&     X                      
     �                     )  �     +                     4  �	     0                .file   �  ��  gmingw_helpers.    .text   p                       .data   @                        .bss    �                          �  �  
   �                    �  2     .                     �  �                            �&     :                      �
     �                     )  �     +                 .file   "  ��  gpseudo-reloc.c          p                             �          4        the_secs          @  P          Z             e  p                        �  �                    .text   p     =  &             .data   @                        .bss                           .rdata  �	     [                .xdata       0                 .pdata  p     $   	                 �  �  
   K  �                 �  `     �                    �  �     �  
                 �  �     0                    �  <     W                       '     �                     �     	                       T     O                    )       +                     4  (
     �                .file   B  ��  gusermatherr.c         �  �"                           �            �  �"      .text   �"     L                .data   @                        .bss                          .xdata  <                      .pdata  �                         �  \�  
   �                   �  8                         �  "     r                     �  �     0                      �,     �                      �     �                     )  @     +                     4       P                .file   V  ��  gxtxtmode.c        .text    #                       .data   @                        .bss                               �  Q�  
   �                    �  J     .                     �                              K-     :                      m     �                     )  p     +                 .file   x  ��  gcrt_handler.c         �   #                       .text    #     �               .data   @                        .bss    0                      .xdata  H                      .rdata  �
     (   
             .pdata  �                         �  پ  
   �                   �  x     ~                    �  �"     _                    �  0     0                      �-     �  
                   �                                                     )  �     +                     4  X     P                .file   �  ��  gtlsthrd.c             	  �$                           5	  `          C	  @          Q	  0%          n	  H          �	  �%          �	  0&      .text   �$     b  "             .data   @                        .bss    @     H                 .xdata  P     0                 .pdata  �     0                    �  ��  
   �
  A                 �  �     a                    �  �#     �                    �  `     0                    �  �                            /     x                          %                    )  �     +                     4  �     (               .file   �  ��  gtlsmcrt.c         .text   0'                       .data   @                       .bss    �                           �  ��  
   �                    �  W      .                     �  �                            �1     :                      B     �                     )        +                 .file   �  ��  g    �	            .text   0'                       .data   P                        .bss    �                          �  �  
   �                    �  �      0                     �  �                            �1     :                      �     �                     )  0     +                 .file   �  ��  gpesect.c              �	  0'                           �	  `'          �	  �'           
  P(          
  �(          5
  )          H
  �)          X
  �)          u
  `*      .text   0'     �  	             .data   P                        .bss    �                       .xdata  �     ,                 .pdata  �     l                    �  ��  
   �  �                 �  �      �                    �  �&     �                    �  �     0                    �  �     �                       �1     K                     
     T                       �     �                     )  `     +                     4  �     (               .text   0+     2                 .data   P                        .bss    �                       .text   p+                       .data   P                        .bss    �                           )  �     +                 .file     ��  gmingw_matherr.    .text   p+                       .data   P                       .bss    �                           �  ��  
   �                    �  ?#     .                     �                               I7     :                      }     �                     )  �     +                 .file   0  ��  gucrt_vfprintf.    vfprintfp+                       .text   p+                     .data   `                      .bss    �                       .xdata  �                      .pdata  T                         �  V�  
   �                   �  m#     8                    �  �*     X                     �        0                      �7     r   	                   -     �                     )  �     +                     4  �
     8                .file   N  ��  gucrt_sprintf.c    sprintf �+                       .text   �+     B                .data   p                      .bss    �                       .xdata  �                      .pdata  `                         �  ��  
   �                   �  �$     9                    �  
+     F                     �  P     0                      �7     �   	                        �                     )        +                     4  0     8                .file   l  ��  gucrt_snprintf.    snprintf�+                       .text   �+     9                .data   �                      .bss    �                       .xdata  �                      .pdata  l                         �  z�  
   �                   �  �%     9                    �  P+     _                     �  �     0                      �8     �   	                   �     �                     )  P     +                     4  h     8                .file   �  ��  gucrt_printf.c     printf   ,                       .text    ,     H                .data   �                      .bss    �                       .xdata  �                      .pdata  x                         �  �  
   �  
                 �  '     l                    �  �+     -                     �  �     0                      9     �   	                   �     �                     )  �     +                     4  �     H                .file   �  ��  gucrt_fprintf.c    fprintf p,                       .text   p,     2                .data   �                      .bss    �                       .xdata  �                      .pdata  �                         �  ��  
   �                   �  �(     b                    �  �+     F                     �  �     0                      �9     �   	                   �     �                     )  �     +                     4  �     8                .file   �  ��  g__initenv.c           �
  �          �
  �      .text   �,                       .data   �                      .bss    �                          �  t 
   �                   �  �)     �                     �                              %:     [                      �                         )  �     +                 .file   
  ��  gucrtbase_compa        �
  �,                           �
  �,          �
  0-      _onexit �-          �
  �-          �
                             �-          $  .      tzset   P.          2  �                    _tzset  �.          N            _            p            �  $          �         .text   �,       "             .data   �      x   
             .bss    �                       .xdata  �     P                 .pdata  �     l                .rdata                            �   
   �  Y                 �  �*                          �  ",     |                    �  0     0                      �:     �                     a                            �     `                    )       +                     4        �               .text   �.      .data   @      .bss    �      .idata$7�      .idata$5      .idata$40      .idata$6h      .text   �.      .data   @      .bss    �      .idata$7�      .idata$5      .idata$48      .idata$6v      .text   �.      .data   @      .bss    �      .idata$7�      .idata$5      .idata$4@      .idata$6�      .text   �.      .data   @      .bss    �      .idata$7�      .idata$5       .idata$4H      .idata$6�      .file     ��  gfake              hname   0      fthunk        .text   �.                       .data   @                       .bss    �                       .idata$2�                      .idata$40      .idata$5      .file   >  ��  gfake              .text   �.                       .data   @                       .bss    �                       .idata$4P                      .idata$5(                      .idata$7�                      .text   �.      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6J      .text   �.      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4      .idata$6T      .text    /      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4       .idata$6^      .file   L  ��  gfake              hname         fthunk  �      .text   /                       .data   @                       .bss    �                       .idata$2�                      .idata$4      .idata$5�      .file   �  ��  gfake              .text   /                       .data   @                       .bss    �                       .idata$4(                      .idata$5                       .idata$7�     !                 .text   /      .data   @      .bss    �      .idata$7\      .idata$5�      .idata$4�      .idata$6�
      .text   /      .data   @      .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6�
      .text    /      .data   @      .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6�
      .text   (/      .data   @      .bss    �      .idata$7h      .idata$5�      .idata$4�      .idata$6�
      .text   0/      .data   @      .bss    �      .idata$7l      .idata$5�      .idata$4�      .idata$6�
      .text   8/      .data   @      .bss    �      .idata$7p      .idata$5�      .idata$4�      .idata$6
      .text   @/      .data   @      .bss    �      .idata$7t      .idata$5�      .idata$4�      .idata$6$      .text   H/      .data   @      .bss    �      .idata$7x      .idata$5�      .idata$4�      .idata$6.      .text   P/      .data   @      .bss    �      .idata$7|      .idata$5�      .idata$4�      .idata$68      .text   X/      .data   @      .bss    �      .idata$7�      .idata$5�      .idata$4       .idata$6B      .file   �  ��  gfake              hname   �      fthunk  �      .text   `/                       .data   @                       .bss    �                       .idata$2�                      .idata$4�      .idata$5�      .file   3  ��  gfake              .text   `/                       .data   @                       .bss    �                       .idata$4                      .idata$5�                      .idata$7�                       .text   `/      .data   @      .bss    �      .idata$7�
      .idata$5       .idata$4(      .idata$6x	      .text   h/      .data   @      .bss    �      .idata$7�
      .idata$5      .idata$40      .idata$6�	      .text   p/      .data   @      .bss    �      .idata$7�
      .idata$5      .idata$48      .idata$6�	      .text   x/      .data   @      .bss    �      .idata$7       .idata$5      .idata$4@      .idata$6�	      .text   �/      .data   @      .bss    �      .idata$7      .idata$5       .idata$4H      .idata$6�	      .text   �/      .data   @      .bss    �      .idata$7      .idata$5(      .idata$4P      .idata$6�	      .text   �/      .data   @      .bss    �      .idata$7      .idata$50      .idata$4X      .idata$6�	      .text   �/      .data   @      .bss    �      .idata$7      .idata$58      .idata$4`      .idata$6�	      .text   �/      .data   @      .bss    �      .idata$7      .idata$5@      .idata$4h      .idata$6
      .text   �/      .data   @      .bss    �      .idata$7      .idata$5H      .idata$4p      .idata$6

      .text   �/      .data   @      .bss    �      .idata$7      .idata$5P      .idata$4x      .idata$6,
      .text   �/      .data   @      .bss    �      .idata$7       .idata$5X      .idata$4�      .idata$6L
      .text   �/      .data   @      .bss    �      .idata$7$      .idata$5`      .idata$4�      .idata$6X
      .text   �/      .data   @      .bss    �      .idata$7(      .idata$5h      .idata$4�      .idata$6h
      .text   �/      .data   @      .bss    �      .idata$7,      .idata$5p      .idata$4�      .idata$6�
      .text   �/      .data   @      .bss    �      .idata$70      .idata$5x      .idata$4�      .idata$6�
      .text   �/      .data   @      .bss    �      .idata$74      .idata$5�      .idata$4�      .idata$6�
      .file   A  ��  gfake              hname   (      fthunk         .text   �/                       .data   @                       .bss    �                       .idata$2x                      .idata$4(      .idata$5       .file   k  ��  gfake              .text   �/                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$78     "                 .text   �/      .data   @      .bss    �      .idata$7�
      .idata$5�      .idata$4       .idata$6B	      .text   �/      .data   @      .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6Z	      .text    0      .data   @      .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6d	      .text   0      .data   @      .bss    �      .idata$7�
      .idata$5�      .idata$4      .idata$6n	      .file   y  ��  gfake              hname          fthunk  �      .text   0                       .data   @                       .bss    �                       .idata$2d                      .idata$4       .idata$5�      .file   �  ��  gfake              .text   0                       .data   @                       .bss    �                       .idata$4                       .idata$5�                      .idata$7�
     "                 .text   0      .data   @      .bss    �      .idata$7�
      .idata$5�      .idata$4�      .idata$6.	      .file   �  ��  gfake              hname   �      fthunk  �      .text    0                       .data   @                       .bss    �                       .idata$2P                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text    0                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7�
                      .text    0      .data   @      .bss    �      .idata$7l
      .idata$5�      .idata$4�      .idata$6	      .text   (0      .data   @      .bss    �      .idata$7p
      .idata$5�      .idata$4�      .idata$6	      .text   00      .data   @      .bss    �      .idata$7t
      .idata$5�      .idata$4�      .idata$6	      .text   80      .data   @      .bss    �      .idata$7x
      .idata$5�      .idata$4�      .idata$6$	      .file   �  ��  gfake              hname   �      fthunk  �      .text   @0                       .data   @                       .bss    �                       .idata$2<                      .idata$4�      .idata$5�      .file   �  ��  gfake              .text   @0                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7|
                      .text   @0      .data   @      .bss    �      .idata$7<
      .idata$5�      .idata$4�      .idata$6�      .text   H0      .data   @      .bss    �      .idata$7@
      .idata$5�      .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk  �      .text   P0                       .data   @                       .bss    �                       .idata$2(                      .idata$4�      .idata$5�      .file   R  ��  gfake              .text   P0                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7D
     &                 .text   P0      .data   @      .bss    �      .idata$7(
      .idata$5x      .idata$4�      .idata$6�      .text   X0      .data   @      .bss    �      .idata$7$
      .idata$5p      .idata$4�      .idata$6�      .text   `0      .data   @      .bss    �      .idata$7 
      .idata$5h      .idata$4�      .idata$6�      .text   h0      .data   @      .bss    �      .idata$7
      .idata$5`      .idata$4�      .idata$6�      .text   p0      .data   @      .bss    �      .idata$7
      .idata$5X      .idata$4�      .idata$6�      .text   x0      .data   @      .bss    �      .idata$7
      .idata$5P      .idata$4x      .idata$6t      .text   �0      .data   @      .bss    �      .idata$7
      .idata$5H      .idata$4p      .idata$6X      .text   �0      .data   @      .bss    �      .idata$7
      .idata$5@      .idata$4h      .idata$6H      .text   �0      .data   @      .bss    �      .idata$7
      .idata$58      .idata$4`      .idata$60      .text   �0      .data   @      .bss    �      .idata$7
      .idata$50      .idata$4X      .idata$6      .file   `  ��  gfake              hname   X      fthunk  0      .text   �0                       .data   @                       .bss    �                       .idata$2                      .idata$4X      .idata$50      .file   n  ��  gfake              .text   �0                       .data   @                       .bss    �                       .idata$4�                      .idata$5�                      .idata$7,
     
                 .file   �  ��  gcygming-crtend        �  �?                       .text   �0                       .data   @                       .bss    �                           �  �?                         �  (                          �  �                         �  �?                         )  @     +                 .idata$2        .idata$5�      .idata$4�       .idata$2�       .idata$50      .idata$4X      .idata$4P      .idata$5(      .idata$7�      .idata$4�      .idata$5�      .idata$78      .rsrc       
    __xc_z         putchar P/          �  X          �  p          �  �.            X            0/          .  p          :  ,
          V  �          i              x  �?          �  �           �  8          �  �          �  �          �  d           
  p0          ,
  �          N
  �          f
  �          s
  �          �
             �
             �
      	        �
  0          �
  �/          �
  �       __xl_a  0           �
  �0            P          +  p          ?            g  �      _cexit  x/          �  H          �  `  ��       �     ��       �  �          �                �                ��       8     ��       T  0           f  0          �  h      __xl_d  @           �  h          �  p      _tls_end   	        �  P          �  0      __tzname�.             P0          
                         0  �           A  0           Q             o  �          �      	    memcpy  �/          �            �  `          �  �      puts    X/          �  �          �  P            0          %  �      malloc  80      _CRT_MT @           G  `0          S  �          `              n  �          �  �          �  p          �            �     ��       �  �          �  �          
  @             �          4  H          Y  �          s  �          ~  (          �  (          �  �
          �  @0          �  �            �            P      fflush  @/          3  X0          B  (           u             �  P           �  P          �  �/          �        abort   �/          �  p            �           #  P           3         __dll__     ��       Q      ��       f  h/          q  �          �  �          �  �0          �  P           �  �          �   0            `            x          /  0          ?  P           k  @          w     ��       �  $       strrchr  0          �  �
      calloc  (0          �             �  8/          �  �            �           0  �          =  p          [  �          �  �          �  H          �  h      Sleep   h0          �  �      _commode�          �  `          �  �          �  @            �            �?          "             K  �          _  <           �  �           �             �  �          �  �      __xi_z  (           �               �                        &             6  �          W  �      strstr  0          z   /          �             �  8          �  �      signal  �/          �  /          �  �          �  �                          �      strncmp  /          +  �?          :  �           I  �          i  �          v  �          �  �           �  p          �  x          �  �                ��         �          )  `          =            g  8          �  0          �            �  �          �  �          �  �             �             �          9            J     ��       ]  H          }  H0          �  �/          �  �/          �  �0          �  x          �  �                         .  (          H  `           W     ��       l              {  �           �            �  8          �  �          �  �           �  �                  __xl_z  H       __end__                �          3  X          U  0      strcmp  �.          c  �?      __xi_a             q  �/          �              �  `          �  x0      __xc_a              �  �          �     ��       �  P           �     ��         �      _fmode             !  h          3  �          G            `  0          q            �  �          �  �           �  �/          �             �  �          �  8          �  D
          !  `/          ,  p          A        __xl_c  8           N     	        [  �          p  x          �  X          �  �          �  �          �  0           �  �             �          .   �.          9   @          S   |
          �   0       _newmode�          �   �/      fwrite  H/          �   �          �   �          �   �          �       ��       �       ��       �   0+          !  �          !  /          (!  p      exit    �/          :!     ��       V!  �          c!      ��       {!  `          �!  p           �!  0      _exit   �/          �!  �           �!  @          �!  �/      strlen  �.          �!  8          "  p          "  �/          #"  @          H"  �0          ^"  �/          {"             �"             �"  x           �"  p          #  �           #  �          ,#  P          ;#  �           T#  P           d#  (/          |#  �           �#  p/      free    00          �#  @          �#  �      �#  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str .debug_loclists .debug_rnglists __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase .rdata$.refptr.__mingw_app_type managedapp .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode startinfo .rdata$.refptr._dowildcard __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state has_cctor .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a WinMainCRTStartup .l_startw mainCRTStartup .CRT$XCAA .CRT$XIAA .debug_info .debug_abbrev .debug_loclists .debug_aranges .debug_rnglists .debug_line .debug_str .debug_line_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame verifyRecord print_xml oem_query_create.constprop.0 oem_query diagnostics_client print_usage.isra.0 StartService lockdown_client string2hexString syscfg.23 verificationReport plFactorySN.18 plFactoryMLB.17 plFactoryMESA.16 plFactoryTouchID.15 plFactoryBAT.14 plFactoryRCAM.13 plFactoryFCAM.12 plFactoryLCD.11 syscfgData .text.startup .xdata.startup .pdata.startup syscfgFindByIndex.part.0 syscfgDataLength syscfg_reinit syscfgKeyCount syscfgInitWithData syscfgGetData syscfgGetSize syscfgFindByTag syscfgCopyDataForTag syscfgFindByIndex syscfg_find_tag syscfgPlist syscfgRootPlist syscfgPlistCopy plist_syscfgHeader plist_syscfgNode do_syscfg swap_uint16 swap_int16 swap_uint32 swap_int32 swap_int64 swap_uint64 swap_syscfgHeader swap_syscfgEntry swap_syscfgMemEntry swap_syscfgEntryCNTB __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error mark_section_writable maxSections _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_raise_matherr stUserMathErr __mingw_setusermatherr _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names local__winitenv local__initenv _get_output_format __getmainargs __wgetmainargs at_quick_exit .rdata$.refptr.__mingw_module_is_dll _amsg_exit __ms_fwprintf .rdata$.refptr.__imp__tzset initial_daylight initial_timezone initial_tznames initial_tzname0 initial_tzname1 register_frame_ctor .ctors.65535 __imp_plist_get_string_val ___RUNTIME_PSEUDO_RELOC_LIST__ __daylight plist_new_dict __stdio_common_vfwprintf __imp_abort __lib64_libkernel32_a_iname __imp___p__environ __data_start__ ___DTOR_LIST__ __imp_timezone libplist_2_0_dll_iname __imp_idevice_new_with_options __imp_plist_new_uint _head_lib64_libapi_ms_win_crt_private_l1_1_0_a SetUnhandledExceptionFilter .refptr.__mingw_initltsdrot_force plist_array_append_item __imp_calloc __imp___p__fmode __imp___p___argc __imp_tzname ___tls_start__ .refptr.__native_startup_state _set_invalid_parameter_handler __imp_tzset GetLastError __imp__initialize_wide_environment __rt_psrelocs_start __imp_lockdownd_service_descriptor_free __imp_diagnostics_relay_goodbye plist_new_uint __dll_characteristics__ __size_of_stack_commit__ __lib64_libapi_ms_win_crt_time_l1_1_0_a_iname __mingw_module_is_dll __imp_idevice_free __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp_plist_new_bool __imp__set_invalid_parameter_handler plist_new_array .refptr.__CTOR_LIST__ __imp_plist_array_append_item VirtualQuery __imp___p___argv ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ .refptr.__mingw_module_is_dll .refptr.__imp___initenv _tls_start __imp_lockdownd_client_free __imp_plist_new_array .refptr._matherr .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ plist_new_string __mingw_oldexcpt_handler lockdownd_service_descriptor_free TlsGetValue __imp_strcmp __bss_start__ __imp___C_specific_handler __imp_putchar ___RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___tzname __size_of_heap_commit__ __imp___stdio_common_vfprintf __imp_strrchr __imp_GetLastError .refptr._dowildcard __imp__initialize_narrow_environment __mingw_initltsdrot_force __imp_free __imp__configure_wide_argv __imp_at_quick_exit __lib64_libapi_ms_win_crt_math_l1_1_0_a_iname __p__environ .refptr.__mingw_app_type __mingw_initltssuo_force __imp_plist_get_data_val VirtualProtect _head_lib64_libapi_ms_win_crt_environment_l1_1_0_a __imp__tzset ___crt_xp_start__ __imp_LeaveCriticalSection __C_specific_handler .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___ms_fwprintf ___crt_xp_end__ __imp_lockdownd_start_service __minor_os_version__ __p___argv libimobiledevice_1_0_dll_iname __lib64_libapi_ms_win_crt_string_l1_1_0_a_iname EnterCriticalSection _MINGW_INSTALL_DEBUG_MATHERR __imp_puts _set_new_mode .refptr.__xi_a __imp_plist_new_dict .refptr._CRT_MT _head_lib64_libapi_ms_win_crt_math_l1_1_0_a __imp__exit __section_alignment__ __native_dllmain_reason __lib64_libapi_ms_win_crt_private_l1_1_0_a_iname _tls_used __stdio_common_vsprintf __IAT_end__ _head_lib64_libapi_ms_win_crt_time_l1_1_0_a __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ __imp_diagnostics_relay_query_mobilegestalt lockdownd_start_service __imp_plist_get_data_ptr plist_new_bool .refptr._newmode plist_new_data plist_get_data_ptr __data_end__ __imp_fwrite __CTOR_LIST__ diagnostics_relay_query_ioregistry_entry __imp__set_new_mode _head_lib64_libapi_ms_win_crt_heap_l1_1_0_a __imp___getmainargs _head_lib64_libkernel32_a __imp_diagnostics_relay_client_free __bss_end__ idevice_set_debug_level __imp_strstr __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force __imp_diagnostics_relay_client_new __p__fmode .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __acrt_iob_func __native_startup_state __imp_plist_to_xml ___crt_xc_start__ lockdownd_client_free ___CTOR_LIST__ __imp_snprintf .refptr.__dyn_tls_init_callback __imp_signal plist_get_data_val _head_lib64_libapi_ms_win_crt_string_l1_1_0_a __imp_plist_new_data plist_get_string_val .refptr.__mingw_initltsdyn_force __rt_psrelocs_size plist_dict_set_item .refptr.__ImageBase __imp_lockdownd_client_new_with_handshake __lib64_libapi_ms_win_crt_runtime_l1_1_0_a_iname diagnostics_relay_client_new __imp___p___wargv __imp_strlen __imp_malloc .refptr._gnu_exception_handler __imp___wgetmainargs lockdownd_client_new_with_handshake __imp___daylight __file_alignment__ __imp_InitializeCriticalSection __p__wenviron _initialize_narrow_environment _crt_at_quick_exit InitializeCriticalSection __imp_exit _head_lib64_libapi_ms_win_crt_stdio_l1_1_0_a _head_libimobiledevice_1_0_dll diagnostics_relay_goodbye __imp_vfprintf __major_os_version__ __mingw_pcinit __imp___initenv diagnostics_relay_query_mobilegestalt __imp_plist_dict_get_item __imp_plist_new_string _head_libplist_2_0_dll __IAT_start__ __imp__cexit __imp___stdio_common_vfwprintf __imp_SetUnhandledExceptionFilter __imp__onexit __DTOR_LIST__ __set_app_type syscfg_debug_func __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ __imp___stdio_common_vsprintf __imp_TlsGetValue __imp___p__wenviron idevice_new_with_options __setusermatherr __imp___timezone .refptr._commode __imp_fprintf _configure_wide_argv __mingw_pcppinit __imp___p__commode __imp__crt_atexit __lib64_libapi_ms_win_crt_environment_l1_1_0_a_iname __p___argc __imp_VirtualProtect idevice_free ___tls_end__ .refptr.__imp__tzset __imp_VirtualQuery __imp__initterm __imp_diagnostics_relay_query_ioregistry_entry __mingw_initltsdyn_force _dowildcard __lib64_libapi_ms_win_crt_stdio_l1_1_0_a_iname __dyn_tls_init_callback __timezone __imp_plist_dict_set_item __lib64_libapi_ms_win_crt_heap_l1_1_0_a_iname Dont_Compare_LCD _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ ___chkstk_ms __native_startup_lock __p__commode __rt_psrelocs_end __minor_subsystem_version__ __imp_fflush __minor_image_version__ __imp___set_app_type __imp_sprintf __imp__crt_at_quick_exit __imp_printf .refptr.__xc_a _configure_narrow_argv diagnostics_relay_client_free .refptr.__xi_z _crt_atexit .refptr._MINGW_INSTALL_DEBUG_MATHERR DeleteCriticalSection _initialize_wide_environment __imp_idevice_set_debug_level __imp__configure_narrow_argv _head_lib64_libapi_ms_win_crt_runtime_l1_1_0_a __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp___winitenv plist_dict_get_item .refptr.__xc_z __imp__get_output_format ___crt_xt_end__ __stdio_common_vfprintf __imp_daylight __p___wargv plist_to_xml __mingw_app_type 