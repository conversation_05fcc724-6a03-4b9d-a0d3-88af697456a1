/* Class = "NSMenu"; title = "Find"; ObjectID = "1b7-l0-nxx"; */
"1b7-l0-nxx.title" = "Găsește";

/* Class = "NSMenuItem"; title = "Lower"; ObjectID = "1tx-W0-xDw"; */
"1tx-W0-xDw.title" = "Mai jos";

/* Class = "NSMenuItem"; title = "Customize Toolbar…"; ObjectID = "1UK-8n-QPP"; */
"1UK-8n-QPP.title" = "Personalizare bară de instrumente…";

/* Class = "NSMenuItem"; title = "MobileDeviceUpdater"; ObjectID = "1Xt-HY-uBw"; */
"1Xt-HY-uBw.title" = "MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Raise"; ObjectID = "2h7-ER-AoG"; */
"2h7-ER-AoG.title" = "Mai sus";

/* Class = "NSMenuItem"; title = "Transformations"; ObjectID = "2oI-Rn-ZJC"; */
"2oI-Rn-ZJC.title" = "Transformări";

/* Class = "NSMenu"; title = "Spelling"; ObjectID = "3IN-sU-3Bg"; */
"3IN-sU-3Bg.title" = "Ortografie";

/* Class = "NSMenuItem"; title = "Use Default"; ObjectID = "3Om-Ey-2VK"; */
"3Om-Ey-2VK.title" = "Utilizează valori implicite";

/* Class = "NSMenu"; title = "Speech"; ObjectID = "3rS-ZA-NoH"; */
"3rS-ZA-NoH.title" = "Enunțare";

/* Class = "NSMenuItem"; title = "Find"; ObjectID = "4EN-yA-p0u"; */
"4EN-yA-p0u.title" = "Găsește";

/* Class = "NSMenuItem"; title = "Enter Full Screen"; ObjectID = "4J7-dP-txa"; */
"4J7-dP-txa.title" = "Afișează pe tot ecranul";

/* Class = "NSMenuItem"; title = "Quit MobileDeviceUpdater"; ObjectID = "4sb-4s-VLi"; */
"4sb-4s-VLi.title" = "Termină MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "About MobileDeviceUpdater"; ObjectID = "5kV-Vb-QxS"; */
"5kV-Vb-QxS.title" = "Despre MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Edit"; ObjectID = "5QF-Oa-p0T"; */
"5QF-Oa-p0T.title" = "Editare";

/* Class = "NSMenuItem"; title = "Copy Style"; ObjectID = "5Vv-lz-BsD"; */
"5Vv-lz-BsD.title" = "Copiază stilul";

/* Class = "NSMenuItem"; title = "Redo"; ObjectID = "6dh-zS-Vam"; */
"6dh-zS-Vam.title" = "Refă";

/* Class = "NSMenu"; title = "Writing Direction"; ObjectID = "8mr-sm-Yjd"; */
"8mr-sm-Yjd.title" = "Sens de scriere";

/* Class = "NSMenuItem"; title = "Substitutions"; ObjectID = "9ic-FL-obx"; */
"9ic-FL-obx.title" = "Înlocuiri";

/* Class = "NSMenuItem"; title = "Smart Copy/Paste"; ObjectID = "9yt-4B-nSM"; */
"9yt-4B-nSM.title" = "Copiere/lipire inteligentă";

/* Class = "NSMenuItem"; title = "Tighten"; ObjectID = "46P-cB-AYj"; */
"46P-cB-AYj.title" = "Mai strâns";

/* Class = "NSMenuItem"; title = "Correct Spelling Automatically"; ObjectID = "78Y-hA-62v"; */
"78Y-hA-62v.title" = "Corectează ortografia automat";

/* Class = "NSMenuItem"; title = "Use Default"; ObjectID = "agt-UL-0e3"; */
"agt-UL-0e3.title" = "Utilizează valori implicite";

/* Class = "NSMenuItem"; title = "Print…"; ObjectID = "aTl-1u-JFS"; */
"aTl-1u-JFS.title" = "Tipărește…";

/* Class = "NSMenuItem"; title = "Window"; ObjectID = "aUF-d1-5bR"; */
"aUF-d1-5bR.title" = "Fereastră";

/* Class = "NSMenu"; title = "Font"; ObjectID = "aXa-aM-Jaq"; */
"aXa-aM-Jaq.title" = "Font";

/* Class = "NSMenu"; title = "Main Menu"; ObjectID = "AYu-sK-qS6"; */
"AYu-sK-qS6.title" = "Meniu principal";

/* Class = "NSMenuItem"; title = "\tLeft to Right"; ObjectID = "BgM-ve-c93"; */
"BgM-ve-c93.title" = "\tDe la stânga la dreapta";

/* Class = "NSMenuItem"; title = "Show Colors"; ObjectID = "bgn-CT-cEk"; */
"bgn-CT-cEk.title" = "Afișează culori";

/* Class = "NSMenu"; title = "File"; ObjectID = "bib-Uj-vzu"; */
"bib-Uj-vzu.title" = "Fișier";

/* Class = "NSMenuItem"; title = "Preferences…"; ObjectID = "BOF-NM-1cW"; */
"BOF-NM-1cW.title" = "Preferințe…";

/* Class = "NSMenuItem"; title = "Use Selection for Find"; ObjectID = "buJ-ug-pKt"; */
"buJ-ug-pKt.title" = "Utilizează selecția pentru găsire";

/* Class = "NSMenuItem"; title = "Save As…"; ObjectID = "Bw7-FT-i3A"; */
"Bw7-FT-i3A.title" = "Salvează ca…";

/* Class = "NSMenu"; title = "Transformations"; ObjectID = "c8a-y6-VQd"; */
"c8a-y6-VQd.title" = "Transformări";

/* Class = "NSMenuItem"; title = "Use None"; ObjectID = "cDB-IK-hbR"; */
"cDB-IK-hbR.title" = "Nu utiliza nimic";

/* Class = "NSMenuItem"; title = "Selection"; ObjectID = "cqv-fj-IhA"; */
"cqv-fj-IhA.title" = "Selecție";

/* Class = "NSMenuItem"; title = "Smart Links"; ObjectID = "cwL-P1-jid"; */
"cwL-P1-jid.title" = "Linkuri inteligente";

/* Class = "NSMenu"; title = "Text"; ObjectID = "d9c-me-L2H"; */
"d9c-me-L2H.title" = "Text";

/* Class = "NSMenuItem"; title = "Make Lower Case"; ObjectID = "d9M-CD-aMd"; */
"d9M-CD-aMd.title" = "Transformă în minuscule";

/* Class = "NSMenuItem"; title = "File"; ObjectID = "dMs-cI-mzQ"; */
"dMs-cI-mzQ.title" = "Fișier";

/* Class = "NSMenuItem"; title = "Undo"; ObjectID = "dRJ-4n-Yzg"; */
"dRJ-4n-Yzg.title" = "Anulează";

/* Class = "NSMenuItem"; title = "Spelling and Grammar"; ObjectID = "Dv1-io-Yv7"; */
"Dv1-io-Yv7.title" = "Ortografie și gramatică";

/* Class = "NSMenuItem"; title = "Close"; ObjectID = "DVo-aG-piG"; */
"DVo-aG-piG.title" = "Închide";

/* Class = "NSMenu"; title = "Help"; ObjectID = "F2S-fz-NVQ"; */
"F2S-fz-NVQ.title" = "Ajutor";

/* Class = "NSMenuItem"; title = "Text"; ObjectID = "Fal-I4-PZk"; */
"Fal-I4-PZk.title" = "Text";

/* Class = "NSMenu"; title = "Substitutions"; ObjectID = "FeM-D8-WVr"; */
"FeM-D8-WVr.title" = "Înlocuiri";

/* Class = "NSMenuItem"; title = "MobileDeviceUpdater Help"; ObjectID = "FKE-Sm-Kum"; */
"FKE-Sm-Kum.title" = "Ajutor MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Bold"; ObjectID = "GB9-OM-e27"; */
"GB9-OM-e27.title" = "Aldin";

/* Class = "NSMenu"; title = "Format"; ObjectID = "GEO-Iw-cKr"; */
"GEO-Iw-cKr.title" = "Format";

/* Class = "NSMenuItem"; title = "Font"; ObjectID = "Gi5-1S-RQB"; */
"Gi5-1S-RQB.title" = "Font";

/* Class = "NSMenuItem"; title = "Use Default"; ObjectID = "GUa-eO-cwY"; */
"GUa-eO-cwY.title" = "Utilizează valori implicite";

/* Class = "NSMenuItem"; title = "Paste"; ObjectID = "gVA-U4-sdL"; */
"gVA-U4-sdL.title" = "Lipește";

/* Class = "NSMenuItem"; title = "Writing Direction"; ObjectID = "H1b-Si-o9J"; */
"H1b-Si-o9J.title" = "Sens de scriere";

/* Class = "NSMenuItem"; title = "View"; ObjectID = "H8h-7b-M4v"; */
"H8h-7b-M4v.title" = "Vizualizare";

/* Class = "NSMenuItem"; title = "Show Spelling and Grammar"; ObjectID = "HFo-cy-zxI"; */
"HFo-cy-zxI.title" = "Afișează ortografia și gramatica";

/* Class = "NSMenuItem"; title = "Text Replacement"; ObjectID = "HFQ-gK-NFA"; */
"HFQ-gK-NFA.title" = "Înlocuire de text";

/* Class = "NSMenuItem"; title = "Smart Quotes"; ObjectID = "hQb-2v-fYv"; */
"hQb-2v-fYv.title" = "Ghilimele inteligente";

/* Class = "NSMenu"; title = "View"; ObjectID = "HyV-fh-RgO"; */
"HyV-fh-RgO.title" = "Vizualizare";

/* Class = "NSMenuItem"; title = "Check Document Now"; ObjectID = "hz2-CU-CR7"; */
"hz2-CU-CR7.title" = "Verifică documentul acum";

/* Class = "NSMenu"; title = "Services"; ObjectID = "hz9-B4-Xy5"; */
"hz9-B4-Xy5.title" = "Servicii";

/* Class = "NSMenuItem"; title = "Subscript"; ObjectID = "I0S-gh-46l"; */
"I0S-gh-46l.title" = "Indice";

/* Class = "NSMenuItem"; title = "Smaller"; ObjectID = "i1d-Er-qST"; */
"i1d-Er-qST.title" = "Mai mic";

/* Class = "NSMenuItem"; title = "Open…"; ObjectID = "IAo-SY-fd9"; */
"IAo-SY-fd9.title" = "Deschide…";

/* Class = "NSMenu"; title = "Baseline"; ObjectID = "ijk-EB-dga"; */
"ijk-EB-dga.title" = "Linie de bază";

/* Class = "NSMenuItem"; title = "Justify"; ObjectID = "J5U-5w-g23"; */
"J5U-5w-g23.title" = "Aliniat justificat";

/* Class = "NSMenuItem"; title = "Use None"; ObjectID = "J7y-lM-qPV"; */
"J7y-lM-qPV.title" = "Nu utiliza nimic";

/* Class = "NSMenuItem"; title = "Kern"; ObjectID = "jBQ-r6-VK2"; */
"jBQ-r6-VK2.title" = "Spațiere selectivă caractere";

/* Class = "NSMenuItem"; title = "\tRight to Left"; ObjectID = "jFq-tB-4Kx"; */
"jFq-tB-4Kx.title" = "\tDe la dreapta la stânga";

/* Class = "NSMenuItem"; title = "Format"; ObjectID = "jxT-CU-nIS"; */
"jxT-CU-nIS.title" = "Format";

/* Class = "NSMenuItem"; title = "Revert to Saved"; ObjectID = "KaW-ft-85H"; */
"KaW-ft-85H.title" = "Revino la versiunea salvată";

/* Class = "NSMenuItem"; title = "Show All"; ObjectID = "Kd2-mp-pUS"; */
"Kd2-mp-pUS.title" = "Afișează tot";

/* Class = "NSMenuItem"; title = "Show Sidebar"; ObjectID = "kIP-vf-haE"; */
"kIP-vf-haE.title" = "Afișează bara laterală";

/* Class = "NSMenuItem"; title = "\tLeft to Right"; ObjectID = "Lbh-J2-qVU"; */
"Lbh-J2-qVU.title" = "\tDe la stânga la dreapta";

/* Class = "NSMenuItem"; title = "Bring All to Front"; ObjectID = "LE2-aR-0XJ"; */
"LE2-aR-0XJ.title" = "Adu tot în față";

/* Class = "NSMenuItem"; title = "Paste Ruler"; ObjectID = "LVM-kO-fVI"; */
"LVM-kO-fVI.title" = "Lipește rigla";

/* Class = "NSMenuItem"; title = "Check Grammar With Spelling"; ObjectID = "mK6-2p-4JG"; */
"mK6-2p-4JG.title" = "Verifică gramatica odată cu ortografia";

/* Class = "NSMenuItem"; title = "Copy Ruler"; ObjectID = "MkV-Pr-PK5"; */
"MkV-Pr-PK5.title" = "Copiază rigla";

/* Class = "NSMenuItem"; title = "Services"; ObjectID = "NMo-om-nkz"; */
"NMo-om-nkz.title" = "Servicii";

/* Class = "NSMenuItem"; title = "\tDefault"; ObjectID = "Nop-cj-93Q"; */
"Nop-cj-93Q.title" = "\tImplicit";

/* Class = "NSMenuItem"; title = "Ligatures"; ObjectID = "o6e-r0-MWq"; */
"o6e-r0-MWq.title" = "Ligaturi";

/* Class = "NSMenuItem"; title = "Baseline"; ObjectID = "OaQ-X3-Vso"; */
"OaQ-X3-Vso.title" = "Linie de bază";

/* Class = "NSMenu"; title = "Open Recent"; ObjectID = "oas-Oc-fiZ"; */
"oas-Oc-fiZ.title" = "Deschide recente";

/* Class = "NSMenuItem"; title = "Loosen"; ObjectID = "ogc-rX-tC1"; */
"ogc-rX-tC1.title" = "Mai larg";

/* Class = "NSMenuItem"; title = "Hide MobileDeviceUpdater"; ObjectID = "Olw-nP-bQN"; */
"Olw-nP-bQN.title" = "Ascunde MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Find Previous"; ObjectID = "OwM-mh-QMV"; */
"OwM-mh-QMV.title" = "Găsește anteriorul";

/* Class = "NSMenuItem"; title = "Minimize"; ObjectID = "OY7-WF-poV"; */
"OY7-WF-poV.title" = "Minimizează";

/* Class = "NSMenuItem"; title = "Stop Speaking"; ObjectID = "Oyz-dy-DGm"; */
"Oyz-dy-DGm.title" = "Oprește enunțarea";

/* Class = "NSMenuItem"; title = "Delete"; ObjectID = "pa3-QI-u2k"; */
"pa3-QI-u2k.title" = "Șterge";

/* Class = "NSMenuItem"; title = "Bigger"; ObjectID = "Ptp-SP-VEL"; */
"Ptp-SP-VEL.title" = "Mai mare";

/* Class = "NSMenuItem"; title = "Save…"; ObjectID = "pxx-59-PXV"; */
"pxx-59-PXV.title" = "Salvează…";

/* Class = "NSMenuItem"; title = "Show Fonts"; ObjectID = "Q5e-8K-NDq"; */
"Q5e-8K-NDq.title" = "Afișează fonturi";

/* Class = "NSMenuItem"; title = "Find Next"; ObjectID = "q09-fT-Sye"; */
"q09-fT-Sye.title" = "Găsește următorul";

/* Class = "NSMenuItem"; title = "Page Setup…"; ObjectID = "qIS-W8-SiK"; */
"qIS-W8-SiK.title" = "Configurare pagină…";

/* Class = "NSMenuItem"; title = "Zoom"; ObjectID = "R4o-n2-Eq4"; */
"R4o-n2-Eq4.title" = "Zoom";

/* Class = "NSMenuItem"; title = "\tRight to Left"; ObjectID = "RB4-Sm-HuC"; */
"RB4-Sm-HuC.title" = "\tDe la dreapta la stânga";

/* Class = "NSMenuItem"; title = "Check Spelling While Typing"; ObjectID = "rbD-Rh-wIN"; */
"rbD-Rh-wIN.title" = "Verifică ortografia în timpul scrierii";

/* Class = "NSMenuItem"; title = "Smart Dashes"; ObjectID = "rgM-f4-ycn"; */
"rgM-f4-ycn.title" = "Cratime inteligente";

/* Class = "NSMenuItem"; title = "Superscript"; ObjectID = "Rqc-34-cIF"; */
"Rqc-34-cIF.title" = "Exponent";

/* Class = "NSMenuItem"; title = "Select All"; ObjectID = "Ruw-6m-B2m"; */
"Ruw-6m-B2m.title" = "Selectează tot";

/* Class = "NSMenuItem"; title = "Jump to Selection"; ObjectID = "S0p-oC-mLd"; */
"S0p-oC-mLd.title" = "Salt la selecție";

/* Class = "NSMenuItem"; title = "Show Toolbar"; ObjectID = "snW-S8-Cw5"; */
"snW-S8-Cw5.title" = "Afișează bara de instrumente";

/* Class = "NSMenu"; title = "Window"; ObjectID = "Td7-aD-5lo"; */
"Td7-aD-5lo.title" = "Fereastră";

/* Class = "NSMenu"; title = "Kern"; ObjectID = "tlD-Oa-oAM"; */
"tlD-Oa-oAM.title" = "Spațiere selectivă caractere";

/* Class = "NSMenuItem"; title = "Data Detectors"; ObjectID = "tRr-pd-1PS"; */
"tRr-pd-1PS.title" = "Detectoare de date";

/* Class = "NSMenuItem"; title = "Open Recent"; ObjectID = "tXI-mr-wws"; */
"tXI-mr-wws.title" = "Deschide recente";

/* Class = "NSMenuItem"; title = "Capitalize"; ObjectID = "UEZ-Bs-lqG"; */
"UEZ-Bs-lqG.title" = "Inițială cu majusculă";

/* Class = "NSMenu"; title = "MobileDeviceUpdater"; ObjectID = "uQy-DD-JDr"; */
"uQy-DD-JDr.title" = "MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Cut"; ObjectID = "uRl-iY-unG"; */
"uRl-iY-unG.title" = "Taie";

/* Class = "NSMenuItem"; title = "Hide Others"; ObjectID = "Vdr-fp-XzO"; */
"Vdr-fp-XzO.title" = "Ascunde restul";

/* Class = "NSMenuItem"; title = "Center"; ObjectID = "VIY-Ag-zcb"; */
"VIY-Ag-zcb.title" = "Centrat";

/* Class = "NSMenuItem"; title = "Italic"; ObjectID = "Vjx-xi-njq"; */
"Vjx-xi-njq.title" = "Cursiv";

/* Class = "NSMenuItem"; title = "Paste Style"; ObjectID = "vKC-jM-MkH"; */
"vKC-jM-MkH.title" = "Lipește stilul";

/* Class = "NSMenuItem"; title = "Show Ruler"; ObjectID = "vLm-3I-IUL"; */
"vLm-3I-IUL.title" = "Afișează rigla";

/* Class = "NSMenuItem"; title = "Make Upper Case"; ObjectID = "vmV-6d-7jI"; */
"vmV-6d-7jI.title" = "Transformă în majuscule";

/* Class = "NSMenuItem"; title = "Clear Menu"; ObjectID = "vNY-rz-j42"; */
"vNY-rz-j42.title" = "Degajează meniul";

/* Class = "NSMenu"; title = "Ligatures"; ObjectID = "w0m-vy-SC9"; */
"w0m-vy-SC9.title" = "Ligaturi";

/* Class = "NSMenu"; title = "Edit"; ObjectID = "W48-6f-4Dl"; */
"W48-6f-4Dl.title" = "Editare";

/* Class = "NSMenuItem"; title = "New"; ObjectID = "Was-JA-tGl"; */
"Was-JA-tGl.title" = "Nou";

/* Class = "NSMenuItem"; title = "Align Right"; ObjectID = "wb2-vD-lq4"; */
"wb2-vD-lq4.title" = "Aliniat la dreapta";

/* Class = "NSMenuItem"; title = "Paste and Match Style"; ObjectID = "WeT-3V-zwk"; */
"WeT-3V-zwk.title" = "Lipește și adaptează stilul";

/* Class = "NSMenuItem"; title = "Help"; ObjectID = "wpr-3q-Mcd"; */
"wpr-3q-Mcd.title" = "Ajutor";

/* Class = "NSMenuItem"; title = "Underline"; ObjectID = "WRG-CD-K1S"; */
"WRG-CD-K1S.title" = "Subliniat";

/* Class = "NSMenuItem"; title = "Copy"; ObjectID = "x3v-GG-iWU"; */
"x3v-GG-iWU.title" = "Copiază";

/* Class = "NSMenuItem"; title = "Use All"; ObjectID = "xQD-1f-W4t"; */
"xQD-1f-W4t.title" = "Utilizează tot";

/* Class = "NSMenuItem"; title = "Speech"; ObjectID = "xrE-MZ-jX0"; */
"xrE-MZ-jX0.title" = "Enunțare";

/* Class = "NSMenuItem"; title = "Find…"; ObjectID = "Xz5-n4-O0W"; */
"Xz5-n4-O0W.title" = "Găsește…";

/* Class = "NSMenuItem"; title = "Find and Replace…"; ObjectID = "YEy-JH-Tfz"; */
"YEy-JH-Tfz.title" = "Găsește și înlocuiește…";

/* Class = "NSMenuItem"; title = "\tDefault"; ObjectID = "YGs-j5-SAR"; */
"YGs-j5-SAR.title" = "\tImplicit";

/* Class = "NSMenuItem"; title = "Start Speaking"; ObjectID = "Ynk-f8-cLZ"; */
"Ynk-f8-cLZ.title" = "Pornește enunțarea";

/* Class = "NSMenuItem"; title = "Show Substitutions"; ObjectID = "z6F-FW-3nz"; */
"z6F-FW-3nz.title" = "Afișează înlocuirile";

/* Class = "NSMenuItem"; title = "Align Left"; ObjectID = "ZM1-6Q-yy1"; */
"ZM1-6Q-yy1.title" = "Aliniat la stânga";

/* Class = "NSMenuItem"; title = "Paragraph"; ObjectID = "ZvO-Gk-QUH"; */
"ZvO-Gk-QUH.title" = "Paragraf";

