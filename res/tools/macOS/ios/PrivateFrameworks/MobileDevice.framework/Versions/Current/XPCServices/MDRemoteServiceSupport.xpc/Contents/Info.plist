<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>20A241111</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>MDRemoteServiceSupport</string>
	<key>CFBundleExecutable</key>
	<string>MDRemoteServiceSupport</string>
	<key>CFBundleIdentifier</key>
	<string>com.apple.MDRemoteServiceSupport</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>MDRemoteServiceSupport</string>
	<key>CFBundlePackageType</key>
	<string>XPC!</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>13A6114e</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>11.0</string>
	<key>DTSDKBuild</key>
	<string>20A2411</string>
	<key>DTSDKName</key>
	<string>macosx11.0internal</string>
	<key>DTXcode</key>
	<string>1300</string>
	<key>DTXcodeBuild</key>
	<string>13A6114e</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.11</string>
	<key>XPCService</key>
	<dict>
		<key>ServiceType</key>
		<string>Application</string>
	</dict>
</dict>
</plist>
