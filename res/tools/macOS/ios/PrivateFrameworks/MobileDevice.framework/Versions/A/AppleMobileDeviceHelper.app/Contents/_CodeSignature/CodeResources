<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppleMobileBackup</key>
		<data>
		ML9zWDlvJxQLqqaWiB9ronlnbdk=
		</data>
		<key>Resources/ClientDescription.plist</key>
		<data>
		6134ZbKmDiZmESqNbEiG88vruvE=
		</data>
		<key>Resources/ClientDescription20.plist</key>
		<data>
		TV08NP+ZK+E1tuEztmkD+4mtwww=
		</data>
		<key>Resources/ClientDescription30.plist</key>
		<data>
		ocqXQaYMcGLjcbXe2tfceh6sHSg=
		</data>
		<key>Resources/ClientDescription33.plist</key>
		<data>
		JQApM0H52NJTaIy00ESveI7GJ9s=
		</data>
		<key>Resources/ClientDescription33Tiger.plist</key>
		<data>
		wuVDw6FyiYAntQ17vM2brl+Ov4c=
		</data>
		<key>Resources/ClientDescription40.plist</key>
		<data>
		yEjhSeuS4+H8GFtm/zyQddOomDM=
		</data>
		<key>Resources/ClientDescription40Tiger.plist</key>
		<data>
		AxDffNhnEHkXWkusCFIgAthz0Is=
		</data>
		<key>Resources/ClientDescription50.plist</key>
		<data>
		T9AqND1bdDe3gYhPv95s24RS5U8=
		</data>
		<key>Resources/ClientDescription50SnowLeopard.plist</key>
		<data>
		yEjhSeuS4+H8GFtm/zyQddOomDM=
		</data>
		<key>Resources/ClientDescription50Tiger.plist</key>
		<data>
		AxDffNhnEHkXWkusCFIgAthz0Is=
		</data>
		<key>Resources/MDCrashReportTool</key>
		<data>
		UrEcr9Qo066B3wXrJWKiU7hOz3Q=
		</data>
		<key>Resources/iPodSyncClientImages.icns</key>
		<data>
		K2G+bv1n34Ab17MMs0l8BQn2ZOE=
		</data>
		<key>version.plist</key>
		<data>
		zpsshENzzBSttREDQe9i38dtT4U=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/AppleMobileBackup</key>
		<dict>
			<key>hash</key>
			<data>
			ML9zWDlvJxQLqqaWiB9ronlnbdk=
			</data>
			<key>hash2</key>
			<data>
			tDK0T+1iSOPs1eFk5nnzhsHusLJkjVQEF+ciGV6ymhk=
			</data>
		</dict>
		<key>Resources/ClientDescription.plist</key>
		<dict>
			<key>hash</key>
			<data>
			6134ZbKmDiZmESqNbEiG88vruvE=
			</data>
			<key>hash2</key>
			<data>
			LWEtdrnj6uHaE1Mxz9sMWhffyFCGY6l1zECNo6LfUW4=
			</data>
		</dict>
		<key>Resources/ClientDescription20.plist</key>
		<dict>
			<key>hash</key>
			<data>
			TV08NP+ZK+E1tuEztmkD+4mtwww=
			</data>
			<key>hash2</key>
			<data>
			m6A+AAm1BaAatveHNdk4JJIhHbZFNQurc8l/UrUDjgo=
			</data>
		</dict>
		<key>Resources/ClientDescription30.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ocqXQaYMcGLjcbXe2tfceh6sHSg=
			</data>
			<key>hash2</key>
			<data>
			Q3g+mKELydr8NzS39WP9LqUm/g5C71yZHk841p2xRhM=
			</data>
		</dict>
		<key>Resources/ClientDescription33.plist</key>
		<dict>
			<key>hash</key>
			<data>
			JQApM0H52NJTaIy00ESveI7GJ9s=
			</data>
			<key>hash2</key>
			<data>
			t005DfH+h8ckbH83TGLF+RU8u+HIVCqjVfeeyLAHbfw=
			</data>
		</dict>
		<key>Resources/ClientDescription33Tiger.plist</key>
		<dict>
			<key>hash</key>
			<data>
			wuVDw6FyiYAntQ17vM2brl+Ov4c=
			</data>
			<key>hash2</key>
			<data>
			vu0GbusZcDESYUNPbQCYDPAuD98h58Y8lvA2oYJi9OE=
			</data>
		</dict>
		<key>Resources/ClientDescription40.plist</key>
		<dict>
			<key>hash</key>
			<data>
			yEjhSeuS4+H8GFtm/zyQddOomDM=
			</data>
			<key>hash2</key>
			<data>
			bf2oMP31NBblebtHLQrTXTjitJCMqPrLMsEQcRXsMPU=
			</data>
		</dict>
		<key>Resources/ClientDescription40Tiger.plist</key>
		<dict>
			<key>hash</key>
			<data>
			AxDffNhnEHkXWkusCFIgAthz0Is=
			</data>
			<key>hash2</key>
			<data>
			pAn2T+1jIppkzDKXV5Z89okpTKUaNFP271Y0nmW3q7I=
			</data>
		</dict>
		<key>Resources/ClientDescription50.plist</key>
		<dict>
			<key>hash</key>
			<data>
			T9AqND1bdDe3gYhPv95s24RS5U8=
			</data>
			<key>hash2</key>
			<data>
			O75m29NVwIf6qjDHNelktc7/C2AWJGI44U7J0iLM9UM=
			</data>
		</dict>
		<key>Resources/ClientDescription50SnowLeopard.plist</key>
		<dict>
			<key>hash</key>
			<data>
			yEjhSeuS4+H8GFtm/zyQddOomDM=
			</data>
			<key>hash2</key>
			<data>
			bf2oMP31NBblebtHLQrTXTjitJCMqPrLMsEQcRXsMPU=
			</data>
		</dict>
		<key>Resources/ClientDescription50Tiger.plist</key>
		<dict>
			<key>hash</key>
			<data>
			AxDffNhnEHkXWkusCFIgAthz0Is=
			</data>
			<key>hash2</key>
			<data>
			pAn2T+1jIppkzDKXV5Z89okpTKUaNFP271Y0nmW3q7I=
			</data>
		</dict>
		<key>Resources/MDCrashReportTool</key>
		<dict>
			<key>hash</key>
			<data>
			UrEcr9Qo066B3wXrJWKiU7hOz3Q=
			</data>
			<key>hash2</key>
			<data>
			JsP6S4jaKs3OmJ6SsTHZ9/guHFgriho8qe4BxZF2ISM=
			</data>
		</dict>
		<key>Resources/iPodSyncClientImages.icns</key>
		<dict>
			<key>hash</key>
			<data>
			K2G+bv1n34Ab17MMs0l8BQn2ZOE=
			</data>
			<key>hash2</key>
			<data>
			RxTCbW3Z4ZZuxhtMxck1TIgal1pkqWoSPJgfcglLzSE=
			</data>
		</dict>
		<key>version.plist</key>
		<dict>
			<key>hash</key>
			<data>
			zpsshENzzBSttREDQe9i38dtT4U=
			</data>
			<key>hash2</key>
			<data>
			XOmZzH3dW7O1nCMhvwYo5vcrOJNdVS1vhXIHWpFjjWY=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
