/* Class = "NSMenu"; title = "Find"; ObjectID = "1b7-l0-nxx"; */
"1b7-l0-nxx.title" = "查找";

/* Class = "NSMenuItem"; title = "Lower"; ObjectID = "1tx-W0-xDw"; */
"1tx-W0-xDw.title" = "降低";

/* Class = "NSMenuItem"; title = "Customize Toolbar…"; ObjectID = "1UK-8n-QPP"; */
"1UK-8n-QPP.title" = "自定工具栏…";

/* Class = "NSMenuItem"; title = "MobileDeviceUpdater"; ObjectID = "1Xt-HY-uBw"; */
"1Xt-HY-uBw.title" = "MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Raise"; ObjectID = "2h7-ER-AoG"; */
"2h7-ER-AoG.title" = "升高";

/* Class = "NSMenuItem"; title = "Transformations"; ObjectID = "2oI-Rn-ZJC"; */
"2oI-Rn-ZJC.title" = "转换";

/* Class = "NSMenu"; title = "Spelling"; ObjectID = "3IN-sU-3Bg"; */
"3IN-sU-3Bg.title" = "拼写";

/* Class = "NSMenuItem"; title = "Use Default"; ObjectID = "3Om-Ey-2VK"; */
"3Om-Ey-2VK.title" = "使用默认";

/* Class = "NSMenu"; title = "Speech"; ObjectID = "3rS-ZA-NoH"; */
"3rS-ZA-NoH.title" = "语音";

/* Class = "NSMenuItem"; title = "Find"; ObjectID = "4EN-yA-p0u"; */
"4EN-yA-p0u.title" = "查找";

/* Class = "NSMenuItem"; title = "Enter Full Screen"; ObjectID = "4J7-dP-txa"; */
"4J7-dP-txa.title" = "进入全屏幕";

/* Class = "NSMenuItem"; title = "Quit MobileDeviceUpdater"; ObjectID = "4sb-4s-VLi"; */
"4sb-4s-VLi.title" = "退出MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "About MobileDeviceUpdater"; ObjectID = "5kV-Vb-QxS"; */
"5kV-Vb-QxS.title" = "关于MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Edit"; ObjectID = "5QF-Oa-p0T"; */
"5QF-Oa-p0T.title" = "编辑";

/* Class = "NSMenuItem"; title = "Copy Style"; ObjectID = "5Vv-lz-BsD"; */
"5Vv-lz-BsD.title" = "拷贝样式";

/* Class = "NSMenuItem"; title = "Redo"; ObjectID = "6dh-zS-Vam"; */
"6dh-zS-Vam.title" = "重做";

/* Class = "NSMenu"; title = "Writing Direction"; ObjectID = "8mr-sm-Yjd"; */
"8mr-sm-Yjd.title" = "书写方向";

/* Class = "NSMenuItem"; title = "Substitutions"; ObjectID = "9ic-FL-obx"; */
"9ic-FL-obx.title" = "替换";

/* Class = "NSMenuItem"; title = "Smart Copy/Paste"; ObjectID = "9yt-4B-nSM"; */
"9yt-4B-nSM.title" = "智能拷贝/粘贴";

/* Class = "NSMenuItem"; title = "Tighten"; ObjectID = "46P-cB-AYj"; */
"46P-cB-AYj.title" = "紧排";

/* Class = "NSMenuItem"; title = "Correct Spelling Automatically"; ObjectID = "78Y-hA-62v"; */
"78Y-hA-62v.title" = "自动纠正拼写";

/* Class = "NSMenuItem"; title = "Use Default"; ObjectID = "agt-UL-0e3"; */
"agt-UL-0e3.title" = "使用默认";

/* Class = "NSMenuItem"; title = "Print…"; ObjectID = "aTl-1u-JFS"; */
"aTl-1u-JFS.title" = "打印…";

/* Class = "NSMenuItem"; title = "Window"; ObjectID = "aUF-d1-5bR"; */
"aUF-d1-5bR.title" = "窗口";

/* Class = "NSMenu"; title = "Font"; ObjectID = "aXa-aM-Jaq"; */
"aXa-aM-Jaq.title" = "字体";

/* Class = "NSMenu"; title = "Main Menu"; ObjectID = "AYu-sK-qS6"; */
"AYu-sK-qS6.title" = "主菜单";

/* Class = "NSMenuItem"; title = "\tLeft to Right"; ObjectID = "BgM-ve-c93"; */
"BgM-ve-c93.title" = "\t从左到右";

/* Class = "NSMenuItem"; title = "Show Colors"; ObjectID = "bgn-CT-cEk"; */
"bgn-CT-cEk.title" = "显示颜色";

/* Class = "NSMenu"; title = "File"; ObjectID = "bib-Uj-vzu"; */
"bib-Uj-vzu.title" = "文件";

/* Class = "NSMenuItem"; title = "Preferences…"; ObjectID = "BOF-NM-1cW"; */
"BOF-NM-1cW.title" = "偏好设置…";

/* Class = "NSMenuItem"; title = "Use Selection for Find"; ObjectID = "buJ-ug-pKt"; */
"buJ-ug-pKt.title" = "查找所选内容";

/* Class = "NSMenuItem"; title = "Save As…"; ObjectID = "Bw7-FT-i3A"; */
"Bw7-FT-i3A.title" = "存储为…";

/* Class = "NSMenu"; title = "Transformations"; ObjectID = "c8a-y6-VQd"; */
"c8a-y6-VQd.title" = "转换";

/* Class = "NSMenuItem"; title = "Use None"; ObjectID = "cDB-IK-hbR"; */
"cDB-IK-hbR.title" = "都不使用";

/* Class = "NSMenuItem"; title = "Selection"; ObjectID = "cqv-fj-IhA"; */
"cqv-fj-IhA.title" = "所选内容";

/* Class = "NSMenuItem"; title = "Smart Links"; ObjectID = "cwL-P1-jid"; */
"cwL-P1-jid.title" = "智能链接";

/* Class = "NSMenu"; title = "Text"; ObjectID = "d9c-me-L2H"; */
"d9c-me-L2H.title" = "文本";

/* Class = "NSMenuItem"; title = "Make Lower Case"; ObjectID = "d9M-CD-aMd"; */
"d9M-CD-aMd.title" = "变为小写";

/* Class = "NSMenuItem"; title = "File"; ObjectID = "dMs-cI-mzQ"; */
"dMs-cI-mzQ.title" = "文件";

/* Class = "NSMenuItem"; title = "Undo"; ObjectID = "dRJ-4n-Yzg"; */
"dRJ-4n-Yzg.title" = "撤销";

/* Class = "NSMenuItem"; title = "Spelling and Grammar"; ObjectID = "Dv1-io-Yv7"; */
"Dv1-io-Yv7.title" = "拼写和语法";

/* Class = "NSMenuItem"; title = "Close"; ObjectID = "DVo-aG-piG"; */
"DVo-aG-piG.title" = "关闭";

/* Class = "NSMenu"; title = "Help"; ObjectID = "F2S-fz-NVQ"; */
"F2S-fz-NVQ.title" = "帮助";

/* Class = "NSMenuItem"; title = "Text"; ObjectID = "Fal-I4-PZk"; */
"Fal-I4-PZk.title" = "文本";

/* Class = "NSMenu"; title = "Substitutions"; ObjectID = "FeM-D8-WVr"; */
"FeM-D8-WVr.title" = "替换";

/* Class = "NSMenuItem"; title = "MobileDeviceUpdater Help"; ObjectID = "FKE-Sm-Kum"; */
"FKE-Sm-Kum.title" = "MobileDeviceUpdater帮助";

/* Class = "NSMenuItem"; title = "Bold"; ObjectID = "GB9-OM-e27"; */
"GB9-OM-e27.title" = "粗体";

/* Class = "NSMenu"; title = "Format"; ObjectID = "GEO-Iw-cKr"; */
"GEO-Iw-cKr.title" = "格式";

/* Class = "NSMenuItem"; title = "Font"; ObjectID = "Gi5-1S-RQB"; */
"Gi5-1S-RQB.title" = "字体";

/* Class = "NSMenuItem"; title = "Use Default"; ObjectID = "GUa-eO-cwY"; */
"GUa-eO-cwY.title" = "使用默认";

/* Class = "NSMenuItem"; title = "Paste"; ObjectID = "gVA-U4-sdL"; */
"gVA-U4-sdL.title" = "粘贴";

/* Class = "NSMenuItem"; title = "Writing Direction"; ObjectID = "H1b-Si-o9J"; */
"H1b-Si-o9J.title" = "书写方向";

/* Class = "NSMenuItem"; title = "View"; ObjectID = "H8h-7b-M4v"; */
"H8h-7b-M4v.title" = "视图";

/* Class = "NSMenuItem"; title = "Show Spelling and Grammar"; ObjectID = "HFo-cy-zxI"; */
"HFo-cy-zxI.title" = "显示拼写和语法";

/* Class = "NSMenuItem"; title = "Text Replacement"; ObjectID = "HFQ-gK-NFA"; */
"HFQ-gK-NFA.title" = "文本替换";

/* Class = "NSMenuItem"; title = "Smart Quotes"; ObjectID = "hQb-2v-fYv"; */
"hQb-2v-fYv.title" = "智能引号";

/* Class = "NSMenu"; title = "View"; ObjectID = "HyV-fh-RgO"; */
"HyV-fh-RgO.title" = "视图";

/* Class = "NSMenuItem"; title = "Check Document Now"; ObjectID = "hz2-CU-CR7"; */
"hz2-CU-CR7.title" = "立即检查文稿";

/* Class = "NSMenu"; title = "Services"; ObjectID = "hz9-B4-Xy5"; */
"hz9-B4-Xy5.title" = "服务";

/* Class = "NSMenuItem"; title = "Subscript"; ObjectID = "I0S-gh-46l"; */
"I0S-gh-46l.title" = "下标";

/* Class = "NSMenuItem"; title = "Smaller"; ObjectID = "i1d-Er-qST"; */
"i1d-Er-qST.title" = "较小";

/* Class = "NSMenuItem"; title = "Open…"; ObjectID = "IAo-SY-fd9"; */
"IAo-SY-fd9.title" = "打开…";

/* Class = "NSMenu"; title = "Baseline"; ObjectID = "ijk-EB-dga"; */
"ijk-EB-dga.title" = "基线";

/* Class = "NSMenuItem"; title = "Justify"; ObjectID = "J5U-5w-g23"; */
"J5U-5w-g23.title" = "两端对齐";

/* Class = "NSMenuItem"; title = "Use None"; ObjectID = "J7y-lM-qPV"; */
"J7y-lM-qPV.title" = "都不使用";

/* Class = "NSMenuItem"; title = "Kern"; ObjectID = "jBQ-r6-VK2"; */
"jBQ-r6-VK2.title" = "字距调整";

/* Class = "NSMenuItem"; title = "\tRight to Left"; ObjectID = "jFq-tB-4Kx"; */
"jFq-tB-4Kx.title" = "\t从右到左";

/* Class = "NSMenuItem"; title = "Format"; ObjectID = "jxT-CU-nIS"; */
"jxT-CU-nIS.title" = "格式";

/* Class = "NSMenuItem"; title = "Revert to Saved"; ObjectID = "KaW-ft-85H"; */
"KaW-ft-85H.title" = "复原到已存储版本";

/* Class = "NSMenuItem"; title = "Show All"; ObjectID = "Kd2-mp-pUS"; */
"Kd2-mp-pUS.title" = "全部显示";

/* Class = "NSMenuItem"; title = "Show Sidebar"; ObjectID = "kIP-vf-haE"; */
"kIP-vf-haE.title" = "显示边栏";

/* Class = "NSMenuItem"; title = "\tLeft to Right"; ObjectID = "Lbh-J2-qVU"; */
"Lbh-J2-qVU.title" = "\t从左到右";

/* Class = "NSMenuItem"; title = "Bring All to Front"; ObjectID = "LE2-aR-0XJ"; */
"LE2-aR-0XJ.title" = "前置全部窗口";

/* Class = "NSMenuItem"; title = "Paste Ruler"; ObjectID = "LVM-kO-fVI"; */
"LVM-kO-fVI.title" = "粘贴标尺";

/* Class = "NSMenuItem"; title = "Check Grammar With Spelling"; ObjectID = "mK6-2p-4JG"; */
"mK6-2p-4JG.title" = "检查拼写和语法";

/* Class = "NSMenuItem"; title = "Copy Ruler"; ObjectID = "MkV-Pr-PK5"; */
"MkV-Pr-PK5.title" = "拷贝标尺";

/* Class = "NSMenuItem"; title = "Services"; ObjectID = "NMo-om-nkz"; */
"NMo-om-nkz.title" = "服务";

/* Class = "NSMenuItem"; title = "\tDefault"; ObjectID = "Nop-cj-93Q"; */
"Nop-cj-93Q.title" = "\t默认";

/* Class = "NSMenuItem"; title = "Ligatures"; ObjectID = "o6e-r0-MWq"; */
"o6e-r0-MWq.title" = "连字";

/* Class = "NSMenuItem"; title = "Baseline"; ObjectID = "OaQ-X3-Vso"; */
"OaQ-X3-Vso.title" = "基线";

/* Class = "NSMenu"; title = "Open Recent"; ObjectID = "oas-Oc-fiZ"; */
"oas-Oc-fiZ.title" = "打开最近使用";

/* Class = "NSMenuItem"; title = "Loosen"; ObjectID = "ogc-rX-tC1"; */
"ogc-rX-tC1.title" = "松排";

/* Class = "NSMenuItem"; title = "Hide MobileDeviceUpdater"; ObjectID = "Olw-nP-bQN"; */
"Olw-nP-bQN.title" = "隐藏MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Find Previous"; ObjectID = "OwM-mh-QMV"; */
"OwM-mh-QMV.title" = "查找上一个";

/* Class = "NSMenuItem"; title = "Minimize"; ObjectID = "OY7-WF-poV"; */
"OY7-WF-poV.title" = "最小化";

/* Class = "NSMenuItem"; title = "Stop Speaking"; ObjectID = "Oyz-dy-DGm"; */
"Oyz-dy-DGm.title" = "停止朗读";

/* Class = "NSMenuItem"; title = "Delete"; ObjectID = "pa3-QI-u2k"; */
"pa3-QI-u2k.title" = "删除";

/* Class = "NSMenuItem"; title = "Bigger"; ObjectID = "Ptp-SP-VEL"; */
"Ptp-SP-VEL.title" = "较大";

/* Class = "NSMenuItem"; title = "Save…"; ObjectID = "pxx-59-PXV"; */
"pxx-59-PXV.title" = "存储…";

/* Class = "NSMenuItem"; title = "Show Fonts"; ObjectID = "Q5e-8K-NDq"; */
"Q5e-8K-NDq.title" = "显示字体";

/* Class = "NSMenuItem"; title = "Find Next"; ObjectID = "q09-fT-Sye"; */
"q09-fT-Sye.title" = "查找下一个";

/* Class = "NSMenuItem"; title = "Page Setup…"; ObjectID = "qIS-W8-SiK"; */
"qIS-W8-SiK.title" = "页面设置…";

/* Class = "NSMenuItem"; title = "Zoom"; ObjectID = "R4o-n2-Eq4"; */
"R4o-n2-Eq4.title" = "缩放";

/* Class = "NSMenuItem"; title = "\tRight to Left"; ObjectID = "RB4-Sm-HuC"; */
"RB4-Sm-HuC.title" = "\t从右到左";

/* Class = "NSMenuItem"; title = "Check Spelling While Typing"; ObjectID = "rbD-Rh-wIN"; */
"rbD-Rh-wIN.title" = "键入时检查拼写";

/* Class = "NSMenuItem"; title = "Smart Dashes"; ObjectID = "rgM-f4-ycn"; */
"rgM-f4-ycn.title" = "智能破折号";

/* Class = "NSMenuItem"; title = "Superscript"; ObjectID = "Rqc-34-cIF"; */
"Rqc-34-cIF.title" = "上标";

/* Class = "NSMenuItem"; title = "Select All"; ObjectID = "Ruw-6m-B2m"; */
"Ruw-6m-B2m.title" = "全选";

/* Class = "NSMenuItem"; title = "Jump to Selection"; ObjectID = "S0p-oC-mLd"; */
"S0p-oC-mLd.title" = "跳到所选内容";

/* Class = "NSMenuItem"; title = "Show Toolbar"; ObjectID = "snW-S8-Cw5"; */
"snW-S8-Cw5.title" = "显示工具栏";

/* Class = "NSMenu"; title = "Window"; ObjectID = "Td7-aD-5lo"; */
"Td7-aD-5lo.title" = "窗口";

/* Class = "NSMenu"; title = "Kern"; ObjectID = "tlD-Oa-oAM"; */
"tlD-Oa-oAM.title" = "字距调整";

/* Class = "NSMenuItem"; title = "Data Detectors"; ObjectID = "tRr-pd-1PS"; */
"tRr-pd-1PS.title" = "数据检测器";

/* Class = "NSMenuItem"; title = "Open Recent"; ObjectID = "tXI-mr-wws"; */
"tXI-mr-wws.title" = "打开最近使用";

/* Class = "NSMenuItem"; title = "Capitalize"; ObjectID = "UEZ-Bs-lqG"; */
"UEZ-Bs-lqG.title" = "首字母大写";

/* Class = "NSMenu"; title = "MobileDeviceUpdater"; ObjectID = "uQy-DD-JDr"; */
"uQy-DD-JDr.title" = "MobileDeviceUpdater";

/* Class = "NSMenuItem"; title = "Cut"; ObjectID = "uRl-iY-unG"; */
"uRl-iY-unG.title" = "剪切";

/* Class = "NSMenuItem"; title = "Hide Others"; ObjectID = "Vdr-fp-XzO"; */
"Vdr-fp-XzO.title" = "隐藏其他";

/* Class = "NSMenuItem"; title = "Center"; ObjectID = "VIY-Ag-zcb"; */
"VIY-Ag-zcb.title" = "中间";

/* Class = "NSMenuItem"; title = "Italic"; ObjectID = "Vjx-xi-njq"; */
"Vjx-xi-njq.title" = "斜体";

/* Class = "NSMenuItem"; title = "Paste Style"; ObjectID = "vKC-jM-MkH"; */
"vKC-jM-MkH.title" = "粘贴样式";

/* Class = "NSMenuItem"; title = "Show Ruler"; ObjectID = "vLm-3I-IUL"; */
"vLm-3I-IUL.title" = "显示标尺";

/* Class = "NSMenuItem"; title = "Make Upper Case"; ObjectID = "vmV-6d-7jI"; */
"vmV-6d-7jI.title" = "变为大写";

/* Class = "NSMenuItem"; title = "Clear Menu"; ObjectID = "vNY-rz-j42"; */
"vNY-rz-j42.title" = "清除菜单";

/* Class = "NSMenu"; title = "Ligatures"; ObjectID = "w0m-vy-SC9"; */
"w0m-vy-SC9.title" = "连字";

/* Class = "NSMenu"; title = "Edit"; ObjectID = "W48-6f-4Dl"; */
"W48-6f-4Dl.title" = "编辑";

/* Class = "NSMenuItem"; title = "New"; ObjectID = "Was-JA-tGl"; */
"Was-JA-tGl.title" = "新增";

/* Class = "NSMenuItem"; title = "Align Right"; ObjectID = "wb2-vD-lq4"; */
"wb2-vD-lq4.title" = "右对齐";

/* Class = "NSMenuItem"; title = "Paste and Match Style"; ObjectID = "WeT-3V-zwk"; */
"WeT-3V-zwk.title" = "粘贴并匹配样式";

/* Class = "NSMenuItem"; title = "Help"; ObjectID = "wpr-3q-Mcd"; */
"wpr-3q-Mcd.title" = "帮助";

/* Class = "NSMenuItem"; title = "Underline"; ObjectID = "WRG-CD-K1S"; */
"WRG-CD-K1S.title" = "下划线";

/* Class = "NSMenuItem"; title = "Copy"; ObjectID = "x3v-GG-iWU"; */
"x3v-GG-iWU.title" = "拷贝";

/* Class = "NSMenuItem"; title = "Use All"; ObjectID = "xQD-1f-W4t"; */
"xQD-1f-W4t.title" = "全部使用";

/* Class = "NSMenuItem"; title = "Speech"; ObjectID = "xrE-MZ-jX0"; */
"xrE-MZ-jX0.title" = "语音";

/* Class = "NSMenuItem"; title = "Find…"; ObjectID = "Xz5-n4-O0W"; */
"Xz5-n4-O0W.title" = "查找…";

/* Class = "NSMenuItem"; title = "Find and Replace…"; ObjectID = "YEy-JH-Tfz"; */
"YEy-JH-Tfz.title" = "查找和替换…";

/* Class = "NSMenuItem"; title = "\tDefault"; ObjectID = "YGs-j5-SAR"; */
"YGs-j5-SAR.title" = "\t默认";

/* Class = "NSMenuItem"; title = "Start Speaking"; ObjectID = "Ynk-f8-cLZ"; */
"Ynk-f8-cLZ.title" = "开始朗读";

/* Class = "NSMenuItem"; title = "Show Substitutions"; ObjectID = "z6F-FW-3nz"; */
"z6F-FW-3nz.title" = "显示替换";

/* Class = "NSMenuItem"; title = "Align Left"; ObjectID = "ZM1-6Q-yy1"; */
"ZM1-6Q-yy1.title" = "左对齐";

/* Class = "NSMenuItem"; title = "Paragraph"; ObjectID = "ZvO-Gk-QUH"; */
"ZvO-Gk-QUH.title" = "段落";

