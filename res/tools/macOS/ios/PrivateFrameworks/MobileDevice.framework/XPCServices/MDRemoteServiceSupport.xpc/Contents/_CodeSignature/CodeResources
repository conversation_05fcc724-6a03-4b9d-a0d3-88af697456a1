<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/ar.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_AU.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr_CA.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/id.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/no.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/vi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>version.plist</key>
		<data>
		jouum+9hIW75rMQWDVLUBpxxipU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/ar.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_AU.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr_CA.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/id.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/no.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/vi.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GUyxtxpn/Mo2cKn/KFxC/Ltt9co=
			</data>
			<key>hash2</key>
			<data>
			UA0e+JLyvxbD8B1Ks9evu9Z6NotK5pBjz2Yl+fwPVqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>version.plist</key>
		<dict>
			<key>hash</key>
			<data>
			jouum+9hIW75rMQWDVLUBpxxipU=
			</data>
			<key>hash2</key>
			<data>
			uL2s3DQoFWDlV78VQfsJe07jfKZJfMrHc2peBHr/D78=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
