<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		ZDUcwWkxkKfWuzvdVckhWH8nCXs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/libswiftCore.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ecoLQJefJZi51EYDY7XBjL/u9jc=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftCore" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftCoreFoundation.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			VlOL9ONs4v5dbAa6qWMCtT4DJQs=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftCoreFoundation" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftCoreGraphics.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			fIqo9HjiiN/6pAdRefSc4TFuU9w=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftCoreGraphics" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftDarwin.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			quVCv4qA+F3KaYJGiWCmLtZE3kk=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftDarwin" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftDispatch.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			n9S54kn34GWbCIDYnlnlj+wdZjY=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftDispatch" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftFoundation.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			7Hr/QD0r9uC4D1CMvheFRaoajB0=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftFoundation" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftIOKit.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y+AKuGQ3R176Z9551igty7CaU1E=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftIOKit" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Frameworks/libswiftObjectiveC.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			uwLcAUGE5taiZd8e5PfmvGHH434=
			</data>
			<key>requirement</key>
			<string>identifier "com.apple.dt.runtime.swiftObjectiveC" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: chris sabeti (V32JWZABL2)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Headers/AMCoreAudio-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			4antI8cMzZ80sFAJsGELufgwEGg=
			</data>
			<key>hash2</key>
			<data>
			o1ibb+52XhHCXTbE2kZEno4vX8QZ6DmKmIYof6jCXEg=
			</data>
		</dict>
		<key>Headers/AMCoreAudio.h</key>
		<dict>
			<key>hash</key>
			<data>
			tuBJkEfoSBROgUJZz23TEiH0N4M=
			</data>
			<key>hash2</key>
			<data>
			JyhRwDPiqP2uOnnVNUpZp4cU2Jjj2vLNMHgST6IWLsA=
			</data>
		</dict>
		<key>Modules/AMCoreAudio.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			vNefVVmuXvBQqyuRAsO8FL0tRc4=
			</data>
			<key>hash2</key>
			<data>
			5+13YhrWyD9HVITl2p7LuPXigrxe+N8TffwPXfMsptk=
			</data>
		</dict>
		<key>Modules/AMCoreAudio.swiftmodule/x86_64-apple-macos.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			7tkXOxYCekwRS2j7vscaIYO04WU=
			</data>
			<key>hash2</key>
			<data>
			1hNMHcqcyhtP3e/NQnO5Jbp9ebCyvPvij+vnwFyY9kM=
			</data>
		</dict>
		<key>Modules/AMCoreAudio.swiftmodule/x86_64.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			vNefVVmuXvBQqyuRAsO8FL0tRc4=
			</data>
			<key>hash2</key>
			<data>
			5+13YhrWyD9HVITl2p7LuPXigrxe+N8TffwPXfMsptk=
			</data>
		</dict>
		<key>Modules/AMCoreAudio.swiftmodule/x86_64.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			7tkXOxYCekwRS2j7vscaIYO04WU=
			</data>
			<key>hash2</key>
			<data>
			1hNMHcqcyhtP3e/NQnO5Jbp9ebCyvPvij+vnwFyY9kM=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			1hDKIDeSsvj+/t1Z8utZrHJz8bw=
			</data>
			<key>hash2</key>
			<data>
			yn/b8rE7PF8XTbhhRCJPEKaVVhWdCtBaVj0/562b3XY=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ZDUcwWkxkKfWuzvdVckhWH8nCXs=
			</data>
			<key>hash2</key>
			<data>
			vaM0KgPqdFrYywnBtmIp2zAn5LiLmf3qarVpjdw7npc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
