#!/bin/bash
# Check if /sys/class/power_supply/battery is accessible


identifier=$1

if ! ./adb -s $1 shell test -e /sys/class/power_supply/battery ; then
  exit 1
fi
# Get a list of all folders in /sys/class/power_supply/
folders=$(./adb shell "cd /sys/class/power_supply/ ; ls -d */")
cycles_found=0
for folder in $folders; do
  # cd into each folder to check if it's accessible
  if ! ./adb -s $1 shell test -e "/sys/class/power_supply/$folderuevent" 2>/dev/null ; then
    # If uevent file is not present in the folder or permission denied, skip it
    continue
  fi
  # If uevent file is present, look for the word "Cycle" (case insensitive)
  cycle=$(./adb -s $1 shell "cat /sys/class/power_supply/$folder/uevent" 2>/dev/null | grep -i "Cycle")
  if [ ! -z "$cycle" ]; then
    # Extract the value from the line containing "Cycle"
    value=$(echo "$cycle" | cut -d= -f2 | tr -d '\n')
    # Check if the value contains only digits
    if [[ "$value" =~ ^[0-9]+$ ]]; then
      echo "Value: $value"
      echo "Path: /sys/class/power_supply/$folder"
      cycles_found=1
      break  # Exit the loop after printing the first occurrence of the value
    fi
  fi
done
# If no value was found, print an error message
if [ "$cycles_found" -eq 0 ]; then
  echo "battery cycles not found"
  exit 1
fi
