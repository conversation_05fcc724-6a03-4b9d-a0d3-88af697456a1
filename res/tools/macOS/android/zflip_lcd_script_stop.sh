#!/bin/bash

# Check if the device ID parameter is provided
if [ -z "$1" ]; then
  echo "Usage: ./zflip_lcd_script_stop.sh [Device ID]"
  exit 1
fi

# Assign the device ID parameter to a variable
device_id="$1"

# Run adb commands with the device ID specified
./adb -s "$device_id" shell settings put secure enabled_accessibility_services com.android.shell/com.android.shell.ShellAccessibilityService
./adb -s "$device_id" shell am force-stop com.sec.android.app.hwmoduletest
./adb -s "$device_id" shell am force-stop com.samsung.android.dialer

# Throw success message and exit
echo "success"
exit 0
