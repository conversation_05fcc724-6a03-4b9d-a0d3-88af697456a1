server.port=8082
applicationName=Phonecheck
applicationVersion=3.12.1c
# mqtt configurations
mqtt.scheme=tcp://
mqtt.host=127.0.0.1
mqtt.port=1883
mqtt.server.enabled=false
mqtt.autoReconnect=true
mqtt.maxReconnectDelay=-1
mqtt.connectionTimeout=3600
mqtt.keepAlive=120
mqtt.maxInFlight=32000
mqtt.client.id=phonecheck-backend
# logging configurations
logging.level.com.phonecheck=INFO
logging.path=${user.home}/AppData/Local/PhoneCheck3/logs
# spring configurations
spring.main.lazy-initialization=true
spring.main.web-application-type=servlet
spring.jmx.enabled=false

rest.template.timeout=60000

# phonecheck database
spring.datasource.url=jdbc:h2:${user.home}/AppData/Local/PhoneCheck3/db/data;AUTO_SERVER=TRUE;LOCK_TIMEOUT=4000
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=Ph0neCheck!
spring.sql.init.mode=never
spring.jpa.hibernate.ddl-auto=none
spring.jpa.defer-datasource-initialization=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.datasource.hikari.maximum-pool-size=50
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.jpa.open-in-view=false
spring.task.execution.pool.core-size=64
spring.task.execution.pool.max-size=1024
spring.task.execution.pool.queue-capacity=1024
spring.task.execution.thread-name-prefix=ExecutorThread-
server.tomcat.max-threads=2048
# communicator properties
communicator.redis.port=6379
communicator.redis.host=localhost
management.endpoints.web.exposure.include=*
# api endpoints
cloud.service.base.url=${CLOUD_URL}
eu.cloud.service.base.url=${EU_CLOUD_URL}
cloud.phonecheck.service.base.url=${CLOUD_PHONECHECK_URL}
eu.cloud.phonecheck.service.base.url=${EU_CLOUD_PHONECHECK_URL}
phonecheck.api.base.url=${PHONECHECK_BASE_URL}
eu.phonecheck.api.base.url=${EU_PHONECHECK_BASE_URL}
brightstar.phonecheck.api.base.url=${BRIGHTSTAR_PHONECHECK_URL}
cloud.vpp.base.url=${CLOUD_VAPP_BASE_URL}
japanese.telephony.url=${JAPANESE_TELEPHONY_URL}
