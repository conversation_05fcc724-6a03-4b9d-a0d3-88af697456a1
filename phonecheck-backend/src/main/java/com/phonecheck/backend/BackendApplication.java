package com.phonecheck.backend;

import com.phonecheck.command.system.mac.IgnoreDeviceCommand;
import com.phonecheck.command.system.mac.StopSyncIpodsCommand;
import com.phonecheck.communicator.redis.RedisService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.config.UrlConfigInitializer;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.OsChecker;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.IOException;
import java.util.Locale;

import static com.phonecheck.model.mqtt.heartbeat.MqttHeartbeatIntervals.BACKEND_HEARTBEAT_INTERVAL;
import static com.phonecheck.model.mqtt.heartbeat.MqttHeartbeatIntervals.BACKEND_HEARTBEAT_TIMEOUT;

@SpringBootApplication(scanBasePackages = "com.phonecheck")
@EnableAsync
@EnableScheduling
public class BackendApplication implements CommandLineRunner {
    private static final Logger LOGGER = LoggerFactory.getLogger(BackendApplication.class);

    private static final String STARTING_POINT_INSTALLER = "installer";

    private final IMqttAsyncClient mqttClient;
    private final InMemoryStore inMemoryStore;
    private final RedisService redisService;
    private final CommandExecutor commandExecutor;
    private final OsChecker osChecker;

    public BackendApplication(final IMqttAsyncClient mqttClient,
                              final InMemoryStore inMemoryStore,
                              final RedisService redisService,
                              final CommandExecutor commandExecutor,
                              final OsChecker osChecker) {
        this.mqttClient = mqttClient;
        this.inMemoryStore = inMemoryStore;
        this.redisService = redisService;
        this.commandExecutor = commandExecutor;
        this.osChecker = osChecker;
    }

    public static void main(final String[] args) {
        SpringApplication application = new SpringApplication(BackendApplication.class);
        if (System.getProperty("os.name").toLowerCase(Locale.ROOT).contains("windows")) {
            application.setAdditionalProfiles("windows");
        }

        application.addInitializers(new UrlConfigInitializer());
        application.run(args);
    }

    @Override
    public void run(final String[] args) {
        if (args.length == 1) {
            String startingPoint = args[0];

            System.setProperty("application.type", "backend");
            inMemoryStore.setAppStartedFromInstaller(startingPoint.equals(STARTING_POINT_INSTALLER));
            LOGGER.debug("Backend Application started from Installer");
        } else {
            LOGGER.debug("Backend Application started from IDE");
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationStarted(final ApplicationReadyEvent event) {
        if (mqttClient.isConnected()) {
            redisService.killRedisServer();
            redisService.startRedisServer();

            if (osChecker.isMac()) {
                try {
                    LOGGER.info("Disabling IOS devices showing in the Mac finder");
                    commandExecutor.execute(new IgnoreDeviceCommand(true));
                    commandExecutor.execute(new StopSyncIpodsCommand(true));
                } catch (IOException e) {
                    LOGGER.error("Failed to disable IOS devices showing in the Mac finder", e);
                }
            }

            new Thread(() -> {
                int backendHeartbeatsCompleted = 0;

                try {
                    while ((backendHeartbeatsCompleted += BACKEND_HEARTBEAT_INTERVAL) < BACKEND_HEARTBEAT_TIMEOUT) {
                        // Notify UI
                        final MqttMessage message = new MqttMessage();
                        final String topic = TopicBuilder.buildGenericTopic("backend", "started");
                        mqttClient.publish(topic, message);

                        Thread.sleep(BACKEND_HEARTBEAT_INTERVAL * 1000);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error occurred while publishing backend/started MQTT topic.", e);
                }
            }).start();
        } else {
            LOGGER.error("""
                    MQTT client is not connected to broker. Possible reasons:\s
                    1- Daemon Application not running\s
                    2- Error occurred while connecting to MQTT broker\s
                    Application is closing.""");

            int exitCode = SpringApplication.exit(event.getApplicationContext(), () -> 0);
            System.exit(exitCode);
        }
    }
}
