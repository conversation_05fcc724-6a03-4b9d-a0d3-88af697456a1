package com.phonecheck.backend.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.AbstractCloudDeviceDataSyncService;
import com.phonecheck.backend.util.autoexport.DeviceDataConversionUtil;
import com.phonecheck.model.autoexport.AutoExportData;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.constants.DeviceEraseType;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceState;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.AutoExportStatusMessage;
import com.phonecheck.model.mqtt.messages.PublishableMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.util.DateFormatUtil;
import com.phonecheck.model.util.FunctionalityStatusUtil;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.model.util.TestResultsUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.File;

import static com.phonecheck.model.util.DateFormatUtil.DATE_TIME_FORMAT;
import static com.phonecheck.model.util.DeviceCosmeticResultsUtil.getAllCosmeticTestResults;

/**
 * Service to perform auto export of device data
 */
@Service
public class DeviceDataConversionAndExportService extends AbstractCloudDeviceDataSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceDataConversionAndExportService.class);

    private final OsChecker osChecker;
    private IMqttAsyncClient mqttClient;
    private final ApplicationContext applicationContext;
    private final ObjectMapper mapper;


    public DeviceDataConversionAndExportService(final InMemoryStore inMemoryStore, final OsChecker osChecker,
                                                final IMqttAsyncClient mqttClient,
                                                final ApplicationContext applicationContext,
                                                final ObjectMapper mapper) {
        super(inMemoryStore);
        this.osChecker = osChecker;
        this.applicationContext = applicationContext;
        this.mqttClient = mqttClient;
        this.mapper = mapper;
    }

    /**
     * Trigger auto export from more options
     * @param device
     */
    public void triggerAutoExport(final Device device) {
        // If auto export was enabled then export file
        if (getInMemoryStore().getLocalCustomizations().isAutoExport()) {
            if (StringUtils.isNotBlank(getInMemoryStore().getLocalCustomizations().getExportFilePath())) {
                LOGGER.info("Triggering auto export from UI");
                exportData(device);
            } else {
                LOGGER.info("No export path set, skipping export");
            }
        } else {
            LOGGER.info("Auto export is disabled.");
        }
    }


    /**
     * Export data based on format
     * @param device
     */
    private void exportData(final Device device) {
        if (StringUtils.isNotBlank(getInMemoryStore().getLocalCustomizations().getExportFilePath())) {
            if (getInMemoryStore().getLocalCustomizations().isExportFormatJson()) {
                generateJsonExport(device);
            } else if ((getInMemoryStore().getLocalCustomizations().isExportFormatXml())) {
                generateXmlExport(device);
            } else {
                LOGGER.info("No export format set, skipping export");
            }
        }
    }

    /**
     * If auto export is enabled for test results then trigger it
     * @param device
     */
    public void triggerAutoExportOnTestResults(final Device device) {
        // If auto export was enabled then export file
        if (getInMemoryStore().getLocalCustomizations().isAutoExport() &&
                getInMemoryStore().getLocalCustomizations().isExportOnAppResults()) {
            if (StringUtils.isNotBlank(getInMemoryStore().getLocalCustomizations().getExportFilePath())) {
                if (getInMemoryStore().getLocalCustomizations().isExportFormatJson()) {
                    generateJsonExport(device);
                } else if ((getInMemoryStore().getLocalCustomizations().isExportFormatXml())) {
                    generateXmlExport(device);
                } else {
                    LOGGER.info("No export format set, skipping export");
                }
            }
        } else {
            LOGGER.info("Auto export is disabled.");
        }
    }

    /**
     * If auto export is enabled for erase/restore success then trigger it
     * @param device
     */
    public void triggerAutoExportOnEraseRestore(final Device device) {
        // If auto export was enabled then export file
        if (getInMemoryStore().getLocalCustomizations().isAutoExport() &&
                getInMemoryStore().getLocalCustomizations().isExportOnSuccessfulErase()) {
            if (StringUtils.isNotBlank(getInMemoryStore().getLocalCustomizations().getExportFilePath())) {
                if (getInMemoryStore().getLocalCustomizations().isExportFormatJson()) {
                    generateJsonExport(device);
                } else if ((getInMemoryStore().getLocalCustomizations().isExportFormatXml())) {
                    generateXmlExport(device);
                } else {
                    LOGGER.info("No export format set, skipping export");
                }
            }
        } else {
            LOGGER.info("Auto export is disabled.");
        }
    }

    /**
     * Generate xml file for auto export
     * @param device
     */
    private void generateXmlExport(final Device device) {
        LOGGER.info("Attempting auto export for device in format XML");
        LOGGER.info("Device being exported: {}", device);
        String topic = TopicBuilder.build(device, "auto-export", "status");
        AutoExportStatusMessage autoExportStatusMessage = new AutoExportStatusMessage();
        autoExportStatusMessage.setId(device.getId());
        String exportFilePath = getInMemoryStore().getLocalCustomizations().getExportFilePath();
        try {
            String xmlStr = DeviceDataConversionUtil.convertToXml(mapDeviceObject(device));
            String filename =
                    StringUtils.isBlank(device.getImei()) ?
                            (StringUtils.isBlank(device.getSerial()) ? null : device.getSerial()) : device.getImei();
            if (filename != null) {
                DeviceDataConversionUtil.saveToFile(
                        xmlStr != null ?
                                xmlStr : "", exportFilePath + File.separator + filename + ".xml");
                LOGGER.info("Device data successfully saved to {}.xml",
                        exportFilePath + File.separator + filename);
                autoExportStatusMessage.setExportStatusMessage("Export success");
            } else {
                LOGGER.warn("Cannot save export file as serial and IMEI are both null");
                autoExportStatusMessage.setExportStatusMessage("Export failed: no imei");
            }
        } catch (final Exception e) {
            LOGGER.error("Error occurred while mapping export data", e);
            autoExportStatusMessage.setExportStatusMessage("Export failed: exception");
        }
        publishToMqttTopic(topic, autoExportStatusMessage);
    }

    /**
     * Generate json file for auto export
     * @param device
     */
    private void generateJsonExport(final Device device) {
        LOGGER.info("Attempting auto export for device in format JSON");
        LOGGER.info("Device being exported: {}", device);
        String topic = TopicBuilder.build(device, "auto-export", "status");
        AutoExportStatusMessage autoExportStatusMessage = new AutoExportStatusMessage();
        autoExportStatusMessage.setId(device.getId());
        String exportFilePath = getInMemoryStore().getLocalCustomizations().getExportFilePath();
        try {
            String jsonStr = DeviceDataConversionUtil.convertToJson(mapDeviceObject(device));
            String filename =
                    StringUtils.isBlank(device.getImei()) ?
                            (StringUtils.isBlank(device.getSerial()) ? null : device.getSerial()) : device.getImei();
            if (filename != null) {
                DeviceDataConversionUtil.saveToFile(
                        jsonStr != null ?
                                jsonStr : "", exportFilePath + File.separator + filename + ".json");
                LOGGER.info("Device data successfully saved to {}.json",
                        exportFilePath + File.separator + filename);
                autoExportStatusMessage.setExportStatusMessage("Export success");
            } else {
                LOGGER.warn("Cannot save export file as serial and IMEI are both null");
                autoExportStatusMessage.setExportStatusMessage("Export failed: no imei");
            }
        } catch (final Exception e) {
            LOGGER.error("Error occurred while mapping export data", e);
            autoExportStatusMessage.setExportStatusMessage("Export failed: exception");
        }
        publishToMqttTopic(topic, autoExportStatusMessage);
    }

    protected void publishToMqttTopic(final String topic, final PublishableMessage requestMessage) {
        if (!mqttClient.isConnected()) {
            LOGGER.warn("MQTT client was disconnected from the server");
            LOGGER.info("Assigning a new client to the listener");
            mqttClient = applicationContext.getBean(IMqttAsyncClient.class);
        }
        try {
            LOGGER.info("Publishing message to topic: {}", topic);
            final MqttMessage message = new MqttMessage();
            message.setPayload(mapper.writeValueAsBytes(requestMessage));
            mqttClient.publish(topic, message);
        } catch (MqttException e) {
            LOGGER.error("Could not publish to {}", topic, e);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {}", topic, e);
        }
    }

    /**
     * Map device object to Export data
     *
     * @param device
     * @return auto export data object
     */
    public AutoExportData mapDeviceObject(final Device device) {
        AutoExportData.AutoExportDataBuilder autoExportDataBuilder = AutoExportData.builder();

        boolean isDeviceMicResultsAvailable = device.getDeviceTestResult() != null &&
                device.getDeviceTestResult().getMicrophoneResults() != null;

        final String androidEraseType = DeviceEraseType.ANDROID_ERASE_TYPE.getValue();
        final String iosEraseType = DeviceEraseType.IOS_ERASE_TYPE.getValue();
        final String iosRestoreType = DeviceEraseType.IOS_RESTORE_TYPE.getValue();
        String eraseType = StringUtils.EMPTY;
        if (device instanceof AndroidDevice) {
            if (device.getEraseStartTime() != null) {
                eraseType = androidEraseType;
            }
        } else {
            IosDevice iosDevice = (IosDevice) device;
            if (device.getEraseStartTime() != null) {
                if (StringUtils.isNotBlank(iosDevice.getEraserType())) {
                    eraseType = iosDevice.getEraserType();
                } else {
                    eraseType = iosEraseType;
                }
            } else if (device.getRestoreStartTime() != null) {
                if (StringUtils.isNotBlank(iosDevice.getRestoreType())) {
                    eraseType = iosDevice.getRestoreType();
                } else {
                    eraseType = iosRestoreType;
                }
            }
        }

        // Get all battery test related info
        String batteryDrainDuration = StringUtils.EMPTY;
        String batteryChargeStart = StringUtils.EMPTY;
        String batteryChargeEnd = StringUtils.EMPTY;
        String batteryDrainType = StringUtils.EMPTY;
        String totalDischarge = StringUtils.EMPTY;

        DeviceTestResult deviceTestResult = device.getDeviceTestResult();

        if (deviceTestResult != null && deviceTestResult.getBatteryResults() != null &&
                deviceTestResult.getBatteryResults().getBatteryDrain() != null) {
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration())) {
                batteryDrainDuration = deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery())) {
                batteryChargeStart = deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery())) {
                batteryChargeEnd = deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType())) {
                batteryDrainType = deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDischarge())) {
                totalDischarge = deviceTestResult.getBatteryResults().getBatteryDrain()
                        .getTotalDischarge();
            }
        }

        String pendingTests = StringUtils.EMPTY;
        if (deviceTestResult != null) {
            pendingTests = TestResultsUtil.listToCommaSeparatedString(deviceTestResult.getTestResults().getPending());
        }

        String simLockStatus = Boolean.TRUE.equals(device.getSimLock()) ? "Locked" :
                (Boolean.FALSE.equals(device.getSimLock()) ? "Unlocked" : StringUtils.EMPTY);

        String unlockStatus = StringUtils.isNotBlank(device.getUnlockStatus()) ?
                device.getUnlockStatus() : ("Locked".equalsIgnoreCase(simLockStatus) ?
                "LK" : "Unlocked".equalsIgnoreCase(simLockStatus) ? "UNLK" : StringUtils.EMPTY);

        String eSimErased = device.isESimErased() &&
                ((device.getEraseStartTime() != null && device.getEraseStartTime() > 0) ||
                        (device.getRestoreStartTime() != null && device.getRestoreStartTime() > 0))
                ? "Yes" : "No";

        String getErased = (device.getEraseStartTime() != null && device.getEraseStartTime() > 0
                && device.getEraseEndTime() != null && device.getEraseEndTime() > 0)
                || (device.getRestoreStartTime() != null && device.getRestoreStartTime() > 0
                && device.getRestoreEndTime() != null && device.getRestoreEndTime() > 0)
                ? "Yes" : "No";

        String eraseEndTime = device.getEraseEndTime() != null
                ? DateFormatUtil.millisToUTCDateTime(device.getEraseEndTime(), DATE_TIME_FORMAT)
                : (device.getRestoreEndTime() != null
                ? DateFormatUtil.millisToUTCDateTime(device.getRestoreEndTime(), DATE_TIME_FORMAT)
                : " ");

        String workingStatus;
        String functionalityStatus =
                FunctionalityStatusUtil.getFunctionalityStatus(device, false,
                        getInMemoryStore().getAssignedCloudCustomization());
        if (FunctionalityStatusUtil.FULLY_FUNCTIONAL.equals(functionalityStatus)) {
            workingStatus = "Yes";
        } else if (FunctionalityStatusUtil.SEE_NOTES.equals(functionalityStatus)) {
            workingStatus = "No";
        } else if (FunctionalityStatusUtil.PENDING.equals(functionalityStatus)) {
            workingStatus = "Pending";
        } else {
            workingStatus = StringUtils.EMPTY;
        }

        String manualEntryStatus = device.isManualEntry() ? "Yes" : "No";

        String eraseStartTime;
        if (device.getEraseStartTime() != null) {
            eraseStartTime = DateFormatUtil.millisToUTCDateTime(device.getEraseStartTime(), DATE_TIME_FORMAT);
        } else if (device.getRestoreStartTime() != null) {
            eraseStartTime = DateFormatUtil.millisToUTCDateTime(device.getRestoreStartTime(), DATE_TIME_FORMAT);
        } else {
            eraseStartTime = " ";
        }

        if (device instanceof IosDevice iosDevice) {
            String modelNumber = device.getModelNo() + (iosDevice.getRegionInfo() != null ?
                    iosDevice.getRegionInfo() : StringUtils.EMPTY);
            final MdmStatus deviceMdmStatus = iosDevice.getMdmInfo() == null ? null :
                    iosDevice.getMdmInfo().getMdmStatus();
            autoExportDataBuilder
                    .fMic(isDeviceMicResultsAvailable ?
                            String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getFmAmplitude()) : "")
                    .regulatoryModelNumber(iosDevice.getRegulatoryModelNumber())
                    .network(iosDevice.getSimCarrierBundleInfo() == null ? StringUtils.EMPTY :
                            iosDevice.getSimCarrierBundleInfo().getProvider())
                    .appleId(iosDevice.getCurrentAppleId())
                    .modelNumber(modelNumber)
                    .vMic(isDeviceMicResultsAvailable ?
                            String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getRmAmplitude())
                            : StringUtils.EMPTY)
                    .batterySerial(iosDevice.getOemPartsCurrent() == null
                            ? StringUtils.EMPTY : iosDevice.getOemPartsCurrent().getBatterySerial())
                    .sim1Mcc(iosDevice.getSimCarrierBundleInfo() == null ? StringUtils.EMPTY :
                            iosDevice.getSimCarrierBundleInfo().getMcc())
                    .oemBatteryHealth(iosDevice.getOemBatteryNotice())
                    .mdmResponse(iosDevice.getMdmRawResponse() == null ?
                            StringUtils.EMPTY : iosDevice.getMdmRawResponse())
                    .screenTime(iosDevice.getScreenTime())
                    .osVersion(iosDevice.getProductVersion())
                    .mdm(deviceMdmStatus == null ? StringUtils.EMPTY : deviceMdmStatus.getKey())
                    .sim1mnc(iosDevice.getSimCarrierBundleInfo() == null ? StringUtils.EMPTY :
                            iosDevice.getSimCarrierBundleInfo().getMnc())
                    .bMic(isDeviceMicResultsAvailable ?
                            String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getBmAmplitude()) : "")
                    .appVersion(getInMemoryStore().getIosAppVersion())
                    .build();
        } else {
            boolean isVideoMicResultAvailable = isDeviceMicResultsAvailable && StringUtils.isNotBlank(device.
                    getDeviceTestResult().getMicrophoneResults().getVideoMicrophone())
                    && !"null".equalsIgnoreCase(device.getDeviceTestResult().
                    getMicrophoneResults().getVideoMicrophone());

            boolean isMicrophoneResultAvailable = isDeviceMicResultsAvailable && StringUtils.isNotBlank(device.
                    getDeviceTestResult().getMicrophoneResults().getMicrophone())
                    && !"null".equalsIgnoreCase(device.getDeviceTestResult().
                    getMicrophoneResults().getVideoMicrophone());

            AndroidDevice androidDevice = (AndroidDevice) device;

            autoExportDataBuilder
                    .network(androidDevice.getSimNetwork())
                    .modelNumber(device.getModelNo())
                    .vMic(isVideoMicResultAvailable ?
                            "LS: " + device.getDeviceTestResult().getMicrophoneResults().getVideoMicrophone() :
                            StringUtils.EMPTY)
                    .knox(androidDevice.getKnox() != null ? androidDevice.getKnox().getKey() : "")
                    .osVersion(String.valueOf(androidDevice.getOsMajorVersion() > 0 ?
                            androidDevice.getOsMajorVersion() : ""))
                    .mdm(androidDevice.getMdmStatus() != null ? androidDevice.getMdmStatus().getKey() :
                            StringUtils.EMPTY)
                    .bMic(isMicrophoneResultAvailable ?
                            "LS: " + device.getDeviceTestResult().getMicrophoneResults().getMicrophone() :
                            StringUtils.EMPTY)
                    .appVersion(getInMemoryStore().getAndroidAppVersion())
                    .build();
        }

        // Triple <cosmetic Result, Failed cosmetic result, Passed cosmetic result>
        Triple<String, String, String> cosmeticsResultsTriple =
                getAllCosmeticTestResults(deviceTestResult, getInMemoryStore().getAssignedCloudCustomization());
        String cosmeticResults = null;
        String failedCosmeticTestResults = null;
        String passedCosmeticTestResults = null;
        if (cosmeticsResultsTriple != null) {
            cosmeticResults = cosmeticsResultsTriple.getLeft();
            failedCosmeticTestResults = cosmeticsResultsTriple.getMiddle();
            passedCosmeticTestResults = cosmeticsResultsTriple.getRight();
        }

        autoExportDataBuilder
                .platform(osChecker.getName())
                .memory(device.getDiskSize() == null ? StringUtils.EMPTY : device.getDiskSize().toString())
                .eraseType(eraseType)
                .transactionId(getInMemoryStore().getTransaction().getTransactionId())
                .batteryDrainDuration(batteryDrainDuration)
                .batteryChargeStart(batteryChargeStart)
                .boxNo(getInMemoryStore().getTransaction().getBoxNo())
                .firmware(device.getFirmware())
                .rooted(device.getRooted() == null ? StringUtils.EMPTY : device.getRooted().getKey())
                .color(device.getColor())
                .unlockStatus(unlockStatus)
                .wareHouse(getInMemoryStore().getWarehouseName())
                .esnResponse(device.getEsnRawResponse())
                .eId(device.getEid())
                .vendorName(getInMemoryStore().getTransaction().getVendorName())
                .productCode(device.getProductCode())
                .wifiMacAddress(device.getWifiAddress())
                .make(device.getMake())
                .simSerial2(device.getSimSerial2())
                .fccId(device.getFccid())
                .working(workingStatus)
                .pesn(device.getPesn())
                .batteryModel(device.getBatteryInfo() != null ?
                        device.getBatteryInfo().getModel() : StringUtils.EMPTY)
                .iCloudInfo((device.getSourceApiResponse1() != null ? device.getSourceApiResponse1() + "\n" : "") +
                        (device.getSourceApiResponse2() != null ? device.getSourceApiResponse2() : ""))
                .imei2(device.getImei2())
                .startHeat("") // TODO: not sure what value to set
                .endHeat("")   // TODO: not sure what value to set
                .decimalMeId(device.getMeidDecimal())
                .lpn(device.getLpn())
                .defectsCode(device.getDefectsCode())
                .carrierLockResponse(device.getCarrierLockRawResponse() == null ? StringUtils.EMPTY :
                        device.getCarrierLockRawResponse())
                .stationId(device.getStationId())
                .simSerial(device.getSimSerial())
                .simTechnology(device.getSimTechnology())
                .buildNo(getInMemoryStore().getBuildNo())
                .manualEntry(manualEntryStatus)
                .testerName(getInMemoryStore().getTesterName())
                .transactionDate(getInMemoryStore().getTransaction().getTransactionDate())
                .esn(device.getEsnStatus())
                .eSimPresent(device.isEsimActive() ? "Yes" : "No")
                .batterySource((device.getBatteryInfo() != null &&
                        device.getBatteryInfo().getSource() != null) ?
                        device.getBatteryInfo().getSource().getKey() : StringUtils.EMPTY)
                .invoiceNo(getInMemoryStore().getTransaction().getInvoiceNo())
                .eSimErased(eSimErased)
                .eraseEndTime(eraseEndTime)
                .serial(device.getSerial())
                .deviceState(device.getDeviceState() != null ? device.getDeviceState().getText() :
                        DeviceState.HELLO.getText())
                .simLock(simLockStatus)
                .restoreCode(device.getRestoreCode())
                .model(device.getModel())
                .meId2(device.getMeid2() == null ? StringUtils.EMPTY : device.getMeid2())
                .batteryDrain(totalDischarge)
                .batteryCurrentMaxCapacity(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCurrentCapacity()) : StringUtils.EMPTY)
                .batteryCycle(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCycle()) : StringUtils.EMPTY)
                .countryOfOrigin(device.getCountryOfOrigin())
                .meId(device.getMeid())
                .qty(getInMemoryStore().getTransaction().getQty())
                .cocoCurrentCapacity(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCocoCurrentCapacity()) : null)
                .carrier(device.getCarrier())
                .testPlanName(device.getTestPlan())
                .deviceLock(device.getDeviceLock() == null ? StringUtils.EMPTY : device.getDeviceLock().getKey())
                .batteryTemperature(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getTemperature()) : StringUtils.EMPTY)
                .pesn2(device.getPesn2())
                .batteryPercentage(String.valueOf(device.getBatteryPercentage()))
                .color(device.getColor())
                .grade(device.getGrade())
                .erased(getErased)
                .licenseId(String.valueOf(getInMemoryStore().getTransaction().getLicenseId()))
                .batterChargeEnd(batteryChargeEnd)
                .custom1(device.getCustom1())
                .batteryDrainType(batteryDrainType)
                .batteryDesignMaxCapacity(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getDesignedCapacity()) : StringUtils.EMPTY)
                .portNumber(device.getPortNumber() == null || device.getPortNumber() == -1 ? StringUtils.EMPTY :
                        Integer.valueOf(device.getPortNumber() + 1).toString())
                .simLockResponse(device.getSimLockRawResponse() == null ?
                        StringUtils.EMPTY : device.getSimLockRawResponse())
                .eraseStartTime(eraseStartTime)
                .batteryResistance(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getBatteryResistance()) : StringUtils.EMPTY)
                .udid(device.getId())
                .simErased(((device.getEraseStartTime() != null && device.getEraseStartTime() > 0) ||
                        (device.getRestoreStartTime() != null && device.getRestoreStartTime() > 0) ? "Yes" : "No"))
                .notes(device.getNotes())
                .licenseIdentifier(device.getLicenseIdentifier())
                .batteryHealthPercentage(getBatteryHealthValue(device))
                .os(device.getOperatingSystem())
                .skuCode(device.getSkuCode() != null ? device.getSkuCode() : " ")
                .decimalMEID2(device.getMeidDecimal2())
                .imei(device.getImei())
                .cocoBatteryHealth(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCocoCurrentCapacity()) : "")
                .isMobileCosmetics(
                        getInMemoryStore().getAssignedCloudCustomization().getCosmeticSettings().isEnableCosmetics() &&
                                CloudCustomizationResponse.CosmeticType.QA.equals(
                                        getInMemoryStore().getAssignedCloudCustomization()
                                                .getCosmeticSettings().getCosmeticType()) ?
                                1 : 0
                )
                // TODO: below fields we dont know what to populate
//                .manualFailure()
//                .pcCarrier()
//                .batteryDrainInfo()
//                .sim1Name()
//                .sim2Name()
//                .sim2Mcc()
//                .network1("LTE")
//                .network2("LTE")
//                .cocoDesignCapacity()
                .errorCode(getInMemoryStore().getAssignedCloudCustomization().getName())
                .ram(device.getRam() != null ? device.getRam() : "")
                .gradePerformed(device.isGradePerformed());

        autoExportDataBuilder.failed(
                        StringUtils.defaultString(getAllFailedTestResults(deviceTestResult, failedCosmeticTestResults),
                                StringUtils.EMPTY))
                .passed(
                        StringUtils.defaultString(getAllPassedTestResults(deviceTestResult, passedCosmeticTestResults),
                                StringUtils.EMPTY))
                .pending(pendingTests)
                .cosmeticsPassed(StringUtils.defaultString(passedCosmeticTestResults,
                        StringUtils.EMPTY))
                .cosmeticsFailed(StringUtils.defaultString(failedCosmeticTestResults,
                        StringUtils.EMPTY))
                .cosmetics(StringUtils.defaultString(cosmeticResults,
                        StringUtils.EMPTY));
        AutoExportData deviceData = autoExportDataBuilder.build();

        LOGGER.info("Mapped AutoExportData object: {}", deviceData);
        return deviceData;
    }
}
