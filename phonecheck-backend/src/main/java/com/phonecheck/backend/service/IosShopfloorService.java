package com.phonecheck.backend.service;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.ShopfloorCustomizationResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.AsyncEsnRequestEvent;
import com.phonecheck.model.event.device.ios.IosAppInstallEvent;
import com.phonecheck.model.event.grading.PerformGradingRequestEvent;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.util.CustomizationUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service for handling shopfloor routes and actions for iOS devices.
 */
@Service
public class IosShopfloorService extends ShopfloorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosShopfloorService.class);

    public IosShopfloorService(final ApplicationEventPublisher eventPublisher,
                               final InMemoryStore inMemoryStore,
                               final DeviceActionService deviceActionService,
                               final SourceApiActionService sourceApiActionService,
                               final PerformGradingService performGradingService,
                               final CustomizationUtil customizationUtil) {
        super(eventPublisher, inMemoryStore, deviceActionService, sourceApiActionService,
                performGradingService, customizationUtil);
    }

    /**
     * Proceeds with the iOS shopfloor route based on the provided device and customization data.
     *
     * @param device           the device to process
     * @param shopfloorObjects the list of shopfloor customization data objects
     * @param fromMaster       indicates if the processing is from the master device
     */
    public void proceedForIosShopfloorRoute(final IosDevice device,
                                            final List<ShopfloorCustomizationResponse.ShopfloorObject>
                                                    shopfloorObjects,
                                            final boolean fromMaster) {
        try {
            LOGGER.info("Proceed for IosShopfloorRoute. From master : {}", fromMaster);
            if (shopfloorObjects == null || shopfloorObjects.isEmpty()) {
                handleNoRouteAvailable(fromMaster, device);
                return;
            }

            LOGGER.info("Performing all shopfloor routes");
            for (ShopfloorCustomizationResponse.ShopfloorObject shopObject : shopfloorObjects) {
                if (isMatchingRoute(shopObject, device) && isShopFloorColorMatching(shopObject, device)) {
                    device.setShopfloorObject(shopObject);
                    processMatchingRoute(shopObject, device, fromMaster, shopfloorObjects);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while performing shopfloor for iOS device", e);
        }
    }

    /**
     * Checks if the given shopfloor object matches the route for the device.
     *
     * @param shopObjects the shopfloor object to check
     * @param device      the device to check against
     * @return true if the route matches, false otherwise
     */
    private boolean isMatchingRoute(final ShopfloorCustomizationResponse.ShopfloorObject shopObjects,
                                    final IosDevice device) {
        LOGGER.info("Checking for Matching route shopfloor");

        if (shopObjects.getRoute() != null) {
            return (StringUtils.containsIgnoreCase(shopObjects.getRoute().getOs(), APPLE) ||
                    StringUtils.containsIgnoreCase(shopObjects.getRoute().getOs(), BOTH)) &&
                    (StringUtils.isBlank(shopObjects.getRoute().getCapacity()) ||
                            StringUtils.containsIgnoreCase(shopObjects.getRoute().getCapacity(),
                                    device.getDiskSize().getSize().toString().replace(" ", ""))) &&
                    (StringUtils.isBlank(shopObjects.getRoute().getRegulatoryModel()) ||
                            StringUtils.containsIgnoreCase(shopObjects.getRoute().getRegulatoryModel(),
                                    device.getRegulatoryModelNumber())) &&
                    (StringUtils.containsIgnoreCase(device.getProductType(),
                            shopObjects.getRoute().getAppleCategory()) ||
                            shopObjects.getRoute().getAppleCategory().equalsIgnoreCase(BOTH));
        }
        return false;
    }

    /**
     * Processes a matching route for the device.
     *
     * @param shopObject           the matching shopfloor object
     * @param device               the device to process
     * @param fromMaster           whether the call is from the master
     * @param shopfloorObjectsList the list of shopfloor objects
     */
    private void processMatchingRoute(final ShopfloorCustomizationResponse.ShopfloorObject shopObject,
                                      final IosDevice device,
                                      final boolean fromMaster,
                                      final List<ShopfloorCustomizationResponse.ShopfloorObject>
                                              shopfloorObjectsList) {
        LOGGER.info("Process for Matching route shopfloor {}", shopObject);
        String[] model = shopObject.getRoute().getModel().split(",");
        for (String deviceModel : model) {
            if (StringUtils.isBlank(deviceModel) || deviceModel.trim().equalsIgnoreCase(device.getModel())) {
                processMatchingModelNo(shopObject, device, fromMaster, shopfloorObjectsList);
                return;
            }
        }
    }

    /**
     * Processes a matching model number for the device.
     *
     * @param shopfloorObjects     the shopfloor object with the matching model number
     * @param device               the device to process
     * @param fromMaster           whether the call is from the master
     * @param shopfloorObjectsList the list of shopfloor objects
     */
    private void processMatchingModelNo(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                        final IosDevice device,
                                        final boolean fromMaster,
                                        final List<ShopfloorCustomizationResponse.ShopfloorObject>
                                                shopfloorObjectsList) {
        LOGGER.info("Process for Matching model no shopfloor");
        String[] modelNo = shopfloorObjects.getRoute().getModelNo().split(",");
        for (String deviceModelNo : modelNo) {
            if (StringUtils.isBlank(deviceModelNo) || deviceModelNo.trim().equalsIgnoreCase(device.getModelNo())) {
                handleSelectedRoute(shopfloorObjects, device, fromMaster, shopfloorObjectsList);
                return;
            }
        }
    }

    /**
     * Handles the selected route for the device.
     *
     * @param shopObjects          the shopfloor object with the selected route
     * @param device               the device to process
     * @param fromMaster           whether the call is from the master
     * @param shopfloorObjectsList the list of shopfloor objects
     */
    private void handleSelectedRoute(final ShopfloorCustomizationResponse.ShopfloorObject shopObjects,
                                     final IosDevice device,
                                     final boolean fromMaster,
                                     final List<ShopfloorCustomizationResponse.ShopfloorObject>
                                             shopfloorObjectsList) {
        LOGGER.info("SelectedRoute : {}", shopObjects.getRoute());
        if (StringUtils.containsIgnoreCase(shopObjects.getCriteria().getGrade(), YES)) {
            processGrading(shopObjects, device, fromMaster, shopfloorObjectsList);
        } else {
            setRouteAvailable(shopObjects, device);
        }
    }

    /**
     * Processes grading for the device.
     *
     * @param shopfloorObjects     the shopfloor object with the grading information
     * @param device               the device to process
     * @param fromLast             whether the call is from the last step
     * @param shopfloorObjectsList the list of shopfloor objects
     */
    private void processGrading(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                final IosDevice device,
                                final boolean fromLast,
                                final List<ShopfloorCustomizationResponse.ShopfloorObject> shopfloorObjectsList) {
        LOGGER.info("Process for Grading shopfloor");
        device.setGradingProfileId(String.valueOf(shopfloorObjects.getRoute().getProfileId()));
        if (!device.isGradePerformed()) {
            performGrading(device, fromLast, true);
        } else {
            performGrading(device, fromLast, false);
            shopFloorGoForMatchedGradeRoute(shopfloorObjectsList, device, fromLast);
        }
    }

    /**
     * Performs grading for the device.
     *
     * @param device           the device to grade
     * @param fromMaster       whether the call is from the master
     * @param isGradingPerform whether grading should be performed
     */
    private void performGrading(final IosDevice device,
                                final boolean fromMaster,
                                final boolean isGradingPerform) {
        LOGGER.info("Publish ProcessGradingRequestEvent and handle post grading");
        getEventPublisher().publishEvent(new PerformGradingRequestEvent(this, device, isGradingPerform));
        if (shouldProceedForShopFloorNextActions()) {
            handlePostGrading(device, fromMaster);
        }
    }

    /**
     * Handles post-grading actions for the device.
     *
     * @param device   the device that was graded
     * @param fromLast whether the call is from the last step
     */
    private void handlePostGrading(final IosDevice device,
                                   final boolean fromLast) {
        LOGGER.info("handle post grading, isRouteAvailable {}, fromLast {}", device.isRouteAvailable(), fromLast);
        if (!device.isRouteAvailable() && fromLast) {
            getEventPublisher().publishEvent(new IosAppInstallEvent(this, device, false));
            if (getInMemoryStore().getAssignedCloudCustomization().getImeiCheck().isAutomaticIMEICheck()) {
                getEventPublisher().publishEvent(new AsyncEsnRequestEvent(this, device,
                        EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION));
            }
        }
    }


    /**
     * Sets the route as available for the device.
     *
     * @param shopObjects the shopfloor object with the selected route
     * @param device      the device to update
     */
    private void setRouteAvailable(final ShopfloorCustomizationResponse.ShopfloorObject shopObjects,
                                   final IosDevice device) {
        LOGGER.info("Set route available");
        device.setRouteAvailable(true);
        LOGGER.info("Route for ios shopfloor : {}", shopObjects.getRoute());
        handleShopfloorDefects(shopObjects, device);
    }

    /**
     * Handles shopfloor defects for the device.
     *
     * @param shopfloorObjects the shopfloor object with the defect information
     * @param device           the device to update
     */
    private void handleShopfloorDefects(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                        final IosDevice device) {
        LOGGER.info("Handle shopfloor defects");
        if (shopfloorObjects.getDefectsInfo() != null) {
            LOGGER.info("Handle shopfloor associated defects value {}",
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects());
            device.setGradingAssociateDefects(String.valueOf(
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects()));
            if (shopfloorObjects.getDefectsInfo().getAssociateDefects() == 1) {
                getPerformGradingService().addDataFromGradingDefects(shopfloorObjects);
            }
        }
        handleShopfloorCriteria(shopfloorObjects, device);
    }

    /**
     * Handles shopfloor criteria for the device.
     *
     * @param shopfloorObjects the shopfloor object with the criteria information
     * @param device           the device to update
     */
    private void handleShopfloorCriteria(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                         final IosDevice device) {
        LOGGER.info("Handle shopfloor Criteria");
        if (!shouldProceedForShopFloorNextActions()) {
            return;
        }
        if (StringUtils.containsIgnoreCase(shopfloorObjects.getCriteria().getDiagnostic(), YES)) {
            installDiagnostics(shopfloorObjects, device);
        }
        if (StringUtils.containsIgnoreCase(shopfloorObjects.getCriteria().getEsn(), YES) &&
                getInMemoryStore().getAssignedCloudCustomization().getImeiCheck().isAutomaticIMEICheck()) {
            getEventPublisher().publishEvent(new AsyncEsnRequestEvent(this, device,
                    EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION));
        }
        if (StringUtils.containsIgnoreCase(shopfloorObjects.getCriteria().getErase(), YES)) {
            handleErase(shopfloorObjects, device);
            device.setShouldEraseForShopfloor(true);
        }
        if (StringUtils.containsIgnoreCase(shopfloorObjects.getCriteria().getRestore(), YES)) {
            getDeviceActionService().restoreOperationRequest(device);
        }
    }

    /**
     * Installs diagnostics on the device.
     *
     * @param shopfloorObjects the shopfloor object with the diagnostics information
     * @param device           the device to update
     */
    private void installDiagnostics(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                    final IosDevice device) {
        LOGGER.info("Installing app for diagnostics {}", shopfloorObjects.getRoute().getTestPlans().getApple());
        device.setTestPlan(shopfloorObjects.getRoute().getTestPlans().getApple());
        getEventPublisher().publishEvent(new IosAppInstallEvent(this, device, false));
    }

    /**
     * Handles the erase operation for the device.
     *
     * @param shopfloorObjects the shopfloor object with the erase information
     * @param device           the device to update
     */
    private void handleErase(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                             final IosDevice device) {
        LOGGER.info("Handle erase for shopfloor");
        if (StringUtils.containsIgnoreCase(shopfloorObjects.getCriteria().getDiagnostic(), NO)) {
            if (isPowerOffCustomizationEnabled()) {
                getDeviceActionService().powerOffOperationRequest(device);
            } else {
                LOGGER.info("Grading done for the device? {}", device.isGradePerformed());
                getSourceApiActionService().callResultsOrLabelApi(device);
            }
        } else if (StringUtils.containsIgnoreCase(shopfloorObjects.getCriteria().getRestore(), YES)) {
            getDeviceActionService().restoreOperationRequest(device);
        }
    }

    /**
     * Handles the case when no route is available for the device.
     *
     * @param fromMaster whether the call is from the master
     * @param device     the device to update
     */
    private void handleNoRouteAvailable(final boolean fromMaster,
                                        final IosDevice device) {
        LOGGER.info("Handle no route available for android shopfloor fromMaster {} isRouteAvailable {}", fromMaster,
                device.isRouteAvailable());
        if (!fromMaster) {
            return;
        }
        if (!device.isRouteAvailable()) {
            if (shouldProceedForShopFloorNextActions()) {
                getEventPublisher().publishEvent(new IosAppInstallEvent(this, device, false));
                if (getInMemoryStore().getAssignedCloudCustomization().getImeiCheck().isAutomaticIMEICheck()) {
                    getEventPublisher().publishEvent(new AsyncEsnRequestEvent(this, device,
                            EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION));
                }
            }
            if (getInMemoryStore().getAssignedCloudCustomization().getAdvancedSettings() != null &&
                    getInMemoryStore().getAssignedCloudCustomization().getAdvancedSettings().isShopfloorEnabled() &&
                    isPrintCustomizationEnabled()) {
                device.setRestoreCode("Exception: " + System.lineSeparator() + "Route Not Configured");
                getDeviceActionService().printOperationRequest(device, getInMemoryStore().getLocalCustomizations());
            } else {
                if (!device.isGradePerformed()) {
                    getEventPublisher().publishEvent(new PerformGradingRequestEvent(this, device,
                            true));
                } else {
                    getEventPublisher().publishEvent(new PerformGradingRequestEvent(this, device,
                            false));
                }
            }
        }
    }

    /**
     * Checks if the shopfloor color matches the device color.
     *
     * @param shopfloorObject the shopfloor object to check
     * @param device          the device to check against
     * @return true if the colors match, false otherwise
     */
    private boolean isShopFloorColorMatching(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObject,
                                             final IosDevice device) {
        LOGGER.info("Check for shopfloor color matching");
        final String modelColors = shopfloorObject.getRoute().getModelColors();
        if (StringUtils.isBlank(modelColors)) {
            return true;
        }
        final String deviceColor = device.getColor().trim().toLowerCase();
        return Arrays.stream(modelColors.split(","))
                .map(String::trim)
                .anyMatch(color -> color.equalsIgnoreCase(deviceColor));
    }

    /**
     * Processes the matched grade route for the device.
     *
     * @param shopfloorObjects the list of shopfloor objects
     * @param device           the device to process
     * @param fromMaster       whether the call is from the master
     */
    public void shopFloorGoForMatchedGradeRoute(
            final List<ShopfloorCustomizationResponse.ShopfloorObject> shopfloorObjects,
            final IosDevice device,
            final boolean fromMaster) {
        try {
            LOGGER.info("Go for matched grade route for shopfloor");
            if (shopfloorObjects == null || shopfloorObjects.isEmpty()) {
                handleNoRouteAvailable(fromMaster, device);
                return;
            }
            for (ShopfloorCustomizationResponse.ShopfloorObject shopObjects : shopfloorObjects) {
                if (isMatchingRoute(shopObjects, device) && isShopFloorColorMatching(shopObjects, device)) {
                    if (isMatchingGrade(shopObjects, device.getGrade())) {
                        handleShopfloorCriteria(shopObjects, device);
                        return;
                    }
                }
            }
            handleNoRouteAvailable(fromMaster, device);
        } catch (Exception e) {
            LOGGER.error("Error occurred while processing shop floor route", e);
        }
    }

    /**
     * Checks if the shopfloor object grade matches the device grade.
     *
     * @param shopfloorObjects the shopfloor object to check
     * @param deviceGrade      the device grade to check against
     * @return true if the grades match, false otherwise
     */
    private boolean isMatchingGrade(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                    final String deviceGrade) {
        LOGGER.info("Matching grade for shopfloor grade {}", deviceGrade);
        String[] grades = shopfloorObjects.getRoute().getGrade().split(",");
        for (String grade : grades) {
            if (StringUtils.isBlank(grade) || grade.trim().equalsIgnoreCase(deviceGrade)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if print customization is enabled.
     *
     * @return true if print customization is enabled, false otherwise
     */
    private boolean isPrintCustomizationEnabled() {
        LOGGER.info("Check if print customization enabled");
        return getInMemoryStore().getAssignedCloudCustomization().getWorkflow() != null &&
                getInMemoryStore().getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow() != null &&
                getInMemoryStore().getAssignedCloudCustomization().getWorkflow().isTestResultWorkflowEnabled() &&
                getInMemoryStore().getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow().contains(
                        CloudCustomizationResponse.AutomationSteps.PRINT);
    }

    /**
     * Checks if power-off customization is enabled.
     *
     * @return true if power-off customization is enabled, false otherwise
     */
    private boolean isPowerOffCustomizationEnabled() {
        LOGGER.info("Check if power off customization enabled");
        return getInMemoryStore().getAssignedCloudCustomization().getWorkflow() != null &&
                getInMemoryStore().getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow() != null &&
                getInMemoryStore().getAssignedCloudCustomization().getWorkflow().isTestResultWorkflowEnabled() &&
                getInMemoryStore().getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow().contains(
                        CloudCustomizationResponse.AutomationSteps.POWER_OFF);
    }

    /**
     * Proceeds with the iOS shopfloor route based on the provided device and customization data.
     *
     * @param device           the device to process
     * @param shopfloorObjects the list of shopfloor customization data objects
     */
    public void performMatchedShopfloorRoute(final IosDevice device,
                                             final List<ShopfloorCustomizationResponse.ShopfloorObject>
                                                     shopfloorObjects) {
        try {
            LOGGER.info("Proceed for Ios matched shopfloor Route");
            if (shopfloorObjects == null || shopfloorObjects.isEmpty()) {
                return;
            }
            for (ShopfloorCustomizationResponse.ShopfloorObject shopObjects : shopfloorObjects) {
                if (isMatchingRoute(shopObjects, device) && isShopFloorColorMatching(shopObjects, device)) {
                    processMatchingRouteForIos(shopObjects, device);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while performing matched shopfloor for iOS device", e);
        }
    }

    /**
     * Processes a matching route for the device.
     *
     * @param shopObjects the matching shopfloor object
     * @param device      the device to process
     */
    private void processMatchingRouteForIos(final ShopfloorCustomizationResponse.ShopfloorObject shopObjects,
                                            final IosDevice device) {
        LOGGER.info("Process for Matching route shopfloor for iOS device");
        String[] model = shopObjects.getRoute().getModel().split(",");
        for (String deviceModel : model) {
            if (StringUtils.isBlank(deviceModel) || deviceModel.trim().equalsIgnoreCase(device.getModel())) {
                processMatchingModelNoForIos(shopObjects, device);
                return;
            }
        }
    }

    /**
     * Processes a matching model number for the device.
     *
     * @param shopfloorObjects the shopfloor object with the matching model number
     * @param device           the device to process
     */
    private void processMatchingModelNoForIos(final ShopfloorCustomizationResponse.ShopfloorObject shopfloorObjects,
                                              final IosDevice device) {
        LOGGER.info("Process for Matching route model no shopfloor");
        String[] modelNo = shopfloorObjects.getRoute().getModelNo().split(",");
        for (String deviceModelNo : modelNo) {
            if (StringUtils.isBlank(deviceModelNo) || deviceModelNo.trim().equalsIgnoreCase(device.getModelNo())) {
                String[] grades = shopfloorObjects.getRoute().getGrade().split(",");
                for (String grade : grades) {
                    if (StringUtils.isBlank(grade) || grade.trim().equalsIgnoreCase(device.getGrade())) {
                        setRouteAvailable(shopfloorObjects, device);
                    }
                }
                return;
            }
        }
    }


    /**
     * Finds the best matching route for an iOS device based on the specified source channel.
     *
     * @param sourceChannel The source channel to match against.
     * @param device        The iOS device for which the route is being determined.
     * @return The list of shopfloor objects that match the source channel, or null if no matching route is found.
     */
    public List<ShopfloorCustomizationResponse.ShopfloorObject> findBestMatchingRouteForIos(
            final String sourceChannel, final Device device) {
        List<ShopfloorCustomizationResponse.ShopfloorObject> selectedRoute;
        if (!device.isRouteAvailable()) {
            selectedRoute = getShopObjectsBySourceChannel(
                    getInMemoryStore().getShopfloorResponse().getShopfloorCustomization().getStations(), sourceChannel);
            if (!selectedRoute.isEmpty()) {
                return selectedRoute;
            }

            selectedRoute = getShopObjectsBySourceChannel(
                    getInMemoryStore().getShopfloorResponse().getShopfloorCustomization().getWarehouse(),
                    sourceChannel);
            if (!selectedRoute.isEmpty()) {
                return selectedRoute;
            }

            selectedRoute = getShopObjectsBySourceChannel(
                    getInMemoryStore().getShopfloorResponse().getShopfloorCustomization().getMaster(), sourceChannel);
            if (!selectedRoute.isEmpty()) {
                return selectedRoute;
            }
        }
        return null;
    }


    /**
     * Retrieves shopfloor objects from the given list that match the specified source channel.
     *
     * @param shopData      The list of shopfloor objects to filter.
     * @param sourceChannel The source channel to match against.
     * @return A list of shopfloor objects that match the source channel.
     */
    private List<ShopfloorCustomizationResponse.ShopfloorObject> getShopObjectsBySourceChannel(
            final List<ShopfloorCustomizationResponse.ShopfloorObject> shopData, final String sourceChannel) {
        ArrayList<ShopfloorCustomizationResponse.ShopfloorObject> matchingRoute = new ArrayList<>();
        if (shopData != null && !shopData.isEmpty()) {
            for (ShopfloorCustomizationResponse.ShopfloorObject shopObjects : shopData) {
                if (shopObjects.getRoute().getSourceChannel().equalsIgnoreCase(
                        sourceChannel.replace("\"", ""))) {
                    matchingRoute.add(shopObjects);
                }
            }
        }
        return matchingRoute;
    }

}
