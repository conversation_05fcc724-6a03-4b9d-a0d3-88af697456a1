package com.phonecheck.backend.service;

import com.phonecheck.command.system.mac.GetMacRunningProcessesCommand;
import com.phonecheck.command.system.mac.MacKillProcessCommand;
import com.phonecheck.command.system.windows.GetWindowsRunningProcessesCommand;
import com.phonecheck.command.system.windows.WindowsKillProcessCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.constants.ProcessConstants;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.system.mac.GetMacRunningProcessesParser;
import com.phonecheck.parser.system.windows.GetWindowsRunningProcessesParser;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
@AllArgsConstructor
public class KillApplicationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(KillApplicationService.class);

    private final OsChecker osChecker;
    private final CommandExecutor executor;
    private final GetMacRunningProcessesParser macRunningProcessesParser;
    private final GetWindowsRunningProcessesParser windowsRunningProcessesParser;

    /**
     * Terminates processes associated with the specified package names.
     */
    public void killApplicationProcesses() {
        try {
            // Kill redis server
            String[] redisProcessFilters = {ProcessConstants.REDIS};
            killProcesses("redis", redisProcessFilters);

            // Kill adb process
            String[] adbProcessFilters = {ProcessConstants.ADB_PROCESS};
            killProcesses("adb", adbProcessFilters);

            // Kill all dangling idevice*.exe processes
            String[] iDeviceProcessFilters = {ProcessConstants.IDEVICE};
            killProcesses("idevice", iDeviceProcessFilters);

            String[] restoreFilters = {ProcessConstants.PHONECHECK_RESTORE};
            killProcesses("phonecheckRestore", restoreFilters);

            String[] preCheckFilters = {ProcessConstants.PRE_CHECK};
            killProcesses("precheck", preCheckFilters);

        } catch (IOException e) {
            LOGGER.error("Error occurred while terminating application", e);
        }
    }

    /**
     * Terminates processes named "PhoneCheck" and "PhoneCheck2" every 2 minutes.
     */
    @Scheduled(initialDelay = 20000, fixedRate = 120000)
    public void killPhoneCheckProcesses() {
        try {
            String[] processFilters = {ProcessConstants.PHONECHECK1, ProcessConstants.PHONECHECK2,
                    ProcessConstants.PHONECHECK_SM};
            killProcesses("PhoneCheck", processFilters);
        } catch (IOException e) {
            LOGGER.error("Error occurred while terminating PhoneCheck processes", e);
        }
    }

    /**
     * Method to kill processes started by the application
     *
     * @param keyword        keyword to be used to retrieve processes
     * @param packageFilters process package list based on which we will kill processes retrieved by keyword
     * @throws IOException exception
     */
    public void killProcesses(final String keyword, final String[] packageFilters) throws IOException {
        long currentBackendProcessId = ProcessHandle.current().pid();

        if (osChecker.isMac()) {
            String output = executor.execute(new GetMacRunningProcessesCommand(keyword));
            LOGGER.info("Kill process output: {}", output);
            List<String> processIds = macRunningProcessesParser.parse(output, packageFilters);
            for (String processId : processIds) {
                LOGGER.info("Killing PID: {} for process: {}", processId, keyword);
                if (currentBackendProcessId != Long.parseLong(processId)) {
                    executor.execute(new MacKillProcessCommand(processId));
                }
            }
        } else {
            String output = executor.execute(new GetWindowsRunningProcessesCommand(keyword));
            LOGGER.info("Kill process output: {}", output);
            List<String> processIds = windowsRunningProcessesParser.parse(output, packageFilters);
            for (String processId : processIds) {
                LOGGER.info("Killing PID: {} for process: {}", processId, keyword);
                if (currentBackendProcessId != Long.parseLong(processId)) {
                    executor.execute(new WindowsKillProcessCommand(processId));
                }
            }
        }
    }

}