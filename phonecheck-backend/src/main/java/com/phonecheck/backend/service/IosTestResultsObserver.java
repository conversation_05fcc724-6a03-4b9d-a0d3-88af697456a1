package com.phonecheck.backend.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.app.uninstall.DeviceUninstallAppService;
import com.phonecheck.command.device.ios.test.IosReadFileCommand;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.device.results.IosTestResultsService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.constants.FileConstants;
import com.phonecheck.model.constants.ios.IosAppIdConstants;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.AppTestingDoneStage;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceReconnectRequireEvent;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.event.device.iwatch.IWatchAppTestResultEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceColorChangeMessage;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.mqtt.messages.PublishableMessage;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.*;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.TestResultsUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

import static com.phonecheck.model.constants.ios.Ios32BitDeviceModels.IOS_32_BIT_DEVICES;
import static com.phonecheck.model.twowayapi.TwoWayApiConstants.GRADING_PERFORMED_KEY;

@Component
@AllArgsConstructor
public class IosTestResultsObserver {

    public static final String BATTERY_DRAIN = "Battery Drain";
    private static final Logger LOGGER = LoggerFactory.getLogger(IosTestResultsObserver.class);
    private final ApplicationEventPublisher eventPublisher;
    private final InMemoryStore inMemoryStore;

    private final IosTestResultsService testResultsService;
    private final DeviceTestResultDBService deviceTestResultDBService;
    private final DeviceStageUpdater stageUpdater;
    private final DeviceAutomationQueueService automationQueueService;
    private final CommandExecutor executor;
    private final ObjectMapper mapper;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final ApplicationContext applicationContext;
    private final DeviceDataConversionAndExportService deviceDataConversionAndExportService;
    private final LocalizationService localizationService;
    private IMqttAsyncClient mqttClient;
    private final VendorCriteriaService vendorCriteriaService;
    private final DeviceUninstallAppService deviceUninstallAppService;
    private final IosDeviceInfoService iosDeviceInfoService;

    protected void setDeviceIdMDC(final String deviceId) {
        MDC.clear();
        MDC.put("id", deviceId);
    }

    public void startResultsListener(final IosDevice device) {

        Runnable runnable = () -> {
            setDeviceIdMDC(device.getId());
            LOGGER.info("Starting the test results observer");
            IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
            while (deviceInTracker != null && !deviceInTracker.isRetrievedResults()) {
                String output = null;
                try {
                    if (IOS_32_BIT_DEVICES.contains(deviceInTracker.getProductType())) {
                        output = executor
                                .execute(new IosReadFileCommand(deviceInTracker.getId(),
                                        FileConstants.TEST_RESULTS_FILE_NAME, IosAppIdConstants.APP_STORE_BUNDLE_ID_2));
                    } else {
                        output = executor
                                .execute(new IosReadFileCommand(deviceInTracker.getId(),
                                        FileConstants.TEST_RESULTS_FILE_NAME,
                                        inMemoryStore.getIosAppBundleIdentifier()));
                    }

                } catch (IOException e) {
                    LOGGER.error("Error occurred while reading the test results file for device");
                }

                if (StringUtils.isNotEmpty(output) && !deviceInTracker.isEraseInProgress()
                        && !deviceInTracker.isRestoreInProgress()) {
                    if (output.contains("ERROR: Could not Open AFC File")) {
                        // results not available, continue in the loop
                        LOGGER.debug("File not ready yet");
                    } else if (output.contains("No device found with udid")) {
                        // device unplugged. stop the thread.
                        LOGGER.info("Device disconnected, stopping observer");
                        break;
                    } else if (output.contains("ERROR: Could not connect to lockdownd")) {
                        LOGGER.info("Device not available to read results. Stopping observer");
                        break;
                    } else if (output.contains("BundleID Not Accessable")) {
                        LOGGER.info("Test app deleted. Cant read results. Stopping observer");
                        break;
                    } else if (output.contains("ERROR:")) {
                        LOGGER.info("Device reached an inconsistent state. Cannot recover. Stopping observer");
                        break;
                    } else {
                        if (inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST) {
                            LOGGER.info("Observing IWatch Test results file output: {}", output);
                            LOGGER.info("Setting and syncing test results from observer for iWatch");
                            eventPublisher.publishEvent(new IWatchAppTestResultEvent(this, deviceInTracker));
                        } else {
                            LOGGER.info("Observing Test results file output: {}", output);
                            LOGGER.info("Setting and syncing test results from the observer");
                            setAndSyncTestResults(deviceInTracker);
                        }
                    }
                } else {
                    LOGGER.info("Device may be disconnected, stopping observer");
                    break;
                }
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    // do nothing
                }
                // Exit from the loop when device is removed from the tracker in order to stop this thread
                if (deviceConnectionTracker.getDevice(deviceInTracker.getId()) == null) {
                    break;
                }
            }
            LOGGER.info("Exiting the test results observer");
        };
        new Thread(runnable).start();
    }

    private void setAndSyncTestResults(final IosDevice device) {
        try {
            LOGGER.info("Test results ready on the IOS device.");
            DeviceTestResultStatus deviceTestResultFromFileStatus;
            if (IOS_32_BIT_DEVICES.contains(device.getProductType())) {
                deviceTestResultFromFileStatus = testResultsService.getTestResults(device,
                        IosAppIdConstants.APP_STORE_BUNDLE_ID_2);
            } else {
                deviceTestResultFromFileStatus = testResultsService.getTestResults(device,
                        inMemoryStore.getIosAppBundleIdentifier());
            }

            DeviceTestResult deviceTestResultFromFile = deviceTestResultFromFileStatus.getDeviceTestResult();
            if (deviceTestResultFromFile != null && deviceTestResultFromFile.getTestResults() != null) {
                // Testing is completed from mobile application
                deviceTestResultFromFile.getTestResults().setTestingCompleted(true);

                if (testResultsService.isTestResultsAlreadyRetrieved(device)) {
                    LOGGER.info("Test results already retrieved, exiting test results observer process");
                    return;
                } else {
                    LOGGER.info("New test results were fetched by observer thread from device");
                }

                // Get in-app grade
                String inAppGrade = deviceTestResultFromFile.getGradeResults();
                // if in-app grade is not present then set it to device grade, if that is also blank then check local db
                if (StringUtils.isBlank(inAppGrade)) {
                    inAppGrade = device.getGrade();
                    if (StringUtils.isBlank(inAppGrade)) {
                        inAppGrade = deviceTestResultDBService.getDeviceGrade(
                                String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId());
                    }
                }

                // Set device grade results to device
                deviceTestResultFromFile.setGradeResults(inAppGrade);

                // Updates failed and passed test results in the DeviceTestResult.
                testResultsService.updateFailedTestResults(device, deviceTestResultFromFile);
                testResultsService.updatePassedTestResults(device, deviceTestResultFromFile);

                if (device.getDeviceTestResult() == null) {
                    DeviceTestResult deviceTestResult = new DeviceTestResult();
                    device.setDeviceTestResult(deviceTestResult);
                }
                device.getDeviceTestResult().setTestResults(deviceTestResultFromFile.getTestResults());

                device.getDeviceTestResult().setGradeResults(deviceTestResultFromFile.getGradeResults());
                testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(
                        device, deviceTestResultFromFile.getBatteryResults());
                device.getDeviceTestResult().setMicrophoneResults(deviceTestResultFromFile.getMicrophoneResults());
                device.getDeviceTestResult().setDeviceColor(deviceTestResultFromFile.getDeviceColor());
                if (deviceTestResultFromFile.getCosmeticResults() != null
                        && (deviceTestResultFromFile.getCosmeticResults().getFailed() != null
                        || deviceTestResultFromFile.getCosmeticResults().getPassed() != null
                        || deviceTestResultFromFile.getCosmeticResults().getPending() != null)) {
                    device.getDeviceTestResult().setCosmeticResults(deviceTestResultFromFile.getCosmeticResults());
                } else {
                    DeviceTestResult deviceTestResult = deviceTestResultDBService.getDeviceTestResults(
                            String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId(),
                            String.valueOf(inMemoryStore.getLicenseId()), device.getSerial());
                    device.getDeviceTestResult().setCosmeticResults(
                            deviceTestResult != null ? deviceTestResult.getCosmeticResults() : null);
                }

                LOGGER.info("Test results that are set in device: {}", device.getDeviceTestResult());

                if (StringUtils.isNotEmpty(inAppGrade)) {
                    if (!StringUtils.equals(device.getGradingAssociateDefects(), GRADING_PERFORMED_KEY) &&
                            !(StringUtils.equals(device.getGrade(), device.getGradingSystemGrade()) &&
                                    StringUtils.equals(device.getGrade(),
                                            deviceTestResultFromFile.getGradeResults()))) {
                        device.setGrade(inAppGrade);
                    }
                }

                String color = deviceTestResultFromFile.getDeviceColor();
                if (!StringUtils.equalsIgnoreCase(device.getColor(), color) && StringUtils.isNotBlank(color)) {
                    device.setColor(color);
                    final String topic = TopicBuilder.build(device, "color", "change", "response");
                    final DeviceColorChangeMessage requestMessage = new DeviceColorChangeMessage();
                    requestMessage.setId(device.getId());
                    requestMessage.setColor(color);
                    publishToMqttTopic(topic, requestMessage);
                }

                // Add initial defects including touch id and face id statuses in AppTestResult
                // so that it syncs to cloud and database with other test results.
                TestResultsUtil.prependInitialDefectsToAppResults(device,
                        inMemoryStore.getAssignedCloudCustomization(), localizationService);
                vendorCriteriaService.calculateVendorCriteria(device);

                AppTestingDoneStage stage = AppTestingDoneStage.builder()
                        .id(device.getId())
                        .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                        .licenseId(String.valueOf(inMemoryStore.getLicenseId()))
                        .serial(device.getSerial())
                        .deviceType(device.getDeviceType())
                        .deviceTestResult(device.getDeviceTestResult())
                        .timestamp(System.currentTimeMillis())
                        .ebayRefurbished(device.getEbayRefurbished())
                        .ebayRejection(device.getEbayRejection())
                        .swappaQualified(device.getSwappaQualified())
                        .swappaRejection(device.getSwappaRejection())
                        .amazonRenewed(device.getAmazonRenewed())
                        .amazonRenewedRejection(device.getAmazonRenewedRejection())
                        .backMarketQualified(device.getBackMarketQualified())
                        .backMarketRejection(device.getBackMarketRejection())
//                        .ebayGradeId(device.getEbayGradeId())
//                        .SwappaGradeId(device.getSwappaGradeId())
//                        .AmazonGradeId(device.getAmazonGradeId())
//                        .BackMarketGradeId(device.getBackmarketGradeId())
                        .build();
                stageUpdater.update(stage);

                device.setStage(DeviceStage.APP_TESTING_DONE);
                LOGGER.info("EbayR - {} , SWappaR - {}, BackmarketR - {}, AmazonR - {} , device stage {} ",
                        device.getEbayRejection(), device.getSwappaRejection(), device.getBackMarketRejection(),
                        device.getAmazonRenewedRejection(), device.getStage());
                if (inMemoryStore.getAssignedCloudCustomization().getWifiSettings().isDisconnect()) {
                    // Remove Wifi Profile
                    String ssid = inMemoryStore.getAssignedCloudCustomization().getWifiSettings() != null ?
                            inMemoryStore.getAssignedCloudCustomization().getWifiSettings().getName() : null;
                    deviceUninstallAppService.removeWifiProfile(device, ssid);
                }

                // Update SKU Once
                eventPublisher.publishEvent(new DeviceSkuCodeUpdateEvent(this, device));

                // Notify UI
                final String topic = TopicBuilder.build(device, "tested");
                final DeviceTestedMessage message = new DeviceTestedMessage();
                message.setId(device.getId());
                message.setDeviceTestResult(device.getDeviceTestResult());
                message.setVendorName(device.getVendorName());
                publishToMqttTopic(topic, message);

                // Added 500 ms sleep for test results to fully sync with UI module
                Thread.sleep(500);

                // trigger auto export
                deviceDataConversionAndExportService.triggerAutoExportOnTestResults(device);

                // start test result automation
                automationQueueService
                        .enqueueDeviceAutomationRequest(new DeviceTestResultAutomationEvent(this, device));
            } else {
                LOGGER.error("Failed to fetch test results from the file");

                if (deviceTestResultFromFileStatus.getNotificationStatus() != null &&
                        List.of(NotificationStatus.FAILED_NO_DEVICE, NotificationStatus.LOCKDOWN_FAILED)
                                .contains(deviceTestResultFromFileStatus.getNotificationStatus())) {
                    LOGGER.error("Notification post status: {}",
                            deviceTestResultFromFileStatus.getNotificationStatus());

                    device.setReconnectRequired(true);
                    eventPublisher.publishEvent(
                            new DeviceReconnectRequireEvent(this,
                                    device,
                                    ErrorConstants.SYNC_FAILED)
                    );
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while setting test results", e);
        }
    }

    protected void publishToMqttTopic(final String topic, final PublishableMessage requestMessage) {
        if (!mqttClient.isConnected()) {
            LOGGER.warn("MQTT client was disconnected from the server");
            LOGGER.info("Assigning a new client to the observer");
            mqttClient = applicationContext.getBean(IMqttAsyncClient.class);
        }
        try {
            final MqttMessage message = new MqttMessage();
            message.setPayload(mapper.writeValueAsBytes(requestMessage));
            mqttClient.publish(topic, message);
        } catch (MqttException e) {
            LOGGER.error("Could not publish to {}", topic, e);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {}", topic, e);
        }
    }
}
