package com.phonecheck.backend.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.info.MarketplaceVendor;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.cloudapi.DeviceFeaturesResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.grading.VendorCriteriaStatus;
import com.phonecheck.model.grading.VendorGradeResult;
import com.phonecheck.model.grading.VendorGradingCriteria;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.PublishableMessage;
import com.phonecheck.model.mqtt.messages.VendorInfoMessage;
import com.phonecheck.model.status.EsnStatus;
import com.phonecheck.model.status.KnoxStatus;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.OemStatus;
import com.phonecheck.model.util.FunctionalityStatusUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service class responsible for calculating vendor-specific grading criteria for devices
 * across various marketplaces such as eBay, Amazon, Swappa, and BackMarket.
 */
@Service
public class VendorCriteriaService {
    private static final Logger LOGGER = LoggerFactory.getLogger(VendorCriteriaService.class);
    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;

    @Getter
    private IMqttAsyncClient mqttClient;
    @Getter
    private final ObjectMapper mapper;
    @Autowired
    private ApplicationContext applicationContext;

    public VendorCriteriaService(final InMemoryStore inMemoryStore, final IMqttAsyncClient mqttClient,
                                 final DeviceConnectionTracker deviceConnectionTracker,
                                 final ObjectMapper objectMapper) {
        this.mqttClient = mqttClient;
        this.mapper = objectMapper;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
    }

    public void calculateVendorCriteria(final Device device) {
        try {
            calculateEbayVendorCriteria(device);
            calculateAmazonVendorCriteria(device);
            calculateBackMarketVendorCriteria(device);
            calculateSwappaVendorCriteria(device);
        } catch (Exception ex) {
            LOGGER.error("Error while calculating vendor criteria for device {}",
                    device.getSerial(), ex);
        }
    }

    private void calculateEbayVendorCriteria(final Device device) {
        try {
            calculateVendorCriteria(device, MarketplaceVendor.EBAY);
        } catch (Exception ex) {
            LOGGER.error("Error calculating criteria for device {} and vendor {}",
                    device.getSerial(), MarketplaceVendor.EBAY.getName(), ex);
            setDefaultValues(device, MarketplaceVendor.EBAY);
        }
    }

    private void calculateSwappaVendorCriteria(final Device device) {
        try {
            calculateVendorCriteria(device, MarketplaceVendor.SWAPPA);
        } catch (Exception ex) {
            LOGGER.error("Error calculating Swappa Vendor criteria for device {} and vendor {}",
                    device.getSerial(), MarketplaceVendor.SWAPPA.getName(), ex);
            setDefaultValues(device, MarketplaceVendor.SWAPPA);
        }
    }

    private void calculateAmazonVendorCriteria(final Device device) {
        try {
            calculateVendorCriteria(device, MarketplaceVendor.AMAZON);
        } catch (Exception ex) {
            LOGGER.error("Error calculating AmazonVendor criteria for device {} and vendor {}",
                    device.getSerial(), MarketplaceVendor.AMAZON.getName(), ex);
            setDefaultValues(device, MarketplaceVendor.AMAZON);
        }
    }

    private void calculateBackMarketVendorCriteria(final Device device) {
        try {
            calculateVendorCriteria(device, MarketplaceVendor.BACKMARKET);
        } catch (Exception ex) {
            LOGGER.error("Error calculating BackMarket Vendor criteria for device {} and vendor {}",
                    device.getSerial(), MarketplaceVendor.BACKMARKET.getName(), ex);
            setDefaultValues(device, MarketplaceVendor.BACKMARKET);
        }
    }

    /**
     * Calculates and applies the vendor grading criteria for a device.
     *
     * @param device        the device for which the criteria are calculated
     * @param vendorService the vendor service providing the grading criteria
     */
    private void calculateVendorCriteria(final Device device, final MarketplaceVendor vendorService) {

        List<VendorGradingCriteria.Grade> gradesList = getGradesForVendor(vendorService.getName());
        if (gradesList.isEmpty()) {
            notifyUIWithDefaultValues(device, vendorService);
        } else {
            try {
                VendorGradeResult gradeResult = calculateCriteria(device, vendorService.getName(), gradesList);
                setDeviceInfoFields(device, gradeResult, vendorService);
                notifyUI(device, gradeResult);
            } catch (Exception ex) {
                LOGGER.error("Error while calculating vendor criteria for device {}", device.getSerial(), ex);
                notifyUIWithDefaultValues(device, vendorService);
            }
        }
    }

    /**
     * Calculates the grading criteria for a device based on the vendor's grading standards.
     *
     * @param device     the device for which the grading is performed
     * @param vendorName the name of the vendor providing the grading criteria
     * @param gradesList the list of grading criteria
     * @return {@link VendorGradeResult} containing the final calculated grade and rejection reasons.
     */
    private VendorGradeResult calculateCriteria(final Device device, final String vendorName,
                                                final List<VendorGradingCriteria.Grade> gradesList) {
        for (VendorGradingCriteria.Grade grade : gradesList) {
            boolean allCriteriaPassed = true;
            for (VendorGradingCriteria.GradeCriteria gradeCriteria : grade.getCriteria()) {
                VendorCriteriaStatus criteriaStatus = checkCriteria(gradeCriteria, device);
                LOGGER.info("Criteria status: {}", criteriaStatus);
                gradeCriteria.setCriteriaResult(criteriaStatus);
                if (!isCriteriaConsideredPassed(criteriaStatus)) {
                    allCriteriaPassed = false;
                }
            }
            grade.setQualified(allCriteriaPassed);
        }
        VendorGradeResult gradeResult = determineGradeResult(gradesList);
        final String vendorRejected = determineRejectionReasons(gradesList, device);
        if (StringUtils.isNotBlank(vendorRejected)) {
            gradeResult.setVendorRejected(vendorRejected);
        }
        updateVendorInTracker(device, vendorName, gradeResult);
        LOGGER.info("Grade result at the end:{}", gradeResult);
        return gradeResult;
    }

    /**
     * Retrieves the grading criteria for a specified vendor.
     *
     * @param vendorName the name of the vendor whose grading criteria is to be fetched
     * @return {@link VendorGradingCriteria.Grade} a list or an empty list if the vendor name is invalid
     * or no criteria are available
     */
    private List<VendorGradingCriteria.Grade> getGradesForVendor(final String vendorName) {
        if (vendorName == null || vendorName.trim().isEmpty()) {
            LOGGER.warn("Vendor name is null or empty");
            return new ArrayList<>();
        }
        try {
            List<VendorGradingCriteria.GradingData> vendorGradingData = inMemoryStore.getGradingCriteria().
                    getData().stream()
                    .filter(data -> data.getVendorName().equalsIgnoreCase(vendorName))
                    .toList();
            return vendorGradingData.stream()
                    .flatMap(data -> data.getGrades() == null ? Stream.empty() : data.getGrades().stream())
                    .map(VendorGradingCriteria.Grade::new)
                    .toList();
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Exception occurred while fetching the grades ", e);
            return new ArrayList<>();
        }
    }

    /**
     * Checks a specific grading criterion against the properties of a device.
     *
     * @param gradeCriteria the grading criterion to evaluate
     * @param device        the device to be checked against the criterion
     * @return the {@link VendorCriteriaStatus} representing the result of the evaluation,
     * such as passed, failed, ignored, or not tested
     */
    private VendorCriteriaStatus checkCriteria(final VendorGradingCriteria.GradeCriteria gradeCriteria,
                                               final Device device) {
        if (gradeCriteria.getTests() == null || gradeCriteria.getTests().isEmpty() ||
                gradeCriteria.getAnswers() == null || gradeCriteria.getAnswers().isEmpty()) {
            return VendorCriteriaStatus.CRITERIA_IGNORED;
        }

        LOGGER.info("Vendor criteria :{}", gradeCriteria.getTestTitle());
        String expectedAnswer = gradeCriteria.getAnswers();
        String testRange = gradeCriteria.getTests();

        switch (gradeCriteria.getTestTitle()) {
            case "Battery Health":
                BatteryInfo batteryinfo = device.getBatteryInfo();
                LOGGER.info("Battery Info {}", batteryinfo);
                if (batteryinfo != null) {
                    if (batteryinfo.getHealthPercentage() <= 0) {
                        return VendorCriteriaStatus.CRITERIA_IGNORED;
                    }
                    try {
                        String[] rangeParts = testRange.split("-");
                        int minRange = Integer.parseInt(rangeParts[0]);
                        if (batteryinfo.getHealthPercentage() >= minRange) {
                            return VendorCriteriaStatus.CRITERIA_BATTERY_QUALIFIED;
                        } else {
                            return VendorCriteriaStatus.CRITERIA_BATTERY_NOT_QUALIFIED;
                        }
                    } catch (Exception e) {
                        LOGGER.error("Error while evaluating Battery Health criteria for device {}", e.getMessage(), e);
                        return VendorCriteriaStatus.CRITERIA_BATTERY_IGNORED;
                    }
                } else {
                    LOGGER.warn("Battery info not present");
                    return VendorCriteriaStatus.CRITERIA_IGNORED;
                }

            case "iCloud Locked":
            case "DeviceLock":
                DeviceLock expectedLockStatus = DeviceLock.fromKey(expectedAnswer);
                DeviceLock deviceLock = device.getDeviceLock();
                LOGGER.info("Device Lock Response from GC {}", expectedLockStatus);
                LOGGER.info("Device Lock from device {}", deviceLock);
                if (DeviceLock.NA.equals(expectedLockStatus)) {
                    return VendorCriteriaStatus.CRITERIA_IGNORED;
                }
                if (deviceLock == null) {
                    return device.getDeviceType().isIosDevice()
                            ? VendorCriteriaStatus.CRITERIA_MISSING_I_CLOUD
                            : VendorCriteriaStatus.CRITERIA_MISSING_DEVICE_LOCK;
                }
                if (deviceLock.equals(expectedLockStatus)) {
                    if (deviceLock == DeviceLock.ON) {
                        return device.getDeviceType().isIosDevice()
                                ? VendorCriteriaStatus.CRITERIA_I_DEVICE_LOCK
                                : VendorCriteriaStatus.CRITERIA_DEVICE_LOCK;
                    } else if (deviceLock == DeviceLock.OFF) {
                        return VendorCriteriaStatus.CRITERIA_PASSED;
                    }
                }
                return device.getDeviceType().isIosDevice()
                        ? VendorCriteriaStatus.CRITERIA_I_DEVICE_LOCK
                        : VendorCriteriaStatus.CRITERIA_DEVICE_LOCK;

            case "Rooted":
                RootedStatus deviceRootedStatus = device.getRooted();
                LOGGER.info("Rooted Status for device {}", deviceRootedStatus);
                if (deviceRootedStatus == null) {
                    return device.getDeviceType().isIosDevice()
                            ? VendorCriteriaStatus.CRITERIA_MISSING_DEVICE_JAIL_BREAK
                            : VendorCriteriaStatus.CRITERIA_MISSING_DEVICE_ROOTED;
                }
                if (expectedAnswer.equalsIgnoreCase(RootedStatus.NA.getKey())
                        || deviceRootedStatus == RootedStatus.NA) {
                    return VendorCriteriaStatus.CRITERIA_IGNORED;
                }
                if (expectedAnswer.equalsIgnoreCase(deviceRootedStatus.getKey())) {
                    if (deviceRootedStatus == RootedStatus.ROOTED) {
                        return device.getDeviceType().isIosDevice()
                                ? VendorCriteriaStatus.CRITERIA_DEVICE_JAIL_BREAK
                                : VendorCriteriaStatus.CRITERIA_DEVICE_ROOTED;
                    } else if (deviceRootedStatus == RootedStatus.NOT_ROOTED) {
                        return VendorCriteriaStatus.CRITERIA_PASSED;
                    }
                }
                return device.getDeviceType().isIosDevice()
                        ? VendorCriteriaStatus.CRITERIA_DEVICE_JAIL_BREAK
                        : VendorCriteriaStatus.CRITERIA_DEVICE_ROOTED;

            case "LCD":
            case "Back Camera Quality":
            case "Front Camera Quality":
            case "Volume Up Button":
            case "Volume Down Button":
            case "Power Button":
                if (device.getDeviceTestResult() == null || device.getDeviceTestResult().getTestResults() == null) {
                    return VendorCriteriaStatus.CRITERIA_NOT_TESTED;
                }
                Boolean testingCompleted = device.getDeviceTestResult().getTestResults().getTestingCompleted();
                LOGGER.info("Value of testing completed:{}", testingCompleted);
                if (!Boolean.TRUE.equals(testingCompleted)) {
                    return VendorCriteriaStatus.CRITERIA_NOT_TESTED;
                }

                List<String> passed = device.getDeviceTestResult().getTestResults().getPassed();
                List<String> failed = device.getDeviceTestResult().getTestResults().getFailed();

                if (passed == null) {
                    passed = new ArrayList<>();
                }
                if (failed == null) {
                    failed = new ArrayList<>();
                }
                passed = passed.stream().map(p -> p.replace(" ", ""))
                        .collect(Collectors.toList());
                failed = failed.stream().map(f -> f.replace(" ", ""))
                        .collect(Collectors.toList());

                final String criteriaTest = gradeCriteria.getTests().replace(" ", "");

                LOGGER.info("Passed Tests:{}, Failed Test:{}, criteriaTest:{}", passed, failed, criteriaTest);

                if (!failed.contains(criteriaTest) && !passed.contains(criteriaTest)) {
                    return VendorCriteriaStatus.CRITERIA_MISSING_TEST_REQUIREMENT;
                }

                if (failed.contains(criteriaTest)) {
                    return VendorCriteriaStatus.CRITERIA_FAILED;
                } else {
                    return VendorCriteriaStatus.CRITERIA_PASSED;
                }

            case "Flip Switch":
                if (device instanceof IosDevice) {
                    if (device.getDeviceTestResult() == null || device.getDeviceTestResult().getTestResults() == null) {
                        return VendorCriteriaStatus.CRITERIA_NOT_TESTED;
                    }
                    Boolean testCompleted = device.getDeviceTestResult().getTestResults().getTestingCompleted();
                    if (Boolean.FALSE.equals(testCompleted) || testCompleted == null) {
                        return VendorCriteriaStatus.CRITERIA_NOT_TESTED;
                    }
                    List<String> pass = device.getDeviceTestResult().getTestResults().getPassed();
                    List<String> fail = device.getDeviceTestResult().getTestResults().getFailed();
                    if (testCompleted && !fail.contains(gradeCriteria.getTestTitle())
                            && !pass.contains(gradeCriteria.getTestTitle())) {
                        return VendorCriteriaStatus.CRITERIA_MISSING_TEST_REQUIREMENT;
                    }
                    Map<String, DeviceFeaturesResponse> iosDeviceFeatures = inMemoryStore.getIosDeviceFeatures();
                    DeviceFeaturesResponse deviceFeatures = iosDeviceFeatures.get(((IosDevice) device)
                            .getProductType());
                    if (deviceFeatures == null) {
                        return VendorCriteriaStatus.CRITERIA_FAILED;
                    }
                    if (!deviceFeatures.isFlipSwitch()) {
                        return VendorCriteriaStatus.CRITERIA_IGNORED;
                    } else {
                        List<String> failedResult = device.getDeviceTestResult().getTestResults().getFailed();
                        if (failedResult.contains("Flip Switch")) {
                            return VendorCriteriaStatus.CRITERIA_FAILED;
                        } else {
                            return VendorCriteriaStatus.CRITERIA_PASSED;
                        }
                    }
                } else {
                    return VendorCriteriaStatus.CRITERIA_IGNORED;
                }

            case "Working":
                String workingStatus = getWorkingStatus(device);
                LOGGER.info("device working status {}", workingStatus);
                if (workingStatus.equalsIgnoreCase(expectedAnswer)) {
                    return VendorCriteriaStatus.CRITERIA_PASSED;
                } else {
                    return VendorCriteriaStatus.CRITERIA_FAILED;
                }

            case "ESN":
                EsnStatus actualEsnStatus = EsnStatus.fromKey(device.getEsnStatus());
                EsnStatus expectedEsnStatus = EsnStatus.fromKey(expectedAnswer);
                LOGGER.info("device ESN status {}", actualEsnStatus);
                LOGGER.info("GC ESN status {}", expectedEsnStatus);
                if (actualEsnStatus != null && expectedEsnStatus != null) {
                    if (actualEsnStatus.equals(expectedEsnStatus)) {
                        return VendorCriteriaStatus.CRITERIA_PASSED;
                    } else if (EsnStatus.ESN_BAD.equals(actualEsnStatus)) {
                        return VendorCriteriaStatus.CRITERIA_ESN_BAD;
                    } else {
                        return VendorCriteriaStatus.CRITERIA_IGNORED;
                    }
                }
                return VendorCriteriaStatus.CRITERIA_MISSING_ESN;

            case "MDM":
                MdmStatus actualMdmStatus = device.getMdmStatus();
                MdmStatus expectedMdmStatus = MdmStatus.fromKey(expectedAnswer);
                LOGGER.info("device MDM status {}", actualMdmStatus);
                LOGGER.info("GC MDM status {}", expectedMdmStatus);
                if (actualMdmStatus == null) {
                    return VendorCriteriaStatus.CRITERIA_MISSING_MDM;
                }
                if (actualMdmStatus.equals(expectedMdmStatus) || (actualMdmStatus.equals(MdmStatus.ON_FOR_PHONECHECK)
                        && expectedMdmStatus.equals(MdmStatus.OFF))) {
                    return VendorCriteriaStatus.CRITERIA_PASSED;
                }
                if (actualMdmStatus.equals(MdmStatus.ON)) {
                    return VendorCriteriaStatus.CRITERIA_MDM_ON;
                }
                return VendorCriteriaStatus.CRITERIA_MISSING_MDM;

            case "KNOX":
                KnoxStatus actualKnoxStatus = device.getKnox();
                KnoxStatus expectedKnoxStatus = KnoxStatus.fromKey(expectedAnswer);
                LOGGER.info("device KNOX status {}", actualKnoxStatus);
                LOGGER.info("GC KNOX status {}", expectedKnoxStatus);
                if (actualKnoxStatus == null) {
                    return VendorCriteriaStatus.CRITERIA_MISSING_KNOX;
                }
                if (actualKnoxStatus.equals(expectedKnoxStatus)) {
                    return actualKnoxStatus.equals(KnoxStatus.KNOX_ON)
                            ? VendorCriteriaStatus.CRITERIA_KNOX_ON
                            : VendorCriteriaStatus.CRITERIA_PASSED;
                }
                if (KnoxStatus.KNOX_ON.equals(actualKnoxStatus) && KnoxStatus.KNOX_OFF.equals(expectedKnoxStatus)) {
                    return VendorCriteriaStatus.CRITERIA_KNOX_ON;
                }
                return VendorCriteriaStatus.CRITERIA_MISSING_KNOX;

            case "OEM Status":
                if (device instanceof IosDevice iosDeviceInfo) {
                    if (iosDeviceInfo.getOverallOemStatus() == null) {
                        return VendorCriteriaStatus.CRITERIA_MISSING_OEM;
                    }
                    OemStatus actualOemStatus = iosDeviceInfo.getOverallOemStatus();
                    LOGGER.info("OEM Status : {}", actualOemStatus);
                    if (expectedAnswer.equalsIgnoreCase(OemStatus.NA.getText())) {
                        return OemStatus.NA.equals(actualOemStatus)
                                ? VendorCriteriaStatus.CRITERIA_OEM_NA
                                : VendorCriteriaStatus.CRITERIA_MISSING_OEM;
                    } else if (expectedAnswer.equalsIgnoreCase(OemStatus.GENUINE.getText())) {
                        return OemStatus.GENUINE.equals(actualOemStatus)
                                ? VendorCriteriaStatus.CRITERIA_PASSED
                                : VendorCriteriaStatus.CRITERIA_OEM_NOT_GENUINE;
                    } else if (expectedAnswer.equalsIgnoreCase(OemStatus.NOT_GENUINE.getText())) {
                        return OemStatus.NOT_GENUINE.equals(actualOemStatus)
                                ? VendorCriteriaStatus.CRITERIA_OEM_NOT_GENUINE
                                : VendorCriteriaStatus.CRITERIA_MISSING_OEM;
                    }
                    return VendorCriteriaStatus.CRITERIA_MISSING_OEM;
                } else {
                    return VendorCriteriaStatus.CRITERIA_IGNORED;
                }
            default:
                if ("None can fail".equalsIgnoreCase(expectedAnswer)) {
                    return VendorCriteriaStatus.CRITERIA_NOT_TESTED;
                } else if ("NA".equalsIgnoreCase(expectedAnswer)) {
                    return VendorCriteriaStatus.CRITERIA_IGNORED;
                } else {
                    return VendorCriteriaStatus.CRITERIA_FAILED;
                }
        }
    }

    private String getWorkingStatus(final Device device) {
        String workingStatus;
        if (device.isManualEntry()) {
            workingStatus = device.getManualEntryWorkingStatus();
        } else {
            String functionalityStatus = FunctionalityStatusUtil.getFunctionalityStatus(device, false,
                    inMemoryStore.getAssignedCloudCustomization());
            LOGGER.info("Functionality status Customization : {}", functionalityStatus);
            if (FunctionalityStatusUtil.FULLY_FUNCTIONAL.equals(functionalityStatus)) {
                workingStatus = "Yes";
            } else if (FunctionalityStatusUtil.SEE_NOTES.equals(functionalityStatus)) {
                workingStatus = "No";
            } else if (FunctionalityStatusUtil.PENDING.equals(functionalityStatus)) {
                workingStatus = "Pending";
            } else {
                workingStatus = StringUtils.EMPTY;
            }
        }
        return workingStatus;
    }

    /**
     * Updates the device tracker with grading results for a specified vendor.
     *
     * @param device      the device for which the tracker is updated
     * @param vendorName  the name of the vendor whose grading results are being updated
     * @param gradeResult the grading results containing grade ID, refurbishment status and rejection reasons
     */
    private void updateVendorInTracker(final Device device, final String vendorName,
                                       final VendorGradeResult gradeResult) {
        LOGGER.info("Updating DeviceTracker for device {} with vendor {}", device.getSerial(), vendorName);
        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Grade performed request received but device has been disconnected, stopping processing.");
            return;
        }
        switch (vendorName) {
            case "eBay" -> {
                deviceInTracker.setEbayRefurbished(gradeResult.getVendorRefurbished());
                deviceInTracker.setEbayRejection(gradeResult.getVendorRejected());
                deviceInTracker.setEbayGradeId(gradeResult.getGradeId());
            }
            case "Amazon" -> {
                deviceInTracker.setAmazonRenewed(gradeResult.getVendorRefurbished());
                deviceInTracker.setAmazonRenewedRejection(gradeResult.getVendorRejected());
                deviceInTracker.setAmazonGradeId(gradeResult.getGradeId());
            }
            case "BackMarket" -> {
                deviceInTracker.setBackMarketQualified(gradeResult.getVendorRefurbished());
                deviceInTracker.setBackMarketRejection(gradeResult.getVendorRejected());
                deviceInTracker.setBackmarketGradeId(gradeResult.getGradeId());
            }
            case "Swappa" -> {
                deviceInTracker.setSwappaQualified(gradeResult.getVendorRefurbished());
                deviceInTracker.setSwappaRejection(gradeResult.getVendorRejected());
                deviceInTracker.setSwappaGradeId(gradeResult.getGradeId());
            }
            default -> LOGGER.warn("Unknown vendor: {}", vendorName);
        }
    }

    private boolean isCriteriaConsideredPassed(final VendorCriteriaStatus status) {
        return switch (status) {
            case CRITERIA_PASSED,
                 CRITERIA_IGNORED,
                 CRITERIA_BATTERY_QUALIFIED -> true;
            default -> false;
        };
    }

    /**
     * Returns the grade result based on the vendor's grading.
     *
     * @param grades the list of grades to check
     * @return a {@link VendorGradeResult} with the grade ID, title, and a message.
     * Returns "Not Qualified" if no grades are qualified.
     */
    private VendorGradeResult determineGradeResult(final List<VendorGradingCriteria.Grade> grades) {
        for (VendorGradingCriteria.Grade grade : grades) {
            if (grade.isQualified()) {
                return new VendorGradeResult(grade.getGradeId(), grade.getGradeTitle(), "N/A");
            }
        }
        return new VendorGradeResult(-1, "Not Qualified", "Criteria not met");
    }

    /**
     * Returns a string of rejection reasons based on the grading criteria.
     *
     * @param grades the list of grades to check
     * @param device the device object for which the rejection reasons are being determined
     * @return a string with rejection reasons or an empty string if there are no rejections
     */
    private String determineRejectionReasons(final List<VendorGradingCriteria.Grade> grades, final Device device) {
        Set<String> failedTests = new HashSet<>();
        Set<String> missingTests = new HashSet<>();
        Set<String> workingStatus = new HashSet<>();
        Set<String> missingTestRequirements = new HashSet<>();
        Set<String> notTested = new HashSet<>();
        boolean hasMissingTestRequirement = false;

        for (VendorGradingCriteria.Grade grade : grades) {

            for (VendorGradingCriteria.GradeCriteria criteria : grade.getCriteria()) {
                VendorCriteriaStatus status = criteria.getCriteriaResult();
                String testTitle = criteria.getTestTitle();

                switch (status) {
                    case CRITERIA_FAILED -> {
                        if ("Working".equalsIgnoreCase(testTitle)) {
                            workingStatus.add("Working Status: " + getWorkingStatus(device));
                        } else {
                            failedTests.add(testTitle);
                        }
                    }
                    case CRITERIA_MISSING_ESN, CRITERIA_MISSING_KNOX, CRITERIA_MISSING_MDM, CRITERIA_MISSING_OEM,
                         CRITERIA_MISSING_DEVICE_JAIL_BREAK, CRITERIA_MISSING_DEVICE_ROOTED -> {
                        missingTests.add(testTitle);
                    }
                    case CRITERIA_MISSING_DEVICE_LOCK -> {
                        missingTests.add("DeviceLock");
                    }
                    case CRITERIA_MISSING_I_CLOUD -> {
                        missingTests.add("iCloud Locked");
                    }
                    case CRITERIA_NOT_TESTED -> {
                        notTested.add(testTitle);
                    }
                    case CRITERIA_BATTERY_NOT_QUALIFIED -> {
                        missingTestRequirements.add("Battery Health Not Qualified");
                    }
                    case CRITERIA_DEVICE_JAIL_BREAK, CRITERIA_DEVICE_ROOTED, CRITERIA_KNOX_ON, CRITERIA_MDM_ON -> {
                        missingTestRequirements.add(testTitle + " ON");
                    }
                    case CRITERIA_I_DEVICE_LOCK -> {
                        missingTestRequirements.add("iCloud Locked ON");
                    }
                    case CRITERIA_DEVICE_LOCK -> {
                        missingTestRequirements.add("DeviceLock ON");
                    }
                    case CRITERIA_OEM_NOT_GENUINE -> {
                        missingTestRequirements.add(testTitle + " Not Genuine");
                    }
                    case CRITERIA_OEM_NA -> {
                        missingTestRequirements.add(testTitle + " NA");
                    }
                    case CRITERIA_ESN_BAD -> {
                        missingTestRequirements.add("Bad " + testTitle);
                    }
                    case CRITERIA_MISSING_TEST_REQUIREMENT -> {
                        hasMissingTestRequirement = true;
                    }
                    case CRITERIA_BATTERY_QUALIFIED -> {
                        missingTestRequirements.remove("Battery Health Not Qualified");
                    }
                    default -> {
                    }
                }
            }
        }

        List<String> rejectionReasons = new ArrayList<>();
        if (!failedTests.isEmpty()) {
            rejectionReasons.add("Failed Tests " + String.join(", ", failedTests));
        }
        if (!missingTests.isEmpty()) {
            rejectionReasons.add("Missing ".concat(String.join(", ", missingTests)));
        }
        if (!workingStatus.isEmpty()) {
            rejectionReasons.add(String.join("; ", workingStatus));
        }
        if (!notTested.isEmpty()) {
            rejectionReasons.add("Not Tested " + String.join(", ", notTested));
        }
        if (!missingTestRequirements.isEmpty()) {
            rejectionReasons.add(String.join("; ", missingTestRequirements));
        }
        if (hasMissingTestRequirement) {
            rejectionReasons.add("Missing Test Requirements");
        }
        return String.join("; ", rejectionReasons).trim();
    }

    /**
     * Sets the default values for a device based on the vendor service.
     *
     * @param deviceInfo    the device whose values will be set
     * @param vendorService the vendor service (e.g., EBAY, AMAZON, etc.)
     */
    private void setDefaultValues(final Device deviceInfo,
                                  final MarketplaceVendor vendorService) {
        switch (vendorService) {
            case EBAY -> {
                deviceInfo.setEbayRefurbished("NA");
                deviceInfo.setEbayRejection("NA");
                deviceInfo.setEbayGradeId(-1);
            }
            case AMAZON -> {
                deviceInfo.setAmazonRenewed("NA");
                deviceInfo.setAmazonRenewedRejection("NA");
                deviceInfo.setAmazonGradeId(-1);
            }
            case BACKMARKET -> {
                deviceInfo.setBackMarketQualified("NA");
                deviceInfo.setBackMarketRejection("NA");
                deviceInfo.setBackmarketGradeId(-1);
            }
            case SWAPPA -> {
                deviceInfo.setSwappaQualified("NA");
                deviceInfo.setSwappaRejection("NA");
                deviceInfo.setSwappaGradeId(-1);
            }
            default -> {
                LOGGER.warn("Unknown vendor service: {}", vendorService);
            }
        }
    }

    /**
     * Updates the device fields with grading results based on the vendor service.
     *
     * @param deviceInfo    the device whose fields will be updated
     * @param gradeResult   the grading results to set on the device
     * @param vendorService the vendor service (e.g., EBAY, AMAZON, etc.)
     */
    private void setDeviceInfoFields(final Device deviceInfo,
                                     final VendorGradeResult gradeResult,
                                     final MarketplaceVendor vendorService) {
        switch (vendorService) {
            case EBAY -> {
                deviceInfo.setEbayRefurbished(gradeResult.getVendorRefurbished());
                deviceInfo.setEbayRejection(gradeResult.getVendorRejected());
                deviceInfo.setEbayGradeId(gradeResult.getGradeId());
            }
            case AMAZON -> {
                deviceInfo.setAmazonRenewed(gradeResult.getVendorRefurbished());
                deviceInfo.setAmazonRenewedRejection(gradeResult.getVendorRejected());
                deviceInfo.setAmazonGradeId(gradeResult.getGradeId());
            }
            case BACKMARKET -> {
                deviceInfo.setBackMarketQualified(gradeResult.getVendorRefurbished());
                deviceInfo.setBackMarketRejection(gradeResult.getVendorRejected());
                deviceInfo.setBackmarketGradeId(gradeResult.getGradeId());
            }
            case SWAPPA -> {
                deviceInfo.setSwappaQualified(gradeResult.getVendorRefurbished());
                deviceInfo.setSwappaRejection(gradeResult.getVendorRejected());
                deviceInfo.setSwappaGradeId(gradeResult.getGradeId());
            }
            default -> {
                LOGGER.warn("Unknown vendor service : {}", vendorService);
            }
        }
    }

    private void notifyUI(final Device device, final VendorGradeResult gradeResult) {
        final String topic = TopicBuilder.build(device, "vendor", "criteria");
        VendorInfoMessage vendorinfo = new VendorInfoMessage();
        vendorinfo.setGradeResult(gradeResult);
        vendorinfo.setVendorName(device.getVendorName());
        publishToMqttTopic(topic, vendorinfo);
        LOGGER.info("Notified UI for device {} via topic {}", device.getSerial(), topic);
    }

    private void notifyUIWithDefaultValues(final Device device, final MarketplaceVendor vendorService) {
        setDefaultValues(device, vendorService);
        final String topic = TopicBuilder.build(device, "vendor", "criteria");
        VendorInfoMessage vendorinfo = new VendorInfoMessage();
        vendorinfo.setVendorName(device.getVendorName());
        publishToMqttTopic(topic, vendorinfo);
        LOGGER.info("Notified UI with default values for device {} via topic {}", device.getSerial(), topic);
    }

    protected void publishToMqttTopic(final String topic, final PublishableMessage requestMessage) {
        if (!mqttClient.isConnected()) {
            LOGGER.warn("MQTT client was disconnected from the server");
            LOGGER.info("Assigning a new client to the listener");
            mqttClient = applicationContext.getBean(IMqttAsyncClient.class);
        }
        try {
            LOGGER.info("Publishing message to topic: {}", topic);
            final MqttMessage message = new MqttMessage();
            message.setPayload(mapper.writeValueAsBytes(requestMessage));
            mqttClient.publish(topic, message);
        } catch (MqttException e) {
            LOGGER.error("Could not publish to {}", topic, e);
        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {}", topic, e);
        }
    }

}
