package com.phonecheck.backend.listener.device.iwatch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.iwatch.IWatchGotReplacedEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Listener iwatch got replaced in same transaction
 */
@Component
public class IWatchGotReplacedListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchGotReplacedListener.class);
    private static final Logger IWATCH_LOGGER = LoggerFactory.getLogger("IWatchLogger");

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final CloudDeviceDataSyncService cloudDeviceDataSyncService;

    /**
     * Constructor to initialize dependencies.
     *
     * @param mqttClient                 MQTT client for communication.
     * @param objectMapper               ObjectMapper for JSON processing.
     * @param deviceConnectionTracker    Service to track connected iOS devices.
     * @param inMemoryStore              In-memory store for temporary data.
     * @param cloudDeviceDataSyncService Service for syncing device data to the cloud.
     */
    public IWatchGotReplacedListener(final IMqttAsyncClient mqttClient,
                                     final ObjectMapper objectMapper,
                                     final DeviceConnectionTracker deviceConnectionTracker,
                                     final InMemoryStore inMemoryStore,
                                     final CloudDeviceDataSyncService cloudDeviceDataSyncService) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.cloudDeviceDataSyncService = cloudDeviceDataSyncService;
    }

    /**
     * Handles an event to retrieve battery health data from an iWatch device.
     *
     * @param event The event that triggers battery health data retrieval.
     */
    @Async
    @EventListener
    public void onEvent(final IWatchGotReplacedEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Host device {} disconnected. Aborting battery health retrieval.", device.getId());
            IWATCH_LOGGER.warn("Host device {} disconnected. Aborting battery health retrieval.", device.getId());
            return;
        }

 /*       IWatchFieldsMapperUtil.resetIWatchInfoFromDevice(deviceInTracker);
        deviceInTracker.setIWatchInfo(event.getIWatchInfo());

        final Device syncableDevice = cloudDeviceDataSyncService.getDeviceFromIWatchForSyncing(deviceInTracker);
        String response = cloudDeviceDataSyncService.syncDeviceRecordOnCloud(
                inMemoryStore.getTransaction(), syncableDevice, getClass());

        LOGGER.info("Synced the new device record for the iWatch to cloud db. Response = {}", response);
        IWATCH_LOGGER.info("Synced the new device record for the iWatch to cloud db. Response = {}", response);*/

    }
}
