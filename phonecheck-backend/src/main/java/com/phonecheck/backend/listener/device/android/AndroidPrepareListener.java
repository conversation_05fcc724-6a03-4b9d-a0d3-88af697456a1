package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceActionService;
import com.phonecheck.command.device.android.info.AndroidAtRebrandCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.android.AndroidAtDeviceStreamService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceConnectionAutomationEvent;
import com.phonecheck.model.event.device.android.AndroidAtPrepareStreamParseEvent;
import com.phonecheck.model.event.device.android.AndroidPrepareEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidPrepareResponseMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.peo.android.AndroidPrepareService;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Android device prepare listener to react on the UI prepare request
 * also same listener class is used to parse prepare stream
 */
@Component
public class AndroidPrepareListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidPrepareListener.class);

    private final ApplicationEventPublisher eventPublisher;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final AndroidPrepareService androidPrepareService;
    private final AndroidAtDeviceStreamService androidAtDeviceStreamService;
    private final DeviceActionService deviceActionService;
    private final CommandExecutor executor;

    public AndroidPrepareListener(final ObjectMapper objectMapper,
                                  final IMqttAsyncClient mqttClient,
                                  final ApplicationEventPublisher eventPublisher,
                                  final DeviceConnectionTracker deviceConnectionTracker,
                                  final AndroidPrepareService androidPrepareService,
                                  final AndroidAtDeviceStreamService androidAtDeviceStreamService,
                                  final DeviceActionService deviceActionService,
                                  final CommandExecutor executor) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.androidPrepareService = androidPrepareService;
        this.androidAtDeviceStreamService = androidAtDeviceStreamService;
        this.deviceActionService = deviceActionService;
        this.executor = executor;
    }

    @Async
    @EventListener
    public void onEvent(final AndroidPrepareEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Android prepare requested but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Android prepare event received");

        try {
            if (event.isNotifyStreamThatTestManuOpened()) {
                androidAtDeviceStreamService.writeAtDeviceStream(deviceInTracker, "y");
            } else if ((AndroidConnectionMode.AT.equals(deviceInTracker.getAndroidConnectionMode())
                    && deviceInTracker.isExploitSupported())) {
                deviceInTracker.setStage(DeviceStage.PREPARE_DEVICE_IN_PROGRESS);
                notifyPrepareStatusToUi(deviceInTracker, StringUtils.EMPTY);

                androidPrepareService.startPreparingDevice(deviceInTracker);
            } else {
                if (AndroidConnectionMode.ADB.equals(deviceInTracker.getAndroidConnectionMode())
                        && androidPrepareService.waitForAuthorizationAndPrepareDevice(deviceInTracker)) {
                    device.setAuthorizationStatus(AuthorizationStatus.UNAUTHORIZED);
                    LOGGER.info("Samsung device is prepared.");

                    try {
                        Thread.sleep(5000);
                    } catch (final InterruptedException e) {
                        // do nothing
                    }
                } else {
                    deviceInTracker.setStage(DeviceStage.PREPARE_DEVICE_FAILURE);
                    notifyPrepareStatusToUi(deviceInTracker, StringUtils.EMPTY);
                }

                if (deviceInTracker.getCurrentRunningAutomation() == AutomationWorkflow.CONNECTION) {
                    LOGGER.info("Tried to prepare for connection automation, perform pending steps of the automation");
                    eventPublisher.publishEvent(new DeviceConnectionAutomationEvent(this, deviceInTracker));
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while preparing android at device.", e);
        }
    }

    @Async
    @EventListener
    public void onEvent(final AndroidAtPrepareStreamParseEvent event) throws IOException {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Android prepare stream parsing requested but device has been disconnected," +
                    " stopping processing.");
            return;
        }

        LOGGER.info("Android prepare parse stream request event received for key -> (\"{}\") with data -> ({})",
                event.getAndroidAtStreamKey().getValue(),
                event.getData());

        String data = event.getData();

        switch (event.getAndroidAtStreamKey()) {
            case PORT_UPDATED_KEY, USING_Q_PORT_KEY -> {
                String port = data.substring(data.indexOf(":") + 1).trim();

                deviceInTracker.setPortName(port);
                deviceInTracker.getAtPort().getAPort().setPortName(port);
            }
            case MANUALLY_OPEN_TEST_MENU_KEY -> {
                deviceActionService.emergencyDialerRequest(deviceInTracker);
            }
            case ALREADY_PREPARED_KEY -> {
                androidAtDeviceStreamService.writeAtDeviceStream(deviceInTracker, "y");
            }
            case REBOOTING_FOR_EXPLOIT_KEY -> {
                deviceInTracker.setRestartingForUnlockingOrPrepare(true);
            }
            case REBOOTED_KEY -> {
                deviceInTracker.setRestartingForUnlockingOrPrepare(false);
            }
            case ADB_ACTIVATED_KEY -> {
                if (androidPrepareService.waitForAuthorizationAndPrepareDevice(deviceInTracker)) {

                    LOGGER.info("Samsung device is prepared.");

                    try {
                        Thread.sleep(5000);
                    } catch (final InterruptedException e) {
                        // do nothing
                    }

                    if (deviceInTracker.getCurrentRunningAutomation() == AutomationWorkflow.CONNECTION) {
                        LOGGER.info("Tried to prepare for connection automation," +
                                " perform pending steps of the automation");
                        eventPublisher.publishEvent(new DeviceConnectionAutomationEvent(this, deviceInTracker));
                    }
                } else {
                    deviceInTracker.setStage(DeviceStage.PREPARE_DEVICE_FAILURE);
                    notifyPrepareStatusToUi(deviceInTracker, StringUtils.EMPTY);
                }
            }
            case ERROR_KEY -> {
                if (!data.contains("RESETING CSC FAILED!")) {
                    if (data.contains("TRY AGAIN!")) {
                        androidAtDeviceStreamService.stopAtDeviceStreamRoutine(deviceInTracker);
                        androidPrepareService.startPreparingDevice(deviceInTracker);
                    } else {
                        deviceInTracker.setRestartingForUnlockingOrPrepare(false);
                        deviceInTracker.setStage(DeviceStage.PREPARE_DEVICE_FAILURE);
                        notifyPrepareStatusToUi(deviceInTracker, data);
                    }
                }
            }
            case EXECUTION_COMPLETED_KEY -> {
                if (deviceInTracker.getStage().equals(DeviceStage.PREPARE_DEVICE_IN_PROGRESS)) {
                    executor.execute(new AndroidAtRebrandCommand(deviceInTracker.getPortName(),
                            deviceInTracker.getOriginalCarrier()));
                }
            }
            default -> LOGGER.warn("Key is not matching");
        }
    }

    /**
     * Notify prepare status to Ui
     *
     * @param device        AndroidDevice
     * @param failureReason string explaining the reason of prepare failure
     */
    private void notifyPrepareStatusToUi(final AndroidDevice device, final String failureReason) {
        // Notify UI
        final String frpTopic = TopicBuilder.build(device, "android", "prepare", "status");
        final AndroidPrepareResponseMessage message = new AndroidPrepareResponseMessage();
        message.setId(device.getId());
        message.setSetupDoneStatus(device.getSetupDoneStatus());
        message.setAndroidConnectionMode(device.getAndroidConnectionMode());
        message.setSupported(device.isExploitSupported());
        message.setDeviceStage(device.getStage());
        message.setFailureReason(failureReason);
        publishToMqttTopic(frpTopic, message);
    }
}
