package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.stage.ImeiCollectionSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.SourceApiCallRequestEventV2;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoNeededEvent;
import com.phonecheck.model.event.device.android.AndroidDeviceLockInfoEvent;
import com.phonecheck.model.event.device.android.AndroidImeiAcquisitionSuccessEvent;
import com.phonecheck.model.event.device.android.AndroidPostImeiAcquisitionEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidImeiAcquisitionSuccessMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.util.CustomizationUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import static com.phonecheck.model.twowayapi.TwoWayApiConstants.*;

@Component
public class AndroidImeiAcquisitionSuccessListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidImeiAcquisitionSuccessListener.class);

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final DeviceStageUpdater deviceStageUpdater;
    private final ApplicationEventPublisher eventPublisher;

    private final CustomizationUtil customizationUtil;

    public AndroidImeiAcquisitionSuccessListener(final ObjectMapper objectMapper,
                                                 final IMqttAsyncClient mqttClient,
                                                 final DeviceConnectionTracker deviceConnectionTracker,
                                                 final InMemoryStore inMemoryStore,
                                                 final DeviceStageUpdater deviceStageUpdater,
                                                 final ApplicationEventPublisher eventPublisher,
                                                 final CustomizationUtil customizationUtil) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.deviceStageUpdater = deviceStageUpdater;
        this.eventPublisher = eventPublisher;
        this.customizationUtil = customizationUtil;
    }

    @EventListener
    public void onEvent(final AndroidImeiAcquisitionSuccessEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Android post imei acquisition event received. Device processing will continue");

        // Update stage here
        ImeiCollectionSuccessStage successStage = ImeiCollectionSuccessStage.builder()
                .imei(deviceInTracker.getImei())
                .imei2(deviceInTracker.getImei2())
                .id(deviceInTracker.getId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .build();

        deviceStageUpdater.updateStage(successStage);

        // Notify UI
        final String topic = TopicBuilder.build(device, "imei", "acquisition", "success");
        AndroidImeiAcquisitionSuccessMessage message = new AndroidImeiAcquisitionSuccessMessage();
        message.setId(device.getId());
        message.setImei(deviceInTracker.getImei());
        message.setImei2(deviceInTracker.getImei2());
        publishToMqttTopic(topic, message);

        boolean isSourceApiEnabled = customizationUtil.isSourceAPIEnabled();

        if (isSourceApiEnabled) {
            // Both below events need to be called Synchronously when source API is enabled
            final AndroidBatteryInfoNeededEvent batteryInfoNeededEvent
                    = new AndroidBatteryInfoNeededEvent(this, deviceInTracker);
            eventPublisher.publishEvent(batteryInfoNeededEvent);

            final AndroidDeviceLockInfoEvent frpInfoEvent
                    = new AndroidDeviceLockInfoEvent(this, deviceInTracker);
            eventPublisher.publishEvent(frpInfoEvent);

        } else {
            // Async event to fetch battery info
            new Thread(() -> {
                final AndroidBatteryInfoNeededEvent batteryInfoNeededEvent
                        = new AndroidBatteryInfoNeededEvent(this, deviceInTracker);
                eventPublisher.publishEvent(batteryInfoNeededEvent);
            }).start();

            // Async event for Frp info collection
            new Thread(() -> {
                final AndroidDeviceLockInfoEvent frpInfoEvent
                        = new AndroidDeviceLockInfoEvent(this, deviceInTracker);
                eventPublisher.publishEvent(frpInfoEvent);
            }).start();
        }

        boolean hasReprocessAction = false;
        if (shouldSourceApiBeCalled(deviceInTracker)) {
            LOGGER.info("Raising source API call event for the device.");
            eventPublisher.publishEvent(
                    new SourceApiCallRequestEventV2(this, deviceInTracker));
            final String apiResponse = deviceInTracker.getSourceApiResponse1();
            if (StringUtils.isNotBlank(apiResponse)) {
                try {
                    JsonNode jsonNode = getMapper().readTree(apiResponse);
                    final String action = jsonNode.path(NEXT_ACTION).asText(NA);
                    LOGGER.info("Post source API call, next action was:{}", action);
                    hasReprocessAction = StringUtils.equals(action, RE_PROCESS);
                } catch (JsonProcessingException ee) {
                    LOGGER.error("Exception while reading source API response.", ee);
                }
            }
        }

        if (isSourceApiEnabled && (hasReprocessAction || deviceInTracker.isErasedBySourceApi())) {
            LOGGER.info("Source API has been called and next action is ReProcess or device is Erased by API. " +
                    "Hence returning from here");
            return;
        }

        LOGGER.info("We are continuing processing of device here post source API call or check");
        eventPublisher.publishEvent(
                new AndroidPostImeiAcquisitionEvent(this, deviceInTracker, event.isShouldInstallApps()));
    }

    /**
     * This method checks all the condition to determine
     * if source API should be called for the device
     *
     * @param deviceInTracker : Device in action
     * @return true if source API should be called
     *
     */
    private boolean shouldSourceApiBeCalled(final AndroidDevice deviceInTracker) {
        if (customizationUtil.isSourceAPIEnabled()) {
            LOGGER.info("Required settings are enabled in cloud customization for source API call");

            /* SOURCE API should not be called if device is locked or any of the process
               like Grading / Restore /Erase is being performed on the device */
            if ((deviceInTracker.getIsErasePerformed() == null || !deviceInTracker.getIsErasePerformed()) &&
                    !deviceInTracker.isEraseInProgress() && !deviceInTracker.isRestartInProgress() &&
                    !deviceInTracker.isRestoreInProgress() && deviceInTracker.getDeviceLock() !=  DeviceLock.ON &&
                    !deviceInTracker.isGradePerformed()) {
                LOGGER.info("Source API should be called for the device");
                return true;
            }
        }
        return false;
    }
}
