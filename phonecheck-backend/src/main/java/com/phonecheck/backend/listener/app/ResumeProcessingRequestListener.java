package com.phonecheck.backend.listener.app;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.event.app.ResumeProcessingRequestEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class ResumeProcessingRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResumeProcessingRequestListener.class);
    private final InMemoryStore inMemoryStore;


    public ResumeProcessingRequestListener(final IMqttAsyncClient mqttClient,
                                           final ObjectMapper objectMapper,
                                           final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
    }

    /**
     * Event to set resume processing for OTA to true
     *
     * @param event ResumeProcessingRequestEvent
     */
    @Async
    @EventListener
    public void onEvent(final ResumeProcessingRequestEvent event) {
        LOGGER.info("Resume processing request event");
        inMemoryStore.setResumeProcessingForOta(true);
    }
}
