package com.phonecheck.backend.listener.manual;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.event.device.ManualEntrySyncRequestEvent;
import com.phonecheck.model.manual.ManualEntryModel;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.ManualEntryUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Initiate when manual entries inserted from UI, inserts manual entry data into
 * the local database and synchronizes it with the cloud.
 */
@Component
public class ManualEntrySyncListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualEntrySyncListener.class);

    private final InMemoryStore inMemoryStore;
    private final CloudDeviceDataSyncService cloudDeviceDataSyncService;

    public ManualEntrySyncListener(final ObjectMapper objectMapper,
                                   final IMqttAsyncClient mqttClient,
                                   final InMemoryStore inMemoryStore,
                                   final CloudDeviceDataSyncService cloudDeviceDataSyncService) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.cloudDeviceDataSyncService = cloudDeviceDataSyncService;
    }

    /**
     * Event from UI to sync manual entry to cloud
     *
     * @param event DeviceLpnUpdateRequestEvent from UI
     */
    @Async
    @EventListener
    public void onEvent(final ManualEntrySyncRequestEvent event) {
        LOGGER.info("Manual entry sync request received for data : {}", event.getManualEntries());

        for (ManualEntryModel manualEntry : event.getManualEntries()) {

            // Setup device from manual entry record and sync with cloud s
            cloudDeviceDataSyncService.syncDeviceRecordToCloud(ManualEntryUtil.setupDeviceFromManualEntry(manualEntry),
                    inMemoryStore.getTransaction());
        }
    }
}
