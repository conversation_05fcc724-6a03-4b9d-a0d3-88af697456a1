package com.phonecheck.backend.listener.shopfloor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.AndroidShopfloorService;
import com.phonecheck.backend.service.IosShopfloorService;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ResultsApiCallRequestEvent;
import com.phonecheck.model.event.shopfloor.PerformMatchedRouteShopfloorEvent;
import com.phonecheck.model.event.shopfloor.PerformShopfloorRequestEvent;
import com.phonecheck.model.event.shopfloor.ProceedShopfloorRequestEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.util.CustomizationUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


/**
 * Listener class that handles requests to perform shopfloor operations for devices.
 */
@Component
public class PerformShopfloorRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PerformShopfloorRequestListener.class);
    private final InMemoryStore inMemoryStore;
    private final IosDeviceInfoService iosDeviceInfoService;
    private final IosShopfloorService iosShopfloorService;
    private final AndroidShopfloorService androidShopfloorService;
    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final CustomizationUtil customizationUtil;
    private final ApplicationEventPublisher  eventPublisher;

    protected PerformShopfloorRequestListener(final IMqttAsyncClient mqttClient,
                                              final ObjectMapper objectMapper,
                                              final InMemoryStore inMemoryStore,
                                              final IosDeviceInfoService deviceInfoService,
                                              final AndroidDeviceInfoService androidDeviceInfoService,
                                              final IosShopfloorService iosShopfloorService,
                                              final AndroidShopfloorService androidShopfloorService,
                                              final DeviceConnectionTracker deviceConnectionTracker,
                                              final CustomizationUtil customizationUtil,
                                              final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.iosDeviceInfoService = deviceInfoService;
        this.androidDeviceInfoService = androidDeviceInfoService;
        this.iosShopfloorService = iosShopfloorService;
        this.androidShopfloorService = androidShopfloorService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.customizationUtil = customizationUtil;
        this.eventPublisher = eventPublisher;
    }

    /**
     * Asynchronously handles the PerformShopfloorRequestEvent.
     *
     * @param event The PerformShopfloorRequestEvent to handle.
     */
    @Async
    @EventListener
    public void onEvent(final PerformShopfloorRequestEvent event) {
        final Device deviceInTracker = deviceConnectionTracker.getDevice(event.getDevice().getId());
        if (deviceInTracker == null) {
            LOGGER.info("Device is null when perform shopfloor request received");
            return;
        }
        LOGGER.info("Request to perform shopfloor");
        deviceInTracker.setStage(DeviceStage.PERFORM_SHOPFLOOR);
        notifyStatusLabelToUi(deviceInTracker, DeviceStage.PERFORM_SHOPFLOOR);
        if (DeviceType.IPHONE.equals(deviceInTracker.getDeviceType()) ||
                DeviceType.IPAD.equals(deviceInTracker.getDeviceType())) {
            performShopfloorForIos((IosDevice) deviceInTracker);
        } else if (DeviceType.ANDROID.equals(deviceInTracker.getDeviceType())) {
            performShopfloorForAndroid((AndroidDevice) deviceInTracker);
        }
    }

    @Async
    @EventListener
    public void onEvent(final ProceedShopfloorRequestEvent event) {
        final Device deviceInTracker = deviceConnectionTracker.getDevice(event.getDevice().getId());
        if (deviceInTracker == null) {
            LOGGER.info("Device is null when proceed shopfloor request received");
            return;
        }
        LOGGER.info("Request to proceed shopfloor");
        iosShopfloorService.proceedForIosShopfloorRoute((IosDevice) deviceInTracker, event.getRoutes(),
                event.isFromMaster());
    }

    /**
     * Asynchronously handles the PerformMatchedRouteShopfloorEvent.
     *
     * @param event The PerformMatchedRouteShopfloorEvent to handle.
     */
    @Async
    @EventListener
    public void onEvent(final PerformMatchedRouteShopfloorEvent event) {
        final Device deviceInTracker = deviceConnectionTracker.getDevice(event.getDevice().getId());
        if (deviceInTracker == null) {
            LOGGER.info("Device is null when perform matched shopfloor route request received");
            return;
        }
        LOGGER.info("Request to perform matched shopfloor route ");
        if (DeviceType.IPHONE.equals(deviceInTracker.getDeviceType()) ||
                DeviceType.IPAD.equals(deviceInTracker.getDeviceType())) {
            performMatchedRouteShopfloorForIos((IosDevice) deviceInTracker);
        } else if (DeviceType.ANDROID.equals(deviceInTracker.getDeviceType())) {
            performMatchedRouteShopfloorForAndroid((AndroidDevice) deviceInTracker);
        }

        /*
            On connection - Data verification,
            Advanced setting - Shopfloor and result api enabled and
            shopfloor grading is performed, then call Results API.
         */
        if (deviceInTracker.isGradePerformed() && customizationUtil.isShopfloorCustomizationEnabled() &&
                customizationUtil.isResultsApiEnabled() &&
                customizationUtil.isDataVerificationStepOnConnection()) {
            LOGGER.info("On connection, Data verification only. Shop floor and Results API is enabled");
            eventPublisher.publishEvent(new ResultsApiCallRequestEvent(this, deviceInTracker, false));
        }
    }

    /**
     * Performs shopfloor operations specific to an iOS device.
     *
     * @param device The iOS device for which shopfloor operations are performed.
     */
    private void performShopfloorForIos(final IosDevice device) {
        if (!iosDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device)) {
            if (device.getShopfloorObject() == null || (!device.isRouteAvailable() &&
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects() != 1)) {
                iosShopfloorService.proceedForIosShopfloorRoute(device,
                        inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getStations(),
                        false);
            }
            if (device.getShopfloorObject() == null || (!device.isRouteAvailable() &&
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects() != 1)) {
                iosShopfloorService.proceedForIosShopfloorRoute(device,
                        inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getWarehouse(),
                        false);
            }
            if (device.getShopfloorObject() == null || (!device.isRouteAvailable() &&
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects() != 1)) {
                iosShopfloorService.proceedForIosShopfloorRoute(device,
                        inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getMaster(), true);
            }
        }
    }

    /**
     * Performs shopfloor operations specific to an Android device.
     *
     * @param device The Android device for which shopfloor operations are performed.
     */
    private void performShopfloorForAndroid(final AndroidDevice device) {
        if (!(androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device))) {
            if (device.getShopfloorObject() == null || (!device.isRouteAvailable() &&
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects() != 1)) {
                androidShopfloorService.proceedForAndroidShopfloorRoute(device,
                        inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getStations(),
                        false);
            }
            if (device.getShopfloorObject() == null || (!device.isRouteAvailable() &&
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects() != 1)) {
                androidShopfloorService.proceedForAndroidShopfloorRoute(device,
                        inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getWarehouse(),
                        false);
            }
            if (device.getShopfloorObject() == null || (!device.isRouteAvailable() &&
                    device.getShopfloorObject().getDefectsInfo().getAssociateDefects() != 1)) {
                androidShopfloorService.proceedForAndroidShopfloorRoute(device,
                        inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getMaster(),
                        true);
            }
        }
    }

    /**
     * Performs matched route shopfloor operations specific to an iOS device.
     *
     * @param device The iOS device for which shopfloor operations are performed.
     */
    private void performMatchedRouteShopfloorForIos(final IosDevice device) {
        if (!device.isRouteAvailable()) {
            iosShopfloorService.performMatchedShopfloorRoute(device,
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getStations());
        }
        if (!device.isRouteAvailable()) {
            iosShopfloorService.performMatchedShopfloorRoute(device,
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getWarehouse());
        }
        if (!device.isRouteAvailable()) {
            iosShopfloorService.performMatchedShopfloorRoute(device,
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getMaster());
        }
    }

    /**
     * Performs matched route shopfloor operations specific to an Android device.
     *
     * @param device The Android device for which shopfloor operations are performed.
     */
    private void performMatchedRouteShopfloorForAndroid(final AndroidDevice device) {
        if (!device.isRouteAvailable()) {
            androidShopfloorService.performMatchedShopfloorRoute(device,
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getStations());
        }
        if (!device.isRouteAvailable()) {
            androidShopfloorService.performMatchedShopfloorRoute(device,
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getWarehouse());
        }
        if (!device.isRouteAvailable()) {
            androidShopfloorService.performMatchedShopfloorRoute(device,
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getMaster());
        }
    }
}