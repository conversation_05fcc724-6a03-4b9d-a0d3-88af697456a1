package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.stage.FrpInfoSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidDeviceLockInfoSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceLockStatusMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class AndroidDeviceLockInfoSuccessListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDeviceLockInfoSuccessListener.class);
    private final DeviceConnectionTracker deviceConnectionTracker;

    private final DeviceStageUpdater stageUpdater;
    private final InMemoryStore inMemoryStore;


    public AndroidDeviceLockInfoSuccessListener(final ObjectMapper objectMapper,
                                                final IMqttAsyncClient mqttClient,
                                                final DeviceConnectionTracker deviceConnectionTracker,
                                                final DeviceStageUpdater stageUpdater,
                                                final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);

        this.deviceConnectionTracker = deviceConnectionTracker;
        this.stageUpdater = stageUpdater;
        this.inMemoryStore = inMemoryStore;
    }

    @EventListener
    public void onEvent(final AndroidDeviceLockInfoSuccessEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Device lock info retrieved but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Android device lock info success event received");

        // Update frp status in database
        final FrpInfoSuccessStage frpInfoSuccessStage = FrpInfoSuccessStage.builder()
                .id(device.getId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .deviceLockStatus(deviceInTracker.getDeviceLock().getKey())
                .timestamp(System.currentTimeMillis())
                .build();
        stageUpdater.updateStage(frpInfoSuccessStage);

        // Notify UI
        final String frpTopic = TopicBuilder.build(deviceInTracker, "frp", "response");
        final DeviceLockStatusMessage message = new DeviceLockStatusMessage();
        message.setId(deviceInTracker.getId());
        message.setDeviceLock(deviceInTracker.getDeviceLock());
        message.setPinLockEnabled(deviceInTracker.isPinLockEnabled());
        publishToMqttTopic(frpTopic, message);
    }
}
