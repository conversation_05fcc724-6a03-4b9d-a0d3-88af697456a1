package com.phonecheck.backend.listener.app;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.KillApplicationService;
import com.phonecheck.device.connection.android.GetAndroidDevicesThread;
import com.phonecheck.device.connection.ios.GetIosDevicesThread;
import com.phonecheck.model.event.app.ExitApplicationEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ExitApplicationMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class ExitApplicationListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExitApplicationListener.class);

    private final GetIosDevicesThread iosDevicesThread;
    private final GetAndroidDevicesThread androidDevicesThread;
    private final KillApplicationService killAppService;

    protected ExitApplicationListener(final IMqttAsyncClient mqttClient,
                                      final ObjectMapper objectMapper,
                                      final GetIosDevicesThread iosDevicesThread,
                                      final GetAndroidDevicesThread androidDevicesThread,
                                      final KillApplicationService killAppService) {
        super(mqttClient, objectMapper);
        this.iosDevicesThread = iosDevicesThread;
        this.androidDevicesThread = androidDevicesThread;
        this.killAppService = killAppService;
    }

    @Async
    @EventListener
    public void onEvent(final ExitApplicationEvent event) {
        LOGGER.info("Shutting down request received.");

        // Stop devices thread so that no new processes create after daemon and ui close
        if (iosDevicesThread != null) {
            iosDevicesThread.quit();
        }
        if (androidDevicesThread != null) {
            androidDevicesThread.quit();
        }

        // Initiate service to kill processes
        killAppService.killApplicationProcesses();

        publishToMqttTopic(TopicBuilder.buildGenericTopic("exit", "daemon"),
                new ExitApplicationMessage());

        try {
            Thread.sleep(1500);
        } catch (InterruptedException e) {
            // do nothing
        }

        // After killing daemon and UI, exit from backend
        System.exit(0);
    }
}
