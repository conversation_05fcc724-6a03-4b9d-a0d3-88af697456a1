package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.FirmwareStatusVerificationEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.FirmwareStatusMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Listener class for firmware status verification request
 */
@Component
public class FirmwareStatusVerificationListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirmwareStatusVerificationListener.class);
    private final InMemoryStore inMemoryStore;

    public FirmwareStatusVerificationListener(final IMqttAsyncClient mqttClient,
                                              final ObjectMapper objectMapper,
                                              final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
    }

    @Async
    @EventListener
    public void onEvent(final FirmwareStatusVerificationEvent event) {
        try {
            Map<String, FirmwareModel.FirmwareResponse> firmwareModels = inMemoryStore.getFirmwareModels();

            Map<String, FirmwareDownloadStatus> firmwareDownloadStatusMap =
                    firmwareModels.entrySet().stream().
                            collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getDownloadStatus()));

            LOGGER.info("Sending map:{}", firmwareDownloadStatusMap);

            FirmwareStatusMessage requestMessage = new FirmwareStatusMessage();
            requestMessage.setFirmwareDownloadStatusMap(firmwareDownloadStatusMap);
            String topic = TopicBuilder.buildGenericTopic("firmware-status", "response");
            publishToMqttTopic(topic, requestMessage);
        } catch (Exception e) {
            LOGGER.error("Error while fetching current state of firmwares", e);
        }

    }
}
