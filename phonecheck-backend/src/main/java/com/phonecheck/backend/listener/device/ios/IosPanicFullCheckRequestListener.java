package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;;
import com.phonecheck.info.ios.PreCheckStreamService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosPanicFullCheckEvent;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class IosPanicFullCheckRequestListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosPanicFullCheckRequestListener.class);

    private final PreCheckStreamService preCheckStreamService;
    private final DeviceConnectionTracker deviceConnectionTracker;

    protected IosPanicFullCheckRequestListener(final IMqttAsyncClient mqttClient,
                                               final ObjectMapper objectMapper,
                                               final PreCheckStreamService preCheckStreamService,
                                               final DeviceConnectionTracker deviceConnectionTracker) {
        super(mqttClient, objectMapper);
        this.preCheckStreamService = preCheckStreamService;
        this.deviceConnectionTracker = deviceConnectionTracker;
    }


    /**
     * Execute pre-check stream and read panic full test result
     *
     * @param event IosPanicFullCheckEvent
     */
    @Async
    @EventListener
    public void onEvent(final IosPanicFullCheckEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Pre check to be performed but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("PreCheck initiated for ios device to read panic full result");
        preCheckStreamService.readPreCheckStreamForPanicFullResult(
                (IosDevice) event.getDevice());

    }
}
