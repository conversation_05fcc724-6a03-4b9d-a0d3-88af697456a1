package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceAutomationService;
import com.phonecheck.device.poweroff.DevicePowerOffService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceState;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceManualPowerOffCustomAutomationEvent;
import com.phonecheck.model.event.device.DeviceManualPowerOffRequestEvent;
import com.phonecheck.model.event.device.DevicePowerOffRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DevicePowerOffResponseMessage;
import com.phonecheck.model.status.PowerOffStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class DevicePowerOffRequestListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(DevicePowerOffRequestListener.class);
    private final DevicePowerOffService devicePowerOffService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceAutomationService automationService;
    private final DeviceAutomationQueueService automationQueueService;

    private final IosDeviceInfoService iosDeviceInfoService;

    private static final int RETRIES = 15;

    public DevicePowerOffRequestListener(final DevicePowerOffService devicePowerOffService,
                                         final IMqttAsyncClient mqttClient,
                                         final DeviceConnectionTracker deviceConnectionTracker,
                                         final ObjectMapper objectMapper,
                                         final DeviceAutomationService automationService,
                                         final DeviceAutomationQueueService automationQueueService,
                                         final IosDeviceInfoService iosDeviceInfoService) {
        super(mqttClient, objectMapper);
        this.devicePowerOffService = devicePowerOffService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.automationService = automationService;
        this.automationQueueService = automationQueueService;
        this.iosDeviceInfoService = iosDeviceInfoService;
    }

    /**
     * Handles device power off request event
     *
     * @param event DevicePowerOffRequestEvent
     */
    @EventListener
    public void onEvent(final DevicePowerOffRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Power off requested but the device has been disconnected, stopping the process.");
            return;
        }

        LOGGER.info("Initiate power off for the device");

        handlePowerOffRequest(deviceInTracker);
    }

    /**
     * Handles device manual power off request event
     *
     * @param event DeviceManualPowerOffRequestEvent
     */
    @Async
    @EventListener
    public void onEvent(final DeviceManualPowerOffRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Power off requested but the device has been disconnected, stopping the process.");
            return;
        }

        LOGGER.info("Initiate manual power off for the device");

        boolean shouldTriggerPowerOff = automationService.shouldRunManualPowerOffAutomation(deviceInTracker);
        if (shouldTriggerPowerOff) {
            automationQueueService.enqueueDeviceAutomationRequest(new DeviceManualPowerOffCustomAutomationEvent(
                    this, deviceInTracker));
        } else {
            handlePowerOffRequest(deviceInTracker);
        }
    }

    /**
     * Handle power off request, either it is manual power off or not
     *
     * @param device device
     */
    private void handlePowerOffRequest(final Device device) {

        PowerOffStatus powerOffStatus = PowerOffStatus.FAILED_UNKNOWN;
        String powerOffSuccessTopic = null;

        if (device instanceof IosDevice iosDevice) {

            try {
                for (int i = 1; i <= RETRIES; i++) {
                    if (device.getDeviceState() == DeviceState.HOME
                            || iosDeviceInfoService.isDeviceOnHello(iosDevice)) {
                        break;
                    }
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                LOGGER.error("Error while checking device state before power-off", e);
            }
            powerOffStatus = devicePowerOffService.powerOffDevice(iosDevice);
            powerOffSuccessTopic = TopicBuilder.build(iosDevice, "power-off", "success");
        } else if (device instanceof AndroidDevice androidDevice) {
            powerOffStatus = devicePowerOffService.powerOffAndroid(androidDevice);
            powerOffSuccessTopic = TopicBuilder.build(androidDevice, "power-off", "success");
        }

        LOGGER.info("Device power off status: {}", powerOffStatus);
        // If power off fails notify UI for the power off error
        if (!PowerOffStatus.POWER_OFF_SUCCESS.equals(powerOffStatus)) {
            notifyError(device, powerOffStatus.getLocalizedKey());
        } else {
            final DevicePowerOffResponseMessage message = new DevicePowerOffResponseMessage();
            message.setId(device.getId());
            publishToMqttTopic(powerOffSuccessTopic, message);
        }
    }
}
