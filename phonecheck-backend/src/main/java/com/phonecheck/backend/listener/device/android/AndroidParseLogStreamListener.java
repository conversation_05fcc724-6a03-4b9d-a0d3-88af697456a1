package com.phonecheck.backend.listener.device.android;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.Cloud3DeviceDataSyncService;
import com.phonecheck.api.cloud.CloudDeviceService;
import com.phonecheck.app.android.AndroidAlertSliderService;
import com.phonecheck.app.android.AndroidAppService;
import com.phonecheck.app.android.AndroidFingerPrintService;
import com.phonecheck.app.android.AndroidZFlipDisplayAutomationService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.dao.service.EraserInfoDBService;
import com.phonecheck.device.android.wifi.AndroidWifiAutoDisconnectService;
import com.phonecheck.device.erase.AndroidEraseService;
import com.phonecheck.device.results.AndroidTestResultsService;
import com.phonecheck.model.android.EraseOperation;
import com.phonecheck.model.cloudapi.SyncedCloudDevice;
import com.phonecheck.model.constants.android.AndroidAppPackageConstants;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.SyncableDevice;
import com.phonecheck.model.device.stage.AppTestingDoneStage;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.event.device.android.AndroidEraseSuccessEvent;
import com.phonecheck.model.event.device.android.AndroidManualEraseRequestEvent;
import com.phonecheck.model.event.device.android.AndroidParseLogStreamEvent;
import com.phonecheck.model.event.device.android.XiaomiEraseRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceColorChangeMessage;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidEraseResponseMessage;
import com.phonecheck.model.mqtt.messages.android.ShowHideManualAppInstallMessage;
import com.phonecheck.model.status.DeviceEraseStatus;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.status.WifiStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.syslog.android.AndroidSysLogKey;
import com.phonecheck.model.syslog.android.AndroidSysLogKeyType;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.DeviceTestResultStatus;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.TestResultsUtil;
import com.phonecheck.parser.device.android.erase.AndroidEraseOperationsParser;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.phonecheck.model.android.EraseOperation.ERASE;
import static com.phonecheck.model.android.EraseOperation.FACTORY_RESET;
import static com.phonecheck.model.util.TestResultsUtil.didTestResultsChange;


/**
 * Listener to start parsing of log stream
 */
@Component
public class AndroidParseLogStreamListener extends AbstractListener {
    public static final String BATTERY_DRAIN = "Battery Drain";
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidParseLogStreamListener.class);
    private static final Logger PCUTILITY_LOGGER = LoggerFactory.getLogger("PcUtilityLogger");
    private static final String CANT_FIND_DEVICE_ERROR_MSG = "cantFindDeviceReconnect";
    private static final String XIAOMI = "Xiaomi";

    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final AndroidEraseOperationsParser androidEraseOperationsParser;
    private final AndroidEraseService androidEraseService;
    private final EraserInfoDBService eraserInfoDBService;
    private final AndroidTestResultsService testResultsService;
    private final DeviceTestResultDBService deviceTestResultDBService;
    private final AndroidFingerPrintService fingerPrintService;
    private final AndroidAppService appService;
    private final AndroidAlertSliderService alertSliderService;
    private final AndroidZFlipDisplayAutomationService zFlipDisplayAutomationService;
    private final Cloud3DeviceDataSyncService cloud3DeviceDataSyncService;
    private final DeviceStageUpdater stageUpdater;
    private final DeviceAutomationQueueService automationQueueService;
    private final ApplicationEventPublisher eventPublisher;
    private final AndroidWifiAutoDisconnectService androidWifiAutoDisconnectService;
    private final CloudDeviceService cloudDeviceService;
    private final DeviceDataConversionAndExportService deviceDataConversionAndExportService;
    private final LocalizationService localizationService;
    private final VendorCriteriaService vendorCriteriaService;

    public AndroidParseLogStreamListener(final IMqttAsyncClient mqttClient,
                                         final ObjectMapper objectMapper,
                                         final InMemoryStore inMemoryStore,
                                         final DeviceConnectionTracker deviceConnectionTracker,
                                         final AndroidTestResultsService testResultsService,
                                         final DeviceTestResultDBService deviceTestResultDBService,
                                         final DeviceStageUpdater stageUpdater,
                                         final AndroidEraseOperationsParser androidEraseOperationsParser,
                                         final AndroidEraseService androidEraseService,
                                         final EraserInfoDBService eraserInfoDBService,
                                         final AndroidFingerPrintService fingerPrintService,
                                         final AndroidAppService appService,
                                         final AndroidAlertSliderService alertSliderService,
                                         final AndroidZFlipDisplayAutomationService zFlipDisplayAutomationService,
                                         final Cloud3DeviceDataSyncService cloud3DeviceDataSyncService,
                                         final DeviceAutomationQueueService automationQueueService,
                                         final ApplicationEventPublisher eventPublisher,
                                         final AndroidWifiAutoDisconnectService androidWifiAutoDisconnectService,
                                         final CloudDeviceService cloudDeviceService,
                                         final DeviceDataConversionAndExportService
                                                 deviceDataConversionAndExportService,
                                         final LocalizationService localizationService,
                                         final VendorCriteriaService vendorCriteriaService) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
        this.androidEraseOperationsParser = androidEraseOperationsParser;
        this.androidEraseService = androidEraseService;
        this.eraserInfoDBService = eraserInfoDBService;
        this.testResultsService = testResultsService;
        this.deviceTestResultDBService = deviceTestResultDBService;
        this.fingerPrintService = fingerPrintService;
        this.appService = appService;
        this.alertSliderService = alertSliderService;
        this.zFlipDisplayAutomationService = zFlipDisplayAutomationService;
        this.cloud3DeviceDataSyncService = cloud3DeviceDataSyncService;
        this.automationQueueService = automationQueueService;
        this.stageUpdater = stageUpdater;
        this.androidWifiAutoDisconnectService = androidWifiAutoDisconnectService;
        this.cloudDeviceService = cloudDeviceService;
        this.deviceDataConversionAndExportService = deviceDataConversionAndExportService;
        this.localizationService = localizationService;
        this.vendorCriteriaService = vendorCriteriaService;
    }

    @Async
    @EventListener
    public void onEvent(final AndroidParseLogStreamEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        final AndroidSysLogKey androidSysLogKey = event.getAndroidSysLogKey();
        final String sysLogResponse = event.getSysLogResponse();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Trying to parse log stream but device has been disconnected, stopping processing.");
            return;
        }

        if (androidSysLogKey.getType() == AndroidSysLogKeyType.ERASE) {
            androidEraseSyslogHandler(deviceInTracker, sysLogResponse, androidSysLogKey);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.FACTORY_RESET &&
                AndroidSysLogKey.START_FACTORY_RESET.getKey().equals(androidSysLogKey.getKey())) {
            LOGGER.info("Raising AndroidEraseRequest event based on Factory Reset request sent from the Test app");
            eventPublisher.publishEvent(new AndroidManualEraseRequestEvent(this, device));
        } else if (AndroidSysLogKey.START_READING_TEST_RESULTS.getKey().equals(androidSysLogKey.getKey())) {
            setAndSyncTestResults(deviceInTracker);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.FINGER_PRINT) {
            performFingerprintOperations(deviceInTracker, androidSysLogKey);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.DEVICE_INFO) {
            deviceInTracker.setPcUtilityDeviceInfoRetrieved(true);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.ALERT_SLIDER) {
            // start testing alert slider
            deviceInTracker.setAlertSliderUpPerformed(false);
            deviceInTracker.setAlertSliderMiddlePerformed(false);
            deviceInTracker.setAlertSliderDownPerformed(false);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.ALERT_SLIDER_UP) {
            deviceInTracker.setAlertSliderUpPerformed(true);
            alertSliderService.performAlertSliderOperations(deviceInTracker, androidSysLogKey);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.ALERT_SLIDER_MIDDLE) {
            deviceInTracker.setAlertSliderMiddlePerformed(true);
            alertSliderService.performAlertSliderOperations(deviceInTracker, androidSysLogKey);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.ALERT_SLIDER_DOWN) {
            deviceInTracker.setAlertSliderDownPerformed(true);
            alertSliderService.performAlertSliderOperations(deviceInTracker, androidSysLogKey);
        } else if (androidSysLogKey.getType() == AndroidSysLogKeyType.Z_FLIP_SCREEN_TEST) {
            zFlipDisplayAutomationService.runZflipAutomation(deviceInTracker, androidSysLogKey);
        }
    }

    /**
     * Handles the output of android erase syslog
     *
     * @param device           target device
     * @param sysLogResponse   syslog response string
     * @param androidSysLogKey syslog key
     */
    private void androidEraseSyslogHandler(final AndroidDevice device,
                                           final String sysLogResponse,
                                           final AndroidSysLogKey androidSysLogKey) {

        final String topic = TopicBuilder.build(device, "erase", "response");
        AndroidEraseResponseMessage message = new AndroidEraseResponseMessage();
        device.setStage(DeviceStage.ERASE_IN_PROGRESS);
        message.setId(device.getId());
        message.setAndroidConnectionMode(device.getAndroidConnectionMode());

        try {
            LOGGER.info("EraserSyslogResponse: {} ", sysLogResponse);
            PCUTILITY_LOGGER.info("EraserSyslogResponse: {} ", sysLogResponse);
            if (sysLogResponse.startsWith(AndroidSysLogKey.START_READING_ERASE_STATUS.getKey())
                    || sysLogResponse.endsWith(AndroidSysLogKey.END_READING_ERASE_STATUS.getKey())) {
                final Map<String, Boolean> operationStatusMap = androidEraseOperationsParser.parse(sysLogResponse);

                if (operationStatusMap != null && !operationStatusMap.isEmpty()) {
                    Map.Entry<String, Boolean> entry = operationStatusMap.entrySet().iterator().next();
                    boolean isCompleted = entry.getValue();
                    String operationKey = entry.getKey();
                    EraseOperation operation = EraseOperation.getByValue(operationKey);

                    if (operation != null) {
                        switch (operation) {
                            case E_SIM_ERASE:
                                if (isCompleted && device.isEsimActive()) {
                                    device.setESimErased(true);
                                } else {
                                    LOGGER.warn("Esim could not be erased. No internet connection.");
                                    PCUTILITY_LOGGER.info("Esim could not be erased. No internet connection.");
                                    device.setESimErased(false);
                                }
                                break;
                            case ASK_FACTORY_RESET_PERMISSION:
                                if (isCompleted) {
                                    LOGGER.info("Starting FACTORY_RESET operation," +
                                            " inside ASK_FACTORY_RESET_PERMISSION");
                                    PCUTILITY_LOGGER.info("Starting FACTORY_RESET operation," +
                                            " inside ASK_FACTORY_RESET_PERMISSION");
                                    androidEraseService.startOperation(device, FACTORY_RESET.getValue());
                                    if (StringUtils.equalsIgnoreCase(device.getMake(), XIAOMI)) {
                                        eventPublisher.publishEvent(new XiaomiEraseRequestEvent(this, device));
                                    } else {
                                        androidEraseService.startOperation(device, FACTORY_RESET.getValue());
                                    }
                                }
                                break;
                            case FACTORY_RESET:
                                if (isCompleted) {
                                    synchronized (device) {
                                        LOGGER.info("Completed FACTORY_RESET operation");
                                        PCUTILITY_LOGGER.info("Completed FACTORY_RESET operation");
                                        if (device.getEraseCheckedStatus() == DeviceEraseStatus.ERASE_FAILED_UNKNOWN) {
                                            LOGGER.warn("Erase process failed earlier, skipping success notification.");
                                            PCUTILITY_LOGGER.warn("Erase process failed earlier, " +
                                                    "skipping success notification.");
                                            break;
                                        }
                                        notifyEraseSuccess(device);
                                    }
                                } else if (!StringUtils.equalsIgnoreCase(device.getMake(), XIAOMI) ||
                                        (StringUtils.equalsIgnoreCase(device.getMake(), XIAOMI) &&
                                                device.isSetXiaomiAppAdminProcessCompleted())) {
                                    LOGGER.info("Failed during FACTORY_RESET operation");
                                    PCUTILITY_LOGGER.warn("Failed during FACTORY_RESET operation");
                                    device.setEraseCheckedStatus(DeviceEraseStatus.ERASE_FAILED_UNKNOWN);
                                    message.setEraseStatus(DeviceEraseStatus.ERASE_FAILED_UNKNOWN);
                                    eraserInfoDBService.insertEraserInfo(device);
                                    publishToMqttTopic(topic, message);
                                }

                                // Sync device Data to the cloud
                                syncDeviceDataToCloud(device);
                            default:
                                break;
                        }
                    }
                }
            } else if (AndroidSysLogKey.BEGINNING_OF_CRASH.getKey().equals(androidSysLogKey.getKey())) {
                LOGGER.info("Starting ERASE operation, inside beginning of crash");
                PCUTILITY_LOGGER.info("Starting ERASE operation, inside beginning of crash");
                androidEraseService.startOperation(device, ERASE.getValue());
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    // do nothing
                }
                LOGGER.info("Starting FACTORY_RESET operation");
                PCUTILITY_LOGGER.info("Starting FACTORY_RESET operation");
                androidEraseService.startOperation(device, FACTORY_RESET.getValue());

            } else if (AndroidSysLogKey.WIPE_DATA.getKey().equals(androidSysLogKey.getKey())) {
                synchronized (device) {
                    LOGGER.info("Completed FACTORY_RESET, inside wipe data");
                    PCUTILITY_LOGGER.info("Completed FACTORY_RESET, inside wipe data");
                    notifyEraseSuccess(device);
                }
            }

        } catch (IOException e) {
            LOGGER.error("Exception occurred during erase", e);
            message.setEraseStatus(DeviceEraseStatus.ERASE_FAILED_UNKNOWN);
            device.setEraseInProgress(false);
            publishToMqttTopic(topic, message);
            eraserInfoDBService.insertEraserInfo(device);

            // Sync device Data to the cloud
            syncDeviceDataToCloud(device);
        }
    }

    /**
     * Get android device test results from device
     *
     * @param device Android device
     */
    private void setAndSyncTestResults(final AndroidDevice device) {
        try {
            LOGGER.info("Test results ready on the Android device.");
            DeviceTestResultStatus deviceTestResultFromFileStatus = testResultsService.getTestResults(device);

            DeviceTestResult deviceTestResultFromFile = deviceTestResultFromFileStatus.getDeviceTestResult();
            if (deviceTestResultFromFile != null && deviceTestResultFromFile.getTestResults() != null) {
                LOGGER.info("Device test results from file : {}", deviceTestResultFromFile.getTestResults());

                if (device.getPreviouslyRanAutomation().containsKey(AutomationWorkflow.TEST_RESULTS) &&
                        !didTestResultsChange(deviceTestResultFromFile, device.getDeviceTestResult())) {
                    LOGGER.info("Test results were already synced and did not change, wont re-trigger automation");
                    return;
                }

                if (StringUtils.containsIgnoreCase(device.getMake(), XIAOMI)) {
                    final String topic = TopicBuilder.build(device, "manual", "app", "instructions");
                    final ShowHideManualAppInstallMessage requestMessage =
                            new ShowHideManualAppInstallMessage();
                    requestMessage.setId(device.getId());
                    requestMessage.setShowManualAppInstallInstructions(false);
                    publishToMqttTopic(topic, requestMessage);
                }

                if (testResultsService.isTestResultsAlreadyRetrieved(device)) {
                    LOGGER.info("Although test results were already retrieved, but found test results in syslog again");
                } else {
                    LOGGER.info("New test results were fetched by syslogs from device");
                }

                // Testing is completed from mobile application
                deviceTestResultFromFile.getTestResults().setTestingCompleted(true);

                // Get in-app grade
                String inAppGrade = deviceTestResultFromFile.getGradeResults();
                // if in-app grade is not present then set it to device grade, if that is also blank then check local db
                if (StringUtils.isBlank(inAppGrade)) {
                    inAppGrade = device.getGrade();
                    if (StringUtils.isBlank(inAppGrade)) {
                        inAppGrade = deviceTestResultDBService.getDeviceGrade(
                                String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId());
                    }
                }

                // Set device grade results to device
                deviceTestResultFromFile.setGradeResults(inAppGrade);

                // Update failed test results for android
                testResultsService.updateFailedTestResults(device, deviceTestResultFromFile);
                testResultsService.updatePassedTestResults(device, deviceTestResultFromFile);

                if (device.getDeviceTestResult() == null) {
                    DeviceTestResult deviceTestResult = new DeviceTestResult();
                    device.setDeviceTestResult(deviceTestResult);
                }
                device.getDeviceTestResult().setTestResults(deviceTestResultFromFile.getTestResults());

                device.getDeviceTestResult().setGradeResults(deviceTestResultFromFile.getGradeResults());

                testResultsService.
                        setBatteryResultsAndUpdateBatteryDrainResult(device,
                                deviceTestResultFromFile.getBatteryResults());

                device.getDeviceTestResult().setMicrophoneResults(deviceTestResultFromFile.getMicrophoneResults());
                device.getDeviceTestResult().setDeviceColor(deviceTestResultFromFile.getDeviceColor());
                if (deviceTestResultFromFile.getCosmeticResults() != null
                        && (deviceTestResultFromFile.getCosmeticResults().getFailed() != null
                        || deviceTestResultFromFile.getCosmeticResults().getPassed() != null
                        || deviceTestResultFromFile.getCosmeticResults().getPending() != null)) {
                    device.getDeviceTestResult().setCosmeticResults(deviceTestResultFromFile.getCosmeticResults());
                } else {
                    DeviceTestResult deviceTestResult = deviceTestResultDBService.getDeviceTestResults(
                            String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId(),
                            String.valueOf(inMemoryStore.getLicenseId()), device.getSerial());
                    device.getDeviceTestResult().setCosmeticResults(
                            deviceTestResult != null ? deviceTestResult.getCosmeticResults() : null);
                }

                LOGGER.info("Test results that are set in device: {}", device.getDeviceTestResult());

                if (StringUtils.isNotEmpty(inAppGrade)) {
                    device.setGrade(inAppGrade);
                }
                String color = deviceTestResultFromFile.getDeviceColor();
                if (!StringUtils.equalsIgnoreCase(device.getColor(), color) && StringUtils.isNotBlank(color)) {
                    device.setColor(color);
                    final String topic = TopicBuilder.build(device, "color", "change", "response");
                    final DeviceColorChangeMessage requestMessage = new DeviceColorChangeMessage();
                    requestMessage.setId(device.getId());
                    requestMessage.setColor(color);
                    publishToMqttTopic(topic, requestMessage);
                }

                // Add initial defects in AppTestResult
                // so that it syncs to cloud and database with other test results.
                TestResultsUtil.prependInitialDefectsToAppResults(device,
                        inMemoryStore.getAssignedCloudCustomization(), localizationService);
                vendorCriteriaService.calculateVendorCriteria(device);
                AppTestingDoneStage stage = AppTestingDoneStage.builder()
                        .id(device.getId())
                        .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                        .licenseId(String.valueOf(inMemoryStore.getLicenseId()))
                        .serial(device.getSerial())
                        .deviceType(device.getDeviceType())
                        .deviceTestResult(device.getDeviceTestResult())
                        .timestamp(System.currentTimeMillis())
                        .ebayRefurbished(device.getEbayRefurbished())
                        .ebayRejection(device.getEbayRejection())
                        .swappaQualified(device.getSwappaQualified())
                        .swappaRejection(device.getSwappaRejection())
                        .amazonRenewed(device.getAmazonRenewed())
                        .amazonRenewedRejection(device.getAmazonRenewedRejection())
                        .backMarketQualified(device.getBackMarketQualified())
                        .backMarketRejection(device.getBackMarketRejection())
//                        .ebayGradeId(device.getEbayGradeId())
//                        .SwappaGradeId(device.getSwappaGradeId())
//                        .AmazonGradeId(device.getAmazonGradeId())
//                        .BackMarketGradeId(device.getBackmarketGradeId())
                        .build();
                stageUpdater.update(stage);

                device.setStage(DeviceStage.APP_TESTING_DONE);
                LOGGER.info("EbayR - {} , SWappaR - {}, BackmarketR - {}, AmazonR - {} , device stage {} ",
                        device.getEbayRejection(), device.getSwappaRejection(), device.getBackMarketRejection(),
                        device.getAmazonRenewedRejection(), device.getStage());
                // Update SKU Once
                eventPublisher.publishEvent(new DeviceSkuCodeUpdateEvent(this, device));

                // Notify UI
                final String topic = TopicBuilder.build(device, "tested");
                final DeviceTestedMessage message = new DeviceTestedMessage();
                message.setId(device.getId());
                message.setDeviceTestResult(device.getDeviceTestResult());
                message.setVendorName(device.getVendorName());
                publishToMqttTopic(topic, message);

                // Added 500 ms sleep for test results to fully sync with UI module
                Thread.sleep(500);

                // Perform Results/Label/Source api operations
                testResultsService.performLabelSourceApiOperations(device);

                // trigger auto export
                deviceDataConversionAndExportService.triggerAutoExportOnTestResults(device);

                // start test result automation
                automationQueueService
                        .enqueueDeviceAutomationRequest(new DeviceTestResultAutomationEvent(this, device));

                // Sync device Data to the cloud
                final SyncableDevice syncableDevice = SyncableDevice.builder()
                        .transaction(inMemoryStore.getTransaction())
                        .device(device)
                        .source(getClass())
                        .build();
                cloud3DeviceDataSyncService.enqueueDeviceDataSyncRequest(syncableDevice);

                if (inMemoryStore.getAssignedCloudCustomization().getWifiSettings() != null
                        && inMemoryStore.getAssignedCloudCustomization().getWifiSettings().isDisconnect()) {
                    WifiStatus wifiStatus = androidWifiAutoDisconnectService.turnOffWifi(device);
                    LOGGER.info("Wifi disconnect customization was enabled and wifi status is : {}", wifiStatus);
                }

            } else {
                LOGGER.error("Failed to fetch test results from the file");
                if (deviceTestResultFromFileStatus.getNotificationStatus() != null &&
                        List.of(NotificationStatus.FAILED_NO_DEVICE, NotificationStatus.LOCKDOWN_FAILED)
                                .contains(deviceTestResultFromFileStatus.getNotificationStatus())) {
                    LOGGER.error("Notification post status: {}",
                            deviceTestResultFromFileStatus.getNotificationStatus());
                    notifyError(device, CANT_FIND_DEVICE_ERROR_MSG);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while setting test results", e);
        }
    }

    /**
     * Perform fingerprint support operations on device based on given sys log keys
     *
     * @param device           target android device
     * @param androidSysLogKey syslog key
     */
    private void performFingerprintOperations(final AndroidDevice device,
                                              final AndroidSysLogKey androidSysLogKey) {
        LOGGER.info("Fingerprint syslog event received for key: {} ", androidSysLogKey);

        if ("samsung".equalsIgnoreCase(device.getMake())) {
            if (AndroidFingerPrintService.FINGERPRINT_GESTURE_SUPPORTED_MODELS.contains(device.getModelNo())) {
                if (androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_1
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_2
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_3
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_4
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_5) {

                    fingerPrintService.runFingerPrintActivity(device.getId());
                }
                if (androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_SCAN_EFFECT) {

                    fingerPrintService.runFingerPrintMainActivity(device.getId(),
                            androidSysLogKey.getKey());
                }
            } else if (AndroidFingerPrintService.NON_MEID_SUPPORTED_MODELS.contains(device.getModelNo())) {
                if (androidSysLogKey == AndroidSysLogKey.START_FINGER_PRINT_ADMIN
                        || androidSysLogKey == AndroidSysLogKey.END_FINGER_PRINT_ADMIN) {
                    if (appService.makeDeviceAdmin(device.getId(), AndroidAppPackageConstants.PC_UTILITY_PACKAGE)) {

                        fingerPrintService.runFingerPrintActivity(device.getId());

                    }
                } else if (androidSysLogKey == AndroidSysLogKey.START_FINGER_PRINT_PIN
                        || androidSysLogKey == AndroidSysLogKey.END_FINGER_PRINT_PIN) {

                    fingerPrintService.runFingerPrintPasswordAutomationForUnderDisplay(device.getId());

                } else if (androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_6
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_7
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_8
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_9
                        || androidSysLogKey == AndroidSysLogKey.FINGER_PRINT_10) {

                    try {
                        appService.forceKillApp(device.getId(), AndroidAppPackageConstants.SETTINGS_PACKAGE);
                    } catch (IOException e) {
                        LOGGER.error("Exception occurred while force killing settings app", e);
                    }
                    fingerPrintService.runFingerPrintActivity(device.getId());
                }
            } else {

                fingerPrintService.performFingerPrintOperationForSamsung(device, androidSysLogKey);
            }
        } else if (StringUtils.containsIgnoreCase(device.getModel(), "pixel")
                || StringUtils.containsIgnoreCase(device.getMake(), "google")
                || StringUtils.containsIgnoreCase(device.getMake(), "Xiaomi")) {

            fingerPrintService.performFingerPrintOperationForOtherDevices(device, androidSysLogKey);
        }
    }

    /**
     * Method to sync device data to cloud with latest erase info
     *
     * @param device target device
     */
    private void syncDeviceDataToCloud(final AndroidDevice device) {
        String eraseNotes;
        if (DeviceStage.DISCONNECTED.equals(device.getStage())) {
            eraseNotes = DeviceEraseStatus.ERASE_FAILED_DISCONNECTED.getText();
        } else if (DeviceStage.ERASE_FAILED.equals(device.getStage())) {
            eraseNotes = DeviceEraseStatus.ERASE_FAILED_UNKNOWN.getText();
        } else {
            eraseNotes = "";
        }

        SyncedCloudDevice cloudDevice =
                cloudDeviceService.getDeviceBySerial(device.getSerial(), inMemoryStore.getLicenseId());

        String cloudEraseNotes = cloudDevice != null ? cloudDevice.getErasedNotes() : null;
        StringBuilder eraseNotesBuilder = new StringBuilder();
        String lastEraseNote = null;
        if (StringUtils.isNotBlank(cloudEraseNotes)) {
            eraseNotesBuilder.append(cloudEraseNotes);

            String[] previousEraseNotes = cloudEraseNotes.split(";");

            if (previousEraseNotes.length > 0) {
                lastEraseNote = previousEraseNotes[previousEraseNotes.length - 1];
            }
        }


        if (!eraseNotes.equals(lastEraseNote)) {
            eraseNotesBuilder.append(eraseNotes).append(";");
        }

        device.setErasedNotes(eraseNotesBuilder.toString());
    }

    /**
     * notify erase success
     *
     * @param device AndroidDevice
     */
    private void notifyEraseSuccess(final AndroidDevice device) {
        if (!Boolean.TRUE.equals(device.getIsErasePerformed())) {
            // do not move this flag setting, it may delay the android erase process
            device.setIsErasePerformed(Boolean.TRUE);
            LOGGER.info("Completed erase, raising erase success event");
            eventPublisher.publishEvent(new AndroidEraseSuccessEvent(this,
                    device));
        }
    }
}
