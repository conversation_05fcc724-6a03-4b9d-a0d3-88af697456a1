package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.device.results.AndroidTestResultsService;
import com.phonecheck.info.DeviceTestPlanUtil;
import com.phonecheck.info.android.AndroidBatteryInfoService;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoFailureEvent;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoNeededEvent;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoSuccessEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.util.CustomizationUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Initiates Battery info data collection
 */
@Component
public class AndroidBatteryInfoNeededListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidBatteryInfoNeededListener.class);
    private final ApplicationEventPublisher eventPublisher;
    private final AndroidBatteryInfoService batteryInfoService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceTestPlanUtil deviceTestPlanUtil;
    private final AndroidTestResultsService androidTestResultsService;

    private final InMemoryStore inMemoryStore;
    private final CustomizationUtil customizationUtil;

    public AndroidBatteryInfoNeededListener(final IMqttAsyncClient mqttClient,
                                            final ObjectMapper objectMapper,
                                            final ApplicationEventPublisher eventPublisher,
                                            final AndroidBatteryInfoService batteryInfoService,
                                            final DeviceConnectionTracker deviceConnectionTracker,
                                            final InMemoryStore inMemoryStore,
                                            final CustomizationUtil customizationUtil,
                                            final DeviceTestPlanUtil deviceTestPlanUtil,
                                            final AndroidTestResultsService androidTestResultsService) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.batteryInfoService = batteryInfoService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.customizationUtil = customizationUtil;
        this.deviceTestPlanUtil = deviceTestPlanUtil;
        this.androidTestResultsService = androidTestResultsService;
    }

    @EventListener
    public void onEvent(final AndroidBatteryInfoNeededEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Battery info collection needed but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Android battery info collection needed");

        BatteryInfo batteryInfo = getBatteryInfo(device);
        // Publish event
        if (batteryInfo != null) {
            float healthPercent = Math.round(batteryInfo.getHealthPercentage());
            // for androids, if battery health percentage is between 76-79 then round it off to 80
            if (healthPercent > 75 && healthPercent < 80) {
                batteryInfo.setHealthPercentage(80);
            }

            final String language = inMemoryStore.getCurrentLanguage();
            //TODO As of now not added for English
            if (StringUtils.equalsIgnoreCase(language, "Japanese")) {
                final Pair<Boolean, Boolean> isBatteryDegraded =
                        batteryInfoService.getBatteryMessageWithOCR(device.getId(),
                        language);
                LOGGER.info("is battery found degraded post OCR command:{}", isBatteryDegraded);
                if (customizationUtil.isBatteryWarningCustomizationEnabled() && isBatteryDegraded.getLeft()) {
                    deviceInTracker.setBatteryDegraded(true);
                    if (isBatteryDegraded.getRight()) {
                        batteryInfo.setHealthPercentage(
                                setBatteryHealthForDegraded(batteryInfo.getHealthPercentage(), 80));
                        if (batteryInfo.getHealthPercentage() < 50) {
                            batteryInfo.setHealthPercentage(
                                    adjustBatteryHealthForBatteryWarning(batteryInfo.getHealthPercentage(), 50));
                        }
                    } else {
                        batteryInfo.setHealthPercentage(
                                setBatteryHealthForDegraded(batteryInfo.getHealthPercentage(), 50));
                    }
                    batteryInfo.setCycle(batteryInfoService.getCyclesFromBatteryAsocHealth(
                            batteryInfo.getHealthPercentage()));
                    batteryInfo.setCurrentCapacity(
                            Math.round((batteryInfo.getHealthPercentage() / 100f) * batteryInfo.getDesignedCapacity()));
                } else if (batteryInfo.getHealthPercentage() <= 80) {
                    batteryInfo.setHealthPercentage(
                            adjustBatteryHealthForBatteryWarning(batteryInfo.getHealthPercentage(), 80));
                }
            }
            deviceInTracker.setBatteryInfo(batteryInfo);
            deviceInTracker.setBatteryStateHealth(batteryInfo.getHealthPercentage());
            deviceInTracker.setBatteryPercentage(batteryInfo.getBatteryPercentage());
            eventPublisher.publishEvent(new AndroidBatteryInfoSuccessEvent(this, deviceInTracker));
        } else {
            eventPublisher.publishEvent(new AndroidBatteryInfoFailureEvent(this, deviceInTracker));
        }
    }

    /**
     * Fetched device battery info
     *
     * @param device target android device
     * @return BatteryInfo
     */
    private BatteryInfo getBatteryInfo(final AndroidDevice device) {
        BatteryInfo batteryInfo = null;
        try {
            if (device.getMake().equalsIgnoreCase("google")) {
                batteryInfo = batteryInfoService.getGoogleBatteryInfo(device);
                if (batteryInfo == null || batteryInfo.getHealthPercentage() == 0) {
                    batteryInfo = batteryInfoService.getBatteryHealthFromCycleCount(device);
                }
                if (batteryInfo == null || batteryInfo.getHealthPercentage() == 0) {
                    batteryInfo = batteryInfoService.getBatteryHealthFromFgCycles(device);
                }
                if (batteryInfo == null || batteryInfo.getHealthPercentage() == 0) {
                    batteryInfo = batteryInfoService.getBatteryHealthFromBatteryCycles(device);
                }
            } else {
                batteryInfo = batteryInfoService.getBatteryHealthFromAsoc(device);
                if (batteryInfo == null) {
                    batteryInfo = batteryInfoService.getBatteryHealthFromUsage(device);
                }
                if (batteryInfo == null) {
                    batteryInfo = batteryInfoService.getBatteryHealthFromFgCycles(device);
                }
                if (batteryInfo == null) {
                    batteryInfo = batteryInfoService.getBatteryHealthFromBatteryCycles(device);
                }
            }
            if (batteryInfo == null && !device.getMake().equalsIgnoreCase("google")) {
                batteryInfo = batteryInfoService.getBatteryHealthFromBatteryStats(device);
            }

            // Remove charging status key if not exist in test plan
            updateChargingStatus(batteryInfo, device);

        } catch (Exception e) {
            LOGGER.error("Error while getting battery info for android", e);
        }
        return batteryInfo;
    }

    /**
     * Adjusts the battery health percentage for a degraded battery.
     * The adjustment is based on the initial health percentage, applying a reduction
     * in fixed steps depending on the range of the input health value.
     *
     * @param batteryHealth the initial battery health percentage, typically a value between 0 and 100
     * @return the adjusted battery health percentage based on degradation criteria
     * @param threshold
     */
    private int setBatteryHealthForDegraded(final int batteryHealth, final int threshold) {
        LOGGER.info("Set battery health for degraded {}", batteryHealth);
        if (batteryHealth < threshold) {
            return batteryHealth;
        }
        // Calculate reduction based on ranges
        // Every 5% increase in battery health adds 5 to the reduction
        // Base reduction is 6 for 80%, increasing by 5 for each range
        int rangeIndex = (batteryHealth - threshold) / 5;  // How many ranges above 80%
        return batteryHealth - (6 + (rangeIndex * 5));
    }

    /**
     * Adjusts the battery health percentage for a degraded battery. if battery warning is greater than threshold and
     * health is less than threshold
     *
     * @param batteryHealth the initial battery health percentage, typically a value between 0 and 100
     * @param threshold
     * @return the adjusted battery health percentage based on degradation criteria
     */
    private int adjustBatteryHealthForBatteryWarning(final int batteryHealth, final int threshold) {
        LOGGER.info("Set battery health for low battery levels {}", batteryHealth);

        if (batteryHealth >= threshold) {
            return batteryHealth;
        } else if (batteryHealth < 5) {
            return threshold + batteryHealth;
        }
        // Calculate addition based on ranges
        // Every 5% increase in battery health subtracts 5 from the addition
        // Base addition is threshold for 5%, decreasing by 5 for each range
        int rangeIndex = (batteryHealth - 5) / 5;  // How many ranges above 5%
        return batteryHealth + ((threshold - 4) - (rangeIndex * 5));
    }

    /**
     * Removes the charging status from the battery info if it is not needed.
     * Logs messages based on different conditions:
     * - If battery info is null, logs a message and returns.
     * - If the device is in PreCheck mode, logs a message and returns.
     * - If the charging test key is not in the test plan, sets charging status to null.
     * - Otherwise, logs that charging status should be added on the UI.
     *
     * @param batteryInfo the battery information to update
     * @param device      the iOS device associated with the battery info
     */
    private void updateChargingStatus(final BatteryInfo batteryInfo, final AndroidDevice device) {
        if (batteryInfo == null) {
            LOGGER.info("Found battery info is null, while updating charging status in battery info");
            return;
        }
        if (deviceTestPlanUtil.isChargingKeyExistInTestPlan(device)) {
            LOGGER.info("Charging test key not found in test plan in default mode, no need to add on UI");
            batteryInfo.setIsCharging(null);
            //delete the test key from passed / failed test results.
            Optional.ofNullable(device)
                    .map(AndroidDevice::getDeviceTestResult)
                    .map(DeviceTestResult::getTestResults)
                    .ifPresent(testResults -> {
                        Optional.ofNullable(testResults.getPassed())
                                .ifPresent(passed -> passed.remove(InitialDefectKey.BATTERY_CHARGING_STATUS));

                        Optional.ofNullable(testResults.getFailed())
                                .ifPresent(failed -> failed.remove(InitialDefectKey.BATTERY_CHARGING_STATUS));
                    });
        } else {
            LOGGER.info("Charging test found in test plan in default mode, add charging status Pass/Fail on UI");
            androidTestResultsService.updateBatteryChargingTestInPassFailResult(device);
        }
    }
}
