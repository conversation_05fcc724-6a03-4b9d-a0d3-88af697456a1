package com.phonecheck.backend.listener.device.iwatch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.app.AppFileService;
import com.phonecheck.app.ios.IosAppService;
import com.phonecheck.app.ios.IosAppVersionService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.iwatch.IWatchHostAppInstallEvent;
import com.phonecheck.model.event.device.iwatch.IWatchPushAllFilesEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.iwatch.IWatchHostAppInstallingMessage;
import com.phonecheck.model.status.AppInstallStatus;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * Initiated to install consumer app to iWatch host
 */
@Component
public class IWatchHostAppInstallListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchHostAppInstallListener.class);

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final AppFileService appFileService;
    private final IosAppVersionService iosAppVersionService;
    private final IosAppService appService;
    private final ApplicationEventPublisher eventPublisher;

    public IWatchHostAppInstallListener(final IMqttAsyncClient mqttClient,
                                        final ObjectMapper objectMapper,
                                        final DeviceConnectionTracker deviceConnectionTracker,
                                        final AppFileService appFileService,
                                        final IosAppVersionService iosAppVersionService,
                                        final IosAppService appService,
                                        final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.appFileService = appFileService;
        this.iosAppVersionService = iosAppVersionService;
        this.appService = appService;
        this.eventPublisher = eventPublisher;
    }

    @EventListener
    public void onEvent(final IWatchHostAppInstallEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Try to install iWatch diagnostic app but host has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("IWatch diagnostic app installation initiated");

        // Notify UI
        final String topic = TopicBuilder.build(device, "iwatch", "app", "install-status");
        IWatchHostAppInstallingMessage message = new IWatchHostAppInstallingMessage();
        message.setId(device.getId());
        publishToMqttTopic(topic, message);

        try {
            final File appIpaFile = appFileService.getAppFile(device.getDeviceFamily(), StringUtils.EMPTY);
            final String loadedPhoneCheckAppVersion = iosAppVersionService.getIosAppIpaVersion(appIpaFile);


            LOGGER.info("IWatch diagnostic app installation parameters are, " +
                    "File path:{}, appVersion:{}", appIpaFile.getPath(), loadedPhoneCheckAppVersion);

            AppInstallStatus appInstallStatus = appService.installIWatchDiagnosticApp(device,
                    loadedPhoneCheckAppVersion,
                    appIpaFile.getPath());

            LOGGER.info("IWatch diagnostic app installation status on device: {}", appInstallStatus);

            if (AppInstallStatus.SUCCESS.equals(appInstallStatus) ||
                    AppInstallStatus.NOT_REQUIRED.equals(appInstallStatus)) {
                // First time on host connection we pushed all config files to device, then on each iWatch connection
                // we only update allSyncAble.json and pushed
                device.setAppInstalled(true);
                eventPublisher.publishEvent(new IWatchPushAllFilesEvent(this, device));
            }

        } catch (Exception e) {
            LOGGER.error("Error while installing or pushing files to host", e);
        }

    }

}
