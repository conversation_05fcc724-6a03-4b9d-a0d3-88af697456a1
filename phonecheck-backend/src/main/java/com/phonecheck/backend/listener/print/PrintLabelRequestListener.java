package com.phonecheck.backend.listener.print;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.client.customization.ClientCustomizationService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.device.DeviceManualPrintCustomAutomationEvent;
import com.phonecheck.model.event.print.PrintLabelRequestEvent;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.print.service.LabelPrintService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.List;

import static com.phonecheck.model.print.label.LabelVariant.ANDROID_PROVISION_QR_LABEL;

/**
 * Listener to process print label Request event.
 */
@Component
public class PrintLabelRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PrintLabelRequestListener.class);

    private final LabelPrintService labelPrintService;
    private final ClientCustomizationService customizationService;
    private final InMemoryStore inMemoryStore;
    private final DeviceAutomationQueueService automationQueueService;

    public PrintLabelRequestListener(final IMqttAsyncClient mqttClient,
                                     final ObjectMapper objectMapper,
                                     final LabelPrintService labelPrintService,
                                     final ClientCustomizationService customizationService,
                                     final InMemoryStore inMemoryStore,
                                     final DeviceAutomationQueueService automationQueueService) {
        super(mqttClient, objectMapper);
        this.labelPrintService = labelPrintService;
        this.customizationService = customizationService;
        this.inMemoryStore = inMemoryStore;
        this.automationQueueService = automationQueueService;
    }

    @Async
    @EventListener
    public void onEvent(final PrintLabelRequestEvent event) {
        PrintOperation printOperation = event.getPrintOperation();
        PrintMode printMode = printOperation.getPrintMode();
        Device device;

        if (printOperation != null) {
            if (printOperation.getLabelName().equals(ANDROID_PROVISION_QR_LABEL.getName())) {
                labelPrintService.enqueuePrintOperation(printOperation);
                return;
            }
            device = printOperation.getDevice();
            setDeviceIdMDC(device.getId());
        } else {
            LOGGER.warn("Canceling print request as device data not provided to perform print operation.");
            return;
        }

        // Set of modes that should be skipped to check required fields
        EnumSet<PrintMode> printModesToSkipValidation = EnumSet.of(PrintMode.TRANSACTION,
                PrintMode.MANUAL_ENTRY, PrintMode.IWATCH, PrintMode.AIRPOD);

        if (!printModesToSkipValidation.contains(printMode)) {
            CloudCustomizationResponse assignedCloudCustomizations = inMemoryStore.getAssignedCloudCustomization();

            // Don't proceed to print if any unpopulated required fields are found
            if (assignedCloudCustomizations.getRestrictiveActions().isPrint()) {

                List<String> unpopulatedRequiredFields = customizationService.
                        getUnpopulatedRequiredFields(device, assignedCloudCustomizations);
                if (!unpopulatedRequiredFields.isEmpty()) {
                    LOGGER.info("Print is a restricted action and unpopulated required fields are found: {}," +
                                    " so stopping print and sending unpopulated required fields to UI",
                            unpopulatedRequiredFields);
                    // Notify Ui for required fields status
                    notifyRequiredFields(device, unpopulatedRequiredFields, false);
                    return;
                }
            }
        }

        LOGGER.info("Print request for device queued");
        labelPrintService.enqueuePrintOperation(printOperation);


        if (PrintMode.DEVICE.equals(printMode) && Boolean.TRUE.equals(printOperation.isManualPrint())) {
                LOGGER.info("Manual print custom workflow automation triggered");
            automationQueueService.
                    enqueueDeviceAutomationRequest(new DeviceManualPrintCustomAutomationEvent(this, device));
        }
    }
}
