package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosFaceIdSensorInfoEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosSysLogMessage;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Listens to FaceId working status from syslogs.
 * If event is received for faceIdWorkingStatus as Pending,
 * then sends FaceId status as'Working' notification to UI after waiting for 8 seconds
 * If event is received for faceIdWorkingStatus as Not Working,
 * then instantly sends FaceId status as 'Not-Working' notification  to UI
 */
@Component
public class IosFaceIdSensorWorkingStatusListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosFaceIdSensorWorkingStatusListener.class);

    private final DeviceConnectionTracker deviceConnectionTracker;

    public IosFaceIdSensorWorkingStatusListener(final ObjectMapper mapper, final IMqttAsyncClient mqttClient,
                                                final DeviceConnectionTracker deviceConnectionTracker) {
        super(mqttClient, mapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
    }

    @Async
    @EventListener
    public void onEvent(final IosFaceIdSensorInfoEvent event) {
        IosDevice device = (IosDevice) event.getDevice();
        IosSysLogKey iosSysLogKey = event.getIosSysLogKey();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Want to get device's face Id working status but device has been disconnected, stopping " +
                    "processing.");
            return;
        }

        final String topic = TopicBuilder.build(device, "parsed", "syslog");
        final IosSysLogMessage message = new IosSysLogMessage();
        message.setId(device.getId());
        message.setIosSysLogKey(iosSysLogKey);

        int timeout = 8;
        if (WorkingStatus.PENDING.equals(device.getFaceIdSensor())) {
            LOGGER.info("FaceId status found PENDING on device");
            while (timeout > 0) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    LOGGER.warn("Device faceIdSensorWorking thread interrupted");
                }
                // Break the loop if the device faceIdSensor status has changed to working or not working state
                // We don't need to send faceIdSensor status change message to UI
                // as that message will be sent by another thread where the faceId status has changed from
                // Pending to Working.YES or Working.NO
                if (!WorkingStatus.PENDING.equals(device.getFaceIdSensor())) {
                    LOGGER.debug("Device faceId sensor status has changed to {}", device.getFaceIdSensor());
                    break;
                }
                timeout--;
            }
            // If timeout has reached to 8 seconds, that means that faceId sensor state has not changed to 'Not working'
            // So set the faceId sensor status as 'Working'
            if (timeout <= 0) {
                device.setFaceIdSensor(WorkingStatus.YES);
                LOGGER.info("Timer timed out. Changing device faceId sensor working status from PENDING to YES");
                message.setFaceIdSensor(device.getFaceIdSensor());
                // Publish faceId sensor working status to UI
                publishToMqttTopic(topic, message);
            }
        } else if (WorkingStatus.NO.equals(device.getFaceIdSensor())) {
            LOGGER.info("Changing device faceId sensor working status from PENDING to NO");
            message.setFaceIdSensor(device.getFaceIdSensor());
            // Publish faceId sensor working status to UI
            publishToMqttTopic(topic, message);
        }
    }
}
