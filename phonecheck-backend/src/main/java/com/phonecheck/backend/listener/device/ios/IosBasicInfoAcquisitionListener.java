package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudColorService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.model.TblSimTechnology;
import com.phonecheck.dao.service.lookup.ModelDBLookupService;
import com.phonecheck.dao.service.lookup.NetworkDBLookupService;
import com.phonecheck.device.results.file.push.DeviceFeaturesFileService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.cloudapi.DeviceFeaturesResponse;
import com.phonecheck.model.cloudapi.IosColorCodeResponse;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceState;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceReconnectRequireEvent;
import com.phonecheck.model.event.device.ios.IosBasicInfoAcquisitionEvent;
import com.phonecheck.model.ios.IosProperty;
import com.phonecheck.model.status.ActivationStatus;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.status.SimStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.IosDeviceRamSizeMapper;
import com.phonecheck.parser.device.DeviceOsVersionParser;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static com.phonecheck.model.helper.IOSDeviceModelHelper.IPHONE_PRODUCT_TO_MODEL_MAPPINGS;
import static com.phonecheck.model.status.SetupDoneStatus.DONE;

/**
 * Initiates basic device info acquisition
 * IMEI
 * IMEI2
 * Model No
 * Serial No
 * Regulatory Model No
 * Firmware
 * Activation Status
 * Sim Status
 * ESim
 * Meid
 * Wi-Fi Address
 * Region Info
 * OS
 * Disk Size
 * Setup Done status
 * Model Name
 * Guid
 */
@Component
public class IosBasicInfoAcquisitionListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosBasicInfoAcquisitionListener.class);

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final IosDeviceInfoService iosDeviceInfoService;
    private final DeviceFeaturesFileService deviceFeaturesFileService;
    private final ModelDBLookupService modelDBLookupService;
    private final InMemoryStore inMemoryStore;
    private final DeviceOsVersionParser deviceOsVersionParser;
    private final IosPairingUtil iosPairingUtil;
    private final ApplicationEventPublisher eventPublisher;
    private final NetworkDBLookupService networkDBLookupService;
    private final IosDeviceRamSizeMapper deviceRamSizeMapper;
    private final CloudColorService cloudColorService;


    public IosBasicInfoAcquisitionListener(final IMqttAsyncClient mqttClient,
                                           final ObjectMapper objectMapper,
                                           final DeviceConnectionTracker deviceConnectionTracker,
                                           final IosDeviceInfoService iosDeviceInfoService,
                                           final DeviceFeaturesFileService deviceFeaturesFileService,
                                           final ModelDBLookupService modelDBLookupService,
                                           final DeviceOsVersionParser deviceOsVersionParser,
                                           final InMemoryStore inMemoryStore,
                                           final IosPairingUtil iosPairingUtil,
                                           final ApplicationEventPublisher eventPublisher,
                                           final NetworkDBLookupService networkDBLookupService,
                                           final IosDeviceRamSizeMapper deviceRamSizeMapper,
                                           final CloudColorService cloudColorService) {
        super(mqttClient, objectMapper);

        this.iosPairingUtil = iosPairingUtil;
        this.eventPublisher = eventPublisher;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.iosDeviceInfoService = iosDeviceInfoService;
        this.deviceFeaturesFileService = deviceFeaturesFileService;
        this.modelDBLookupService = modelDBLookupService;
        this.deviceOsVersionParser = deviceOsVersionParser;
        this.inMemoryStore = inMemoryStore;
        this.networkDBLookupService = networkDBLookupService;
        this.deviceRamSizeMapper = deviceRamSizeMapper;
        this.cloudColorService = cloudColorService;
    }

    @EventListener
    public void onEvent(final IosBasicInfoAcquisitionEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Info collection needed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("IOS basic device info acquisition requested");

        PairStatus pairStatus = iosPairingUtil.checkAndNotifyUiIfNotPaired(deviceInTracker);
        if (!PairStatus.PAIRED.equals(pairStatus)) {
            LOGGER.warn("Device is not paired. Cannot continue processing. PairStatus: {}", pairStatus);
            deviceInTracker.setReconnectRequired(true);
            eventPublisher.publishEvent(new DeviceReconnectRequireEvent(this, device, ErrorConstants.PAIRING_FAILED));
            return;
        }

        try {
            final Map<IosProperty, String> properties =
                    iosDeviceInfoService.getProperties(deviceInTracker,
                            false,
                            IosProperty.IMEI,
                            IosProperty.IMEI2,
                            IosProperty.MODEL_NUMBER,
                            IosProperty.SERIAL_NUMBER,
                            IosProperty.ACTIVATION_STATE,
                            IosProperty.REGION_INFO,
                            IosProperty.SIM_STATUS,
                            IosProperty.MOBILE_EQUIPMENT_IDENTIFIER,
                            IosProperty.WIFI_ADDRESS,
                            IosProperty.REGULATORY_MODEL_NUMBER,
                            IosProperty.SIM1_IS_EMBEDDED,
                            IosProperty.SIM2_IS_EMBEDDED,
                            IosProperty.BASEBAND_VERSION,
                            IosProperty.RELEASE_TYPE,
                            IosProperty.DEVICE_ENCLOSURE_COLOR,
                            IosProperty.DEVICE_COLOR,
                            IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY
                    );

            if (null == properties || properties.isEmpty()) {
                LOGGER.error("Error getting device properties. Device may be unpaired.");
                return;
            } else {
                properties.forEach((iosProperty, s) -> LOGGER.info("Prop value: {} : {}", iosProperty, s));
            }

            if (StringUtils.isBlank(deviceInTracker.getImei())) {
                deviceInTracker.setImei(properties.get(IosProperty.IMEI));
            }
            if (StringUtils.isBlank(deviceInTracker.getImei2())) {
                deviceInTracker.setImei2(properties.get(IosProperty.IMEI2));
            }
            if (StringUtils.isBlank(deviceInTracker.getSimSerial())) {
                deviceInTracker.setSimSerial(properties.get(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY));
            }
            deviceInTracker.setModelNo(properties.get(IosProperty.MODEL_NUMBER));
            deviceInTracker.setRegulatoryModelNumber(properties.get(IosProperty.REGULATORY_MODEL_NUMBER));
            deviceInTracker.setFirmware(properties.get(IosProperty.BASEBAND_VERSION));
            if (StringUtils.isBlank(deviceInTracker.getRegulatoryModelNumber())) {
                TblSimTechnology simTechnologyFromDb =
                        networkDBLookupService.getRegulatoryModel(deviceInTracker.getModelNo());
                if (simTechnologyFromDb != null) {
                    LOGGER.info("Set regulatory model {} from Sim technology table",
                            simTechnologyFromDb.getRegulatoryModel());
                    deviceInTracker.setRegulatoryModelNumber(simTechnologyFromDb.getRegulatoryModel());
                }
            }
            if (StringUtils.isBlank(deviceInTracker.getSerial())) {
                deviceInTracker.setSerial(properties.get(IosProperty.SERIAL_NUMBER));
            }

            ActivationStatus activationStatus;
            if (null == properties.get(IosProperty.ACTIVATION_STATE)) {
                activationStatus = ActivationStatus.FAILED_UNKNOWN;
            } else if ("Activated".equalsIgnoreCase(properties.get(IosProperty.ACTIVATION_STATE))) {
                activationStatus = ActivationStatus.ACTIVATED;
            } else {
                activationStatus = ActivationStatus.FAILED_UNKNOWN;
            }
            LOGGER.info("Activation status while fetching basic info: {}", activationStatus);
            deviceInTracker.setActivationStatus(activationStatus);

            SimStatus simStatus;
            if (null == properties.get(IosProperty.SIM_STATUS)) {
                simStatus = SimStatus.FAILED_UNKNOWN;
            } else if ("kCTSIMSupportSIMStatusNotInserted".equalsIgnoreCase(properties.get(IosProperty.SIM_STATUS))) {
                simStatus = SimStatus.NOT_INSERTED;
            } else if ("kCTSIMSupportSIMStatusReady".equalsIgnoreCase(properties.get(IosProperty.SIM_STATUS))) {
                simStatus = SimStatus.READY;
            } else {
                simStatus = SimStatus.NOT_READY;
            }
            deviceInTracker.setSimStatus(simStatus);

            deviceInTracker.setSim1Esim("true".equals(properties.get(IosProperty.SIM1_IS_EMBEDDED)));
            deviceInTracker.setSim2Esim("true".equals(properties.get(IosProperty.SIM2_IS_EMBEDDED)));
            deviceInTracker.setMeid(properties.get(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER));
            deviceInTracker.setWifiAddress(properties.get(IosProperty.WIFI_ADDRESS));
            deviceInTracker.setRegionInfo(properties.get(IosProperty.REGION_INFO));
            deviceInTracker.setSerial(properties.get(IosProperty.SERIAL_NUMBER));
            deviceInTracker.setReleaseType(properties.get(IosProperty.RELEASE_TYPE));
            deviceInTracker.setEsimActive(deviceInTracker.isSim1Esim() || deviceInTracker.isSim2Esim());

            if (!properties.containsKey(IosProperty.DEVICE_ENCLOSURE_COLOR) ||
                    properties.get(IosProperty.DEVICE_ENCLOSURE_COLOR).equalsIgnoreCase("unknown")) {
                LOGGER.info("Set color code via device color property {}", properties.get(IosProperty.DEVICE_COLOR));
                deviceInTracker.setColorCode(properties.get(IosProperty.DEVICE_COLOR));
            } else {
                deviceInTracker.setColorCode(properties.get(IosProperty.DEVICE_ENCLOSURE_COLOR));
            }

            // Setting device OS version
            deviceInTracker.setOsMajorVersion(deviceOsVersionParser.parse(device.getProductVersion()));
            // Setting device disk size
            deviceInTracker.setDiskSize(iosDeviceInfoService.getDiskSize(device));
            // Setting Device setup done status
            deviceInTracker.setSetupDoneStatus(iosDeviceInfoService.getDeviceSetupDoneStatus(device));
            // Setting device features
            DeviceFeaturesResponse deviceFeaturesResponse = createAndGetDeviceFeatures(device);
            // Setting model for the device from device features
            deviceInTracker.setModel(getModelName(device.getProductType(), deviceFeaturesResponse));

            setDeviceColor(deviceInTracker);

            if (StringUtils.isNotBlank(deviceInTracker.getModel())) {
                deviceInTracker.setRam(getRamSize(deviceInTracker.getModel(), deviceInTracker.getDiskSize().getSize()));
            }
            // Setting device state
            if (!DeviceState.DFU.equals(deviceInTracker.getDeviceState())) {
                deviceInTracker.setDeviceState(device.getSetupDoneStatus() != null &&
                        device.getSetupDoneStatus().equals(DONE) ? DeviceState.HOME : DeviceState.HELLO);
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while loading basic device info", e);
        }
    }

    /**
     * Get device features from file if already exists
     * and if file not exists then retrieve from cloud and create new features file
     *
     * @param device target device
     * @return deviceFeaturesResponse
     */
    public DeviceFeaturesResponse createAndGetDeviceFeatures(final IosDevice device) {

        LOGGER.info("Getting devices features for the device");

        DeviceFeaturesResponse deviceFeaturesResponse;

        if (inMemoryStore.getIosDeviceFeatures() != null && !inMemoryStore.getIosDeviceFeatures().isEmpty() &&
              inMemoryStore.getIosDeviceFeatures().containsKey(device.getProductType())) {

            LOGGER.info("Devices features for the device were found in memory store");
            deviceFeaturesResponse = inMemoryStore.getIosDeviceFeatures().get(device.getProductType());

        } else {
            LOGGER.info("Getting devices features response for the device");

            deviceFeaturesResponse =
                    deviceFeaturesFileService.getDeviceFeaturesForIos(device.getProductType());
            if (inMemoryStore.getIosDeviceFeatures() == null) {
                inMemoryStore.setIosDeviceFeatures(new HashMap<>());
            }
            inMemoryStore.getIosDeviceFeatures().put(device.getProductType(), deviceFeaturesResponse);
        }

        if (deviceFeaturesResponse != null) {
            createDeviceFeaturesFile(deviceFeaturesResponse, device.getSerial());
        }

        return deviceFeaturesResponse;
    }

    /**
     * Creates the device features json file in the support file path
     *
     * @param deviceFeaturesResponse DeviceFeaturesResponse
     * @param serial                 device serial
     */
    private void createDeviceFeaturesFile(final DeviceFeaturesResponse deviceFeaturesResponse, final String serial) {
        try {
            LOGGER.info("Creating DeviceFeatures.json for device.");
            deviceFeaturesFileService.createDeviceFeaturesFileForIos(deviceFeaturesResponse, serial);
        } catch (IOException e) {
            LOGGER.error("Failed to create DeviceFeatures.json", e);
        }
    }

    /**
     * Method to get the device model name.
     * If model is not present in the database then fetch it from deviceFeaturesResponse from cloud.
     * If model still not found, use the IPHONE_MODELS map to get the model name
     *
     * @param productType            product type
     * @param deviceFeaturesResponse device features api response object
     * @return device model name string
     */
    private String getModelName(final String productType, final DeviceFeaturesResponse deviceFeaturesResponse) {
        String modelName = null;
        if (StringUtils.isNotEmpty(productType)) {
            try {
                modelName = modelDBLookupService.getIosModel(productType);
            } catch (final UnsupportedOperationException e) {
                // If the product type is null or empty, the model parser factory won't know what to do with it
                // This can happen when the phone is disconnected and reconnected - suddenly the product type and
                // version aren't available anymore
                LOGGER.error("Could not find parser for iPhone model {}", productType, e);
            }
        }
        if (StringUtils.isEmpty(modelName) && deviceFeaturesResponse != null) {
            modelName = deviceFeaturesResponse.getModelName();
        }
        if (StringUtils.isEmpty(modelName) && StringUtils.isNotEmpty(productType)) {
            modelName = IPHONE_PRODUCT_TO_MODEL_MAPPINGS.get(productType);
        }
        return modelName;
    }

    private String getRamSize(final String model, final long diskSise) {
        return deviceRamSizeMapper.getDeviceRam(model, diskSise);
    }

    private void setDeviceColor(final IosDevice device) {
            // Setting device color
            if (StringUtils.isBlank(device.getColor())) {
                device.setColor(getDeviceColor(device.getProductType(), device.getColorCode()));

                if (StringUtils.isBlank(device.getColor()) && !StringUtils.isBlank(device.getModelNo())) {
                    new Thread(() -> {
                        setDeviceIdMDC(device.getId());
                        LOGGER.info("iOS color does not found in local db. Going to call pending color api");
                        cloudColorService.sendPendingColorToCloudDb(
                                device.getModelNo(),
                                device.getModel(),
                                device.getImei(),
                                device.getSerial(),
                                device.getColorCode(),
                                String.valueOf(inMemoryStore.getLicenseId())
                        );
                    }).start();
                }
            }
    }

    /**
     * Get device color from color codes received from cloud
     *
     * @param productType device product type
     * @param colorCode   device color code
     * @return device color
     */
    private String getDeviceColor(final String productType, final String colorCode) {
        // Retry to fetch IOS color codes from cloud if they were not found in inMemoryStore
        if (inMemoryStore.getIosColorCodes() == null ||
                inMemoryStore.getIosColorCodes().getColorCodes() == null ||
                inMemoryStore.getIosColorCodes().getColorCodes().length == 0) {
            inMemoryStore.setIosColorCodes(cloudColorService.getAllIosColorCodes(
                    inMemoryStore.getUserToken(), inMemoryStore.getMasterToken()));
        }

        // Fetch ios device color from cloud api
        if (inMemoryStore.getIosColorCodes() != null
                && inMemoryStore.getIosColorCodes().getColorCodes() != null
                && inMemoryStore.getIosColorCodes().getColorCodes().length > 0) {
            IosColorCodeResponse.ColorCode selectedColorCode =
                    Arrays.stream(inMemoryStore.getIosColorCodes().getColorCodes())
                            .filter(colorCodeResponse ->
                                    StringUtils.equalsIgnoreCase(colorCodeResponse.getIdentifier(), productType)
                                            && StringUtils.equalsIgnoreCase(colorCodeResponse.getCode(), colorCode))
                            .findFirst().orElse(null);
            return selectedColorCode != null && StringUtils.isNotBlank(selectedColorCode.getColor())
                    ? selectedColorCode.getColor()
                    : null;
        }
        return null;
    }

}
