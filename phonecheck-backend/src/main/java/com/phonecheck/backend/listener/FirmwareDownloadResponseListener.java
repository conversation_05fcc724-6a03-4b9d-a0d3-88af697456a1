package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.FirmwareDownloadResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.FirmwareDownloadResponseMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class FirmwareDownloadResponseListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirmwareDownloadResponseListener.class);
    private final ApplicationEventPublisher eventPublisher;

    protected FirmwareDownloadResponseListener(final IMqttAsyncClient mqttClient,
                                               final ObjectMapper objectMapper,
                                               final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
    }


    @Async
    @EventListener
    public void onEvent(final FirmwareDownloadResponseEvent event) {
        final FirmwareDownloadResponseMessage message = new FirmwareDownloadResponseMessage();
        message.setFirmwareId(event.getFirmwareId());
        message.setFirmwareDownloadStatus(event.getFirmwareDownloadStatus());
        message.setProgress(event.getProgress());
        String topic = TopicBuilder.buildGenericTopic("firmware-download", "response");
        publishToMqttTopic(topic, message);
    }
}
