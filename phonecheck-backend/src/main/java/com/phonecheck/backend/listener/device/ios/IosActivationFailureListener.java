package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.IosResetDeviceService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.ActivationFailureStage;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.stage.DeviceState;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceReconnectRequireEvent;
import com.phonecheck.model.event.device.SynchronousEsnRequestEvent;
import com.phonecheck.model.event.device.ios.IosActivationFailureEvent;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosActivationFailureMessage;
import com.phonecheck.model.status.ActivationStatus;
import com.phonecheck.model.store.InMemoryStore;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Notifies broker of activation failure
 */
@Component
public class IosActivationFailureListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosActivationFailureListener.class);
    private final InMemoryStore inMemoryStore;
    private final ApplicationEventPublisher eventPublisher;
    private final DeviceStageUpdater stageUpdater;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceDataConversionAndExportService deviceDataConversionAndExportService;

    private final IosResetDeviceService iosResetDeviceService;

    public IosActivationFailureListener(final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                        final InMemoryStore inMemoryStore,
                                        final ApplicationEventPublisher eventPublisher,
                                        final DeviceStageUpdater stageUpdater,
                                        final DeviceConnectionTracker deviceConnectionTracker,
                                        final DeviceDataConversionAndExportService
                                                deviceDataConversionAndExportService,
                                        final IosResetDeviceService iosResetDeviceService) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.eventPublisher = eventPublisher;
        this.stageUpdater = stageUpdater;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.deviceDataConversionAndExportService = deviceDataConversionAndExportService;
        this.iosResetDeviceService = iosResetDeviceService;
    }

    @EventListener
    public void onEvent(final IosActivationFailureEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Activation failed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("IOS device failed to activate ({})", event.getActivationStatus().name());
        // Notify UI
        final IosActivationFailureMessage message = new IosActivationFailureMessage();
        final String topic = TopicBuilder.build(device, "activate", "failure");
        message.setId(device.getId());
        message.setActivationStatus(event.getActivationStatus());
        message.setIsErasedPerformed(deviceInTracker.getIsErasePerformed());
        message.setDeviceRestoreStatus(deviceInTracker.getRestoreStatus());
        publishToMqttTopic(topic, message);

        // Update the device state in the DB
        final ActivationFailureStage activationFailureStage = ActivationFailureStage
                .builder()
                .id(device.getId())
                .activationStatus(event.getActivationStatus())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .timestamp(System.currentTimeMillis())
                .build();

        if (ActivationStatus.FAILED_UNKNOWN.equals(event.getActivationStatus())
                || ActivationStatus.FAILED_NO_DEVICE.equals(event.getActivationStatus())
                || ActivationStatus.NOT_ACTIVATED.equals(event.getActivationStatus())) {
            deviceInTracker.setReconnectRequired(true);
            eventPublisher.publishEvent(
                    new DeviceReconnectRequireEvent(this,
                            device,
                            ErrorConstants.ACTIVATION_FAILED)
            );
        } else if (ActivationStatus.ICLOUD_LOCK_ON.equals(event.getActivationStatus())) {
            deviceInTracker.setDeviceLock(DeviceLock.ON);

            eventPublisher.publishEvent(
                    new DeviceReconnectRequireEvent(this,
                            device,
                            ErrorConstants.ICLOUD_LOCKED)
            );
        }

        LOGGER.debug("Updating device to stage {} in data store", activationFailureStage.getStage().name());
        stageUpdater.updateStage(activationFailureStage);

        // Update the in-memory device state
        deviceInTracker.setStage(DeviceStage.ACTIVATION_FAILED);

        /*
         * If a device came from recovery and had auto export enabled for successful erase/restore,
         * then perform a synchronous ESN check and then generate the auto export document.
         */
        if (DeviceState.DFU.equals(deviceInTracker.getDeviceState())) {
            LOGGER.info("Device restored from recovery mode, checking if auto export is needed");
            if (inMemoryStore.getLocalCustomizations().isAutoExport() &&
                    inMemoryStore.getLocalCustomizations().isExportOnSuccessfulErase()) {
                LOGGER.info("Auto export was enabled for successful restore, checking for ESN info if needed");
                if (StringUtils.isBlank(deviceInTracker.getEsnRawResponse()) &&
                        StringUtils.isBlank(deviceInTracker.getUsInsuranceBlackListResponse())) {
                    final SynchronousEsnRequestEvent synchronousEsnRequestEvent =
                            new SynchronousEsnRequestEvent(this, deviceInTracker,
                                    EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION);
                    eventPublisher.publishEvent(synchronousEsnRequestEvent);
                }
                deviceDataConversionAndExportService.triggerAutoExportOnEraseRestore(deviceInTracker);
            } else {
                LOGGER.info("Auto export is disabled for restore.");
            }
        }

        iosResetDeviceService.removeProfilesAndDeactivateDevice(device);
    }
}
