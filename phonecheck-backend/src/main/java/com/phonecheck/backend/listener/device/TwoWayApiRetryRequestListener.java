package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.api.cloud.TwoWayApiCallService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.*;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.TwoWayApiErrorDecisionMessage;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.LocalizationService;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import static com.phonecheck.model.twowayapi.TwoWayApiConstants.*;

/**
 * This Listener will be called to retry API in case of error
 * As of now we do not have any limit on number of times retry can be called
 * */
@Component
public class TwoWayApiRetryRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(TwoWayApiRetryRequestListener.class);
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final TwoWayApiCallService twoWayApiCallService;
    private final LocalizationService localizationService;
    private final ApplicationEventPublisher eventPublisher;

    private final InMemoryStore inMemoryStore;

    public TwoWayApiRetryRequestListener(final ObjectMapper objectMapper,
                                         final IMqttAsyncClient mqttClient,
                                         final DeviceConnectionTracker deviceConnectionTracker,
                                         final TwoWayApiCallService twoWayApiCallService,
                                         final LocalizationService localizationService,
                                         final ApplicationEventPublisher eventPublisher,
                                         final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.twoWayApiCallService = twoWayApiCallService;
        this.localizationService = localizationService;
        this.eventPublisher = eventPublisher;
        this.inMemoryStore = inMemoryStore;
    }

    @Async
    @EventListener
    public void onEvent(final TwoWayApiRetryRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("BStar api retry Called but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Calling BStar api retry");
        retryApiCall(deviceInTracker, event.getErrorTitle(), event.getPrintOperation());
    }

    private void retryApiCall(final Device device, final String errorTitle, final PrintOperation printOperation) {
        LOGGER.info("Raising event for API call. Error title {}", errorTitle);
        switch (errorTitle) {
            case SOURCE_API_ERROR ->  {
                device.setSourceApiCalled(false);
                eventPublisher.publishEvent(new SourceApiCallRequestEventV2(this, device));
            }
            case RESULTS_API_ERROR -> eventPublisher.publishEvent(new ResultsApiCallRequestEventV2(this,
                    device));
            case LABEL_API_ERROR ->  eventPublisher.publishEvent(new LabelApiCallRequestEventV2(this, device,
                    printOperation));
            default -> LOGGER.warn("No appropriate retry error present.");
        }
    }

    /**
     * This handles logic for bStar api retries. It performs operation to decide if pop-up needed
     * to cancel or go for a retry
     *
     * @param event TwoWayApiRetryRequestEvent
     * @param deviceInTracker Device
     */
    private void bStarApiRetries(final TwoWayApiRetryRequestEvent event, final Device deviceInTracker) {
        ObjectNode flattenedApiResponse = twoWayApiCallService.flattenObjectNode(getBStarResponseObject(event));
        if (flattenedApiResponse != null && (flattenedApiResponse.has(HTTP_STATUS_CODE_KEY) &&
                StringUtils.equalsIgnoreCase(flattenedApiResponse.get(HTTP_STATUS_CODE_KEY).textValue(), "200"))) {
            LOGGER.info("BStar api retries success, publish cancel-bstar-error topic");
            eventPublisher.publishEvent(new ResumeTwoWayApiEvent(this, deviceInTracker,
                    getBStarResponseObject(event), event.getErrorTitle(), event.getApiCallType(),
                    event.getPrintOperation()));
        } else {
            LOGGER.info("BStar api retries, publish bStar-decision topic");
            final TwoWayApiErrorDecisionMessage message = new TwoWayApiErrorDecisionMessage();

            String topic =  inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST ?
                    TopicBuilder.buildGenericTopic("iwatch-bstar-decision", "request") : TopicBuilder
                    .buildGenericTopic("bstar-decision", "request");

           // final String topic = TopicBuilder.buildGenericTopic("bstar-decision", "request");
            message.setId(deviceInTracker.getId());
            message.setResponseObject(getBStarResponseObject(event));
            message.setRequestObject(event.getRequestObject());
            message.setErrorTitle(event.getErrorTitle());
            message.setApiCallType(event.getApiCallType());
            message.setPrintOperation(event.getPrintOperation());
            message.setFromRetry(true);
            publishToMqttTopic(topic, message);
        }
    }


    /**
     * This method fetch response from cloud for bStar api
     *
     * @param event TwoWayApiRetryRequestEvent
     * @return Response as ObjectNode
     */
    private ObjectNode getBStarResponseObject(final TwoWayApiRetryRequestEvent event) {
        ObjectNode apiResponse = null;
        LOGGER.info("Calling BStar api for integration response Title {}", event.getErrorTitle());
        if (event.getErrorTitle().equalsIgnoreCase(
                localizationService.getLanguageSpecificText("sourceApiError"))) {
            apiResponse = twoWayApiCallService.getSourceApiIntegrationResponse(event.getRequestObject());
            LOGGER.info("Source Api Response : {}", apiResponse);
        } else if (event.getErrorTitle().equalsIgnoreCase(
                localizationService.getLanguageSpecificText("labelApiError"))) {
            apiResponse = twoWayApiCallService.getLabelApiIntegrationResponse(event.getRequestObject());
            LOGGER.info("Label Api Response : {}", apiResponse);
        } else if (event.getErrorTitle().equalsIgnoreCase(
                localizationService.getLanguageSpecificText("resultsApiError"))) {
            apiResponse = twoWayApiCallService.getResultsApiIntegrationResponse(event.getRequestObject());
            LOGGER.info("Results Api Response : {}", apiResponse);
        }
        return apiResponse;
    }
}