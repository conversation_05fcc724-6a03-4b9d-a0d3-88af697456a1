package com.phonecheck.backend.listener.transaction;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.VendorDetailDBService;
import com.phonecheck.model.event.transaction.TransactionContinueRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.TransactionContinueResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Listener to load transaction continue data on request from UI
 */
@Component
public class TransactionContinueRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionContinueRequestListener.class);

    private final InMemoryStore inMemoryStore;
    private final VendorDetailDBService vendorDetailDBService;

    public TransactionContinueRequestListener(final IMqttAsyncClient mqttClient,
                                              final ObjectMapper objectMapper,
                                              final InMemoryStore inMemoryStore,
                                              final VendorDetailDBService vendorDetailDBService) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.vendorDetailDBService = vendorDetailDBService;
    }

    @EventListener
    public void onEvent(final TransactionContinueRequestEvent event) {
        String topic = TopicBuilder.buildGenericTopic("transaction-continue", "response");
        TransactionContinueResponseMessage transactionContinueResponseMessage =
                new TransactionContinueResponseMessage();

        Transaction transactionToContinue = event.getTransaction();

        try {
            vendorDetailDBService.setCurrentTransaction(transactionToContinue);
            inMemoryStore.setTransaction(transactionToContinue);

            transactionContinueResponseMessage.setSuccessful(true);
        } catch (Exception e) {
            LOGGER.error("Error occurred while continuing the old transaction {}",
                    transactionToContinue.getTransactionId());

            transactionContinueResponseMessage.setSuccessful(false);
        }

        transactionContinueResponseMessage.setSuccessful(true);
        publishToMqttTopic(topic, transactionContinueResponseMessage);
    }
}