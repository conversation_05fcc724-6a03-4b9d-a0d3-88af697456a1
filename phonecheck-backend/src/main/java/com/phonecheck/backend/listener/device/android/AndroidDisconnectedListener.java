package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.Cloud3DeviceDataSyncService;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.dao.service.UphInfoDbService;
import com.phonecheck.info.android.AndroidAtDeviceStreamService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.SyncableDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidDisconnectedEvent;
import com.phonecheck.model.event.syslog.StopDeviceSysLogEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.UphInfoUpdateMessage;
import com.phonecheck.model.mqtt.messages.ios.IosMqttDisconnectionMessage;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.TestResults;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Notifies broker when an IOS device is disconnected
 */
@Component
public class AndroidDisconnectedListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDisconnectedListener.class);
    private final InMemoryStore inMemoryStore;
    private final ApplicationEventPublisher eventPublisher;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final CloudDeviceDataSyncService cloudDeviceDataSyncService;
    private final Cloud3DeviceDataSyncService cloud3DeviceDataSyncService;
    private final UphInfoDbService uphInfoDbService;
    private final DeviceAutomationQueueService automationQueueService;
    private final AndroidAtDeviceStreamService androidAtDeviceStreamService;


    public AndroidDisconnectedListener(final IMqttAsyncClient mqttClient,
                                       final ObjectMapper objectMapper,
                                       final ApplicationEventPublisher eventPublisher,
                                       final InMemoryStore inMemoryStore,
                                       final DeviceConnectionTracker deviceConnectionTracker,
                                       final CloudDeviceDataSyncService cloudDeviceDataSyncService,
                                       final Cloud3DeviceDataSyncService cloud3DeviceDataSyncService,
                                       final UphInfoDbService uphInfoDbService,
                                       final DeviceAutomationQueueService automationQueueService,
                                       final AndroidAtDeviceStreamService androidAtDeviceStreamService) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.cloudDeviceDataSyncService = cloudDeviceDataSyncService;
        this.cloud3DeviceDataSyncService = cloud3DeviceDataSyncService;
        this.uphInfoDbService = uphInfoDbService;
        this.automationQueueService = automationQueueService;
        this.androidAtDeviceStreamService = androidAtDeviceStreamService;
    }

    @Async
    @EventListener
    public void onEvent(final AndroidDisconnectedEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final String topic = TopicBuilder.build(device, "disconnected");

        AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());

        if (!event.isManualRelease()
                && deviceInTracker != null
                && (DeviceStage.ERASE_IN_PROGRESS.equals(deviceInTracker.getStage())
                || deviceInTracker.isEraseInProgress())) {
            LOGGER.info("Device is being erased so don't remove from memory.");
            return;
        }

        if (deviceInTracker != null) {
            if (AndroidConnectionMode.ADB.equals(deviceInTracker.getAndroidConnectionMode())) {
                // Stops syslog process for disconnected device
                eventPublisher.publishEvent(new StopDeviceSysLogEvent(this, device));
            } else {
                // stop at device info process
                androidAtDeviceStreamService.stopAtDeviceStreamRoutine(deviceInTracker);
            }
        }

        // Notify UI
        LOGGER.info("Android device was disconnected");
        IosMqttDisconnectionMessage message = new IosMqttDisconnectionMessage();
        message.setDeviceType(device.getDeviceType());
        message.setId(device.getId());
        publishToMqttTopic(topic, message);

        LOGGER.info("Published Android device disconnected message to UI");

        boolean isPairingFailedOrInitialStageBeforeDisconnect = false;

        // Set device as Disconnected and update Column Info
        if (deviceInTracker != null) {

            isPairingFailedOrInitialStageBeforeDisconnect =
                    deviceInTracker.getStage().equals(DeviceStage.PAIRING_FAILED) ||
                            deviceInTracker.getStage().equals(DeviceStage.INITIAL_CONNECTION);

            deviceInTracker.setStage(DeviceStage.DISCONNECTED);

            // - if device was never paired then don't have to enter its uph info
            // - publish uph info update on any device disconnection to keep it up to date
            if (StringUtils.isNotBlank(deviceInTracker.getSerial())) {
                WorkingStatus workingStatus = WorkingStatus.PENDING;
                if (deviceInTracker.getDeviceTestResult() != null &&
                        deviceInTracker.getDeviceTestResult().getTestResults() != null) {
                    TestResults testResults = deviceInTracker.getDeviceTestResult().getTestResults();
                    if (Boolean.TRUE.equals(testResults.getTestingCompleted())) {
                        if (testResults.getFailed() == null || testResults.getFailed().isEmpty()) {
                            workingStatus = WorkingStatus.YES;
                        } else {
                            workingStatus = WorkingStatus.NO;
                        }
                    }
                }
                uphInfoDbService.updateUphOnDisconnect(deviceInTracker.getSerial(), workingStatus,
                        deviceInTracker.getIsErasePerformed());
                publishUphInfoToUi();
            }
        } else {
            LOGGER.info("Android device is already removed from tracker before device could be marked as disconnected");
        }

        if (!isPairingFailedOrInitialStageBeforeDisconnect &&
                !cloudDeviceDataSyncService.isDeviceInDataSyncSet(device)) {
            // Sync device Data to the cloud
            final SyncableDevice syncableDevice = SyncableDevice.builder()
                    .transaction(inMemoryStore.getTransaction())
                    .device(deviceInTracker)
                    .source(getClass())
                    .build();
            new Thread(() -> {
                if (deviceInTracker != null) {
                    setDeviceIdMDC(deviceInTracker.getId());
                    cloudDeviceDataSyncService.syncDeviceRecordToCloud(deviceInTracker, inMemoryStore.getTransaction());
                    final String ddResponse = cloudDeviceDataSyncService.
                            syncDeviceDisconnectOnCloud(deviceInTracker, this);
                    LOGGER.info("Device disconnected sync response: {}", ddResponse);
                    cloud3DeviceDataSyncService.enqueueDeviceDataSyncRequest(syncableDevice);
                }
            }).start();
        } else {
            LOGGER.info("Device disconnected on pairing failed ot Initial connection. " +
                    "Hence, not syncing the device record or disconnect status to the cloud");
        }

        // Update the in-memory model
        deviceConnectionTracker.deviceDisconnected(device.getId());
        automationQueueService.removeDeviceAutomationQueue(device.getId());
    }

    /**
     * Publish UPH info to the UI
     */
    private void publishUphInfoToUi() {
        int processedToday = uphInfoDbService.getTotalDeviceCountProcessedToday();
        int processedCurrentHour = uphInfoDbService.getTotalDeviceCountProcessedInTheCurrentHour();
        int processedLastHour = uphInfoDbService.getTotalDeviceCountProcessedInTheLastHour();
        int uphPrediction = uphInfoDbService.getUPHPredictionInTheCurrentHour();
        int passedToday = uphInfoDbService.getTotalDeviceCountByWorkingStatus(WorkingStatus.YES);
        int pendingToday = uphInfoDbService.
                getTotalDeviceCountByWorkingStatus(WorkingStatus.PENDING);
        int erasedToday = uphInfoDbService.getTotalDeviceCountByErased();

        final String uphTopic = TopicBuilder.buildGenericTopic("uph", "info");
        final UphInfoUpdateMessage uphMessage = new UphInfoUpdateMessage();
        uphMessage.setProcessedLastHour(processedLastHour);
        uphMessage.setProcessedThisHour(processedCurrentHour);
        uphMessage.setTotalProcessedToday(processedToday);
        uphMessage.setUphPrediction(uphPrediction);
        uphMessage.setTotalPassedToday(passedToday);
        uphMessage.setTotalPendingToday(pendingToday);
        uphMessage.setTotalErasedToday(erasedToday);
        publishToMqttTopic(uphTopic, uphMessage);
    }
}
