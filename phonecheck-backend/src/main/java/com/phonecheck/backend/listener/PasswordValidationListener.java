package com.phonecheck.backend.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.PasswordValidationService;
import com.phonecheck.model.event.PasswordValidationRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.PasswordValidationResponseMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class PasswordValidationListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PasswordValidationListener.class);
    private final PasswordValidationService passwordValidationService;

    public PasswordValidationListener(final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                      final PasswordValidationService passwordValidationService) {
        super(mqttClient, objectMapper);
        this.passwordValidationService = passwordValidationService;
    }

    /**
     * Handles password validation request
     *
     * @param event PasswordValidationRequestEvent
     */
    @Async
    @EventListener
    public void onPasswordValidationRequest(final PasswordValidationRequestEvent event) throws JsonProcessingException {
        if (event == null || event.getRequest() == null || event.getRequest().getPassword() == null) {
            LOGGER.error("Invalid PasswordValidationRequestEvent received");
            return;
        }
        boolean result = passwordValidationService.isPasswordCorrect(event.getRequest().getPassword());
        PasswordValidationResponseMessage response = new PasswordValidationResponseMessage(result);
        String responseTopic = TopicBuilder.buildGenericTopic("password-validation", "response");
        MqttTopicMessage message = new MqttTopicMessage(responseTopic, getMapper().writeValueAsString(response));
        publishToMqttTopic(responseTopic, message);
    }
}
