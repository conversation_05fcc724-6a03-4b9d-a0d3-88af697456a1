package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.app.AppFileService;
import com.phonecheck.app.ios.IosAppService;
import com.phonecheck.app.ios.IosAppVersionService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.constants.ios.IosAppIdConstants;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.AppInstallFailureStage;
import com.phonecheck.model.device.stage.AppInstallSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceReconnectRequireEvent;
import com.phonecheck.model.event.device.ios.IosAppInstallEvent;
import com.phonecheck.model.event.device.ios.IosPushFilesEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.AppInstallMessage;
import com.phonecheck.model.mqtt.messages.ios.IosAppInstallFailureMessage;
import com.phonecheck.model.mqtt.messages.ios.IosAppInstallSuccessMessage;
import com.phonecheck.model.mqtt.messages.ios.IosReadyStatusMessage;
import com.phonecheck.model.status.AppInstallStatus;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;

import static com.phonecheck.model.constants.ios.Ios32BitDeviceModels.IOS_32_BIT_DEVICES;

/**
 * Listener to install app on the device, update stage in DB and notify UI
 */
@Component
public class IosAppInstallListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosAppInstallListener.class);
    private final DeviceStageUpdater deviceStageUpdater;
    private final IosAppService appService;
    private final IosAppVersionService iosAppVersionService;
    private final AppFileService appFileService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final ApplicationEventPublisher eventPublisher;
    private final TimerLoggerUtil timerLoggerUtil;
    private final IosPairingUtil iosPairingUtil;


    public IosAppInstallListener(final DeviceStageUpdater deviceStageUpdater,
                                 final IosAppService appService, final AppFileService appFileService,
                                 final IosAppVersionService iosAppVersionService,
                                 final DeviceConnectionTracker deviceConnectionTracker,
                                 final InMemoryStore inMemoryStore, final ApplicationEventPublisher eventPublisher,
                                 final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                 final TimerLoggerUtil timerLoggerUtil,
                                 final IosPairingUtil iosPairingUtil) {
        super(mqttClient, objectMapper);
        this.iosPairingUtil = iosPairingUtil;
        this.deviceStageUpdater = deviceStageUpdater;
        this.appService = appService;
        this.appFileService = appFileService;
        this.iosAppVersionService = iosAppVersionService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.eventPublisher = eventPublisher;
        this.timerLoggerUtil = timerLoggerUtil;
    }

    @EventListener
    public void onEvent(final IosAppInstallEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("App installation requested but device has been disconnected, stopping processing.");
            return;
        }

        PairStatus pairStatus = iosPairingUtil.checkAndNotifyUiIfNotPaired(deviceInTracker);
        if (!PairStatus.PAIRED.equals(pairStatus)) {
            if (deviceInTracker.isEraseInProgress()) {
                LOGGER.warn("Device is currently erasing, skipping to show reconnect popup on UI");
                return;
            }

            LOGGER.warn("Device is not paired. Cannot continue processing. PairStatus: {}", pairStatus);
            deviceInTracker.setReconnectRequired(true);
            eventPublisher.publishEvent(new DeviceReconnectRequireEvent(this, device,
                    ErrorConstants.PAIRING_FAILED));
            return;
        }
        installApp(deviceInTracker, event.isManualInstall(),
                IOS_32_BIT_DEVICES.contains(deviceInTracker.getProductType()));
    }

    /**
     * Install application on target device and after that push test plan
     *
     * @param device          target iphone device
     * @param isManualInstall is app installed manually on the device
     * @param is32BitApp      is device 32 bit
     */
    private void installApp(final IosDevice device, final boolean isManualInstall, final boolean is32BitApp) {

        LocalDateTime operationInstallAppStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(), "Installing app start", operationInstallAppStartTime);

        final File appIpaFile;
        final String iosAppIdentifier;
        if (is32BitApp) {
            LOGGER.info("Install 32 bit iOS app enterprise edition");
            appIpaFile = appFileService.get32BitAppFile();
            iosAppIdentifier = IosAppIdConstants.APP_STORE_BUNDLE_ID_2;
        } else {
            LOGGER.info("Install 64 bit iOS consumer app");
            appIpaFile = appFileService.getAppFile(device.getDeviceFamily(), StringUtils.EMPTY);
            iosAppIdentifier = inMemoryStore.getIosAppBundleIdentifier();
        }
        final String loadedPhonecheckAppVersion = iosAppVersionService.getIosAppIpaVersion(appIpaFile);
        final String vppToken = inMemoryStore.getVppToken();

        String topic;
        try {
            LOGGER.info("Initiate App installation on IOS device");
            // Notify UI that app, install has started
            topic = TopicBuilder.build(device, "app-install", "start");
            AppInstallMessage appInstallMessage = new AppInstallMessage();
            appInstallMessage.setId(device.getId());
            publishToMqttTopic(topic, appInstallMessage);

            AppInstallStatus appInstallStatus = appService.installDiagnosticApp(device, loadedPhonecheckAppVersion,
                    appIpaFile.getPath(), iosAppIdentifier, vppToken);

            if (AppInstallStatus.SUCCESS.equals(appInstallStatus) ||
                    AppInstallStatus.NOT_REQUIRED.equals(appInstallStatus)) {

                LOGGER.info("App installation status on device: {}", appInstallStatus);

                // Update the device state in the DB
                final AppInstallSuccessStage stage = AppInstallSuccessStage
                        .builder()
                        .id(device.getId())
                        .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                        .build();
                deviceStageUpdater.updateStage(stage);

                // Notify UI that app installation was successful
                if (!isManualInstall) {
                    LOGGER.info("App installed successfully as part of automatic processing.");
                    topic = TopicBuilder.build(device, "app-install", "success");
                    final IosAppInstallSuccessMessage appInstallSuccessMessage =
                            new IosAppInstallSuccessMessage();
                    appInstallSuccessMessage.setId(device.getId());
                    appInstallSuccessMessage.setAppAlreadyInstalled(
                            AppInstallStatus.NOT_REQUIRED.equals(appInstallStatus));
                    publishToMqttTopic(topic, appInstallSuccessMessage);
                } else {
                    LOGGER.info("Manual app installation done successfully from the UI.");
                    topic = TopicBuilder.build(device, "ready");
                    final IosReadyStatusMessage readyMessage = new IosReadyStatusMessage();
                    readyMessage.setId(device.getId());
                    readyMessage.setReady(true);
                    publishToMqttTopic(topic, readyMessage);
                }
                device.setAppInstalled(true);
                timerLoggerUtil.printTimerLog(device.getId(), "Installing app end", operationInstallAppStartTime);

                // Publish event to push config files to the device
                final IosPushFilesEvent iosPushFilesEvent = new IosPushFilesEvent(this, device);
                eventPublisher.publishEvent(iosPushFilesEvent);
            } else if (appInstallStatus.equals(AppInstallStatus.ERROR_LOCKDOWND_FAILED)) {
                if (!device.isPairAttempted()) {
                    LOGGER.info("Pairing attempt not yet made for device");
                    device.setPairAttempted(true);
                    PairStatus pairStatus = iosPairingUtil.checkAndNotifyUiIfNotPaired(device);
                    if (PairStatus.PAIRED.equals(pairStatus)) {
                        installApp(device, isManualInstall, is32BitApp);
                    } else {
                        LOGGER.info("Device pairing failed, reconnect required");
                        iosPairingUtil.showReconnectPopup(device);
                    }
                } else {
                    LOGGER.info("Device already attempted pairing, reconnect required");
                    iosPairingUtil.showReconnectPopup(device);
                }
            } else {
                LOGGER.error("Could not install app on IOS device. Status: {}", appInstallStatus);
                timerLoggerUtil.printTimerLog(device.getId(), "Installing app failed", operationInstallAppStartTime);

                // Update the device state in the DB
                final AppInstallFailureStage stage = AppInstallFailureStage
                        .builder()
                        .id(device.getId())
                        .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                        .build();
                deviceStageUpdater.updateStage(stage);

                // Notify UI that app, install has failed
                if (!isManualInstall) {
                    LOGGER.error("App installation failed as part of automatic processing.");

                    topic = TopicBuilder.build(device, "app-install", "failure");
                    IosAppInstallFailureMessage appInstallFailureMessage = new IosAppInstallFailureMessage();
                    appInstallFailureMessage.setId(device.getId());
                    publishToMqttTopic(topic, appInstallFailureMessage);
                } else {
                    LOGGER.error("Manual app installation failed from the UI.");

                    topic = TopicBuilder.build(device, "ready");
                    final IosReadyStatusMessage readyMessage = new IosReadyStatusMessage();
                    readyMessage.setId(device.getId());
                    readyMessage.setReady(false);
                    publishToMqttTopic(topic, readyMessage);
                }
            }
        } catch (final IOException e) {
            LOGGER.error("Could not install app on the IOS device", e);
        }
    }
}
