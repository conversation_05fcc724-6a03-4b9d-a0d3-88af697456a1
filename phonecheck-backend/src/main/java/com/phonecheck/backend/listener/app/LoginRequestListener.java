package com.phonecheck.backend.listener.app;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.LoginService;
import com.phonecheck.communicator.CommunicatorService;
import com.phonecheck.model.constants.StationConstants;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.RunningMode;
import com.phonecheck.model.event.app.LoginRequestEvent;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.login.UserLoginDetails;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.LoginResponseMessage;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.RunningModeUtil;
import com.phonecheck.model.util.UserLoginDetailsUtil;
import com.phonecheck.station.HardwareInfoService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class LoginRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginRequestListener.class);

    private static final String FIRMWARE = "firmware";
    private static final String PROFILE = "profile";
    private static final String SKU_SCHEMA = "sku_schema";

    private final LoginService loginService;
    private final HardwareInfoService hardwareInfoService;
    private final UserLoginDetailsUtil userCredentialsUtil;
    private final InMemoryStore inMemoryStore;
    private final CommunicatorService communicatorService;
    private final RunningModeUtil runningModeUtil;


    protected LoginRequestListener(final IMqttAsyncClient mqttClient,
                                   final ObjectMapper objectMapper,
                                   final LoginService loginService,
                                   final HardwareInfoService hardwareInfoService,
                                   final UserLoginDetailsUtil userCredentialsUtil,
                                   final InMemoryStore inMemoryStore,
                                   final CommunicatorService communicatorService,
                                   final RunningModeUtil runningModeUtil) {
        super(mqttClient, objectMapper);
        this.loginService = loginService;
        this.hardwareInfoService = hardwareInfoService;
        this.userCredentialsUtil = userCredentialsUtil;
        this.inMemoryStore = inMemoryStore;
        this.communicatorService = communicatorService;
        this.runningModeUtil = runningModeUtil;
    }

    @Async
    @EventListener
    public void onEvent(final LoginRequestEvent event) {
        LOGGER.info("Login request initiated");

        UserLoginDetails userLoginDetails = event.getUserLoginDetails();
        String currentLanguage = event.getCurrentLanguage();

        try {
            inMemoryStore.setCurrentLanguage(currentLanguage);
            configureProcessingModeToInMemory();

            final String pcId = hardwareInfoService.getPcId();

            final String result = loginService.login(userLoginDetails, pcId);
            // save login details in memory
            inMemoryStore.setUserLoginDetails(userLoginDetails);

            LoginResponseMessage responseMessage = new LoginResponseMessage();
            responseMessage.setEuServer(inMemoryStore.isEuServer());
            responseMessage.setUser(inMemoryStore.getUserName());
            responseMessage.setLoginMsg(result);
            responseMessage.setIosAppVersion(inMemoryStore.getIosAppVersion());
            responseMessage.setAndroidAppVersion(inMemoryStore.getAndroidAppVersion());
            responseMessage.setUserType(userLoginDetails.getUserType());
            responseMessage.setTransaction(inMemoryStore.getTransaction());
            responseMessage.setTesterName(inMemoryStore.getTesterName());
            responseMessage.setMasterName(inMemoryStore.getMasterName());
            responseMessage.setAssignedCloudCustomization(inMemoryStore.getAssignedCloudCustomization());
            responseMessage.setLicenseId(inMemoryStore.getLicenseId());
            responseMessage.setMasterToken(inMemoryStore.getMasterToken());
            responseMessage.setUserToken(inMemoryStore.getUserToken());
            responseMessage.setFirmwarePath(inMemoryStore.getFirmwareDownloadPath());
            responseMessage.setWarehouseName(inMemoryStore.getWarehouseName());
            responseMessage.setMasterId(inMemoryStore.getMasterId());
            responseMessage.setTesterId(inMemoryStore.getTesterId());
            responseMessage.setDaysForImei(inMemoryStore.getDaysForImei());
            responseMessage.setCustomClientLogoUrl(inMemoryStore.getCustomClientLogoUrl());
            responseMessage.setShopfloorGrades(inMemoryStore.getShopfloorGrades());

            final String topic = TopicBuilder.buildGenericTopic("login", "response");

            Map<String, LabelFxmlResponse.Data> labelFxmlResponseMap = inMemoryStore.getLabelDataMap();
            if (labelFxmlResponseMap != null) {
                communicatorService.setData(topic, labelFxmlResponseMap);
            }
            Map<String, FirmwareModel.FirmwareResponse> latestFirmwareInfo = inMemoryStore.getFirmwareModels();
            if (latestFirmwareInfo != null) {
                communicatorService.setData(FIRMWARE, latestFirmwareInfo);
            }
            Map<String, SelectPackageResponse.ProfilesConfiguration> profile =
                    inMemoryStore.getSelectPackageResponse();
            if (profile != null) {
                communicatorService.setData(PROFILE, profile);
            }
            if (inMemoryStore.getSkuSchemaResponse() != null) {
                communicatorService.setData(SKU_SCHEMA, inMemoryStore.getSkuSchemaResponse());
            }
            // save userLogin details
            if (StationConstants.LOGIN_SUCCESS.equals(result)) {
                userCredentialsUtil.saveUserLoginDetails(userLoginDetails);
            }

            // Notify UI
            publishToMqttTopic(topic, responseMessage);
        } catch (Exception e) {
            LOGGER.error("Error while trying to login", e);
        }
    }

    /**
     * Configures the device process mode in memory according to the current running mode in a file.
     */
    private void configureProcessingModeToInMemory() {
        RunningMode currentRunningMode = runningModeUtil.getRunningModeFromFile();
        LOGGER.info("Current running mode: {}", currentRunningMode);
        if (RunningMode.IWATCH_MODE == currentRunningMode) {
            inMemoryStore.setDeviceConnectionMode(DeviceConnectionMode.IWATCH_HOST);
        } else if (RunningMode.AIRPOD_MODE == currentRunningMode) {
            inMemoryStore.setDeviceConnectionMode(DeviceConnectionMode.AIR_POD);
        } else if (RunningMode.PRE_CHECK == currentRunningMode) {
            inMemoryStore.setDeviceConnectionMode(DeviceConnectionMode.PRE_CHECK);
        } else {
            inMemoryStore.setDeviceConnectionMode(DeviceConnectionMode.PROCESS);
        }
    }
}
