package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidDeviceSdCardWarningEvent;
import com.phonecheck.model.event.device.android.AndroidEraseRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidDeviceSdCardDefectMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.util.TestResultsUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Notifies MQTT broker when DONE action for SD card warning
 * is processed. It will notify whether SD card detected post
 * click of DONE
 */
@Component
public class AndroidDeviceSdCardWarningListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDeviceSdCardWarningListener.class);
    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final DeviceTestResultDBService deviceTestResultDBService;
    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final ApplicationEventPublisher eventPublisher;

    protected AndroidDeviceSdCardWarningListener(final IMqttAsyncClient mqttClient,
                                                 final ObjectMapper objectMapper,
                                                 final AndroidDeviceInfoService androidDeviceInfoService,
                                                 final DeviceTestResultDBService deviceTestResultDBService,
                                                 final InMemoryStore inMemoryStore,
                                                 final DeviceConnectionTracker deviceConnectionTracker,
                                                 final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.androidDeviceInfoService = androidDeviceInfoService;
        this.deviceTestResultDBService = deviceTestResultDBService;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
    }

    @EventListener
    public void onEvent(final AndroidDeviceSdCardWarningEvent event) {
        Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        LOGGER.info("Received DONE action for SD card warning for the device");

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("SD card warning Done action cannot be processed. " +
                    "Seems device has been disconnected, stopping processing");
            return;
        }

        if (!androidDeviceInfoService.isSdCardCheckEnabledAndSdCardDetected((AndroidDevice) deviceInTracker)
                && event.isEraseRequired()) {
            eventPublisher.publishEvent(new AndroidEraseRequestEvent(this, deviceInTracker));
        }

        boolean isSDCardDefectRequired = false;

        boolean failSDCardNotRemoved = inMemoryStore.getAssignedCloudCustomization().getWorkflow()
                .getWarningMessages().isFailSDCardNotRemoved();

        if (androidDeviceInfoService.isSdCardCheckEnabledAndSdCardDetected((AndroidDevice) deviceInTracker)
                && failSDCardNotRemoved) {
            LOGGER.info("failSDCardNotRemoved is true, adding SD card defect");
            AndroidDeviceSdCardDefectMessage defectMsg = new AndroidDeviceSdCardDefectMessage();
            defectMsg.setDeviceId(deviceInTracker.getId());
            defectMsg.setDefectToBeAdded(true);
            publishToMqttTopic(TopicBuilder.build(deviceInTracker, "sd-card", "defect"), defectMsg);
            isSDCardDefectRequired = true;
        } else {
            LOGGER.info("SD card not found or failSDCardNotRemoved is false, " +
                    "so SD card defect not required in failed tests");
        }

        LOGGER.info("Defect list post sim-card action processing: {}", isSDCardDefectRequired);

        final List<String> failedTests = deviceInTracker.getDeviceTestResult().getTestResults().getFailed();
        if (isSDCardDefectRequired) {
            if (!failedTests.contains(InitialDefectKey.SD_CARD_DETECTED.getKey())) {
                failedTests.add(InitialDefectKey.SD_CARD_DETECTED.getKey());
            }
        } else {
            failedTests.remove(InitialDefectKey.SD_CARD_DETECTED.getKey());
        }

        deviceTestResultDBService.updateFailedTests(
                String.valueOf(inMemoryStore.getTransaction().getTransactionId()),
                deviceInTracker.getSerial(),
                TestResultsUtil.listToCommaSeparatedString(failedTests)
        );
    }
}
