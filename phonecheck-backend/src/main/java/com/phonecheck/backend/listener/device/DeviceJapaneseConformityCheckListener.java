package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.JapaneseConformityCheckService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceJapaneseConformityCheckEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.JapaneseConformityResponseMessage;
import com.phonecheck.model.status.JapaneseConformityStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class DeviceJapaneseConformityCheckListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceJapaneseConformityCheckListener.class);
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final JapaneseConformityCheckService japaneseConformityCheckService;

    public DeviceJapaneseConformityCheckListener(final IMqttAsyncClient mqttClient,
                                                 final ObjectMapper objectMapper,
                                                 final DeviceConnectionTracker deviceConnectionTracker,
                                                 final JapaneseConformityCheckService japaneseConformityCheckService) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.japaneseConformityCheckService = japaneseConformityCheckService;
    }

    /**
     * Handles device ESN request event
     *
     * @param event DeviceJapaneseConformityCheckEvent
     */
    @Async
    @EventListener
    public void onEvent(final DeviceJapaneseConformityCheckEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Japanese conformity check requested but the device has been disconnected," +
                    " stopping the process.");
            return;
        }

        LOGGER.info("Initiate japanese conformity check retrieval request for the device");

        String model;
        if (deviceInTracker instanceof IosDevice iosDevice) {
            model = iosDevice.getRegulatoryModelNumber();
        } else {
            model = deviceInTracker.getModelNo();
        }

        JapaneseConformityStatus japaneseConformityStatus = japaneseConformityCheckService
                .checkDeviceJapaneseConformityCertified(model);

        deviceInTracker.setJapaneseConformityStatus(japaneseConformityStatus);

        LOGGER.info("Japanese conformity certification status for device model {} is {}",
                model, japaneseConformityStatus);

        final String topic = TopicBuilder.build(device, "jConformity", "response");

        JapaneseConformityResponseMessage responseMessage = new JapaneseConformityResponseMessage();
        responseMessage.setId(device.getId());
        responseMessage.setJapaneseConformityStatus(japaneseConformityStatus);
        publishToMqttTopic(topic, responseMessage);
    }
}
