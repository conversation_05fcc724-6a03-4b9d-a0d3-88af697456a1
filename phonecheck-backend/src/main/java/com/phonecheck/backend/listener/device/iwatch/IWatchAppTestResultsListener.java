package com.phonecheck.backend.listener.device.iwatch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.device.results.IosTestResultsService;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.event.device.iwatch.IWatchAppTestResultEvent;
import com.phonecheck.model.event.device.iwatch.IWatchTestResultAutomationEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.iwatch.IWatchTestedMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.BatteryResults;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.DeviceTestResultStatus;
import com.phonecheck.model.test.TestResults;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import static com.phonecheck.model.twowayapi.TwoWayApiConstants.GRADING_PERFORMED_KEY;

/**
 * Notifies broker and initiates pairing when an iWatch host device is connected
 */
@Component
public class IWatchAppTestResultsListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchAppTestResultsListener.class);
    public static final String BATTERY_DRAIN = "Battery Drain";

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final IosTestResultsService testResultsService;
    private final InMemoryStore inMemoryStore;
    private final ApplicationEventPublisher eventPublisher;
    private final CloudDeviceDataSyncService cloudDeviceDataSyncService;
    private final DeviceAutomationQueueService automationQueueService;
    private final DeviceTestResultDBService deviceTestResultDBService;

    public IWatchAppTestResultsListener(final IMqttAsyncClient mqttClient,
                                        final ObjectMapper objectMapper,
                                        final DeviceConnectionTracker deviceConnectionTracker,
                                        final IosTestResultsService testResultsService,
                                        final InMemoryStore inMemoryStore,
                                        final ApplicationEventPublisher eventPublisher,
                                        final CloudDeviceDataSyncService cloudDeviceDataSyncService,
                                        final DeviceTestResultDBService deviceTestResultDBService,
                                        final DeviceAutomationQueueService automationQueueService) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.testResultsService = testResultsService;
        this.inMemoryStore = inMemoryStore;
        this.eventPublisher = eventPublisher;
        this.cloudDeviceDataSyncService = cloudDeviceDataSyncService;
        this.automationQueueService = automationQueueService;
        this.deviceTestResultDBService = deviceTestResultDBService;
    }

    @Async
    @EventListener
    public void onEvent(final IWatchAppTestResultEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Device has been disconnected while syncing iWatch app test result, stopping processing.");
            return;
        }

        LOGGER.info("IWatch test results ready on the IOS device.");
        try {
            DeviceTestResultStatus testResultFromFileStatus = testResultsService.getTestResults(device,
                    inMemoryStore.getIosAppBundleIdentifier());
            DeviceTestResult deviceTestResultFromFile = testResultFromFileStatus.getDeviceTestResult();

            if (deviceTestResultFromFile == null ||
                    deviceTestResultFromFile.getTestResults() == null) {
                LOGGER.error("Failed to fetch test results from the file");
                return;
            } else {
                // Get in-app grade
                String inAppGrade = deviceTestResultFromFile.getGradeResults();
                // if in-app grade is not present then set it to device grade, if that is also blank then check local db
                if (StringUtils.isBlank(inAppGrade)) {
                    inAppGrade = device.getGrade();
                    if (StringUtils.isBlank(inAppGrade)) {
                        inAppGrade = deviceTestResultDBService.getDeviceGrade(
                                String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId());
                    }
                }

                // Set device grade results to device
                deviceTestResultFromFile.setGradeResults(inAppGrade);

                if (StringUtils.isNotEmpty(inAppGrade)) {
                    if (!StringUtils.equals(device.getGradingAssociateDefects(), GRADING_PERFORMED_KEY) &&
                            !(StringUtils.equals(device.getGrade(), device.getGradingSystemGrade()) &&
                                    StringUtils.equals(device.getGrade(),
                                            deviceTestResultFromFile.getGradeResults()))) {
                        device.setGrade(inAppGrade);
                    }
                }
            }

            setAndSyncTestResults(deviceInTracker, deviceTestResultFromFile);

            // Update SKU Once
            eventPublisher.publishEvent(new DeviceSkuCodeUpdateEvent(this, device));

            // Notify UI
            final String topic = TopicBuilder.build(device, "iwatch", "tested");
            final IWatchTestedMessage message = new IWatchTestedMessage();
            message.setId(device.getId());
            message.setDeviceTestResult(device.getDeviceTestResult());
            message.setVendorName(device.getVendorName());
            message.setWatchSerial(deviceInTracker.getIWatchInfo() != null ?
                    deviceInTracker.getIWatchInfo().getSerial() : null);
            publishToMqttTopic(topic, message);

            // start test result automation
            automationQueueService
                    .enqueueDeviceAutomationRequest(new IWatchTestResultAutomationEvent(this, device));

            // Cloud sync request
            Device syncableDevice = cloudDeviceDataSyncService.getDeviceFromIWatchForSyncing(deviceInTracker);
            String response = cloudDeviceDataSyncService.syncDeviceRecordOnCloud(
                    inMemoryStore.getTransaction(), syncableDevice, getClass());
            LOGGER.info("Synced the test results for iWatch to the cloud db. Response = {}", response);

        } catch (Exception e) {
            LOGGER.error("Error in IWatchHostConnectedListener", e);
        }

    }

    /**
     * Synchronizes and sets the test results for the specified device.
     * This method updates the test results for the given device by fetching and
     * setting results from the provided `DeviceTestResult`. It also ensures that
     * the test completion status is updated and logs information about the retrieval
     * of test results from the device's syslogs. Additionally, it updates battery
     * drain test results and other specific result categories.
     *
     * @param device                   the  device
     * @param deviceTestResultFromFile the test results obtained
     */
    private void setAndSyncTestResults(final IosDevice device, final DeviceTestResult deviceTestResultFromFile) {
        // Testing is completed from mobile application
        deviceTestResultFromFile.getTestResults().setTestingCompleted(true);

        // We won't stop reading test results from syslog even the results are read by observer thread
        if (testResultsService.isTestResultsAlreadyRetrieved(device)) {
            LOGGER.info("Although test results were already retrieved, but found test results in syslog again");
        } else {
            LOGGER.info("New test results were fetched by syslogs from device");
        }

        if (device.getDeviceTestResult() == null) {
            DeviceTestResult deviceTestResult = new DeviceTestResult();
            device.setDeviceTestResult(deviceTestResult);
        }
        device.getDeviceTestResult().setTestResults(deviceTestResultFromFile.getTestResults());

        // Method to update Battery drain test as pass or fail
        updateBatteryDrainPassFailResult(device, deviceTestResultFromFile);

        device.getDeviceTestResult().setBatteryResults(deviceTestResultFromFile.getBatteryResults());
        device.getDeviceTestResult().setMicrophoneResults(deviceTestResultFromFile.getMicrophoneResults());
        device.getDeviceTestResult().setDeviceColor(deviceTestResultFromFile.getDeviceColor());
        device.getDeviceTestResult().setGradeResults(deviceTestResultFromFile.getGradeResults());

        LOGGER.info("IWatch test results that are set in device: {}", device.getDeviceTestResult());

    }

    /**
     * Update Battery drain result as pass or fail
     *
     * @param device            Device
     * @param deviceTestResults Test results from device
     **/
    private void updateBatteryDrainPassFailResult(final Device device, final DeviceTestResult deviceTestResults) {
        LOGGER.info("Updating battery drain as either passed / failed for iWatch");
        final BatteryResults batteryResults = deviceTestResults.getBatteryResults();
        final TestResults testResults = device.getDeviceTestResult().getTestResults();
        if (batteryResults != null && batteryResults.getBatteryDrain() != null) {
            String totalDischarge = batteryResults.getBatteryDrain().getTotalDischarge();
            LOGGER.info("Total battery discharge : {}", totalDischarge);
            int totalDischargePercentage = -1;
            try {
                totalDischargePercentage = Integer.parseInt(totalDischarge.replace("%", ""));
            } catch (NumberFormatException e) {
                LOGGER.error("Error while parsing total discharge percentage", e);
            }
            boolean isBatteryDrainFailed = totalDischargePercentage > inMemoryStore.getAssignedCloudCustomization()
                    .getBatterySettings().getFailDrainIfDischargePercentage();
            if (StringUtils.isNotBlank(totalDischarge) && isBatteryDrainFailed) {
                testResults.getFailed().add(BATTERY_DRAIN);
                testResults.getPassed().remove(BATTERY_DRAIN);
                LOGGER.info("Added battery drain into failed test results object");
            } else if (StringUtils.isNotBlank(totalDischarge)) {
                testResults.getPassed().add(BATTERY_DRAIN);
                testResults.getFailed().remove(BATTERY_DRAIN);
                LOGGER.info("Added battery drain into passed test results object");
            } else {
                LOGGER.info("Total battery discharge is blank. Hence, not added to pass/fail result.");
            }
        }
    }
}
