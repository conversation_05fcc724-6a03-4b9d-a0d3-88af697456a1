package com.phonecheck.backend.listener.transaction;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudTransactionService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.communicator.CommunicatorService;
import com.phonecheck.model.cloudapi.TransactionResponse;
import com.phonecheck.model.event.transaction.CloudDeviceLookupRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.CloudDeviceLookupResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Listener to load cloud device lookup data on request from UI
 */
@Component
public class CloudDeviceLookupRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudDeviceLookupRequestListener.class);

    private final InMemoryStore inMemoryStore;
    private final CloudTransactionService cloudTransactionService;
    private final CommunicatorService communicatorService;

    public CloudDeviceLookupRequestListener(final IMqttAsyncClient mqttClient,
                                            final ObjectMapper objectMapper,
                                            final InMemoryStore inMemoryStore,
                                            final CloudTransactionService cloudTransactionService,
                                            final CommunicatorService communicatorService) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.cloudTransactionService = cloudTransactionService;
        this.communicatorService = communicatorService;
    }

    @EventListener
    @Async
    public void onEvent(final CloudDeviceLookupRequestEvent event) {
        String lookupType = event.getLookupType();
        String lookupValue = event.getLookupValue();
        LOGGER.info("Request to fetch cloud device lookup initiated for " +
                "type {} and value {}", lookupType, lookupValue);
        String licenseId = String.valueOf(inMemoryStore.getLicenseId());
        // Retrieve from cloud
        TransactionResponse transactionResponse =
                cloudTransactionService.getCloudDeviceLookup(licenseId, lookupType, lookupValue);

        String topic = TopicBuilder.buildGenericTopic("device-lookup-detail", "response");
        // Add data to communicator DB
        communicatorService.setData(
                topic,
                transactionResponse
        );

        // Notify UI
        CloudDeviceLookupResponseMessage cloudDeviceLookupResponseMessage =
                new CloudDeviceLookupResponseMessage();
        cloudDeviceLookupResponseMessage.setCurrentTransactionId(inMemoryStore.getTransaction().getTransactionId());
        publishToMqttTopic(topic, cloudDeviceLookupResponseMessage);
    }
}