package com.phonecheck.backend.listener.port;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.port.*;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.portmap.LoadPortMappingRequestMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapDeviceConnectionMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapResponseMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapUpdateRequestMessage;
import com.phonecheck.model.port.PortMapping;
import com.phonecheck.port.PortMappingService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * This class contains listeners for the events related to
 * Port Mapping
 */
@Component
public class PortMappingListeners extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PortMappingListeners.class);
    private final PortMappingService portMappingService;

    public PortMappingListeners(final IMqttAsyncClient mqttClient,
                                final ObjectMapper objectMapper,
                                final PortMappingService portMappingService) {
        super(mqttClient, objectMapper);
        this.portMappingService = portMappingService;
    }

    /**
     * Load saved port mapping request listener.
     *
     * @param event PortMapRequestEvent
     */
    @EventListener
    public void onPortMapRequest(final PortMapRequestEvent event) {
        LOGGER.info("Port mapping info requested");
        PortMapping portMapping = portMappingService.getSavedPortMapping();

        LOGGER.info("Current port mapping : {}", portMapping);
        PortMapResponseMessage portMapResponseMessage = new PortMapResponseMessage();
        portMapResponseMessage.setPortMapping(portMapping);

        String topic = TopicBuilder.buildGenericTopic("port-map", "response");
        publishToMqttTopic(topic, portMapResponseMessage);
    }

    /**
     * Initiates Port mapping
     *
     * @param event PortMapRequestEvent
     */
    @EventListener
    public void onPortMapInitiate(final PortMapInitiateEvent event) {
        LOGGER.info("Port mapping initiating");
        portMappingService.resetPortMapping();
    }

    /**
     * Device connection during port map event listener.
     *
     * @param event PortMapDeviceConnectionEvent
     */
    @EventListener
    public void onPortMapConnection(final PortMapDeviceConnectionEvent event) {
        LOGGER.info("Detected device during port mapping");
        Device device = event.getDevice();
        String identifier;
        if (device instanceof IosDevice && ((IosDevice) device).isRecoveryMode()) {
            identifier = device.getSerial();
        } else {
            identifier = device.getId();
        }
        boolean mappedSuccessfully = portMappingService.mapUsbPortLocationForDeviceId(identifier);

        if (mappedSuccessfully) {
            PortMapDeviceConnectionMessage mappingMessage = new PortMapDeviceConnectionMessage();
            mappingMessage.setPortNumber(portMappingService.getMappedPortNumber());

            String topic = TopicBuilder.buildGenericTopic("port-map", "connection");
            publishToMqttTopic(topic, mappingMessage);
        } else {
            LOGGER.warn("Could not map port for device: {}", identifier);
        }
    }

    /**
     * Request port map update event listener.
     *
     * @param event PortMapUpdateRequestEvent
     */
    @EventListener
    public void onPortMapUpdateRequest(final PortMapUpdateRequestEvent event) {
        PortMapUpdateRequestMessage mappingMessage = new PortMapUpdateRequestMessage();
        String topic = TopicBuilder.buildGenericTopic("port-map", "update", "request");
        publishToMqttTopic(topic, mappingMessage);
    }

    /**
     * Port mapping save request event listener.
     *
     * @param event PortMapSaveEvent
     */
    @EventListener
    public void onPortMapSave(final PortMapSaveEvent event) {
        LOGGER.info("Saving port mapping");
        portMappingService.savePortMapping();
        // Notify UI to load new PortMapping
        if (portMappingService.getDeviceConnectionMode().equals(DeviceConnectionMode.PROCESS)) {
            LoadPortMappingRequestMessage message = new LoadPortMappingRequestMessage();
            String topic = TopicBuilder.buildGenericTopic("port-map", "load", "request");
            publishToMqttTopic(topic, message);
        }
    }

    /**
     * Port mapping remap request event listener.
     *
     * @param event PortMapRemapEvent
     */
    @EventListener
    public void onPortMapRemap(final PortMapRemapEvent event) {
        LOGGER.info("Resetting Port mapping");
        portMappingService.resetPortMapping();
    }

    /**
     * Ongoing Port mapping cancel request event listener.
     *
     * @param event PortMapCancelEvent
     */
    @EventListener
    public void onPortMapCancel(final PortMapCancelEvent event) {
        LOGGER.info("Port mapping cancellation requested");
        portMappingService.finishPortMapping();
    }
}
