package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.Cloud3DeviceDataSyncService;
import com.phonecheck.app.uninstall.DeviceUninstallAppService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.device.results.IosTestResultsService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.constants.ios.IosAppIdConstants;
import com.phonecheck.model.device.*;
import com.phonecheck.model.device.stage.*;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceReconnectRequireEvent;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.event.device.ios.IosManualEraseRequestEvent;
import com.phonecheck.model.event.device.ios.IosParseLogStreamEvent;
import com.phonecheck.model.event.device.ios.IosPushFilesEvent;
import com.phonecheck.model.event.device.iwatch.IWatchAppTestResultEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceColorChangeMessage;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.mqtt.messages.ios.IosSysLogMessage;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import com.phonecheck.model.syslog.ios.IosSysLogKeyType;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.DeviceTestResultStatus;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.RunningModeUtil;
import com.phonecheck.model.util.TestResultsUtil;
import com.phonecheck.parser.device.ios.info.IosOemPartsStatusService;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.phonecheck.model.constants.ios.Ios32BitDeviceModels.IOS_32_BIT_DEVICES;
import static com.phonecheck.model.twowayapi.TwoWayApiConstants.GRADING_PERFORMED_KEY;

/**
 * Listener to start parsing of log stream
 */
@Component
public class IosParseLogStreamListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosParseLogStreamListener.class);
    private static final List<IosSysLogKeyType> SYS_LOG_KEY_TYPE_LIST = List.of(IosSysLogKeyType.BATTERY_HEALTH,
            IosSysLogKeyType.OEM_NOTICE,
            IosSysLogKeyType.ICLOUD,
            IosSysLogKeyType.TOUCH_ID,
            IosSysLogKeyType.MANUAL_FACE_ID);

    private final ApplicationEventPublisher eventPublisher;
    private final InMemoryStore inMemoryStore;

    private final IosTestResultsService testResultsService;
    private final IosDeviceInfoService iosDeviceInfoService;
    private final DeviceTestResultDBService deviceTestResultDBService;
    private final Cloud3DeviceDataSyncService cloud3DeviceDataSyncService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceStageUpdater stageUpdater;
    private final DeviceAutomationQueueService automationQueueService;
    private final IosOemPartsStatusService oemPartsStatusService;
    private final IosPairingUtil iosPairingUtil;
    private final DeviceDataConversionAndExportService deviceDataConversionAndExportService;
    private final LocalizationService localizationService;
    private final VendorCriteriaService vendorCriteriaService;
    private final DeviceUninstallAppService deviceUninstallAppService;
    private final RunningModeUtil runningModeUtil;

    public IosParseLogStreamListener(final ApplicationEventPublisher eventPublisher, final IMqttAsyncClient mqttClient,
                                     final ObjectMapper objectMapper,
                                     final InMemoryStore inMemoryStore,
                                     final IosTestResultsService testResultsService,
                                     final IosDeviceInfoService iosDeviceInfoService,
                                     final DeviceTestResultDBService deviceTestResultDBService,
                                     final Cloud3DeviceDataSyncService cloud3DeviceDataSyncService,
                                     final DeviceConnectionTracker deviceConnectionTracker,
                                     final DeviceStageUpdater stageUpdater,
                                     final DeviceAutomationQueueService automationQueueService,
                                     final IosOemPartsStatusService oemPartsStatusService,
                                     final IosPairingUtil iosPairingUtil,
                                     final DeviceDataConversionAndExportService deviceDataConversionAndExportService,
                                     final LocalizationService localizationService,
                                     final VendorCriteriaService vendorCriteriaService,
                                     final DeviceUninstallAppService deviceUninstallAppService,
                                     final RunningModeUtil runningModeUtil) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.inMemoryStore = inMemoryStore;
        this.testResultsService = testResultsService;
        this.iosDeviceInfoService = iosDeviceInfoService;
        this.deviceTestResultDBService = deviceTestResultDBService;
        this.cloud3DeviceDataSyncService = cloud3DeviceDataSyncService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.stageUpdater = stageUpdater;
        this.automationQueueService = automationQueueService;
        this.oemPartsStatusService = oemPartsStatusService;
        this.iosPairingUtil = iosPairingUtil;
        this.deviceDataConversionAndExportService = deviceDataConversionAndExportService;
        this.localizationService = localizationService;
        this.vendorCriteriaService = vendorCriteriaService;
        this.deviceUninstallAppService = deviceUninstallAppService;
        this.runningModeUtil = runningModeUtil;
    }

    @Async
    @EventListener
    public void onEvent(final IosParseLogStreamEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        final IosSysLogKey iosSysLogKey = event.getIosSysLogKey();
        final String sysLogResponse = event.getSysLogResponse();
        setDeviceIdMDC(device.getId());

        // ignore excessive logs for syslog keys
        if (!IosSysLogKeyType.WIFI.equals(iosSysLogKey.getType()) &&
                (!RunningMode.IWATCH_MODE.equals(runningModeUtil.getRunningModeFromFile()) ||
                        !IosSysLogKey.IWATCH_ERASE_KEY_4.equals(iosSysLogKey))) {
            LOGGER.info("Key received in SysLogs: {}", iosSysLogKey);
        }

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Trying to parse log stream but device has been disconnected, stopping processing.");
            return;
        }
        if (IosSysLogKey.START_READING_TEST_RESULTS.getKey().equals(iosSysLogKey.getKey())) {
            PairStatus pairStatus = iosPairingUtil.checkAndNotifyUiIfNotPaired(deviceInTracker);
            if (!PairStatus.PAIRED.equals(pairStatus)) {
                LOGGER.warn("Device is not paired. Cannot continue processing. PairStatus: {}", pairStatus);
                deviceInTracker.setReconnectRequired(true);
                eventPublisher.publishEvent(new DeviceReconnectRequireEvent(this, device,
                        ErrorConstants.PAIRING_FAILED));
                return;
            }
            if (inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST) {
                LOGGER.info("Setting and syncing test results from the syslog parser for iWatch");
                eventPublisher.publishEvent(new IWatchAppTestResultEvent(this, deviceInTracker));
            } else {
                LOGGER.info("Setting and syncing test results from the syslog parser");
                setAndSyncTestResults(deviceInTracker);
            }
        } else if (iosSysLogKey.getType() == IosSysLogKeyType.FACTORY_RESET) {
            LOGGER.info("Raising IosEraseRequest event based on Factory Reset request sent from the Test app");
            eventPublisher.publishEvent(new IosManualEraseRequestEvent(this, device));
        } else if (iosSysLogKey.getType() == IosSysLogKeyType.FETCH_CURRENT_BATTERY) {
            LOGGER.info("Raise IosPushFilesEvent when FETCH_CURRENT_BATTERY request sent from the Test app");
            eventPublisher.publishEvent(new IosPushFilesEvent(this, device));
        } else {
            DeviceLock deviceLock = deviceInTracker.getDeviceLock();
            boolean isSysLogParsed =
                    iosDeviceInfoService.parseSysLogStream(deviceInTracker, iosSysLogKey, sysLogResponse);
            // Send mqtt message to UI when BATTERY_HEALTH, OEM, ICLOUD and Touch id
            // syslog parsers are successfully parsed
            if (isSysLogParsed && SYS_LOG_KEY_TYPE_LIST.contains(iosSysLogKey.getType()) &&
                    DeviceConnectionMode.IWATCH_HOST != inMemoryStore.getDeviceConnectionMode()) {
                final String topic = TopicBuilder.build(device, "parsed", "syslog");
                final IosSysLogMessage message = new IosSysLogMessage();
                message.setId(device.getId());
                // Currently setting BATTERY_HEALTH, OEM, ICLOUD and Touch id SysLogKey in mqtt message
                message.setIosSysLogKey(iosSysLogKey);
                if (iosSysLogKey.getType() == IosSysLogKeyType.BATTERY_HEALTH) {
                    if (iosSysLogKey.equals(IosSysLogKey.BATTERY_HEALTH_SETTINGS_1) ||
                            iosSysLogKey.equals(IosSysLogKey.BATTERY_HEALTH_SETTINGS_2)) {
                        LOGGER.info("User navigated to Battery health screen");

                        deviceInTracker.setBatteryScreenOpened(true);
                    } else {
                        LOGGER.info("Battery health info received from syslog");

                        message.setBatteryStateHealth(deviceInTracker.getBatteryStateHealth() != 0
                                ? deviceInTracker.getBatteryStateHealth()
                                : deviceInTracker.getPreviousBatteryStateHealth());
                        message.setBatteryInfo(deviceInTracker.getBatteryInfo());
                        message.setBatteryDegraded(deviceInTracker.getBatteryDegraded());

                        // Update battery info in database, We are changing the battery source of device from parser and
                        // it should be updated in database
                        final BatteryInfoSuccessStage stage = BatteryInfoSuccessStage
                                .builder()
                                .id(deviceInTracker.getId())
                                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                                .batteryDegraded(deviceInTracker.getBatteryDegraded())
                                .batteryStateHealth(deviceInTracker.getBatteryStateHealth() != 0
                                        ? deviceInTracker.getBatteryStateHealth()
                                        : deviceInTracker.getPreviousBatteryStateHealth())
                                .batteryPercentage(deviceInTracker.getBatteryPercentage())
                                .batteryInfo(deviceInTracker.getBatteryInfo())
                                .timestamp(System.currentTimeMillis())
                                .build();
                        stageUpdater.updateStage(stage);
                    }
                } else if (iosSysLogKey.getType() == IosSysLogKeyType.ICLOUD) {
                    LOGGER.info("Icloud lock info received from syslog");
                    message.setDeviceLock(deviceInTracker.getDeviceLock());
                    // Update icloud status in database only if icloud is ON because we are setting ICloud status
                    // from activation success listener too, and we should not change
                    // the icloud status if it comes as OFF
                    if (deviceLock != deviceInTracker.getDeviceLock() &&
                            deviceInTracker.getDeviceLock().equals(DeviceLock.ON)) {
                        final IcloudInfoSuccessStage icloudInfoSuccessStage = IcloudInfoSuccessStage.builder()
                                .id(deviceInTracker.getId())
                                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                                .iCloudStatus(deviceInTracker.getDeviceLock().getKey())
                                .timestamp(System.currentTimeMillis())
                                .build();
                        stageUpdater.updateStage(icloudInfoSuccessStage);
                    }
                } else if (iosSysLogKey.getType() == IosSysLogKeyType.TOUCH_ID) {
                    LOGGER.info("TouchId sensor info received from syslog");
                    message.setTouchIdSensor(deviceInTracker.getTouchIdSensor());
                } else if (iosSysLogKey.getType() == IosSysLogKeyType.OEM_NOTICE) {
                    LOGGER.info("OEM notice info received from syslog");

                    // Recalculate all OEM parts statuses, overall status and oemPartsJson string
                    oemPartsStatusService.reCalculateOemStatusUsingNotices(deviceInTracker,
                            inMemoryStore.getLocalCustomizations().isIgnoreNaForOem());

                    message.setOemBatteryNotice(deviceInTracker.getOemBatteryNotice());
                    message.setOemBatteryServiceNotice(deviceInTracker.getOemBatteryServiceNotice());
                    message.setOemFrontCameraNotice(deviceInTracker.getOemFrontCameraNotice());
                    message.setOemBackCameraNotice(deviceInTracker.getOemBackCameraNotice());
                    message.setOemDisplayNotice(deviceInTracker.getOemDisplayNotice());

                    message.setOemSerialStatus(deviceInTracker.getOemSerialStatus());
                    message.setOemMainBoardStatus(deviceInTracker.getOemMainBoardStatus());
                    message.setOemBatteryStatus(deviceInTracker.getOemBatteryStatus());
                    message.setOemFrontCameraStatus(deviceInTracker.getOemFrontCameraStatus());
                    message.setOemBackCameraStatus(deviceInTracker.getOemBackCameraStatus());
                    message.setOemLcdStatus(deviceInTracker.getOemLcdStatus());
                    message.setOverallOemStatus(deviceInTracker.getOverallOemStatus());

                    // update oem data in db
                    updateOemInfoInDb(deviceInTracker);
                } else if (iosSysLogKey.getType() == IosSysLogKeyType.MANUAL_FACE_ID) {
                    message.setFaceIdSensor(WorkingStatus.PENDING);
                }
                // Notify UI
                publishToMqttTopic(topic, message);
            }
        }
    }

    /**
     * Get iphone device grade from local DB
     * If grade is not present in local DB TBLDeviceInfo, then fetch grade from cloud api
     * Set grade fetched from cloud in local DB
     *
     * @param device Iphone device
     */
    private void setAndSyncTestResults(final IosDevice device) {
        try {
            LOGGER.info("Test results ready on the IOS device.");
            DeviceTestResultStatus deviceTestResultFromFileStatus;
            if (IOS_32_BIT_DEVICES.contains(device.getProductType())) {
                deviceTestResultFromFileStatus = testResultsService.getTestResults(device,
                        IosAppIdConstants.APP_STORE_BUNDLE_ID_2);
            } else {
                deviceTestResultFromFileStatus = testResultsService.getTestResults(device,
                        inMemoryStore.getIosAppBundleIdentifier());
            }

            DeviceTestResult deviceTestResultFromFile = deviceTestResultFromFileStatus.getDeviceTestResult();
            if (deviceTestResultFromFile != null && deviceTestResultFromFile.getTestResults() != null) {
                // Testing is completed from mobile application
                deviceTestResultFromFile.getTestResults().setTestingCompleted(true);

                // We won't stop reading test results from syslog even the results are read by observer thread
                if (testResultsService.isTestResultsAlreadyRetrieved(device)) {
                    LOGGER.info("Although test results were already retrieved, but found test results in syslog again");
                } else {
                    LOGGER.info("New test results were fetched by syslogs from device");
                }

                // Get in-app grade
                String inAppGrade = deviceTestResultFromFile.getGradeResults();
                // if in-app grade is not present then set it to device grade, if that is also blank then check local db
                if (StringUtils.isBlank(inAppGrade)) {
                    inAppGrade = device.getGrade();
                    if (StringUtils.isBlank(inAppGrade)) {
                        inAppGrade = deviceTestResultDBService.getDeviceGrade(
                                String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId());
                    }
                }

                // Set device grade results to device
                deviceTestResultFromFile.setGradeResults(inAppGrade);

                // Updates failed and passed test results in the DeviceTestResult.
                testResultsService.updateFailedTestResults(device, deviceTestResultFromFile);
                testResultsService.updatePassedTestResults(device, deviceTestResultFromFile);

                if (device.getDeviceTestResult() == null) {
                    DeviceTestResult deviceTestResult = new DeviceTestResult();
                    device.setDeviceTestResult(deviceTestResult);
                }
                device.getDeviceTestResult().setTestResults(deviceTestResultFromFile.getTestResults());

                device.getDeviceTestResult().setGradeResults(deviceTestResultFromFile.getGradeResults());

                testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(
                        device, deviceTestResultFromFile.getBatteryResults());

                device.getDeviceTestResult().setMicrophoneResults(deviceTestResultFromFile.getMicrophoneResults());
                device.getDeviceTestResult().setDeviceColor(deviceTestResultFromFile.getDeviceColor());
                if (deviceTestResultFromFile.getCosmeticResults() != null
                        && (deviceTestResultFromFile.getCosmeticResults().getFailed() != null
                        || deviceTestResultFromFile.getCosmeticResults().getPassed() != null
                        || deviceTestResultFromFile.getCosmeticResults().getPending() != null)) {
                    device.getDeviceTestResult().setCosmeticResults(deviceTestResultFromFile.getCosmeticResults());
                } else {
                    DeviceTestResult deviceTestResult = deviceTestResultDBService.getDeviceTestResults(
                            String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId(),
                            String.valueOf(inMemoryStore.getLicenseId()), device.getSerial());
                    device.getDeviceTestResult().setCosmeticResults(
                            deviceTestResult != null ? deviceTestResult.getCosmeticResults() : null);
                }

                LOGGER.info("Test results that are set in device: {}", device.getDeviceTestResult());

                if (StringUtils.isNotEmpty(inAppGrade)) {
                    if (!StringUtils.equals(device.getGradingAssociateDefects(), GRADING_PERFORMED_KEY) &&
                            !(StringUtils.equals(device.getGrade(), device.getGradingSystemGrade()) &&
                                    StringUtils.equals(device.getGrade(),
                                            deviceTestResultFromFile.getGradeResults()))) {
                        device.setGrade(inAppGrade);
                    }
                }

                String color = deviceTestResultFromFile.getDeviceColor();
                if (!StringUtils.equalsIgnoreCase(device.getColor(), color) && StringUtils.isNotBlank(color)) {
                    device.setColor(color);
                    final String topic = TopicBuilder.build(device, "color", "change", "response");
                    final DeviceColorChangeMessage requestMessage = new DeviceColorChangeMessage();
                    requestMessage.setId(device.getId());
                    requestMessage.setColor(color);
                    publishToMqttTopic(topic, requestMessage);
                }

                // Add initial defects including touch id and face id statuses in AppTestResult
                // so that it syncs to cloud and database with other test results.
                TestResultsUtil.prependInitialDefectsToAppResults(device,
                        inMemoryStore.getAssignedCloudCustomization(), localizationService);
                vendorCriteriaService.calculateVendorCriteria(device);
                AppTestingDoneStage stage = AppTestingDoneStage.builder()
                        .id(device.getId())
                        .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                        .licenseId(String.valueOf(inMemoryStore.getLicenseId()))
                        .serial(device.getSerial())
                        .deviceType(device.getDeviceType())
                        .deviceTestResult(device.getDeviceTestResult())
                        .timestamp(System.currentTimeMillis())
                        .ebayRefurbished(device.getEbayRefurbished())
                        .ebayRejection(device.getEbayRejection())
                        .swappaQualified(device.getSwappaQualified())
                        .swappaRejection(device.getSwappaRejection())
                        .amazonRenewed(device.getAmazonRenewed())
                        .amazonRenewedRejection(device.getAmazonRenewedRejection())
                        .backMarketQualified(device.getBackMarketQualified())
                        .backMarketRejection(device.getBackMarketRejection())
//                        .ebayGradeId(device.getEbayGradeId())
//                        .SwappaGradeId(device.getSwappaGradeId())
//                        .AmazonGradeId(device.getAmazonGradeId())
//                        .BackMarketGradeId(device.getBackmarketGradeId())
                        .build();
                stageUpdater.update(stage);

                device.setStage(DeviceStage.APP_TESTING_DONE);
                LOGGER.info("EbayR - {} , SWappaR - {}, BackmarketR - {}, AmazonR - {} , device stage {} ",
                        device.getEbayRejection(), device.getSwappaRejection(), device.getBackMarketRejection(),
                        device.getAmazonRenewedRejection(), device.getStage());
                if (inMemoryStore.getAssignedCloudCustomization().getWifiSettings().isDisconnect()) {
                    // Remove Wifi Profile
                    String ssid = inMemoryStore.getAssignedCloudCustomization().getWifiSettings() != null ?
                            inMemoryStore.getAssignedCloudCustomization().getWifiSettings().getName() : null;
                    deviceUninstallAppService.removeWifiProfile(device, ssid);
                }

                // Update SKU Once
                eventPublisher.publishEvent(new DeviceSkuCodeUpdateEvent(this, device));

                // Notify UI
                final String topic = TopicBuilder.build(device, "tested");
                final DeviceTestedMessage message = new DeviceTestedMessage();
                message.setId(device.getId());
                message.setDeviceTestResult(device.getDeviceTestResult());
                message.setVendorName(device.getVendorName());
                publishToMqttTopic(topic, message);

                // Added 500 ms sleep for test results to fully sync with UI module
                Thread.sleep(500);

                // Perform Results/Label/Source api operations
                testResultsService.performLabelSourceApiOperations(device);

                deviceDataConversionAndExportService.triggerAutoExportOnTestResults(device);

                // start test result automation
                automationQueueService
                        .enqueueDeviceAutomationRequest(new DeviceTestResultAutomationEvent(this, device));

                // Sync device Data to the cloud
                final SyncableDevice syncableDevice = SyncableDevice.builder()
                        .transaction(inMemoryStore.getTransaction())
                        .device(device)
                        .source(getClass())
                        .build();
                cloud3DeviceDataSyncService.enqueueDeviceDataSyncRequest(syncableDevice);
            } else {
                LOGGER.error("Failed to fetch test results from the file");

                if (deviceTestResultFromFileStatus.getNotificationStatus() != null &&
                        List.of(NotificationStatus.FAILED_NO_DEVICE, NotificationStatus.LOCKDOWN_FAILED)
                                .contains(deviceTestResultFromFileStatus.getNotificationStatus())) {
                    LOGGER.error("Notification post status: {}",
                            deviceTestResultFromFileStatus.getNotificationStatus());

                    device.setReconnectRequired(true);
                    eventPublisher.publishEvent(
                            new DeviceReconnectRequireEvent(this,
                                    device,
                                    ErrorConstants.SYNC_FAILED)
                    );
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while setting test results", e);
        }
    }

    /**
     * updates Oem info in database
     *
     * @param device target device
     */
    private void updateOemInfoInDb(final IosDevice device) {
        // Update database
        final OemDataCollectionSuccessStage stage = OemDataCollectionSuccessStage
                .builder()
                .id(device.getId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .overallOemStatus(device.getOverallOemStatus())
                .oemPartsJson(device.getOemPartsJson())
                .timestamp(System.currentTimeMillis())
                .build();
        stageUpdater.updateStage(stage);
    }
}
