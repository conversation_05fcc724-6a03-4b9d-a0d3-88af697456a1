package com.phonecheck.backend.listener.device.ios.precheck;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.info.ios.PreCheckTestResultsService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.PreCheckInfo;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.precheck.PreCheckInfoResultsRequestEvent;
import com.phonecheck.model.event.device.ios.precheck.PreCheckSemiResultManualFailedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PreCheckSemiResultsManualFailedListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreCheckSemiResultsManualFailedListener.class);
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final ApplicationEventPublisher eventPublisher;
    private final PreCheckTestResultsService preCheckTestResultsService;

    public PreCheckSemiResultsManualFailedListener(final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                                   final DeviceConnectionTracker deviceConnectionTracker,
                                                   final ApplicationEventPublisher eventPublisher,
                                                   final PreCheckTestResultsService preCheckTestResultsService) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
        this.preCheckTestResultsService = preCheckTestResultsService;
    }

    @Async
    @EventListener
    public void onEvent(final PreCheckSemiResultManualFailedEvent event) {
        final IosDevice eventDevice = (IosDevice) event.getDevice();
        final PreCheckInfo.SemiAutoResults updatedSemiResults = event.getSemiResults();
        setDeviceIdMDC(eventDevice.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(eventDevice.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Pre check to be performed but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("PreCheck semi results manually changed request: {}", updatedSemiResults);

        final PreCheckInfo trackerPreCheckInfo = deviceInTracker.getPreCheckInfo();

        if (trackerPreCheckInfo == null) {
            LOGGER.info("Found pre-check info null in tracker device");
            return;
        }

        try {
            trackerPreCheckInfo.setSemiAutoResults(updatedSemiResults);
            LOGGER.info("PreCheck semi results override in preCheck info: {}", trackerPreCheckInfo);

            preCheckTestResultsService.mapSemiResultsToDeviceTestResults(deviceInTracker);
            // If all semi tests are performed manually,
            // stop the pre-check exe and start iDevice-info testing
            PreCheckInfo.SemiAutoResults trackerSemiResults = trackerPreCheckInfo.getSemiAutoResults();
                List<String> pendingTest = preCheckTestResultsService.getSemiPendingTests(trackerSemiResults);
                if (pendingTest.isEmpty()) {
                    // Tested : Notify UI
                    final String testedTopic = TopicBuilder.build(deviceInTracker, "tested");
                    final DeviceTestedMessage message = new DeviceTestedMessage();
                    message.setId(deviceInTracker.getId());
                    message.setDeviceTestResult(deviceInTracker.getDeviceTestResult());
                    message.setVendorName(deviceInTracker.getVendorName());
                    publishToMqttTopic(testedTopic, message);

                    LOGGER.info("All semi test already performed, going to stop semi auto test process");
                    // Asynchronous getting device info tests using ideviceinfo exe
                    eventPublisher.publishEvent(new PreCheckInfoResultsRequestEvent(this, deviceInTracker));
                }

        } catch (Exception e) {
            LOGGER.error("Failed to update semi test result in tracker {}", updatedSemiResults, e);
        }


    }
}