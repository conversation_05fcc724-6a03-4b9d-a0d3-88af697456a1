package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidSimLockChangeEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class AndroidSimLockChangeListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidSimLockChangeListener.class);
    private static final String NETWORK_LOCK = "LK";
    private static final String NETWORK_UNLOCK = "UNLK";


    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceInfoDBService deviceInfoDBService;
    private final InMemoryStore inMemoryStore;

    public AndroidSimLockChangeListener(final ObjectMapper objectMapper,
                                        final IMqttAsyncClient mqttClient,
                                        final DeviceConnectionTracker deviceConnectionTracker,
                                        final DeviceInfoDBService deviceInfoDBService,
                                        final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.deviceInfoDBService = deviceInfoDBService;
        this.inMemoryStore = inMemoryStore;
    }

    @Async
    @EventListener
    public void onEvent(final AndroidSimLockChangeEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Sim lock change requested but device has been disconnected," +
                    " stopping processing.");
            return;
        }

        LOGGER.info("Android Sim lock change event received");

        boolean simLock = event.getDevice().getSimLock();
        deviceInTracker.setUnlockStatus(simLock ? NETWORK_LOCK : NETWORK_UNLOCK);
        deviceInTracker.setSimLock(simLock);
        // Update TBLDeviceInfo with updated sim lock
        deviceInfoDBService.updateDeviceSimLockAndUnlockStatus(device.getId(),
                simLock ? "Locked" : "Unlocked",
                deviceInTracker.getUnlockStatus(),
                String.valueOf(inMemoryStore.getTransaction().getTransactionId()));
    }
}