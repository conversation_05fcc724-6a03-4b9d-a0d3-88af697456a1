package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.api.cloud.TwoWayApiCallService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.TwoWayApiServiceV2;
import com.phonecheck.backend.util.autoexport.DeviceDataConversionUtil;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceLpnUpdateRequestEvent;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.event.device.SourceApiCallRequestEventV2;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceReprocessRequestMessage;
import com.phonecheck.model.mqtt.messages.TwoWayApiErrorDecisionMessage;
import com.phonecheck.model.mqtt.messages.iwatch.IWatchApiStatusMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.LocalizationService;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import static com.phonecheck.model.twowayapi.TwoWayApiConstants.*;

/**
 * Listener for Source API call
 * */
@Component
public class SourceApiCallListenerV2 extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(SourceApiCallListenerV2.class);

    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;

    private final DeviceInfoDBService deviceInfoDBService;

    private final ObjectMapper objectMapper;

    private final DeviceDataConversionAndExportService deviceDataConversionAndExportService;

    private final LocalizationService localizationService;

    private final TwoWayApiCallService twoWayApiCallService;

    private final ApplicationEventPublisher eventPublisher;

    private final TwoWayApiServiceV2 twoWayApiServiceV2;

    private final CloudDeviceDataSyncService cloudDeviceDataSyncService;


    public SourceApiCallListenerV2(final IMqttAsyncClient mqttClient,
                                   final ObjectMapper objectMapper,
                                   final InMemoryStore inMemoryStore,
                                   final DeviceConnectionTracker deviceConnectionTracker,
                                   final DeviceInfoDBService deviceInfoDBService,
                                   final DeviceDataConversionAndExportService deviceDataConversionAndExportService,
                                   final LocalizationService localizationService,
                                   final TwoWayApiCallService twoWayApiCallService,
                                   final ApplicationEventPublisher eventPublisher,
                                   final TwoWayApiServiceV2 twoWayApiServiceV2,
                                   final CloudDeviceDataSyncService cloudDeviceDataSyncService) {
        super(mqttClient, objectMapper);
        this.objectMapper = objectMapper;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.deviceInfoDBService = deviceInfoDBService;
        this.deviceDataConversionAndExportService = deviceDataConversionAndExportService;
        this.localizationService = localizationService;
        this.twoWayApiCallService = twoWayApiCallService;
        this.eventPublisher = eventPublisher;
        this.twoWayApiServiceV2 = twoWayApiServiceV2;
        this.cloudDeviceDataSyncService = cloudDeviceDataSyncService;
    }

    @EventListener
    public void onEvent(final SourceApiCallRequestEventV2 event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Source API need to be called but device has been disconnected, stopping processing.");
            return;
        }

        makeCallToSourceApi(device);
    }

    private void makeCallToSourceApi(final Device device) {
        try {
            LOGGER.info("Calling source API for the device");

            device.setStage(DeviceStage.SOURCE_API_CALLING);

            String deviceInfoJsonString = null;
            if (inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST) {
                notifyUiInCaseOfIWatch(device);
                final Device iwatchDevice = cloudDeviceDataSyncService.
                        getDeviceFromIWatchForSyncing((IosDevice) device);

                if (iwatchDevice == null) {
                    LOGGER.info("Cannot make source API call for the iWatch as it is null");
                    return;
                }
                deviceInfoJsonString = DeviceDataConversionUtil
                        .convertToJson(deviceDataConversionAndExportService.mapDeviceObject(iwatchDevice));

                ((IosDevice) device).getIWatchInfo().setSourceApiCalled(true);
            } else {
                notifyStatusLabelToUi(device, DeviceStage.SOURCE_API_CALLING);
                deviceInfoJsonString = DeviceDataConversionUtil
                        .convertToJson(deviceDataConversionAndExportService.mapDeviceObject(device));
            }

            final ObjectNode deviceJsonObject = (ObjectNode) objectMapper.readTree(deviceInfoJsonString);

            final String apiKeyValue = StringUtils.isBlank(inMemoryStore.getAssignedCloudCustomization().
                    getAdvancedSettings().getSourceAPIKey()) ? DEFAULT_API_KEY : inMemoryStore.
                    getAssignedCloudCustomization().getAdvancedSettings().getSourceAPIKey();

            deviceJsonObject.put(API_KEY, apiKeyValue.trim());

            if (deviceJsonObject.has(ESN_RESPONSE)) {
                deviceJsonObject.remove(ESN_RESPONSE);
            }

            if (deviceJsonObject.has(ICLOUD_INFO)) {
                deviceJsonObject.put(API_RESPONSE, deviceJsonObject.get(ICLOUD_INFO).asText());
                deviceJsonObject.remove(ICLOUD_INFO);
            }

            if (deviceJsonObject.has(ERROR_CODE)) {
                deviceJsonObject.put(PACKAGE_NAME, deviceJsonObject.get(ERROR_CODE).asText());
                deviceJsonObject.remove(ERROR_CODE);
            }
            deviceJsonObject.put(DEVICE_UPDATED_DATE, getCurrentTimeToSqlDateTime());
            deviceJsonObject.put(STATION_ID, inMemoryStore.getUserName());

            if (DeviceFamily.IOS == device.getDeviceFamily()) {
                deviceJsonObject.put(TESTER_DEVICE_TIME, ((IosDevice) device).getScreenTime());
            }

            final ObjectNode sourceApiRequestObject = objectMapper.createObjectNode();
            sourceApiRequestObject.put(SOURCE_REQUEST, deviceJsonObject);

            LOGGER.info("Source API Request:{}", objectMapper.writeValueAsString(sourceApiRequestObject));

            ObjectNode sourceApiResponseObject = twoWayApiCallService.getSourceApiIntegrationResponse(
                    sourceApiRequestObject);

            LOGGER.info("Source API Response:{}", sourceApiResponseObject);

            device.setSourceApiCalled(true);

            if (sourceApiResponseObject != null) {
                final int httpStatusCode = sourceApiResponseObject.path(HTTP_STATUS_CODE_KEY).asInt(-1);
                LOGGER.info("Http status code post source api call is :{}", httpStatusCode);
                if (httpStatusCode == 200) {
                    processResponse(device, sourceApiResponseObject);
                } else {
                    notifyUIForApiErrors(device, sourceApiRequestObject, sourceApiResponseObject);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while calling source api ", e);
        }
    }
    private void notifyUIForApiErrors(final Device device,
                                      final ObjectNode sourceApiRequestObject,
                                      final ObjectNode sourceApiResponseObject) {
        final TwoWayApiErrorDecisionMessage message = new TwoWayApiErrorDecisionMessage();

        String topic =  inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST ?
                TopicBuilder.buildGenericTopic("iwatch-bstar-decision", "request") : TopicBuilder
                .buildGenericTopic("bstar-decision", "request");

      //  final String topic = TopicBuilder.buildGenericTopic("bstar-decision", "request");
        message.setId(device.getId());
        message.setRequestObject(sourceApiRequestObject);
        message.setResponseObject(sourceApiResponseObject);
        message.setErrorTitle(localizationService.getLanguageSpecificText("sourceApiError"));
        message.setFromRetry(false);
        publishToMqttTopic(topic, message);
    }


    private void processResponse(final Device device, final ObjectNode sourceApiResponse) {
        final ObjectNode flattenedApiResponse = twoWayApiCallService.flattenObjectNode(sourceApiResponse);
        device.setSourceApiResponse1(sourceApiResponse.toString());

        boolean isDeviceIWatch = inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST;

        if (!isDeviceIWatch) {
            if (flattenedApiResponse.has(SOURCE_SKU)) {
                final String updatedSku = flattenedApiResponse.path(SOURCE_SKU).asText();
                if (StringUtils.isNotBlank(updatedSku) && !StringUtils.equalsIgnoreCase(updatedSku, NULL_KEY)) {
                    LOGGER.info("SKU was updated. Raising event now");
                    eventPublisher.publishEvent(new DeviceSkuCodeUpdateEvent(this, device));
                }
            }

            if (flattenedApiResponse.has(SOURCE_LPN)) {
                final String updatedLpn = flattenedApiResponse.path(SOURCE_LPN).asText();
                if (StringUtils.isNotBlank(updatedLpn) && !StringUtils.equalsIgnoreCase(updatedLpn, NULL_KEY)) {
                    LOGGER.info("Source LPN was updated. Raising event now");
                    eventPublisher.publishEvent(new DeviceLpnUpdateRequestEvent(this, device));
                }
            }
        }

        if (flattenedApiResponse.has(NEXT_ACTION)) {
            final String nextAction = flattenedApiResponse.path(NEXT_ACTION).asText(NA);
            proceedAsNextAction(nextAction, device, flattenedApiResponse, isDeviceIWatch);
        } else {
            LOGGER.info("NO next action specified in Source API response");
        }
    }

    private void proceedAsNextAction(final String nextAction, final Device device, final ObjectNode apiResponse,
                                      final boolean isDeviceIWatch) {
        LOGGER.info("Processing next action {} for the device", nextAction);
        switch (nextAction) {
            case CONTINUE_TEST:
                twoWayApiServiceV2.handleContinueTestAction(device, apiResponse, isDeviceIWatch);
                break;
            case RE_PROCESS:
                final String topic = isDeviceIWatch ? TopicBuilder.build(device, "iwatch", "reprocess",
                  "popup", "request") : TopicBuilder.build(device, "device", "reprocess", "popup", "request");

                final DeviceReprocessRequestMessage message = new DeviceReprocessRequestMessage();
                message.setId(device.getId());
                publishToMqttTopic(topic, message);
                break;
            case PRINT_EXCEPTION_LABEL:
                twoWayApiServiceV2.handlePrintExceptionLabelAction(device, isDeviceIWatch);
                break;
            case CALL_RESULT:
                boolean isEraseRequired = false;
                if (apiResponse.has(STATUS_MESSAGE)) {
                    String statusMessage = apiResponse.get(STATUS_MESSAGE).textValue();
                    isEraseRequired =  (StringUtils.isNotBlank(statusMessage) &&
                            StringUtils.containsIgnoreCase(statusMessage, ERASE_RESET));
                }
                LOGGER.info("In Call Result action is Erase Required? {}", isEraseRequired);
                twoWayApiServiceV2.handleCallResultsAction(device, isEraseRequired, isDeviceIWatch);
                break;
            default:
                LOGGER.info("Nothing to execute for this action");
        }
    }

    private void notifyUiInCaseOfIWatch(final Device device) {
        LOGGER.info("Notifying UI for Source API call");
        final String apiTopic = TopicBuilder.build(device, "api-call-status", "request");
        IWatchApiStatusMessage apiMessage = new IWatchApiStatusMessage();
        apiMessage.setDeviceStage(DeviceStage.SOURCE_API_CALLING);
        apiMessage.setId(device.getId());
        publishToMqttTopic(apiTopic, apiMessage);
    }
}

