package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceImeiSerialValidationUpdateRequestEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class DeviceImeiSerialValidationUpdateListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceImeiSerialValidationUpdateListener.class);
    private final DeviceInfoDBService deviceInfoDBService;
    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;

    public DeviceImeiSerialValidationUpdateListener(final ObjectMapper objectMapper,
                                                    final IMqttAsyncClient mqttClient,
                                                    final DeviceInfoDBService deviceInfoDBService,
                                                    final InMemoryStore inMemoryStore,
                                                    final DeviceConnectionTracker deviceConnectionTracker) {
        super(mqttClient, objectMapper);
        this.deviceInfoDBService = deviceInfoDBService;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
    }

    /**
     * Event from UI to set device imei validation to DB and in deviceConnectionTracker
     *
     * @param event DeviceImeiSerialValidationUpdateRequestEvent from UI
     */
    @Async
    @EventListener
    public void onEvent(final DeviceImeiSerialValidationUpdateRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Imei validation request initiated," +
                    " but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Set device Imei/Serial validation {}", device.getIsImeiValidate());

        deviceInTracker.setIsImeiValidate(device.getIsImeiValidate());

        deviceInfoDBService.updateImeiSerialValidation(device.getId(),
                String.valueOf(inMemoryStore.getTransaction().getTransactionId()),
                device.getIsImeiValidate());

    }
}
