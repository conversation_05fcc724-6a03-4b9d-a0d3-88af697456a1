package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.device.connection.android.AndroidDeviceConnectionService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.stage.InitialConnectionStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidConnectedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidConnectedMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.model.store.InMemoryStore;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class AndroidConnectedListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidConnectedListener.class);
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceStageUpdater stageUpdater;
    private final InMemoryStore inMemoryStore;
    private final AndroidDeviceConnectionService androidDeviceConnectionService;

    public AndroidConnectedListener(final ObjectMapper objectMapper,
                                    final IMqttAsyncClient mqttClient,
                                    final DeviceConnectionTracker deviceConnectionTracker,
                                    final DeviceStageUpdater stageUpdater,
                                    final InMemoryStore inMemoryStore,
                                    final AndroidDeviceConnectionService androidDeviceConnectionService) {

        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.androidDeviceConnectionService = androidDeviceConnectionService;
        this.stageUpdater = stageUpdater;
        this.inMemoryStore = inMemoryStore;
    }

    @Async
    @EventListener
    public void onEvent(final AndroidConnectedEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());
        device.setDeviceCreatedDate(getCurrentTimeToSqlDateTime());
        LOGGER.info("New android device connected in {} mode and {} auth status", device.getAndroidConnectionMode(),
                device.getAuthorizationStatus());

        // if device connected in AT mode, check one last time if it moved to ADB mode
        if (device.getAndroidConnectionMode().equals(AndroidConnectionMode.AT)) {
            updateDeviceIfInADBMode(device);
        }

        // Get authorization status for device
        AuthorizationStatus authorizationStatus = device.getAuthorizationStatus();

        // Notify ui device is connected
        final String topic = TopicBuilder.build(device, "connected");
        AndroidConnectedMessage message = new AndroidConnectedMessage();
        message.setId(device.getId());
        message.setAuthorizationStatus(authorizationStatus);
        message.setModemSerial(device.getPortName());
        message.setAndroidConnectionMode(device.getAndroidConnectionMode());
        message.setPortNumber(device.getPortNumber());
        publishToMqttTopic(topic, message);

        if (AuthorizationStatus.OFFLINE.equals(authorizationStatus)) {
            LOGGER.error("Cannot process device that is offline. Reconnect.");
            return;
        }

        // if there is a default grade, then set it in the device object right away
        if (inMemoryStore.getAssignedCloudCustomization() != null &&
                StringUtils.isNotBlank(inMemoryStore.getAssignedCloudCustomization().getDefaultGrade())) {
            device.setGrade(inMemoryStore.getAssignedCloudCustomization().getDefaultGrade());
        }

        // Add device in the tracker
        device.setStage(DeviceStage.INITIAL_CONNECTION);
        device.setProcessingStartTimeMillis(System.currentTimeMillis());
        deviceConnectionTracker.deviceConnected(device);

        final InitialConnectionStage stage = InitialConnectionStage
                .builder()
                .id(device.getId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .deviceType(device.getDeviceType())
                .licenseIdentifier(device.getLicenseIdentifier())
                .licenseId(Integer.toString(inMemoryStore.getLicenseId()))
                .testerName(inMemoryStore.getTesterName())
                .buildNo(inMemoryStore.getBuildNo())
                .build();

        // Update the data in database
        stageUpdater.updateStage(stage);
    }

    /**
     * Check if device in AT  mode came back in ADB mode. Update if that's the case.
     *
     * @param device
     */
    private void updateDeviceIfInADBMode(final AndroidDevice device) {
        try {
            Thread.sleep(1000);
            Set<AndroidDevice> connectedDevices = androidDeviceConnectionService.getConnectedDevicesThroughAdb();
            for (AndroidDevice connectedDevice : connectedDevices) {
                if (device.getId().equals(connectedDevice.getId())) {
                    device.setAndroidConnectionMode(AndroidConnectionMode.ADB);
                    device.setAuthorizationStatus(connectedDevice.getAuthorizationStatus());
                    LOGGER.info("Android device switched to {} mode and {} auth status",
                            device.getAndroidConnectionMode(),
                            device.getAuthorizationStatus());
                    break;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while getting adb connected devices to check if mode switched", e);
        }
    }
}
