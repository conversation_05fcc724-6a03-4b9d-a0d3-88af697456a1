package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceAutomationService;
import com.phonecheck.device.activation.IosActivateDeviceService;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceConnectionAutomationEvent;
import com.phonecheck.model.event.device.DeviceInitialDefectAutomationEvent;
import com.phonecheck.model.event.device.ios.IosActivationFailureEvent;
import com.phonecheck.model.event.device.ios.IosActivationNeededEvent;
import com.phonecheck.model.event.device.ios.IosActivationSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosActivationRequestMessage;
import com.phonecheck.model.status.ActivationStatus;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * Initiates iOS device activation
 */
@Component
public class IosActivationNeededListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosActivationNeededListener.class);
    private final ApplicationEventPublisher eventPublisher;
    private final IosActivateDeviceService iosActivateDeviceService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceAutomationService deviceAutomationService;
    private final DeviceAutomationQueueService automationQueueService;
    private final TimerLoggerUtil timerLoggerUtil;

    public IosActivationNeededListener(final ApplicationEventPublisher eventPublisher,
                                       final IosActivateDeviceService iosActivateDeviceService,
                                       final ObjectMapper objectMapper,
                                       final IMqttAsyncClient mqttClient,
                                       final DeviceConnectionTracker deviceConnectionTracker,
                                       final DeviceAutomationService deviceAutomationService,
                                       final DeviceAutomationQueueService automationQueueService,
                                       final TimerLoggerUtil timerLoggerUtil) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.iosActivateDeviceService = iosActivateDeviceService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.deviceAutomationService = deviceAutomationService;
        this.automationQueueService = automationQueueService;
        this.timerLoggerUtil = timerLoggerUtil;
    }

    @Async
    @EventListener
    public void onEvent(final IosActivationNeededEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Activation needed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("IOS device requires activation source {} {}", event.getSource(),
                deviceInTracker.getActivationStatus());
        LocalDateTime activationStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(), "Activation Start", activationStartTime);
        try {
            // Notify UI
            final String topic = TopicBuilder.build(device, "activation", "start");
            IosActivationRequestMessage requestMessage = new IosActivationRequestMessage();
            requestMessage.setId(device.getId());
            publishToMqttTopic(topic, requestMessage);

            ActivationStatus status;
            // Fail activation if IMEI is absent for iPhones and not iPods or iPads
            if (StringUtils.isBlank(deviceInTracker.getImei()) && DeviceType.IPHONE.equals(device.getDeviceType())) {
                status = ActivationStatus.FAILED_NO_IMEI;
            } else {
                // Initiate activation
                status = iosActivateDeviceService.activate(device);
            }

            if (ActivationStatus.FAILED_NO_IMEI.equals(status)
                    && deviceAutomationService.shouldRunInitialDefectAutomation(deviceInTracker)) {
                automationQueueService.enqueueDeviceAutomationRequest(
                        new DeviceInitialDefectAutomationEvent(this, deviceInTracker));
            }

            if (ActivationStatus.ICLOUD_LOCK_ON.equals(status)
                    && SetupDoneStatus.NOT_DONE.equals(deviceInTracker.getSetupDoneStatus())
                    && deviceAutomationService.shouldRunConnectionAutomation(deviceInTracker)
                    && deviceInTracker.getCurrentRunningAutomation() != AutomationWorkflow.CONNECTION) {
                deviceInTracker.setDeviceLock(DeviceLock.ON);
                automationQueueService.enqueueDeviceAutomationRequest(
                        new DeviceConnectionAutomationEvent(this, deviceInTracker));
                LOGGER.info("Device locked, Running device connection automation");
            }

            if (ActivationStatus.ACTIVATED.equals(status)) {
                timerLoggerUtil.printTimerLog(device.getId(), "Activation End", activationStartTime);
                eventPublisher.publishEvent(
                        new IosActivationSuccessEvent(this, device, DeviceLock.NA, event.isShouldInstallApp()));
            } else if (ActivationStatus.ICLOUD_LOCK_ON.equals(status)
                    && SetupDoneStatus.DONE.equals(deviceInTracker.getSetupDoneStatus())) {
                deviceInTracker.setDeviceLock(DeviceLock.ON);
                timerLoggerUtil.printTimerLog(device.getId(), "Activation End", activationStartTime);
                eventPublisher.publishEvent(
                        new IosActivationSuccessEvent(this, device, DeviceLock.ON, event.isShouldInstallApp()));
            } else {
                timerLoggerUtil.printTimerLog(device.getId(), "Activation Failed", activationStartTime);
                eventPublisher.publishEvent(new IosActivationFailureEvent(this, device, status));
            }
        } catch (IOException e) {
            LOGGER.error("Could not activate device ", e);
            timerLoggerUtil.printTimerLog(device.getId(), "Activation Failed", activationStartTime);
            eventPublisher.publishEvent(new IosActivationFailureEvent(this, device, ActivationStatus.FAILED_UNKNOWN));
        }
    }
}
