package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceCustom1UpdateRequestEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class DeviceCustom1UpdateListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceCustom1UpdateListener.class);
    private final DeviceInfoDBService deviceInfoDBService;
    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final ApplicationEventPublisher eventPublisher;

    public DeviceCustom1UpdateListener(final ObjectMapper objectMapper,
                                       final IMqttAsyncClient mqttClient,
                                       final DeviceInfoDBService deviceInfoDBService,
                                       final InMemoryStore inMemoryStore,
                                       final DeviceConnectionTracker deviceConnectionTracker,
                                       final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.deviceInfoDBService = deviceInfoDBService;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
    }

    /**
     * Event from UI to change device custom1 in DB and in deviceConnectionTracker
     *
     * @param event Custom1UpdateEvent from UI
     */
    @Async
    @EventListener
    public void onEvent(final DeviceCustom1UpdateRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Custom1 changed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Updating device custom1 from {} to {}", deviceInTracker.getCustom1(), device.getCustom1());

        deviceInTracker.setCustom1(device.getCustom1());
        deviceInfoDBService.updateCustom1(device.getId(),
                device.getCustom1(),
                String.valueOf(inMemoryStore.getTransaction().getTransactionId()));

        if (deviceInTracker.getPreviouslyRanAutomation().containsKey(AutomationWorkflow.TEST_RESULTS) &&
                deviceInTracker.getPreviouslyRanAutomation().get(AutomationWorkflow.TEST_RESULTS)
                        == AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS) {
            LOGGER.info("Test result automation was failed due to required fields, Performing automation again.");
            eventPublisher.publishEvent(new DeviceTestResultAutomationEvent(this, device));
        }
    }
}
