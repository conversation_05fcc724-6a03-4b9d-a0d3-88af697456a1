package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.device.DeviceProcessStatus;
import com.phonecheck.model.device.DeviceRunningProcesses;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.BatteryInfoSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosBatteryInfoSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceBatteryInfoSuccessMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Notifies MQTT broker when battery info collection succeeded
 */
@Component
public class IosBatteryInfoSuccessListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosBatteryInfoSuccessListener.class);
    private final DeviceStageUpdater stageUpdater;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;

    public IosBatteryInfoSuccessListener(final IMqttAsyncClient mqttClient,
                                         final ObjectMapper objectMapper,
                                         final DeviceStageUpdater stageUpdater,
                                         final DeviceConnectionTracker deviceConnectionTracker,
                                         final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.stageUpdater = stageUpdater;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
    }

    @EventListener
    public void onEvent(final IosBatteryInfoSuccessEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Battery info collection succeeded but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Battery info collected successfully");

        final BatteryInfoSuccessStage stage = BatteryInfoSuccessStage
                .builder()
                .id(deviceInTracker.getId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .batteryDegraded(deviceInTracker.getBatteryDegraded())
                .batteryStateHealth(deviceInTracker.getBatteryStateHealth() != 0
                        ? deviceInTracker.getBatteryStateHealth()
                        : deviceInTracker.getPreviousBatteryStateHealth())
                .batteryPercentage(deviceInTracker.getBatteryPercentage())
                .batteryInfo(deviceInTracker.getBatteryInfo())
                .timestamp(System.currentTimeMillis())
                .build();

        // Update the device state in the DB
        LOGGER.debug("Updating device stage {} in data store", stage.getStage().name());
        stageUpdater.updateStage(stage);

        final String topic = TopicBuilder.build(deviceInTracker, "battery", "info", "collection", "success");
        // Notify UI
        final DeviceBatteryInfoSuccessMessage batteryInfoSuccessMessage =
                new DeviceBatteryInfoSuccessMessage();
        batteryInfoSuccessMessage.setId(deviceInTracker.getId());
        batteryInfoSuccessMessage.setBatteryInfo(deviceInTracker.getBatteryInfo());
        batteryInfoSuccessMessage.setBatteryStateHealth(deviceInTracker.getBatteryStateHealth());
        batteryInfoSuccessMessage.setBatteryPercentage(deviceInTracker.getBatteryPercentage());
        publishToMqttTopic(topic, batteryInfoSuccessMessage);
        // Battery info retrieved
        deviceInTracker.getDeviceProcessStatusMap().
                put(DeviceRunningProcesses.BATTERY, DeviceProcessStatus.COMPLETED);
    }
}