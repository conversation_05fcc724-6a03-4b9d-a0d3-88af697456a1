package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.dao.TestResultDao;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.model.cloudapi.ShopfloorCustomizationResponse;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceInitialDefectAutomationEvent;
import com.phonecheck.model.event.device.DeviceResultsOverrideRequestEvent;
import com.phonecheck.model.event.device.DeviceSkuCodeUpdateEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceGradeUpdateMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.CosmeticsResults;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.TestResultSummary;
import com.phonecheck.model.util.FunctionalityStatusUtil;
import com.phonecheck.model.util.TestResultsUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.phonecheck.model.twowayapi.TwoWayApiConstants.GRADING_PERFORMED_KEY;

@Component
public class DeviceResultsOverrideRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceResultsOverrideRequestListener.class);
    private final TestResultDao testResultDao;
    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceTestResultDBService deviceTestResultDBService;
    private final DeviceAutomationQueueService automationQueueService;
    private final ApplicationEventPublisher eventPublisher;

    protected DeviceResultsOverrideRequestListener(
            final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
            final TestResultDao testResultDao, final InMemoryStore inMemoryStore,
            final DeviceConnectionTracker deviceConnectionTracker,
            final DeviceTestResultDBService deviceTestResultDBService,
            final DeviceAutomationQueueService automationQueueService,
            final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.testResultDao = testResultDao;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.deviceTestResultDBService = deviceTestResultDBService;
        this.automationQueueService = automationQueueService;
        this.eventPublisher = eventPublisher;
    }

    /**
     * Handles manual test results override event
     *
     * @param event ManualTestResultsOverrideEvent
     */
    @Async
    @EventListener
    public void onEvent(final DeviceResultsOverrideRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        boolean shouldStartTestResultAutomation = event.isShouldStartTestResultAutomation();

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Manual test results override requested but device has been disconnected," +
                    " stopping processing.");
            return;
        }

        LOGGER.info("Initiate Manual test results override");

        if ((device.getDeviceTestResult().getTestResults().getPassed() == null ||
            device.getDeviceTestResult().getTestResults().getPassed().isEmpty())
                && (device.getDeviceTestResult().getTestResults().getFailed() == null ||
                device.getDeviceTestResult().getTestResults().getFailed().isEmpty())) {
            LOGGER.info("Test results override requested device has empty test results. Fetching test results from DB");
            DeviceTestResult deviceTestResultFromDb = deviceTestResultDBService.getDeviceTestResults(
                    String.valueOf(inMemoryStore.getTransaction().getTransactionId()), device.getId(),
                    String.valueOf(inMemoryStore.getLicenseId()), device.getSerial());
            if (deviceTestResultFromDb != null
                    && deviceTestResultFromDb.getTestResults() != null) {
                LOGGER.info("Results retrieved from the database");
                device.getDeviceTestResult().getTestResults()
                        .setPassed(deviceTestResultFromDb.getTestResults().getPassed());
                device.getDeviceTestResult().getTestResults()
                        .setFailed(deviceTestResultFromDb.getTestResults().getFailed());
                device.getDeviceTestResult().getTestResults().setTestingCompleted(
                        deviceTestResultFromDb.getTestResults().getTestingCompleted());
            }
        }

        LOGGER.info("Updated test results... passed: {} , failed: {} , testingCompleted: {}",
                device.getDeviceTestResult().getTestResults().getPassed(),
                device.getDeviceTestResult().getTestResults().getFailed(),
                device.getDeviceTestResult().getTestResults().getTestingCompleted());

        if (device.getDeviceTestResult().getCosmeticResults() != null) {
            LOGGER.info("Updated cosmetics passed : {} failed : {}",
                    device.getDeviceTestResult().getCosmeticResults().getPassed(),
                    device.getDeviceTestResult().getCosmeticResults().getFailed());
        } else {
            LOGGER.info("No desktop cosmetics were sent");
        }
        // Set grade for shopfloor defect mapping
        setGradeFromDefectMapping(deviceInTracker, device.getDeviceTestResult());
        // Override app test results to sync to cloud db
        deviceInTracker.setDeviceTestResult(device.getDeviceTestResult());
        if (deviceInTracker instanceof IosDevice iosDevice) {
            LOGGER.info("Updated touchId sensor: {}, and faceId sensor: {}", ((IosDevice) device).getTouchIdSensor(),
                    ((IosDevice) device).getFaceIdSensor());
            if (((IosDevice) device).getTouchIdSensor() != null) {
                iosDevice.setTouchIdSensor(((IosDevice) device).getTouchIdSensor());

            }
            if (((IosDevice) device).getFaceIdSensor() != null) {
                iosDevice.setFaceIdSensor(((IosDevice) device).getFaceIdSensor());

            }
            iosDevice.setTouchIdManuallyChanged(((IosDevice) device).isTouchIdManuallyChanged());
            iosDevice.setFaceIdManuallyChanged(((IosDevice) device).isFaceIdManuallyChanged());
            LOGGER.info("manual face id {} touch id {}", iosDevice.isFaceIdManuallyChanged(),
                    iosDevice.isTouchIdManuallyChanged());
        }
        try {
            // Sync the test results in db
            final TestResultSummary testResultSummary = TestResultSummary.builder()
                    .id(device.getId())
                    .passedTests(TestResultsUtil.listToCommaSeparatedString(
                            deviceInTracker.getDeviceTestResult().getTestResults().getPassed()))
                    .failedTests(TestResultsUtil.listToCommaSeparatedString(
                            deviceInTracker.getDeviceTestResult().getTestResults().getFailed()))
                    .testingCompleted(deviceInTracker.getDeviceTestResult().getTestResults().getTestingCompleted())
                    .build();

            testResultDao.updateGeneralResults(deviceInTracker.getSerial(), deviceInTracker.getDeviceType(),
                    String.valueOf(inMemoryStore.getTransaction().getTransactionId()),
                    String.valueOf(inMemoryStore.getLicenseId()),
                    testResultSummary);

            // Save cosmetics grading results if we have them
            final CosmeticsResults cosmeticsResults = device.getDeviceTestResult().getCosmeticResults();
            if (cosmeticsResults != null) {
                final List<String> cosmeticsPassed = cosmeticsResults.getPassed() != null
                        ? Arrays.stream(cosmeticsResults.getPassed().split(",")).toList()
                        : null;
                final List<String> cosmeticsFailed = cosmeticsResults.getFailed() != null
                        ? Arrays.stream(cosmeticsResults.getFailed().split(",")).toList()
                        : null;
                if (!CollectionUtils.isEmpty(cosmeticsPassed) || !CollectionUtils.isEmpty(cosmeticsFailed)) {
                    try {
                        testResultDao.updateCosmetics(device.getId(),
                                String.valueOf(inMemoryStore.getTransaction().getTransactionId()),
                                cosmeticsPassed, cosmeticsFailed);

                        if (deviceInTracker.getPreviouslyRanAutomation().containsKey(AutomationWorkflow.TEST_RESULTS) &&
                                deviceInTracker.getPreviouslyRanAutomation().get(AutomationWorkflow.TEST_RESULTS)
                                        == AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS) {
                            LOGGER.info("Test result automation was failed due to required fields, " +
                                    "Performing automation again.");
                            eventPublisher.publishEvent(new DeviceTestResultAutomationEvent(this, device));
                        }
                    } catch (JsonProcessingException e) {
                        LOGGER.error("Exception occurred while parsing cosmetics in AppTestingDoneStage", e);
                    }
                }
            }

            // Raise InitialDefect Automation event if failed test results are found
            if (device.getDeviceTestResult().getTestResults().getFailed() != null &&
                    !device.getDeviceTestResult().getTestResults().getFailed().isEmpty()) {
                automationQueueService.enqueueDeviceAutomationRequest(
                        new DeviceInitialDefectAutomationEvent(this, deviceInTracker));
            }

            String functionalityStatus = FunctionalityStatusUtil.getFunctionalityStatus(device, true,
                    inMemoryStore.getAssignedCloudCustomization());
            boolean isDeviceFullyFunctional = functionalityStatus.equals(FunctionalityStatusUtil.FULLY_FUNCTIONAL);

            // Raise automation initiating event if device becomes fully functional after manual override
            // and if device automation is already not running
            if (shouldStartTestResultAutomation && isDeviceFullyFunctional) {
                automationQueueService.enqueueDeviceAutomationRequest(
                        new DeviceTestResultAutomationEvent(this, deviceInTracker));
            }

            //Async event to generate SKU Code
            eventPublisher.publishEvent(new DeviceSkuCodeUpdateEvent(this, device));
        } catch (JsonProcessingException e) {
            LOGGER.error("Could not manually override test results in db", e);
        }
    }

    /**
     * Sets the grade of a device based on the mapping of defects.
     *
     * @param device            target device
     * @param deviceTestResults the test results of the device
     */
    public void setGradeFromDefectMapping(final Device device, final DeviceTestResult deviceTestResults) {
        if (StringUtils.equals(device.getGradingAssociateDefects(), GRADING_PERFORMED_KEY)) {
            LOGGER.info("Set grade from defect mapping on device results");
            List<String> whiteListedDefect = new ArrayList<>();
            if (inMemoryStore.getWhiteListedDefects() != null && !inMemoryStore.getWhiteListedDefects().isEmpty()) {
                for (ShopfloorCustomizationResponse.WhiteListedDefectsMapping
                        whitelistedDefects : inMemoryStore.getWhiteListedDefects()) {
                    List<String> defects = Arrays.stream(whitelistedDefects.getWhiteListedDefects().split(","))
                            .map(String::trim)
                            .toList();
                    whiteListedDefect.addAll(defects);
                }
            }
            if (inMemoryStore.getDefectsList() != null && !inMemoryStore.getDefectsList().isEmpty() &&
                    (deviceTestResults.getTestResults() != null &&
                            deviceTestResults.getTestResults().getFailed() != null &&
                            !deviceTestResults.getTestResults().getFailed().isEmpty())) {
                int counter = 0;
                for (ShopfloorCustomizationResponse.DefectMapping defects : inMemoryStore.getDefectsList()) {
                    LOGGER.info("defect list found in memory store defects {}", inMemoryStore.getDefectsList());
                    List<String> associatedDefects = Arrays.stream(defects.getAssociatedDefects().split(","))
                            .map(String::trim)
                            .toList();
                    associatedDefects = associatedDefects.stream()
                            .filter(defect -> !whiteListedDefect.contains(defect))
                            .collect(Collectors.toList());
                    if (TestResultsUtil.areAllStringsPresent(associatedDefects,
                            TestResultsUtil.listToCommaSeparatedString(deviceTestResults.getTestResults().
                                    getFailed()))) {
                        String associatedDefectGrade = inMemoryStore.getDefectsList().get(counter).getAssociatedGrade();
                        if (StringUtils.isNotBlank(associatedDefectGrade)) {
                            device.setGrade(associatedDefectGrade);
                            device.setGradingSystemGrade(associatedDefectGrade);
                            device.setGradePerformed(true);
                            device.getDeviceTestResult().setGradeResults(associatedDefectGrade);

                            final String topic = TopicBuilder.build(device, "grade", "update");
                            DeviceGradeUpdateMessage message = new DeviceGradeUpdateMessage();
                            message.setId(device.getId());
                            message.setGrade(associatedDefectGrade);
                            publishToMqttTopic(topic, message);
                            break;
                        }
                    }
                    counter++;
                }
            }
        } else {
            LOGGER.info("Defect list is empty in memory store {}", inMemoryStore.getDefectsList());
        }
    }
}
