package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosPushWifiProfileEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosPushWifiFailureMessage;
import com.phonecheck.model.mqtt.messages.ios.IosPushWifiSuccessMessage;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.peo.ios.WifiService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class IosPushWifiProfileListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosPushWifiProfileListener.class);
    private final WifiService wifiService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final TimerLoggerUtil timerLoggerUtil;

    protected IosPushWifiProfileListener(final IMqttAsyncClient mqttClient, final WifiService wifiService,
                                         final DeviceConnectionTracker deviceConnectionTracker,
                                         final ObjectMapper objectMapper,
                                         final TimerLoggerUtil timerLoggerUtil) {
        super(mqttClient, objectMapper);
        this.wifiService = wifiService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.timerLoggerUtil = timerLoggerUtil;
    }

    @Async
    @EventListener
    public void onEvent(final IosPushWifiProfileEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Pushing wifi profile but device has been disconnected, stopping processing.");
            return;
        }
        LocalDateTime pushWifiProfileStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Pushing wifi profile start", pushWifiProfileStartTime);

        LOGGER.info("Pushing wifi profile to IOS device");
        if (wifiService.pushWifiProfile(device)) {
            LOGGER.info("Wifi profile pushed successfully to IOS device");
            timerLoggerUtil.printTimerLog(device.getId(),
                    "Pushing wifi profile end", pushWifiProfileStartTime);
            String topic = TopicBuilder.build(device, "push-wifi", "success");
            IosPushWifiSuccessMessage iosPushWifiSuccessMessage = new IosPushWifiSuccessMessage();
            iosPushWifiSuccessMessage.setId(device.getId());
            publishToMqttTopic(topic, iosPushWifiSuccessMessage);
        } else {
            timerLoggerUtil.printTimerLog(device.getId(),
                    "Pushing wifi profile failed", pushWifiProfileStartTime);
            LOGGER.info("Wifi profile pushed failed to IOS device");
            String topic = TopicBuilder.build(device, "push-wifi", "failure");
            IosPushWifiFailureMessage iosPushWifiFailureMessage = new IosPushWifiFailureMessage();
            iosPushWifiFailureMessage.setId(device.getId());
            publishToMqttTopic(topic, iosPushWifiFailureMessage);
        }
    }
}
