package com.phonecheck.backend.listener.device.ios.precheck;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.info.ios.PreCheckTestResultsService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.PreCheckInfo;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.precheck.PreCheckInfoResultsRequestEvent;
import com.phonecheck.model.event.device.ios.precheck.PreCheckInfoResultsSuccessEvent;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class PreCheckInfoResultsSuccessListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreCheckInfoResultsSuccessListener.class);

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final ApplicationEventPublisher eventPublisher;
    private final PreCheckTestResultsService preCheckTestResultsService;

    public PreCheckInfoResultsSuccessListener(final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                              final DeviceConnectionTracker deviceConnectionTracker,
                                              final ApplicationEventPublisher eventPublisher,
                                              final PreCheckTestResultsService preCheckTestResultsService) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
        this.preCheckTestResultsService = preCheckTestResultsService;
    }

    @Async
    @EventListener
    public void onEvent(final PreCheckInfoResultsSuccessEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        final PreCheckInfo.DeviceInfoResults infoResults = event.getInfoResults();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Pre check to be performed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("PreCheck device info tests override initiated: {}", infoResults);

        deviceInTracker.getPreCheckInfo().setDeviceInfoResults(infoResults);
        preCheckTestResultsService.stopInfoTestProcess(deviceInTracker.getId(),
                deviceInTracker.getPreCheckInfo().getPreCheckRunningProcessTag());
        eventPublisher.publishEvent(new PreCheckInfoResultsRequestEvent(this, deviceInTracker));
    }


}