package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.info.android.eid.EidCommandsService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidEidAcquisitionEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Initiates to fetch imei of android device
 */
@Component
public class AndroidEidAcquisitionListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidEidAcquisitionListener.class);
    private static final int GET_EID_MAX_RETRIES = 2;

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final EidCommandsService eidCommandsService;
    private final DeviceInfoDBService deviceInfoDBService;
    private final InMemoryStore inMemoryStore;

    public AndroidEidAcquisitionListener(final ObjectMapper objectMapper,
                                         final IMqttAsyncClient mqttClient,
                                         final DeviceConnectionTracker deviceConnectionTracker,
                                         final EidCommandsService eidCommandsService,
                                         final DeviceInfoDBService deviceInfoDBService,
                                         final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eidCommandsService = eidCommandsService;
        this.deviceInfoDBService = deviceInfoDBService;
        this.inMemoryStore = inMemoryStore;
    }

    @EventListener
    public void onEvent(final AndroidEidAcquisitionEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Try to Fetch EID but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Android EID acquisition event received");

        if (StringUtils.isBlank(deviceInTracker.getEid())) {
            // Try to fetch EID from OCR
            String eidFromOcr = getEidFromOcr(deviceInTracker);

            if (StringUtils.isBlank(eidFromOcr)) {
                // Try to fetch EID from XML
                String eidFromXml = getEidFromXml(deviceInTracker);
                if (StringUtils.isNotBlank(eidFromXml)) {
                    deviceInTracker.setEid(eidFromXml);
                }
            } else {
                deviceInTracker.setEid(eidFromOcr);
            }
            deviceInfoDBService.updateEid(deviceInTracker.getId(), String.valueOf(inMemoryStore.getTransaction()
                    .getTransactionId()), deviceInTracker.getEid());
        }
    }

    /**
     * Attempts to retrieve the EID of an Android device using OCR commands.
     * with retries in case of failures.
     *
     * @param device The Android device for which to retrieve EID
     * @return eid value if fetched, else return null
     */
    private String getEidFromOcr(final AndroidDevice device) {
        setDeviceIdMDC(device.getId());

        int getEidRetryCounter = 0;
        if (AndroidConnectionMode.ADB.equals(device.getAndroidConnectionMode())) {
            do {
                try {
                    String eid = eidCommandsService.getEidWithOCR(device.getId());
                    if (StringUtils.isNotBlank(eid)) {
                        LOGGER.info("Successfully retrieved EID from OCR command {}", eid);
                        return eid;
                    }

                    Thread.sleep(1000);
                } catch (InterruptedException | IOException e) {
                    LOGGER.error("Error while fetching EID through OCR command", e);
                }

            } while (++getEidRetryCounter < GET_EID_MAX_RETRIES);
        } else {
            LOGGER.info("Device connected in AT mode, skipping getEidFromOcr");
        }
        return null;
    }

    /**
     * Attempts to retrieve the EID of an Android device using XML commands.
     * with retries in case of failures.
     *
     * @param device The Android device for which to retrieve EID
     * @return eid value if retrieved else return null
     */
    private String getEidFromXml(final AndroidDevice device) {
        setDeviceIdMDC(device.getId());
        int getEidRetryCounter = 0;
        if (AndroidConnectionMode.ADB.equals(device.getAndroidConnectionMode())) {
            do {
                try {
                    String eid = eidCommandsService.getEidWithXml(device.getId());
                    if (StringUtils.isNotBlank(eid)) {
                        LOGGER.info("Successfully retrieved EID from XML command {}", eid);
                        return eid;
                    }

                    Thread.sleep(1000);
                } catch (InterruptedException | IOException e) {
                    LOGGER.error("Error while fetching EID through XML command", e);
                }

            } while (++getEidRetryCounter < GET_EID_MAX_RETRIES);
        } else {
            LOGGER.info("Device connected in AT mode, skipping getEidFromXml");
        }
        return null;
    }
}