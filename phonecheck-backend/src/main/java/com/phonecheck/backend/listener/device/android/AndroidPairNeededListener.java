package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.device.connection.android.AndroidDeviceConnectionService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidPairingFailureEvent;
import com.phonecheck.model.event.device.android.AndroidPairingNeededEvent;
import com.phonecheck.model.event.device.android.AndroidPairingSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidPairNeededMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class AndroidPairNeededListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidPairNeededListener.class);
    private final ApplicationEventPublisher eventPublisher;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final AndroidDeviceConnectionService androidDeviceConnectionService;


    public AndroidPairNeededListener(final ApplicationEventPublisher eventPublisher,
                                     final ObjectMapper objectMapper,
                                     final IMqttAsyncClient mqttClient,
                                     final DeviceConnectionTracker deviceConnectionTracker,
                                     final AndroidDeviceConnectionService androidDeviceConnectionService) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.androidDeviceConnectionService = androidDeviceConnectionService;
    }

    /**
     * If device is authorized then publish next event
     * If device is unauthorized then authorize and then publish event
     * If device is offline then send message to UI to reconnect
     *
     * @param event AndroidPairingNeededEvent
     */
    @EventListener
    public void onEvent(final AndroidPairingNeededEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());
        LOGGER.info("Authorization needed for device");

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Authorization needed but device has been disconnected, stopping processing.");
            return;
        }

        // Notify UI
        final String pairNeededTopic = TopicBuilder.
                build(device, "pair");
        AndroidPairNeededMessage pairNeededMessage = new AndroidPairNeededMessage();
        pairNeededMessage.setId(device.getId());
        publishToMqttTopic(pairNeededTopic, pairNeededMessage);

        // Get authorization status for device
        AuthorizationStatus authorizationStatus = deviceInTracker.getAuthorizationStatus();

        if (AuthorizationStatus.UNAUTHORIZED.equals(authorizationStatus)) {
            // Run this loop till user authorize the device or if authorization fails
            while (true) {
                authorizationStatus = androidDeviceConnectionService.
                        getConnectedDeviceAuthorization(device.getId());
                // if authorization status is anything other than unauthorized just break the loop
                if (!AuthorizationStatus.UNAUTHORIZED.equals(authorizationStatus)) {
                    LOGGER.info("Exiting Authorization loop with status: {}", authorizationStatus);
                    break;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    // Do nothing
                }
            }
        }

        deviceInTracker.setAuthorizationStatus(authorizationStatus);

        if (AuthorizationStatus.AUTHORIZED.equals(authorizationStatus)) {
            final AndroidPairingSuccessEvent androidPairingSuccessEvent =
                    new AndroidPairingSuccessEvent(this, device, event.isShouldInstallApps());
            eventPublisher.publishEvent(androidPairingSuccessEvent);
        } else {
            final AndroidPairingFailureEvent androidPairingFailureEvent =
                    new AndroidPairingFailureEvent(this, device);
            eventPublisher.publishEvent(androidPairingFailureEvent);
        }
    }
}
