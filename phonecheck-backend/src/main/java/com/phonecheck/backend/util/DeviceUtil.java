package com.phonecheck.backend.util;

import com.phonecheck.model.device.Device;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.phonecheck.client.customization.ClientCustomizationService.WIFI;
import static com.phonecheck.client.customization.ClientCustomizationService.WIFI_ONLY;


public class DeviceUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceUtil.class);

    private static final String CELLULAR = "Cellular";

    /**
     * check if the device is a non-imei device
     * @param device
     * @return true if device doesn't have an imei
     * false otherwise
     */
    public static boolean isNonImeiDevice(final Device device) {
        // Carrier field can be "Wifi" or "Wifi Only" but not "Wifi + Cellular"
        boolean isNonImeiDevice = StringUtils.isBlank(device.getImei())
                || (StringUtils.isNotBlank(device.getImei()) && device.getImei().equals(device.getSerial()))
                || (StringUtils.containsIgnoreCase(device.getCarrier(), WIFI) &&
                                    !StringUtils.containsIgnoreCase(device.getCarrier(), CELLULAR))
                || StringUtils.equalsIgnoreCase(device.getCarrier(), WIFI_ONLY);

        LOGGER.info("Is device non imei ? {} ", isNonImeiDevice);

        return isNonImeiDevice;
    }
}
