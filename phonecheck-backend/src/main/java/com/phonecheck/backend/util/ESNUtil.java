package com.phonecheck.backend.util;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.cloudapi.EsnResponse;
import com.phonecheck.model.constants.EsnFieldColor;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.service.EsnCheckInfo;
import com.phonecheck.model.status.EsnStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

@Component
public class ESNUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(ESNUtil.class);
    private final ObjectMapper mapper = new ObjectMapper();
    private static final List<String> BAD_STATUS = Arrays.asList("Lost or stolen", "Blocked");
    private static final List<String> GOOD_STATUS = Arrays.asList("Not Compatible", "Invalid IMEI", "Good");

    /**
     * Retrieves the content of the ESN-Check information.
     *
     * @param esnApiResponses The ESN license check response list
     * @return The content of the ESN check information as a String.
     */
    public String getEsnContent(final EsnCheckInfo esnApiResponses) {
        StringBuilder contentBuilder = new StringBuilder();
        String content = "";
        try {

            if (esnApiResponses.getUsInsuranceBlackListInfo() != null) {
                appendInsuranceResponseToContent(esnApiResponses.getUsInsuranceBlackListInfo(), contentBuilder);
            }

            if (esnApiResponses.getEsnApiResults() != null) {
                for (EsnResponse.EsnApiResponse esnResponse : esnApiResponses.getEsnApiResults()) {

                    contentBuilder.append("API: ").append(esnResponse.getApi()).append("<br>");
                    contentBuilder.append("Remarks: ").append(esnResponse.getRemarks()).append("<br>");
                    contentBuilder.append("Field Color: ").append(esnResponse.getFieldColor()).append("<br>");
                    contentBuilder.append("IMEI: ").append(esnResponse.getImei()).append("<br>");
                    contentBuilder.append("Carrier: ").append(esnResponse.getCarrier()).append("<br>");
                    contentBuilder.append("FallBackCall: ").append(esnResponse.getFallbackCall()).append("<br>");
                    contentBuilder.append("Charge Status: ").append(esnResponse.getChargeStatus()).append("<br>");

                    if (esnResponse.getRawResponse() != null) {
                        getESNRawResponseValues(esnResponse.getRawResponse(), contentBuilder);
                    }
                    contentBuilder.append("<br>------------------------------------------------<br><br>");
                }
            }

            if (esnApiResponses.getTracFoneStraightTalkAPIResponse() != null) {
                appendTracfoneResponseToContent(esnApiResponses.getTracFoneStraightTalkAPIResponse(),
                        contentBuilder);
            }
        } catch (Exception e) {
            LOGGER.error("Exception while parsing ", e);
        }
        content = contentBuilder.toString();
        content = content.replace(",", "<br>");
        content = content.replace("null", "not available");
        return content;
    }

    /**
     * Converts the data response value and appends device details the content builder
     *
     * @param response       The response object to convert.
     * @param contentBuilder The content builder to append details to.
     */
    private void appendInsuranceResponseToContent(final EsnResponse.UsInsuranceBlackListInfo response,
                                                  final StringBuilder contentBuilder) {
        try {
            EsnResponse.UsInsuranceBlackListInfo deviceResponse = mapper.convertValue(response,
                    EsnResponse.UsInsuranceBlackListInfo.class);

            contentBuilder.append("IMEI: ").append(deviceResponse.getData().getIMEI()).append("<br>");
            contentBuilder.append("Require Sim: ").append(deviceResponse.getData().isRequiresSim()).append("<br>");
            contentBuilder.append("Reuse Sim: ").append(deviceResponse.getData().isReuseSim()).append("<br>");
            contentBuilder.append("Model: ").append(deviceResponse.getData().getModel()).append("<br>");
            contentBuilder.append("Manufacture Name: ").append(deviceResponse.getData()
                    .getManufacturer()).append("<br>");
            contentBuilder.append("Is GSM: ").append(deviceResponse.getData().isGSM()).append("<br>");
            contentBuilder.append("eSim Compatible: ").append(deviceResponse.getData()
                    .isESIMCompatible()).append("<br>");
            contentBuilder.append("Sim Slots: ").append(deviceResponse.getData().isSimSlots()).append("<br>");
            contentBuilder.append("VolLTE Compatible: ").append(deviceResponse.getData()
                    .isVoLTECompatible()).append("<br>");
            contentBuilder.append("Compatibility: ").append(deviceResponse.getData().getCompatibility()).append("<br>");
            contentBuilder.append("Device Type: ").append(deviceResponse.getData().getDeviceType()).append("<br>");
            contentBuilder.append("Blacklist Status: ").append(deviceResponse.getData()
                    .getBlacklistStatus()).append("<br>");
            contentBuilder.append("<br>------------------------------------------------<br><br>");
        } catch (Exception e) {
            LOGGER.error("Exception while parsing US Insurance blacklist response ", e);
        }
    }

    /**
     * Converts the data response value and appends device details the content builder
     *
     * @param response       The response object to convert.
     * @param contentBuilder The content builder to append details to.
     */
    private void appendTracfoneResponseToContent(final EsnResponse.TracFoneStraightTalkAPIResponse response,
                                                 final StringBuilder contentBuilder) {
        try {
            EsnResponse.TracFoneStraightTalkAPIResponse deviceResponse = mapper.convertValue(response,
                    EsnResponse.TracFoneStraightTalkAPIResponse.class);

            EsnResponse.TracFoneStraightTalkAPIResponse.TracFoneApiResponse tracFoneApiResponse =
                    deviceResponse.getTracFoneApiResponse();

            if (tracFoneApiResponse.getOverall().equalsIgnoreCase("N/A")) {
                contentBuilder.append("Trackfone StraightLight Eligibility: ").append("N/A")
                        .append("<br>");
            } else {
                contentBuilder.append("Overall: ").append(tracFoneApiResponse.getOverall()).append("<br>");
                contentBuilder.append("IMEI: ").append(tracFoneApiResponse.getImei()).append("<br>");
                contentBuilder.append("Brand: ").append(tracFoneApiResponse.getBrand()).append("<br>");
                contentBuilder.append("Type: ").append(tracFoneApiResponse.getType()).append("<br>");
                contentBuilder.append("Part No:  ").append(tracFoneApiResponse.getPartNo()).append("<br>");
                contentBuilder.append("Trackfone StraightLight Eligibility: ").append(tracFoneApiResponse
                        .getTracfoneStraightTalkEligibility()).append("<br>");
                contentBuilder.append("Sim Required: ").append(tracFoneApiResponse.getSimRequired()).append("<br>");
                contentBuilder.append("Reserved Line: ").append(tracFoneApiResponse.getReservedLine()).append("<br>");
                contentBuilder.append("Safe Link Plan: ").append(tracFoneApiResponse.getSafeLinkPlan()).append("<br>");
                contentBuilder.append("Zip Code: ").append(tracFoneApiResponse.getZipCode()).append("<br>");
                contentBuilder.append("Related Account:  ").append(tracFoneApiResponse.getRelatedAccount()).
                        append("<br>");
                contentBuilder.append("Billing Account:  ").append(tracFoneApiResponse.getBillingAccount()).
                        append("<br>");
                contentBuilder.append("Activation Date:  ").append(tracFoneApiResponse.getActivationDate()).
                        append("<br>");
            }
            contentBuilder.append("<br>------------------------------------------------<br><br>");
        } catch (Exception e) {
            LOGGER.error("Exception while parsing Tracfone response ", e);
        }
    }

    /**
     * Retrieves the raw response values from an ESN license check response
     * and appends them to the content builder.
     *
     * @param response       The response object containing the raw response values.
     * @param contentBuilder The StringBuilder object to build esn content
     */
    private void getESNRawResponseValues(final Object response, final StringBuilder contentBuilder) {
        // verify if ESNRawResponse is object then map it to RawResponse object and retrieve values one by one
        // if ESNRawResponse is string then append in content builder
        if (response instanceof LinkedHashMap<?, ?>) {

            try {
                EsnResponse.EsnApiResponse.RawResponse rawResponse = mapper.convertValue(response,
                        EsnResponse.EsnApiResponse.RawResponse.class);

                contentBuilder.append("Ref Code: ").append(rawResponse.getRefCode()).append("<br>");
                contentBuilder.append("Device Type: ").append(rawResponse.getDeviceType()).append("<br>");
                contentBuilder.append("Marketing Name: ").append(rawResponse.getMarketingName()).append("<br>");
                contentBuilder.append("Brand Name: ").append(rawResponse.getBrandName()).append("<br>");
                contentBuilder.append("Manufacturer: ").append(rawResponse.getManufacturer()).append("<br>");
                contentBuilder.append("Response Status: ").append(rawResponse.getResponseStatus()).append("<br>");
                contentBuilder.append("Black List Status: ").append(rawResponse.getBlacklistStatus()).append("<br>");
                contentBuilder.append("Grey List Status: ").append(rawResponse.getGreyListStatus()).append("<br>");
                contentBuilder.append("Operating System: ").append(rawResponse.getOperatingSys()).append("<br>");
                contentBuilder.append("Model Name: ").append(rawResponse.getModelName()).append("<br>");
                contentBuilder.append("Checkmend ID:").append(rawResponse.getCheckmendId()).append("<br>");
                if (rawResponse.getImeiHistory() != null && rawResponse.getImeiHistory().length > 0) {
                    for (int i = 0; i < rawResponse.getImeiHistory().length; i++) {
                        contentBuilder.append("Blacklisted Reason Desc: ")
                                .append(rawResponse.getImeiHistory()[i].getReasonCodeDesc()).append("<br>");
                        contentBuilder.append("Blacklisted Date: ")
                                .append(rawResponse.getImeiHistory()[i].getDate()).append("<br>");
                        contentBuilder.append("Blacklisted Country: ")
                                .append(rawResponse.getImeiHistory()[i].getCountry()).append("<br>");
                    }
                }
            } catch (Exception e) {
                EsnResponse.EsnApiResponse.RawResponseErrorCheckMend rawResponseCM = mapper.convertValue(response,
                        EsnResponse.EsnApiResponse.RawResponseErrorCheckMend.class);
                contentBuilder.append("Errors: ").append(rawResponseCM.getErrors()).append("<br>");
                if (rawResponseCM.getImei_1() != null) {
                    contentBuilder.append("IMEI 1: ").append(rawResponseCM.getImei_1()).append("<br>");
                }
                if (rawResponseCM.getImei_2() != null) {
                    contentBuilder.append("IMEI 2: ").append(rawResponseCM.getImei_2()).append("<br>");
                }
                if (rawResponseCM.getSerial() != null) {
                    contentBuilder.append("Serial: ").append(rawResponseCM.getSerial()).append("<br>");
                }
                if (rawResponseCM.getOverall() != null) {
                    contentBuilder.append("Overall: ").append(rawResponseCM.getOverall()).append("<br>");
                }
            }
        } else {
            contentBuilder.append("Raw Response: ").append(response);
        }
    }

    /**
     * Gets the respective service Id for the given carrier and esnCheck enum
     *
     * @param carrier      device carrier
     * @param esnCheckType EsnCheck
     * @return int serviceId
     */
    public String getESNServiceId(final String carrier, final EsnCheckType esnCheckType) {
        String serviceId = "-1";
        if (EsnCheckType.ESN_CHECK == esnCheckType && StringUtils.isNotEmpty(carrier)) {
            if (StringUtils.containsIgnoreCase(carrier, "VERIZON")) {
                serviceId = "9";
            } else if (StringUtils.containsIgnoreCase(carrier.replace("-", ""), "tmobile")) {
                serviceId = "10";
            } else if (StringUtils.containsIgnoreCase(carrier, "sprint")) {
                serviceId = "11";
            } else if (StringUtils.containsIgnoreCase(carrier.replace("&", ""), "ATT")) {
                serviceId = "12";
            }
        }
        return serviceId;
    }

    /**
     * Determines the ESN status based on a list of ESN API responses.
     *
     * @param listResponse The list of ESN API responses
     * @return The ESN field color based on the responses
     */
    public EsnFieldColor getESNFieldColor(final EsnCheckInfo listResponse) {
        if (listResponse.getEsnApiResults() != null) {
            EsnResponse.EsnApiResponse lastResponse = listResponse.getEsnApiResults().get(listResponse
                    .getEsnApiResults().size() - 1);
            if (StringUtils.isNotBlank(lastResponse.getFieldColor())) {
                EsnFieldColor fieldColor = EsnFieldColor.fromColor(lastResponse.getFieldColor());
                LOGGER.info("Returning Esn field color {}", fieldColor);
                return fieldColor;
            }
        }
        LOGGER.info("Returning Esn field color null");
        return null;
    }

    /**
     * Determines the ESN status based on a list of ESN API responses.
     *
     * @param listResponse The list of ESN API responses
     * @return The ESN status based on the responses
     */
    public EsnStatus getESNStatus(final EsnCheckInfo listResponse) {
        EsnStatus esnStatus = EsnStatus.ESN_NA;
        if (listResponse.getUsInsuranceBlackListInfo() != null) {
            esnStatus = getEsnStatusBasedOnBlacklist(listResponse.getUsInsuranceBlackListInfo());
        }

        EsnStatus esnStatusFromTracFone = EsnStatus.ESN_NA;
        if (listResponse.getTracFoneStraightTalkAPIResponse() != null) {
            esnStatusFromTracFone = getEsnStatusBasedOnTracfoneOrStraightWalkCarrier(
                    listResponse.getTracFoneStraightTalkAPIResponse().getTracFoneApiResponse());
        }

        if (listResponse.getEsnApiResults() != null) {
            for (EsnResponse.EsnApiResponse esnApiResponse : listResponse.getEsnApiResults()) {
                if (StringUtils.isNotBlank(esnApiResponse.getRemarks())) {
                    if (StringUtils.containsIgnoreCase(esnApiResponse.getRemarks(), EsnStatus.ESN_BAD.getKey())) {
                        esnStatus = EsnStatus.ESN_BAD;
                        break;
                    }
                    if (StringUtils.containsIgnoreCase(esnApiResponse.getRemarks(), EsnStatus.ESN_GOOD.getKey())
                            || StringUtils.containsIgnoreCase(esnApiResponse.getRemarks(), EsnStatus.ESN_CLEAN.getKey())
                            || StringUtils.containsIgnoreCase(
                            esnApiResponse.getRemarks(), EsnStatus.ESN_NOT_FOUND.getKey())) {
                        esnStatus = EsnStatus.ESN_GOOD;
                    }
                }
            }
        }

        if (esnStatusFromTracFone.equals(EsnStatus.ESN_BAD) || esnStatus.equals(EsnStatus.ESN_BAD)) {
            return EsnStatus.ESN_BAD;
        }
        return esnStatus;
    }

    /**
     * Determines the ESN status based on the blacklist status from the API response.
     *
     * @param esnApiResponse The ESN API response containing the blacklist status.
     * @return The ESN status based on the blacklist status.
     */
    public EsnStatus getEsnStatusBasedOnBlacklist(final EsnResponse.UsInsuranceBlackListInfo esnApiResponse) {
        if (esnApiResponse.getData() != null) {
            String blacklistStatus = esnApiResponse.getData().getBlacklistStatus();
            boolean isBadStatus = BAD_STATUS.stream()
                    .anyMatch(status -> StringUtils.equalsIgnoreCase(blacklistStatus, status));

            boolean isGoodStatus = GOOD_STATUS.stream()
                    .anyMatch(status -> StringUtils.equalsIgnoreCase(blacklistStatus, status));

            if (isBadStatus) {
                return EsnStatus.ESN_BAD;
            }
            if (isGoodStatus || StringUtils.isBlank(blacklistStatus)) {
                return EsnStatus.ESN_GOOD;
            }
        }
        return EsnStatus.ESN_NA;
    }

    /**
     * Determines the ESN status based on the tracfone or straight walk status from the API response.
     *
     * @param tracFoneApiResponse The ESN API response containing the blacklist status.
     * @return The ESN status based on the tracfone or straight walk status.
     */
    public EsnStatus getEsnStatusBasedOnTracfoneOrStraightWalkCarrier(
            final EsnResponse.TracFoneStraightTalkAPIResponse.TracFoneApiResponse tracFoneApiResponse) {

        EsnStatus esnStatus = EsnStatus.ESN_NA;
        if (StringUtils.containsIgnoreCase(tracFoneApiResponse.getOverall(), EsnStatus.ESN_BAD.getKey())) {
            esnStatus = EsnStatus.ESN_BAD;
        } else if (StringUtils.containsIgnoreCase(tracFoneApiResponse.getOverall(), EsnStatus.ESN_GOOD.getKey())
                || StringUtils.containsIgnoreCase(tracFoneApiResponse.getOverall(), EsnStatus.ESN_CLEAN.getKey())
                || StringUtils.containsIgnoreCase(
                tracFoneApiResponse.getOverall(), EsnStatus.ESN_NOT_FOUND.getKey())) {
            esnStatus = EsnStatus.ESN_GOOD;
        }

        return esnStatus;
    }
}