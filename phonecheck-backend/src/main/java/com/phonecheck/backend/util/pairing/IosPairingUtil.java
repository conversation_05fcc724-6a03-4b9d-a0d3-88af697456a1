package com.phonecheck.backend.util.pairing;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.device.results.IosPairDeviceService;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.DeviceReconnectRequireEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceErrorResponseMessage;
import com.phonecheck.model.status.PairStatus;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Component
@AllArgsConstructor
public class IosPairingUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosPairDeviceService.class);
    private final IosPairDeviceService iosPairDeviceService;
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper mapper;
    private final ApplicationContext applicationContext;
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * Check if the device is paired, if yes just return paired and continue processing.
     * If device was unpaired, retry manual pairing from the Trust dialog on the device and then proceed.
     *
     * @param device
     * @return pair status
     */
    public PairStatus checkAndNotifyUiIfNotPaired(final IosDevice device) {
        PairStatus pairStatus = PairStatus.FAILED_UNKNOWN;
        int attempt = 0;
        while (attempt < 10) { // Keep checking device pair status
            attempt++;
            try {
                pairStatus = iosPairDeviceService.validatePair(device);
                if (!PairStatus.PAIRED.equals(pairStatus)) {
                    pairStatus = iosPairDeviceService.pairDevice(device);
                }

                if (List.of(PairStatus.PAIRED, PairStatus.FAILED_NO_DEVICE, PairStatus.FAILED_UNTRUST)
                        .contains(pairStatus)) {
                    // only break if paired, failed because device not found, or user clicks "Don't Trust"
                    break;
                }

            } catch (final IOException e) {
                LOGGER.error("Exception occurred while checking for device paired", e);
            }
            try {
                Thread.sleep(700);
            } catch (final InterruptedException e) {
                // do nothing
            }
        }

        // notify ui for error only if pairing failed and erase isn't in progress
        if (!PairStatus.PAIRED.equals(pairStatus) && !device.isEraseInProgress()) {
            notifyUiForPairing(device);
        }
        return pairStatus;
    }

    /**
     * publish message to the UI for pairing again
     * @param device
     */
    protected void notifyUiForPairing(final Device device) {
        if (!mqttClient.isConnected()) {
            LOGGER.warn("MQTT client was disconnected from the server");
            LOGGER.info("Assigning a new client to the util");
            mqttClient = applicationContext.getBean(IMqttAsyncClient.class);
        }
        LOGGER.info("Notifying UI to pair the device again");
        // we will repurpose the post-error topic since it is already set up to display info in the device box status
        final String topic = TopicBuilder.build(device, "post-error");
        try {
            DeviceErrorResponseMessage deviceErrorResponseMessage = new DeviceErrorResponseMessage();
            deviceErrorResponseMessage.setId(device.getId());
            deviceErrorResponseMessage.setErrorMsg(ErrorConstants.PAIR_DEVICE_AGAIN.getLocalizedKey());

            final MqttMessage message = new MqttMessage();
            message.setPayload(mapper.writeValueAsBytes(deviceErrorResponseMessage));
            mqttClient.publish(topic, message);
        } catch (MqttException | JsonProcessingException e) {
            LOGGER.error("Exception occurred while posting Pairing message to UI", e);
        }
    }

    /**
     * Displays a reconnect popup when the device prepare failed.
     *
     * @param device
     */
    public void showReconnectPopup(final Device device) {
        LOGGER.info("Displaying reconnect popup due to prepare failed");
        device.setReconnectRequired(true);
        eventPublisher.publishEvent(
                new DeviceReconnectRequireEvent(this, device, ErrorConstants.PREPARE_FAILED)
        );
    }
}
