package com.phonecheck.backend.util;

import com.phonecheck.dao.model.*;
import com.phonecheck.model.cloudapi.*;
import com.phonecheck.model.service.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class to map certain service responses to database friendly model objects.
 */
public final class MapperUtil {

    private MapperUtil() {
    }

    public static List<TblCarrierFilter> carrierFilterDbToTblCarrierMapper(final CarrierFilterDb db) {
        final List<TblCarrierFilter> tblCarrierFilters = new ArrayList<>();
        for (CarrierFilterInfoResponse.CarrierFilterInfo info : db.getCarrierFilterInfo()) {
            tblCarrierFilters.add(TblCarrierFilter.builder()
                    .resolvedCarrier(info.getResolvedCarrier())
                    .carrier(info.getCarrier())
                    .id(info.getId())
                    .build());
        }
        return tblCarrierFilters;
    }

    public static List<TblCarrier> carrierInfosToTblCarrierMapper(final CarrierInfoDb carrierInfoDb) {
        final List<TblCarrier> tblCarrierList = new ArrayList<>();
        for (CarrierInfoResponse.CarrierInfo info : carrierInfoDb.getCarrierInfo()) {
            tblCarrierList.add(TblCarrier.builder()
                    .carrier(info.getCarrier())
                    .color(info.getColor())
                    .deviceTitle(info.getDeviceTitle())
                    .rule(info.getRule())
                    .modelNumber(info.getModelNumber())
                    .phoneMemory(info.getPhoneMemory())
                    .id(info.getId())
                    .build());
        }
        return tblCarrierList;
    }

    public static List<TblSimTechnology> simTechnologyDbToSimTechnologyMapper(final SimTechnologyDb db) {
        final List<TblSimTechnology> tblSimTechnologyList = new ArrayList<>();
        for (LatestNetworkInfoResponse.NetworkInfo networkInfo : db.getNetworkInfo()) {
            tblSimTechnologyList.add(TblSimTechnology.builder()
                    .modelId(networkInfo.getModelId())
                    .countryOfOrigin(networkInfo.getCountryOfOrigin())
                    .type(networkInfo.getType())
                    .regulatoryModel(networkInfo.getRegulatoryModel())
                    .model(networkInfo.getModel())
                    .createdOn(networkInfo.getCreatedOn())
                    .updatedOn(networkInfo.getUpdatedOn())
                    .build());
        }
        return tblSimTechnologyList;
    }

    public static List<TblModels> iosModelsToModelNumberMapper(final IosModelDb iosModelsDb) {
        final List<TblModels> tblModels = new ArrayList<>();
        for (IosModelResponse.IosModel iosModel : iosModelsDb.getIosModels()) {
            tblModels.add(TblModels.builder()
                    .productType(iosModel.getProductType())
                    .modelName(iosModel.getModelName())
                    .cpid(iosModel.getCpid())
                    .bdid(iosModel.getBdid())
                    .build());
        }
        return tblModels;
    }

    public static List<TblDeviceColor> androidColorDbtoDeviceColorMapper(final AndroidColorDb db) {
        final List<TblDeviceColor> colorList = new ArrayList<>();
        for (AndroidColorInfoResponse.AndroidColorInfo colorInfo : db.getColorInfoList()) {
            colorList.add(TblDeviceColor.builder()
                    .colorId(colorInfo.getColorId())
                    .color(colorInfo.getColor())
                    .colorCode(colorInfo.getColorCode())
                    .make(colorInfo.getMake())
                    .modelNumber(colorInfo.getModelNumber())
                    .modelName(colorInfo.getModelName()).build());
        }
        return colorList;
    }

    public static List<TblDeviceColor> iosColorDbtoDeviceColorMapper(final IosColorDb db) {
        final List<TblDeviceColor> colorList = new ArrayList<>();
        for (IosColorInfoResponse.IosColorInfo colorInfo : db.getIosColorInfos()) {
            colorList.add(TblDeviceColor.builder()
                    .colorId(colorInfo.getColorId())
                    .color(colorInfo.getColor())
                    .deviceTitle(colorInfo.getDeviceTitle())
                    .imei(colorInfo.getImei())
                    .serialNumber(colorInfo.getSerialNumber())
                    .modelNumber(colorInfo.getModelNumber()).build());
        }
        return colorList;
    }
}
