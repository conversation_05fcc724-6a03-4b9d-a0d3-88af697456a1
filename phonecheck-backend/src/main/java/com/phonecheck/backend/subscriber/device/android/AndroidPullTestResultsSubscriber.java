package com.phonecheck.backend.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.android.AndroidPullTestResultsEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidPullTestResultsRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AndroidPullTestResultsSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidPullTestResultsSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "pull", "results")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Pull test results message received: {}", payload);
        try {
            final AndroidPullTestResultsRequestMessage request = mapper.readValue(payload,
                    AndroidPullTestResultsRequestMessage.class);
            final AndroidDevice device = new AndroidDevice();
            device.setId(request.getId());
            device.setSerial(request.getSerial());
            eventPublisher.publishEvent(new AndroidPullTestResultsEvent(this, device));
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
