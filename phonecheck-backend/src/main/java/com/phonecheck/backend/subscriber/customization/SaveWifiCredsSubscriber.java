package com.phonecheck.backend.subscriber.customization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.SaveWifiCredentialsMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Reacts when a save wifi config message is received from the UI
 */
@Component
@AllArgsConstructor
public class SaveWifiCredsSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(SaveWifiCredsSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final InMemoryStore inMemoryStore;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("wifi", "save")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Save wifi message received: {}", payload);
        try {
            final SaveWifiCredentialsMessage request = mapper.readValue(payload,
                    SaveWifiCredentialsMessage.class);
            inMemoryStore.setWifiConfig(request.getWifiConfig());
            LOGGER.info("Wifi configurations updated");
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }

}
