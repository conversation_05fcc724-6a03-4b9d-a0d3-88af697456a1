package com.phonecheck.backend.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.command.device.android.info.AndroidAtGetProperties1Command;
import com.phonecheck.info.android.AndroidAtDeviceStreamService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidPairingNeededEvent;
import com.phonecheck.model.event.device.android.AndroidPairingSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceConnectionResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@AllArgsConstructor
public class AndroidConnectionResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidConnectionResponseSubscriber.class);
    private static final int CORE_POOL_SIZE = 20;
    private static final int MAX_POOL_SIZE = Integer.MAX_VALUE;
    private static final Long KEEP_ALIVE_TIME = 100L;
    private static final BlockingQueue<Runnable> MESSAGES_QUEUE = new SynchronousQueue<>();
    private static final ThreadPoolExecutor CONNECTION_PROCESSING_THREAD_POOL = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            MESSAGES_QUEUE
    );

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final AndroidAtDeviceStreamService androidAtDeviceStreamService;
    private final ApplicationEventPublisher eventPublisher;
    private final InMemoryStore inMemoryStore;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID,
                "initial", "connection", "response")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Android device connection response status received: {}", payload);

        CONNECTION_PROCESSING_THREAD_POOL.execute(() -> {
            try {
                final DeviceConnectionResponseMessage message =
                        mapper.readValue(payload, DeviceConnectionResponseMessage.class);

                CloudCustomizationResponse cloudCustomization = inMemoryStore.getAssignedCloudCustomization();
                boolean isSetupOnly = cloudCustomization.getWorkflow().getConnectionWorkflow().stream()
                        .anyMatch(CloudCustomizationResponse.AutomationSteps.SETUP::equals);

                final AndroidDevice deviceInTracker =
                        (AndroidDevice) deviceConnectionTracker.getDevice(message.getId());

                // Get authorization status for device
                AuthorizationStatus authorizationStatus = deviceInTracker.getAuthorizationStatus();

                // if ADB mode then check if pairing needed
                if (AndroidConnectionMode.ADB.equals(deviceInTracker.getAndroidConnectionMode())) {
                    if (AuthorizationStatus.AUTHORIZED.equals(authorizationStatus)) {
                        final AndroidPairingSuccessEvent successEvent =
                                new AndroidPairingSuccessEvent(this, deviceInTracker, !isSetupOnly);
                        new Thread(() -> eventPublisher.publishEvent(successEvent)).start();
                    } else {
                        final AndroidPairingNeededEvent neededEvent =
                                new AndroidPairingNeededEvent(this, deviceInTracker, !isSetupOnly);
                        new Thread(() -> eventPublisher.publishEvent(neededEvent)).start();
                    }
                } else {
                    // if AT mode then directly go to info collection
                    androidAtDeviceStreamService.startAtDeviceStreamRoutine(deviceInTracker,
                            new AndroidAtGetProperties1Command(deviceInTracker.getPortName()),
                            !isSetupOnly);
                }

            } catch (JsonProcessingException e) {
                LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
            }
        });
    }
}
