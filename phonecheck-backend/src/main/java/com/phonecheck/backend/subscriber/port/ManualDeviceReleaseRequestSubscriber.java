package com.phonecheck.backend.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.android.AndroidDisconnectedEvent;
import com.phonecheck.model.event.device.ios.IosDisconnectedEvent;
import com.phonecheck.model.event.device.iwatch.IWatchUnpairEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.ManualReleaseDeviceMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when a device needs to be manually released
 */
@Component
@AllArgsConstructor
public class ManualDeviceReleaseRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualDeviceReleaseRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;
    private final InMemoryStore inMemoryStore;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "release", "device"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "release", "device")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Manual release request received: {}", payload);

        try {
            final ManualReleaseDeviceMessage request = mapper.readValue(payload,
                    ManualReleaseDeviceMessage.class);

            if (DeviceConnectionMode.IWATCH_HOST == inMemoryStore.getDeviceConnectionMode()) {
                IosDevice device = new IosDevice();
                device.setId(request.getId());
                eventPublisher.publishEvent(new IWatchUnpairEvent(this, device));
            } else {
                if (StringUtils.containsIgnoreCase(msg.getTopic(), DeviceFamily.ANDROID.name())) {
                    AndroidDevice device = new AndroidDevice();
                    device.setId(request.getId());
                    AndroidDisconnectedEvent event = new AndroidDisconnectedEvent(this, device);
                    event.setManualRelease(true);
                    eventPublisher.publishEvent(event);
                } else {
                    IosDevice device = new IosDevice();
                    device.setId(request.getId());
                    IosDisconnectedEvent event = new IosDisconnectedEvent(this, device);
                    event.setManualRelease(true);
                    eventPublisher.publishEvent(event);
                }
            }

        } catch (JsonProcessingException e) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}

