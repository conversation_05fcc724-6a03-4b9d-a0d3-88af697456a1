package com.phonecheck.backend.subscriber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.login.StartDevicesThreadEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.StartDeviceProcessingMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class StartDevicesProcessingSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(StartDevicesProcessingSubscriber.class);
    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("start", "processing")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Start devices processing message received: {}", payload);
        try {
            // Make sure it is start devices processing message
            final StartDeviceProcessingMessage request = mapper.readValue(payload,
                    StartDeviceProcessingMessage.class);
            eventPublisher.publishEvent(new StartDevicesThreadEvent(this, request.getDeviceConnectionMode()));
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }

    }
}
