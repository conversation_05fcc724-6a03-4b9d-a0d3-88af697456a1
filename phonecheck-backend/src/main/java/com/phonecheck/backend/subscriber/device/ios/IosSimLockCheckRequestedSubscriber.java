package com.phonecheck.backend.subscriber.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.DeviceSimLockCheckEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosSimLockCheckRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class IosSimLockCheckRequestedSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosSimLockCheckRequestedSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "simlock-check", "request")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Sim-lock check request received: {}", payload);
        try {
            final IosSimLockCheckRequestMessage request = mapper.readValue(payload,
                    IosSimLockCheckRequestMessage.class);

            LOGGER.debug("SimLock check customization is enabled");
            new Thread(() -> eventPublisher.publishEvent(
                    new DeviceSimLockCheckEvent(this, request.getDevice()))).start();
        } catch (Exception e) {
            LOGGER.info("Error while parsing payload for SimLock check request", e);
        }
    }
}
