package com.phonecheck.backend.subscriber.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.DeviceCarrierCheckEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceCarrierLockReqMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DeviceCarrierCheckRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceCarrierCheckRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "carrier-simlock", "request"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "carrier-simlock", "request")};
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Carrier Lock check request received: {}", payload);
        try {
            final DeviceCarrierLockReqMessage request = mapper.readValue(payload,
                    DeviceCarrierLockReqMessage.class);

            new Thread(() -> eventPublisher.publishEvent(
                    new DeviceCarrierCheckEvent(this, request.getDevice()))).start();
        } catch (Exception e) {
            LOGGER.info("Error while parsing payload for SimLock check request", e);
        }

    }
}
