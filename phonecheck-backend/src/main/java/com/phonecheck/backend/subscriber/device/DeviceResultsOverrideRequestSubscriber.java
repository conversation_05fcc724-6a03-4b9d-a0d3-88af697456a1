package com.phonecheck.backend.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.DeviceResultsOverrideRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceResultsOverrideRequestMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when a manual test results override request is made
 */
@Component
@AllArgsConstructor
public class DeviceResultsOverrideRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceResultsOverrideRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "results-override", "request"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "results-override", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Device manual test results override request received: {}", payload);
        try {
            final DeviceResultsOverrideRequestMessage request = mapper.readValue(payload,
                    DeviceResultsOverrideRequestMessage.class);
            final Device device;
            if (msg.getTopic().contains("android")) {
                device = new AndroidDevice();
            } else {
                device = new IosDevice();
                ((IosDevice) device).setTouchIdSensor(request.getTouchIdStatus());
                ((IosDevice) device).setFaceIdSensor(request.getFaceIdStatus());
                ((IosDevice) device).setFaceIdManuallyChanged(request.isFaceIdManuallyChanged());
                ((IosDevice) device).setTouchIdManuallyChanged(request.isTouchIdManuallyChanged());
            }

            device.setId(request.getId());
            device.setSerial(request.getSerial());
            device.setDeviceTestResult(request.getDeviceTestResult());
            boolean shouldStartTestResultAutomation = request.isShouldStartTestResultAutomation();

            // Publish event to manually override test results for the device
            final DeviceResultsOverrideRequestEvent event = new DeviceResultsOverrideRequestEvent(this,
                    device, shouldStartTestResultAutomation);
            eventPublisher.publishEvent(event);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
