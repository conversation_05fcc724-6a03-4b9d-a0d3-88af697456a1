package com.phonecheck.backend.subscriber.device.iwatch;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.iwatch.IWatchGetBatteryHealthInfoEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.iwatch.IWatchGetBatteryInfoRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * This subscriber subscribes to the IWatchGetBatteryInfoRequestMessage coming from the UI
 */
@Component
@AllArgsConstructor
public class IWatchGetBatteryInfoRequestSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchGetBatteryInfoRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "iwatch", "battery-info", "requested")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.info("Switch to iWatch mode request received: {}", payload);
        try {
            final IWatchGetBatteryInfoRequestMessage request = mapper.readValue(payload,
                    IWatchGetBatteryInfoRequestMessage.class);
            final IosDevice iosDevice = new IosDevice();
            iosDevice.setId(request.getId());
            iosDevice.setIWatchInstallBatteryProfileStatus(null);
            eventPublisher.publishEvent(new IWatchGetBatteryHealthInfoEvent(this, iosDevice, true));
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}

