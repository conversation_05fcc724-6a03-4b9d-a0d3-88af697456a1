package com.phonecheck.backend.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.android.AndroidAppInstallEvent;
import com.phonecheck.model.event.device.android.AndroidPcUtilityInstallEvent;
import com.phonecheck.model.event.device.ios.IosAppInstallEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.AppInstallMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when device app install request is raised
 */
@Component
@AllArgsConstructor
public class ManualAppInstallRequestSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManualAppInstallRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "app-install", "request"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "app-install", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Device app install request received: {}", payload);
        try {
            final AppInstallMessage request = mapper.readValue(payload, AppInstallMessage.class);

            final Device device;
            if (msg.getTopic().contains("android")) {
                device = new AndroidDevice();
            } else {
                device = new IosDevice();
            }
            device.setId(request.getId());
            device.setSerial(request.getSerial());

            new Thread(() -> {
                // Publish event to initiate app installation on the device
                if (device instanceof IosDevice iosDevice) {
                    final IosAppInstallEvent iosAppInstallEvent = new IosAppInstallEvent(this, iosDevice,
                            request.isManualInstall());
                    eventPublisher.publishEvent(iosAppInstallEvent);
                } else if (device instanceof AndroidDevice androidDevice) {
                    final AndroidPcUtilityInstallEvent androidPcUtilityInstallEvent
                            = new AndroidPcUtilityInstallEvent(this, androidDevice);
                    eventPublisher.publishEvent(androidPcUtilityInstallEvent);
                    final AndroidAppInstallEvent androidAppInstallEvent =
                            new AndroidAppInstallEvent(this, androidDevice,
                                    request.isManualInstall());
                    eventPublisher.publishEvent(androidAppInstallEvent);
                }
            }).start();

        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
