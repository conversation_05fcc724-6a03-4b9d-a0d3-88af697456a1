package com.phonecheck.backend.subscriber.device.ios.precheck;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.ios.precheck.PreCheckSemiResultManualFailedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.precheck.PreCheckManualFailedSemiTestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when manual face id status is raised
 */
@Component
@AllArgsConstructor
public class PreCheckSemiResultsManualFailedSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreCheckSemiResultsManualFailedSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS,
                "pre-check", "manual-failed", "semi-test")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Device manual failed semi test status received: {}", payload);
        try {
            final PreCheckManualFailedSemiTestMessage request = mapper.readValue(payload,
                    PreCheckManualFailedSemiTestMessage.class);

            final IosDevice device = new IosDevice();
            device.setId(request.getId());
            final PreCheckSemiResultManualFailedEvent event =
                    new PreCheckSemiResultManualFailedEvent(this, device, request.getSemiResults());
            eventPublisher.publishEvent(event);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }

    }

}
