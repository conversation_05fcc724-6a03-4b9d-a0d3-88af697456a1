package com.phonecheck.backend.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.DeviceCarrierChangeEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceCarrierChangeMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to UI request to change device carrier
 */
@Component
@AllArgsConstructor
public class DeviceCarrierChangeSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceCarrierChangeSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS, "carrier", "change"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "carrier", "change")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Carrier change request received: {}", payload);
        try {
            final DeviceCarrierChangeMessage requestMessage = mapper.readValue(payload,
                    DeviceCarrierChangeMessage.class);

            final IosDevice device = new IosDevice();
            device.setId(requestMessage.getId());
            device.setCarrier(requestMessage.getCarrier());

            // Publish event to change device carrier
            final DeviceCarrierChangeEvent deviceCarrierChangeEvent = new DeviceCarrierChangeEvent(this, device);
            eventPublisher.publishEvent(deviceCarrierChangeEvent);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
