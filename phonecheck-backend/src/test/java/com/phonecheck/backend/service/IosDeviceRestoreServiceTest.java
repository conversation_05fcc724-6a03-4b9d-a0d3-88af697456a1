package com.phonecheck.backend.service;

import com.phonecheck.device.connection.ios.IosDeviceConnectionService;
import com.phonecheck.device.connection.ios.IosDeviceRestoreService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.FirmwareDownloadRequestEvent;
import com.phonecheck.model.event.device.DeviceConnectionAutomationEvent;
import com.phonecheck.model.event.device.ios.IosDeviceRestoreResponseEvent;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.status.RestoreResponseCode;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.FileUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosDeviceRestoreServiceTest {

    private IosDeviceRestoreService iosDeviceRestoreService;

    @Mock
    private CommandExecutor commandExecutor;

    @Mock
    private IosDeviceConnectionService iosDeviceConnectionService;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private IosDeviceInfoService iosDeviceInfoService;

    @Mock
    private FileUtil fileUtil;
    @Mock
    private InMemoryStore inMemoryStore;


    @BeforeEach
    void beforeEach() {
        iosDeviceRestoreService = new IosDeviceRestoreService(commandExecutor,
                iosDeviceConnectionService,
                eventPublisher,
                iosDeviceInfoService,
                fileUtil,
                inMemoryStore
        );
    }

    @Test
    public void testPushDeviceToRecoveryMode() throws Exception {
        IosDevice device = new IosDevice();
        device.setId("TestID");
        device.setSerial("F17QGZGCGRY5");
        device.setEcid("7997685391949854");

        IosDevice dfuDevice = new IosDevice();
        dfuDevice.setId("DFUModeTestID1");
        dfuDevice.setSerial("F17QGZGCGRY5");
        dfuDevice.setEcid("7997685391949854");

        when(iosDeviceConnectionService.getDevicesInDfuMode()).thenReturn(Set.of(dfuDevice));

        IosDevice outputDevice = iosDeviceRestoreService.pushDeviceToRecoveryMode(device.getId(), device.getSerial(),
                device.getEcid());

        assertNotNull(outputDevice);
        Assertions.assertEquals("DFUModeTestID1", outputDevice.getId());
        Assertions.assertEquals("F17QGZGCGRY5", outputDevice.getSerial());

        verify(iosDeviceConnectionService).getDevicesInDfuMode();

    }

    @Test
    public void testGetDevicesRecovered() throws Exception {
        IosDevice deviceWithEcid = new IosDevice();
        deviceWithEcid.setId("TestID1");
        deviceWithEcid.setEcid("7997685391949854");
        when(iosDeviceConnectionService.getAllConnectedDevicesByEcid()).thenReturn(Set.of(deviceWithEcid));
        IosDevice device = iosDeviceRestoreService.getDeviceByEcidAfterRestore("7997685391949854");
        assertNotNull(device);
        Assertions.assertEquals("TestID1", device.getId());
        Assertions.assertEquals("7997685391949854", device.getEcid());
    }

    @Test
    public void testGetDeviceInRecovery() throws IOException {
        Set<IosDevice> dfuDevices = new HashSet<>();
        IosDevice device = new IosDevice();
        device.setId("testId");
        device.setSerial("TestSerial");
        device.setEcid("7997685391949854");
        dfuDevices.add(device);
        when(iosDeviceConnectionService.getDevicesInDfuMode()).thenReturn(dfuDevices);
        IosDevice outputDevice = iosDeviceRestoreService.getDeviceInRecovery("TestSerial", "7997685391949854");
        verify(iosDeviceConnectionService).getDevicesInDfuMode();
        Assertions.assertEquals("TestSerial", outputDevice.getSerial());
        Assertions.assertEquals("7997685391949854", outputDevice.getEcid());
    }

    @Test
    public void testPublishRestoreResponseEvent() {
        IosDevice device = new IosDevice();
        device.setId("testId");
        device.setSerial("TestSerial");

        iosDeviceRestoreService.publishRestoreResponseEvent(device,
                DeviceRestoreStatus.RESTORE_IN_PROGRESS, RestoreResponseCode.RESTORE_STARTED);

        Mockito.verify(eventPublisher).publishEvent(argThat((IosDeviceRestoreResponseEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(DeviceRestoreStatus.RESTORE_IN_PROGRESS, arg.getRestoreStatus());
            Assertions.assertEquals(RestoreResponseCode.RESTORE_STARTED, arg.getResponseCode());
            return true;
        }));
    }

    @Test
    public void testGetFirmwareByProductType() {
        Map<String, FirmwareModel.FirmwareResponse> firmwareResponseMap = new HashMap<>();
        FirmwareModel.FirmwareResponse response = new FirmwareModel.FirmwareResponse();
        response.setFileName("TestFile");
        response.setName("Test model");
        response.setId("Test Product type");

        FirmwareModel firmwareResponse = FirmwareModel.builder()
                .latestFirmwareInfo(new FirmwareModel.FirmwareResponse[]{
                        response}).build();

        firmwareResponseMap.put("Test Product type", firmwareResponse.getLatestFirmwareInfo()[0]);

        when(inMemoryStore.getFirmwareModels()).thenReturn(firmwareResponseMap);

        FirmwareModel.FirmwareResponse outputRes = iosDeviceRestoreService.
                getFirmwareByProductType("Test Product type");

        verify(inMemoryStore).getFirmwareModels();
        Assertions.assertEquals("TestFile", outputRes.getFileName());
    }

    @Test
    public void testPublishFirmwareDownloadEvent() {
        IosDevice device = new IosDevice();
        device.setId("testId");
        device.setSerial("TestSerial");

        iosDeviceRestoreService.publishFirmwareDownloadEvent(device, "Test File");

        Mockito.verify(eventPublisher).publishEvent(argThat((FirmwareDownloadRequestEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals("Test File", arg.getFirmwareId());
            return true;
        }));
    }

    @Test
    public void testShouldConnectionAutomationRunningAfterRestore() {
        IosDevice device = new IosDevice();
        device.setId("testId");
        device.setCurrentRunningAutomation(AutomationWorkflow.CONNECTION);
        iosDeviceRestoreService.publishConnectionAutomationAfterRestore(device);
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(DeviceConnectionAutomationEvent.class));
    }

    @Test
    public void testConnectionAutomationNotRunningAfterRestore() {
        IosDevice device = new IosDevice();
        device.setId("testId");
        device.setCurrentRunningAutomation(null);
        iosDeviceRestoreService.publishConnectionAutomationAfterRestore(device);
        Mockito.verify(eventPublisher, never()).publishEvent(Mockito.any(DeviceConnectionAutomationEvent.class));
    }
}
