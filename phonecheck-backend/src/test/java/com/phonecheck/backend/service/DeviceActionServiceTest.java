package com.phonecheck.backend.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.app.android.AndroidAppService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.constants.android.AndroidAppPackageConstants;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.DevicePowerOffRequestEvent;
import com.phonecheck.model.event.device.android.AndroidEraseRequestEvent;
import com.phonecheck.model.event.device.android.AndroidUnlockWithCodeRequestEvent;
import com.phonecheck.model.event.device.ios.IosDeviceAutoRestoreEvent;
import com.phonecheck.model.event.device.ios.IosEraseRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.status.RootedStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeviceActionServiceTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private AndroidAppService androidAppService;
    @Mock
    private ApplicationContext applicationContext;
    private DeviceActionService deviceActionService;

    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        deviceActionService = new DeviceActionService(mqttClient, mapper, androidAppService, eventPublisher,
                applicationContext);
    }

    @Test
    public void testIosEraseOperationRequest() {
        Device device = new IosDevice(); // For example, an iOS device
        // Define the behavior of your eventPublisher mock

        deviceActionService.eraseOperationRequest(device);

        // Verify that eventPublisher.publishEvent was called with the correct event
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(IosEraseRequestEvent.class));
    }

    @Test
    public void testAndroidEraseOperationRequest() {
        AndroidDevice device = new AndroidDevice();
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);

        deviceActionService.eraseOperationRequest(device);

        Mockito.verify(androidAppService).getInstalledAppAndroid(any(),
                eq(AndroidAppPackageConstants.PC_UTILITY_PACKAGE_LITE));
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidEraseRequestEvent.class));
    }

    @Test
    public void testPowerOffOperationRequest() {
        Device device = new IosDevice(); // Create a device

        // Define the behavior of your eventPublisher mock

        deviceActionService.powerOffOperationRequest(device);

        // Verify that eventPublisher.publishEvent was called with the correct event
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(DevicePowerOffRequestEvent.class));
    }

    @Test
    public void testScanCustom1OperationRequestWhenCustom1IsBlank() throws MqttException {
        //Create new Device
        Device device = new IosDevice();
        //Set Device Id
        device.setId("id");
        when(mqttClient.isConnected()).thenReturn(true);
        //Calling
        deviceActionService.scanCustom1OperationRequest(device);
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.build(device, "scan-custom1", "request")),
                any(MqttMessage.class));
    }

    @Test
    public void testScanLpnOperationRequestWhenLpnIsBlank() throws MqttException {
        //Create new Device
        Device device = new IosDevice();
        //Set Device Id
        device.setId("id");
        when(mqttClient.isConnected()).thenReturn(true);
        deviceActionService.scanLpnOperationRequest(device);
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.build(device, "scan-lpn", "request")),
                any(MqttMessage.class));
    }

    @Test
    public void testRestoreOperationRequest() {
        //Create new Device
        Device device = new IosDevice();
        device.setId("id");

        deviceActionService.restoreOperationRequest(device);
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(IosDeviceAutoRestoreEvent.class));
    }

    @Test
    public void testRestoreJailBrokenDeviceOperationRequest() {
        //Create new Device
        Device device = new IosDevice();
        device.setId("id");
        device.setRooted(RootedStatus.ROOTED);

        deviceActionService.restoreOperationRequest(device);
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(IosDeviceAutoRestoreEvent.class));
    }

    @Test
    public void testRestoreIosBetaDeviceOperationRequest() {
        //Create new Device
        IosDevice device = new IosDevice();
        device.setId("id");
        device.setReleaseType("Beta");

        deviceActionService.restoreOperationRequest(device);
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(IosDeviceAutoRestoreEvent.class));
    }

    @Test
    public void testImeiSerialValidationRequestInitiated() throws MqttException {
        Device device = new IosDevice();
        device.setId("id");
        when(mqttClient.isConnected()).thenReturn(true);
        deviceActionService.initiateValidateRequest(device);
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.build(device,
                        "validate-imei-serial", "request")),
                any(MqttMessage.class));
    }

    @Test
    public void testAndroidNetworkUnlockRequest() {
        AndroidDevice device = new AndroidDevice();
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);
        device.setImei("123456789009876");

        deviceActionService.androidUnlockWithCodeRequest(device);
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidUnlockWithCodeRequestEvent.class));
    }

    @Test
    public void testAndroidNetworkUnlockRequestNoEvent() {
        AndroidDevice device = new AndroidDevice();
        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);
        device.setImei("");

        deviceActionService.androidUnlockWithCodeRequest(device);
        Mockito.verify(eventPublisher, never()).publishEvent(Mockito.any(AndroidUnlockWithCodeRequestEvent.class));
    }
}
