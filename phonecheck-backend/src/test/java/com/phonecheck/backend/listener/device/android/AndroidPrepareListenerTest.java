package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.DeviceActionService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.android.AndroidAtDeviceStreamService;
import com.phonecheck.model.android.AndroidAtStreamKey;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.AtPort;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceConnectionAutomationEvent;
import com.phonecheck.model.event.device.android.AndroidAtPrepareStreamParseEvent;
import com.phonecheck.model.event.device.android.AndroidPrepareEvent;
import com.phonecheck.model.mqtt.messages.android.AndroidPrepareResponseMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.peo.android.AndroidPrepareService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidPrepareListenerTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private AndroidPrepareService androidPrepareService;
    @Mock
    private AndroidAtDeviceStreamService androidAtDeviceStreamService;
    @Mock
    private DeviceActionService deviceActionService;
    @Mock
    private CommandExecutor executor;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidPrepareListener listener;
    private AndroidDevice testDevice;

    @BeforeEach
    void setUp() {
        listener = new AndroidPrepareListener(
                objectMapper, mqttClient, eventPublisher, deviceConnectionTracker,
                androidPrepareService, androidAtDeviceStreamService, deviceActionService,
                executor
        );

        AtPort atPort = new AtPort();
        AtPort.Port port = new AtPort.Port();
        port.setSerialNumber("123");
        atPort.setAPort(port);

        testDevice = new AndroidDevice();
        testDevice.setId("test-device-123");
        testDevice.setAtPort(atPort);
    }

    @Test
    void testOnEventDeviceNotInTracker() {
        when(deviceConnectionTracker.getDevice(anyString())).thenReturn(null);

        AndroidPrepareEvent event = new AndroidPrepareEvent(this, testDevice);
        listener.onEvent(event);

        verify(deviceConnectionTracker).getDevice(testDevice.getId());
        verifyNoMoreInteractions(androidPrepareService, androidAtDeviceStreamService, eventPublisher);
    }

    @Test
    void testOnEventNotifyStreamTestManuOpened() {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidPrepareEvent event = new AndroidPrepareEvent(this, testDevice);
        event.setNotifyStreamThatTestManuOpened(true);

        listener.onEvent(event);

        verify(androidAtDeviceStreamService).writeAtDeviceStream(testDevice, "y");
        verifyNoMoreInteractions(androidPrepareService);
    }

    @Test
    void testOnEventAtModeWithExploitSupport() throws Exception {
        when(mqttClient.isConnected()).thenReturn(true);
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        testDevice.setAndroidConnectionMode(AndroidConnectionMode.AT);
        testDevice.setExploitSupported(true);

        AndroidPrepareEvent event = new AndroidPrepareEvent(this, testDevice);
        listener.onEvent(event);

        assertEquals(DeviceStage.PREPARE_DEVICE_IN_PROGRESS, testDevice.getStage());
        verify(androidPrepareService).startPreparingDevice(testDevice);

        // Verify MQTT message
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<MqttMessage> messageCaptor = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient).publish(topicCaptor.capture(), messageCaptor.capture());

        AndroidPrepareResponseMessage response = objectMapper.readValue(
                messageCaptor.getValue().getPayload(),
                AndroidPrepareResponseMessage.class
        );
        assertEquals(testDevice.getId(), response.getId());
        assertEquals(DeviceStage.PREPARE_DEVICE_IN_PROGRESS, response.getDeviceStage());
    }

    @Test
    void testOnEventAdbModeWithSuccessfulPreparation() throws Exception {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);
        when(androidPrepareService.waitForAuthorizationAndPrepareDevice(testDevice)).thenReturn(true);

        testDevice.setAndroidConnectionMode(AndroidConnectionMode.ADB);
        testDevice.setCurrentRunningAutomation(AutomationWorkflow.CONNECTION);

        AndroidPrepareEvent event = new AndroidPrepareEvent(this, testDevice);
        listener.onEvent(event);

        assertEquals(AuthorizationStatus.UNAUTHORIZED, testDevice.getAuthorizationStatus());
        verify(eventPublisher).publishEvent(any(DeviceConnectionAutomationEvent.class));
    }

    @Test
    void testOnStreamParseEventPortUpdate() throws Exception {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtPrepareStreamParseEvent event = new AndroidAtPrepareStreamParseEvent(
                this,
                testDevice,
                AndroidAtStreamKey.PORT_UPDATED_KEY,
                "PORT: COM3",
                "com.phonecheck.command.TestCommand"
        );

        listener.onEvent(event);

        assertEquals("COM3", testDevice.getPortName());
        assertEquals("COM3", testDevice.getAtPort().getAPort().getPortName());
    }

    @Test
    void testOnStreamParseEventManuallyOpenTestMenu() throws Exception {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtPrepareStreamParseEvent event = new AndroidAtPrepareStreamParseEvent(
                this,
                testDevice,
                AndroidAtStreamKey.MANUALLY_OPEN_TEST_MENU_KEY,
                "",
                "com.phonecheck.command.TestCommand"
        );

        listener.onEvent(event);

        verify(deviceActionService).emergencyDialerRequest(testDevice);
    }

    @Test
    void testOnStreamParseEventAlreadyPrepared() throws Exception {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtPrepareStreamParseEvent event = new AndroidAtPrepareStreamParseEvent(
                this,
                testDevice,
                AndroidAtStreamKey.ALREADY_PREPARED_KEY,
                "",
                "com.phonecheck.command.TestCommand"
        );

        listener.onEvent(event);

        verify(androidAtDeviceStreamService).writeAtDeviceStream(testDevice, "y");
    }

    @Test
    void testOnStreamParseEventRebootingForExploit() throws IOException {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtPrepareStreamParseEvent event = new AndroidAtPrepareStreamParseEvent(
                this,
                testDevice,
                AndroidAtStreamKey.REBOOTING_FOR_EXPLOIT_KEY,
                "",
                "com.phonecheck.command.TestCommand"
        );

        listener.onEvent(event);

        assertTrue(testDevice.isRestartingForUnlockingOrPrepare());
    }

    @Test
    void testOnStreamParseEventError() throws Exception {
        when(mqttClient.isConnected()).thenReturn(true);
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtPrepareStreamParseEvent event = new AndroidAtPrepareStreamParseEvent(
                this,
                testDevice,
                AndroidAtStreamKey.ERROR_KEY,
                "Some error occurred",
                "com.phonecheck.command.TestCommand"
        );

        listener.onEvent(event);

        assertFalse(testDevice.isRestartingForUnlockingOrPrepare());
        assertEquals(DeviceStage.PREPARE_DEVICE_FAILURE, testDevice.getStage());

        // Verify MQTT message
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<MqttMessage> messageCaptor = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient).publish(topicCaptor.capture(), messageCaptor.capture());

        AndroidPrepareResponseMessage response = objectMapper.readValue(
                messageCaptor.getValue().getPayload(),
                AndroidPrepareResponseMessage.class
        );
        assertEquals(testDevice.getId(), response.getId());
        assertEquals(DeviceStage.PREPARE_DEVICE_FAILURE, response.getDeviceStage());
    }
}