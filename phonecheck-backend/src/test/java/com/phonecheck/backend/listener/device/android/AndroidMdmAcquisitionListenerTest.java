package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.info.android.AndroidMdmInfoService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.stage.AndroidMdmStatusSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidMdmAcquisitionEvent;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.mqtt.messages.android.AndroidMdmStatusInfoMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AndroidMdmAcquisitionListenerTest {
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;

    @Mock
    private AndroidMdmInfoService androidMdmInfoService;

    @Mock
    private DeviceStageUpdater stageUpdater;

    @Mock
    private InMemoryStore inMemoryStore;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private IMqttAsyncClient mqttClient;

    private AndroidMdmAcquisitionListener mdmAcquisitionListener;

    @BeforeEach
    void setUp() {
        mdmAcquisitionListener = new AndroidMdmAcquisitionListener(
                objectMapper,
                mqttClient,
                inMemoryStore,
                stageUpdater,
                deviceConnectionTracker,
                androidMdmInfoService
        );
        when(mqttClient.isConnected()).thenReturn(true);
    }

    @Test
    public void testOnEvent() throws IOException, MqttException {
        AndroidDevice device = new AndroidDevice();
        device.setId("testDeviceId");

        AndroidDevice deviceInTracker = new AndroidDevice();
        deviceInTracker.setId("testDeviceId");

        Transaction transaction = new Transaction();
        transaction.setTransactionId(123);
        when(inMemoryStore.getTransaction()).thenReturn(transaction);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(deviceInTracker);

        MdmStatus mdmStatus = MdmStatus.ON;
        when(androidMdmInfoService.getMdmStatus(deviceInTracker.getId())).thenReturn(mdmStatus);

        AndroidMdmAcquisitionEvent event = new AndroidMdmAcquisitionEvent(mdmAcquisitionListener, device);

        mdmAcquisitionListener.onEvent(event);

        verify(stageUpdater).updateStage(any(AndroidMdmStatusSuccessStage.class));
        Mockito.verify(mqttClient).publish(anyString(), argThat(mqttMessage -> {
            try {
                AndroidMdmStatusInfoMessage message =
                        objectMapper.readValue(new String(mqttMessage.getPayload()),
                                AndroidMdmStatusInfoMessage.class);
                Assertions.assertEquals(device.getId(), message.getId());
                Assertions.assertEquals(mdmStatus, message.getMdmStatus());
            } catch (JsonProcessingException e) {
                return false;
            }
            return true;
        }));
    }


}