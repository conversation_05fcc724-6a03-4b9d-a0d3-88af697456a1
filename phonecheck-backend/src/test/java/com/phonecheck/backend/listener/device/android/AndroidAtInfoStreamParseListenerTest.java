package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.service.DeviceActionService;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.command.device.android.info.AndroidAtGetProperties1Command;
import com.phonecheck.command.device.android.info.AndroidAtGetProperties2Command;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.info.android.AndroidAtDeviceStreamService;
import com.phonecheck.model.android.AndroidAtStreamKey;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.stage.FrpInfoSuccessStage;
import com.phonecheck.model.device.stage.KnoxInfoSuccessStage;
import com.phonecheck.model.device.stage.RootInfoSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidAtInfoStreamParseEvent;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidReadyStatusMessage;
import com.phonecheck.model.status.KnoxStatus;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidAtInfoStreamParseListenerTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private AndroidAtDeviceStreamService androidAtDeviceStreamService;
    @Mock
    private DeviceInfoDBService deviceInfoDBService;
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private DeviceActionService deviceActionService;
    @Mock
    private DeviceAutomationQueueService automationQueueService;
    @Mock
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidAtInfoStreamParseListener listener;
    private AndroidDevice testDevice;

    @BeforeEach
    void setUp() {
        listener = new AndroidAtInfoStreamParseListener(
                objectMapper, mqttClient, eventPublisher, deviceConnectionTracker,
                androidAtDeviceStreamService, deviceInfoDBService, stageUpdater,
                inMemoryStore, deviceActionService, automationQueueService,
                cloudDeviceDataSyncService
        );

        testDevice = new AndroidDevice();
        testDevice.setId("test-device-123");
    }

    @Test
    void testOnEventDeviceNotInTracker() {
        when(deviceConnectionTracker.getDevice(anyString())).thenReturn(null);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.DEVICE_INFO_KEY, "test-data",
                AndroidAtGetProperties1Command.class.getName()
        );

        listener.onEvent(event);

        verify(deviceConnectionTracker).getDevice(testDevice.getId());
        // No further processing should occur
        verifyNoMoreInteractions(eventPublisher, stageUpdater, mqttClient);
    }

    @Test
    void testOnEventHandleFrpInfoKey() throws Exception {
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.FRP_INFO_KEY, "LOCK",
                AndroidAtGetProperties1Command.class.getName()
        );

        listener.onEvent(event);

        // Verify device lock status was updated
        assertEquals(DeviceLock.ON, testDevice.getDeviceLock());

        // Verify stage update
        verify(stageUpdater).updateStage(argThat(stage -> {
            FrpInfoSuccessStage frpStage = (FrpInfoSuccessStage) stage;
            return frpStage.getId().equals(testDevice.getId()) &&
                    frpStage.getDeviceLockStatus().equals(DeviceLock.ON.getKey());
        }));

        // Verify MQTT message
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<MqttMessage> messageCaptor = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient).publish(topicCaptor.capture(), messageCaptor.capture());

        assertEquals(TopicBuilder.build(testDevice, "frp", "response"), topicCaptor.getValue());
    }

    @Test
    void testOnEventHandleRootInfoKey() throws Exception {
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.ROOT_INFO_KEY, "YES",
                AndroidAtGetProperties1Command.class.getName()
        );

        listener.onEvent(event);

        // Verify root status was updated
        assertEquals(RootedStatus.ROOTED, testDevice.getRooted());

        // Verify stage update
        verify(stageUpdater).updateStage(argThat(stage -> {
            RootInfoSuccessStage rootStage = (RootInfoSuccessStage) stage;
            return rootStage.getId().equals(testDevice.getId()) &&
                    rootStage.getRooted() == RootedStatus.ROOTED;
        }));

        // Verify MQTT message
        verify(mqttClient).publish(
                eq(TopicBuilder.build(testDevice, "root-info", "response")),
                any(MqttMessage.class)
        );
    }

    @Test
    void testOnEventHandleKnoxInfoKey() throws Exception {
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.KNOX_INFO_KEY, "ON",
                AndroidAtGetProperties1Command.class.getName()
        );

        listener.onEvent(event);

        // Verify Knox status was updated
        assertEquals(KnoxStatus.KNOX_ON, testDevice.getKnox());

        // Verify stage update
        verify(stageUpdater).updateStage(argThat(stage -> {
            KnoxInfoSuccessStage knoxStage = (KnoxInfoSuccessStage) stage;
            return knoxStage.getId().equals(testDevice.getId()) &&
                    knoxStage.getKnoxStatus().equals(KnoxStatus.KNOX_ON.getKey());
        }));

        // Verify MQTT message
        verify(mqttClient).publish(
                eq(TopicBuilder.build(testDevice, "knox", "response")),
                any(MqttMessage.class)
        );
    }

    @Test
    void testOnEventHandleBatteryInfoKey() {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.BATTERY_INFO_KEY, "85",
                AndroidAtGetProperties2Command.class.getName()
        );

        listener.onEvent(event);

        // Verify battery info was updated
        assertNotNull(testDevice.getBatteryInfo());
        assertEquals(85, testDevice.getBatteryInfo().getHealthPercentage());
        assertEquals(85, testDevice.getBatteryStateHealth());

        // Verify battery info success event was published
        verify(eventPublisher).publishEvent(any(AndroidBatteryInfoSuccessEvent.class));
    }

    @Test
    void testOnEventHandleExecutionCompletedKeyProperties1Command() {
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.EXECUTION_COMPLETED_KEY, "",
                AndroidAtGetProperties1Command.class.getName()
        );
        testDevice.setImei("123456789");

        listener.onEvent(event);

        // Verify next command is started
        verify(androidAtDeviceStreamService).startAtDeviceStreamRoutine(
                eq(testDevice),
                any(AndroidAtGetProperties2Command.class),
                anyBoolean()
        );
    }

    @Test
    void testOnEventHandleExecutionCompletedKeyProperties2Command() throws Exception {
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().build());
        when(deviceConnectionTracker.getDevice(testDevice.getId())).thenReturn(testDevice);

        AndroidAtInfoStreamParseEvent event = new AndroidAtInfoStreamParseEvent(
                this, testDevice, AndroidAtStreamKey.EXECUTION_COMPLETED_KEY, "",
                AndroidAtGetProperties2Command.class.getName()
        );

        listener.onEvent(event);

        // Verify cloud sync
        verify(cloudDeviceDataSyncService).addDeviceToDataSyncSet(testDevice);
        verify(cloudDeviceDataSyncService).syncDeviceRecordOnCloud(any(), eq(testDevice), any());

        // Verify ready message publication
        verify(mqttClient).publish(
                eq(TopicBuilder.build(testDevice, "ready")),
                argThat(message -> {
                    try {
                        AndroidReadyStatusMessage readyMessage = objectMapper.readValue(
                                message.getPayload(), AndroidReadyStatusMessage.class);
                        return readyMessage.isReady() &&
                                readyMessage.getConnectionMode() == AndroidConnectionMode.AT;
                    } catch (Exception e) {
                        return false;
                    }
                })
        );
    }
}