package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.dao.TestResultDao;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.event.device.DeviceResultsOverrideRequestEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.mdm.MdmInfo;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.CosmeticsResults;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.TestResultSummary;
import com.phonecheck.model.test.TestResults;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.TestResultsUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeviceResultsOverrideRequestListenerTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private TestResultDao testResultDao;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private DeviceTestResultDBService deviceTestResultDBService;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private DeviceResultsOverrideRequestListener listener;
    @Mock
    private DeviceAutomationQueueService automationQueueService;
    @Mock
    private LocalizationService localizationService;

    private IosDevice device;


    @BeforeEach
    void beforeEach() {
        device = new IosDevice();
        device.setId("123456");
        device.setSerial("12");
        device.setImei("asdf");
        listener = new DeviceResultsOverrideRequestListener(mqttClient,
                objectMapper, testResultDao, inMemoryStore, deviceConnectionTracker,
                deviceTestResultDBService, automationQueueService, eventPublisher);
    }

    @Test
    public void testOnEventSuccess() throws JsonProcessingException {
        device.setDeviceLock(DeviceLock.OFF);
        device.setEsnStatus("Good");
        device.setBatteryDegraded(false);
        MdmInfo mdmInfo = new MdmInfo();
        mdmInfo.setMdmStatus(MdmStatus.OFF);
        device.setMdmInfo(mdmInfo);
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setFailed(Arrays.asList());
        testResults.setTotal(Arrays.asList("2"));
        testResults.setTestingCompleted(true);
        testResults.setPassed(Arrays.asList("asd,asd22f"));
        testResults.setPassedCount("2");
        deviceTestResult.setTestResults(testResults);
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassedCount("1");
        cosmeticsResults.setPassed("glass");
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);

        HashMap<AutomationWorkflow, AutomationWorkflowStatus> workflowMap = new HashMap<>();
        workflowMap.put(AutomationWorkflow.TEST_RESULTS, AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS);
        device.setPreviouslyRanAutomation(workflowMap);

        CloudCustomizationResponse cloudCustomization = CloudCustomizationResponse.builder().
                id("asd").build();
        TestResultsUtil.prependInitialDefectsToAppResults(device, cloudCustomization, localizationService);

        Mockito.when(deviceConnectionTracker.getDevice(device.getId()))
                .thenReturn(device);
        when(inMemoryStore.getTransaction())
                .thenReturn(Transaction.builder().transactionId(123).build());
        Mockito.when(inMemoryStore.getLicenseId())
                .thenReturn(111);

        final TestResultSummary testResultSummary = TestResultSummary.builder()
                .id("123456")
                .passedTests(TestResultsUtil.listToCommaSeparatedString(
                        device.getDeviceTestResult().getTestResults().getPassed()))
                .failedTests(TestResultsUtil.listToCommaSeparatedString(
                        device.getDeviceTestResult().getTestResults().getFailed()))
                .testingCompleted(device.getDeviceTestResult().getTestResults().getTestingCompleted())
                .build();

        final DeviceResultsOverrideRequestEvent event = new DeviceResultsOverrideRequestEvent(listener, device, true);
        listener.onEvent(event);
        Mockito.verify(testResultDao).updateGeneralResults(device.getSerial(),
                device.getDeviceType(), "123", "111", testResultSummary);
        Mockito.verify(testResultDao).updateCosmetics(device.getId(), "123", List.of("glass"), null);
        verify(automationQueueService).enqueueDeviceAutomationRequest(any(DeviceTestResultAutomationEvent.class));

        ArgumentCaptor<AbstractDeviceEvent> captor = ArgumentCaptor.forClass(AbstractDeviceEvent.class);
        verify(eventPublisher, times(2)).publishEvent(captor.capture());
    }
}
