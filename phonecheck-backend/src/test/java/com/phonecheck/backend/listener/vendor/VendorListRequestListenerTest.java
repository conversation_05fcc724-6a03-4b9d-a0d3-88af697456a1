package com.phonecheck.backend.listener.vendor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudVendorInvoiceService;
import com.phonecheck.model.cloudapi.VendorInvoiceInfoResponse;
import com.phonecheck.model.event.vendor.VendorListRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.VendorListResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VendorListRequestListenerTest {

    private InMemoryStore inMemoryStore;
    @Mock
    private CloudVendorInvoiceService cloudVendorInvoiceService;
    @Mock
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private VendorListRequestListener listener;

    @BeforeEach
    public void setup() {
        inMemoryStore = new InMemoryStore();
        Transaction transaction = new Transaction();
        transaction.setStationId("taramac");
        transaction.setTransactionId(1);
        inMemoryStore.setTransaction(transaction);
        inMemoryStore.setMasterId("131");
        listener = new VendorListRequestListener(mqttClient, objectMapper, inMemoryStore, cloudVendorInvoiceService);
        when(mqttClient.isConnected()).thenReturn(true);
    }

    @Test
    public void onEventGetVendorList() throws MqttException {
        final VendorListRequestEvent event = new VendorListRequestEvent(this);
        // Mocking the VendorInvoiceInfoResponse and its dependencies
        VendorInvoiceInfoResponse vendorInvoiceInfoResponse = new VendorInvoiceInfoResponse();
        vendorInvoiceInfoResponse.setMsg("success");
        VendorInvoiceInfoResponse.ResponseData responseData = new VendorInvoiceInfoResponse.ResponseData();
        List<VendorInvoiceInfoResponse.VendorInvoice> vendorInvoiceList = new ArrayList<>();
        VendorInvoiceInfoResponse.VendorInvoice vendorInvoice1 =
                new VendorInvoiceInfoResponse.VendorInvoice("Vendor1", "Invoice1");
        VendorInvoiceInfoResponse.VendorInvoice vendorInvoice2 =
                new VendorInvoiceInfoResponse.VendorInvoice("Vendor2", "Invoice2");
        vendorInvoiceList.add(vendorInvoice1);
        vendorInvoiceList.add(vendorInvoice2);
        responseData.setAllVendorInvoices(vendorInvoiceList);
        vendorInvoiceInfoResponse.setResponse(responseData);

        // Stubbing the CloudVendorInvoiceService to return the mock response
        when(cloudVendorInvoiceService.getVendorInvoiceListInfo(any(), any())).thenReturn(vendorInvoiceInfoResponse);

        // Execute the onEvent method
        listener.onEvent(event);

        verify(cloudVendorInvoiceService).getVendorInvoiceListInfo(any(), any());
        Mockito.verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("vendor-list", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final VendorListResponseMessage message = objectMapper.readValue(
                        mqttMessage.getPayload(), VendorListResponseMessage.class);
                Assertions.assertFalse(message.getInvoiceList().isEmpty());
                Assertions.assertFalse(message.getVendorList().isEmpty());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    public void onEventGetVendorListEmpty() throws MqttException {
        final VendorListRequestEvent event = new VendorListRequestEvent(this);
        // Mocking the VendorInvoiceInfoResponse and its dependencies
        VendorInvoiceInfoResponse vendorInvoiceInfoResponse = new VendorInvoiceInfoResponse();
        VendorInvoiceInfoResponse.ResponseData responseData = new VendorInvoiceInfoResponse.ResponseData();
        List<VendorInvoiceInfoResponse.VendorInvoice> vendorInvoiceList = new ArrayList<>();
        responseData.setAllVendorInvoices(vendorInvoiceList);
        vendorInvoiceInfoResponse.setResponse(responseData);

        // Stubbing the CloudVendorInvoiceService to return the mock response
        when(cloudVendorInvoiceService.getVendorInvoiceListInfo(any(), any())).thenReturn(vendorInvoiceInfoResponse);

        // Execute the onEvent method
        listener.onEvent(event);

        verify(cloudVendorInvoiceService).getVendorInvoiceListInfo(any(), any());
        Mockito.verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("vendor-list", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final VendorListResponseMessage message = objectMapper.readValue(
                        mqttMessage.getPayload(), VendorListResponseMessage.class);
                Assertions.assertTrue(message.getInvoiceList().isEmpty());
                Assertions.assertTrue(message.getVendorList().isEmpty());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }
}
