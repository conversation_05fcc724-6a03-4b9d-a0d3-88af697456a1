package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.service.FirmwareDownloadService;
import com.phonecheck.backend.util.RestoreExecutorQueue;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.dao.service.EraserInfoDBService;
import com.phonecheck.device.connection.ios.IosDeviceRestoreService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.license.LicenseService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosDeviceAutoRestoreEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosDeviceRestoreResponseMessage;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.status.RestoreResponseCode;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.MacSupportFilePathsStrategy;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.model.util.SupportFilePath;
import com.phonecheck.parser.device.DeviceOsVersionParser;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosRestoreRequestListenerTest {

    @InjectMocks
    private IosRestoreRequestListener iosRecoveryListener;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private IosDeviceRestoreService iosDeviceRestoreService;
    @Mock
    private CommandExecutor executor;
    @Mock
    private EraserInfoDBService eraserInfoDBService;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;
    @Mock
    private DeviceInfoDBService deviceInfoDBService;
    @Mock
    private OsChecker osChecker;
    @Mock
    private DeviceOsVersionParser deviceOsVersionParser;
    @Mock
    private RestoreExecutorQueue restoreExecutorQueue;
    @Mock
    private FirmwareDownloadService firmwareDownloadService;
    @Mock
    private SupportFilePath supportFilePath;

    private IosDevice device;

    @Mock
    private LicenseService licenseService;
    @Mock
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;

    @Mock
    private FileUtil fileUtil;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setup() {
        iosRecoveryListener = new IosRestoreRequestListener(
                mqttClient,
                objectMapper,
                deviceConnectionTracker,
                iosDeviceRestoreService,
                executor,
                eraserInfoDBService,
                inMemoryStore,
                iosDeviceInfoService,
                deviceInfoDBService, osChecker,
                deviceOsVersionParser, restoreExecutorQueue,
                firmwareDownloadService, licenseService, supportFilePath, cloudDeviceDataSyncService, fileUtil);

        when(mqttClient.isConnected()).thenReturn(true);
        device = new IosDevice();
        device.setId("TestID");
        device.setSerial("serial1234");
        device.setFirmware("/");
        device.setModel("iPhone 6s");
        device.setProductType("test product type");
        device.setProductVersion("4.5.2");
    }

    @Test
    public void testWhenNoFirmware() throws Exception {
        when(deviceConnectionTracker.getDevice("TestID")).thenReturn(device);

        when(iosDeviceRestoreService.getFirmwareByProductType(device.getProductType())).
                thenReturn(null);

        iosRecoveryListener.onEvent(new IosDeviceAutoRestoreEvent(this, device));

        verify(deviceConnectionTracker).getDevice("TestID");
        verify(iosDeviceRestoreService).getFirmwareByProductType(device.getProductType());

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "restore", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        IosDeviceRestoreResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                IosDeviceRestoreResponseMessage.class);

        Assertions.assertEquals(DeviceRestoreStatus.RESTORE_FAILED, outputMsg.getRestoreStatus());
        Assertions.assertEquals(RestoreResponseCode.FIRMWARE_NOT_AVAILABLE, outputMsg.getResponseCode());
    }

    @Test
    public void testWhenUpdateOs() throws Exception {
        when(deviceConnectionTracker.getDevice("TestID")).thenReturn(device);

        FirmwareModel.FirmwareResponse firmware = new FirmwareModel.FirmwareResponse();
        firmware.setFileName("testfirmware.ipsw");
        firmware.setId("testFirmware");
        firmware.setMd5Sum("md5FileHash");
        firmware.setFolderMd5Hash("md5FolderHash");
        firmware.setVersion("4.5.2");

        device.setRecoveryMode(false);
        when(iosDeviceRestoreService.getFirmwareByProductType(device.getProductType())).
                thenReturn(firmware);

        iosRecoveryListener.onEvent(new IosDeviceAutoRestoreEvent(this, device));

        verify(deviceConnectionTracker).getDevice("TestID");
        verify(iosDeviceRestoreService).getFirmwareByProductType(device.getProductType());

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);

        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "restore", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        IosDeviceRestoreResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                IosDeviceRestoreResponseMessage.class);

        Assertions.assertEquals(DeviceRestoreStatus.DEVICE_OS_UPDATED, outputMsg.getRestoreStatus());
        Assertions.assertEquals(RestoreResponseCode.UPDATED_OS, outputMsg.getResponseCode());
    }

    @Test
    public void testCloudSyncCalledForDeviceInRecovery() throws Exception {
        Transaction transaction = new Transaction();
        when(deviceConnectionTracker.getDevice("TestID")).thenReturn(device);
        when(inMemoryStore.getFirmwareDownloadPath()).thenReturn("path.ipsw");
        when(inMemoryStore.getTransaction()).thenReturn(transaction);
        when(supportFilePath.getPaths()).thenReturn(new MacSupportFilePathsStrategy(true));
        when(firmwareDownloadService.unzipFirmwareZip(any(File.class), anyString())).thenReturn(true);

        FirmwareModel.FirmwareResponse firmware = mock(FirmwareModel.FirmwareResponse.class);
        when(firmware.getFileName()).thenReturn("testfirmware.ipsw");
        when(firmware.getId()).thenReturn("testFirmware");
        when(firmware.getVersion()).thenReturn("4.5.2");
        when(firmware.getDownloadStatus()).thenReturn(FirmwareDownloadStatus.SUCCESS);

        device.setRecoveryMode(true);
        device.setConnectedInRecovery(true);
        device.setEcid("FSHFRLVL");
        when(iosDeviceRestoreService.getFirmwareByProductType(device.getProductType())).
                thenReturn(firmware);

        iosRecoveryListener.onEvent(new IosDeviceAutoRestoreEvent(this, device));

        verify(deviceConnectionTracker).getDevice("TestID");
        verify(iosDeviceRestoreService).getFirmwareByProductType(device.getProductType());
        verify(cloudDeviceDataSyncService).syncDeviceRecordToCloud(device, transaction);
    }
}
