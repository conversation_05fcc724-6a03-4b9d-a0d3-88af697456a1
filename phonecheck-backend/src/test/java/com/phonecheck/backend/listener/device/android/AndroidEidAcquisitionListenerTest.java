package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.info.android.eid.EidCommandsService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidEidAcquisitionEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidEidAcquisitionListenerTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private EidCommandsService eidCommandsService;
    @Mock
    private AndroidDevice device;
    @Mock
    private AndroidEidAcquisitionEvent event;
    @InjectMocks
    private AndroidEidAcquisitionListener listener;
    @Mock
    private DeviceInfoDBService deviceInfoDBService;
    @Mock
    private InMemoryStore inMemoryStore;


    @BeforeEach
    void setUp() {
        listener = new AndroidEidAcquisitionListener(objectMapper, mqttClient,
                deviceConnectionTracker, eidCommandsService, deviceInfoDBService, inMemoryStore);
        when(event.getDevice()).thenReturn(device);
        when(device.getId()).thenReturn("deviceId");
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
    }

    @Test
    void testOnEventEidRetrievedFromOcr() throws Exception {
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(device.getAndroidConnectionMode()).thenReturn(AndroidConnectionMode.ADB);
        when(eidCommandsService.getEidWithOCR(device.getId())).thenReturn("OCR_EID");

        listener.onEvent(event);

        Thread.sleep(5000);

        verify(eidCommandsService, times(1)).getEidWithOCR(device.getId());
        verify(device, times(1)).setEid("OCR_EID");
    }

    @Test
    void testOnEventEidRetrievedFromXml() throws Exception {
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(device.getAndroidConnectionMode()).thenReturn(AndroidConnectionMode.ADB);
        when(eidCommandsService.getEidWithOCR(device.getId())).thenReturn(null);
        when(eidCommandsService.getEidWithXml(device.getId())).thenReturn("XML_EID");

        listener.onEvent(event);

        Thread.sleep(5000);

        verify(eidCommandsService, times(2)).getEidWithOCR(device.getId());
        verify(eidCommandsService, times(1)).getEidWithXml(device.getId());
        verify(device, times(1)).setEid("XML_EID");
    }

    @Test
    void testOnEventEidNotRetrievedFromAnyMethod() throws Exception {
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(device.getAndroidConnectionMode()).thenReturn(AndroidConnectionMode.ADB);
        when(eidCommandsService.getEidWithOCR(device.getId())).thenReturn(null);
        when(eidCommandsService.getEidWithXml(device.getId())).thenReturn(null);

        listener.onEvent(event);

        Thread.sleep(5000);

        verify(eidCommandsService, times(2)).getEidWithOCR(device.getId());
        verify(eidCommandsService, times(2)).getEidWithXml(device.getId());
        verify(device, never()).setEid(anyString());
    }
}