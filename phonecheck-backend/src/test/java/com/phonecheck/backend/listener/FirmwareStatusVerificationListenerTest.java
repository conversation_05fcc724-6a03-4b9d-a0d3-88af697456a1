package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.FirmwareStatusVerificationEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.FirmwareStatusMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FirmwareStatusVerificationListenerTest {
    @InjectMocks
    private FirmwareStatusVerificationListener listener;

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;

    @Mock
    private InMemoryStore inMemoryStore;

    @BeforeEach
    void setup() {
        listener = new FirmwareStatusVerificationListener(mqttClient, objectMapper, inMemoryStore);
        when(mqttClient.isConnected()).thenReturn(true);
    }

    @Test
    public void testOnEvent() throws MqttException, IOException {

        FirmwareModel.FirmwareResponse res = new FirmwareModel.FirmwareResponse();
        res.setFileName("testfile");
        res.setDownloadStatus(FirmwareDownloadStatus.SUCCESS);
        res.setId("testFirmware");
        Map<String, FirmwareModel.FirmwareResponse> map = new HashMap<>();
        map.put("testFirmware", res);
        when(inMemoryStore.getFirmwareModels()).thenReturn(map);

        listener.onEvent(new FirmwareStatusVerificationEvent(this));

        verify(inMemoryStore).getFirmwareModels();

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("firmware-status", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        FirmwareStatusMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                FirmwareStatusMessage.class);
        Map<String, FirmwareDownloadStatus> expectedMap = new HashMap<>();
        expectedMap.put("testFirmware", res.getDownloadStatus());
        Assertions.assertEquals(expectedMap, outputMsg.getFirmwareDownloadStatusMap());
    }
}
