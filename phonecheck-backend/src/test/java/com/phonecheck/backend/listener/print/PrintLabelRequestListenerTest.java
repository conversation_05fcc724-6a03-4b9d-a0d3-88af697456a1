package com.phonecheck.backend.listener.print;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.print.PrintLabelRequestEvent;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.print.service.LabelPrintService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PrintLabelRequestListenerTest {

    @InjectMocks
    private PrintLabelRequestListener classUnderTest;
    @Mock
    private LabelPrintService labelPrintService;
    @Mock
    private InMemoryStore inMemoryStore;

    @Test
    public void testOnEventSuccess() {

        IosDevice device = new IosDevice();
        device.setId("id");
        PrintOperation printOperation = new PrintOperation();
        printOperation.setDevice(device);
        printOperation.setLabelName("label");
        printOperation.setPrinterName("printer");

        when(inMemoryStore.getAssignedCloudCustomization())
                .thenReturn(CloudCustomizationResponse.builder()
                        .id("userName")
                        .requiredFields(CloudCustomizationResponse.RequiredFields.builder()
                                .color(true).build())
                        .restrictiveActions(CloudCustomizationResponse
                                .RestrictiveActions.builder()
                                .build())
                        .build());

        final PrintLabelRequestEvent event = new PrintLabelRequestEvent(this, printOperation);

        classUnderTest.onEvent(event);

        Mockito.verify(labelPrintService, Mockito.times(1)).enqueuePrintOperation(eq(printOperation));
    }

    @Test
    @DisplayName("Test to not trigger restricted actions when print from manual entry")
    public void testManualEntryPrintEvent() {

        IosDevice device = new IosDevice();
        device.setId("id");
        PrintOperation printOperation = new PrintOperation();
        printOperation.setDevice(device);
        printOperation.setLabelName("label");
        printOperation.setPrinterName("printer");
        printOperation.setPrintMode(PrintMode.MANUAL_ENTRY);

        final PrintLabelRequestEvent event = new PrintLabelRequestEvent(this, printOperation);

        classUnderTest.onEvent(event);

        Mockito.verify(labelPrintService, Mockito.times(1)).enqueuePrintOperation(eq(printOperation));
        Mockito.verify(inMemoryStore, Mockito.times(0)).getAssignedCloudCustomization();
    }

}
