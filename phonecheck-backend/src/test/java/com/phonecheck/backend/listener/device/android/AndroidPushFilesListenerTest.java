package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidPushFilesEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.peo.android.AndroidPushFilesService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidPushFilesListenerTest {
    @Mock
    private AndroidPushFilesService pushFilesService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidPushFilesListener androidPushFilesListener;
    private AndroidDevice device;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;

    @BeforeEach
    void beforeEach() {
        device = new AndroidDevice();
        device.setId("123456");
        device.setSerial("1234");
        androidPushFilesListener = new AndroidPushFilesListener(pushFilesService, deviceConnectionTracker,
                mqttClient, objectMapper, timerLoggerUtil);
        when(mqttClient.isConnected()).thenReturn(true);
    }

    @Test
    public void onEventTest() throws IOException, MqttException {
        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);
        when(pushFilesService.getFiles(device, 0)).thenReturn(List.of("allSyncableData.json"));

        final AndroidPushFilesEvent event = new AndroidPushFilesEvent(this, device);
        androidPushFilesListener.onEvent(event);

        verify(pushFilesService, times(2)).androidPushFile(any(), anyString(), anyString());
        verify(mqttClient, times(2))
                .publish(eq(TopicBuilder.build(device, "push-files", "status")), any(MqttMessage.class));
    }

}

