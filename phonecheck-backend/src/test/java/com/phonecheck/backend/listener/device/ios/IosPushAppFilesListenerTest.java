package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosPushFilesEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.peo.ios.IosPushFilesService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosPushAppFilesListenerTest {

    @Mock
    private IosPushFilesService pushFilesService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private IosPushAppFilesListener iosPushAppFilesListener;
    private IosDevice device;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private IosPairingUtil iosPairingUtil;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;

    @BeforeEach
    void beforeEach() throws IOException {
        device = new IosDevice();
        device.setId("123456");
        device.setSerial("1234");
        device.setProductType("iPhone8,1");
        iosPushAppFilesListener = new IosPushAppFilesListener(pushFilesService, deviceConnectionTracker, inMemoryStore,
                mqttClient, objectMapper, timerLoggerUtil, eventPublisher, iosPairingUtil, iosDeviceInfoService);
    }

    @Test
    public void onEventTest() throws IOException, MqttException {
        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn("12");
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(device)).thenReturn(PairStatus.PAIRED);
        when(pushFilesService.getFiles(device, 0)).thenReturn(List.of("filePath"));
        when(pushFilesService.iosPushFile(device, inMemoryStore.getIosAppBundleIdentifier(), "filePath"))
                .thenReturn("success");
        when(mqttClient.isConnected()).thenReturn(true);
        final IosPushFilesEvent event = new IosPushFilesEvent(this, device);
        iosPushAppFilesListener.onEvent(event);

        verify(pushFilesService, times(1)).iosPushFile(any(), anyString(), anyString());
        verify(mqttClient, times(2))
                .publish(eq(TopicBuilder.build(device, "push-files", "status")), any(MqttMessage.class));
    }

    @Test
    public void testNoDeviceFound() throws Exception {
        final IosDevice device = new IosDevice();
        device.setId("123");

        final IosPushFilesEvent event = new IosPushFilesEvent(this, device);
        iosPushAppFilesListener.onEvent(event);

        verify(pushFilesService, never()).iosPushFile(any(), anyString(), anyString());
    }
}
