package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.DeviceActionService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.AsyncEsnRequestEvent;
import com.phonecheck.model.event.device.android.AndroidPerformCustomizationEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidPerformCustomizationListenerTest {
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private DeviceActionService deviceActionService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidDevice androidDevice;
    private AndroidPerformCustomizationListener listener;

    @BeforeEach
    void setUp() {
        listener = new AndroidPerformCustomizationListener(
                eventPublisher, objectMapper, mqttClient, deviceConnectionTracker, inMemoryStore, deviceActionService
        );
        androidDevice = new AndroidDevice();
        androidDevice.setId("123");
    }

    @Test
    void testOnEventDeviceInTracker() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);
        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);

        listener.onEvent(event);

        verify(deviceConnectionTracker).getDevice(anyString());
    }

    @Test
    void testOnEventDeviceNotInTracker() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);
        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(null);

        listener.onEvent(event);
        verify(deviceConnectionTracker).getDevice(anyString());
    }

    @Test
    void testOnEventNoCustomizationFound() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);

        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(null);

        listener.onEvent(event);

        verify(eventPublisher, never()).publishEvent(any(AsyncEsnRequestEvent.class));
        verify(deviceConnectionTracker).getDevice(anyString());
    }


    @Test
    void testOnEventWhenImeiCheckCustomizationDisabled() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);

        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder().automaticIMEICheck(false).build())
                .build());

        listener.onEvent(event);

        verify(eventPublisher, never()).publishEvent(any(AsyncEsnRequestEvent.class));
        verify(deviceConnectionTracker).getDevice(anyString());
    }

    @Test
    void testOnEventImeiCheckCustomizationEnabled() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);

        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder().automaticIMEICheck(true).build())
                .build());

        listener.onEvent(event);

        verify(eventPublisher).publishEvent(any(AsyncEsnRequestEvent.class));
        verify(deviceConnectionTracker).getDevice(anyString());
    }

    @Test
    void testImeiSerialValidationInitiated() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);
        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings.builder()
                        .initialDefectsWorkflowEnabled(true)
                        .initialDefect(CloudCustomizationResponse.WorkflowInitialDefect.builder()
                                .ImeiSerialValidation(true).build())
                        .build())
                .build());

        listener.onEvent(event);

        verify(deviceActionService, times(1)).initiateValidateRequest(any(Device.class));
    }

    @Test
    void testImeiSerialValidationNotInitiated() {
        AndroidPerformCustomizationEvent event = new AndroidPerformCustomizationEvent(this, androidDevice);
        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings.builder()
                        .initialDefectsWorkflowEnabled(false)
                        .initialDefect(CloudCustomizationResponse.WorkflowInitialDefect.builder()
                                .ImeiSerialValidation(false).build())
                        .build())
                .build());

        listener.onEvent(event);

        verify(deviceActionService, never()).initiateValidateRequest(any(Device.class));
    }
}

