package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.Cloud3DeviceDataSyncService;
import com.phonecheck.app.uninstall.DeviceUninstallAppService;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.device.results.IosTestResultsService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.AppTestingDoneStage;
import com.phonecheck.model.device.stage.OemDataCollectionSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.event.device.ios.IosParseLogStreamEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.mqtt.messages.ios.IosSysLogMessage;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import com.phonecheck.model.test.*;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.RunningModeUtil;
import com.phonecheck.parser.device.ios.info.IosOemPartsStatusService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosParseLogStreamListenerTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String iosAppIdentifier = "com.phonecheckdiag3";
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;
    @Mock
    private IosTestResultsService testResultsService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private DeviceTestResultDBService deviceTestResultDBService;
    @Mock
    private Cloud3DeviceDataSyncService cloud3DeviceDataSyncService;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosParseLogStreamListener listener;
    private IosDevice device;
    @Mock
    private DeviceAutomationQueueService automationQueueService;
    @Mock
    private IosOemPartsStatusService oemPartsStatusService;
    @Mock
    private IosPairingUtil iosPairingUtil;
    @Mock
    private DeviceDataConversionAndExportService deviceDataConversionAndExportService;
    @Mock
    private LocalizationService localizationService;
    @Mock
    private VendorCriteriaService vendorCriteriaService;
    @Mock
    private RunningModeUtil runningModeUtil;

    private DeviceUninstallAppService deviceUninstallAppService;

    @BeforeEach
    void setup() throws IOException {
        device = new IosDevice();
        device.setId("12345678");
        device.setProductType("iPhone8,1");
        device.setTouchIdSensor(WorkingStatus.YES);
        device.setFaceIdSupported(false);
        listener = new IosParseLogStreamListener(eventPublisher, mqttClient, objectMapper, inMemoryStore,
                testResultsService, iosDeviceInfoService, deviceTestResultDBService,
                cloud3DeviceDataSyncService, deviceConnectionTracker, stageUpdater, automationQueueService,
                oemPartsStatusService, iosPairingUtil, deviceDataConversionAndExportService, localizationService,
                vendorCriteriaService, deviceUninstallAppService, runningModeUtil);
    }

    @Test
    public void testNoDevice() throws MqttException, IOException {
        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";

        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);
        listener.onEvent(event);
        verify(mqttClient, never()).publish(anyString(), any(MqttMessage.class));
        verify(testResultsService, never()).getTestResults(any(IosDevice.class), eq(iosAppIdentifier));
        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), eq(iosAppIdentifier));
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key")
    void testOnGettingTestResultsKey1() throws MqttException, IOException {

        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);

        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);

        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setGradeResults("A");
        deviceTestResult.setTestResults(testResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device), eq(iosAppIdentifier))).thenReturn(deviceTestResultStatus);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(false).build()).build());
        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                assertEquals("A", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
        verify(automationQueueService).enqueueDeviceAutomationRequest(any(DeviceTestResultAutomationEvent.class));
    }

    @Test
    @DisplayName("Test initial tests are prepended on getting Start Test key")
    void testOnGettingTestResultsKey3() throws MqttException, IOException {
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);
        when(mqttClient.isConnected()).thenReturn(true);
        device.setImei("123");
        device.setBatteryInfo(BatteryInfo.builder().healthPercentage(50).build());
        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device), eq(iosAppIdentifier))).thenReturn(deviceTestResultStatus);

        CloudCustomizationResponse.Rule healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(70).maximumCycle(1500).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1}).build();
        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder().customizeBattery(true)
                                .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                                .appleFail(true)
                                .build())
                        .wifiSettings(CloudCustomizationResponse.WifiSettings.builder()
                                .disconnect(false).build())
                        .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);
        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";

        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            assertNotNull(arg.getDeviceTestResult().getTestResults());


            Assertions.assertEquals("[BH-A, Battery Warning, failed]",
                    arg.getDeviceTestResult().getTestResults().getFailed().toString());
            Assertions.assertEquals("[Fingerprint Sensor, passed]",
                    arg.getDeviceTestResult().getTestResults().getPassed().toString());
            Assertions.assertEquals(deviceTestResult.getTestResults(),
                    arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload().toString());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                assertEquals("[BH-A, Battery Warning, failed]", message.getDeviceTestResult()
                        .getTestResults().getFailed().toString());
                assertEquals("[Fingerprint Sensor, passed]", message.getDeviceTestResult()
                        .getTestResults().getPassed().toString());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
        verify(automationQueueService).enqueueDeviceAutomationRequest(any(DeviceTestResultAutomationEvent.class));
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key and device has grade results")
    void testOnGettingGradeFromDeviceTestResults() throws MqttException, IOException {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");
        device.setGrade("CCC");

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);
        when(mqttClient.isConnected()).thenReturn(true);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of(("failed"))));
        testResults.setPassed(new ArrayList<>(List.of(("passed"))));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setGradeResults("GR");
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device), eq(iosAppIdentifier))).thenReturn(deviceTestResultStatus);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(false).build()).build());
        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(testResultsService).getTestResults(eq(device), eq(iosAppIdentifier));
        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            // verify the grade results in the test result object got set with grade results
            assertEquals("GR", arg.getDeviceTestResult().getGradeResults());
            Assertions.assertEquals(deviceTestResult.getTestResults(), arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                // verify the grade results in the test result object got set with grade results
                assertEquals("GR", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key and get device grades from the device object")
    void testOnGettingGradeFromDeviceObject() throws MqttException, IOException {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");
        device.setGrade("CCC");

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);
        when(mqttClient.isConnected()).thenReturn(true);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device), eq(iosAppIdentifier))).thenReturn(deviceTestResultStatus);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(false).build()).build());
        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(testResultsService).getTestResults(eq(device), eq(iosAppIdentifier));
        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            // verify the grade results in the test result object got set with the grade in the device object
            assertEquals("CCC", arg.getDeviceTestResult().getGradeResults());
            Assertions.assertEquals(deviceTestResult.getTestResults(), arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                // verify the grade results in the test result object got set with the grade in the device object
                assertEquals("CCC", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key and get device grades from DB")
    void testOnGettingGradeFromDB() throws MqttException, IOException {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);
        when(mqttClient.isConnected()).thenReturn(true);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device), eq(iosAppIdentifier))).thenReturn(deviceTestResultStatus);
        when(deviceTestResultDBService.getDeviceGrade(eq("123"), eq(device.getId()))).thenReturn("C");
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(false).build()).build());
        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(testResultsService).getTestResults(eq(device), eq(iosAppIdentifier));
        verify(deviceTestResultDBService).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            deviceTestResult.setGradeResults("C");
            Assertions.assertEquals(deviceTestResult.getTestResults(), arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                assertEquals("C", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting iCloud key")
    void testOnGettingICloudKey() throws MqttException {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");
        when(mqttClient.isConnected()).thenReturn(true);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        IosSysLogKey iosSysLogKey = IosSysLogKey.ICLOUD_APPLE_ID_FOUND_1;
        String sysLogResponse = "test response";
        when(iosDeviceInfoService.parseSysLogStream(device, iosSysLogKey, sysLogResponse)).thenReturn(true);

        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(mqttClient).publish(eq(TopicBuilder.build(device, "parsed", "syslog")), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final IosSysLogMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        IosSysLogMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceLock());
                assertEquals(message.getDeviceLock(), device.getDeviceLock());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
        verify(iosDeviceInfoService).parseSysLogStream(eq(device),
                eq(iosSysLogKey),
                eq(sysLogResponse));
    }

    @Test
    @DisplayName("Test Parse log on getting face id key")
    void testOnGettingFaceIdKey() throws MqttException {

        device.setFaceIdSensor(WorkingStatus.YES);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        IosSysLogKey iosSysLogKey = IosSysLogKey.FACE_ID_WORKING_3;
        String sysLogResponse = "test response";
        when(iosDeviceInfoService.parseSysLogStream(device, iosSysLogKey, sysLogResponse)).thenReturn(true);

        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(mqttClient, never()).publish(anyString(), any());
        verify(iosDeviceInfoService).parseSysLogStream(eq(device), eq(iosSysLogKey), eq(sysLogResponse));
    }

    @Test
    @DisplayName("Test Parse log on getting OEM key")
    void testOnGettingOEMKey1() throws MqttException {
        when(mqttClient.isConnected()).thenReturn(true);
        device.setModel("iPhone 11");
        device.setOemBatteryNotice("Iphone battery notice");
        device.setOemPartsCurrent(new OemPartsData());
        device.setOemPartsFactory(new OemPartsData());
        device.setOverallOemStatus(OemStatus.GENUINE);
        IosSysLogKey iosSysLogKey = IosSysLogKey.OEM_BATTERY_NOTICE;
        String sysLogResponse = "test response";

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(iosDeviceInfoService.parseSysLogStream(device, iosSysLogKey, sysLogResponse)).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLocalCustomizations()).thenReturn(new LocalCustomizations());
        doCallRealMethod().when(oemPartsStatusService).reCalculateOemStatusUsingNotices(any(), anyBoolean());

        listener.onEvent(new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse));

        verify(oemPartsStatusService).reCalculateOemStatusUsingNotices(any(), anyBoolean());
        assertNotNull(device.getOemPartsJson());
        verify(mqttClient).publish(eq(TopicBuilder.build(device, "parsed", "syslog")), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final IosSysLogMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        IosSysLogMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNull(message.getOemDisplayNotice());
                assertEquals(device.getOemBatteryNotice(), message.getOemBatteryNotice());
                assertEquals(OemStatus.NOT_GENUINE, message.getOemBatteryStatus());
                assertEquals(OemStatus.NA, message.getOemLcdStatus());
                assertEquals(OemStatus.NOT_GENUINE, message.getOverallOemStatus());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
        verify(iosDeviceInfoService).parseSysLogStream(eq(device),
                eq(iosSysLogKey),
                eq(sysLogResponse));
        Mockito.verify(stageUpdater).updateStage(argThat((OemDataCollectionSuccessStage arg) -> {
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals("123", arg.getTransactionId());
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting OEM key2")
    void testOnGettingOEMKey2() throws MqttException {
        when(mqttClient.isConnected()).thenReturn(true);
        device.setModel("iPhone 11");
        device.setOemLcdStatus(OemStatus.GENUINE);
        device.setOemDisplayNotice("iPhone Display notice");
        device.setOemPartsCurrent(new OemPartsData());
        device.setOemPartsFactory(new OemPartsData());
        device.setOverallOemStatus(OemStatus.GENUINE);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        IosSysLogKey iosSysLogKey = IosSysLogKey.OEM_DISPLAY_NOTICE;
        String sysLogResponse = "test response";
        when(iosDeviceInfoService.parseSysLogStream(device, iosSysLogKey, sysLogResponse)).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        doCallRealMethod().when(oemPartsStatusService).reCalculateOemStatusUsingNotices(any(), anyBoolean());
        when(inMemoryStore.getLocalCustomizations()).thenReturn(new LocalCustomizations());

        listener.onEvent(new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse));

        verify(oemPartsStatusService).reCalculateOemStatusUsingNotices(any(), anyBoolean());
        assertNotNull(device.getOemPartsJson());
        verify(mqttClient).publish(eq(TopicBuilder.build(device, "parsed", "syslog")), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final IosSysLogMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        IosSysLogMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNull(message.getOemBatteryNotice());
                assertEquals(device.getOemDisplayNotice(), message.getOemDisplayNotice());
                assertEquals(OemStatus.NA, message.getOemBatteryStatus());
                assertEquals(OemStatus.NOT_GENUINE, message.getOemLcdStatus());
                assertEquals(OemStatus.NOT_GENUINE, message.getOverallOemStatus());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
        verify(iosDeviceInfoService).parseSysLogStream(eq(device),
                eq(iosSysLogKey),
                eq(sysLogResponse));
        Mockito.verify(stageUpdater).updateStage(argThat((OemDataCollectionSuccessStage arg) -> {
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals("123", arg.getTransactionId());
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting key other than Start Test results")
    void testOnGettingKeyOtherThanTestResults() {
        IosSysLogKey iosSysLogKey = IosSysLogKey.ICLOUD_APPLE_ID_NOT_FOUND;
        String sysLogResponse = "iCloud issue detected";

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);
        listener.onEvent(event);

        verify(iosDeviceInfoService).parseSysLogStream(eq(device),
                eq(iosSysLogKey),
                eq(sysLogResponse));
    }


    @Test
    @DisplayName("Test if test results and battery results update is called")
    void testBatteryResultsTestResultsUpdateIsInvoked() throws IOException {

        IosSysLogKey iosSysLogKey = IosSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setBatteryResults(new BatteryResults());
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getIosAppBundleIdentifier()).thenReturn(iosAppIdentifier);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(device)).thenReturn(PairStatus.PAIRED);
        when(testResultsService.getTestResults(eq(device), eq(iosAppIdentifier))).thenReturn(deviceTestResultStatus);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(false).build()).build());

        final IosParseLogStreamEvent event =
                new IosParseLogStreamEvent(listener, device, iosSysLogKey, sysLogResponse);
        listener.onEvent(event);

         verify(testResultsService).updateFailedTestResults(any(Device.class),
                any(DeviceTestResult.class));
        verify(testResultsService).updatePassedTestResults(any(Device.class),
                any(DeviceTestResult.class));
        verify(testResultsService).setBatteryResultsAndUpdateBatteryDrainResult(any(IosDevice.class),
                any(BatteryResults.class));

    }
}
