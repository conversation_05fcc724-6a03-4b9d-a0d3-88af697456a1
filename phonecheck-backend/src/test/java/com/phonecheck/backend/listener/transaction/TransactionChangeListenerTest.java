package com.phonecheck.backend.listener.transaction;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudTransactionService;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.event.transaction.TransactionChangeEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.TransactionChangeResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.transaction.TransactionChangeRecord;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TransactionChangeListenerTest {
    @InjectMocks
    private TransactionChangeListener transactionChangeListener;

    @Mock
    private CloudTransactionService transactionService;
    @Mock
    private InMemoryStore inMemoryStore;

    @Mock
    private IMqttAsyncClient mqttClient;

    @Mock
    private DeviceInfoDBService deviceInfoDBService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private  Transaction newTransaction = null;

    @BeforeEach
    void beforeEach() {
        transactionChangeListener = new TransactionChangeListener(mqttClient, objectMapper, transactionService,
                inMemoryStore, deviceInfoDBService);
        when(mqttClient.isConnected()).thenReturn(true);
        newTransaction = new Transaction();
        newTransaction.setTransactionId(2145);
        newTransaction.setVendorName("TestVendor");
        newTransaction.setQty("4");
        newTransaction.setBoxNo("TestBox");
        newTransaction.setTransactionDate(LocalDateTime.now().toString());
    }
    @Test
    public void testWhenTransactionChangeSuccessful() throws MqttException, IOException {
        when(inMemoryStore.getLicenseId()).thenReturn(59864);
        when(transactionService.changeTransactionForTheRecords(anyInt(),
                 anyString(), anyString(), any(Transaction.class))).thenReturn("success");

        final List<TransactionChangeRecord> selectedTransactions = new ArrayList<>();
        selectedTransactions.add(new TransactionChangeRecord("T1", "S1"));
        selectedTransactions.add(new TransactionChangeRecord("T1", "S2"));

        TransactionChangeEvent event = new TransactionChangeEvent(this, selectedTransactions, newTransaction);

        transactionChangeListener.onEvent(event);

        verify(transactionService, times(2)).changeTransactionForTheRecords(anyInt(),
                anyString(), anyString(), any(Transaction.class));
        verify(inMemoryStore, times(2)).getLicenseId();
        verify(deviceInfoDBService, times(2)).updateTransaction(anyString(), anyString(),
                anyString());

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("transaction-change", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        TransactionChangeResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                TransactionChangeResponseMessage.class);

        Assertions.assertEquals(0, outputMsg.getFailedSerials().size());
    }

    @Test
    public void testWhenTransactionChangeFailed() throws MqttException, IOException {
        when(inMemoryStore.getLicenseId()).thenReturn(59864);
        when(transactionService.changeTransactionForTheRecords(anyInt(),
                anyString(), anyString(), any(Transaction.class))).thenReturn("failed");

        final List<TransactionChangeRecord> selectedTransactions = new ArrayList<>();
        selectedTransactions.add(new TransactionChangeRecord("T1", "S1"));
        selectedTransactions.add(new TransactionChangeRecord("T1", "S2"));

        TransactionChangeEvent event = new TransactionChangeEvent(this, selectedTransactions, newTransaction);

        transactionChangeListener.onEvent(event);

        verify(transactionService, times(2)).changeTransactionForTheRecords(anyInt(),
                anyString(), anyString(), any(Transaction.class));
        verify(inMemoryStore, times(2)).getLicenseId();
        verify(deviceInfoDBService, times(2)).updateTransaction(anyString(), anyString(),
                anyString());

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("transaction-change", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        TransactionChangeResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                TransactionChangeResponseMessage.class);

        Assertions.assertEquals(2, outputMsg.getFailedSerials().size());
    }


}
