package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.Cloud3DeviceDataSyncService;
import com.phonecheck.api.cloud.CloudDeviceService;
import com.phonecheck.app.android.AndroidAlertSliderService;
import com.phonecheck.app.android.AndroidAppService;
import com.phonecheck.app.android.AndroidFingerPrintService;
import com.phonecheck.app.android.AndroidZFlipDisplayAutomationService;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.dao.service.EraserInfoDBService;
import com.phonecheck.device.android.wifi.AndroidWifiAutoDisconnectService;
import com.phonecheck.device.erase.AndroidEraseService;
import com.phonecheck.device.results.AndroidTestResultsService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.android.EraseOperation;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.stage.AppTestingDoneStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidEraseSuccessEvent;
import com.phonecheck.model.event.device.android.AndroidParseLogStreamEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidEraseResponseMessage;
import com.phonecheck.model.status.DeviceEraseStatus;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.syslog.android.AndroidSysLogKey;
import com.phonecheck.model.test.*;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.parser.device.android.erase.AndroidEraseOperationsParser;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidParseLogStreamListenerTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private AndroidTestResultsService testResultsService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private DeviceTestResultDBService deviceTestResultDBService;
    @Mock
    private AndroidEraseOperationsParser androidEraseOperationsParser;
    @Mock
    private AndroidEraseService androidEraseService;
    @Mock
    private EraserInfoDBService eraserInfoDBService;
    @Mock
    private AndroidFingerPrintService fingerPrintService;
    @Mock
    private Cloud3DeviceDataSyncService cloud3DeviceDataSyncService;
    @Mock
    private AndroidAppService appService;
    @Mock
    private AndroidAlertSliderService alertSliderService;
    @Mock
    private AndroidZFlipDisplayAutomationService zFlipDisplayAutomationService;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private AndroidParseLogStreamListener listener;
    @Mock
    private DeviceAutomationQueueService automationQueueService;
    @Mock
    private AndroidWifiAutoDisconnectService androidWifiAutoDisconnectService;
    @Mock
    private CloudDeviceService cloudDeviceService;
    @Mock
    private DeviceDataConversionAndExportService deviceDataConversionAndExportService;
    @Mock
    private LocalizationService localizationService;
    @Mock
    private CommandExecutor executor;
    private AndroidDevice device;
    @Mock
    private VendorCriteriaService vendorCriteriaService;

    @BeforeEach
    void setup() throws IOException {
        device = new AndroidDevice();
        device.setId("12345678");
        listener = new AndroidParseLogStreamListener(mqttClient, objectMapper, inMemoryStore,
                deviceConnectionTracker, testResultsService, deviceTestResultDBService,
                stageUpdater, androidEraseOperationsParser, androidEraseService,
                eraserInfoDBService, fingerPrintService, appService, alertSliderService, zFlipDisplayAutomationService,
                cloud3DeviceDataSyncService, automationQueueService, eventPublisher, androidWifiAutoDisconnectService,
                cloudDeviceService, deviceDataConversionAndExportService, localizationService, vendorCriteriaService);
    }

    @Test
    public void testNoDevice() throws MqttException, IOException {
        AndroidSysLogKey sysLogKey = AndroidSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, sysLogKey, sysLogResponse);
        listener.onEvent(event);
        verify(mqttClient, never()).publish(anyString(), any(MqttMessage.class));
        verify(testResultsService, never()).getTestResults(any(AndroidDevice.class));
        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(androidWifiAutoDisconnectService, never()).turnOffWifi(any());
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key")
    void testOnGettingTestResultsKey1() throws MqttException, IOException {
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        CloudCustomizationResponse cloudCustomizationResponse = CloudCustomizationResponse.builder()
                .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(true).build())
                .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomizationResponse);

        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);

        AndroidSysLogKey sysLogKey = AndroidSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";

        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setGradeResults("A");
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(
                new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(List.of("passed")));
        when(testResultsService.getTestResults(eq(device))).thenReturn(deviceTestResultStatus);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, sysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                assertEquals("A", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
        verify(androidWifiAutoDisconnectService).turnOffWifi(device);
    }

    @Test
    @DisplayName("Test initial tests are prepended on getting Start Test key")
    void testOnGettingTestResultsKey3() throws MqttException, IOException {
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(mqttClient.isConnected()).thenReturn(true);
        CloudCustomizationResponse.Rule healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(70).maximumCycle(1500).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1}).build();
        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder().customizeBattery(true)
                                .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                                .build())
                        .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        device.setImei("123");
        device.setBatteryInfo(BatteryInfo.builder().healthPercentage(50).build());
        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);

        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>());
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>());
        when(testResultsService.getTestResults(eq(device))).thenReturn(deviceTestResultStatus);

        AndroidSysLogKey sysLogKey = AndroidSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, sysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            assertNotNull(arg.getDeviceTestResult().getTestResults());
            Assertions.assertTrue(arg.getDeviceTestResult().getTestResults().getPassed().isEmpty());
            Assertions.assertEquals("[BH-A]",
                    arg.getDeviceTestResult().getTestResults().getFailed().toString());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                assertEquals("[]", message.getDeviceTestResult().getTestResults().getPassed().
                        toString());
                assertEquals("[BH-A]", message.getDeviceTestResult().getTestResults().getFailed().
                        toString());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));

    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key and device has grade results")
    void testOnGettingGradeFromDeviceTestResults() throws MqttException, IOException {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");
        device.setGrade("CCC");

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(mqttClient.isConnected()).thenReturn(true);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        AndroidSysLogKey androidSysLogKey = AndroidSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        deviceTestResult.setGradeResults("GR");
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(
                new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device))).thenReturn(deviceTestResultStatus);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().build());
        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, androidSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(testResultsService).getTestResults(eq(device));
        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            // verify the grade results in the test result object got set with grade results
            assertEquals("GR", arg.getDeviceTestResult().getGradeResults());
            Assertions.assertEquals(deviceTestResult.getTestResults(), arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                // verify the grade results in the test result object got set with grade results
                assertEquals("GR", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key and get device grades from the device object")
    void testOnGettingGradeFromDeviceObject() throws MqttException, IOException {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");
        device.setGrade("CCC");

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);
        when(mqttClient.isConnected()).thenReturn(true);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        AndroidSysLogKey androidSysLogKey = AndroidSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        when(testResultsService.getTestResults(eq(device))).thenReturn(deviceTestResultStatus);
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().build());
        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, androidSysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(testResultsService).getTestResults(eq(device));
        verify(deviceTestResultDBService, never()).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            // verify the grade results in the test result object got set with the grade in the device object
            assertEquals("CCC", arg.getDeviceTestResult().getGradeResults());
            Assertions.assertEquals(deviceTestResult.getTestResults(), arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                // verify the grade results in the test result object got set with the grade in the device object
                assertEquals("CCC", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    @DisplayName("Test Parse log on getting Start Test key and get device grades from DB")
    void testOnGettingGradeFromDB() throws MqttException, IOException {
        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");

        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getLicenseId()).thenReturn(111);


        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        AndroidSysLogKey sysLogKey = AndroidSysLogKey.START_READING_TEST_RESULTS;
        String sysLogResponse = "rhkg38yw4w-The test results are ready to be retrieved.";
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        CosmeticsResults cosmeticsResults = new CosmeticsResults();
        cosmeticsResults.setPassed("Is screen ok?-yes");
        cosmeticsResults.setFailed("Does phone look good-no");

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(
                new ArrayList<>(List.of("failed")));
        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(List.of("passed")));
        when(testResultsService.getTestResults(eq(device))).thenReturn(deviceTestResultStatus);
        when(deviceTestResultDBService.getDeviceGrade(eq("123"), eq(device.getId()))).thenReturn("C");

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, sysLogKey, sysLogResponse);

        listener.onEvent(event);

        verify(testResultsService).getTestResults(eq(device));
        verify(deviceTestResultDBService).getDeviceGrade(anyString(), anyString());
        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            deviceTestResult.setGradeResults("C");
            Assertions.assertEquals(deviceTestResult.getTestResults(), arg.getDeviceTestResult().getTestResults());
            return true;
        }));
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertNotNull(message.getDeviceTestResult());
                assertEquals("C", message.getDeviceTestResult().getGradeResults());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    @DisplayName("Test for getting fingerprint FINGER_PRINT_3 syslog key for samsung 's' series device")
    public void testOnGettingFingerPrintKeys1() {
        // Setting samsung 's' series model
        device.setModelNo("SM-G950");
        device.setMake("samsung");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.FINGER_PRINT_3, "abc");
        listener.onEvent(event);

        verify(fingerPrintService).runFingerPrintActivity(device.getId());
    }

    @Test
    @DisplayName("Test for getting pc utility device info start syslog key")
    public void testOnGettingPcUtilityDeviceInfoStartKey() {
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.DEVICE_INFO_START_HASH, "abc");
        listener.onEvent(event);

        Assertions.assertTrue(device.isPcUtilityDeviceInfoRetrieved());
    }

    @Test
    @DisplayName("Test for getting pc utility device info end syslog key")
    public void testOnGettingPcUtilityDeviceInfoEndKey() {
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.DEVICE_INFO_END_HASH, "abc");
        listener.onEvent(event);

        Assertions.assertTrue(device.isPcUtilityDeviceInfoRetrieved());
    }

    @Test
    @DisplayName("Test for getting fingerprint FINGER_PRINT_SCAN_EFFECT syslog key for samsung 's' series device")
    public void testOnGettingFingerPrintKeys2() {
        // Setting samsung 's' series model
        device.setModelNo("SM-G950");
        device.setMake("samsung");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.FINGER_PRINT_SCAN_EFFECT, "abc");
        listener.onEvent(event);

        verify(fingerPrintService).runFingerPrintMainActivity(device.getId(),
                AndroidSysLogKey.FINGER_PRINT_SCAN_EFFECT.getKey());
    }

    @Test
    @DisplayName("Test for getting fingerprint syslog key for samsung NON_MEID_SUPPORTED_MODELS device")
    public void testOnGettingFingerPrintKeys3() {
        device.setModelNo("SM-N970W");
        device.setMake("samsung");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.START_FINGER_PRINT_PIN, "abc");
        listener.onEvent(event);

        verify(fingerPrintService).runFingerPrintPasswordAutomationForUnderDisplay(device.getId());
    }

    @Test
    @DisplayName("Test for getting fingerprint syslog key for other samsung device")
    public void testOnGettingFingerPrintKeys4() {
        device.setModelNo("A-series");
        device.setMake("samsung");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.FINGER_PRINT_5, "abc");
        listener.onEvent(event);

        verify(fingerPrintService).performFingerPrintOperationForSamsung(device, AndroidSysLogKey.FINGER_PRINT_5);
    }

    @Test
    @DisplayName("Test for getting fingerprint syslog key for google device")
    public void testOnGettingFingerPrintKeys5() {
        // Setting samsung 's' series model
        device.setModelNo("A-series");
        device.setMake("google");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event =
                new AndroidParseLogStreamEvent(listener, device, AndroidSysLogKey.FINGER_PRINT_10, "abc");
        listener.onEvent(event);

        verify(fingerPrintService).performFingerPrintOperationForOtherDevices(device, AndroidSysLogKey.FINGER_PRINT_10);
    }

    @Test
    @DisplayName("Test for getting alert slider syslog key for OnePlus devices")
    public void testOnGettingAlertSliderKey() {
        device.setModelNo("OP-5300");
        device.setMake("Oneplus");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent resetTestEvent = new AndroidParseLogStreamEvent(
                listener, device, AndroidSysLogKey.START_READING_ALERT_SLIDER, "-ButtonTests");
        listener.onEvent(resetTestEvent);

        final AndroidParseLogStreamEvent buttonUpEvent = new AndroidParseLogStreamEvent(
                listener, device, AndroidSysLogKey.ALERT_SLIDER_UP_BY_ASVP, "abc");
        listener.onEvent(buttonUpEvent);


        final AndroidParseLogStreamEvent buttonMiddleEvent = new AndroidParseLogStreamEvent(
                listener, device, AndroidSysLogKey.ALERT_SLIDER_MID_BY_ASVP, "abc");
        listener.onEvent(buttonMiddleEvent);


        final AndroidParseLogStreamEvent buttonDownEvent = new AndroidParseLogStreamEvent(
                listener, device, AndroidSysLogKey.ALERT_SLIDER_DOWN_BY_ASVP, "abc");
        listener.onEvent(buttonDownEvent);


        verify(alertSliderService).performAlertSliderOperations(device, AndroidSysLogKey.ALERT_SLIDER_UP_BY_ASVP);
        verify(alertSliderService).performAlertSliderOperations(device, AndroidSysLogKey.ALERT_SLIDER_MID_BY_ASVP);
        verify(alertSliderService).performAlertSliderOperations(device, AndroidSysLogKey.ALERT_SLIDER_DOWN_BY_ASVP);
    }

    @Test
    @DisplayName("Test for getting zflip display syslog key for Samsung devices")
    public void testOnGettingZFlipDisplayTestKey() {
        device.setModelNo("SM-F711B");
        device.setMake("Samsung");

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent zFlipLCDTestEvent = new AndroidParseLogStreamEvent(listener, device,
                AndroidSysLogKey.START_Z_FLIP_LCD_TEST, "OpenFlipLCDTest--OpenFlipLCDTest");
        listener.onEvent(zFlipLCDTestEvent);

        final AndroidParseLogStreamEvent zFlipDisplayTestEvent = new AndroidParseLogStreamEvent(listener, device,
                AndroidSysLogKey.START_Z_FLIP_DIGITIZER_TEST, "OpenFlipDigitizerTest--OpenFlipDigitizerTest");
        listener.onEvent(zFlipDisplayTestEvent);

        verify(zFlipDisplayAutomationService).runZflipAutomation(device, AndroidSysLogKey.START_Z_FLIP_LCD_TEST);
        verify(zFlipDisplayAutomationService).runZflipAutomation(device, AndroidSysLogKey.START_Z_FLIP_DIGITIZER_TEST);
    }

    @Test
    public void shouldTriggerSuccessEventOnEraseSuccess() throws Exception {
        String sysLogResponse = "UGhvbmVDaGVja0VyYXNlclN0YXJ0";
        AndroidSysLogKey androidSysLogKey = AndroidSysLogKey.START_READING_ERASE_STATUS;

        Map<String, Boolean> parsedResponse = new HashMap<>();
        parsedResponse.put(EraseOperation.FACTORY_RESET.getValue(), true);

        when(androidEraseOperationsParser.parse(sysLogResponse)).thenReturn(parsedResponse);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        final AndroidParseLogStreamEvent event = new AndroidParseLogStreamEvent(listener, device,
                androidSysLogKey, sysLogResponse);
        listener.onEvent(event);

        verify(eventPublisher).publishEvent(any(AndroidEraseSuccessEvent.class));
        verify(mqttClient, never()).publish(any(), any());
    }

    @Test
    public void shouldNotTriggerSuccessEventOnEraseFailure() throws Exception {
        String sysLogResponse = "UGhvbmVDaGVja0VyYXNlclN0YXJ0";
        AndroidSysLogKey androidSysLogKey = AndroidSysLogKey.START_READING_ERASE_STATUS;

        Map<String, Boolean> parsedResponse = new HashMap<>();
        parsedResponse.put(EraseOperation.FACTORY_RESET.getValue(), false);

        when(androidEraseOperationsParser.parse(sysLogResponse)).thenReturn(parsedResponse);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(mqttClient.isConnected()).thenReturn(true);

        final AndroidParseLogStreamEvent event = new AndroidParseLogStreamEvent(listener, device,
                androidSysLogKey, sysLogResponse);
        listener.onEvent(event);

        verify(eventPublisher, never()).publishEvent(any(AndroidEraseSuccessEvent.class));

        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "erase", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                AndroidEraseResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        AndroidEraseResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(DeviceEraseStatus.ERASE_FAILED_UNKNOWN, message.getEraseStatus());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }
}
