package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.dao.service.EraserInfoDBService;
import com.phonecheck.device.connection.ios.IosDeviceRestoreService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosDeviceRestoreResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosDeviceRestoreResponseMessage;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.status.RestoreResponseCode;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.parser.device.DeviceOsVersionParser;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosRestoreResponseListenerTest {

    @InjectMocks
    private IosRestoreResponseListener restoreResponseListener;

    @Mock
    private InMemoryStore inMemoryStore;

    @Mock
    private EraserInfoDBService eraserInfoDBService;

    @Mock
    private DeviceInfoDBService deviceInfoDBService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private IMqttAsyncClient mqttClient;

    private IosDevice device;

    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;

    @Mock
    private IosDeviceRestoreService iosDeviceRestoreService;
    @Mock
    private DeviceOsVersionParser deviceOsVersionParser;

    @Mock
    private DeviceDataConversionAndExportService deviceDataConversionAndExportService;

    @Mock
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;

    @BeforeEach
    void setup() {
        restoreResponseListener = new IosRestoreResponseListener(
                mqttClient, objectMapper, inMemoryStore, eraserInfoDBService,
                deviceInfoDBService, deviceConnectionTracker, iosDeviceRestoreService, deviceOsVersionParser,
                deviceDataConversionAndExportService, cloudDeviceDataSyncService);
        when(mqttClient.isConnected()).thenReturn(true);

        device = new IosDevice();
        device.setId("TestID");
        device.setSerial("serial1234");
        device.setFirmware("/");
        device.setModel("iPhone 6s");
        device.setRestoreStartTime(System.currentTimeMillis());
    }

    @Test
    public void testOnEvent() throws Exception {
        Transaction tran = new Transaction();
        device.setRestoreStartTime(System.currentTimeMillis() - 100L);
        when(inMemoryStore.getTransaction()).thenReturn(tran);
        when(deviceConnectionTracker.getDevice("TestID")).thenReturn(device);

        IosDeviceRestoreResponseEvent event = new IosDeviceRestoreResponseEvent(this,
                device,
                DeviceRestoreStatus.RESTORE_COMPLETED,
                RestoreResponseCode.RESTORE_COMPLETED, 0L);
        restoreResponseListener.onEvent(event);

        verify(deviceInfoDBService).updateRestoreCode(String.valueOf(tran.getTransactionId()),
                device, null);
        verify(eraserInfoDBService).insertEraserInfo(device);

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "restore", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        IosDeviceRestoreResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                IosDeviceRestoreResponseMessage.class);
        Assertions.assertEquals(DeviceRestoreStatus.RESTORE_COMPLETED, outputMsg.getRestoreStatus());
        Assertions.assertEquals(RestoreResponseCode.RESTORE_COMPLETED, outputMsg.getResponseCode());
    }
}
