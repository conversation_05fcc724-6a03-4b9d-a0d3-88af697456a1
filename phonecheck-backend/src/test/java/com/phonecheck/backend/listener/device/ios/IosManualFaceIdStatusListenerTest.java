package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosManualFaceIdStatusEvent;
import com.phonecheck.model.status.WorkingStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosManualFaceIdStatusListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;

    @Mock
    private IosManualFaceIdStatusEvent event;

    private IosDevice device;

    @Mock
    private IosDevice deviceInTracker;

    private IosManualFaceIdStatusListener listener;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("id");
        device.setSerial("serial");
        device.setManualFaceId(true);
        device.setFaceIdSensor(WorkingStatus.YES);
        listener = new IosManualFaceIdStatusListener(mqttClient, objectMapper, deviceConnectionTracker);

    }

    @Test
    void testOnNormalEvent() {
        when(event.getDevice()).thenReturn(device);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(deviceInTracker);

        listener.onEvent(event);

        verify(deviceInTracker).setManualFaceId(true);
        verify(deviceInTracker).setFaceIdSensor(device.getFaceIdSensor());
    }
}
