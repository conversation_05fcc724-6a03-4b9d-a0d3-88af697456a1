package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.app.profile.ConfigProfileService;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.service.lookup.BatteryInfoDBLookupService;
import com.phonecheck.device.results.IosTestResultsService;
import com.phonecheck.info.DeviceTestPlanUtil;
import com.phonecheck.info.ios.IosBatteryInfoService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosBatteryInfoFailureEvent;
import com.phonecheck.model.event.device.ios.IosBatteryInfoNeededEvent;
import com.phonecheck.model.event.device.ios.IosBatteryInfoSuccessEvent;
import com.phonecheck.model.ios.IosConfigProfile;
import com.phonecheck.model.ios.ProcessName;
import com.phonecheck.model.status.MountStatus;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IosBatteryInfoNeededListenerTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private ConfigProfileService configProfileService;
    @Mock
    private BatteryInfoDBLookupService batteryInfoDBLookupService;
    @Mock
    private IosBatteryInfoService iosBatteryInfoService;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private IosPairingUtil iosPairingUtil;
    @Mock
    private IosTestResultsService iosTestResultsService;
    @Mock
    private DeviceTestPlanUtil deviceTestPlanUtil;
    private IosDevice device;
    private IosBatteryInfoNeededListener listener;

    private static final String BATTERY_PROFILE_PATH = "batteryLife.mobileconfig";

    @BeforeEach
    void beforeEach() {
        device = new IosDevice();
        device.setId("123");
        device.setSerial("serial");

        listener = new IosBatteryInfoNeededListener(mqttClient, objectMapper,
                eventPublisher, configProfileService,
                batteryInfoDBLookupService, iosBatteryInfoService, iosDeviceInfoService,
                deviceConnectionTracker, inMemoryStore, timerLoggerUtil, iosPairingUtil, iosTestResultsService,
                deviceTestPlanUtil);

        when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);
    }

    @Test
    public void testWhenBatteryProfilePushed1() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        device.setBatteryStateHealth(80);
        device.setOsMajorVersion(14.4f);
        device.setProductType("iphone");

        File batteryProfile = new File(BATTERY_PROFILE_PATH);
        batteryProfile.createNewFile();

        when(iosBatteryInfoService.getBatteryLifeProfileFile()).thenReturn(batteryProfile);
        when(configProfileService.iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY)))
                .thenReturn(true);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosBatteryInfoService, times(1))
                .getBatteryLifeProfileFile();
        verify(configProfileService, times(1))
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));

        batteryProfile.delete();
    }

    @Test
    public void testWhenBatteryProfilePushed2() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        device.setBatteryStateHealth(0);
        device.setOsMajorVersion(14.4f);
        device.setProductType("iphone");

        File batteryProfile = new File(BATTERY_PROFILE_PATH);
        batteryProfile.createNewFile();

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(iosBatteryInfoService.getBatteryLifeProfileFile()).thenReturn(batteryProfile);
        when(configProfileService.iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY)))
                .thenReturn(true);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosBatteryInfoService, times(5))
                .getBatteryLifeProfileFile();
        verify(configProfileService, times(5))
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));
        verify(configProfileService, times(5))
                .removeProfile(any(IosDevice.class), eq(IosConfigProfile.BATTERY.getId()));

        batteryProfile.delete();
    }

    @Test
    public void testWhenBatteryProfileNotPushed1() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        device.setOsMajorVersion(14.4f);
        device.setProductType("iphone");

        File batteryProfile = new File(BATTERY_PROFILE_PATH);
        batteryProfile.createNewFile();

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(iosBatteryInfoService.getBatteryLifeProfileFile()).thenReturn(batteryProfile);
        when(configProfileService.iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY)))
                .thenReturn(false);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosBatteryInfoService, times(5))
                .getBatteryLifeProfileFile();
        verify(configProfileService, times(5))
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));

        batteryProfile.delete();
    }

    @Test
    public void testWhenBatteryProfileNotPushed2() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        device.setOsMajorVersion(14.4f);
        device.setProductType("iphone");

        File batteryProfile = new File(BATTERY_PROFILE_PATH);

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(iosBatteryInfoService.getBatteryLifeProfileFile()).thenReturn(batteryProfile);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosBatteryInfoService, times(1))
                .getBatteryLifeProfileFile();
        verify(configProfileService, never())
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));
    }

    @Test
    public void testPowerProcessNotKilled() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.DONE);
        device.setImageMountStatus(MountStatus.SUCCESS);
        device.setOsMajorVersion(14.4f);
        device.setProductType("iphone");

        Map<ProcessName, String> processNameIdMap = new HashMap<>();
        processNameIdMap.put(ProcessName.POWERD, "2222");

        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(iosDeviceInfoService.getDeviceProcessIds(eq(device), eq(ProcessName.POWERD))).thenReturn(processNameIdMap);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosDeviceInfoService, times(5))
                .getDeviceProcessIds(eq(device), eq(ProcessName.POWERD));
        verify(iosDeviceInfoService, times(5))
                .killProcess(eq(device), eq(processNameIdMap.get(ProcessName.POWERD)));
    }

    @Test
    public void testPowerProcessKilled() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.DONE);
        device.setImageMountStatus(MountStatus.SUCCESS);
        device.setBatteryStateHealth(80);
        device.setOsMajorVersion(14.4f);
        device.setProductType("iphone");

        Map<ProcessName, String> processNameIdMap = new HashMap<>();
        processNameIdMap.put(ProcessName.POWERD, "2222");

        when(iosDeviceInfoService.getDeviceProcessIds(eq(device), eq(ProcessName.POWERD))).thenReturn(processNameIdMap);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosDeviceInfoService, times(1))
                .getDeviceProcessIds(eq(device), eq(ProcessName.POWERD));
        verify(iosDeviceInfoService, times(1))
                .killProcess(eq(device), eq(processNameIdMap.get(ProcessName.POWERD)));
    }

    @Test
    public void testOnEvent() throws IOException {
        BatteryInfo batteryInfo = new BatteryInfo();
        batteryInfo.setModel("Battery Model");
        batteryInfo.setHealthPercentage(78);

        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(batteryInfo);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        assertEquals(batteryInfo, device.getBatteryInfo());
        verify(iosBatteryInfoService).getBatteryInfoViaIoReg(eq(device));
        verify(eventPublisher).publishEvent(isA(IosBatteryInfoSuccessEvent.class));
    }

    @Test
    public void testOnEventForIpadOnHello() throws IOException, InterruptedException {
        device.setSetupDoneStatus(SetupDoneStatus.NOT_DONE);
        device.setBatteryStateHealth(80);
        device.setOsMajorVersion(14.4f);
        device.setProductType("ipad");

        BatteryInfo batteryInfo = new BatteryInfo();
        batteryInfo.setOemHealthPercentage(83);
        batteryInfo.setModel("Battery Model");

        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(batteryInfo);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        Thread.sleep(1000);

        assertNotNull(device.getBatteryInfo());
        verify(iosBatteryInfoService, never())
                .getBatteryLifeProfileFile();
        verify(configProfileService, never())
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));
        verify(configProfileService, never())
                .removeProfile(any(IosDevice.class), eq(IosConfigProfile.BATTERY.getId()));
        verify(iosDeviceInfoService, never())
                .getDeviceProcessIds(eq(device), eq(ProcessName.POWERD));
        verify(iosDeviceInfoService, never())
                .killProcess(eq(device), any());
    }

    @Test
    public void testOnEventForIpadOnHome() throws IOException, InterruptedException {
        device.setSetupDoneStatus(SetupDoneStatus.DONE);
        device.setBatteryStateHealth(85);
        device.setOsMajorVersion(14.4f);
        device.setProductType("ipad");

        BatteryInfo batteryInfo = new BatteryInfo();
        batteryInfo.setHealthPercentage(83);
        batteryInfo.setOemHealthPercentage(83);
        batteryInfo.setModel("Battery Model");

        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(batteryInfo);
        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        Thread.sleep(1000);

        assertNotNull(device.getBatteryInfo());
        assertEquals(83, device.getBatteryInfo().getHealthPercentage());

        verify(iosBatteryInfoService, never())
                .getBatteryLifeProfileFile();
        verify(configProfileService, never())
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));
        verify(configProfileService, never())
                .removeProfile(any(IosDevice.class), eq(IosConfigProfile.BATTERY.getId()));
        verify(iosDeviceInfoService, never())
                .getDeviceProcessIds(eq(device), eq(ProcessName.POWERD));
        verify(iosDeviceInfoService, never())
                .killProcess(eq(device), any());
    }

    @Test
    public void testLockdownErrorAndDevicePaired() throws Exception {

        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(null);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(device)).thenReturn(PairStatus.PAIRED);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosPairingUtil, times(1)).checkAndNotifyUiIfNotPaired(device);
        verify(iosPairingUtil, times(1)).showReconnectPopup(device);
        verify(eventPublisher).publishEvent(any(IosBatteryInfoFailureEvent.class));
    }

    @Test
    public void testLockdownErrorAndDeviceNotPaired() throws Exception {
        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(null);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(device)).thenReturn(PairStatus.FAILED_NO_DEVICE);

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosPairingUtil, times(1)).checkAndNotifyUiIfNotPaired(device);
        verify(iosPairingUtil, times(1)).showReconnectPopup(device);
        verify(eventPublisher).publishEvent(any(IosBatteryInfoFailureEvent.class));
    }

    @Test
    public void testWaitForBatteryHealthSuccessEvent() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.DONE);
        device.setBatteryStateHealth(0);
        device.setPreviousBatteryStateHealth(0);
        device.setOsMajorVersion(18.4f);
        device.setImageMountStatus(MountStatus.FAILED_UNKNOWN);
        device.setProductType("iphone");

        BatteryInfo batteryInfo = new BatteryInfo();
        batteryInfo.setHealthPercentage(100);

        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(batteryInfo);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosBatteryInfoService, times(0))
                .getBatteryLifeProfileFile();
        verify(configProfileService, times(0))
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));
        verify(configProfileService, times(0))
                .removeProfile(any(IosDevice.class), eq(IosConfigProfile.BATTERY.getId()));
        verify(eventPublisher).publishEvent(any(IosBatteryInfoSuccessEvent.class));
    }

    @Test
    public void testWaitForBatteryHealthFailedEvent() throws IOException {
        device.setSetupDoneStatus(SetupDoneStatus.DONE);
        device.setBatteryStateHealth(0);
        device.setPreviousBatteryStateHealth(0);
        device.setOsMajorVersion(18.4f);
        device.setImageMountStatus(MountStatus.FAILED_UNKNOWN);
        device.setProductType("iphone");

        BatteryInfo batteryInfo = new BatteryInfo();

        when(iosBatteryInfoService.getBatteryInfoViaIoReg(any(IosDevice.class))).thenReturn(batteryInfo);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());

        listener.onEvent(new IosBatteryInfoNeededEvent(listener, device));

        verify(iosBatteryInfoService, times(0))
                .getBatteryLifeProfileFile();
        verify(configProfileService, times(0))
                .iosPushProfile(any(IosDevice.class), any(File.class), eq(IosConfigProfile.BATTERY));
        verify(configProfileService, times(0))
                .removeProfile(any(IosDevice.class), eq(IosConfigProfile.BATTERY.getId()));
        verify(eventPublisher).publishEvent(any(IosBatteryInfoFailureEvent.class));
    }
}
