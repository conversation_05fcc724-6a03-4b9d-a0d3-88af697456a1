package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.util.ModelToMakeConverter;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.InitialConnectionStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosConnectedEvent;
import com.phonecheck.model.ios.IosProperty;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosMqttConnectionMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosDeviceConnectedListenerTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private ModelToMakeConverter modelToMakeConverter;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    private Transaction transaction;

    private IosDevice device;
    private IosDeviceConnectedListener listener;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setProductType("iPhone5,3");
        device.setModel("iPhone 5C");
        device.setProductVersion("10.0.0");
        device.setSerial("serial");
        device.setPortNumber(1);
        device.setEcid("abcd");

        transaction = new Transaction();
        transaction.setTransactionId(123);

        listener = new IosDeviceConnectedListener(mqttClient, objectMapper, stageUpdater,
                modelToMakeConverter, deviceConnectionTracker, iosDeviceInfoService,
                inMemoryStore, timerLoggerUtil, eventPublisher);
    }

    @Test
    void testOnEventRegularConnection() {
        final IosConnectedEvent event = new IosConnectedEvent(listener, device);

        IosMqttConnectionMessage expectedMessage = new IosMqttConnectionMessage();
        expectedMessage.setId(device.getId());
        expectedMessage.setSerial(device.getSerial());
        expectedMessage.setUsbMode(device.isUsbMode());
        expectedMessage.setProductVersion(device.getProductVersion());
        expectedMessage.setProductType(device.getProductType());
        expectedMessage.setEcid(device.getEcid());

        try {
            when(iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_TYPE))
                    .thenReturn("iPhone5,3");
            when(iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_VERSION))
                    .thenReturn("10.0.0");
            when(iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.ECID))
                    .thenReturn("abcd");
            when(modelToMakeConverter.convert(any())).thenReturn("Apple");
            when(inMemoryStore.getLicenseId()).thenReturn(123);
            when(inMemoryStore.getTransaction()).thenReturn(transaction);
            when(mqttClient.isConnected()).thenReturn(true);
            listener.onEvent(event);

            verify(iosDeviceInfoService).getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_TYPE);
            verify(iosDeviceInfoService).getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_VERSION);
            verify(iosDeviceInfoService).getPropValueByKeyWithRetries(device, IosProperty.ECID);

            // Make sure the initial "device connected" MQTT message was published
            verify(mqttClient).publish(argThat(topic -> {
                assertNotNull(topic);
                assertEquals(TopicBuilder.build(device, "connected"), topic);
                return true;
            }), argThat(mqttMessage -> {
                assertNotNull(mqttMessage);
                assertNotNull(mqttMessage.getPayload());
                try {
                    final IosMqttConnectionMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                            IosMqttConnectionMessage.class);
                    assertEquals(expectedMessage.getId(), message.getId());
                    assertEquals(expectedMessage.getSerial(), message.getSerial());
                    assertEquals(expectedMessage.isUsbMode(), message.isUsbMode());
                    assertEquals(expectedMessage.getProductVersion(), message.getProductVersion());
                    assertEquals(expectedMessage.getProductType(), message.getProductType());
                    assertEquals(expectedMessage.getEcid(), message.getEcid());
                } catch (IOException e) {
                    return false;
                }

                return true;
            }));

            verify(stageUpdater).updateStage(argThat((InitialConnectionStage stage) -> {
                assertNotNull(stage);
                assertEquals(device.getId(), stage.getId());
                assertEquals(device.getDeviceType(), stage.getDeviceType());
                assertEquals("Apple", stage.getMake());
                assertEquals(device.getProductVersion(), stage.getProductVersion());
                assertEquals(device.getEcid(), stage.getLicenseIdentifier());
                assertEquals("123", stage.getLicenseId());
                return true;
            }));
            verify(deviceConnectionTracker).deviceConnected(any(IosDevice.class));
            Assertions.assertEquals("iPhone5,3", device.getProductType());
            Assertions.assertEquals("10.0.0", device.getProductVersion());
            Assertions.assertEquals("abcd", device.getEcid());

        } catch (IOException | MqttException e) {
            Assertions.fail(e);
        }
    }

    @Test
    void testOnEventNotOnHelloConnection() {
        final IosConnectedEvent event = new IosConnectedEvent(listener, device);

        IosMqttConnectionMessage expectedMessage = new IosMqttConnectionMessage();
        expectedMessage.setId(device.getId());
        expectedMessage.setSerial(device.getSerial());
        expectedMessage.setUsbMode(device.isUsbMode());
        expectedMessage.setProductVersion(device.getProductVersion());
        expectedMessage.setProductType(device.getProductType());
        expectedMessage.setEcid(device.getEcid());

        try {
            when(iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_TYPE))
                    .thenReturn("iPhone5,3");
            when(iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_VERSION))
                    .thenReturn("10.0.0");
            when(iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.ECID))
                    .thenReturn("abcd");
            when(modelToMakeConverter.convert(any())).thenReturn("Apple");
            when(inMemoryStore.getLicenseId()).thenReturn(123);
            when(inMemoryStore.getTransaction()).thenReturn(transaction);
            when(mqttClient.isConnected()).thenReturn(true);
            listener.onEvent(event);

            verify(iosDeviceInfoService).getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_TYPE);
            verify(iosDeviceInfoService).getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_VERSION);
            verify(iosDeviceInfoService).getPropValueByKeyWithRetries(device, IosProperty.ECID);

            // Make sure the initial "device connected" MQTT message was published
            verify(mqttClient).publish(argThat(topic -> {
                assertNotNull(topic);
                assertEquals(TopicBuilder.build(device, "connected"), topic);
                return true;
            }), argThat(mqttMessage -> {
                assertNotNull(mqttMessage);
                assertNotNull(mqttMessage.getPayload());
                try {
                    final IosMqttConnectionMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                            IosMqttConnectionMessage.class);
                    assertEquals(expectedMessage.getId(), message.getId());
                    assertEquals(expectedMessage.getSerial(), message.getSerial());
                    assertEquals(expectedMessage.isUsbMode(), message.isUsbMode());
                    assertEquals(expectedMessage.getProductVersion(), message.getProductVersion());
                    assertEquals(expectedMessage.getProductType(), message.getProductType());
                    assertEquals(expectedMessage.getEcid(), message.getEcid());
                } catch (IOException e) {
                    return false;
                }

                return true;
            }));

            verify(stageUpdater).updateStage(argThat((InitialConnectionStage stage) -> {
                assertNotNull(stage);
                assertEquals(device.getId(), stage.getId());
                assertEquals(device.getDeviceType(), stage.getDeviceType());
                assertEquals("Apple", stage.getMake());
                assertEquals(device.getProductVersion(), stage.getProductVersion());
                assertEquals(device.getEcid(), stage.getLicenseIdentifier());
                assertEquals("123", stage.getLicenseId());
                return true;
            }));
            verify(deviceConnectionTracker).deviceConnected(any(IosDevice.class));
            Assertions.assertEquals("iPhone5,3", device.getProductType());
            Assertions.assertEquals("10.0.0", device.getProductVersion());
            Assertions.assertEquals("abcd", device.getEcid());

        } catch (IOException | MqttException e) {
            Assertions.fail(e);
        }
    }
}
