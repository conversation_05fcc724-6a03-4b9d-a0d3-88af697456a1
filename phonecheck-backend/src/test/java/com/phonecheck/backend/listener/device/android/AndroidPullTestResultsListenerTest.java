package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceDataConversionAndExportService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.device.android.wifi.AndroidWifiAutoDisconnectService;
import com.phonecheck.device.results.AndroidTestResultsService;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.stage.AppTestingDoneStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidPullTestResultsEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.test.TestResults;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.LocalizationService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidPullTestResultsListenerTest {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private AndroidTestResultsService testResultsService;
    @Mock
    private DeviceTestResultDBService deviceTestResultDBService;
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private DeviceAutomationQueueService automationQueueService;
    @Mock
    private AndroidWifiAutoDisconnectService androidWifiAutoDisconnectService;
    @Mock
    private LocalizationService localizationService;
    @Mock
    private DeviceDataConversionAndExportService deviceDataConversionAndExportService;
    @Mock
    private VendorCriteriaService vendorCriteriaService;
    private InMemoryStore inMemoryStore;
    private AndroidPullTestResultsListener listener;
    private AndroidDevice device;


    @BeforeEach
    void beforeEach() {
        inMemoryStore = new InMemoryStore();
        inMemoryStore.setLicenseId(111);
        inMemoryStore.setTransaction(Transaction.builder().transactionId(123).build());
        device = new AndroidDevice();
        device.setId("12345678");
        device.setSerial("8989");
        device.setImei("123");
        device.setVendorName("Vendor");
        inMemoryStore.setUserName("userName");
        listener = new AndroidPullTestResultsListener(mqttClient, objectMapper, eventPublisher, testResultsService,
                deviceTestResultDBService,
                inMemoryStore, deviceConnectionTracker, stageUpdater,
                automationQueueService, androidWifiAutoDisconnectService, localizationService,
                deviceDataConversionAndExportService, vendorCriteriaService);
    }

    @Test
    @DisplayName("Test to find no app test results")
    void testFailedPullResults() throws IOException, MqttException {
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(mqttClient.isConnected()).thenReturn(true);
        listener.onEvent(new AndroidPullTestResultsEvent(listener, device));

        verify(testResultsService).getTestResultsFromFile(any(AndroidDevice.class));

        verify(stageUpdater, never()).update(any(AppTestingDoneStage.class));

        final String topic = TopicBuilder.build(device, "post-error");
        verify(mqttClient).publish(eq(topic), any(MqttMessage.class));
        verify(androidWifiAutoDisconnectService, never()).turnOffWifi(any());
    }

    @Test
    @DisplayName("Test to fetch device test results from device")
    void testPullResultsFromDevice() throws IOException, MqttException {
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());
        deviceTestResult.setGradeResults("B+");
        when(mqttClient.isConnected()).thenReturn(true);
        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("android");
        device.setBatteryInfo(BatteryInfo.builder().healthPercentage(70).build());
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(testResultsService.getTestResultsFromFile(any(AndroidDevice.class)))
                .thenReturn(deviceTestResult);

        CloudCustomizationResponse.Rule healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(75).maximumCycle(1500).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1}).build();
        CloudCustomizationResponse.DeviceTest deviceLKTest = new CloudCustomizationResponse.DeviceTest("id",
                "Device Lock", "Both", "", "", "", 0);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .testPlan(new CloudCustomizationResponse.TestPlan(null, null,
                                List.of(deviceLKTest), "", List.of(), "", List.of(), ""))
                        .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().disconnect(true).build())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder().customizeBattery(true)
                                .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                                .build())
                        .build();
        inMemoryStore.setAssignedCloudCustomization(cloudCustomization);

        listener.onEvent(new AndroidPullTestResultsEvent(listener, device));

        Assertions.assertTrue(deviceTestResult.getTestResults()
                .getFailed().contains(InitialDefectKey.DEVICE_LOCK.getKey()));

        verify(testResultsService).getTestResultsFromFile(any(AndroidDevice.class));

        verify(stageUpdater).update(argThat((AppTestingDoneStage arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getId());
            Assertions.assertEquals(device.getSerial(), arg.getSerial());
            Assertions.assertEquals(device.getDeviceType(), arg.getDeviceType());
            Assertions.assertEquals("123", arg.getTransactionId());
            Assertions.assertEquals("111", arg.getLicenseId());
            Assertions.assertEquals("B+", arg.getDeviceTestResult().getGradeResults());
            Assertions.assertEquals("[BH-A, Device Lock]",
                    arg.getDeviceTestResult().getTestResults().getFailed().toString());
            Assertions.assertEquals("[]",
                    arg.getDeviceTestResult().getTestResults().getPassed().toString());
            return true;
        }));

        // Make sure the esn check response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "tested"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceTestedMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceTestedMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(deviceTestResult.getTestResults().getPassed(),
                        message.getDeviceTestResult().getTestResults().getPassed());
                Assertions.assertTrue(message.getDeviceTestResult().getTestResults()
                        .getFailed().contains(InitialDefectKey.DEVICE_LOCK.getKey()));
                assertNull(message.getDeviceTestResult().getBatteryResults());
                assertNull(message.getDeviceTestResult().getMicrophoneResults());
                assertNull(message.getDeviceTestResult().getCosmeticResults());
                assertEquals("[BH-A, Device Lock]", message.getDeviceTestResult().getTestResults().getFailed().
                        toString());
                assertEquals("[]", message.getDeviceTestResult().getTestResults().getPassed().
                        toString());
                assertEquals("B+", message.getDeviceTestResult().getGradeResults());
                assertEquals(device.getVendorName(), message.getVendorName());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
        verify(androidWifiAutoDisconnectService).turnOffWifi(device);
    }

    @Test
    public void testNoDeviceFound() throws MqttException, IOException {
        final AndroidDevice device = new AndroidDevice();
        device.setId("123");

        listener.onEvent(new AndroidPullTestResultsEvent(listener, device));

        verify(mqttClient, never()).publish(anyString(), any(MqttMessage.class));
        verify(testResultsService, never()).getTestResultsFromFile(any(AndroidDevice.class));
        verify(androidWifiAutoDisconnectService, never()).turnOffWifi(any());
    }
}
