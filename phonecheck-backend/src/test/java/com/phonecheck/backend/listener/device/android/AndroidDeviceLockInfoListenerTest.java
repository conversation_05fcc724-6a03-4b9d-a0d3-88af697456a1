package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.info.android.AndroidDeviceLockInfoService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidDeviceLockInfoEvent;
import com.phonecheck.model.mqtt.messages.DeviceLockStatusMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidDeviceLockInfoListenerTest {
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private AndroidDeviceLockInfoService androidDeviceLockInfoService;
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private InMemoryStore inMemoryStore;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidDeviceLockInfoListener frpInfoNeededListener;

    @BeforeEach
    void setUp() {
        frpInfoNeededListener = new AndroidDeviceLockInfoListener(
                objectMapper,
                mqttClient,
                deviceConnectionTracker,
                androidDeviceLockInfoService,
                stageUpdater,
                inMemoryStore
        );
    }

    @Test
    public void testOnEvent() throws IOException, MqttException {
        AndroidDevice device = new AndroidDevice();
        device.setId("testDeviceId");

        AndroidDevice androidDeviceInTracker = new AndroidDevice();
        androidDeviceInTracker.setAndroidConnectionMode(AndroidConnectionMode.ADB);
        androidDeviceInTracker.setId("testDeviceId");

        Transaction transaction = new Transaction();
        transaction.setTransactionId(123);
        when(mqttClient.isConnected()).thenReturn(true);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(androidDeviceInTracker);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder()
                .transactionId(1).build());

        AndroidDeviceLockInfoEvent event = new AndroidDeviceLockInfoEvent(frpInfoNeededListener, device);

        DeviceLock expectedLock = DeviceLock.ON;
        when(androidDeviceLockInfoService.getFrpStatusViaAdb(androidDeviceInTracker)).thenReturn(expectedLock);

        frpInfoNeededListener.onEvent(event);

        Mockito.verify(mqttClient).publish(anyString(), argThat(mqttMessage -> {
            try {
                DeviceLockStatusMessage message =
                        objectMapper.readValue(new String(mqttMessage.getPayload()),
                                DeviceLockStatusMessage.class);
                Assertions.assertEquals(device.getId(), message.getId());
                Assertions.assertEquals(expectedLock, message.getDeviceLock());
            } catch (JsonProcessingException e) {
                return false;
            }
            return true;
        }));
    }
}
