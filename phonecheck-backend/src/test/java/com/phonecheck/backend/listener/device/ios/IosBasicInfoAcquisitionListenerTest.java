package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudColorService;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.api.cloud.CloudDeviceService;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.service.lookup.ModelDBLookupService;
import com.phonecheck.dao.service.lookup.NetworkDBLookupService;
import com.phonecheck.device.results.file.push.DeviceFeaturesFileService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.cloudapi.DeviceFeaturesResponse;
import com.phonecheck.model.device.DiskSize;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosBasicInfoAcquisitionEvent;
import com.phonecheck.model.ios.IosProperty;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.status.SimStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.IosDeviceRamSizeMapper;
import com.phonecheck.model.util.MemoryUnit;
import com.phonecheck.parser.device.DeviceOsVersionParser;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IosBasicInfoAcquisitionListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;
    @Mock
    private DeviceFeaturesFileService deviceFeaturesFileService;
    @Mock
    private CloudDeviceService cloudDeviceService;
    @Mock
    private ModelDBLookupService modelDBLookupService;
    private final InMemoryStore inMemoryStore = new InMemoryStore();
    private IosDevice device;
    private Transaction transaction;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private DeviceOsVersionParser deviceOsVersionParser;
    @Mock
    private IosPairingUtil iosPairingUtil;
    @Mock
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @InjectMocks
    private IosBasicInfoAcquisitionListener listener;
    @Mock
    private NetworkDBLookupService networkDBLookupService;
    @Mock
    private IosDeviceRamSizeMapper deviceRamSizeMapper;
    @Mock
    private CloudColorService cloudColorService;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setProductType("iPhone11,8");
        device.setModel("Apple Iphone");
        device.setModelNo("MG4W");
        device.setImei("1111111");
        device.setImei2("888888");
        transaction = new Transaction();
        transaction.setTransactionId(123);
        inMemoryStore.setTransaction(transaction);
        inMemoryStore.setUserName("username");


        listener = new IosBasicInfoAcquisitionListener(mqttClient, objectMapper, deviceConnectionTracker,
                iosDeviceInfoService, deviceFeaturesFileService, modelDBLookupService,
                deviceOsVersionParser, inMemoryStore, iosPairingUtil, eventPublisher,
                networkDBLookupService, deviceRamSizeMapper, cloudColorService);
    }

    @Test
    @DisplayName("Test Basic Info collection acquisition listener")
    void testOnEvent1() {
        final IosBasicInfoAcquisitionEvent event = new IosBasicInfoAcquisitionEvent(listener, device);

        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);
        try {
            when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);

            final Map<IosProperty, String> properties = new HashMap<>();
            properties.put(IosProperty.IMEI, "1111111");
            properties.put(IosProperty.IMEI2, "888888");
            properties.put(IosProperty.MODEL_NUMBER, "Iphone XR Model");
            properties.put(IosProperty.SERIAL_NUMBER, "serial");
            properties.put(IosProperty.ACTIVATION_STATE, "Activate");
            properties.put(IosProperty.REGION_INFO, "LL/A");
            properties.put(IosProperty.SIM_STATUS, "kCTSIMSupportSIMStatusReady");
            properties.put(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER, "35350249706398");
            properties.put(IosProperty.WIFI_ADDRESS, "f4:be:ec:b7:98:23");
            properties.put(IosProperty.REGULATORY_MODEL_NUMBER, "rm");
            properties.put(IosProperty.SIM1_IS_EMBEDDED, "true");
            properties.put(IosProperty.SIM2_IS_EMBEDDED, "true");
            properties.put(IosProperty.RELEASE_TYPE, "Beta");
            properties.put(IosProperty.DEVICE_ENCLOSURE_COLOR, "1");
            properties.put(IosProperty.DEVICE_COLOR, "color");
            properties.put(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY, "simserial");

            Mockito.when(iosDeviceInfoService.getProperties(device,
                            false,
                            IosProperty.IMEI,
                            IosProperty.IMEI2,
                            IosProperty.MODEL_NUMBER,
                            IosProperty.SERIAL_NUMBER,
                            IosProperty.ACTIVATION_STATE,
                            IosProperty.REGION_INFO,
                            IosProperty.SIM_STATUS,
                            IosProperty.MOBILE_EQUIPMENT_IDENTIFIER,
                            IosProperty.WIFI_ADDRESS,
                            IosProperty.REGULATORY_MODEL_NUMBER,
                            IosProperty.SIM1_IS_EMBEDDED,
                            IosProperty.SIM2_IS_EMBEDDED,
                            IosProperty.BASEBAND_VERSION,
                            IosProperty.RELEASE_TYPE,
                            IosProperty.DEVICE_ENCLOSURE_COLOR,
                            IosProperty.DEVICE_COLOR,
                            IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY))
                    .thenReturn(properties);

            device.setDiskSize(new DiskSize(64L, MemoryUnit.GIGABYTES));
            device.setBatteryPercentage(80);
            device.setSetupDoneStatus(SetupDoneStatus.DONE);
            device.setRegulatoryModelNumber("rm");
            device.setProductVersion("15.1.1");

            when(iosDeviceInfoService.getDiskSize(device)).thenReturn(device.getDiskSize());
            when(deviceFeaturesFileService.getDeviceFeaturesForIos(anyString())).
                    thenReturn(DeviceFeaturesResponse.builder().modelName("Iphone XR Model").build());

            listener.onEvent(event);

            IosDevice iosDevice = (IosDevice) event.getDevice();
            assertEquals(properties.get(IosProperty.IMEI), iosDevice.getImei());
            assertEquals(properties.get(IosProperty.IMEI2), iosDevice.getImei2());
            assertEquals(properties.get(IosProperty.MODEL_NUMBER), iosDevice.getModel());
            assertEquals(properties.get(IosProperty.SERIAL_NUMBER), iosDevice.getSerial());
            assertEquals(properties.get(IosProperty.REGION_INFO), iosDevice.getRegionInfo());
            assertEquals(properties.get(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER), iosDevice.getMeid());
            assertEquals(properties.get(IosProperty.WIFI_ADDRESS), iosDevice.getWifiAddress());
            assertEquals(properties.get(IosProperty.REGULATORY_MODEL_NUMBER), iosDevice.getRegulatoryModelNumber());
            assertEquals(properties.get(IosProperty.RELEASE_TYPE), iosDevice.getReleaseType());
            assertEquals(properties.get(IosProperty.DEVICE_ENCLOSURE_COLOR), iosDevice.getColorCode());
            assertEquals(properties.get(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY), iosDevice.getSimSerial());
            assertEquals(SimStatus.READY, iosDevice.getSimStatus());
            assertTrue(iosDevice.isSim1Esim());
            assertTrue(iosDevice.isSim2Esim());


            verify(iosDeviceInfoService).getDiskSize(any());
            verify(deviceFeaturesFileService).getDeviceFeaturesForIos(anyString());
            verify(deviceFeaturesFileService)
                    .createDeviceFeaturesFileForIos(any(DeviceFeaturesResponse.class), anyString());

        } catch (IOException e) {
            fail(e);
        }
    }

    @Test
    @DisplayName("Test Basic Info collection acquisition listener")
    void testOnEvent2() throws InterruptedException {
        final IosBasicInfoAcquisitionEvent event = new IosBasicInfoAcquisitionEvent(listener, device);

        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);
        try {
            when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);

            device.setOsMajorVersion(16f);
            device.setSetupDoneStatus(SetupDoneStatus.DONE);
            device.setGuid("www.phonecheck.devicereqporthistory.com");

            final Map<IosProperty, String> properties = new HashMap<>();
            properties.put(IosProperty.IMEI, "1111111");
            properties.put(IosProperty.IMEI2, "888888");
            properties.put(IosProperty.MODEL_NUMBER, "Iphone XR Model");
            properties.put(IosProperty.SERIAL_NUMBER, "serial");
            properties.put(IosProperty.ACTIVATION_STATE, "Activate");
            properties.put(IosProperty.REGION_INFO, "LL/A");
            properties.put(IosProperty.SIM_STATUS, "kCTSIMSupportSIMStatusReady");
            properties.put(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER, "35350249706398");
            properties.put(IosProperty.WIFI_ADDRESS, "f4:be:ec:b7:98:23");
            properties.put(IosProperty.REGULATORY_MODEL_NUMBER, "rm");
            properties.put(IosProperty.SIM1_IS_EMBEDDED, "true");
            properties.put(IosProperty.SIM2_IS_EMBEDDED, "false");
            properties.put(IosProperty.RELEASE_TYPE, "Beta");
            properties.put(IosProperty.DEVICE_ENCLOSURE_COLOR, "red");
            properties.put(IosProperty.DEVICE_COLOR, "color");
            properties.put(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY, "simserial");

            Mockito.when(iosDeviceInfoService.getProperties(device,
                            false,
                            IosProperty.IMEI,
                            IosProperty.IMEI2,
                            IosProperty.MODEL_NUMBER,
                            IosProperty.SERIAL_NUMBER,
                            IosProperty.ACTIVATION_STATE,
                            IosProperty.REGION_INFO,
                            IosProperty.SIM_STATUS,
                            IosProperty.MOBILE_EQUIPMENT_IDENTIFIER,
                            IosProperty.WIFI_ADDRESS,
                            IosProperty.REGULATORY_MODEL_NUMBER,
                            IosProperty.SIM1_IS_EMBEDDED,
                            IosProperty.SIM2_IS_EMBEDDED,
                            IosProperty.BASEBAND_VERSION,
                            IosProperty.RELEASE_TYPE,
                            IosProperty.DEVICE_ENCLOSURE_COLOR,
                            IosProperty.DEVICE_COLOR,
                            IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY))
                    .thenReturn(properties);

            device.setDiskSize(new DiskSize(64L, MemoryUnit.GIGABYTES));
            device.setBatteryPercentage(80);
            device.setSetupDoneStatus(SetupDoneStatus.DONE);
            device.setRegulatoryModelNumber("rm");
            device.setProductVersion("15.1.1");

            when(iosDeviceInfoService.getDiskSize(device)).thenReturn(device.getDiskSize());
            when(deviceOsVersionParser.parse(any())).thenReturn(device.getOsMajorVersion());
            when(iosDeviceInfoService.getDeviceSetupDoneStatus(any())).thenReturn(device.getSetupDoneStatus());
            when(deviceFeaturesFileService.getDeviceFeaturesForIos(anyString())).
                    thenReturn(DeviceFeaturesResponse.builder().modelName("Iphone XR Model").build());

            listener.onEvent(event);

            Thread.sleep(4000);

            IosDevice iosDevice = (IosDevice) event.getDevice();
            assertEquals(properties.get(IosProperty.IMEI), iosDevice.getImei());
            assertEquals(properties.get(IosProperty.IMEI2), iosDevice.getImei2());
            assertEquals(properties.get(IosProperty.MODEL_NUMBER), iosDevice.getModel());
            assertEquals(properties.get(IosProperty.SERIAL_NUMBER), iosDevice.getSerial());
            assertEquals(properties.get(IosProperty.REGION_INFO), iosDevice.getRegionInfo());
            assertEquals(properties.get(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER), iosDevice.getMeid());
            assertEquals(properties.get(IosProperty.WIFI_ADDRESS), iosDevice.getWifiAddress());
            assertEquals(properties.get(IosProperty.REGULATORY_MODEL_NUMBER), iosDevice.getRegulatoryModelNumber());
            assertEquals(properties.get(IosProperty.RELEASE_TYPE), iosDevice.getReleaseType());
            assertEquals(properties.get(IosProperty.DEVICE_ENCLOSURE_COLOR), iosDevice.getColorCode());
            assertEquals(properties.get(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY), iosDevice.getSimSerial());
            assertEquals(SimStatus.READY, iosDevice.getSimStatus());
            assertTrue(iosDevice.isSim1Esim());
            assertFalse(iosDevice.isSim2Esim());
            assertEquals(device.getOsMajorVersion(), iosDevice.getOsMajorVersion());
            assertEquals(device.getSetupDoneStatus(), iosDevice.getSetupDoneStatus());
            assertEquals(device.getGuid(), iosDevice.getGuid());

            verify(iosDeviceInfoService).getDiskSize(any());
            verify(deviceOsVersionParser).parse(any());
            verify(iosDeviceInfoService).getDeviceSetupDoneStatus(any());
            verify(deviceFeaturesFileService).getDeviceFeaturesForIos(anyString());
            verify(deviceFeaturesFileService)
                    .createDeviceFeaturesFileForIos(any(DeviceFeaturesResponse.class), anyString());

        } catch (IOException e) {
            fail(e);
        }
    }

    @Test
    @DisplayName("Test Info connection needed listener if device features file exist")
    void testOnEventIfDeviceFeaturesFileExist() {
        final IosBasicInfoAcquisitionEvent event = new IosBasicInfoAcquisitionEvent(listener, device);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);
        try {
            final Map<IosProperty, String> properties = new HashMap<>();
            properties.put(IosProperty.IMEI, "1111111");
            properties.put(IosProperty.IMEI2, "888888");
            properties.put(IosProperty.MODEL_NUMBER, "Iphone XR Model");
            properties.put(IosProperty.SERIAL_NUMBER, "serial");
            properties.put(IosProperty.ACTIVATION_STATE, "Activate");
            properties.put(IosProperty.REGION_INFO, "LL/A");
            properties.put(IosProperty.SIM_STATUS, "kCTSIMSupportSIMStatusReady");
            properties.put(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER, "35350249706398");
            properties.put(IosProperty.WIFI_ADDRESS, "f4:be:ec:b7:98:23");
            properties.put(IosProperty.REGULATORY_MODEL_NUMBER, "rm");
            properties.put(IosProperty.SIM1_IS_EMBEDDED, "true");
            properties.put(IosProperty.SIM2_IS_EMBEDDED, "false");
            properties.put(IosProperty.RELEASE_TYPE, "Beta");
            properties.put(IosProperty.DEVICE_ENCLOSURE_COLOR, "red");
            properties.put(IosProperty.DEVICE_COLOR, "color");
            properties.put(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY, "simserial");

            Mockito.when(iosDeviceInfoService.getProperties(device,
                            false,
                            IosProperty.IMEI,
                            IosProperty.IMEI2,
                            IosProperty.MODEL_NUMBER,
                            IosProperty.SERIAL_NUMBER,
                            IosProperty.ACTIVATION_STATE,
                            IosProperty.REGION_INFO,
                            IosProperty.SIM_STATUS,
                            IosProperty.MOBILE_EQUIPMENT_IDENTIFIER,
                            IosProperty.WIFI_ADDRESS,
                            IosProperty.REGULATORY_MODEL_NUMBER,
                            IosProperty.SIM1_IS_EMBEDDED,
                            IosProperty.SIM2_IS_EMBEDDED,
                            IosProperty.BASEBAND_VERSION,
                            IosProperty.RELEASE_TYPE,
                            IosProperty.DEVICE_ENCLOSURE_COLOR,
                            IosProperty.DEVICE_COLOR,
                            IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY))
                    .thenReturn(properties);
            when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);
            DeviceFeaturesResponse deviceFeaturesResponse = new ObjectMapper().readValue(
                    "{\"ProductType\":\"iPhone6,1\"," +
                            "\"ModelName\":\"iPhone 5S\"," +
                            "\"BDID\":\"00\"," +
                            "\"CPID\":\"8960\"," +
                            "\"HeadSet\":true," +
                            "\"HeadsetLightPort\":false," +
                            "\"TelePhoto\":false," +
                            "\"UltraWide\":false," +
                            "\"DigitizerNotch\":false," +
                            "\"FlipSwitch\":true," +
                            "\"SkipHomeButton\":false," +
                            "\"WirelessCharging\":false," +
                            "\"EarPiece\":true," +
                            "\"model_id\":\"18\"}", DeviceFeaturesResponse.class);
            when(deviceFeaturesFileService.getDeviceFeaturesForIos(anyString())).thenReturn(deviceFeaturesResponse);
            listener.onEvent(event);

            assertEquals(device.getModel(), "iPhone 5S");
        } catch (IOException e) {
            fail(e);
        }
    }


    @Test
    @DisplayName("Test Info connection needed listener if device features file does not exist")
    void testOnEventIfDeviceFeaturesFileNotExist() {
        final IosBasicInfoAcquisitionEvent event = new IosBasicInfoAcquisitionEvent(listener, device);
        when(iosPairingUtil.checkAndNotifyUiIfNotPaired(any(IosDevice.class))).thenReturn(PairStatus.PAIRED);
        try {
            final Map<IosProperty, String> properties = new HashMap<>();
            properties.put(IosProperty.IMEI, "1111111");
            properties.put(IosProperty.IMEI2, "888888");
            properties.put(IosProperty.MODEL_NUMBER, "Iphone XR Model");
            properties.put(IosProperty.SERIAL_NUMBER, "serial");
            properties.put(IosProperty.ACTIVATION_STATE, "Activate");
            properties.put(IosProperty.REGION_INFO, "LL/A");
            properties.put(IosProperty.SIM_STATUS, "kCTSIMSupportSIMStatusReady");
            properties.put(IosProperty.MOBILE_EQUIPMENT_IDENTIFIER, "35350249706398");
            properties.put(IosProperty.WIFI_ADDRESS, "f4:be:ec:b7:98:23");
            properties.put(IosProperty.REGULATORY_MODEL_NUMBER, "rm");
            properties.put(IosProperty.SIM1_IS_EMBEDDED, "true");
            properties.put(IosProperty.SIM2_IS_EMBEDDED, "false");
            properties.put(IosProperty.RELEASE_TYPE, "Beta");
            properties.put(IosProperty.DEVICE_ENCLOSURE_COLOR, "red");
            properties.put(IosProperty.DEVICE_COLOR, "color");
            properties.put(IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY, "simserial");

            Mockito.when(iosDeviceInfoService.getProperties(device,
                            false,
                            IosProperty.IMEI,
                            IosProperty.IMEI2,
                            IosProperty.MODEL_NUMBER,
                            IosProperty.SERIAL_NUMBER,
                            IosProperty.ACTIVATION_STATE,
                            IosProperty.REGION_INFO,
                            IosProperty.SIM_STATUS,
                            IosProperty.MOBILE_EQUIPMENT_IDENTIFIER,
                            IosProperty.WIFI_ADDRESS,
                            IosProperty.REGULATORY_MODEL_NUMBER,
                            IosProperty.SIM1_IS_EMBEDDED,
                            IosProperty.SIM2_IS_EMBEDDED,
                            IosProperty.BASEBAND_VERSION,
                            IosProperty.RELEASE_TYPE,
                            IosProperty.DEVICE_ENCLOSURE_COLOR,
                            IosProperty.DEVICE_COLOR,
                            IosProperty.INTEGRATED_CIRCUIT_CARD_IDENTITY))
                    .thenReturn(properties);
            device.setProductType("iPhone 16,1");
            device.setSerial("12345");
            DeviceFeaturesResponse deviceFeaturesResponse = new ObjectMapper().readValue(
                    "{\"ProductType\":\"iPhone6,1\"," +
                            "\"ModelName\":\"iPhone 5S\"," +
                            "\"BDID\":\"00\"," +
                            "\"CPID\":\"8960\"," +
                            "\"HeadSet\":true," +
                            "\"HeadsetLightPort\":false," +
                            "\"TelePhoto\":false," +
                            "\"UltraWide\":false," +
                            "\"DigitizerNotch\":false," +
                            "\"FlipSwitch\":true," +
                            "\"SkipHomeButton\":false," +
                            "\"WirelessCharging\":false," +
                            "\"EarPiece\":true," +
                            "\"model_id\":\"18\"}", DeviceFeaturesResponse.class);
            when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);
            when(deviceFeaturesFileService.getDeviceFeaturesForIos(eq(device.getProductType())))
                    .thenReturn(deviceFeaturesResponse);
            listener.onEvent(event);

            assertEquals(device.getModel(), "iPhone 5S");
            verify(deviceFeaturesFileService).getDeviceFeaturesForIos(anyString());
        } catch (IOException e) {
            fail(e);
        }
    }

}
