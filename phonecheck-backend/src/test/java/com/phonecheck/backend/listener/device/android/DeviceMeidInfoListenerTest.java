package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.device.DeviceMeidInfoListener;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.info.DeviceMeidService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.stage.MeidInfoSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceMeidInfoEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.portmap.DeviceMeidInfoMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeviceMeidInfoListenerTest {
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;

    @Mock
    private DeviceMeidService deviceMeidService;

    @Mock
    private DeviceStageUpdater stageUpdater;

    @Mock
    private InMemoryStore inMemoryStore;

    @Mock
    private IMqttAsyncClient mqttClient;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private DeviceMeidInfoListener deviceMeidInfoListener;

    @BeforeEach
    void setUp() {
        deviceMeidInfoListener = new DeviceMeidInfoListener(
                objectMapper,
                mqttClient,
                deviceConnectionTracker,
                deviceMeidService,
                stageUpdater,
                inMemoryStore
        );
        when(mqttClient.isConnected()).thenReturn(true);
    }

    @Test
    public void testOnEvent() throws IOException, MqttException {
        AndroidDevice device = new AndroidDevice();
        device.setId("testDeviceId");

        AndroidDevice androidDeviceInTracker = new AndroidDevice();
        androidDeviceInTracker.setId("testDeviceId");
        androidDeviceInTracker.setImei("499957952682481");
        androidDeviceInTracker.setImei2("866104043013070");

        Transaction transaction = new Transaction();
        transaction.setTransactionId(123);
        when(inMemoryStore.getTransaction()).thenReturn(transaction);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(androidDeviceInTracker);

        DeviceMeidInfoEvent event = new DeviceMeidInfoEvent(deviceMeidInfoListener, device);

        String meid = "meid1";
        when(deviceMeidService.getMeidHex(eq("49995795268248"))).thenReturn(meid);
        String meidDecimal = "meidDecimal1";
        when(deviceMeidService.getMeidDec(eq("49995795268248"))).thenReturn(meidDecimal);
        String pesn = "pesn1";
        when(deviceMeidService.getEsnHex(eq("49995795268248"))).thenReturn(pesn);

        String meid2 = "meid2";
        when(deviceMeidService.getMeidHex(eq("86610404301307"))).thenReturn(meid2);
        String meidDecimal2 = "meidDecimal2";
        when(deviceMeidService.getMeidDec(eq("86610404301307"))).thenReturn(meidDecimal2);
        String pesn2 = "pesn2";
        when(deviceMeidService.getEsnHex(eq("86610404301307"))).thenReturn(pesn2);

        deviceMeidInfoListener.onEvent(event);

        Assertions.assertEquals(meid, androidDeviceInTracker.getMeid());
        Assertions.assertEquals(meid2, androidDeviceInTracker.getMeid2());
        Assertions.assertEquals(meidDecimal, androidDeviceInTracker.getMeidDecimal());
        Assertions.assertEquals(meidDecimal2, androidDeviceInTracker.getMeidDecimal2());
        Assertions.assertEquals(pesn, androidDeviceInTracker.getPesn());
        Assertions.assertEquals(pesn2, androidDeviceInTracker.getPesn2());

        verify(stageUpdater, times(1)).updateStage(any(MeidInfoSuccessStage.class));

        // Make sure the esn check response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "meid", "info", "collection", "success"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final DeviceMeidInfoMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        DeviceMeidInfoMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(meid, message.getMeid());
                assertEquals(meid2, message.getMeid2());
                assertEquals(meidDecimal, message.getMeidDecimal());
                assertEquals(meidDecimal2, message.getMeidDecimal2());
                assertEquals(pesn, message.getPesn());
                assertEquals(pesn2, message.getPesn2());
            } catch (IOException e) {
                return false;
            }
            return true;
        }));
    }
}