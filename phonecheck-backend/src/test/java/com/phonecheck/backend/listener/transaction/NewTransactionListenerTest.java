package com.phonecheck.backend.listener.transaction;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.TransactionService;
import com.phonecheck.model.event.transaction.NewTransactionEvent;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class NewTransactionListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private TransactionService transactionService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private NewTransactionListener listener;

    @BeforeEach
    void beforeEach() {
        listener = new NewTransactionListener(mqttClient, objectMapper, transactionService);
    }

    @Test
    public void testOnEventSuccess() {
        when(mqttClient.isConnected()).thenReturn(true);
        final NewTransactionEvent event = new NewTransactionEvent(listener);
        listener.onEvent(event);
        Mockito.verify(transactionService).createNewTransaction();
    }

}
