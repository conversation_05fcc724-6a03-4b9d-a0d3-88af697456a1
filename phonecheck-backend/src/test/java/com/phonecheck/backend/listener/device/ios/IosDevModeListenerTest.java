package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosDevModeEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosDevModeMessage;
import com.phonecheck.mount.image.DevModeService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosDevModeListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private DevModeService devModeService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;

    private IosDevice device;
    private IosDevModeListener listener;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        listener = new IosDevModeListener(mqttClient, objectMapper, devModeService, deviceConnectionTracker);
    }

    @Test
    void testOnEventAutoAcceptSuccessful() throws IOException, MqttException {
        final IosDevModeEvent event = new IosDevModeEvent(listener, device);

        when(devModeService.autoAcceptDevMode(any(IosDevice.class))).thenReturn(true);

        listener.onEvent(event);

        // Make sure the "dev mode" MQTT message was not published
        Mockito.verify(mqttClient, never()).publish(anyString(), any(MqttMessage.class));
    }

    @Test
    void testOnEventAutoAcceptFailed() throws IOException, MqttException {
        final IosDevModeEvent event = new IosDevModeEvent(listener, device);
        when(mqttClient.isConnected()).thenReturn(true);
        when(devModeService.autoAcceptDevMode(any(IosDevice.class))).thenReturn(false);

        listener.onEvent(event);

        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "dev", "mode"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final IosDevModeMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        IosDevModeMessage.class);
                assertEquals(device.getId(), message.getId());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }
}
