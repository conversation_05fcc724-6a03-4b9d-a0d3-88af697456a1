package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.device.DeviceCarrierChangeListener;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.event.device.DeviceCarrierChangeEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosDeviceCarrierChangeListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceInfoDBService deviceInfoDBService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private IosDevice device;
    private DeviceCarrierChangeListener listener;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setSerial("9802390239023");
        device.setCarrier("AT&T");

        listener = new DeviceCarrierChangeListener(objectMapper, mqttClient,
                deviceInfoDBService, deviceConnectionTracker, inMemoryStore, eventPublisher);
    }

    @Test
    void testNullOnEventDeviceCarrierChange() {

        listener.onEvent(new DeviceCarrierChangeEvent(this, device));

        verify(deviceInfoDBService, never()).updateDeviceCarrier(anyString(), anyString(), anyString());
    }

    @Test
    void testOnEventDeviceCarrierChange() {
        IosDevice deviceInTracker = new IosDevice();
        deviceInTracker.setId("123456");
        deviceInTracker.setCarrier("ABC");

        HashMap<AutomationWorkflow, AutomationWorkflowStatus> workflowMap = new HashMap<>();
        workflowMap.put(AutomationWorkflow.TEST_RESULTS, AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS);
        deviceInTracker.setPreviouslyRanAutomation(workflowMap);

        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(deviceInTracker);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());

        listener.onEvent(new DeviceCarrierChangeEvent(this, device));

        assertEquals(device.getCarrier(), deviceInTracker.getCarrier());
        verify(deviceInfoDBService).updateDeviceCarrier(eq(deviceInTracker.getId()), eq(deviceInTracker.getCarrier()),
                eq("123"));

        ArgumentCaptor<AbstractDeviceEvent> captor = ArgumentCaptor.forClass(AbstractDeviceEvent.class);
        verify(eventPublisher, times(2)).publishEvent(captor.capture());
    }
}
