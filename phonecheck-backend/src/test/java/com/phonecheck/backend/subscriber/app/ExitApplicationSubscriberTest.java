package com.phonecheck.backend.subscriber.app;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.mqtt.messages.LoginRequestMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class ExitApplicationSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    private ExitApplicationSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new ExitApplicationSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessageWrongMessage() throws JsonProcessingException {

        final LoginRequestMessage requestMessage = new LoginRequestMessage();
        MqttTopicMessage message = new MqttTopicMessage("exit",
                mapper.writeValueAsString(requestMessage));

        Exception exception = assertThrows(RuntimeException.class, () -> {
            subscriber.onMessage(message);
        });

        Assertions.assertNotNull(exception);
        Assertions.assertTrue(true);
    }
}
