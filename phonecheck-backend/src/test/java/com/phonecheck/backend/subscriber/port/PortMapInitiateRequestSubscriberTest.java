package com.phonecheck.backend.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.port.PortMapInitiateEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapInitiateRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PortMapInitiateRequestSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    private PortMapInitiateRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new PortMapInitiateRequestSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessageInitiatePortMapRequest() throws JsonProcessingException {
        final PortMapInitiateRequestMessage requestMessage = new PortMapInitiateRequestMessage();

        MqttTopicMessage message = new MqttTopicMessage("initiate/port-map/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(PortMapInitiateEvent.class));
    }
}