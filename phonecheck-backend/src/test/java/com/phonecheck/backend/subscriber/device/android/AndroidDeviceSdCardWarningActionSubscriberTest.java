package com.phonecheck.backend.subscriber.device.android;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.android.AndroidDeviceSdCardWarningEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidDeviceSdCardDetectionMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class AndroidDeviceSdCardWarningActionSubscriberTest {
    private AndroidDeviceSdCardWarningActionSubscriber subscriber;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private IMqttAsyncClient client;
    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    void setup() {
        subscriber = new AndroidDeviceSdCardWarningActionSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        final AndroidDeviceSdCardDetectionMessage requestMessage = new AndroidDeviceSdCardDetectionMessage();
        requestMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage(DeviceFamily.ANDROID + "/sd-card/action-done",
                mapper.writeValueAsString(requestMessage));

        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(AndroidDeviceSdCardWarningEvent.class));
    }


}
