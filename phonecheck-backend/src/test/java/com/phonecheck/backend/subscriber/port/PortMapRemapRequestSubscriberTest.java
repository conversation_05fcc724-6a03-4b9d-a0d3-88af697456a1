package com.phonecheck.backend.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.port.PortMapRemapEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapRemapRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PortMapRemapRequestSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    private PortMapRemapRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new PortMapRemapRequestSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessageRemapPortMapRequest() throws JsonProcessingException {
        final PortMapRemapRequestMessage requestMessage = new PortMapRemapRequestMessage();

        MqttTopicMessage message = new MqttTopicMessage("remap/port-map/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(PortMapRemapEvent.class));
    }
}