package com.phonecheck.backend.subscriber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.login.StartDevicesThreadEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.StartDeviceProcessingMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class StartDeviceProcessingSubscriberTest {
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private final ObjectMapper mapper = new ObjectMapper();
    private StartDevicesProcessingSubscriber processingSubscriber;

    @BeforeEach
    void setup() {
        processingSubscriber = new StartDevicesProcessingSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessageStartProcessingRequest() throws JsonProcessingException {
        MqttTopicMessage message = new MqttTopicMessage("start/processing",
                mapper.writeValueAsString(new StartDeviceProcessingMessage()));
        processingSubscriber.onMessage(message);
        verify(eventPublisher).publishEvent(any(StartDevicesThreadEvent.class));
    }
}
