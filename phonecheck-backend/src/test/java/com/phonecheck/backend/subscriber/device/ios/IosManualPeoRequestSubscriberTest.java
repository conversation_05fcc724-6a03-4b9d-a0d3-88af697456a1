package com.phonecheck.backend.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.event.device.ios.IosPairingNeededEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosManualPeoRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosManualPeoRequestSubscriberTest {
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private final ObjectMapper mapper = new ObjectMapper();
    private IosManualPeoRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosManualPeoRequestSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessageManualPeoRequest() throws JsonProcessingException, InterruptedException {
        MqttTopicMessage message = new MqttTopicMessage(DeviceType.IPHONE +
                "manual/peo", mapper.writeValueAsString(new IosManualPeoRequestMessage()));
        subscriber.onMessage(message);
        Thread.sleep(2000);
        verify(eventPublisher).publishEvent(any(IosPairingNeededEvent.class));
    }
}
