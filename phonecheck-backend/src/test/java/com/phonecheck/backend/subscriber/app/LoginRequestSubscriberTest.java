package com.phonecheck.backend.subscriber.app;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.app.LoginRequestEvent;
import com.phonecheck.model.mqtt.messages.LoginRequestMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class LoginRequestSubscriberTest {

    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private LoginRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new LoginRequestSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessageWrongMessage() throws JsonProcessingException, InterruptedException {
        final LoginRequestMessage requestMessage = new LoginRequestMessage();

        MqttTopicMessage message = new MqttTopicMessage("login/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        Thread.sleep(2000);
        verify(eventPublisher).publishEvent(any(LoginRequestEvent.class));
    }
}
