package com.phonecheck.backend.subscriber.print;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.print.PrintersListAcquisitionEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.PrintersListRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class PrinterListRequestSubscriberTest {
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private final ObjectMapper mapper = new ObjectMapper();
    private PrintersListRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new PrintersListRequestSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessage() throws JsonProcessingException {
        PrintersListRequestMessage requestMessage = new PrintersListRequestMessage();
        requestMessage.setId("12");
        MqttTopicMessage message = new MqttTopicMessage("printer/list/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(PrintersListAcquisitionEvent.class));
    }
}
