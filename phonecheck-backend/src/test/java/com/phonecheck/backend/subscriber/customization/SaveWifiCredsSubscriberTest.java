package com.phonecheck.backend.subscriber.customization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.SaveWifiCredentialsMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.wifi.WifiConfig;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class SaveWifiCredsSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private InMemoryStore inMemoryStore;
    private SaveWifiCredsSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new SaveWifiCredsSubscriber(mapper, client, inMemoryStore);
    }

    @Test
    void testOnWifiSaveMessage() throws JsonProcessingException {
        WifiConfig wifiConfig = WifiConfig.builder().ssid("ssid").password("pass").build();
        SaveWifiCredentialsMessage saveWifiCredentialsMessage = new SaveWifiCredentialsMessage();
        saveWifiCredentialsMessage.setWifiConfig(wifiConfig);

        MqttTopicMessage message = new MqttTopicMessage(
                "wifi/save", mapper.writeValueAsString(saveWifiCredentialsMessage));
        subscriber.onMessage(message);

        ArgumentCaptor<WifiConfig> captor = ArgumentCaptor.forClass(WifiConfig.class);
        verify(inMemoryStore).setWifiConfig(captor.capture());
        Assertions.assertEquals(wifiConfig.getSsid(), captor.getValue().getSsid());
        Assertions.assertEquals(wifiConfig.getPassword(), captor.getValue().getPassword());
    }

}
