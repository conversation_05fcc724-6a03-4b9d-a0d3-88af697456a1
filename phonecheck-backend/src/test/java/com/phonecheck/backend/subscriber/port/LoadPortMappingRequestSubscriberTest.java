package com.phonecheck.backend.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.LoadPortMappingRequestMessage;
import com.phonecheck.port.PortMappingService;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LoadPortMappingRequestSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private PortMappingService portMappingService;
    @Mock
    private ApplicationContext applicationContext;


    private LoadPortMappingRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new LoadPortMappingRequestSubscriber(mapper, mqttClient, portMappingService,
                applicationContext);
    }

    @Test
    void testOnMessage() throws JsonProcessingException, MqttException {
        final LoadPortMappingRequestMessage requestMessage = new LoadPortMappingRequestMessage();

        when(mqttClient.isConnected()).thenReturn(true);
        MqttTopicMessage message = new MqttTopicMessage("port-map/available/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        String topic = TopicBuilder.buildGenericTopic("port-map", "available", "response");

        verify(portMappingService).isMappingAvailable();
        verify(portMappingService).getMappedPortsCount();
        verify(mqttClient).publish(eq(topic), any(MqttMessage.class));
    }
}