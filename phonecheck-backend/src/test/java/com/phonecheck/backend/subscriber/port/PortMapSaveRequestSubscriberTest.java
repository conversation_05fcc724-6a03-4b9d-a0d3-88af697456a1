package com.phonecheck.backend.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.port.PortMapSaveEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.PortMapSaveRequestMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PortMapSaveRequestSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    private PortMapSaveRequestSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new PortMapSaveRequestSubscriber(mapper, mqttClient, eventPublisher);
    }

    @Test
    void testOnMessageSavePortMapRequest() throws JsonProcessingException {
        final PortMapSaveRequestMessage requestMessage = new PortMapSaveRequestMessage();

        MqttTopicMessage message = new MqttTopicMessage("save/port-map/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(PortMapSaveEvent.class));
    }
}