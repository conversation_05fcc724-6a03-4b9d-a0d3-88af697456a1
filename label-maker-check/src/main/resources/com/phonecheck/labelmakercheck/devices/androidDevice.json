{"id": "28181JEGR01832", "deviceType": "ANDROID", "deviceFamily": "ANDROID", "operatingSystem": "Android", "osMajorVersion": 14, "licenseIdentifier": "28181JEGR01832", "serial": "28181JEGR01832", "imei": "355984762823561", "meid": "35598476282356", "meidDecimal": "089505906202630486", "simSerial": "", "simSerial2": "", "stage": "READY", "modelNo": "GB62Z", "wifiAddress": "02:00:00:00:00:00", "portNumber": 0, "vendorName": "LabelMakerCheck", "invoiceNo": "666", "boxNo": "020", "grade": "SCRF", "carrier": "Unlocked", "pesn": "803944C6", "deviceLock": "OFF", "rooted": "NOT_ROOTED", "diskSize": {"size": 128, "memoryUnit": "GIGABYTES"}, "make": "Google", "guid": "https://historyreport.phonecheck.com/report-qr/4fa583a3-6183-11ef-a914-020509c2a72f", "firmware": "AP2A.240605.024", "retrievedResults": true, "deviceTestResult": {"MicrophoneResults": {"RMAmplitude": 0, "FMAmplitude": 0, "BMAmplitude": 0}, "TestResults": {"pending": "", "total": "Earpiece@Earpiece,Headset-Right,Headset Port,Headset-Left,Microphone@Microphone,Auto LS,Loud Speaker@Loud Speaker,Video Microphone@Video Microphone,Camera Test,AutoSnapFront,Front Camera@Front Camera,Front Camera Quality@Front Camera Quality,Flashlight,Camera AutoFocus,Rear Camera Quality@Rear Camera Quality,AutoSnapRear,Rear Camera@Rear Camera,Telephoto Camera,Telephoto Camera Quality,UltraWide Camera@UltraWide Camera,UltraWide Camera Quality@UltraWide Camera Quality,Cosmetics,LCD Flow 1,Volume Up Button@Volume Up Button,Home Button,3D Touch,Force Touch,LCD@LCD,Glass Condition,Edge Screen,Digitizer@Digitizer,Proximity Sensor,Screen Rotation,Accelerometer@Accelerometer,Auto Accelerometer,Gyroscope,Vibration@Vibration", "passed": " Accelerometer, Digitizer, Vibration,LCD", "failed": " Earpiece, Front Camera, Front Camera Quality, Loud Speaker, Microphone, Rear Camera, Rear Camera Quality, UltraWide Camera, UltraWide Camera Quality, Video Microphone, Volume Up Button,Proximity Sensor", "testingCompleted": true, "passed_count": "4", "pending_count": "0", "not_supported_count": "22", "not_supported": "Headset-Right, Headset Port, Headset-Left, Auto LS, Camera Test, AutoSnapFront, Flashlight, Camera AutoFocus, AutoSnapRear, Telephoto Camera, Telephoto Camera Quality, Cosmetics, LCD Flow 1, Home Button, 3D Touch, Force Touch, Glass Condition, Edge Screen, Proximity Sensor, Screen Rotation, Auto Accelerometer, Gyroscope", "not_attended_count": "12", "not_attended": "ProximitySensor,VolumeUpButton,LoudSpeaker,Microphone,VideoMicrophone,Earpiece,FrontCamera,FrontCameraQuality,RearCamera,RearCameraQuality,UltraWideCamera,UltraWideCameraQuality", "failed_count": "12", "total_count": "37"}, "GradeResults": "SCRF", "BatteryResults": {}, "DeviceColor": "Sonpari Gold"}, "currentRunningAutomation": "TEST_RESULTS", "automationSteps": [{"step": "print", "status": "IN_PROGRESS"}], "previouslyRanAutomation": {"CONNECTION": "SUCCESS"}, "isErasePerformed": false, "model": "Pixel 6a Dual", "batteryPercentage": 36, "batteryDegraded": false, "batteryInfo": {"cycle": 78, "batteryPercentage": 36, "healthPercentage": 97, "oemHealthPercentage": 0, "cocoHealthPercentage": 0, "currentCapacity": 4194, "designedCapacity": 4312, "cocoCurrentCapacity": 0, "cocoDesignedCapacity": 0, "source": "BS01", "batteryResistance": 0, "temperature": 0, "averageTemperature": 0, "minTemperature": 0, "maxTemperature": 0, "charging": true}, "batteryStateHealth": 97, "previousBatteryStateHealth": 0, "mdmStatus": "OFF", "knox": "KNOX_OFF", "deviceState": "HOME", "deviceCreatedDate": "**********.671805", "deviceUpdatedDate": "**********.384981", "deviceProcessStatusMap": {"MEID": "COMPLETED", "INFO_COLLECTION": "COMPLETED", "SKU": "COMPLETED"}, "androidConnectionMode": "ADB", "authorizationStatus": "AUTHORIZED", "ram": "6 GB RAM", "abiList": "arm64-v8a,armeabi-v7a,armeabi\n", "sdCardErased": false, "pcUtilityDeviceInfoRetrieved": true, "simNetwork": "", "alertSliderMiddlePerformed": false, "alertSliderDownPerformed": false, "dualSimSupported": true, "alertSliderUpPerformed": false, "restartInProgressForUnlocking": false, "sim1Esim": false, "sim2Esim": false, "prepared": false, "shutdownInProgress": false, "reconnectRequired": false, "manualEntry": false, "eraseInProgress": false, "connectedInRecovery": false, "manualErase": false, "appInstalled": true, "sourceApiCalled": false, "esimActive": false, "pairAttempted": false, "restoreInProgress": false, "wifiConnected": false, "restartInProgress": false, "esimErased": false}