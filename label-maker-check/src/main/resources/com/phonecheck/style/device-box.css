.device-box {
    -fx-background-radius: 8;
    -fx-background-color: white;
    -fx-effect: dropshadow(gaussian, grey, 10, 0, 0, 0);
}

.device-box-selected {
    -fx-border-color: black;
    -fx-border-width: 2;
    -fx-border-radius: 8;
}

.label-bold-heading {
    -fx-font-size: 18px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold
 }

.label-bold-heading-2 {
    -fx-font-size: 14px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold
 }

 .label-normal {
     -fx-font-size: 12px;
     -fx-font-family: "Arial";
     -fx-font-weight: normal
  }

.combo-box {
    -fx-border-color: white;
    -fx-background-color: white;
    -fx-background: white;
    -fx-border-radius: 0;
    -fx-padding: 0 0 0 0;
    -fx-border-width: 0 0 1 0;
    -fx-text-background:white;
}

.combo-box:hover {
    -fx-border-color: black;
}

.combo-box .arrow {
    -fx-background-color: white
}

.combo-box:hover .arrow {
    -fx-background-color: #45667B;
}

.combo-box .arrow-button {
    -fx-background-color: white;
    -fx-padding: 0 0 0 0;
}

.combo-box .list-cell {
    -fx-background-color: white;
    -fx-text-fill: black;
}

.combo-box .text-field {
    -fx-background-color: white;
    -fx-font-weight: bold;
    -fx-font: 12px "System";
    -fx-text-fill: black;
    -fx-prompt-text-fill: lightgrey;
}

.text-field {
    -fx-background-insets: 0;
    -fx-background-color: transparent, white, transparent, white;
    -fx-background-radius: 0, 0, 0, 0;
    -fx-box-border: none;
    -fx-focus-color: -fx-control-inner-background;
    -fx-faint-focus-color: -fx-control-inner-background;
    -fx-text-box-border: -fx-control-inner-background;
}

.device-defects-container {
    -fx-background-color: white;
}

.market-place-certified-container{
    -fx-background-color: #E9FFF0;
    -fx-background-radius: 3;
}

.initial-defects-container {
    -fx-background-color: #F6F6F6;
    -fx-background-radius: 3;
}

.cosmetics-check-box{
    -fx-min-width: 210;
    -fx-pref-height: 32;
    -fx-background-radius: 5, 5, 5, 5;
    -fx-border-radius: 4, 4, 4, 4;
    -fx-border-color: #D2D6DA;
    -fx-padding: 5px;
    -fx-border-insets: 5px;
    -fx-background-insets: 5px;
    -fx-text-fill: #374151;
    -fx-font-size: 13;
    -fx-font-family: Helvetica;
}
.cosmetics-check-box>.box{
    -fx-background-color: white;
    -fx-border-color: #979797;
    -fx-pref-height: 14.25;
    -fx-pref-width: 14.25;
    -fx-max-height: 14.25;
    -fx-max-width: 14.25;
    -fx-background-radius: 2 2 2 2 ;
    -fx-border-radius: 2 2 2 2;
}
.cosmetics-check-box:selected>.box{
    -fx-background-radius: 2 2 2 2;
    -fx-border-radius: 2 2 2 2;
    -fx-background-color: #079af4;
    -fx-pref-height: 14.25;
    -fx-pref-width: 14.25;
    -fx-max-height: 14.25;
    -fx-max-width: 14.25;
    -fx-border-color: transparent;

}
.cosmetics-check-box:selected>.box>.mark{
    -fx-background-color: white;
}
