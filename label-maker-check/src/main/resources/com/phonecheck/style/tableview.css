@import "fonts.css";

.table-cell {
    -fx-border-width: 0;
    -fx-border-color: transparent -fx-box-border transparent transparent;
    -fx-font-size: 14px;
    -fx-font-family: "Arial";
    -fx-alignment: center;
    -fx-text-fill: #212121;
}

.table-view {
    -fx-table-header-border-color: #959595;
    -fx-table-cell-border-color: -fx-box-border;
    -fx-border-width: 0;
    -fx-padding: 0;
}

.table-view:focused, .table-view:unfocused {
    -fx-background-color: transparent;
    -fx-border-width: 0;
    }

.table-row-cell {
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #CCCCCC;
    -fx-background-color: white;
}

.table-row-cell:odd {
    -fx-border-color: #CCCCCC;
    -fx-background-color: white;
    -fx-background-insets: 0, 0 0 1 0;
}

.table-row-cell:filled:selected:focused, .table-row-cell:filled:selected {
    -fx-background-color: rgb(205, 205, 205);
}

.table-view:row-selection .table-row-cell:filled:hover {
    -fx-background-color: rgb(231, 231, 231);
}

.table-view:row-selection .table-row-cell:filled:focused:hover {
    -fx-background-color: rgb(205, 205, 205);
}

.table-view:focused .table-row-cell:filled:focused:hover {
    -fx-background-color: -fx-box-border, rgb(231, 231, 231);
}

.table-view .column-header-background {
    -fx-background-color: white;
}

.table-view .column-header, .table-view .filler {
    -fx-background-color: white;
    -fx-size: 35;
    -fx-border-color: #CCCCCC;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 0 5 0 0;
    -fx-font-size: 15px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold
}

.table-view .show-hide-columns-button {
    -fx-background-color: white;
    -fx-border-color: -fx-box-border;
    -fx-border-insets: -1 -1 0 0;
}

.table-view .column-drag-header {
    -fx-background-color: #CCCCCC;
    -fx-opacity: 0.6;
}

.table-view .column-resize-line {
    -fx-background-color: rgb(45, 137, 239);
}

.table-view .column-overlay {
    -fx-background-color: darkgray;
    -fx-opacity: 0.2;
}

.table-view .arrow {
    -fx-padding: 0.1em;
    -fx-shape: "M4,17v-2.889l4.124-3.86l4.125,3.86V17l-4.125-3.375L4,17z";
}

.noheader .column-header-background {
    -fx-max-height: 0;
    -fx-pref-height: 0;
    -fx-min-height: 0;
}

.no-column-lines .column-header-background {
    -fx-border-width: 0;
    -fx-padding: 0;
}