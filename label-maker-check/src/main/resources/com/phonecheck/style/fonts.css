@font-face {
    font-family: SF-Compact-Display-Regular;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Regular.otf');
}

@font-face {
    font-family: SF-Compact-Display-Medium;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Medium.otf');
}

@font-face {
    font-family: SF-Compact-Display-Light;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Light.otf');
}

@font-face {
    font-family: SF-Compact-Display-Black;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Black.otf');
}

@font-face {
    font-family: SF-Compact-Display-Bold;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Bold.otf');
}


@font-face {
    font-family: SF-Compact-Display-Heavy;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Heavy.otf');
}

@font-face {
    font-family: SF-Compact-Display-Semibold;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Semibold.otf');
}

@font-face {
    font-family: SF-Compact-Display-Thin;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Thin.otf');
}

@font-face {
    font-family: SF-Compact-Display-Ultralight;
    src: url('../fonts/sf-compact-display/SF-Compact-Display-Ultralight.otf');
}

@font-face {
    font-family: DIN-Bold;
    src: url('../fonts/din/DIN-Bold.ttf');
}

@font-face {
    font-family: DIN-BoldAlternate;
    src: url('../fonts/din/DIN-BoldAlternate.ttf');
}

@font-face {
    font-family: DIN-LightAlternate;
    src: url('../fonts/din/DIN-LightAlternate.ttf');
}

@font-face {
    font-family: DIN-RegularAlternate;
    src: url('../fonts/din/DIN-RegularAlternate.ttf');
}

@font-face {
    font-family: DIN-MediumAlternate;
    src: url('../fonts/din/DIN-MediumAlternate.ttf');
}
