/* Use with port-mapping.fxml */
.root {
    -accent-color: #3961FB;
    -disconnected-color: #9D9EA0;

    -fx-background-color: white;
    -fx-font-size: 15px;
    -fx-font-smoothing-type: lcd;
}

.title {
    -fx-font-style: normal;
    -fx-font-weight: 700;
    -fx-text-fill: -accent-color;
    -fx-underline: true;
}

.label, #mainVBox .jfx-button {
    -fx-font-family: "Roboto";
}

#mainVBox .jfx-button {
    -fx-background-color: -accent-color;
    -fx-text-fill: white;
    -fx-font-weight: 700;
    -fx-min-width: 325px;
    -fx-border-width: 1;
    -fx-border-radius: 2px;
}

#mainVBox .jfx-button.outline {
    -fx-background-color: white;
    -fx-text-fill: -accent-color;
    -fx-border-color: black;
    -fx-border-radius: 4px;
    -fx-border-width: 1;
}

.small {
    -fx-font-size: 12px;
}

#portsFlowPane {
    -fx-border-color: black;
    -fx-border-radius: 8px;
    -fx-border-width: 2;
    -fx-view-order: 0;
}

#portsFlowPane .port {
    width: 60;
    height: 32;
    -fx-max-width: 60;
    -fx-max-height: 32;
    -fx-border-color: -disconnected-color;
    -fx-border-width: 1;
}

#portsFlowPane .disconnected {
    -fx-background-color: -disconnected-color;
}

#portsFlowPane .mapped {
    -fx-background-color: #22BA3B;
}

#numberText {
    -fx-font-size: 19px;
}

.usb-icon {
    -fx-image: url("../image/icon/usb-icon.png");
}