package com.phonecheck.labelmakercheck.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.LabelFxmlService;
import com.phonecheck.api.cloud.MasterLoginService;
import com.phonecheck.labelmakercheck.service.LmcSaveLabelImageService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.store.UiInMemoryStore;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.StackPane;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.List;
import java.util.*;

@Component
public class LabelMakerCheckController implements Initializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(LabelMakerCheckController.class);

    @Getter
    @Autowired
    private ObjectMapper mapper;
    @Getter
    @Autowired
    private LmcSaveLabelImageService lmcSaveLabelImageService;
    @Getter
    @Autowired
    private MasterLoginService masterLoginService;
    @Getter
    @Autowired
    private LabelFxmlService labelFxmlService;
    @Getter
    @Autowired
    private UiInMemoryStore uiInMemoryStore;
    @FXML
    private StackPane stackPane;
    @FXML
    private ComboBox<String> lmcSelectDevice;
    @FXML
    private ComboBox<String> lmcSelectLabel;
    @FXML
    private Button lmcPrintToPdfButton;
    @FXML
    private ImageView previewImageView;

    @FXML
    private Button lmcShowFXML;

    @Setter
    private Device device;

    private final Map<String, Device> devicesMap = new HashMap<>();

    @Value("classpath:/com/phonecheck/labelmakercheck/devices/IosDevice.json")
    private Resource iosDeviceResource;
    @Value("classpath:/com/phonecheck/labelmakercheck/devices/androidDevice.json")
    private Resource androidDeviceResource;

    /**
     * Raises request to print as pdf for device
     */
    @FXML
    private void onLmcPrintToPDF() {
        if (StringUtils.isNotBlank(lmcSelectDevice.getValue())
                && StringUtils.isNotBlank(lmcSelectLabel.getValue())) {
            LOGGER.debug("Starting print to PDF from Label Maker Check");
            generateAndSendPrintLabelSingleRequest(true);
        }
    }

    /**
     * Raises request to show FXML for device
     */
    @FXML
    private void onlmcRefreshLabels() throws IOException {
        LOGGER.info("Calling Label Refresh");
        fetchLabelDataForMaster();
    }

    public void fetchLabelDataForMaster() {
        //set combobox value to blank (initial state)
        lmcSelectLabel.setItems(null);
        previewImageView.setImage(new Image("/com/phonecheck/image/label.png"));
        //get master token and call labelmaker to get list of labels and related data for master
        LOGGER.info("calling api to get master token");
        String token = getMasterLoginService().getServerApiToken("PCEngineering", "nxY8PC@");
        Map<String, LabelFxmlResponse.Data> labelMap = getLabelFxmlService().getLabelsFromCloud(token, null);
        getUiInMemoryStore().setLabelDataMap(labelMap);

        //Fetch keys (Label names) from labelMap and set list to Select Label drop down
        LOGGER.info("populating list with label names");
        List<String> labelsFrmCloud = labelMap.keySet().stream().sorted().toList();
        final ObservableList<String> observableLabelsList = FXCollections.observableArrayList(labelsFrmCloud);
        lmcSelectLabel.setItems(observableLabelsList);
        LOGGER.info("refresh completed");
    }

    /**
     * Raises request to show FXML for device
     */
    @FXML
    private void onlmcShowFXML() throws IOException {
        if (StringUtils.isNotBlank(lmcSelectLabel.getValue())) {
            LOGGER.debug("Starting show FXML from Label Maker Check");
            String selectedLabelName = lmcSelectLabel.getValue();
            String fxmlToDisplay;
            if (StringUtils.isNotBlank(getUiInMemoryStore().getLabelDataMap().get(selectedLabelName).getFxmlV2())) {
                fxmlToDisplay = getUiInMemoryStore().getLabelDataMap().get(selectedLabelName).getFxmlV2();
            } else {
                fxmlToDisplay = getUiInMemoryStore().getLabelDataMap().get(selectedLabelName).getFxml();
            }

            new File(System.getProperty("user.home") + "/Library/Caches/LabelMakerCheck/").mkdir();

            File tempFile = new File(System.getProperty("user.home") + "/Library/Caches/LabelMakerCheck/"
                    + selectedLabelName + System.currentTimeMillis());
            Files.write(tempFile.toPath(), fxmlToDisplay.getBytes());

            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                Desktop.getDesktop().browse(tempFile.toURI());
            } else {
                LOGGER.warn("Cannot open the fxml view");
            }
        }
    }


    /**
     * Method to save label image request and notify backend application to print
     *
     * @param printToPdf flag
     */
    private void generateAndSendPrintLabelSingleRequest(final boolean printToPdf) {
        //This utility only has 1 print, but as we are using existing print method we are setting the printOperation
        List<PrintOperation> printOperations = new ArrayList<>();
        //Method call to set values from dialog to printOperation object
        PrintOperation primaryOperation = setValuesToPrintFromDialog(printToPdf);
        if (primaryOperation != null) {
            printOperations.add(primaryOperation);
        }
        // Enqueue print operations
        for (PrintOperation printOperation : printOperations) {
            getLmcSaveLabelImageService().enqueueSaveLabelImageOperation(printOperation);
        }
    }

    /**
     * Sets the primary values to print-operation from a dialog.
     *
     * @param printToPdf a boolean indicating whether to print-to-PDF
     * @return a PrintOperation object with the primary values set, or null if the primary label or printer is empty
     */
    private PrintOperation setValuesToPrintFromDialog(final boolean printToPdf) {
        boolean isLabelEmpty = StringUtils.isEmpty(lmcSelectLabel.getValue());
        //set device object with value from label
        device = devicesMap.get(lmcSelectDevice.getValue());
        PrintOperation printOperation;
        if (isLabelEmpty) {
            return null;
        }
        //set device object and required values to printOperation method
        printOperation = new PrintOperation();
        printOperation.setDevice(device);
        printOperation.setPrintToPdf(printToPdf);
        printOperation.setManualPrint(true);
        printOperation.setLabelName(lmcSelectLabel.getValue());
        printOperation.setPrinterName("None");
        return printOperation;
    }

    /**
     * Initialize the print UI elements
     *
     * @param location  The location used to resolve relative paths for the root object, or
     *                  {@code null} if the location is not known.
     * @param resources The resources used to localize the root object, or {@code null} if
     *                  the root object was not localized.
     */
    @Override
    public void initialize(final URL location, final ResourceBundle resources) {

        fetchLabelDataForMaster();

        //set values to InMemoryStore
        getUiInMemoryStore().setMasterUserName("PCEngineering");
        getUiInMemoryStore().setLoggedInTesterName("Ashwij");
        getUiInMemoryStore().setLoggedInUserId("AshwijUgrankar");

        try {
            //Create dummy ios and android device and set it to a map with Key as Model name
            String content = iosDeviceResource.getContentAsString(Charset.defaultCharset());
            String content2 = androidDeviceResource.getContentAsString(Charset.defaultCharset());
            IosDevice iosDevice = getMapper().readValue(content, IosDevice.class);
            AndroidDevice androidDevice = getMapper().readValue(content2, AndroidDevice.class);
            devicesMap.put(iosDevice.getModel(), iosDevice);
            devicesMap.put(androidDevice.getModel(), androidDevice);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        final ObservableList<String> observableDevicesList =
                FXCollections.observableArrayList(devicesMap.keySet().stream().toList());
        //Load the key from device map as a list to Select Device ComboBox
        lmcSelectDevice.setItems(observableDevicesList);
        //set listener to comboBox so that label image is set when value is changed
        lmcSelectLabel.getSelectionModel().selectedItemProperty().addListener(setLabelComboBoxChangeListener());
    }

    /**
     * Method to set image of label selected in the preview image box when label changed
     *
     * @return ChangeListener
     */
    private ChangeListener<String> setLabelComboBoxChangeListener() {
        return (observable, oldValue, newValue) -> {
            if (StringUtils.isNotBlank(newValue)) {
                if (!newValue.equals(oldValue)) {
                    LabelFxmlResponse.Data labelData =
                            getUiInMemoryStore().getLabelDataMap().get(lmcSelectLabel.getValue());
                    previewImageView.setImage(new Image(labelData.getImageUrl()));
                }
            }
        };
    }
}
