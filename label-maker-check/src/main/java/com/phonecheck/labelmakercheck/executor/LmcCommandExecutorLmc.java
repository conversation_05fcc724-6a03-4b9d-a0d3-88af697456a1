package com.phonecheck.labelmakercheck.executor;

import com.phonecheck.command.ICommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class LmcCommandExecutorLmc extends LmcAbstractExecutor {
    private static final Logger LOGGER = LoggerFactory.getLogger(LmcCommandExecutorLmc.class);
    private static final int COMMAND_RETRIES = 5;

    public String executeForTest(final ICommand command) throws IOException {
        try {
            for (int i = 0; i < COMMAND_RETRIES; i++) {
                String response = runCommandForTest(command);
                if (response != null && isDeviceFound(response)) {
                    return response;
                }
                // wait 500 ms for the next retry
                Thread.sleep(500);
                LOGGER.warn("Command {} execution failed during attempt no: {}", command.getClass().getName(), i + 1);
            }
        } catch (final InterruptedException e) {
            LOGGER.error("Command thread interrupted.", e);
        }
        return null;
    }
}
